<?php

/**
 * Test Multiple Styles in Product Data
 * Minh họa trường hợp có nhiều style trong $dataProduct
 */

echo "=== TEST MULTIPLE STYLES SCENARIO ===\n\n";

// Mô phỏng dữ liệu CSV
$csvData = [
    ['STYLE' => 'STYLE_A', 'COLOR' => 'RED'],
    ['STYLE' => 'STYLE_A', 'COLOR' => 'BLUE'],
    // Thiếu STYLE_B từ product data
];

// Mô phỏng dữ liệu Product (2 styles khác nhau)
$productData = [
    ['style' => 'STYLE_A', 'color' => 'RED'],
    ['style' => 'STYLE_A', 'color' => 'BLUE'], 
    ['style' => 'STYLE_B', 'color' => 'GREEN'], // Thiếu trong CSV
    ['style' => 'STYLE_B', 'color' => 'YELLOW'], // Thiếu trong CSV
];

echo "CSV Data:\n";
foreach ($csvData as $row) {
    echo "- Style: {$row['STYLE']}, Color: {$row['COLOR']}\n";
}

echo "\nProduct Data:\n";
foreach ($productData as $product) {
    echo "- Style: {$product['style']}, Color: {$product['color']}\n";
}

echo "\n=== EXPECTED RESULTS WITH FIXED LOGIC ===\n\n";

echo "Valid CSV:\n";
echo "- STYLE_A_RED: ✅ (có trong product data)\n";
echo "- STYLE_A_BLUE: ✅ (có trong product data)\n";
echo "Count: 2\n\n";

echo "Valid Product:\n";
echo "- STYLE_A_RED: ✅ (có trong CSV)\n";
echo "- STYLE_A_BLUE: ✅ (có trong CSV)\n";
echo "Count: 2\n\n";

echo "Invalid CSV:\n";
echo "- Không có lỗi CSV\n";
echo "Count: 0\n\n";

echo "Invalid Product:\n";
echo "- STYLE_B_GREEN: ❌ (không có trong CSV)\n";
echo "- STYLE_B_YELLOW: ❌ (không có trong CSV)\n";
echo "Count: 2\n\n";

echo "=== PROBLEM WITH OLD LOGIC ===\n";
echo "❌ Chỉ xử lý style cuối cùng (STYLE_B)\n";
echo "❌ Bỏ qua STYLE_A trong product data\n";
echo "❌ Tất cả CSV rows với STYLE_A bị báo lỗi\n";
echo "❌ Không có invalid product data\n\n";

echo "=== SOLUTION WITH NEW LOGIC ===\n";
echo "✅ Xử lý tất cả styles trong product data\n";
echo "✅ Tạo đúng product style-color combinations\n";
echo "✅ Validation chính xác cho từng combination\n";
echo "✅ Phân loại đúng valid/invalid cho cả CSV và Product\n\n";

echo "=== NEW LOGIC FLOW ===\n";
echo "1. Thu thập tất cả styles và colors từ product data\n";
echo "2. Tạo tất cả style-color combinations từ product data\n";
echo "3. Validate CSV rows với tất cả combinations\n";
echo "4. Validate product combinations với CSV data\n";
echo "5. Phân loại chính xác valid/invalid cho cả hai nguồn\n";

?>
