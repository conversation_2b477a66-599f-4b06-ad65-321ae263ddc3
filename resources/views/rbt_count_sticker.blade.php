<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
            margin-left: 8px;
        }

        .date {
            font-size: 20pt;
            position: absolute;
            top: 40px;
            left: 230px;
            font-weight: normal;
        }

        .barcode {
            font-size: 27pt;
            position: absolute;
            top: 120px;
            left: 230px;
            font-weight: bold;
        }
        .qr {
            margin: auto;
            position: absolute;
            top: 40px;
            left: 30px;
        }
        .page {
            position: relative;
            display: flex;
            background: #f9f9f9;
            border-radius: 6px;
            font-family: sans-serif;
        }

    </style>
</head>

<body>
    <div>
        <?php
        $total = count($items);
        ?>
        @foreach ($items as $key => $item)
            <div class="page">
                <div class="date">
                    <span>
                    {{ $item->date }}
                    </span>
                </div>
                <div class="barcode">
                    <span>{{ $item->counter }}</span>
                </div>
                <div class="qr">
                    {!! $item->barcode_qr !!}
                </div>
            </div>


            @if ($key + 1 < $total)
                <div class="page-break"></div>
            @endif
        @endforeach
    </div>
</body>

</html>
