<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
            margin-left: 8px;
        }

        .date {
            font-size: 10pt;
            position: absolute;
            top: 10px;
            left: 10px;
            font-weight: normal;
        }

        .barcode {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 27pt;
            font-weight: bold;
        }
        .qr {
            margin: auto;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
        }
        .page {
            position: relative;
            display: flex;
            background: #f9f9f9;
            border-radius: 6px;
            font-family: sans-serif;
        }

    </style>
</head>

<body>
    <div>
        <?php
        $total = count($items);
        ?>
        @foreach ($items as $key => $item)
            @php
                    $len = strlen((string)$item->counter);
                    if ($len === 1) {
                        $fontSize = '105pt';
                        $right = '80px';
                    } elseif ($len === 2) {
                        $fontSize = '95pt';
                        $right = '10px';
                    } elseif ($len === 3) {
                        $fontSize = '65pt';
                    } else {
                        $fontSize = '48pt';
                    }
            @endphp
            <div class="page">
                <div class="date">
                    <span>
                    {{ $item->date }}
                    </span>
                </div>
                <div class="barcode"
                     style="
                        font-size: {{ $fontSize }};
                        right: {{ $right ?? '10px' }};
                    ">
                    <span>{{ $item->counter }}</span>
                </div>
                <div class="qr">
                    {!! $item->barcode_qr !!}
                </div>
            </div>


            @if ($key + 1 < $total)
                <div class="page-break"></div>
            @endif
        @endforeach
    </div>
</body>

</html>
