<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three Images Layout</title>
    <style>
        @page {
            margin: 0;
        }

        body {
            margin: 0;
            padding: 0;
            position: relative;
        }

        .image {
            position: absolute;
            width: 4800px;
            height: 7200px;
        }

        .corner-x {
            width: 50px;
            height: 1px;
            background-color: #343131; /* Màu đen */
            position: absolute;
        }

        .corner-y {
            width: 1px;
            height: 50px;
            background-color: #343131; /* Màu đen */
            position: absolute;
        }

        .corner-wrap-x {
            width: 216px;
            height: 8.34px;
            background-color: black; /* Màu đen */
            position: absolute;
        }

        .corner-wrap-y {
            width: 8.34px;
            height: 216px;
            background-color: black; /* Màu đen */
            position: absolute;
        }
    </style>
</head>
<body>
<div class="corner-wrap-x top-left"
     style="position: absolute; left: {{ $cornerPositions['top_left']['x_start'] }}px; top: {{ $cornerPositions['top_left']['x_end'] }}px;"></div>
<div class="corner-wrap-y top-left"
     style="position: absolute; left: {{ $cornerPositions['top_left']['y_start'] }}px; top: {{ $cornerPositions['top_left']['y_end'] }}px;"></div>

{{--<!-- Top-right corner -->--}}
<div class="corner-wrap-x top-right"
     style="position: absolute; left: {{ $cornerPositions['top_right']['x_start'] }}px; top: {{ $cornerPositions['top_right']['x_end'] }}px;"></div>
<div class="corner-wrap-y top-right"
     style="position: absolute; left: {{ $cornerPositions['top_right']['y_start'] }}px; top: {{ $cornerPositions['top_right']['y_end'] }}px;"></div>

{{--<!-- Bottom-left corner -->--}}
<div class="corner-wrap-x bottom-left"
     style="position: absolute; left: {{ $cornerPositions['bottom_left']['x_start'] }}px; top: {{ $cornerPositions['bottom_left']['x_end'] }}px"></div>
<div class="corner-wrap-y bottom-left"
     style="position: absolute; left: {{ $cornerPositions['bottom_left']['y_start'] }}px;top: {{ $cornerPositions['bottom_left']['y_end'] }}px"></div>

{{--<!-- Bottom-right corner -->--}}
<div class="corner-wrap-x bottom-right"
     style="position: absolute; left: {{ $cornerPositions['bottom_right']['x_start'] }}px; top: {{ $cornerPositions['bottom_right']['x_end'] }}px;"></div>
<div class="corner-wrap-y bottom-right"
     style="position: absolute; left: {{ $cornerPositions['bottom_right']['y_start'] }}px; top: {{ $cornerPositions['bottom_right']['y_end'] }}px;"></div>

@foreach($data as $image)
    @if($isRotateImage)
        @if($isRotateQr)
            <div class="qr-code"
                 style="position: absolute;
                left: {{$image['left'] - 50}}px;
                top: {{$topQr - 250}}px;
                width: 1800px;
                height: 210px;
                transform: rotate(90deg); transform-origin: left top">
                <div>
                    <div style="display: inline-block; text-align: center; font-size: 50px;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="display: inline-block;">
                        <div
                            style="margin-left: 0; font-size: 25px; color: #343131; font-weight: normal">{{ $image['label'] }}</div>
                        <div
                            style="margin-left: 0; font-size: 25px; color: #343131; font-weight: normal">{{ $name }}</div>
                    </div>
                </div>
            </div>
        @else
            <div class="qr-code"
                 style="position: absolute;
                left: {{ $image['leftQr']}}px;
                top: {{$topQr }}px;
                width: 1800px;
                height: 210px;
               }">
                <div>
                    <div style="position: absolute; bottom: 10px;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="position: absolute; bottom: 10px;">
                        <div
                            style="margin-left: 110px; font-size: 25px; color: #343131; font-weight: normal">{{ $image['label'] }}</div>
                        <div
                            style="margin-left: 110px; font-size: 25px; color: #343131; font-weight: normal">{{ $name }}</div>
                    </div>
                </div>
            </div>
        @endif
    @else
        @if($isRotateQr)
            <div class="qr-code"
                 style="position: absolute;
            left: {{$image['left'] - 50}}px;
            top: {{$topQr - 250}}px;
            width: 1800px;
            height: 210px;
            transform: rotate(90deg); transform-origin: left top">
                <div>
                    <div style="display: inline-block; text-align: center; font-size: 50px;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="display: inline-block;">
                        <div
                            style="margin-left: 0; font-size: 25px; color: #343131; font-weight: normal">{{ $image['label'] }}</div>
                        <div
                            style="margin-left: 0; font-size: 25px; color: #343131; font-weight: normal">{{ $name }}</div>
                    </div>
                </div>
            </div>
        @else
            <div class="qr-code"
                 style="position: absolute;
            left: {{ $image['leftQr']}}px;
            top: {{$topQr }}px;
            width: 1800px;
            height: 210px;
           }">
                <div>
                    <div style="position: absolute; bottom: 10px;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="position: absolute; bottom: 10px;">
                        <div
                            style="margin-left: 110px; font-size: 25px; color: #343131; font-weight: normal">{{ $image['label'] }}</div>
                        <div
                            style="margin-left: 110px; font-size: 25px; color: #343131; font-weight: normal">{{ $name }}</div>
                    </div>
                </div>
            </div>
        @endif
    @endif
    <div style="
            width: {{ $image['width_area_die_cut'] }}px;
            height: {{ $image['height_area_die_cut'] }}px;
            left: {{$image['left']}}px;
            top: {{$topImage}}px;
            position: absolute;">
        <img src="{{$image['image']}}" alt="Image 1" class="image"
             style="position: absolute;
            top: 50%;
            left: 50%;
            width: {{ $image['image_width'] }}px;
            height: {{ $image['image_height'] }}px;
            {{ $isRotateImage ? 'transform: translate(-50%, -50%) rotate(90deg);' : 'transform: translate(-50%, -50%);' }}">

    </div>
@endforeach

</body>
</html>
