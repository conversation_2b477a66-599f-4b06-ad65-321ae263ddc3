<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap" rel="stylesheet">
    <style>
        @page {
            margin: 0px;
        }

        html {
            font-family: 'Roboto', sans-serif;
        }

        html,
        body {
            margin: 0px;
            padding: 0px;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
            margin: 0;
        }

        @media print {
            body {
                width: 21cm;
                height: 29.7cm;
                /* margin: 30mm 45mm 30mm 45mm; */
            }
        }

        p {
            font-weight: 500;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        td,
        th {
            text-align: left;
            border: 2px solid #aeb9c4;
            padding: 8px;
        }

        .payment td,
        th {
            border-top: none;
        }
    </style>
</head>

<body>
    <div>
        <div class="header" style="clear:both; position:relative; margin: 8px 16px;">
            <div class="logo" style="
                    position:absolute;
                    left:0;
                    background: black;
                    width: 165px;
                ">
                <div style="
                        background: white;
                        border-radius: 50%;
                        padding: 25px;
                        width: 115px;
                        height: 115px;
                    ">
                    <img src="{{ public_path() . '/logo.png' }}" width="100" style="position: absolute; top: 20px; left: 32px;" />
                </div>
            </div>
            <div class="company" style="margin-left: 173px; margin-top: 50px;">
                <div style="font-weight: 700; font-size: 28px; line-height: 28px;">SwiftPOD LLC</div>
                <div style="font-weight: 700; font-size: 18px; line-height: 18px;">SWIFT PRINT ON DEMAND</div>
            </div>
        </div>
        <div class="title" style="clear:both; position:relative; margin: 8px 16px;">
            <div style="width: 67%; float: left;">
                <h2 style="font-weight: 700; font-size: 48px;">&nbsp;</h2>
                <p>SwiftPOD LLC</p>
                <p>2070 S 7TH ST STE E</p>
                <p>SAN JOSE, CA 95112</p>
                <p>408.712.8986 <EMAIL></p>
            </div>
            <div style="width: 33%; float: left;">
                <h2 style="font-weight: 700; font-size: 48px;">INVOICE</h2>
                <div style="position:relative;">
                    <div style="position: absolute; left: 0;">
                        <p>Date:</p>
                        <p>Invoice #:</p>
                        <p>Period:</p>
                    </div>
                    <div style="margin-left: 50%;">
                        <p>{{ $invoice->end_at->format('M d, Y')}}&nbsp;</p>
                        <p>{{ $invoice->invoice_number }}&nbsp;</p>
                        <p>{{ $invoice->period }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div style="clear:both; position:relative; border-top: 2px solid #aeb9c4; height: 20px; margin: 0 4px;"></div>
        <div class="info" style="position:relative; margin: 8px 16px;">
            <div style="width: 67%; float: left;">
                <p>BILL TO:</p>
                <p>{{ $invoice->bill_to_name }}</p>
                <p>ATTN: {{ $invoice->store->billing_contact }}</p>
                <p>{{ $invoice->bill_to_street1 }}</p>
                <p>{{ $invoice->bill_to_city }}, {{ $invoice->bill_to_state }}, {{ $invoice->bill_to_zip }}, {{ $invoice->bill_to_country }} </p>
                <p>{{ $invoice->bill_to_email }}</p>
            </div>
            <div style="width: 33%; float: left;">
                <p>BILL DUE:</p>
                <p>{{ $invoice->created_at->addDays($invoice->store->days_until_bill_due)->toFormattedDateString() }}</p>
            </div>
        </div>
        <div class="description" style="clear:both; margin: 8px 16px; padding: 20px 0;">
            <table border="1">
                <thead>
                    <th>DESCRIPTION</th>
                    <th>UNITS</th>
                    <th>PRICE</th>
                    <th>AMOUNT</th>
                </thead>
                <tbody>
                    <tr>
                        <td>EMBELLISHED GARMENTS</td>
                        <td>{{ $invoice->total_items }}</td>
                        <td>${{ number_format($invoice->total_items > 0 ? $invoice->total_prices_without_handling / $invoice->total_items : 0, 2) }}</td>
                        <td>${{ number_format($invoice->total_prices_without_handling, 2) }}</td>
                    </tr>
                    <tr>
                        <td>SHIPPING</td>
                        <td>{{ $invoice->total_shipping }}</td>
                        <td>${{ number_format($invoice->total_shipping > 0 ? $invoice->total_shipping_prices / $invoice->total_shipping : 0, 2) }}</td>
                        <td>${{ number_format($invoice->total_shipping_prices, 2) }}</td>
                    </tr>
                    @if(isset($invoice->total_peak_order))
                        <tr>
                            <td>PEAK SHIPPING SURCHARGE</td>
                            <td>{{ $invoice->total_peak_order }}</td>
                            <td>MULTIPLE</td>
                            <td>${{ number_format($invoice->total_peak_shipping_fee, 2) }}</td>
                        </tr>
                    @endif

                    @if($invoice->total_quantity_handling)
                        <tr>
                            <td>HANDLING</td>
                            <td>{{ $invoice->total_quantity_handling }}</td>
                            <td>${{ number_format($invoice->total_quantity_handling > 0 ? $invoice->total_prices_handling / $invoice->total_quantity_handling : 0, 2) }}</td>
                            <td>${{ number_format($invoice->total_prices_handling, 2) }}</td>
                        </tr>
                    @endif
                    @if(isset($invoice->surcharge_fee))
                        @foreach($invoice->surcharge_fee as $surcharge)
                            <tr>
                                <td>{{strtoupper($surcharge['name'])}}</td>
                                <td>{{ $surcharge['quantity'] }}</td>
                                <td>${{ number_format($surcharge['fee'], 2) }}</td>
                                <td>${{ number_format($surcharge['total'], 2) }}</td>
                            </tr>
                        @endforeach
                    @endif
                    @if($invoice->has_insert_invoice)
                        <tr>
                            <td>INSERT</td>
                            <td>{{ $invoice->total_inserts }}</td>
                            <td>${{ number_format($invoice->total_inserts > 0 ? $invoice->total_price_inserts / $invoice->total_inserts : 0, 2) }}</td>
                            <td>${{ number_format($invoice->total_price_inserts, 2) }}</td>
                        </tr>
                    @endif
                    <tr>
                        <td colspan="4" style="text-align: right;">
                            SUB TOTAL = ${{ number_format($invoice->total_prices_without_handling + $invoice->total_prices_handling + $invoice->total_shipping_prices + $invoice->total_price_inserts + $invoice->total_peak_shipping_fee + $invoice->total_surcharge_fee, 2) }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="payment" style="position:relative;">
                <div style="width: 67%; float: left;">
                    <p>Bank of America</p>
                    <p>222 Broadway</p>
                    <p>New York, NY 10038</p>
                    <p>Account #: ************</p>
                    <p>Routing ACH/EFT #: *********</p>
                    <p>Routing Domestic Wire #: *********</p>
                    <p>SWIFT/BIC Core: BOFAUS3N</p>
                    <p>PingPong: <EMAIL></p>
                    <p>Payoneer: <EMAIL></p>
                    <p>LianLian: <EMAIL></p>
{{--                    <p><EMAIL> (with 3% charge)</p>--}}
                </div>
                <div style="width: 33%; float: left;">
                    <table>
                        <tbody>
                        @if(!empty($invoice->total_promotion))
                            <tr>
                                <td>PROMOTION</td>
                                <td>${{ number_format($invoice->total_promotion, 2) }}</td>
                            </tr>
                        @endif
                            <tr>
                                <td>TAX RATE</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SALES TAX</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>OTHER</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>TOTAL</td>
                                <td>${{ number_format($invoice->total_prices_without_handling + $invoice->total_prices_handling + $invoice->total_shipping_prices + $invoice->total_surcharge_fee + $invoice->total_peak_shipping_fee + $invoice->total_price_inserts - $invoice->total_promotion, 2) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="footer" style="clear: both; padding-top: 30px; text-align: center;">
            <div style="text-align: center;">
                <p>Total due in {{ $invoice->store->days_until_bill_due > 0 ? $invoice->store->days_until_bill_due : 7 }} days. Overdue accounts subject to a service charge of 3% per month.</p>
            </div>
            <div style="margin-top: 32px; font-weight: 700; font-size: 28px;">THANK YOU FOR YOUR BUSINESS!</div>
        </div>
    </div>
</body>

</html>
