<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
            margin-left: 8px;
        }

        .info-order {
            top: 10px;
            font-size: 11pt;
            position: absolute;
            width: 97%;
        }

        .order-date {
            position: absolute;
            top: 0;
            right: 0px;
            font-size: 19px;

        }

        .counter {
            font-size: 12pt;
        }

        .store {
            position: absolute;
            font-size: 14pt;
            top: 38px;
        }

        .code-store {
            position: absolute;
            font-size: 8.5pt;
            top: 50px;
        }

        .status {
            top: 76px;
            position: absolute;
            max-width: 168px;
        }

        .status span {
            vertical-align: middle;
            background: black;
            color: #ffffff;
            padding: 5px 0 5px 5px;
            text-align: center;
            border: 1px solid #000000;
            font-size: 11pt;
        }

        .status span + .tiktok {
            padding-top: 5px;
        }

        .status .tiktok {
            padding-top: 1px;
            vertical-align: middle;
            height: 35px;
            width: auto;
        }

        .product {
            max-width: 240px;
            position: absolute;
            bottom: 15px;
        }

        .style {
            font-size: 10pt;
        }

        .area-top {
            position: absolute;
            height: 48px !important;
        }

        .area-bottom {
            position: absolute;
            height: 48px !important;
        }

        .area-top span {
            border: 1px solid #000000;
            padding: 2px;
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 17pt;
            font-weight: bolder;
            display: inline-block;
            line-height: 45px;
            z-index: 9999;
        }

        /*using for $method class*/
        .DTG {
            background: #ffffff;
            color: #000000;
        }

        .DTF {
            background: #ffffff;
            color: #000000;
            border-radius: 50%;
        }

        .EMB {
            background: #ffffff;
            color: #000000;
        }

        .NECK {
            background: #ffffff;
            color: #000000;
            border-radius: 50%;
        }

        .FILM {
            background: #ffffff;
            color: #000000;
            border-radius: 50%;
        }

        .UV {
            color: #ffffff;
            background: #000000;
        }

        .MUGS {
            color: #ffffff;
            background: #000000;
            border-radius: 50%;
        }

        /*end using for $method class*/

        .area-bottom span {
            border: 1px solid #000000;
            padding: 2px;
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 17pt;
            font-weight: bolder;
            display: inline-block;
            line-height: 45px;
            z-index: 9999;
        }

        .hexagon {
            position: relative;
            background-color: black;
            width: 44px;
            height: 26px;
            color: white;
            text-align: center;
            display: inline-block;
            margin: 10px 2px 10px 0px;
        }

        .triangle-top {
            position: absolute;
            left: 0;
            width: 0;
            height: 0;
            top: -50%;
            border-left: 22px solid white;
            border-right: 22px solid white;
            border-bottom: 18px solid black !important;
        }

        .triangle-bottom {
            position: absolute;
            left: 0;
            width: 0;
            height: 0;
            bottom: -50%;
            border-left: 22px solid white;
            border-right: 22px solid white;
            border-top: 18px solid black !important;
        }

        .emb-text {
            border: none !important;
            margin: auto;
            position: relative;
            top: -4pt !important;
            font-size: 14pt !important;
        }

        .info-qr {
            position: absolute;
            /*left: 320px;*/
            top: 35px;
            right: 15px;
        }

        .platen {
            border: 1px solid black;
            /*position: absolute;*/
            /*right: 15px;*/
            /*top: 200px;*/
            text-align: center;
            z-index: 9999;
            padding: 0px 2px;
            width: auto;
            display: inline-block;

        }

        .pretreat {
            border: 1px solid black;
            /*position: absolute;*/
            /*right: 90px;*/
            /*top: 200px;*/
            text-align: center;
            z-index: 9999;
            padding: 0px 2px;
            width: auto;
            display: inline-block;

        }

        .hexa {
            border: 1px solid black;
            background: black;
            color: #ffffff;
            /*position: absolute;*/
            /*right: 158px;*/
            /*top: 200px;*/
            text-align: center;
            z-index: 9999;
            padding: 0px 3px;
            width: auto;
            display: inline-block;
        }

        .item-num {
            text-align: center;
            z-index: 9999;
            font-size: 12pt;
        }

        .qr {
            margin-top: 0px;
        }

        .bk {
            /*position: absolute;*/
            /*left: 259px;*/
            /*top: 100px;*/
            width: auto;
            display: inline-block;

        }

        .bk span {
            background: black;
            color: #ffffff;
            padding: 0px 3px;
            text-align: center;
            z-index: 9999;
            border: 1px solid #000000;
            width: auto;
            display: inline-block;

        }

        .label-line {
            display: inline-block;
            /*gap: 8px;*/
            /*align-items: center;*/
        }

        .wip-urgent {
            display: table;
            width: 99%;
            height: 100%;
        }

        .wip-urgent .wip-urgent_content {
            height: 100%;
            font-size: 40px;
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }
    </style>
</head>

<body>
<div>
    <?php
    /** @var array $items */
    $total = count($items);
    ?>

    @foreach ($items as $key => $item)
            <?php
            $subLabel = substr($item->label_id, 7);
    ?>
        <div class="info-order">
            <div class="label-id">{{ $subLabel }}</div>
            <div class="order-date">
                <span style="font-size: 11pt">{{ $item->order_date_formatted ?? '' }}</span>
                @if (!empty($item->isThankYouCardOrder))
                    <img
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAeCAYAAABE4bxTAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAO+SURBVFhHzZdJKH1hGMbNmZKN2cZ8LVAWSkimlBApC4mlIksshBQZFlZYiLIwFEpImRaUFMmcjJfCgkwbmT163/rO/9xzD133utf/qadzvvPd+53ffd/3G64V/jNZKR/8tXSAPj4+8P7+bjGrSQIiGEuL3ql8LwOJyFxcXGB2dhbT09OYmZkxm6emprCysqIHw0D08PX1FYODg/D394eNjQ1sbW1NNo2jZtFvbW2NgoICXF9fM4iA4wgdHBzAz88PLi4uSE5ORm5uLrKzs01yVlYWMjMz9Ux9NH5wcDAD1tTUMIioKY7QxMQE7O3tkZOTg+fn53/xM0EU9aenJx3TMyEqC4pSSkoK3t7e+BmxMND4+DgcHBxQVFQkG/LnEkV6c3ODubk5DAwMsKkc+vr6uD7v7u74M9vb2wwUFxfHsOL7OkCFhYU6g/9E4vMPDw+ora2Fp6cnnJycuAxcXV3h7OwMR0dHtLa24uXlBWtrawwUHx8vZeXXgchUjwkJCXBzc0NeXh5KSkpQWlqK/Px8hoqJiWHozc1NywDt7e0xUFhYGFZXV6ViXV9fh5eXFyIjIxloa2vLMkD7+/sSEKVFjEMR8fb2RlRUlPFAav5KSqDQ0FAsLCzg9vYW9/f3WFpa4royKUJfWU2i7+joiKcyRaOsrAwNDQ1obm5GeXk5F3dsbKxxNURrA/26y8tLXF1dSaZfK9YNNVE/LXaUMoqIsIeHBwICAlBfX88AVFMGA9ECtrOzg7q6OlRUVKCqqgqVlZXstrY27vtOx8fH6O3t5Sne0tLCbmpqQnd3N87Pz/nFPwJ6fHzE6OgoAgMDeWb4+PhwCuieQk59yjTKUynu6So/bog2aWNjw3AgSolWq0VPTw/a29vR2dmJjo4Ovu/v7+cIKGHUgJQyGohEaaOaoRDTsUSY2mqmLUGttpTARgHR0r68vMyrLO1vxcXFkqmt5sbGRhweHspQ1CVfKA0CojZtdmNjY9BoNPD19eVzEh1P1Ex9VGN0UqCVWZlGpUUUvwWil4vdntri9Eigw8PDGBkZ+dZDQ0OYn5/nXV4JoLRBKaOziZ2dHZKSknhKn56eclHT1VCfnJwYZJoQVHNdXV0MRIc2+YGfT4xnZ2eIjo6Gu7s7EhMTkZaWhtTUVL7+tmnc9PR0hIeHcxBo5oroMJDI6+TkJDIyMhASEsLHS3M6KCgIERERqK6u5p1ApFOKEIkKeXd3F4uLi7wpmtNUbzQBBIxcOv/L5IVnbpPEVS69v9LKWWEufyU9oL/WJ+aTv9MgFUVjAAAAAElFTkSuQmCC">
                @else
                    <span>{{ $item->ink_color_detected_at }}</span>
                @endif
                
            </div>
        </div>
        <div class="code-store">
            @if (in_array($item->store_id, [\App\Models\Store::STORE_REDBUBBLE,\App\Models\Store::STORE_BNFR_API,
                                            \App\Models\Store::STORE_SCALEFUL,\App\Models\Store::STORE_D2_AMERICA,
                                             \App\Models\Store::STORE_D2_LICENSED, \App\Models\Store::PRINTIFY_API_ID]))
                {{ $item->store_code ?? 'N/A' }}
            @endif
            @if (isset($item->is_rbt) && isset($item->location_barcode))
                <span>{{ $item->location_barcode }}</span>
            @endif
        </div>

        <div class="status">
            @if ($item->is_fba)
                <span class="fba">FBA</span>
            @endif
            @if ($item->is_xqc)
                <span class="xqc">XQC</span>
            @endif
            @if ($item->is_eps)
                <span class="xqc">EPS</span>
            @endif
            @if (isset($item->order_type) && in_array($item->order_type, [\App\Models\SaleOrder::ORDER_TYPE_LABEL_ORDER, \App\Models\SaleOrder::ORDER_TYPE_TIKTOK_ORDER]))
                <img class="tiktok"
                     src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAYAAAA5ZDbSAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAABXSSURBVHhe7Z0LfFTVncd/984rM5nJZCYvCUkgQFTEtfiAwrZYxbqKirUF1ygoYK2tgm1V7MfW7W63q3YRo1ZAdPHJ6oKvWgVXWbU+a4CAPIwRCI8QkpBA3plM5pGZs/9z54RMQgIzyZ1kBu6Xz2HuOXPn5t7zO4//eV6JEYgBdXV12Lx5M4qLi1FeXo6KigpUVVWhsbERPp9PnHV6YTQakZaWhpycHOTn56OgoABTpkzBpEmTkJWVJc5SGS6wWpSWlrI5c+Ywh8PBE43monA8zubOncvKyspEbKqDKgIvX76cybLc541rLnqn1+vZ008/LWJ3cAxK4KKioj5vUHPquWXLlonYHhgDEpjq1T5vRnOxcyUlJSL2oyNqgQsLC/u8Ac3F3s2bN0+oEDkRW9Gtra3Izc1VPjWGD26FHzp0CGazWYScmIgE5s0bLq5G/FBdXY3s7Gzh6x9ZfPbLpk2bNHHjkJEjR2Lr1q3C1z8nzMG8KMjLyxM+jXiktrb2hJ0k/QrM61q73S58GvFMe3s7LBaL8PWk3yJaK5YThxOVsn0KfOONN2rWcgLR0NCABQsWCF8veBEdjtaJkbiOjC6hYjfH1cGSJImjUwyjERlFjwCBToqKHo98YuhUOSUFjUuWwl++VwTGJ1y7YDAofAJFZsHSpUuPpYZTzcnJyWw8PeNZLEguEIXrZBPod5YrLu/zuvHm+MBPOD1y8CmbewnJbMGZ7S0IdrjARCqP9Gl1VieqbypE25pXRUh8EyZpt5FFyoujUx+ekLlrouMqFkT1Sdwh+NA2/uzQjxOAVatWiSN61q4cfCrnXk54DuZ1cDO5+4xJuFpvgicsxfeJTo/AZ5vhufrnkDLTYJRklHpa8NPqzeKE+EKv18Pv9yvHisA7duzAxIkTlYBTld4CH6Wc+azJilkGkzjjJLR1AOfMBlJtVO7psNfbgoLd74ov44+ysjKMHz8+VEQvWbJECTzdCKXxCLGZgXEjEfT7gGAnbJJOfBGfPPIItRgIReD33ntP8WichGsvBjo8dMDg0CeFwuKU9evXK5/y4cOH0dzcrHg0Tgy7ey6k+lalPWLUGZGjj2xMdjior6/HkSNHIPNprRqRoZihs6eDtVN9TEbptOQMJTxeKSkpgbxlyxbh1YgE6dF7ILlI4IAfCxz5IjQ+4fPS5b1747v7Le7ISAVuuRpoasbljlEiMD7Zs2cP5H379gmvRqRIj94NZtADvk7cGse5eP/+/ZD5rA2N6JE2rABqavDvmeeKkPiDG9AyH0vUGAAFeWCvL0HOEQ9uSInPyRF8HZh83PCSRsRIl00GHrsLa6WxIiS+8Hq9/U/Z0YiQ+TOBvz6G1/UFIiC+0ARWg0vOx+x9xZidMlIExA+awGqRl4bXW6pw3i03i4D4QBNYZXa8tBrTKg9Qrp4mQoYXTeAY8FnuaNz48WewuVvguPvX0GWfIb4Zenj36klGu08N+hoPXmmyojDS8eABsCrowx2yAXkUzVLVAXhLtsH95Rfw7ixFoKEegdoj6KyuFmerDx/41wSOocCcVvo7/+xx4XOJIcNoRJJsomKT/wOa338X1TOuCZ0YA7jAWhEdY1IkGe+bU/ChwYIxHj/2tDWisa0BbnjhP3pUnBU7NIGHiKk6A/5mSUGD1YlfGc0YAR3a+BztGKMJPMQ4KEf/1mTB36FH3ajz8FryBDx4xnlYkDoas2LQjtYEHkZslKuvt47EA1nn4vlR38cbZ80U36iHJvCwwhAMBsACfgQDHsDfIcLVQxM4nojB3HRN4FMcTeBIefwVMDefMptYaAJHSvFOYPTVYO9/KQISA03gSEk2g9mSgTkPgE2dD7brgPgivolvgU0mJF9zFdL/9DBGvrsOo7ZtRv6B3RhTU4kxVfsxurwMucWfI2vVM+IHMUaWwLIzwBpbIP3gduDaexEoPyi+jE/iTmDT+RORueJJFLhbcI7Hg9x17yLt/vuQfOVlME04B/rsEdA5U6FLT4chLxfmKZNgv3mO+HXs4XauTAnvSyvDqo1/g27qz+E7vxCu1zfA63bHXcd+3Aic+su7MOZQBcZ8tQ2OO++kmGTwuRrRSS7oagXr8IDxjcT9nUBnyHF/0ONFwK1++/FkyNSkuf3odlzl2wmjj8F6z3IYz5yF9p/cjeZX1qO1rRUBcW6/yLGP/mEXOPXuX+EsxnDGn5+ELsMJf3sjAq4WIBA8tlC7q30YpPM6yDWyII6Qq6f80kRuOPKNLrSQBe+1HYZU9hr+6D8IyZEK667DSL1/BawjZsCVPwNHbliM2keex5F3P0b9t+VobWiCy+VS7tnn9UKKscj8LoelVDGeOwE5H7wP4xk5lAOblW0Vei9C52vTeXc8j4xGOs6QZFyuN2CyrMd4nR755HfKOsiNzZDJwg2OyoaXcnY768SNlcUo7uieEjzo4cIFf0Dwi22Qkkx0nzJK3I2YvO8D8WU3D2X9A3434jv0B0k4HzWreKlDpQx8fvjpOTqpHg8a9GA6GXqDASZ6HqVup3+ywQpp24viSoNn2IYLMx5dioKvS6Fz2JUimEd4b3HdFFZHD51JKfw3RgvqrU7UkHspyYaFRjOm6wzIJ3H5XnzmIEMy5SkbRWq63ohRyRnI0Md2nLc/Hqj7GtL2lzF3/6fY7qWSyGIF0tKBTLIZqIRKIvvBYrPCSgnORAJzcWPJkAucv3cP0u5dDC8XlnJbb2F5Ebw3GMA0Eq/YbMcmSyruJUHtPEf0QyddIsjzAP2WUc4E/T5A/uHklZaDOH/vBjhLX8VtB/+O4vajVK4bIZlskPiyU8q5EpVEvDSgioiEiI0UQyaw7HTgLL8HhjG5VMc2HidsgMSpJmG+S0VvfbITayx2TKDjRKcp4MdzTfvxj/s/grTjFeTsXIObDnyCZfV7sMFViz3eVjR0etHGyCSjOFCboRGY6pwzGxopT3Ui2O4+ZjQp0DO10YNRzYj3zCl4g5xzCKzL4aK604M1rYfwy5qtuJKEPmvPe0jf9TZSvnkT8vaXxFnqEfuYpHrmLDI2An6X0qTpkXNJ3FoqXKeRMVBH9evFVH+ezsSiUom5wONqDpGF7APz+nqIy+vLvVQs/ScZUK8lpYhQDbWJqcAj334LuvRMBN0dx4l7mNyHZETdSQaURuyImcDJM6+G7drrEHQ1HSfuPrJ0PyIjajpvA2rElJgJnPvOeqUDo6dBRTmXapoNZEhxa1kj9sRE4KxnnyEZqSXaa+1xDeXcP1Cde7naxlR4ItLogeoC67Ky4Pzp7VTvtvYomnlT6FJq6C8eYJ3LLUxWVQe8/yXY6vWK48eobQj9nRj3CCUqqguc8cgSavh0Uu7tNvr5IAH3rbNEby3zsVd272OhfSIvnAs2/9+A+59UHD9mEwthuvQXkLLje8+q4UJ1gZ23zEOgPSz3krI19N/LSdaQPwrYnX8Csq8Ee+tjugL9y0gl5wBLo0/u+HF6KoJ+v9J5H0pGGuGoKrB94Z2hMdCwePaQ5wJJhx9GU+8ebQIbPRN493OwglxIyWZIOp2SaJRkQyUCv3FZkiHrDJANSZD15HQmOo934HcN5mmoKrDjlwsR9LmO5V7eJOL7Mj9vjiL37qogUa8DMxvBUqw9hOLXUzrmDVSPU6L5qqMZj9fvxh2HinHD/k+x8NBGPFm/B5XuBoQWTmrwWFClXJPtdhQ0N5Nx1XTsij4SpIBy0wfU5o0Uln4pWKZTybHhKHLpTfD5PVhUsw2rmqLbwG2oxoPjCVXHg61XXxVKLWHGVR1F5COmZOE7OeyyO8Ac9n7ETcK/krCmb96IWtzTGdUENk+/RLGeuwpGbjmnUlF9fqQdGp9sBduxm3JIz7paqXX1Zoz95i/4jyPfiFCNSFFNYMv0y8B8oW12OW3kFkaxep4tpqYQ3+gzDOVKsh5pO9div79dCdOIDtUENuaPBTq75xHymRk/jlRg3tb9tgIS1RnHoN/DaMMFu/8XjUGfCNSIFtUE5hfq6prkszP4BLkJlPsiga39PyC92xBTrGVqVq2s3YltXm03+sGgisCS03Gs7uXwmZBn9zKU+kMxyUrKIPEJaAI+55jn4EU12mblg0UVgfUpth5tLf42kwupeRQJ/HeeOj4hLexWKOc/3VBORpvGYFGniO41rst7ry6MYjhQ9vEkIcoAXvfqjHji6K6QXzXouvzapxmqCMy8XnEUgq+iHRPFe4WClqRjka/0ggU7sdvHp+GpiJ7uR7EJBihyBz2jaCEkEqoIHGhq7FkHUxxyIysS+FnBLCf9JwpkisR9HvVfTq3PyqJ75HW7CIgS1tRy+grMXKHVQV190Px/exSR0fm970DydDWFZBzwqS+w5dLpyie30AcCq6NEHG4nJAiq3XEgQEWYmM/Mo1AfhcD6WT9EgNrCXQRisAt9ytxCBAMdPUqaqLSubTz2fImEanfs2bI11NShWOOR6OVLSCIk2WZF29QJkLzc2GKwx2Bdke3i6Qh2dPe0cW1TI50F0kL2QK8J+9E833CimsDujz8JrbkRtEZZFAYeXohgPR+JYpiQ1LPLcrBkrlyBgNI674aPiYyNNEdu+ZbK+PB3FUpoDkT1asthQzWBXW+/Q48dSuF6+uDznqPB+d2JaLzyu3QhN2wmmwgdPIaCcXD+4k4E29uO2Qi8py2VjMCCSHva1n8GWC3CR9B1KhKkb1y9InrTJmrdUANJpwPPx+V8MVUU8KhP/p8laOqgiPN4UJiaF/pikIze/hUCvMkVluB43ruAzxAJeU/OlzsAo0gM/DqUOL71JEYXqmoC80rN/cFHkExGmCjqNnZGX4TxhCHtfguejlY8aB/8K9VH798FyWIMLcIOqz/5OPXvDWE58gSwihqwA9U9x6hlA750J8b7ptQTmGhY+ih0shm8X6s0GF0O7iLVbod/51qMtTsxTbnSwBhbWw1T/lgEXVQi9DCOGMZTDrwwfOTqRPDpuWYy+rquIT62n3Y5mOggQyvgd0NPqb2MBO4MKxajwWa1gu1Yg3VFq0RI5DjuuRtn09/VOVMQcPWcm81nXTbQd9FM32XLX4PE98cS8KXaB93UZEoQVBWYc/Q3v4PBbIeLInJrsKflGg1cFvs987C6tgZ4+SUYJ5wT+qIPdCPOQPrDD2Fccz2yih5DwN0E5vP3EJdTQe3rPxvMyI10IOTVDRRDdI2u6/AEqzNgbXNFyJ8A8DsfWDY7AQVtTWgnU/pHTMZT5sFbxM9UV+GekTngZpevpgr+ykpIPJdmZMI4rgBcLt4MCnrcYH1sC8EfkO8e8C9GM34XzRyxc2YruT68/uVNwfxv3iArmtrFcU7MXsqRcvMcZK9+GYddDWi2ponQwfFm5UEUWgwYkWpHEt22si8N7/HqDK2BUiTtQ1g+slVF37+hS8IsviFKhLBn3gT7wzOQ0rvb5Pyv8hX6ObveESHxTUzfupJf/i2a8nPxXwE9rjeq0zMVaGrGdc1H8VmqDaYkM5JJOG4qhcvKH4abdy466ggEMYNJWE11blLYhIKTQr8LpkyDNCZsi30qMSQq3hdUfIEXmxNjn0pFYFmWKQOo3+2mS0tDfn09HB3N2GxWt2fqcEsLnvK4sIHa2l8bDQgI8bi4RiqiR1KuvjnJgptNFoweQOJi1/wKbHeFMge6CyUR8ZUTO15W/IlAcnIypIyMDHY0Rq93sc6cCcM772Btpxf/FKt9q7w+tAb8yhi0mZo/Nt78iSa39oI9uhps6WpImU4RQii5Nwl3HCzG0wk0Jzs3Nxcy/y9WuNatg+6F57AolpuSmYxIsSQjk5zNbB6UuNhaBvbHZ3uKS/CVDC6/J6HE5YwYMQLy2LGxfblx/a23ofLtt/C48Mctew6CXbGIkn2WCBDwppEhGRfv+1AEJA5jxoyBPG7cOOGNHd7rfoIHPvsEcdv3U9cI9r2fgmU6yCgJM9l40Uylz5sNe7AtQXquwjnzzDMhX3TRRcIbWzp+cCkmvPCs8MURm76m9u4sZe3xcWuiZB0Oe9swu/LvIiSxmDx5MuSpU6cKb+ypufVnmHXffcIXB7z6AdiVd4GdkXbcdBxl6JOs5lG71omQxGPSpEm8Q4gxh8OB5uahK4Lm33orXnjuOeEbHtj1vwH7qATITj+u56urSTSq7C+oTIAeq75Io2ZqPTVTlWQ7Y8YMJXCoePH55zH5/AuAtqHfqZ29/SlYwY/A+GqKkRk9xeV1LpfXaEPBt39NWHE5M6mJylFy8I4dOzBx4kQlYCjhvVDt9z8J429vA1K6p/vEhJJvwH79KNiuCiDr+CKZz7aUxQyPbBL3MJ+8kMCUlZVh/PjxIYF5QO9iaigpdU7BhJuuBxbOAs4eJULVga3dADz9Bv2RfWBpdkjGnu1kRVj+7Pok1JBBlUPixqTvdgjhXZR+f2jCxbFkvGzZMnE09JzbuBEPvPQU8P2fkcCzwX6/Eox3OvCt8KOEz19WduUp/C2Y4xLKtUVgtQ1gI6iu7UtcPnSoN+Ohw9sx8hQQl7NixQpxJIpocTysuZiTTm3Oj0f9AOfKqUBrkzIPio0aAYzOhjSaPnMyAacDzKiD5A8A/G0rXNDqI0BFDXCA3NHQKwKYNRkwGY57Jv64PER5GYbegn3uesw48AnK1V4qM4yESdpT4KKiIixevFj4ho8Lk1KxJu/7KDA7AH/oxRbBAAnKF5gH+MAIv2WSiXdKKGuOSCzehtXTJz8mUXsnVUVYHs5zrM6Ive5G3FW9Be+7asUZpwa8JF60aJHw9RKYM9y5OJzxphQ8lHUefuzMpxsj4QI+UooEJsdvWtn4rMfdd6OIGTqg/3gCMCrXWN+0Hw/WlWJTR+JMu4kU/sy9RwaPE3jTpk2YMmWK8MUP05MzcUPqaFxhzUSe0UpWcNhI8LFVBtxPjn8oKaATVV4XPu1owH837sMGVx0/6ZRl69atuOACan6GcZzAnJtuuglr1qwRvviDz1PINViQb7Iij+pRB+VOA6VePz0K38+jjop1/rKLms4OeBJkiclgmT9/Pl544QXh66ZPgTl2ux2treqv8tNQH6fTiYaGvudp9yswF5eLrBH/tLe3w2LpeyI/WR99k5KSgsrKSuHTiFdqa2v7FZfTr8AcPttj48aNwqcRb2zZsgVZWb0mKPSi3yI6nEOHDiEvT53FYBrqUF1djezsbOHrnxPm4C54TuZ1Mi+2NYYXPgzodrsjEpcTkcAcm82GlpYWFBYWihCNoWbevHnKGK+ZTy6MFF5ER0txcbHSjaC5oXMlJSUi9qNjQAJ3UVRU1OfNaE4998QTT4jYHhiDEriL5cuXM75Coq8b1Fz0Tq/Xs5UrV4rYHRyqCNxFaWkpmzNnDp/j1eeNa65/x+Ns7ty5rKysTMSmOkTUTBoIdXV12Lx5M6i+Rnl5OSoqKlBVVYWmpiZ4e219eLpgNBoVKzgnJwf5+fkoKChQBnb47MeTtWcHBvD/gLnOG5sOqloAAAAASUVORK5CYII="
                     alt="">
            @endif
        </div>
        <div class="info-product">
            <div class="product">
                <div
                        class="style">{{ (isset($item->product_type) && $item->product_type != 'Canvas') ? $item->style_formatted : ($item->product_style_sku ?? '') }}</div>
                <div class="size">{{ $item->size_formatted }}</div>
                <div class="color">{{ $item->color_formatted }}</div>
            </div>

            {{--  ben phai wip  --}}
                <?php
        $areaTop = [];
    $areaBottom = [];
    $areas = $item->print_side_formatted;

    if (count($areas) <= 2) {
        $areaTop = array_slice($areas, 0, 1, true);
        $areaBottom = array_diff_key($areas, $areaTop);
    } elseif (count($areas) <= 4) {
        $areaTop = array_slice($areas, 0, 2, true);
        $areaBottom = array_diff_key($areas, $areaTop);
    } else {
        $areaTop = array_slice($areas, 0, 3, true);
        $areaBottom = array_diff_key($areas, $areaTop);
    }
    ?>

            <div class="area-top"
                 style="left: {{ 168 + (3 - count($areaTop)) * 52 }}px; top: {{ count($areas) <= 2 ? 75 : 85 }}px">
                @foreach ($areaTop as $area => $method)
                    @if($method != 'EMB')
                        <span class="{{ $method }}">{{ $area }}</span>
                    @endif
                    @if($method == 'EMB')
                        <div class="hexagon">
                            <div class="triangle-top"></div>
                            <div class="triangle-bottom"></div>
                            <span class="emb-text">
                                    {{$area}}
                                </span>
                        </div>
                    @endif
                @endforeach
            </div>
            <div class="area-bottom" style="left: {{ 168 + (3 - count($areaBottom)) * 52 }}px; top: 150px">
                @foreach ($areaBottom as $area => $method)
                    @if($method != 'EMB')
                        <span class="{{ $method }}">{{ $area }}</span>
                    @endif
                    @if($method == 'EMB')
                        <div class="hexagon">
                            <div class="triangle-top"></div>
                            <div class="triangle-bottom"></div>
                            <span class="emb-text">
                                    {{$area}}
                                </span>
                        </div>
                    @endif
                @endforeach
            </div>

            @if(!empty($item->is_hard_good_and_shirt))
                <div style="position: absolute; left: 212px; top: 142px">
                    <img
                            style="height: 40px"
                            src="data:image/png;base64,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"
                            alt="">
                </div>
            @endif

            @if(!empty($item->shirt_quantity))
                <div
                        style="position: absolute; left: {{ 232 - (strlen(trim($item->shirt_quantity)) * 9) }} px; top: 205px; font-size: 32px">
                    {{ $item->shirt_quantity }}
                </div>
            @endif


            <div class="info-qr">
                <div class="item-num">{{ $item->barcode_number . '/' . $item->order_quantity }}</div>
                <div class="qr">{!! $item->barcode_qr !!}</div>
            </div>
            <div style="display: inline-block; position: absolute; top: 210px; right: 0; margin-right:10px;">
                <div class="bk">
                    @if ($item->ink_color == 1)
                        <span>BK</span>
                    @endif
                </div>

                @if (!is_null($item->pretreat_name))
                    <div class="pretreat">{{ $item->pretreat_name }}</div>
                @endif

                @if ($item->isHexa)
                    <div class="hexa">HEXA</div>
                @endif

                @if ($item->isLargePlaten)
                    <div class="platen">16x21</div>
                @endif

                @if (isset($item->isCustomPlaten16x18) && $item->isCustomPlaten16x18)
                    <div class="platen">16x18</div>
                @endif
            </div>
        </div>

        @if(empty($item->is_rbt) && !empty($item->label_urgent))
            <div class="page-break"></div>
            <div class="wip-urgent">
                <div class="wip-urgent_content">{{ $item->label_urgent }}</div>
            </div>
        @endif

        @if ($key + 1 < $total)
            <div class="page-break"></div>
        @endif
    @endforeach
</div>
</body>

</html>
