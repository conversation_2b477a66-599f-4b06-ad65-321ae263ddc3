<?php 
    use App\Models\SaleOrderClaimSupport;
    $listIssue = SaleOrderClaimSupport::getIssueArray();
    switch ($details['solution']) {
        case 'refund':
            $solution = 'Request Refund';
            break;
    
        case 'replacement':
            $solution = 'Request Replacement';
            break;
    
        case 'no_need':
            $solution = 'No Need';
            break;
    
        default:
        $solution = 'No Need';
        break;
    }
    
?>

<p>Dear Support Team,</p>
<p>A customer has submitted a claim:</p>
<p><b>Order #: </b> {{ $details['saleOrder']['external_number'] }}</p>
<p><b>Store: </b> {{ $details['store']['name'] }}</p>
<p><b>Customer email: </b> {{ $details['customer_email'] }}</p>
@if($details['type'] == '2')
    <p><b>Issue: </b> My Item(s) Need Some Care</p>
@else 
    <p><b>Issue: </b> My Order is Lost in Transit</p>
@endif

@if($details['type'] == '2')
    <p><b>Item issue: </b> {{ $listIssue[$details['issue']] }}</p>
@endif

<p><b>Preferred resolution: </b>  {{ $solution }}</p>

@if($details['type'] == '2')
<p><b> Order item has problems: </b></p>
    @foreach ($details['saleOrderItem'] as $items)
    {{ $items['external_id'] }} - {{ $items['sku'] }}<br>
    @endforeach
@endif
<p><b>Additional details: </b> {{ $details['additional_details'] }}</p>
<p>Please review and assist promptly.</p>
@if($details['type'] == '2')
    @foreach ($details['images'] as $attachmentUrl)
        <img height="150" width="250" src="{{ $attachmentUrl['link_url'] }}" alt="Attachment">
    @endforeach
@endif
<br>
@if(isset($details['feedback']))
<?php 
    switch ($details['feedback']['status']) {
        case 'new':
            $classStatus = 'New';
            break;
        case 'in_review':
            $classStatus = 'in Review';
            break;
        case 'approve':
            $classStatus = 'Approve';
            break;
        case 'rejected':
            $classStatus = 'Rejected';
            break;
        case 'closed':
            $classStatus = 'Closed';
            break;
        default:
        $classStatus = 'New';
    
        break;
}
?>
<p>Thank you for the feedback</p>
<p>Your Claim Ticket have been {{ $classStatus }}</p>
<p><b>Here is our feedback regarding the claim: </b> {{ $details['feedback']['feedback'] }}</p>
    @if(isset($details['feedbackFiles']))
        @foreach ($details['feedbackFiles'] as $attachmentUrl)
            <img height="150" width="250" src="{{ $attachmentUrl['file'] }}" alt="Attachment">
        @endforeach
    @endif

@endif
