@extends('emails.topup.layout')

@section('content')

<div class="content">
    <strong>Hi {{ $invoice->store->name }},</strong>
    @if ($invoice->status === \App\Models\Invoice::STATUS_PAID)
        <p>Your invoice for the production period {{ Carbon\Carbon::parse($invoice->start_at)->format('m/d/Y') }} to {{
            Carbon\Carbon::parse($invoice->end_at)->format('m/d/Y') }} has been paid successfully. Thank you for your
            payment.
        </p>
    @else
        <p>We're pleased to inform you that your payment has been successfully processed.</p>
    @endif
   
    <strong>Transaction details:</strong>
    <ul>
        <li><strong>Invoice number:</strong> {{ $invoice->invoice_number }}</li>
        <li><strong>Invoice total:</strong> ${{ number_format($invoice->total_amount, 2) }}</li>
        <li><strong>Amount paid:</strong> ${{ number_format($invoice->transaction_total_amount, 2) }}</li>
        <li><strong>Remaining balance:</strong>
            ${{ number_format($invoice->amount, 2) }}</li>
        <li><strong>Payment method:</strong> {{ $invoice->payment_method }}</li>
    </ul>
    @if ($invoice->status != \App\Models\Invoice::STATUS_PAID)
        <p>To complete your payment, please click the button below:</p>
        <table role="presentation" style="width: 100%; text-align: center; margin: 0 auto;">
            <tr>
                <td>
                    <a href="{{ config('app.frontend_url').'/invoices' }}" style="display: inline-block; background-color: #6c63ff; color: white; padding: 10px 20px; 
                                                border: none; border-radius: 5px; text-decoration: none; font-size: 16px; 
                                                text-align: center; cursor: pointer;">
                        Pay remaining balance
                    </a>
                </td>
            </tr>
        </table>
    @endif
    <p>If you have any questions or concerns, please do not hesitate to contact us.</p>
    <p>Thank you for your business.</p>
    <p>Best regards,<br>SwiftPOD</p>
</div>

@endsection