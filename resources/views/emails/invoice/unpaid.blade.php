@extends('emails.topup.layout')

@section('content')

<div class="content">
    <strong>Hi {{ $invoice->store->name }},</strong>
    <p>I hope this message finds you well.</p>
    <p>Your invoice for the production period {{ Carbon\Carbon::parse($invoice->start_at)->format('m/d/Y') }} to {{
        Carbon\Carbon::parse($invoice->end_at)->format('m/d/Y') }} has been generated and is now available for your
        review.</p>
    <p> The payment is due by {{
        Carbon\Carbon::parse($invoice->created_at)->addDays($invoice->days_until_bill_due)->format('m/d/Y') }}.</p>
    <ul>
        <li><strong>Invoice number:</strong> {{ $invoice->invoice_number }}</li>
        <li><strong>Total amount:</strong> ${{ number_format($invoice->amount, 2) }}</li>
    </ul>
    <table role="presentation" style="width: 100%; text-align: center; margin: 0 auto;">
        <tr>
            <td>
                <a href="{{ config('app.frontend_url').'/invoices' }}" style="display: inline-block; background-color: #6c63ff; color: white; padding: 10px 20px; 
                                  border: none; border-radius: 5px; text-decoration: none; font-size: 16px; 
                                  text-align: center; cursor: pointer;">
                    View and Pay Invoice
                </a>
            </td>
        </tr>
    </table>
    <p>You can review your invoice and complete the payment directly on our seller system.</p>
    <p>Thank you for your business.</p>
    <p>Best regards,<br>SwiftPOD</p>
</div>

@endsection