<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">

    <title>Detect Color</title>
</head>
<body>

<div class="container">
    <h1>Detect Color</h1>
    <form method="post">
        <div class="mb-3">
            <label for="exampleFormControlInput1" class="form-label">Link Image</label>
            <textarea value="<?php echo $url; ?>" type="url" class="form-control" id="url" name="url"
                      placeholder="http"></textarea>
            <input type="hidden" name="_token" value="<?php echo $token; ?>"/>

        </div>
        <button class="btn btn-primary">Upload</button>

    </form>



    <div class="my-3"><h4>Black Ink</h4> <br>
        <?php foreach ($black as $item): ?>
            <a style="display: block; width: 150px; height: 170px; float:left; " class="m-2"
               href="/storage/detect_org/<?php echo $item; ?>" target="_blank"> <img style=" max-width: 150px"
                    src="/storage/detect/<?php echo $item; ?>" class="border mb-3"> </a>
        <?php endforeach; ?>
    </div>
    <hr style="clear: both">
    <div class="my-5"><h4>White Ink</h4> <br>
        <?php foreach ($white as $item): ?>
            <a style="display: block; width: 150px; height: 170px; float:left;" class="m-2"
               href="/storage/detect_org/<?php echo $item; ?>" href="/storage/detect_org/<?php echo $item; ?>"
               target="_blank"> <img style="background: darkblue; max-width: 150px"
                                     src="/storage/detect/<?php echo $item; ?>"
                                     class="border mb-3">
            </a>
        <?php endforeach; ?>
    </div>
    <br>
    <hr>
    <div class="my-3" style="clear: both"><h4>All Ink</h4> <br>
        <?php foreach ($mix as $item): ?>
            <a style="display: block;width: 150px; height: 170px; float:left;" class="m-2"
               href="/storage/detect_org/<?php echo $item; ?>" href="/storage/detect_org/<?php echo $item; ?>"
               target="_blank"> <img
                    style="background: chartreuse; max-width:150px;" src="/storage/detect/<?php echo $item; ?>" class="border mb-3">
            </a>
        <?php endforeach; ?>
    </div>

</div>

<!-- Optional JavaScript; choose one of the two! -->

<!-- Option 1: Bootstrap Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>

<!-- Option 2: Separate Popper and Bootstrap JS -->
<!--
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js" integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js" integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF" crossorigin="anonymous"></script>
-->
</body>
</html>
