<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Calibri Light', sans-serif;
            src: local('Calibri Light'), url('../fonts/calibril.ttf') format('woff');
        }

        .corner-x {
            width: 50px;
            height: 1px;
            background-color: #343131; /* Màu đen */
            position: absolute;
        }

        .corner-y {
            width: 1px;
            height: 50px;
            background-color: #343131; /* Màu đen */
            position: absolute;
        }

        .corner-wrap-x {
            width: 200px;
            height: 8.34px;
            background-color: black; /* Màu đen */
            position: absolute;
        }

        .corner-wrap-y {
            width: 8.34px;
            height: 200px;
            background-color: black; /* Màu đen */
            position: absolute;
        }
    </style>
</head>

<body style="width: 18750px; background-color: white;
    @if(isset($heightPage))
        height: {{$heightPage}}px;
    @endif
">
<div class="file" style="width: 100%">
    <?php
    $lastItem = end($data);
    ?>
    <div class="corner-wrap-x top-left"
         style="position: absolute; left: {{$data[0]['leftPs'] - 90}}px; top: {{$data[0]['topPs'] - 90}}px;"></div>
    <div class="corner-wrap-y top-left"
         style="position: absolute; left: {{$data[0]['leftPs'] - 93}}px; top: {{$data[0]['topPs'] - 90}}px;"></div>

    <!-- Top-right corner -->
    <div class="corner-wrap-x top-right"
         style="position: absolute; left: {{$dieCutParentWidth - 105 + 87}}px; top: {{$data[0]['topPs'] - 90}}px;"></div>
    <div class="corner-wrap-y top-right"
         style="position: absolute; left: {{$dieCutParentWidth + 105 + 78}}px; top: {{$data[0]['topPs'] - 90}}px;"></div>

    <!-- Bottom-left corner -->
    <div class="corner-wrap-x bottom-left"
         style="position: absolute; left: {{$data[0]['leftPs'] - 90}}px; top: {{$lastItem['topPs'] + $lastItem['height_area_die_cut'] + 83}}px"></div>
    <div class="corner-wrap-y bottom-left"
         style="position: absolute; left: {{$data[0]['leftPs'] - 93}}px;top: {{$lastItem['topPs'] + $lastItem['height_area_die_cut'] - 200 + 90}}px"></div>

    <!-- Bottom-right corner -->
    <div class="corner-wrap-x bottom-right"
         style="position: absolute; left: {{$dieCutParentWidth - 105 + 87}}px; top: {{$lastItem['topPs'] + $lastItem['height_area_die_cut'] + 83}}px;"></div>
    <div class="corner-wrap-y bottom-right"
         style="position: absolute; left: {{$dieCutParentWidth + 105 + 78}}px; top: {{$lastItem['topPs'] + $lastItem['height_area_die_cut'] - 200 + 90}}px;"></div>

    @foreach ($data as $image)
        <div class="die-cut" style="position: absolute; width: {{ $image['width_area_die_cut'] }}px; height: {{ $image['height_area_die_cut'] }}px;
         top: {{ $image['topPs']}}px; left: {{ $image['leftPs']}}px; text-align: center;">
            <!-- Top-left corner -->
            <div class="corner-x top-left" style="position: absolute; top: -10px; left: -10px;"></div>
            <div class="corner-y top-left" style="position: absolute; top: -10px; left: -10px;"></div>

            <!-- Top-right corner -->
            <div class="corner-x top-right" style="position: absolute; top: -10px; right: -10px;"></div>
            <div class="corner-y top-right" style="position: absolute; top: -10px; right: -10px;"></div>

            <!-- Bottom-left corner -->
            <div class="corner-x bottom-left" style="position: absolute; bottom: -10px; left: -10px;"></div>
            <div class="corner-y bottom-left" style="position: absolute; bottom: -10px; left: -10px;"></div>

            <!-- Bottom-right corner -->
            <div class="corner-x bottom-right" style="position: absolute; bottom: -10px; right: -10px;"></div>
            <div class="corner-y bottom-right" style="position: absolute; bottom: -10px; right: -10px;"></div>


            <div class="qr-code"
                 style="position: absolute;margin-top: 45px; left: 50%; transform: translateX(-50%); width: 1800px; text-align: center;">
                <div
                    style="position: absolute; bottom: 0; left: 50%; transform: translateX(-50%); width: 100%; height: 100%;">
                    <div style="display: inline-block; text-align: center; font-size: 50px; margin-right: 30px; color: #666666; font-weight: normal">
                        {{ $image['itemNumber'] }}
                    </div>
                    <div style="display: inline-block; text-align: center; font-size: 70px;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="display: inline-block;">
                        <div style="margin-left: 50px; font-size: 75px; color: #666666; font-weight: normal">{{ $image['label'] }}</div>
                        <div style="margin-left: 40px; font-size: 50px; color: #666666; font-weight: normal">{{ $name }}</div>
                    </div>
                    @if($image['isHardGoodAndShirt'])
                        <div style="display: inline-block;">
                            <img class="img-t-shirt"
                                 style="height: 160px"
                                 src="data:image/png;base64,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"
                                 alt="">
                        </div>
                    @endif
                    @if($image['shirtQuantity'])
                        <div style="display: inline-block; font-size: 80px; line-height: 150px; vertical-align: middle; color: #666666">
                            {{$image['shirtQuantity']}}
                        </div>
                    @endif
                </div>
            </div>
            <div class="max-print" style="position: absolute; width: {{ $image['width_area_max_print'] }}px; height: {{ $image['height_area_max_print'] }}px;
             bottom:  {{ $image['bottom'] }}px; left: 50%; transform: translateX(-50%); background-color: {{ is_null($image['background_color']) ? '' : $image['background_color'] }};">
                <div class="file" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                 width: {{ $image['width_area_safe'] }}px; height: {{ $image['height_area_safe'] }}px; text-align: center;
                 background-color: {{ is_null($image['background_color']) ? '' : $image['background_color'] }};">
                    <img src="{{ $image['image'] }}" alt="" class="image"
                         style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scaleX(-1);
                     width: {{ $image['width'] }}px; height: {{ $image['height'] }}px;">
                </div>
            </div>
        </div>
    @endforeach
</div>
</body>
</html>
