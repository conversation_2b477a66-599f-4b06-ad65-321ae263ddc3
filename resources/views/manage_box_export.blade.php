<table>
    <thead>
        <tr class="border">
            <th style="border: 1px solid black; width: 150px; text-align: center; background-color: #ede19f;">SKU</th>
            <th style="border: 1px solid black; width: 350px; text-align: center; background-color: #ede19f;">Product Name</th>
            <th style="border: 1px solid black; width: 200px; text-align: center; background-color: #ede19f;">Location</th>
            <th style="border: 1px solid black; width: 150px; text-align: center; background-color: #ede19f;">Box Quantity</th>
            <th style="border: 1px solid black; width: 150px; text-align: center; background-color: #ede19f;">Item Quantity</th>
            @if ($is_admin)
                <th style="border: 1px solid black; width: 150px; text-align: center; background-color: #ede19f;">Cost Value</th>
            @endif
        </tr>
    </thead>
    <tbody>
        @foreach($data as $item)
            <tr style="border: 1px solid black;">
                <th style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">{{ $item['product_sku'] }}</th>
                <td style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">{{ $item['product_name'] }}</td>
                <td style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">{{ $item['location'] }}</td>
                <td style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">{{ $item['box_quantity'] }}</td>
                <td style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">{{ $item['item_quantity'] }}</td>
                @if ($is_admin)
                    <td style="border: 1px solid black; text-align: center; font-weight: bold; background-color: #bfbfbf">${{ $item['cost_value'] }}</td>
                @endif
            </tr>
            @foreach($item['location_items'] as $location)
                <tr style="border: 1px solid black;">
                    <th style="border: 1px solid black; text-align: center;"></th>
                    <td style="border: 1px solid black; text-align: center;"></td>
                    <td style="border: 1px solid black; text-align: center;">{{ $location['barcode'] }}</td>
                    <td style="border: 1px solid black; text-align: center;">{{ $location['box_quantity'] }}</td>
                    <td style="border: 1px solid black; text-align: center;">{{ $location['item_quantity'] }}</td>
                    @if ($is_admin)
                        <td style="border: 1px solid black; text-align: center;">${{ $location['cost_value'] }}</td>
                    @endif
                </tr>
            @endforeach
        @endforeach
    </tbody>
</table>
