<?php

/**
 * Example: Error Classification in Validation Response
 * Minh họa cách phân loại lỗi trong response mới
 */

echo "=== EXAMPLE: ERROR CLASSIFICATION ===\n\n";

// Mô phỏng response từ API với lỗi phân loại
$validationResponse = [
    'valid' => [
        'STYLE_A_RED' => [/* valid data */]
    ],
    
    // Tất cả lỗi (để tương thích với code cũ)
    'invalid' => [
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'YELLOW'],
            'reason' => "Color 'YELLOW' is not in the allowed colors: RED, BLUE, GREEN.",
            'source' => 'csv'
        ],
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'BLUE'],
            'reason' => "Product data contains Style 'STYLE_A' and Color 'BLUE' but this combination is missing in CSV file.",
            'source' => 'product_data'
        ]
    ],
    
    // Lỗi từ CSV
    'invalidCsv' => [
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'YELLOW'],
            'reason' => "Color 'YELLOW' is not in the allowed colors: RED, BLUE, GREEN.",
            'source' => 'csv'
        ]
    ],
    
    // Lỗi từ Product Data
    'invalidProduct' => [
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'BLUE'],
            'reason' => "Product data contains Style 'STYLE_A' and Color 'BLUE' but this combination is missing in CSV file.",
            'source' => 'product_data'
        ]
    ],
    
    'countValid' => 1,
    'countInvalid' => 2,        // Tổng số lỗi
    'countInvalidCsv' => 1,     // Số lỗi từ CSV
    'countInvalidProduct' => 1, // Số lỗi từ Product Data
    'status' => false
];

echo "Response Structure:\n";
echo json_encode($validationResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

echo "\n\n=== USAGE EXAMPLES ===\n\n";

echo "1. Kiểm tra có lỗi không:\n";
echo "   Status: " . ($validationResponse['status'] ? 'PASS' : 'FAIL') . "\n";
echo "   Total errors: " . $validationResponse['countInvalid'] . "\n\n";

echo "2. Phân tích lỗi theo nguồn:\n";
echo "   CSV errors: " . $validationResponse['countInvalidCsv'] . "\n";
echo "   Product data errors: " . $validationResponse['countInvalidProduct'] . "\n\n";

echo "3. Hiển thị lỗi CSV:\n";
foreach ($validationResponse['invalidCsv'] as $error) {
    echo "   - " . $error['reason'] . "\n";
}

echo "\n4. Hiển thị lỗi Product Data:\n";
foreach ($validationResponse['invalidProduct'] as $error) {
    echo "   - " . $error['reason'] . "\n";
}

echo "\n=== BENEFITS ===\n";
echo "✅ Phân loại rõ ràng nguồn gốc lỗi\n";
echo "✅ Dễ dàng thống kê và báo cáo\n";
echo "✅ Tương thích với code cũ (vẫn có 'invalid' và 'countInvalid')\n";
echo "✅ Giúp người dùng hiểu rõ cần sửa gì ở đâu\n";

?>
