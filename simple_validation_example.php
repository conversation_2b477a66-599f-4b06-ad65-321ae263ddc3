<?php

/**
 * <PERSON><PERSON> dụ đơn giản sử dụng các hàm validation mới
 */

// D<PERSON> liệu dataProduct như bạn cung cấp
$dataProduct = [
    [
        "sku" => "UNPT3W24M",
        "style" => "3001",
        "color" => "ASH",
        "size" => "24MO",
        "gtin" => "",
        "gtin_case" => 1
    ],
    [
        "sku" => "UNPT3W24M",
        "style" => "3002",
        "color" => "ASH",
        "size" => "24MO",
        "gtin" => "",
        "gtin_case" => 1
    ]
];

// Dữ liệu cần kiểm tra
$data = [
    ['STYLE' => '3001', 'COLOR' => 'ASH'],    // Hợp lệ
    ['STYLE' => '3002', 'COLOR' => 'ASH'],    // Hợp lệ
    ['STYLE' => '3003', 'COLOR' => 'RED'],    // Không hợp lệ
    ['STYLE' => '3001', 'COLOR' => 'BLUE'],   // Không hợp lệ
];

// <PERSON><PERSON>ch sử dụng 1: <PERSON><PERSON><PERSON> tra từng dòng đơn giản
echo "=== Cách 1: Kiểm tra đơn giản ===\n";
$productRepository = new \App\Repositories\ProductRepository();

foreach ($data as $index => $row) {
    $isValid = $productRepository->isValidStyleAndColor($row, $dataProduct);
    echo "Dòng " . ($index + 1) . ": Style {$row['STYLE']}, Color {$row['COLOR']} - " . 
         ($isValid ? "HỢP LỆ" : "KHÔNG HỢP LỆ") . "\n";
}

echo "\n";

// Cách sử dụng 2: Kiểm tra với thông báo lỗi chi tiết
echo "=== Cách 2: Kiểm tra với thông báo lỗi ===\n";

foreach ($data as $index => $row) {
    $result = $productRepository->validateSingleRowStyleColor($row, $dataProduct);
    echo "Dòng " . ($index + 1) . ": ";
    if ($result['valid']) {
        echo "HỢP LỆ\n";
    } else {
        echo "LỖI - " . $result['error'] . "\n";
    }
}

echo "\n";

// Cách sử dụng 3: Kiểm tra toàn bộ mảng
echo "=== Cách 3: Kiểm tra toàn bộ mảng ===\n";

$fullResult = $productRepository->validateDataWithProductArray($data, $dataProduct);

if ($fullResult['has_errors']) {
    echo "Có {$fullResult['total_errors']} lỗi trong {$fullResult['total_errors'] + $fullResult['total_valid']} dòng:\n";
    foreach ($fullResult['errors'] as $error) {
        echo "- Dòng " . ($error['row_index'] + 1) . ": {$error['error']}\n";
    }
    echo "Số dòng hợp lệ: {$fullResult['total_valid']}\n";
} else {
    echo "Tất cả {$fullResult['total_valid']} dòng đều hợp lệ!\n";
}

/**
 * Kết quả mong đợi:
 * 
 * === Cách 1: Kiểm tra đơn giản ===
 * Dòng 1: Style 3001, Color ASH - HỢP LỆ
 * Dòng 2: Style 3002, Color ASH - HỢP LỆ
 * Dòng 3: Style 3003, Color RED - KHÔNG HỢP LỆ
 * Dòng 4: Style 3001, Color BLUE - KHÔNG HỢP LỆ
 * 
 * === Cách 2: Kiểm tra với thông báo lỗi ===
 * Dòng 1: HỢP LỆ
 * Dòng 2: HỢP LỆ
 * Dòng 3: LỖI - Style '3003' và Color 'RED' không tồn tại trong danh sách sản phẩm
 * Dòng 4: LỖI - Style '3001' và Color 'BLUE' không tồn tại trong danh sách sản phẩm
 * 
 * === Cách 3: Kiểm tra toàn bộ mảng ===
 * Có 2 lỗi trong 4 dòng:
 * - Dòng 3: Style '3003' và Color 'RED' không tồn tại trong danh sách sản phẩm.
 * - Dòng 4: Style '3001' và Color 'BLUE' không tồn tại trong danh sách sản phẩm.
 * Số dòng hợp lệ: 2
 */

/**
 * Cách sử dụng trong Controller thực tế:
 */
class ExampleController
{
    public function validateImportData(Request $request)
    {
        // Lấy dataProduct từ request
        $dataProduct = json_decode($request->input('dataProduct'), true);
        
        // Lấy data từ Excel hoặc form
        $data = $request->input('data');
        
        $productRepository = app(\App\Repositories\ProductRepository::class);
        
        // Cách 1: Validate toàn bộ
        $result = $productRepository->validateDataWithProductArray($data, $dataProduct);
        
        if ($result['has_errors']) {
            return response()->json([
                'success' => false,
                'message' => "Có {$result['total_errors']} lỗi trong dữ liệu",
                'errors' => $result['errors'],
                'valid_count' => $result['total_valid']
            ], 422);
        }
        
        // Xử lý dữ liệu hợp lệ
        return response()->json([
            'success' => true,
            'message' => "Tất cả {$result['total_valid']} dòng dữ liệu hợp lệ",
            'data' => $result['valid']
        ]);
    }
    
    public function validateSingleRow(Request $request)
    {
        $row = $request->input('row'); // ['STYLE' => '3001', 'COLOR' => 'ASH']
        $dataProduct = json_decode($request->input('dataProduct'), true);
        
        $productRepository = app(\App\Repositories\ProductRepository::class);
        $result = $productRepository->validateSingleRowStyleColor($row, $dataProduct);
        
        return response()->json($result);
    }
}
