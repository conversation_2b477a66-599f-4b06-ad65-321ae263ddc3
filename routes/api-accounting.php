<?php

use App\Http\Controllers\AuthAccountingController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\SaleOrderController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\TopupController;
use Illuminate\Support\Facades\Route;

Route::prefix('accounting')->group(function () {
    Route::post('/auth/login', [AuthAccountingController::class, 'login']);
    Route::post('/auth/google/login', [AuthController::class, 'loginWithGoogle']);
    Route::post('/auth/logout', [AuthAccountingController::class, 'logout']);

    Route::middleware('auth.accounting')->group(function () {
        Route::get('store/all', [StoreController::class, 'getAll']);
        // Topup
        Route::prefix('topups')->group(function () {
            Route::get('/', [TopupController::class, 'getListTopup']);
            Route::get('/count-pending', [TopupController::class, 'getPendingTopups']);
            Route::get('/count-total', [TopupController::class, 'getTotalTopups']);
            Route::get('/{id}', [TopupController::class, 'showTopup']);
            Route::put('/{id}', [TopupController::class, 'updateTopup']);
        });
        //Orders
        Route::prefix('orders')->group(function () {
            Route::get('', [SaleOrderController::class, 'getListOrderStorePrepaid']);
            Route::get('/detail/{id}', [SaleOrderController::class, 'getAccountingOrderDetail']);
            Route::get('/{id}', [SaleOrderController::class, 'getPrePaidOrderDetail']);
            Route::post('/{id}/refund', [SaleOrderController::class, 'refundPrePaidOrder']);
        });
    });
});
