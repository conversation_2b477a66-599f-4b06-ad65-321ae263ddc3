<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\AdjustPullingShelvesController;
use App\Http\Controllers\ApiShipStation\WebhookShipStation;
use App\Http\Controllers\AuthAppIpController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BacklogController;
use App\Http\Controllers\BackOrderController;
use App\Http\Controllers\BarcodeAssignController;
use App\Http\Controllers\BarcodeController;
use App\Http\Controllers\BarcodeEMBController;
use App\Http\Controllers\BarcodeLatexController;
use App\Http\Controllers\BoxController;
use App\Http\Controllers\BoxMovingController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BulbPowerController;
use App\Http\Controllers\ChangeLogController;
use App\Http\Controllers\ClaimController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CostReportController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\DetectColorController;
use App\Http\Controllers\DocCategoryController;
use App\Http\Controllers\DocController;
use App\Http\Controllers\DtfController;
use App\Http\Controllers\DtgToDtfController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\ExportHistoryController;
use App\Http\Controllers\FilterSaleOrderController;
use App\Http\Controllers\FirebaseController;
use App\Http\Controllers\ForecastController;
use App\Http\Controllers\ImportRouterName;
use App\Http\Controllers\InsertPrintingController;
use App\Http\Controllers\InsightAdjustmentController;
use App\Http\Controllers\InsightAdjustmentExportController;
use App\Http\Controllers\InsightAdjustmentSizeController;
use App\Http\Controllers\InsightAdjustmentStyleController;
use App\Http\Controllers\InsightLabelOrdersFulfillmentTimeController;
use App\Http\Controllers\InsightLateOrderController;
use App\Http\Controllers\InsightOffendersDepartmentController;
use App\Http\Controllers\InsightTiktokOrdersFulfillmentTimeController;
use App\Http\Controllers\InternalRequestController;
use App\Http\Controllers\InternalTicketController;
use App\Http\Controllers\InventoryAdditionController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\InventoryDeductionController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LabelController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\LicensedDesignController;
use App\Http\Controllers\LicensedHolderController;
use App\Http\Controllers\LicensedProductController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\LocationMachineController;
use App\Http\Controllers\MachineController;
use App\Http\Controllers\MaintenanceTrackingController;
use App\Http\Controllers\MissingScannedWIPController;
use App\Http\Controllers\Mug3DController;
use App\Http\Controllers\MugController;
use App\Http\Controllers\NeckController;
use App\Http\Controllers\OnHoldSettingController;
use App\Http\Controllers\OrnamentController;
use App\Http\Controllers\PartNumberController;
use App\Http\Controllers\PartNumberOverviewController;
use App\Http\Controllers\PaymentTermController;
use App\Http\Controllers\PeakShippingFeeController;
use App\Http\Controllers\PerformanceController;
use App\Http\Controllers\PerformanceReportController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\PosterController;
use App\Http\Controllers\PretreatPresetController;
use App\Http\Controllers\PretreatPresetSkuController;
use App\Http\Controllers\PrinterController;
use App\Http\Controllers\PrintingPresetController;
use App\Http\Controllers\PrintingPresetSkuController;
use App\Http\Controllers\ProductColorController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductionReportController;
use App\Http\Controllers\ProductMaterialThicknessController;
use App\Http\Controllers\ProductPrintAreaController;
use App\Http\Controllers\ProductPrintSideController;
use App\Http\Controllers\ProductRankHistoryController;
use App\Http\Controllers\ProductRouletteController;
use App\Http\Controllers\ProductSizeController;
use App\Http\Controllers\ProductSkuMatchingController;
use App\Http\Controllers\ProductSpecController;
use App\Http\Controllers\ProductStyleController;
use App\Http\Controllers\ProductStyleIccProfileController;
use App\Http\Controllers\ProductStyleSizeWeightController;
use App\Http\Controllers\ProductTiktokController;
use App\Http\Controllers\ProductTypeController;
use App\Http\Controllers\ProductTypeWeightController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\QualityControlController;
use App\Http\Controllers\Rbt\RbtSupportController;
use App\Http\Controllers\RbtCountStickerController;
use App\Http\Controllers\RbtSkuMovementController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\SaleOrderController;
use App\Http\Controllers\SaleOrderItemBarcodeController;
use App\Http\Controllers\SaleOrderItemController;
use App\Http\Controllers\SaleOrderItemImageController;
use App\Http\Controllers\SaleOrderReportController;
use App\Http\Controllers\ScreenClientController;
use App\Http\Controllers\ScreenDesignController;
use App\Http\Controllers\ScreenOrderController;
use App\Http\Controllers\ScreenOrderPackagingController;
use App\Http\Controllers\ScreenPackagingController;
use App\Http\Controllers\SellbriteSyncController;
use App\Http\Controllers\SellbriteWarehouseController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SettingMoveWarehouseController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\ShipmentExportationController;
use App\Http\Controllers\ShipmentManifestController;
use App\Http\Controllers\ShipmentMappingDeliveryController;
use App\Http\Controllers\ShipmentTransitController;
use App\Http\Controllers\ShippingCarrierController;
use App\Http\Controllers\ShippingCarrierPackageController;
use App\Http\Controllers\ShippingCarrierServiceController;
use App\Http\Controllers\ShippingMethodController;
use App\Http\Controllers\SkuBulbPowerController;
use App\Http\Controllers\StickerController;
use App\Http\Controllers\StockTransferController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\StoreProductController;
use App\Http\Controllers\StoreShipmentController;
use App\Http\Controllers\SupplyAdjustInventoryController;
use App\Http\Controllers\SupplyBoxController;
use App\Http\Controllers\SupplyCategoryController;
use App\Http\Controllers\SupplyController;
use App\Http\Controllers\SupplyInventoryAdditionController;
use App\Http\Controllers\SupplyInventoryController;
use App\Http\Controllers\SupplyInventoryDeductionController;
use App\Http\Controllers\SupplyLocationController;
use App\Http\Controllers\SupplyPurchaseOrderController;
use App\Http\Controllers\SupplyReportController;
use App\Http\Controllers\SupplyTestCountController;
use App\Http\Controllers\SupplyUnitController;
use App\Http\Controllers\SurchargeFeeController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\Tech\TechSupportController;
use App\Http\Controllers\TestCountAdjustmentController;
use App\Http\Controllers\TokenController;
use App\Http\Controllers\ToolOrderController;
use App\Http\Controllers\TrademarkController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\VisuaController;
use App\Http\Controllers\WareHouseController;
use App\Http\Controllers\WeightCubicController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('employee-performance-report', [PerformanceReportController::class, 'fetchEmployeePerformance'])->name('fetchEmployeePerformance');
Route::post('calculate-work-hour', [TechSupportController::class, 'calculateWorkedHour']);

Route::get('oos-test', [QualityControlController::class, 'test']);
Route::get('screen-order/{id}', [ScreenOrderController::class, 'getDetail']);

Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/app/login', [AuthAppIpController::class, 'login']);
Route::post('/auth/google/login', [AuthController::class, 'loginWithGoogle']);
Route::post('/app/update/ip', [AuthAppIpController::class, 'update']);

Route::post('/auth/register', [AuthController::class, 'register']);
Route::get('/product/attribute', [ProductController::class, 'getProductByAttribute']);
Route::any('/products/fetch-all', [ProductController::class, 'fetchProducts']);
Route::any('/products/attributes', [ProductController::class, 'getProductAttributeList'])->name('get_product_attribute_list');
Route::get('/products/fetch-product-by-attributes', [ProductController::class, 'fetchProductByAttribute']);
Route::prefix('store')->group(function () {
    Route::get('get-all', [StoreController::class, 'getAll']);
});
Route::group(['middleware' => 'jwt', 'prefix' => 'auth'], function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/user-profile', [AuthController::class, 'userProfile'])->name('auth_userProfile');
});

Route::group(['middleware' => ['jwt', 'log.api.access']], function () {
    Route::get('/back-log/fetch', [BacklogController::class, 'fetchBackLog']);
    Route::prefix('shipment-transit')->group(function () {
        Route::get('get-performance', [ShipmentTransitController::class, 'getPerformance']);
        Route::get('get-performance-by-zone', [ShipmentTransitController::class, 'getPerformanceByZone']);
    });
    Route::prefix('rbt-support')->group(function () {
        Route::get('/location', [LocationController::class, 'getRbtLocation']);
        Route::post('/dark-pod-to-rack', [RbtSupportController::class, 'darkPodToRack']);
        Route::get('get-sku-in-location', [RbtSupportController::class, 'getSKUInLocation']);

        Route::post('/generate-batch', [RbtSupportController::class, 'generateBatch']);
        Route::post('/wip/confirm-print', [RbtSupportController::class, 'confirmBarcodePrinted']);
    });
    Route::prefix('machine')->group(function () {
        Route::get('', [MachineController::class, 'getList'])->name('get_machine_list');
        Route::get('fetch-all', [MachineController::class, 'fetchAll'])->name('get_all_machine_list');
        Route::post('', [MachineController::class, 'create'])->name('create_machine');
        Route::put('{id}', [MachineController::class, 'update'])->name('update_machine');
        Route::delete('{id}', [MachineController::class, 'delete'])->name('delete_machine');
    });

    Route::prefix('custom-design')->group(function () {
        Route::get('', [ScreenDesignController::class, 'index']);
        Route::get('get-all', [ScreenDesignController::class, 'getAll']);
        Route::get('get-last-item', [ScreenDesignController::class, 'getLastItem']);

        Route::post('', [ScreenDesignController::class, 'store']);
        Route::put('{id}', [MachineController::class, 'update']);
        Route::delete('{id}', [MachineController::class, 'delete']);
    });

    Route::prefix('screen-client')->group(function () {
        Route::get('', [ScreenClientController::class, 'index']);
        Route::get('get-all', [ScreenClientController::class, 'getAll']);
        Route::get('{id}', [ScreenClientController::class, 'getDetail']);
        Route::post('', [ScreenClientController::class, 'store']);
        Route::put('{id}', [ScreenClientController::class, 'update']);
    });

    Route::prefix('screen-order')->group(function () {
        Route::get('import/history', [ScreenOrderController::class, 'importOrderHistories']);
        Route::post('import-csv', [ScreenOrderController::class, 'importOrders']);

        Route::get('', [ScreenOrderController::class, 'getList']);
        Route::get('get-count/by-status', [ScreenOrderController::class, 'getCountByStatus']);
        Route::get('{id}', [ScreenOrderController::class, 'getDetail']);
        //        Route::post('', [ScreenOrderController::class, 'store']);
        Route::put('{id}', [ScreenOrderController::class, 'updateOrder']);
        Route::put('update-address/{id}', [ScreenOrderController::class, 'updateAddress']);
    });

    Route::prefix('screen-packaging')->group(function () {
        Route::get('list-order-packaging', [ScreenPackagingController::class, 'getListPackagingOrder']);
        Route::post('', [ScreenPackagingController::class, 'store']);
        Route::get('', [ScreenPackagingController::class, 'getList']);
        Route::post('{id}', [ScreenPackagingController::class, 'edit']);
        Route::get('{id}', [ScreenPackagingController::class, 'getDetail']);
    });

    Route::prefix('screen-order-packaging')->group(function () {
        Route::get('{id}', [ScreenOrderPackagingController::class, 'getDetail']);
        Route::post('', [ScreenOrderPackagingController::class, 'store']);
        Route::delete('{id}', [ScreenOrderPackagingController::class, 'delete']);
    });

    Route::get('location-machine', [LocationMachineController::class, 'fetchAll']);

    Route::prefix('maintenance-tracking')->group(function () {
        Route::get('', [MaintenanceTrackingController::class, 'getList'])->name('get_maintenance_tracking_list');
        Route::get('detail/{id}', [MaintenanceTrackingController::class, 'getDetail'])->name('get_maintenance_tracking_detail');
        Route::get('get-count', [MaintenanceTrackingController::class, 'getCount'])->name('get_count_maintenance_tracking');
        Route::post('', [MaintenanceTrackingController::class, 'create'])->name('create_maintenance_tracking');
        Route::put('{id}/update-status/', [MaintenanceTrackingController::class, 'updateStatus'])->name('update_maintenance_tracking');
        Route::put('{id}/assign', [MaintenanceTrackingController::class, 'assign'])->name('assign_maintenance_tracking');
        Route::post('comment', [MaintenanceTrackingController::class, 'comment'])->name('comment_maintenance_tracking');
    });

    Route::prefix('token')->group(function () {
        Route::post('', [TokenController::class, 'addToken']);
    });
    //Todo : User
    Route::prefix('user')->group(function () {
        Route::get('', [UserController::class, 'fetchUser'])->name('get_user_list');
        Route::get('list-all', [UserController::class, 'getListAllUser'])->name('get_all_user_list');
        Route::post('', [UserController::class, 'create'])->name('create_user');
        Route::put('{id}', [UserController::class, 'update'])->name('update_user');
        Route::delete('{id}', [UserController::class, 'delete'])->name('delete_user');
    });

    //Todo : Printing Preset
    Route::prefix('printing-preset')->group(function () {
        Route::get('', [PrintingPresetController::class, 'getList'])->name('get_printing_preset_list');
        Route::get('{id}', [PrintingPresetController::class, 'getDetail'])->name('get_printing_preset_by_id');
        Route::post('', [PrintingPresetController::class, 'create'])->name('create_printing_preset');
        Route::put('{id}', [PrintingPresetController::class, 'update'])->name('update_printing_preset');
        Route::delete('{id}', [PrintingPresetController::class, 'delete'])->name('delete_printing_preset');
    });

    //Todo : Printing Preset Sku
    Route::prefix('printing-preset-sku')->group(function () {
        Route::get('', [PrintingPresetSkuController::class, 'getList'])->name('get_printing_preset_sku_list');
        Route::get('{id}', [PrintingPresetSkuController::class, 'getDetail'])->name('get_printing_preset_sku_by_id');
        Route::post('', [PrintingPresetSkuController::class, 'create'])->name('create_printing_preset_sku');
        Route::put('{id}', [PrintingPresetSkuController::class, 'update'])->name('update_printing_preset_sku');
        Route::delete('{id}', [PrintingPresetSkuController::class, 'delete'])->name('delete_printing_preset_sku');
    });
    //Todo : pretreat Preset
    Route::prefix('pretreat-preset')->group(function () {
        Route::get('', [PretreatPresetController::class, 'fetch'])->name('fetch_pretreat_preset');
        Route::get('fetch-all', [PretreatPresetController::class, 'fetchAll'])->name('get_pretreat_preset_sku_all');
        Route::post('', [PretreatPresetController::class, 'create'])->name('create_pretreat_preset');
        Route::put('{id}', [PretreatPresetController::class, 'update'])->name('update_pretreat_preset');
        Route::post('verify-csv-file', [PretreatPresetController::class, 'verifyCsvFile'])->name('verify_pretreat_preset');
        Route::post('import-csv', [PretreatPresetController::class, 'importCsv'])->name('import_pretreat_preset');
        Route::get('export', [PretreatPresetController::class, 'exportCsv'])->name('export_pretreat_preset');
    });
    //Todo : Pretreat Preset Sku
    Route::prefix('pretreat-preset-sku')->group(function () {
        Route::get('export', [PretreatPresetSkuController::class, 'exportCsv']);
        Route::post('verify-csv-file', [PretreatPresetSkuController::class, 'verifyFile']);
        Route::post('import-csv', [PretreatPresetSkuController::class, 'importCsv']);
        Route::post('import-csv', [PretreatPresetSkuController::class, 'importCsv']);
        Route::get('', [PretreatPresetSkuController::class, 'getList'])->name('get_pretreat_preset_sku_list');
        Route::get('{id}', [PretreatPresetSkuController::class, 'getDetail'])->name('get_pretreat_preset_sku_by_id');
        Route::post('', [PretreatPresetSkuController::class, 'create'])->name('create_pretreat_preset_sku');
        Route::put('{id}', [PretreatPresetSkuController::class, 'update'])->name('update_pretreat_preset_sku');
        Route::delete('{id}', [PretreatPresetSkuController::class, 'delete'])->name('delete_pretreat_preset_sku');
    });

    Route::prefix('part-number')->group(function () {
        Route::get('', [PartNumberController::class, 'getList']);
        Route::post('', [PartNumberController::class, 'create']);
        Route::post('/verify-import', [PartNumberController::class, 'verifyImport']);
        Route::post('/import', [PartNumberController::class, 'import']);
        Route::put('{id}', [PartNumberController::class, 'update']);
    });

    Route::prefix('part-number-overview')->group(function () {
        Route::get('', [PartNumberOverviewController::class, 'fetchAll']);
        Route::get('export', [PartNumberOverviewController::class, 'export']);
        Route::get('history/{id}', [PartNumberOverviewController::class, 'history']);
    });

    Route::apiResource(
        'brand',
        BrandController::class,
        [
            'names' => [
                'index' => 'brand',
                'store' => 'brand',
                'update' => 'brand',
                'destroy' => 'brand',
                'show' => 'brand'
            ]
        ],
    );
    ///Todo : Product
    Route::get('/product', [ProductController::class, 'fetchProduct']);
    Route::get('/product/get-by_gtin', [ProductController::class, 'getProductByGTIN']);
    Route::get('/product/attribute/list', [ProductController::class, 'getProductsByAttribute']);
    Route::get('/product/history-blank-cost-year/{id}', [ProductController::class, 'getProductHistoryBlankCostYear']);

    Route::prefix('surcharge-fee')->group(function () {
        Route::get('', [SurchargeFeeController::class, 'getList']);
        Route::post('', [SurchargeFeeController::class, 'createOrUpdate']);
    });

    Route::prefix('products')->group(function () {
        Route::get('sku', [ProductController::class, 'getProductBySku'])->name('get_product_list_with_attribute');
        Route::get('with-attributes', [ProductController::class, 'getProductListWithAttribute'])->name('get_product_list_with_attribute');
        Route::post('', [ProductController::class, 'create'])->name('create_product');
        Route::get('all', [ProductController::class, 'getAll'])->name('products');
        Route::get('exist', [ProductController::class, 'checkExist'])->name('product_exist');
        Route::put('{id}', [ProductController::class, 'update'])->name('update_product');
        Route::post('upload/{style}', [ProductController::class, 'upload'])->name('upload');
        Route::put('update-by-id/{id}', [ProductController::class, 'updateById']);
        Route::put('update-by-style/{style}', [ProductController::class, 'updateByStyle']);
        Route::get('pulling-shelves/quantity', [ProductController::class, 'getPullingShelvesProductQuantity'])->name('get_pulling_shelves_product_quantity');
        Route::get('get-print-method', [ProductStyleController::class, 'getPrintMethod'])->name('get_product_print_method');
        Route::get('info', [ProductController::class, 'getInfo'])->name('get_product_info');
        Route::post('/check-exist-skus', [ProductController::class, 'existsSkus']);
        Route::get('/get-newest-po-price', [ProductController::class, 'getNewestPoPrice']);
        Route::post('/validate-variants-csv', [ProductController::class, 'validateVariantsCsv']);
        Route::post('/validate-variants-csv-many-sheet', [ProductController::class, 'validateVariantsCsvManySheet']);
        Route::post('/import-variants', [ProductController::class, 'importVariants']);
        Route::post('/import-variants-csv-many-sheet', [ProductController::class, 'importVariantsCsvManySheet']);
        Route::post('/validate-weights-csv', [ProductController::class, 'validateWeightsCsv']);
        Route::post('/import-weights', [ProductController::class, 'importWeights']);
        Route::get('/colors-by-style', [ProductController::class, 'getColorsByStyle']);
        Route::post('/validate-variants-csv-product-spec', [ProductController::class, 'validateVariantsCsvProductSpec']);
        Route::post('/import-variants-csv-product-spec', [ProductController::class, 'importVariantsProductSpec']);

        Route::prefix('styles')->group(function () {
            Route::get('', [ProductStyleController::class, 'getList'])->name('get_product_style_list');
            Route::get('/all', [ProductStyleController::class, 'fetchAll'])->name('get_product_style_all');
            Route::post('', [ProductStyleController::class, 'create'])->name('create_product_style');
            Route::get('/print-area', [ProductStyleController::class, 'getProductPrintArea'])->name('get_product_print_area');
            Route::put('{id}', [ProductStyleController::class, 'update'])->name('update_product_style');
            Route::get('getType', [ProductStyleController::class, 'getType'])->name('get_product_type');
            Route::get('get-type', [ProductStyleController::class, 'getTypes'])->name('get_product_types');
            Route::get('search', [ProductStyleController::class, 'searchType'])->name('get_product_types');
            Route::post('export', [ProductStyleController::class, 'exportSkuByType'])->name('get_product_types');
        });
        Route::prefix('colors')->group(function () {
            Route::get('', [ProductColorController::class, 'getList'])->name('get_product_color_list');
            Route::get('/all', [ProductColorController::class, 'fetchAll'])->name('get_product_color_all');
            Route::post('', [ProductColorController::class, 'create'])->name('create_product_color');
            Route::put('{id}', [ProductColorController::class, 'update'])->name('update_product_color');
            Route::delete('{id}', [ProductColorController::class, 'delete'])->name('delete_product_color');
        });
        Route::prefix('sizes')->group(function () {
            Route::get('', [ProductSizeController::class, 'getList'])->name('get_product_size_list');
            Route::get('/all', [ProductSizeController::class, 'fetchAll'])->name('get_product_size_all');
            Route::post('', [ProductSizeController::class, 'create'])->name('create_product_size');
            Route::put('{id}', [ProductSizeController::class, 'update'])->name('update_product_size');
        });
        Route::prefix('type-weights')->group(function () {
            Route::get('', [ProductTypeWeightController::class, 'getList'])->name('get_product_type_weight_list');
            Route::post('', [ProductTypeWeightController::class, 'create'])->name('create_product_type_weight');
            Route::put('{id}', [ProductTypeWeightController::class, 'update'])->name('update_product_type_weight');
        });
        Route::prefix('style-size-weights')->group(function () {
            Route::get('/list', [ProductStyleSizeWeightController::class, 'getList'])->name('get_product_style_size_weight_list');
            Route::get('/history', [ProductStyleSizeWeightController::class, 'getImportHistory'])->name('get_product_style_size_weight_importing_history');
            Route::post('/import-csv', [ProductStyleSizeWeightController::class, 'import'])->name('import_product_style_size_weight');
            Route::post('/verify-csv-file', [ProductStyleSizeWeightController::class, 'verifyCsvFile'])->name('verify_product_style_size_weight');
        });
        Route::prefix('type')->group(function () {
            Route::get('', [ProductTypeController::class, 'getList'])->name('get_product_type_list');
            Route::post('', [ProductTypeController::class, 'create'])->name('create_product_type');
            Route::post('{id}', [ProductTypeController::class, 'update'])->name('update_product_type');
            Route::get('/all', [ProductTypeController::class, 'fetchAll'])->name('get_product_type_all');
        });
    });

    Route::get('/product/{id}', [ProductController::class, 'getProduct'])->name('product');
    Route::delete('/product/{id}', [ProductController::class, 'deleteProduct'])->name('product');
    Route::get('/product-for-create-order', [ProductController::class, 'fetchProductForCreateOrder'])->name('product');

    ///Todo : Warehouse
    Route::get('/warehouse', [WareHouseController::class, 'fetchWarehouse'])->name('warehouse');
    Route::get('/warehouse/{id}', [WareHouseController::class, 'getWarehouse'])->name('warehouse');
    Route::get('/timezones', [WareHouseController::class, 'getTimeZone'])->name('warehouse timezone');
    Route::post('/warehouse', [WareHouseController::class, 'storeWarehouse'])->name('warehouse');
    Route::put('/warehouse/{id}', [WareHouseController::class, 'updateWarehouse'])->name('warehouse');
    Route::post('/warehouse/select', [WareHouseController::class, 'selectWarehouse'])->name('select_warehouse');
    Route::post('/warehouse/test', [WareHouseController::class, 'testWarehouse'])->name('warehouse');
    Route::get('/warehouse/get-rule/{id}', [WareHouseController::class, 'getRule']);

    ///Todo : Vendor
    Route::get('/vendor', [VendorController::class, 'fetchVendor'])->name('vendor');
    Route::get('/vendor/{id}', [VendorController::class, 'getVendor'])->name('vendor');
    Route::post('/vendor', [VendorController::class, 'storeVendor'])->name('vendor');
    Route::put('/vendor/{id}', [VendorController::class, 'updateVendor'])->name('vendor');
    Route::delete('/vendor/{id}', [VendorController::class, 'deleteVendor'])->name('vendor');

    //Todo : Box
    Route::prefix('box')->group(function () {
        Route::get('', [BoxController::class, 'fetchBox']);
        Route::get('rack', [BoxController::class, 'fetchBoxInRack']);
        Route::get('replenishment/box-need-moving-to-rack', [BoxController::class, 'getBoxNeedMovingToRack']);

        Route::get('{id}', [BoxController::class, 'getBox']);
        Route::post('', [BoxController::class, 'createBox']);
        Route::put('{id}', [BoxController::class, 'updateBox']);
        Route::delete('{id}', [BoxController::class, 'deleteBox']);
        Route::post('import', [BoxController::class, 'importBox']);
        Route::get('/list/generate', [BoxController::class, 'fetchBarCodePrinted']);
        Route::post('/generate', [BoxController::class, 'generateBoxIDs']);
        Route::post('/printed/generate', [BoxController::class, 'updatePrintStatus']);
        Route::get('search/{barcode}', [BoxController::class, 'searchBox']);
    });
    Route::get('/get-box-by-barcode', [BoxController::class, 'getBoxByBarcode']);

    Route::prefix('supply-box')->group(function () {
        Route::get('/list', [SupplyBoxController::class, 'fetchBox']);
        Route::get('/get-boxes', [SupplyBoxController::class, 'getBoxes']);
        Route::get('/get-box-info', [SupplyBoxController::class, 'getBoxInfo']);
    });
    //Todo : Box Moving
    Route::prefix('box-moving')->group(function () {
        Route::get('', [BoxMovingController::class, 'fetchBoxMoving'])->name('box moving');
        Route::post('', [BoxMovingController::class, 'storeBoxMoving'])->name('box moving');
        Route::post('revert', [BoxMovingController::class, 'revertBoxMoving'])->name('box moving');
        Route::post('pulling-shelves-to-rack', [BoxMovingController::class, 'pullingShelvesToRack'])->name('box moving');
        Route::post('revert/pulling-shelves-to-rack', [BoxMovingController::class, 'revertBoxMovingToPullingShelve'])->name('box moving');
        Route::post('revert/dark-pod-to-rack', [BoxMovingController::class, 'revertBoxMovingToDarkPod']);
        Route::get('get-last-product-price', [BoxMovingController::class, 'getLastProductPrice']);
    });

    ///Todo : Location
    Route::get('/location', [LocationController::class, 'fetchLocation']);
    Route::get('/location/check-moving-shelves/{barcode}', [LocationController::class, 'checkMovingShelvesLocation']);
    Route::get('/location/{id}', [LocationController::class, 'getLocation']);
    Route::post('/location', [LocationController::class, 'storeLocation'])->name('location');
    Route::put('/location/{id}', [LocationController::class, 'updateLocation'])->name('location');
    Route::delete('/location/{id}', [LocationController::class, 'deleteLocation'])->name('location');
    Route::post('/location/import', [LocationController::class, 'importLocation'])->name('location');
    Route::get('/location/dark-pod/get-list', [LocationController::class, 'fetchLocationDarkPod']);
    Route::get('/location/dark-pod/{location}', [LocationController::class, 'fetchDarkPodByLocation']);

    Route::prefix('adjust-pulling-shelves')->group(function () {
        Route::get('scan-sku', [AdjustPullingShelvesController::class, 'getAdjustPullingShelvesList'])->name('adjust pulling shelves');
        Route::get('', [AdjustPullingShelvesController::class, 'getAdjustPullingShelvesList'])->name('adjust pulling shelves');
        Route::post('', [AdjustPullingShelvesController::class, 'createAdjustPullingShelves'])->name('adjust pulling shelves');
    });

    Route::prefix('supply-location')->group(function () {
        Route::get('/list', [SupplyLocationController::class, 'fetch']);
        Route::post('/create', [SupplyLocationController::class, 'create']);
        Route::put('/update/{id}', [SupplyLocationController::class, 'update']);
        Route::delete('/delete/{id}', [SupplyLocationController::class, 'delete']);
    });

    ///Todo : Shipping Carrier Package
    Route::prefix('shipping-carrier-package')->group(function () {
        Route::get('', [ShippingCarrierPackageController::class, 'fetchAll'])->name('shipping carrier package fetch all');
        Route::post('', [ShippingCarrierPackageController::class, 'create'])->name('shipping carrier package create');
        Route::put('{id}', [ShippingCarrierPackageController::class, 'update'])->name('shipping carrier package update');
        Route::delete('{id}', [ShippingCarrierPackageController::class, 'delete'])->name('shipping carrier package delete');
    });

    ///Todo : Shipping Carrier
    Route::get('/shipping-carrier', [ShippingCarrierController::class, 'fetchShippingCarrier'])->name('shipping carrier');
    Route::get('/shipping-carrier/{id}', [ShippingCarrierController::class, 'getShippingCarrier'])->name('shipping carrier');
    Route::post('/shipping-carrier', [ShippingCarrierController::class, 'storeShippingCarrier'])->name('shipping carrier');
    Route::put('/shipping-carrier/{id}', [ShippingCarrierController::class, 'updateShippingCarrier'])->name('shipping carrier');
    Route::delete('/shipping-carrier/{id}', [ShippingCarrierController::class, 'deleteShippingCarrier'])->name('shipping carrier');
    Route::get('/shipping-carrier-manifest', [ShippingCarrierController::class, 'getCarrierMakeManifest'])->name('shipping carrier');

    ///Todo : Shipping Carrier Service
    Route::prefix('shipping-carrier-service')->group(function () {
        Route::get('', [ShippingCarrierServiceController::class, 'fetchAll'])->name('shipping carrier service fetch all');
        Route::post('', [ShippingCarrierServiceController::class, 'create'])->name('shipping carrier service create');
        Route::put('{id}', [ShippingCarrierServiceController::class, 'update'])->name('shipping carrier service update');
        Route::delete('{id}', [ShippingCarrierServiceController::class, 'delete'])->name('shipping carrier service delete');
    });

    ///Todo : Payment Term
    Route::get('/payment-term', [PaymentTermController::class, 'fetchPaymentTerm'])->name('payment term');
    Route::get('/payment-term/{id}', [PaymentTermController::class, 'getPaymentTerm'])->name('payment term');
    Route::post('/payment-term', [PaymentTermController::class, 'storePaymentTerm'])->name('payment term');
    Route::put('/payment-term/{id}', [PaymentTermController::class, 'updatePaymentTerm'])->name('payment term');
    Route::delete('/payment-term/{id}', [PaymentTermController::class, 'deletePaymentTerm'])->name('payment term');

    ///Todo : Purchase Order
    Route::get('/purchase-order', [PurchaseOrderController::class, 'fetchPurchaseOrder'])->name('purchase order');
    Route::get('/purchase-order/summary', [PurchaseOrderController::class, 'fetchPurchaseOrderSummary'])->name('purchase order');
    Route::get('/purchase-order/{id}', [PurchaseOrderController::class, 'getPurchaseOrder'])->name('purchase order');
    Route::post('/purchase-order', [PurchaseOrderController::class, 'storePurchaseOrder'])->name('purchase order');
    Route::put('/purchase-order/{id}', [PurchaseOrderController::class, 'updatePurchaseOrder'])->name('purchase order');
    Route::put('/cancel-purchase-order/{id}', [PurchaseOrderController::class, 'cancelPurchaseOrder'])->name('purchase order');
    Route::post('/comment-purchase-order', [PurchaseOrderController::class, 'commentPurchaseOrder'])->name('purchase order');
    Route::get('/purchase-order-history', [PurchaseOrderController::class, 'getPurchaseOrderHistory'])->name('purchase order');
    Route::get('/purchase-order-box-detail', [PurchaseOrderController::class, 'getPurchaseOrderBoxDetail'])->name('purchase order');
    Route::post('/purchase-order-box', [PurchaseOrderController::class, 'storePurchaseOrderBox'])->name('purchase order');
    Route::put('/purchase-order-box/{id}', [PurchaseOrderController::class, 'updatePurchaseOrderBox'])->name('purchase order');
    Route::get('/fetch-product-purchase-order/{po_id}', [PurchaseOrderController::class, 'fetchProductPurchaseOrder'])->name('purchase order');
    Route::delete('/remove-purchase-order-box/{id}', [PurchaseOrderController::class, 'removeProductPurchaseOrderBox'])->name('purchase order');
    Route::delete('/remove-purchase-order-box-item/{id}', [PurchaseOrderController::class, 'removeProductPurchaseOrderBoxItem'])->name('purchase order');
    Route::post('/purchase-order/update-manual', [PurchaseOrderController::class, 'updatePurchaseOrderManual'])->name('purchase order');
    Route::get('/purchase-order-has-product', [PurchaseOrderController::class, 'getPurchaseOrderHasProduct'])->name('purchase order has product');

    ///Todo : Inventory Addition
    Route::get('/get-box-for-inventory', [BoxController::class, 'getBoxForInventory']);
    Route::get('/get-product-for-inventory', [ProductController::class, 'getProductForInventory']);
    Route::get('/get-location-for-inventory', [LocationController::class, 'getLocationForInventory']);
    Route::get('/fetch-purchase-order-by-po-number', [PurchaseOrderController::class, 'fetchPurchaseOrderByPoNumber']);
    Route::get('/fetch-purchase-order-by-invoice-number', [PurchaseOrderController::class, 'fetchPurchaseOrderByInvoice']);
    Route::get('/fetch-tracking-number-by-order', [PurchaseOrderController::class, 'fetchTrackingNumberByOrder']);

    Route::get('/fetch-purchase-order-by-vendor', [PurchaseOrderController::class, 'fetchPurchaseOrderByVendor'])->name('inventory addition');

    //Route::post('/inventory-addition', [InventoryAdditionController::class, 'storeInventoryAddition']);
    Route::post('/inventory-addition/manual', [InventoryAdditionController::class, 'storeInventoryAddition'])->name('inventory addition');

    Route::get('/inventory-addition', [InventoryAdditionController::class, 'fetchInventoryAddition'])->name('inventory addition');

    //Route::post('/revert-inventory-addition', [InventoryAdditionController::class, 'revertInventoryAddition']);
    Route::post('/inventory-addition/revert', [InventoryAdditionController::class, 'revertInventoryAddition'])->name('inventory addition');

    Route::get('/get-order-box-by-tracking-number', [BoxController::class, 'getOrderBoxByTrackingNumber'])->name('inventory addition');

    //Route::post('/add-inventory-addition-distributor', [InventoryAdditionController::class, 'addInventoryAdditionDistributor']);
    Route::post('/inventory-addition/distributor', [InventoryAdditionController::class, 'addInventoryAdditionDistributor'])->name('inventory addition');

    Route::get('/get-product-in-purchase-order', [PurchaseOrderController::class, 'getProductInPurchaseOrder']);
    //Todo : Test Count Adjustment
    Route::get('/test-count', [TestCountAdjustmentController::class, 'fetchTestCount'])->name('test count');
    Route::post('/test-count', [TestCountAdjustmentController::class, 'testCount'])->name('test count');
    Route::post('/scan-box', [TestCountAdjustmentController::class, 'scanBox'])->name('test count');
    Route::post('/complete-test-count', [TestCountAdjustmentController::class, 'completeTestCount'])->name('test count');
    Route::get('/test-count-detail/{id}', [TestCountAdjustmentController::class, 'testCountDetail'])->name('test count');
    Route::delete('/test-count/{id}', [TestCountAdjustmentController::class, 'deleteTestCount'])->name('test count');
    Route::post('/test-count/pause', [TestCountAdjustmentController::class, 'pauseTestCount'])->name('test count');
    Route::get('/test-count/get-box-in-location', [TestCountAdjustmentController::class, 'getAllBoxInLocation'])->name('test count');
    Route::get('/test-count/pause', [TestCountAdjustmentController::class, 'allTestCountPause'])->name('test count');

    //Todo : Inventory
    Route::get('/inventory-detail', [InventoryController::class, 'fetchInventoryDetail']);
    Route::get('/inventory-total-detail', [InventoryController::class, 'fetchInventoryTotalDetail']);
    Route::get('/inventory-overview', [InventoryController::class, 'fetchInventoryOverview']);
    Route::get('/inventory-total-overview', [InventoryController::class, 'fetchInventoryTotalOverview']);
    Route::get('/inventory-overview/export', [InventoryController::class, 'exportInventoryOverview']);
    Route::get('/inventory/history/{sku}', [InventoryController::class, 'fetchHistory']);
    Route::post('/inventory/rbt-adjustment', [RbtSupportController::class, 'createAdjustment']);

    Route::get('/product-rank-history/{productId}', [ProductRankHistoryController::class, 'show']);

    ///Todo : Inventory Deduction
    ///
    Route::get('/looker-studio/missing-scanned-wip', [MissingScannedWIPController::class, 'report']);

    Route::prefix('inventory-deduction')->group(function () {
        Route::get('', [InventoryDeductionController::class, 'fetchInventoryDeduction'])->name('inventory deduction');
        Route::post('', [InventoryDeductionController::class, 'storeInventoryDeduction'])->name('inventory deduction');
        Route::get('csv-history', [InventoryDeductionController::class, 'getCsvHistoryList'])->name('inventory deduction');
        Route::post('verify-csv-file', [InventoryDeductionController::class, 'verifyCsvFile'])->name('inventory deduction');
        Route::post('import-csv-file/{id}', [InventoryDeductionController::class, 'importCsvFile'])->name('inventory deduction');
        //  Route::post('/inventory-deduction/revert', [InventoryDeductionController::class, 'revertInventoryDeduction']);
        Route::post('revert', [InventoryDeductionController::class, 'revertInventoryDeduction'])->name('inventory deduction');
        Route::post('sku', [InventoryDeductionController::class, 'storeInventoryDeductionSku'])->name('inventory deduction sku');
    });

    Route::prefix('product-roulette')->group(function () {
        Route::get('', [ProductRouletteController::class, 'index']);
        Route::post('', [ProductRouletteController::class, 'store']);
        Route::put('{id}', [ProductRouletteController::class, 'update']);
    });

    // Todo : barcode
    Route::get('/barcode/total', [BarcodeController::class, 'totalBarcode']);
    Route::post('/barcode/generate-batch', [BarcodeController::class, 'generateBatch']);
    Route::get('/barcode/count-by-order-type', [BarcodeController::class, 'countByOrderType']);
    Route::get('/barcode/count-priority-store', [BarcodeController::class, 'countPendingPriorityStoreByOrderType']);
    Route::get('/barcode/fetch-by-order-type', [BarcodeController::class, 'fetchByOrderType']);
    Route::get('/barcode/fetch-history-by-order-type', [BarcodeController::class, 'historyByOrderType']);
    Route::post('/barcode/confirm-by-order-type', [BarcodeController::class, 'confirmPrintByOrderType']);
    Route::get('/barcode/fetch-dtf', [BarcodeController::class, 'fetchDTF']);
    Route::get('/barcode/fetch-history-dtf', [BarcodeController::class, 'historyDtf']);
    Route::post('/barcode/confirm-dtf', [BarcodeController::class, 'confirmPrintDtf']);
    Route::get('/barcode/count', [BarcodeController::class, 'countPending'])->name('barcode');
    Route::get('/barcode', [BarcodeController::class, 'fetchPrintedBarcode'])->name('barcode');
    Route::get('/barcode/count-dtf-pending-by-order-type', [BarcodeController::class, 'countDTFByOrderType']);
    Route::post('/barcode/print', [BarcodeController::class, 'printBarcode'])->name('barcode');
    Route::post('/barcode/print-test', [BarcodeController::class, 'printBarcodeTest'])->name('barcode');
    Route::post('/barcode/{id}', [BarcodeController::class, 'updatePrintedBarcode'])->name('barcode');
    Route::post('/barcode/confirm-printed/{id}', [BarcodeController::class, 'confirmBarcodePrinted'])->name('barcode');
    Route::post('/barcode/print/{id}', [BarcodeController::class, 'printBarcodePrinted'])->name('barcode');
    Route::post('/barcode/reprint/{id}', [BarcodeController::class, 'reprintBarcodePrinted'])->name('barcode');
    Route::post('/barcode/log-barcode-printed/{label_id}', [BarcodeController::class, 'logBarcodePrinted']);
    Route::get('/barcode/total', [BarcodeController::class, 'totalBarcode']);
    Route::get('/barcode/last-printed', [BarcodeController::class, 'getLastPrinted']);
    Route::post('/barcode/generate-batch', [BarcodeController::class, 'generateBatch']);

    Route::prefix('emb-wip')->group(function () {
        Route::get('count', [BarcodeEMBController::class, 'countPending']);
        Route::get('', [BarcodeEMBController::class, 'fetchPrintedBarcode']);
        Route::post('print', [BarcodeEMBController::class, 'printBarcode']);
        Route::post('{id}', [BarcodeEMBController::class, 'updatePrintedBarcode']);
        Route::post('confirm-printed/{id}', [BarcodeEMBController::class, 'confirmBarcodePrinted']);
    });

    // Todo : employee
    Route::post('/employee/logout/multiple-staging', [EmployeeController::class, 'logoutEmployeeTimeCheckingMultipleStaging'])->name('employee');
    Route::post('/employee/time-checking', [EmployeeController::class, 'timeChecking'])->name('employee');
    Route::get('/employee/logout/{id}', [EmployeeController::class, 'logoutTimeChecking'])->name('employee');
    Route::get('/employee', [EmployeeController::class, 'fetchEmployee'])->name('employee');
    Route::get('/employee/code/{id}', [EmployeeController::class, 'getEmployeeByCode'])->name('employee');
    Route::post('/employee', [EmployeeController::class, 'store'])->name('employee');
    Route::get('/employee/{id}', [EmployeeController::class, 'show'])->name('employee');
    Route::post('/employee/update/{id}', [EmployeeController::class, 'update'])->name('employee');
    Route::delete('/employee/{id}', [EmployeeController::class, 'destroy'])->name('employee');

    Route::post('/barcode-assign', [BarcodeAssignController::class, 'assign'])->name('barcode assign');
    Route::post('/wips/assign-batch', [BarcodeAssignController::class, 'assignBatch']);
    Route::post('/wips/single-assign', [BarcodeAssignController::class, 'singleAssignment']);
    Route::get('/barcode-assign', [BarcodeAssignController::class, 'fetchBarcodeAssign'])->name('barcode assign');

    // Todo : setting move warehouse

    Route::post('/setting-move-warehouse', [SettingMoveWarehouseController::class, 'store'])->name('setting move warehouse');
    Route::put('/setting-move-warehouse', [SettingMoveWarehouseController::class, 'updatePriority'])->name('setting move warehouse');
    Route::get('/setting-move-warehouse', [SettingMoveWarehouseController::class, 'list'])->name('setting move warehouse');
    Route::get('/setting-move-warehouse/item-reroute', [SettingMoveWarehouseController::class, 'getItemReroute'])->name('setting move warehouse');
    Route::get('/setting-move-warehouse/item-reroute-fail/{id}', [SettingMoveWarehouseController::class, 'getItemRerouteFail'])->name('setting move warehouse');

    // Todo : Quality Control
    Route::get('/quality-control', [QualityControlController::class, 'getDataQualityControl'])->name('quality control');
    Route::get('/quality-control/list', [QualityControlController::class, 'fetchAll'])->name('quality control');
    Route::post('/quality-control/pass', [QualityControlController::class, 'passQualityControl'])->name('quality control');
    Route::post('/quality-control/reject', [QualityControlController::class, 'rejectQc'])->name('quality control');
    Route::get('/quality-control/qc-sla', [QualityControlController::class, 'getSlaQc']);
    Route::get('/quality-control/list-qc-sla', [QualityControlController::class, 'getListSlaQc']);
    Route::get('/quality-control/report-qc-sla', [QualityControlController::class, 'getReportSlaQc']);
    Route::get('/quality-control/weekly-qc-sla', [QualityControlController::class, 'getWeeklySlaQc']);

    // Todo : Role
    Route::post('/roles', [RoleController::class, 'store'])->name('role');
    Route::put('/roles/{id}', [RoleController::class, 'update'])->name('role');
    Route::get('/roles', [RoleController::class, 'fetchAll'])->name('role');
    Route::delete('/roles/{id}', [RoleController::class, 'destroyRole'])->name('role');
    Route::post('/roles/user-role', [RoleController::class, 'userRole'])->name('role');
    Route::post('/roles/role-permission', [RoleController::class, 'rolesAssignExistingPermissions'])->name('role');

    // Todo : permission
    Route::post('/permission', [PermissionController::class, 'store'])->name('permission');
    Route::put('/permission/{id}', [PermissionController::class, 'update'])->name('permission');
    Route::get('/permission', [PermissionController::class, 'fetchAll'])->name('permission');
    Route::delete('/permission/{id}', [PermissionController::class, 'destroy'])->name('permission');

    // Todo : role - permission
    Route::get('/role-permission', [RolePermissionController::class, 'getRolePermission'])->name('role permission');

    // Todo : Setting
    Route::get('/setting/check-ip-backlog', [SettingController::class, 'checkIpBacklog'])->name('check-ip-backlog');
    Route::get('/setting/name', [SettingController::class, 'getSettingByName'])->name('setting');
    Route::get('/setting/fetch', [SettingController::class, 'fetch'])->name('setting');
    Route::get('/setting/{id}', [SettingController::class, 'show'])->name('setting');
    Route::delete('/setting/{id}', [SettingController::class, 'destroy'])->name('setting');
    Route::post('/setting', [SettingController::class, 'store'])->name('setting');
    Route::put('/setting/{id}', [SettingController::class, 'update'])->name('setting');

    // Language
    Route::get('/language/{id}', [LanguageController::class, 'fetch']);
    Route::post('/language', [LanguageController::class, 'store']);
    Route::put('/language/{id}', [LanguageController::class, 'update']);
    Route::delete('/language/{id}', [LanguageController::class, 'delete']);

    // Tag
    Route::get('/tag', [TagController::class, 'fetchAll']);
    Route::get('/tag-restrict-remove', [TagController::class, 'tagRestrictRemove']);
    Route::post('/tag', [TagController::class, 'store']);
    Route::put('/tag/{id}', [TagController::class, 'update']);
    Route::delete('/tag/{id}', [TagController::class, 'delete']);

    ///Todo: auto sync Order
    Route::post('/purchase-order/sync', [ToolOrderController::class, 'syncPurchaseOrder']);

    Route::prefix('shipping-method')->group(function () {
        Route::get('name', [ShippingMethodController::class, 'fetchName']);
        Route::get('get-all', [ShippingMethodController::class, 'getAll']);
        Route::get('/account-shipping-by-warehouse/{id}', [ShippingMethodController::class, 'getShippingAccountByWarehouse']);
    });

    Route::prefix('trademark')->group(function () {
        Route::get('get-list', [TrademarkController::class, 'getAll']);
    });

    // Todo: Sale Order
    Route::prefix('sale-order')->group(function () {
        Route::post('input-email', [SaleOrderController::class, 'submitEmail']);
        Route::post('upload-sample-image', [SaleOrderController::class, 'uploadSampleImage']);
        Route::get('get-count', [SaleOrderController::class, 'getCount']);
        Route::get('get-count-invalid-address', [SaleOrderController::class, 'getCountInvalidAddress']);
        Route::get('report-by-day', [SaleOrderController::class, 'getReportByDay']);
        Route::get('get-aging', [SaleOrderController::class, 'getAging']);
        Route::put('{id}/address-v1', [SaleOrderController::class, 'updateAddressV1']);
        Route::post('{id}/verify-address-v1', [SaleOrderController::class, 'verifyAddressV1']);
        Route::get('', [SaleOrderController::class, 'getList']);
        Route::post('', [SaleOrderController::class, 'create']);
        Route::get('barcode-items/not-reprint', [SaleOrderController::class, 'getBarcodeItemsNotReprint']);
        Route::get('barcode-items/not-manual', [SaleOrderController::class, 'getBarcodeItemsNotManual']);
        Route::get('barcode-items/not-manual-by-list-order-number', [SaleOrderController::class, 'getBarcodeItemsNotManualByListOrderNumbers']);
        Route::get('{id}', [SaleOrderController::class, 'getDetail'])->where(['id' => '[0-9]+']);
        Route::post('{id}/comment', [SaleOrderController::class, 'createComment']);
        Route::put('{id}/order-status', [SaleOrderController::class, 'updateOrderStatus']);
        Route::put('{id}/shipping-method', [SaleOrderController::class, 'updateShippingMethod']);
        Route::put('{id}/tag', [SaleOrderController::class, 'updateTag']);
        Route::put('{id}/update-note', [SaleOrderController::class, 'updateInternalNote']);
        Route::put('{id}/note', [SaleOrderController::class, 'updateNote']);
        Route::put('{id}/address', [SaleOrderController::class, 'updateAddress']);
        Route::put('{id}/ioss-number', [SaleOrderController::class, 'updateIossNumber']);
        Route::post('{id}/verify-address', [SaleOrderController::class, 'verifyAddress']);
        Route::get('{id}/order-items', [SaleOrderController::class, 'getOrderItems']);
        Route::post('reprint', [SaleOrderController::class, 'reprint']);
        Route::post('manual-process', [SaleOrderController::class, 'manualProcess']);
        Route::patch('reroute', [SaleOrderController::class, 'reroute']);
        Route::get('reroute/check-before-reroute', [SaleOrderController::class, 'checkBeforeReroute']);
        Route::post('reroute/verify-csv-file', [SaleOrderController::class, 'rerouteVerifyCsvFile']);
        Route::post('reroute/import-csv-file', [SaleOrderController::class, 'rerouteImportCsvFile']);
        Route::post('{id}/manual-tracking', [SaleOrderController::class, 'manualTracking']);
        Route::get('forecast', [SaleOrderController::class, 'forecast']);
        Route::get('print-type-statistic', [SaleOrderController::class, 'printTypeStatistic']);
        Route::get('get-sla-report', [SaleOrderController::class, 'getSlaReport']);
        Route::get('get-sku-report', [SaleOrderController::class, 'getSkuReport']);
        Route::get('get-client-report', [SaleOrderController::class, 'getClientReport']);
        Route::get('get-client-revenue-report', [SaleOrderController::class, 'getClientRevenueReport']);
        Route::get('get-sku-revenue-report', [SaleOrderController::class, 'getSkuRevenueReport']);
        Route::get('get-revenue-report-by-state', [SaleOrderController::class, 'getRevenueReportByState']);
        Route::get('get-revenue-report-by-day', [SaleOrderController::class, 'getRevenueReportByDay']);
        Route::get('get-order-quantity-report', [SaleOrderController::class, 'getOrderQuantityReport']);
    });

    // Todo: Sale Order Item
    Route::prefix('sale-order-item')->group(function () {
        Route::get('', [SaleOrderItemController::class, 'getList']);
        Route::get('check-exist-external-id', [SaleOrderItemController::class, 'checkExistExternalId']);
    });

    // Todo: Performance
    Route::post('folding', [PerformanceController::class, 'foldingSku'])->name('performance');
    Route::post('pretreat', [PerformanceController::class, 'pretreatSku'])->name('pretreatPerformance');
    Route::post('press', [PerformanceController::class, 'pressLabel']);
    Route::post('log-printed-insert', [PerformanceController::class, 'logInsertPrinted']);

    // Todo: Sale Order Item Barcode
    Route::prefix('sale-order-item-barcode')->group(function () {
        Route::get('', [SaleOrderItemBarcodeController::class, 'getList']);
    });

    // Todo: Sale Order Item Image
    Route::prefix('sale-order-item-image')->group(function () {
        Route::get('download-error', [SaleOrderItemImageController::class, 'getListDownloadError']);
        Route::get('get-count-download-error', [SaleOrderItemImageController::class, 'getCountDownloadError']);
        Route::get('detect-color-error', [SaleOrderItemImageController::class, 'getListDetectColorError']);
        Route::get('manual-uploaded', [SaleOrderItemImageController::class, 'getListManualUploaded']);
        Route::post('retry-download/{id}', [SaleOrderItemImageController::class, 'retryDownload']);
        Route::post('retry-detect-color/{id}', [SaleOrderItemImageController::class, 'retryDetectColor']);
        Route::post('manual-upload/{id}', [SaleOrderItemImageController::class, 'manualUploadFile']);
        Route::get('mug/convert-error', [SaleOrderItemImageController::class, 'convertError']);
        Route::put('mug/download/{barcode_id}', [SaleOrderItemImageController::class, 'downloadMug']);
        Route::post('update-art-file', [SaleOrderItemImageController::class, 'updateArtFile']);
    });

    // Todo: Account
    Route::prefix('account')->group(function () {
        Route::get('', [AccountController::class, 'getList']);
        Route::post('', [AccountController::class, 'create']);
        Route::put('{id}', [AccountController::class, 'update']);
        Route::delete('{id}', [AccountController::class, 'delete']);
    });

    // Todo: Store
    Route::prefix('store')->group(function () {
        Route::get('', [StoreController::class, 'getList']);
        Route::get('all', [StoreController::class, 'getAll']);
        Route::get('{id}', [StoreController::class, 'getStore']);
        Route::post('store-info', [StoreController::class, 'storeInfo']);
        Route::put('store-info', [StoreController::class, 'editStoreInfo']);
        Route::post('store-ship-address', [StoreController::class, 'storeShipAddress']);
        Route::put('store-ship-address', [StoreController::class, 'editShipAddress']);
        Route::post('store-return-address', [StoreController::class, 'storeReturnAddress']);
        Route::put('store-return-address', [StoreController::class, 'editReturnAddress']);
        Route::post('store-info-account', [StoreController::class, 'storeInfoAccount']);
        Route::get('store-info-account/generate-api-key/{id}', [StoreController::class, 'generateApiKey']);
        Route::post('store-info-shipping', [StoreController::class, 'storeInfoShipping']);
        Route::post('store-sla-expiration', [StoreController::class, 'storeSlaExpiration']);
        Route::post('auto-generate-api-key', [StoreController::class, 'autoGenerateAPIKey']);
    });
    Route::prefix('clients')->group(function () {
        Route::get('', [ClientController::class, 'index']);
        Route::get('all', [ClientController::class, 'getAll']);
        Route::get('{id}', [ClientController::class, 'show']);
        Route::post('', [ClientController::class, 'store']);
        Route::put('{id}', [ClientController::class, 'update']);
    });

    // Todo: weight cubic
    Route::prefix('weight-cubic')->group(function () {
        Route::get('getList', [WeightCubicController::class, 'getList'])->name('get_weight_cubic');
        Route::post('', [WeightCubicController::class, 'create'])->name('create_weight_cubic');
        Route::put('{id}', [WeightCubicController::class, 'update'])->name('update_weight_cubic');
        Route::delete('{id}', [WeightCubicController::class, 'delete'])->name('delete_weight_cubic');
    });

    // Todo: Performance Report
    Route::prefix('performance-report')->group(function () {
        Route::get('manager', [PerformanceReportController::class, 'fetchPerformanceReportForManager'])->name('performance-report-for-manager');
        Route::get('manager-v2', [PerformanceReportController::class, 'getPerformanceReportForManager'])->name('performance-report-for-manager-v2');
        Route::get('top-employees', [PerformanceReportController::class, 'fetchTopPerformanceEmployees'])->name('top-employees');
        Route::get('report-departments-for-manager', [PerformanceReportController::class, 'fetchPerformanceByDepartments'])->name('performance-by-departments-for-manager');

        Route::get('summary-report-for-leader', [PerformanceReportController::class, 'summaryPerformanceReportForLeader'])->name('summary-performance-report-for-leader');
        Route::get('report-department-for-leader', [PerformanceReportController::class, 'getPerformanceReportDeptForLeader'])->name('performance-report-for-leader');
        Route::get('report-all-employees', [PerformanceReportController::class, 'fetchReportAllEmployees'])->name('report-all-employees');
        Route::get('report-all-employees-by-time', [PerformanceReportController::class, 'fetchReportAllEmployeeByTime'])->name('report-all-employees-by-time');

        Route::get('fetch-employees-by-department', [PerformanceReportController::class, 'fetchEmployeesByDept'])->name('employees-of-department');

        Route::get('department', [PerformanceReportController::class, 'fetchDepartments'])->name('fetch-departments');
        Route::get('department-report', [PerformanceReportController::class, 'fetchDepartmentsForReport'])->name('fetch-departments-report');
    });

    // Product print area
    Route::get('/product-sku-print-area/attributes', [ProductPrintAreaController::class, 'fetchAllAttribute'])->name('fetchAllAttribute');
    Route::apiResource('product-sku-print-area', ProductPrintAreaController::class);
    Route::get('/product-print-area/fetch-all', [ProductPrintAreaController::class, 'fetchAll']);

    // Todo: Department
    Route::prefix('department')->group(function () {
        Route::get('', [DepartmentController::class, 'getAllDepartment'])->name('department');
        Route::get('job-type', [DepartmentController::class, 'getListJobType'])->name('department.job_type');
        Route::get('list', [DepartmentController::class, 'index'])->name('department.list');
        Route::post('', [DepartmentController::class, 'store'])->name('department.store');
        Route::put('{id}', [DepartmentController::class, 'update'])->name('department.update');
        Route::delete('{id}', [DepartmentController::class, 'destroy'])->name('department.destroy');
    });

    // Todo: Set Pricing For Store
    Route::prefix('store-product')->group(function () {
        Route::get('', [StoreProductController::class, 'index'])->name('store_product');
        Route::post('', [StoreProductController::class, 'store'])->name('store_product.store');
        Route::get('history', [StoreProductController::class, 'pricingHistory'])->name('store_product.product');
        Route::get('product', [StoreProductController::class, 'getProduct'])->name('store_product.product');
        Route::get('stores', [StoreProductController::class, 'getStore'])->name('store_product.stores');
        Route::put('{id}', [StoreProductController::class, 'update'])->name('store_product.update');
        Route::post('import/excel', [StoreProductController::class, 'importCsv'])->name('store_product.validate-csv');
        Route::post('set-status/style', [StoreProductController::class, 'setStatusStyle'])->name('store_product.set_status');
        Route::post('set-status/product/{sku}', [StoreProductController::class, 'setStatus'])->name('store_product.set_status');
        Route::post('clone-pricing', [StoreProductController::class, 'clonePricing'])->name('store_product.clone-pricing');
        Route::put('cancel-import-pricing/{id}/{type}', [StoreProductController::class, 'cancelImportPricing'])->name('store_product.cancel-import-pricing');
        Route::get('clone-pricing-history/{id}', [StoreProductController::class, 'clonePricingHistory'])->name('store_product.clone-pricing-history');
    });
    Route::prefix('store-shipment')->group(function () {
        Route::get('shipping', [StoreShipmentController::class, 'fetchDetailShipment'])->name('store_shipment');
        Route::get('{storeId}', [StoreShipmentController::class, 'index'])->name('store_shipment');
        Route::post('status/{storeId}', [StoreShipmentController::class, 'setStatusStoreShipmentDetail'])->name('store_shipment');
        Route::post('status-detail/{id}', [StoreShipmentController::class, 'setStatusStoreShipment'])->name('store_shipment');
    });

    Route::prefix('product-spec')->group(function () {
        Route::post('verify-csv-file', [ProductSpecController::class, 'verifyCsvFile'])->name('verify_product_spec');
        Route::post('import-csv', [ProductSpecController::class, 'importCsv'])->name('import_product_spec');
    });

    Route::prefix('product-sku-matching')->group(function () {
        Route::get('', [ProductSkuMatchingController::class, 'fetch'])->name('fetch_product_sku_matching');
        Route::delete('{id}', [ProductSkuMatchingController::class, 'delete'])->name('delete_product_sku_matching');
        Route::post('verify-csv-file', [ProductSkuMatchingController::class, 'verifyCsvFile'])->name('verify_product_sku_matching');
        Route::post('import-csv', [ProductSkuMatchingController::class, 'importCsv'])->name('import_product_sku_matching');
    });

    // quality report
    Route::get('/quality-control/report', [QualityControlController::class, 'report'])->name('quality control');
    Route::get('/quality-control/report-by-team', [QualityControlController::class, 'reportByTeam'])->name('report-by-team');

    // shipment delivery map
    Route::get('/delivery-map/statistic', [ShipmentMappingDeliveryController::class, 'statistic']);
    Route::get('/delivery-map/report-by-state', [ShipmentMappingDeliveryController::class, 'reportByState']);
    Route::get('/delivery-map/report-by-rank', [ShipmentMappingDeliveryController::class, 'reportByRank']);

    // label
    Route::prefix('label')->group(function () {
        Route::get('verify-label-id-for-order', [LabelController::class, 'scanLabelIdToVerify']);
        Route::get('', [LabelController::class, 'getDataByLabel']);
        Route::post('create-shipment', [LabelController::class, 'createShipment']);
        Route::post('buy-a-shipment', [LabelController::class, 'buyAShipment']);
        Route::post('printed-success', [LabelController::class, 'logPrintedLabelShipment']);
        Route::post('refund-shipment', [LabelController::class, 'refundShipment']);
        Route::post('fba', [LabelController::class, 'createShippingFba']);
        Route::get('print-label-history', [LabelController::class, 'getPrintLabelHistory']);
        Route::post('gelato', [LabelController::class, 'createShippingGelato']);
        Route::post('buy-a-shipment-one-call', [LabelController::class, 'buyAShipmentOneCall']);
    });
    // Claim
    Route::get('/claim', [ClaimController::class, 'fetchAll']);

    // Sellbrite sync
    Route::prefix('sellbrite')->group(function () {
        Route::get('', [SellbriteSyncController::class, 'index']);
        Route::put('{id}', [SellbriteSyncController::class, 'update']);

        Route::prefix('warehouses')->group(function () {
            Route::get('', [SellbriteWarehouseController::class, 'index']);
            Route::post('map', [SellbriteWarehouseController::class, 'map']);
        });
    });

    ///Todo forecast inventory
    Route::prefix('forecast-inventory')->group(function () {
        Route::get('', [ForecastController::class, 'index']);
    });
    ///Todo backorder
    Route::prefix('backorder')->group(function () {
        Route::post('hide-sku', [BackOrderController::class, 'hideSku']);
        Route::post('', [BackOrderController::class, 'store']);
        Route::post('verify-file-import/{id}', [BackOrderController::class, 'verifyImport'])->name('backorder');
        Route::get('import-csv-file', [BackOrderController::class, 'importCsvFile'])->name('backorder');
    });

    Route::prefix('invoices')->group(function () {
        Route::get('', [InvoiceController::class, 'index']);
        Route::get('/export/{id}/{type}', [InvoiceController::class, 'export']);
        Route::post('/generate-invoice', [InvoiceController::class, 'generateInvoice']);
        Route::get('/product-detail/temp', [InvoiceController::class, 'downloadProductionInvoice']);
        Route::get('/list/product-detail/temp', [InvoiceController::class, 'getProductionInvoiceTemp']);
    });
    // weight - cubic
    Route::get('/weight-cubic', [WeightCubicController::class, 'fetchAll'])->name('weight cubic');

    Route::prefix('filter-sale-order')->group(function () {
        Route::get('', [FilterSaleOrderController::class, 'index']);
        Route::put('{id}', [FilterSaleOrderController::class, 'update']);
        Route::get('{id}', [FilterSaleOrderController::class, 'show']);
        Route::delete('{id}', [FilterSaleOrderController::class, 'delete']);
        Route::post('', [FilterSaleOrderController::class, 'create']);
    });
    Route::prefix('product-print-area')->group(function () {
        Route::get('', [ProductPrintSideController::class, 'index']);
        Route::get('/list', [ProductPrintSideController::class, 'list']);
        Route::get('/{id}', [ProductPrintSideController::class, 'show']);
        Route::post('', [ProductPrintSideController::class, 'store']);
        Route::patch('/{id}', [ProductPrintSideController::class, 'update']);
        Route::put('/drag', [ProductPrintSideController::class, 'drag']);
    });
    Route::prefix('product-material-thickness')->group(function () {
        Route::get('/', [ProductMaterialThicknessController::class, 'index']);
        Route::post('/', [ProductMaterialThicknessController::class, 'store']);
        Route::put('/{id}', [ProductMaterialThicknessController::class, 'update']);
        Route::delete('/{id}', [ProductMaterialThicknessController::class, 'delete']);
    });
    // Mug
    Route::prefix('mug')->group(function () {
        Route::get('count-pending-priority-store', [MugController::class, 'countPendingPriorityStore']);
        Route::get('count', [MugController::class, 'count']);
        Route::get('pdf', [MugController::class, 'pdf']);
        Route::post('convert-pdf', [MugController::class, 'convertPdf']);
        Route::get('history', [MugController::class, 'history']);
        Route::put('download-pdf', [MugController::class, 'downloadPdf']);
        Route::get('history-pdf', [MugController::class, 'historyPdf']);
        Route::post('convert-to-ai', [MugController::class, 'convertToAi']);
    });
    // Mug
    Route::prefix('barcode-latex')->group(function () {
        Route::get('count-pending', [BarcodeLatexController::class, 'countPending']);
        Route::get('fetch-printing', [BarcodeLatexController::class, 'fetchPrinting']);
        Route::get('fetch-history', [BarcodeLatexController::class, 'fetchHistory']);
        Route::post('confirm', [BarcodeLatexController::class, 'confirm']);
    });
    // Mug 3D
    Route::prefix('3d')->group(function () {
        Route::get('count', [Mug3DController::class, 'count']);
        Route::get('pdf', [Mug3DController::class, 'pdf']);
        Route::post('convert-pdf', [Mug3DController::class, 'convertPdf']);
        Route::get('history', [Mug3DController::class, 'history']);
        Route::put('download-pdf', [Mug3DController::class, 'downloadPdf']);
        Route::get('history-pdf', [Mug3DController::class, 'historyPdf']);
    });
    // Poster
    Route::prefix('poster')->group(function () {
        Route::get('count', [PosterController::class, 'count']);
        Route::get('pdf', [PosterController::class, 'pdf']);
        Route::post('convert-pdf', [PosterController::class, 'convertPdf']);
        Route::put('download-pdf', [PosterController::class, 'downloadPdf']);
        Route::get('history-pdf', [PosterController::class, 'historyPdf']);
        Route::post('convert-to-ai', [PosterController::class, 'convertToAi']);
        Route::get('layouts-config', [PosterController::class, 'fetchLayoutsConfig']);
    });
    // DTF
    Route::prefix('dtf')->group(function () {
        Route::get('/preview', [DtfController::class, 'preview']);
        Route::get('count', [DtfController::class, 'count']);
        Route::get('', [DtfController::class, 'index']);
        Route::get('history', [DtfController::class, 'history']);
        Route::put('download', [DtfController::class, 'download']);
        Route::get('scan-label', [DtfController::class, 'scanLabel']);
        Route::post('generate', [DtfController::class, 'generatePdf']);
    });

    // Neck
    Route::prefix('neck')->group(function () {
        Route::get('scan-label', [NeckController::class, 'scanLabel']);
        Route::get('generate-batch-number', [NeckController::class, 'generateBatchNumber']);
        Route::get('', [NeckController::class, 'index']);
        Route::get('history', [NeckController::class, 'history']);
        Route::put('download', [NeckController::class, 'download']);
        Route::post('generate', [NeckController::class, 'generatePdf']);
    });

    Route::prefix('shipment')->group(function () {
        Route::get('', [ShipmentController::class, 'getList'])->name('shipment');
        Route::patch('manual-tracking/{id}', [ShipmentController::class, 'updateManualTracking']);
        Route::get('get-count', [ShipmentController::class, 'getCount'])->name('count-shipment');
    });

    Route::prefix('ornament')->group(function () {
        Route::get('/count', [OrnamentController::class, 'count']);
        Route::get('/count-pending-priority-store', [OrnamentController::class, 'countPendingPriorityStore']);
        Route::get('', [OrnamentController::class, 'index']);
        Route::post('', [OrnamentController::class, 'generateBarcode']);
        Route::get('/history', [OrnamentController::class, 'history']);
        Route::get('/count-pdf', [OrnamentController::class, 'countPdf']);
        Route::get('/scan-label', [OrnamentController::class, 'scanLabel']);
        Route::post('/generate-pdf', [OrnamentController::class, 'generatePdf']);
        Route::get('/list-pdf', [OrnamentController::class, 'listPdf']);
        Route::get('/history-pdf', [OrnamentController::class, 'historyPdf']);
        Route::get('/preset', [OrnamentController::class, 'preset']);
        Route::put('/download', [OrnamentController::class, 'download']);
    });

    Route::prefix('sticker')->group(function () {
        Route::get('/count', [StickerController::class, 'count']);
        Route::get('/list', [StickerController::class, 'list']);
        Route::get('/history', [StickerController::class, 'history']);
        Route::get('/preset', [StickerController::class, 'preset']);
        Route::post('/generate', [StickerController::class, 'generate']);
        Route::post('/convert-to-ai', [StickerController::class, 'convertToAi']);
    });

    Route::prefix('internal-ticket')->group(function () {
        Route::get('', [InternalTicketController::class, 'fetchAll']);
        Route::get('get-count', [InternalTicketController::class, 'getCount']);
        Route::put('{id}', [InternalTicketController::class, 'updateStatus']);
        Route::post('', [InternalTicketController::class, 'create']);
        Route::post('{id}/assign', [InternalTicketController::class, 'assign']);
    });

    Route::prefix('promotion')->group(function () {
        Route::get('', [PromotionController::class, 'fetch']);
        Route::get('get-types', [PromotionController::class, 'getPromotionTypes']);
        Route::get('{id}', [PromotionController::class, 'getDetail']);
        Route::post('', [PromotionController::class, 'create']);
        Route::put('{id}', [PromotionController::class, 'update']);
        Route::put('{id}/update-status', [PromotionController::class, 'updateStatus']);
    });

    Route::prefix('doc')->group(function () {
        Route::get('', [DocController::class, 'fetchAll']);
        Route::post('', [DocController::class, 'store']);
        Route::put('{id}', [DocController::class, 'update']);
        Route::delete('{id}', [DocController::class, 'delete']);
    });

    Route::prefix('doc-category')->group(function () {
        Route::get('', [DocCategoryController::class, 'fetchAll']);
        Route::post('', [DocCategoryController::class, 'store']);
        Route::put('{id}', [DocCategoryController::class, 'update']);
        Route::delete('{id}', [DocCategoryController::class, 'delete']);
    });

    Route::prefix('on-hold-setting')->group(function () {
        Route::get('', [OnHoldSettingController::class, 'fetchAll']);
        Route::put('{id}', [OnHoldSettingController::class, 'update']);
        Route::delete('{id}', [OnHoldSettingController::class, 'delete']);
        Route::post('', [OnHoldSettingController::class, 'create']);
    });

    Route::prefix('cost-report')->group(function () {
        Route::post('', [CostReportController::class, 'createOrUpdate']);
        Route::get('', [CostReportController::class, 'fetchAll']);
        Route::get('/history', [CostReportController::class, 'fetchAllHistory']);
        Route::get('/detail', [CostReportController::class, 'getDetailCostReport']);
        Route::post('/labor', [CostReportController::class, 'createOrUpdateLabor']);
        Route::get('/labor', [CostReportController::class, 'detailCostLabel']);
    });

    Route::prefix('manifest')->group(function () {
        Route::post('scan-tracking-number', [ShipmentManifestController::class, 'scanTrackingNumber']);
        Route::get('list-tracking', [ShipmentManifestController::class, 'getAllTrackingNumberByManifestId']);
        Route::delete('tracking-number/{id}', [ShipmentManifestController::class, 'deletedShipmentManifestTracking']);
        Route::get('pending', [ShipmentManifestController::class, 'getAllPendingManifest']);
        Route::get('printed', [ShipmentManifestController::class, 'getHistoryManifest']);
        Route::get('confirm-print', [ShipmentManifestController::class, 'confirmPrintManifest']);
        Route::get('generate-url', [ShipmentManifestController::class, 'generateManifest']);
        Route::get('print-reprint/{id}', [ShipmentManifestController::class, 'getDataPrintAndReprint']);
        Route::put('barcode-box', [ShipmentManifestController::class, 'updateBarcodeBox']);
    });
    // Todo: Production report
    Route::prefix('production-report')->group(function () {
        Route::get('', [ProductionReportController::class, 'index'])->name('production-report');
    });
    Route::prefix('supply-unit')->group(function () {
        Route::get('', [SupplyUnitController::class, 'fetchAll']);
        Route::post('', [SupplyUnitController::class, 'store']);
        Route::put('{id}', [SupplyUnitController::class, 'update']);
        Route::delete('{id}', [SupplyUnitController::class, 'delete']);
    });
    Route::prefix('supply-category')->group(function () {
        Route::get('', [SupplyCategoryController::class, 'fetchAll']);
        Route::post('', [SupplyCategoryController::class, 'store']);
        Route::put('{id}', [SupplyCategoryController::class, 'update']);
        Route::delete('{id}', [SupplyCategoryController::class, 'delete']);
    });
    Route::prefix('supply')->group(function () {
        Route::get('', [SupplyController::class, 'fetchAll']);
        Route::get('/search', [SupplyController::class, 'search']);
        Route::post('', [SupplyController::class, 'store']);
        Route::put('{id}', [SupplyController::class, 'update']);
        Route::delete('{id}', [SupplyController::class, 'delete']);
        Route::get('/{id}/quantity', [SupplyController::class, 'fetchQuantity']);
    });
    Route::prefix('supply-purchase-order')->group(function () {
        Route::get('', [SupplyPurchaseOrderController::class, 'fetchAll']);
        Route::get('summary', [SupplyPurchaseOrderController::class, 'fetchSummary']);
        Route::get('{id}', [SupplyPurchaseOrderController::class, 'fetch']);
        Route::post('', [SupplyPurchaseOrderController::class, 'store']);
        Route::put('{id}', [SupplyPurchaseOrderController::class, 'update']);
        Route::post('update-manual', [SupplyPurchaseOrderController::class, 'updateManual']);
        Route::delete('{id}', [SupplyPurchaseOrderController::class, 'delete']);
        Route::post('add-message', [SupplyPurchaseOrderController::class, 'addMessage']);
    });
    Route::prefix('supply-addition')->group(function () {
        Route::get('', [SupplyInventoryAdditionController::class, 'fetchAll']);
        Route::post('', [SupplyInventoryAdditionController::class, 'store']);
        Route::post('/revert/{id}', [SupplyInventoryAdditionController::class, 'revert']);
    });
    Route::prefix('supply-deduction')->group(function () {
        Route::get('', [SupplyInventoryDeductionController::class, 'fetchAll']);
        Route::post('', [SupplyInventoryDeductionController::class, 'store']);
        Route::post('/revert/{id}', [SupplyInventoryDeductionController::class, 'revert']);
    });
    Route::prefix('supply-inventory')->group(function () {
        Route::get('', [SupplyInventoryController::class, 'fetchAll']);
        Route::prefix('/test-count')->group(function () {
            Route::get('', [SupplyTestCountController::class, 'fetchTestCount']);
            Route::get('/get-box-in-location', [SupplyTestCountController::class, 'getAllBoxInLocation']);
            Route::get('/scan-box', [SupplyTestCountController::class, 'scanBox']);
            Route::post('/complete-test-count', [SupplyTestCountController::class, 'completeTestCount']);
            Route::get('/pause', [SupplyTestCountController::class, 'allTestCountPause']);
            Route::post('/pause', [SupplyTestCountController::class, 'pauseTestCount']);
            Route::get('/detail/{id}', [SupplyTestCountController::class, 'testCountDetail']);
        });
    });
    Route::prefix('supply-adjust')->group(function () {
        Route::get('', [SupplyAdjustInventoryController::class, 'fetchAll']);
        Route::post('', [SupplyAdjustInventoryController::class, 'store']);
    });

    Route::prefix('shipment-exportation')->group(function () {
        Route::post('tracking-number', [ShipmentExportationController::class, 'scanTracking'])->name('shipment-exportation');
        Route::get('list-tracking', [ShipmentExportationController::class, 'getAllTrackingNumberByExportationId'])->name('shipment-exportation');
        Route::delete('tracking-number/{id}', [ShipmentExportationController::class, 'deleteShipmentExportationTracking'])->name('shipment-exportation');
        Route::get('pending', [ShipmentExportationController::class, 'getAllPendingExportation'])->name('shipment-exportation');
        Route::get('history', [ShipmentExportationController::class, 'getAllHistoryExportation'])->name('shipment-exportation');
        Route::get('generate', [ShipmentExportationController::class, 'generateExportation'])->name('shipment-exportation');
    });

    Route::prefix('printer')->group(function () {
        Route::post('/', [PrinterController::class, 'store']);
        Route::get('/', [PrinterController::class, 'index']);
        Route::put('/{id}', [PrinterController::class, 'update']);
        Route::delete('/{id}', [PrinterController::class, 'destroy']);
        Route::get('/colors', [PrinterController::class, 'getColors']);
    });
    // insert printing
    Route::prefix('insert-printing')->group(function () {
        Route::get('get-label', [InsertPrintingController::class, 'getDataByLabel']);
        Route::post('printed-success', [InsertPrintingController::class, 'printSuccess']);
    });

    Route::prefix('bulb-power')->group(function () {
        Route::get('pretreat', [BulbPowerController::class, 'getBulbPowerPretreatSetting']);
        Route::post('pretreat', [BulbPowerController::class, 'setBulbPowerPretreatSetting']);
        Route::get('printing', [BulbPowerController::class, 'getBulbPowerPrintingSetting']);
        Route::post('printing', [BulbPowerController::class, 'setBulbPowerPrintingSetting']);
    });

    Route::prefix('sku-bulb-power')->group(function () {
        Route::prefix('/printing')->group(function () {
            Route::get('/list', [SkuBulbPowerController::class, 'getSkuBulbPowerPrinting']);
            Route::get('/{id}', [SkuBulbPowerController::class, 'getDetailSkuBulbPowerPrinting']);
            Route::post('/create', [SkuBulbPowerController::class, 'createSkuBulbPowerPrinting']);
            Route::put('/{id}', [SkuBulbPowerController::class, 'updateSkuBulbPowerPrinting']);
            Route::put('/toggle-active/{id}', [SkuBulbPowerController::class, 'toggleActivePowerPrinting']);
        });

        Route::prefix('/pretreat')->group(function () {
            Route::get('/list', [SkuBulbPowerController::class, 'getSkuBulbPowerPretreat']);
            Route::get('/{id}', [SkuBulbPowerController::class, 'getDetailSkuBulbPowerPretreat']);
            Route::post('/create', [SkuBulbPowerController::class, 'createSkuBulbPowerPretreat']);
            Route::put('/{id}', [SkuBulbPowerController::class, 'updateSkuBulbPowerPretreat']);
            Route::put('/toggle-active/{id}', [SkuBulbPowerController::class, 'toggleActivePowerPretreat']);
        });
    });

    Route::get('app/employee/login', [EmployeeController::class, 'loginApp']);

    Route::prefix('stock-transfer')->group(function () {
        Route::get('', [StockTransferController::class, 'index'])->name('stock_transfer_list');
        Route::get('/request-number', [StockTransferController::class, 'requestNumber'])->name('stock_transfer_request_number');
        Route::get('/count-request-pending', [StockTransferController::class, 'countRequestPending'])->name('stock_transfer_total_request_number');
        Route::get('/{id}', [StockTransferController::class, 'fetch'])->name('stock_transfer_detail');
        Route::post('', [StockTransferController::class, 'create'])->name('stock_transfer_create');
        Route::post('/scan-box-number', [StockTransferController::class, 'scanBoxNumber'])->name('stock_transfer_scan_box_number');
        Route::post('/fulfill', [StockTransferController::class, 'confirmFulfill'])->name('stock_transfer_fulfill');
        Route::post('/save', [StockTransferController::class, 'save'])->name('stock_transfer_save');
    });
    Route::prefix('detect-color')->group(function () {
        Route::post('', [DetectColorController::class, 'create'])->name('detect_color_create');
        Route::put('/{id}', [DetectColorController::class, 'update'])->name('detect_color_update');
    });
    Route::prefix('sale-order-report')->group(function () {
        Route::get('fulfillment', [SaleOrderReportController::class, 'getFulfillmentReport'])->name('fulfillment-report');
        Route::get('fulfillment-by-print-method', [SaleOrderReportController::class, 'getFulfillmentPrintMethodReport'])->name('fulfillment-report-by-method');
        Route::get('fulfillment-by-warehouse', [SaleOrderReportController::class, 'getFulfillmentWarehouse'])->name('fulfillment-report-by-warehouse');
    });

    Route::prefix('seller-support')->group(function () {
        Route::get('', [SaleOrderController::class, 'getClaimOrder']);
        Route::post('/comment', [SaleOrderController::class, 'saveComment']);
        Route::get('/comment', [SaleOrderController::class, 'getComment']);
        Route::post('/sent-email', [SaleOrderController::class, 'insertFeedback']);
        Route::post('/generate-ticket-number', [SaleOrderController::class, 'generateName']);
        Route::post('/assign', [SaleOrderController::class, 'assignClaim']);
        Route::post('/status', [SaleOrderController::class, 'updateStatusClaimOrder']);
        Route::get('/count', [SaleOrderController::class, 'getCountClaim']);
        Route::get('/count-status', [SaleOrderController::class, 'getCountClaimStatus']);
    });

    Route::prefix('global-surcharge')->group(function () {
        Route::get('/surcharge', [PeakShippingFeeController::class, 'getSurchargeFee']);
        Route::get('/list-surcharge', [PeakShippingFeeController::class, 'getListSurchargeFee']);
        Route::post('', [PeakShippingFeeController::class, 'store']);
        Route::get('status', [PeakShippingFeeController::class, 'getStatus']);
        Route::put('', [PeakShippingFeeController::class, 'updateStatus']);
    });

    Route::put('regenerate-barcode-printed/{id}', [BarcodeController::class, 'regenerateBarcode']);

    Route::prefix('sale-order/dtg-to-dtf')->group(function () {
        Route::get('/orders', [DtgToDtfController::class, 'orders']);
        Route::post('/convert', [DtgToDtfController::class, 'convert']);
    });

    Route::prefix('change-log')->group(function () {
        Route::post('', [ChangeLogController::class, 'createOrUpdate']);
        Route::get('', [ChangeLogController::class, 'getChangeLog']);
    });
    //security app
    Route::get('/list/ip/users', [AuthAppIpController::class, 'listUsers']);
    Route::get('/list/ip', [AuthAppIpController::class, 'list']);
    Route::get('/count/ip', [AuthAppIpController::class, 'countIp']);
    Route::get('/update/ip', [AuthAppIpController::class, 'updateIp']);
    Route::get('/review/ip', [AuthAppIpController::class, 'reviewIp']);
    Route::put('/ip/users', [AuthAppIpController::class, 'updateUser']);
    Route::post('/ip/users', [AuthAppIpController::class, 'createUser']);
    Route::post('/ip/create', [AuthAppIpController::class, 'createIp']);
    Route::put('/ip/create', [AuthAppIpController::class, 'manualUpdateIp']);
    Route::delete('/ip/delete/{id}', [AuthAppIpController::class, 'deleteIp']);
    Route::delete('/ip/delete/user/{id}', [AuthAppIpController::class, 'deleteUser']);
    Route::get('/list/ip/bypass', [AuthAppIpController::class, 'listBypass']);
    Route::post('ip/add/bypass', [AuthAppIpController::class, 'addByPassUsers']);
    Route::put('/ip/disable/bypass/{id}', [AuthAppIpController::class, 'disableBypass']);
    // status ip restriction
    Route::get('/ip/status', [AuthAppIpController::class, 'getStatus']);
    Route::put('/ip/status', [AuthAppIpController::class, 'updateIpRestriction']);

    Route::prefix('export-histories')->group(function () {
        Route::get('/', [ExportHistoryController::class, 'index']);
        Route::post('/', [ExportHistoryController::class, 'store']);
    });
    Route::prefix('product-roulette')->group(function () {
        Route::get('', [ProductRouletteController::class, 'index']);
        Route::post('', [ProductRouletteController::class, 'store']);
        Route::put('{id}', [ProductRouletteController::class, 'update']);
    });

    //Todo : SKU Movement Plan
    Route::prefix('sku-movement')->group(function () {
        Route::post('upload', [RbtSkuMovementController::class, 'upload']);
        Route::get('', [RbtSkuMovementController::class, 'listTasks']);
        Route::get('get-count', [RbtSkuMovementController::class, 'getCount']);
        Route::put('pick-task/{id}', [RbtSkuMovementController::class, 'pickTask']);
        Route::put('confirm-task/{id}', [RbtSkuMovementController::class, 'confirmTask']);
        Route::get('list-imports', [RbtSkuMovementController::class, 'listImports']);
        Route::get('histories', [RbtSkuMovementController::class, 'listHistories']);
    });
});
Route::group(['middleware' => ['log.api.access']], function () {
    Route::get('/shipment-exportation/download', [ShipmentExportationController::class, 'downloadExportation'])->name('shipment-exportation');

    Route::get('/inventory-overview-report/{warehouse_code}/{start_date}/{end_date}', [InventoryController::class, 'report']);
    Route::get('/supply-inventory-report-by-month/{warehouse_code}/{year_month}', [SupplyInventoryController::class, 'report']);
    Route::get('/purchase-by-vendor/{warehouse_code}/{start_date}/{end_date}', [InventoryController::class, 'reportPurchaseByVendor']);
    Route::get('/sale-by-customer/{start_date}/{end_date}', [InventoryController::class, 'reportSaleByCustomer']);
    Route::get('/inventory-overview-report-temp/{warehouse_code}/{start_date}/{end_date}', [InventoryController::class, 'reportTemp']);
    Route::get('/report-fulfillment', [SaleOrderReportController::class, 'reportTemp']);

    // Export excel shipment
    Route::get('/shipment/export', [ShipmentController::class, 'exportExcel']);
    Route::get('/sale-order/ip-violation-export', [SaleOrderController::class, 'exportExcel']);
    Route::get('/sale-order/sales-report', [SaleOrderController::class, 'salesReport']);
    Route::get('/sale-order/quantity-print-area-report', [SaleOrderController::class, 'quantityPrintAreaReport']);
    Route::get('internal-ticket/file/download', [InternalTicketController::class, 'downloadFile']);

    // Export forecast inventory excel
    Route::get('/forecast-inventory/export', [ForecastController::class, 'export']);
    // Inventory Detail Export excel
    Route::get('/inventory-detail/export', [InventoryController::class, 'inventoryDetailExportExcel']);
    Route::post('webhooks/easy-post', [LabelController::class, 'webhooks']);

    // Claim Export excel
    Route::get('/claim/export', [ClaimController::class, 'exportExcel']);

    // Export excel
    Route::get('/sale-order-item/export', [SaleOrderItemController::class, 'exportExcel']);

    // Export excel for sale order item screen
    Route::get('/sale-order-item-shipment/export', [SaleOrderItemBarcodeController::class, 'exportExcel']);

    // Purchase Export excel
    Route::get('/purchase-order-export', [PurchaseOrderController::class, 'exportExcel']);

    // Addition Export excel
    Route::get('/addition-export', [InventoryAdditionController::class, 'exportExcel']);

    // Deduction Export excel
    Route::get('/inventory-deduction/export', [InventoryDeductionController::class, 'exportExcel']);

    // Supply Purchase Export excel
    Route::get('/supply-purchase-orders/export', [SupplyPurchaseOrderController::class, 'exportExcel']);

    Route::get('/supply-addition/export', [SupplyInventoryAdditionController::class, 'exportExcel']);

    Route::get('/supply-deduction/export', [SupplyInventoryDeductionController::class, 'exportExcel']);

    Route::get('/supply-inventory/export', [SupplyInventoryController::class, 'exportExcel']);

    Route::get('/supply-adjust/export', [SupplyAdjustInventoryController::class, 'exportExcel']);

    // Manage box export excel
    Route::get('/manage-box-export', [BoxController::class, 'exportBox']);

    // export sale report open orders
    Route::get('/sale-order-export-open-order', [SaleOrderController::class, 'exportSaleOrderOpen']);

    // Language
    Route::get('/language', [LanguageController::class, 'fetchAll']);
    Route::get('/barcode/preview', [BarcodeController::class, 'previewBarcode']);

    // Setting
    Route::get('/fetch-setting-by-key', [SettingController::class, 'fetchByKey']);

    Route::get('/import/router-name', [ImportRouterName::class, 'getAllRouterName']); // api update permission from route

    Route::get('/label/download/{id}', [LabelController::class, 'downloadLabel']);
    Route::post('/ship-station/webhook/{id}', [WebhookShipStation::class, 'addWebhookShipStation']);
    Route::get('/label/download-zlp/{id}', [LabelController::class, 'downloadLabelZpl']);
    Route::get('/email-missing-price', [StoreProductController::class, 'emailMissingPriceOfStore']);

    Route::get('/back-log/{warehouseCode}/{job}', [BacklogController::class, 'index']);

    ///Todo : Country , State
    Route::get('/country', [CountryController::class, 'fetchCountry']);
    Route::get('/fetch-country-part-number', [CountryController::class, 'getCountryPartNumber']);
    Route::get('/fetch-state-by-country/{country_id}', [CountryController::class, 'fetchStateByCountry']);
    Route::put('/country/update/{id}', [CountryController::class, 'updateCountry']);
    Route::post('/country/validate-code', [CountryController::class, 'validateCodeCountry']);

    Route::get('manifest/export/{id}', [ShipmentManifestController::class, 'exportManifest']);
    Route::post('employee/department-checkin', [EmployeeController::class, 'departmentCheckin'])->name('department-checkin');
    Route::post('employee/department-checkout', [EmployeeController::class, 'departmentCheckout'])->name('department-checkout');
    Route::get('department/list-all', [DepartmentController::class, 'getListAll'])->name('department.list-all-public');
    Route::get('department/list-employee-checkin', [DepartmentController::class, 'getListEmployeeCheckin'])->name('department.list-employee-checkin');
    Route::get('department/list-employee-checkout', [DepartmentController::class, 'getListEmployeeCheckout'])->name('department.list-employee-checkout');
    Route::get('list-warehouses', [WareHouseController::class, 'getListAll'])->name('warehouse.list-all-public');

    Route::prefix('mobile')->group(function () {
        Route::get('/products/attributes', [ProductController::class, 'getProductAttributeList']);
        Route::post('/barcode/print', [BarcodeController::class, 'mobilePrintBarcode']);
        Route::post('/login', [EmployeeController::class, 'mobileLogin']);
        Route::post('/barcode/reprint', [BarcodeController::class, 'mobileReprintBarcode']);
        Route::post('/barcode/error', [BarcodeController::class, 'mobileBarcodeError']);
        Route::get('/barcode', [BarcodeController::class, 'mobileBarcode']);
        Route::post('/barcode/printed', [BarcodeController::class, 'mobileBarcodePrinted']);
        Route::post('/barcode/{id}/status-print', [BarcodeController::class, 'mobileBarcodeStatusPrint']); // app printer
        Route::get('/barcode/reprint', [BarcodeController::class, 'mobileListReprintBarcode']);
        Route::get('/barcode/{id}', [BarcodeController::class, 'mobileBarcodeDetail']);
        Route::post('/printer/ping', [ProductStyleController::class, 'printerPing']);
        // Route::post('/firebase', [FirebaseController::class, 'testFirebase']);
        Route::post('/firebase-noti', [FirebaseController::class, 'testNotificationFirebase']);
        Route::post('/firebase-topic', [FirebaseController::class, 'testTopicFirebase']);
        Route::post('/firebase-topic/sub', [FirebaseController::class, 'testTopicFirebaseSub']);
        Route::post('/firebase-topic/unsub', [FirebaseController::class, 'testTopicFirebaseUnSub']);
        Route::get('/firebase/info', [FirebaseController::class, 'testFirebaseInfo']);
        Route::post('/deduction-by-batch-id', [BarcodeController::class, 'mobileDeductionByBatchId']);
        // Route::post('/test-print', [BarcodeController::class, 'testPrint']);
    });
    Route::post('tick-print-file-error', [SaleOrderItemImageController::class, 'tickPrintFileError'])->name('tick-print-file-error');
    Route::post('retry-job-image', [SaleOrderItemImageController::class, 'retryJob'])->name('retry-job-image');
    Route::post('reset-image', [SaleOrderItemImageController::class, 'resetImage'])->name('reset-image');
    Route::post('push-job-notify', [SaleOrderController::class, 'pushJobNotify'])->name('push-job-notify');
    Route::prefix('internal-request')->group(function () {
        Route::get('/location/{barcode}', [InternalRequestController::class, 'getLocationAndRequest']);

        Route::get('', [InternalRequestController::class, 'getList']);
        Route::get('picking-up/{employee_id}', [InternalRequestController::class, 'getPickingUp']);
        Route::get('check-exist-request', [InternalRequestController::class, 'checkExistRequest']);
        Route::put('prepare-data', [InternalRequestController::class, 'prepareData']);
        Route::get('get-count-status', [InternalRequestController::class, 'getCountStatus']);
        Route::get('check-valid-type', [InternalRequestController::class, 'checkValidTypeBySku']);
        Route::get('get-count', [InternalRequestController::class, 'getCount']);
        Route::put('update-priority/{id}', [InternalRequestController::class, 'updatePriority']);
        Route::put('release-request/{id}', [InternalRequestController::class, 'release']);
        Route::put('confirm-request/{id}', [InternalRequestController::class, 'confirm']);
        Route::put('receive-request/{id}', [InternalRequestController::class, 'receiveRequest']);
        Route::put('delete/{id}', [InternalRequestController::class, 'delete']);
        Route::put('reject/{id}', [InternalRequestController::class, 'reject']);

        Route::put('{id}', [InternalRequestController::class, 'fulfillRequest']);
        Route::post('', [InternalRequestController::class, 'create']);
        Route::post('missing-box', [InternalRequestController::class, 'missingBox']);
        Route::get('all-box-missing', [InternalRequestController::class, 'allBoxMissing']);
        Route::get('login', [InternalRequestController::class, 'login']);
        Route::get('available-box', [InternalRequestController::class, 'availableBox']);
        Route::get('rbt/get-count', [InternalRequestController::class, 'getCountRbtRequests']);
        Route::put('rbt/confirm', [RbtSupportController::class, 'receivedBox']);
        Route::post('rbt/create', [RbtSupportController::class, 'createInternalRequest']);
    });
    Route::prefix('rbt')->group(function () {
        Route::post('replenishment-to-rack', [RbtSupportController::class, 'movingReplenishmentToRack']);
        Route::get('get-sku-by-wip', [RbtSupportController::class, 'getSkuByWip']);
    });

    Route::get('products/get-by-sku-or-label', [ProductController::class, 'getProductBySkuOrLabel']);
    Route::get('/export-packing-slip', [StockTransferController::class, 'exportPackingSlip'])->name('export-packing-slip');
    Route::get('/stock-transfer/report/{id}', [StockTransferController::class, 'exportStockTransfer'])->name('report-stock-transfer');
    Route::put('retry-create-thumb/{orderId}', [SaleOrderController::class, 'retryCreateThumbForOrder']);
    Route::get('fix-ship-date-shipment/{id}', [ShipmentController::class, 'fixShipDateShipment']);
    Route::get('/order-insert/export', [SaleOrderController::class, 'exportInsert']);
    Route::get('/retry-generate-barcode', [BarcodeController::class, 'generateBarcode']);
    Route::prefix('product-tiktok')->group(function () {
        Route::get('', [ProductTiktokController::class, 'index'])->name('product_tiktok.index');
        Route::post('create', [ProductTiktokController::class, 'store'])->name('product_tiktok.index');
        Route::put('update/{id}', [ProductTiktokController::class, 'update'])->name('product_tiktok.index');
    });
    Route::prefix('product-rbt')->group(function () {
        Route::get('', [RbtSupportController::class, 'listSku']);
        Route::post('create', [RbtSupportController::class, 'storeSku']);
        Route::put('update/{id}', [RbtSupportController::class, 'updateSku']);
        Route::post('import', [RbtSupportController::class, 'importSku']);
        Route::post('remove-all', [RbtSupportController::class, 'removeSkus']);
    });

    Route::prefix('report-history')->group(function () {
        Route::get('/', [ReportController::class, 'index']);
        Route::post('/', [ReportController::class, 'store']);
        Route::post('/export', [ReportController::class, 'saleReport']);
    });
    Route::prefix('icc-profile')->group(function () {
        Route::post('', [ProductStyleIccProfileController::class, 'storeIccProfile']);
        Route::post('signed-url', [ProductStyleIccProfileController::class, 'generateSignedUrl']);
        Route::put('{id}', [ProductStyleIccProfileController::class, 'updateIccProfile']);
        Route::get('', [ProductStyleIccProfileController::class, 'getIccProfile']);
    });
    Route::prefix('/rbt-count-sticker')->group(function () {
        Route::get('', [RbtCountStickerController::class, 'fetchCountSticker']);
        Route::post('/generate', [RbtCountStickerController::class, 'generate']);
        Route::post('printed', [RbtCountStickerController::class, 'printed']);
    });
});

Route::post('/webhook/detect', [VisuaController::class, 'receiveDetectResponse']);
Route::prefix('supply-report')->group(function () {
    Route::get('/ink-consumption', [SupplyReportController::class, 'getInkConsumption']);
    Route::get('/ink-consumption-forecast', [SupplyReportController::class, 'getInkConsumptionForecast']);
});

Route::prefix('/looker-studio')->group(function () {
    Route::prefix('/late-order')->group(function () {
        Route::get('/data-print-method', [InsightLateOrderController::class, 'getDataPrintMethod']);
        Route::get('/data-print-method-table', [InsightLateOrderController::class, 'getDataPrintMethodTable']);
        Route::get('/data-order-type', [InsightLateOrderController::class, 'getDataOrderType']);
        Route::get('/data-order-warehouse', [InsightLateOrderController::class, 'getDataOrderWarehouse']);
        Route::get('/data-product-sku-table', [InsightLateOrderController::class, 'getDataProductSkuTable']);
        Route::get('/data-overview-total', [InsightLateOrderController::class, 'getDataOverviewTotal']);
        Route::get('/list', [InsightLateOrderController::class, 'getList']);
        Route::get('/list/export', [InsightLateOrderController::class, 'exportList']);
    });
});

Route::prefix('/looker-studio')->group(function () {
    Route::prefix('/adjustment')->group(function () {
        Route::get('/overview', [InsightAdjustmentController::class, 'getOverview']);
        Route::get('/total-shipment', [InsightAdjustmentController::class, 'totalShipment']);
        Route::get('/data-by-warehouse', [InsightAdjustmentController::class, 'getDataByWarehouse']);
        Route::get('/data-by-reason', [InsightAdjustmentController::class, 'getDataByReason']);
        Route::get('/data-by-type', [InsightAdjustmentController::class, 'getDataByType']);
        Route::get('/data-shipment-by-type', [InsightAdjustmentController::class, 'getDataShipmentByType']);
        Route::get('/data-by-order-type', [InsightAdjustmentController::class, 'getDataByOrderType']);
    });

    Route::prefix('/adjustment/size')->group(function () {
        Route::get('/data-chart', [InsightAdjustmentSizeController::class, 'getDataChart']);
        Route::get('/data-table', [InsightAdjustmentSizeController::class, 'getDataTable']);
    });

    Route::prefix('/adjustment/style')->group(function () {
        Route::get('/data-chart', [InsightAdjustmentStyleController::class, 'getDataChart']);
        Route::get('/data-table', [InsightAdjustmentStyleController::class, 'getDataTable']);
    });

    Route::prefix('/adjustment/export')->group(function () {
        Route::get('/', [InsightAdjustmentExportController::class, 'index']);
        Route::get('/excel', [InsightAdjustmentExportController::class, 'exportExport']);
    });
});

Route::prefix('/looker-studio')->group(function () {
    Route::prefix('/fulfillment-time/ticktok-orders')->group(function () {
        Route::get('/overview', [InsightTiktokOrdersFulfillmentTimeController::class, 'getOverview']);
        Route::get('/list-data', [InsightTiktokOrdersFulfillmentTimeController::class, 'getListData']);
        Route::get('/performance', [InsightTiktokOrdersFulfillmentTimeController::class, 'getPerformance']);
    });

    Route::prefix('/fulfillment-time/label-orders')->group(function () {
        Route::get('/overview', [InsightLabelOrdersFulfillmentTimeController::class, 'getOverview']);
        Route::get('/list-data', [InsightLabelOrdersFulfillmentTimeController::class, 'getListData']);
        Route::get('/performance', [InsightLabelOrdersFulfillmentTimeController::class, 'getPerformance']);
    });
});

Route::prefix('/looker-studio')->group(function () {
    Route::prefix('/offenders-department')->group(function () {
        Route::get('/report-setting', [InsightOffendersDepartmentController::class, 'getReportSetting']);
        Route::get('/data-report', [InsightOffendersDepartmentController::class, 'getDataReport']);
    });
});

Route::prefix('/looker-studio')->group(function () {
    Route::prefix('/adjustment')->group(function () {
        Route::get('/overview', [InsightAdjustmentController::class, 'getOverview']);
        Route::get('/total-shipment', [InsightAdjustmentController::class, 'totalShipment']);
        Route::get('/data-by-warehouse', [InsightAdjustmentController::class, 'getDataByWarehouse']);
        Route::get('/data-by-reason', [InsightAdjustmentController::class, 'getDataByReason']);
        Route::get('/data-by-type', [InsightAdjustmentController::class, 'getDataByType']);
        Route::get('/data-shipment-by-type', [InsightAdjustmentController::class, 'getDataShipmentByType']);
        Route::get('/data-by-order-type', [InsightAdjustmentController::class, 'getDataByOrderType']);
    });

    Route::prefix('/adjustment/size')->group(function () {
        Route::get('/data-chart', [InsightAdjustmentSizeController::class, 'getDataChart']);
        Route::get('/data-table', [InsightAdjustmentSizeController::class, 'getDataTable']);
    });

    Route::prefix('/adjustment/style')->group(function () {
        Route::get('/data-chart', [InsightAdjustmentStyleController::class, 'getDataChart']);
        Route::get('/data-table', [InsightAdjustmentStyleController::class, 'getDataTable']);
    });

    Route::prefix('/adjustment/export')->group(function () {
        Route::get('/', [InsightAdjustmentExportController::class, 'index']);
        Route::get('/excel', [InsightAdjustmentExportController::class, 'exportExport']);
    });
});

Route::prefix('/licenses')->group(function () {
    Route::prefix('holders')->group(function () {
        Route::get('', [LicensedHolderController::class, 'getListHolders']);
        Route::post('', [LicensedHolderController::class, 'storeHolders']);
        Route::put('{id}', [LicensedHolderController::class, 'updateHolders']);
    });

    Route::prefix('products')->group(function () {
        Route::get('', [LicensedProductController::class, 'getListProducts']);
        Route::post('', [LicensedProductController::class, 'storeProducts']);
        Route::put('{id}', [LicensedProductController::class, 'updateProducts']);
        Route::post('validate', [LicensedProductController::class, 'validateProduct']);
    });

    Route::prefix('designs')->group(function () {
        Route::post('', [LicensedDesignController::class, 'storeDesigns']);
        Route::put('{id}', [LicensedDesignController::class, 'updateDesigns']);
        Route::post('import', [LicensedDesignController::class, 'import']);
        Route::post('download-error', [LicensedDesignController::class, 'downloadError']);
        Route::get('', [LicensedDesignController::class, 'getLicensedDesigns']);
    });
});

include 'api-seller.php';
include 'api-embroidery.php';
include 'api-tech-support.php';
include 'api-rbt.php';
include 'api-lavender.php';
include 'api-accounting.php';

Route::get('/test', [RbtCountStickerController::class, 'handle']);
