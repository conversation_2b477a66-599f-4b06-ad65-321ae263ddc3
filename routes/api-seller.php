<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\AuthSellerController;
use App\Http\Controllers\ChangeLogController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\ImportOrderCsvController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LicensedHolderController;
use App\Http\Controllers\PeakShippingFeeController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductStyleController;
use App\Http\Controllers\SaleOrderController;
use App\Http\Controllers\SaleOrderReportController;
use App\Http\Controllers\SellerController;
use App\Http\Controllers\ShippingMethodController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\StoreProductController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TeamMemberController;
use App\Http\Controllers\TeamMemberRoleController;
use App\Http\Controllers\TopupController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\WalletReceiptController;
use App\Http\Controllers\WalletTransactionController;
use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

Route::prefix('seller')->group(function () {
    Route::post('login', [AuthSellerController::class, 'login']);
    Route::post('google/login', [AuthController::class, 'loginWithGoogle']);
    Route::prefix('stripe')->group(function () {
        Route::post('webhook', [WebhookController::class, 'handleWebhook'])->name('webhook');
    });

    Route::middleware(['auth.support.seller'])->group(function () {
        Route::post('select-store/{id}', [StoreController::class, 'selectStore'])->name('select_store');
    });

    Route::middleware(['auth.seller'])->group(function () {
        Route::name('no_permission.')->group(function () {
            Route::get('me', [SellerController::class, 'me']);
            Route::get('dashboard', [SellerController::class, 'dashboard']);
            Route::post('change-store', [StoreController::class, 'changeStore']);
            Route::get('get-store', [StoreController::class, 'getStoreUser'])->name('get_store');
            Route::prefix('global-surcharge')->group(function () {
                Route::get('status', [PeakShippingFeeController::class, 'getStatusForSeller']);
            });
            Route::prefix('shipping-method')->group(function () {
                Route::get('get-all', [ShippingMethodController::class, 'getAllForSeller'])->name('get_all_shipping_method');
            });
            Route::prefix('change-log')->group(function () {
                Route::get('', [ChangeLogController::class, 'getChangeLog'])->name('change_log');
            });
            Route::get('tags', [TagController::class, 'fetchAll'])->name('get_tags');
            Route::get('countries', [CountryController::class, 'fetchCountry']);
            Route::get('states/{country_id}', [CountryController::class, 'fetchStateByCountry']);
        });

        Route::prefix('orders')->group(function () {
            Route::name('fulfillment_report.view.')->group(function () {
                Route::get('get-type', [ProductStyleController::class, 'getTypesForSeller'])->name('get_product_types');
                Route::get('fulfillment-report', [SaleOrderReportController::class, 'getFulfillmentReportForSeller']);
                Route::get('statistic/sla-order', [SaleOrderController::class, 'statisticOrderSla'])->name('statistic_order_sla.view');
                Route::get('list/sla-order', [SaleOrderController::class, 'getOrderSla'])->name('list_order_sla.view');
            });

            Route::name('sales_report.view.')->group(function () {
                Route::get('report', [SaleOrderController::class, 'getReportByDay']);
            });

            Route::name('orders.view.')->group(function () {
                Route::get('', [SaleOrderController::class, 'getListOrder'])->name('list_order.view');
                Route::get('detail/{id}', [SaleOrderController::class, 'getOrderDetail'])->name('order_detail.view');
                Route::get('summary', [SaleOrderController::class, 'getSummary'])->name('summary_order.view');
                Route::post('sent-email', [SaleOrderController::class, 'uploadFile'])->name('upload_file');
                Route::get('licenses', [LicensedHolderController::class, 'getListHolders']);
            });

            Route::name('orders.edit.')->group(function () {
                Route::post('duplicate', [SaleOrderController::class, 'duplicateOrder'])->name('duplicate_order');
                Route::put('{id}', [SaleOrderController::class, 'sellerSubmitOrder'])->name('submit_order');
                Route::post('update-shipping-method', [SaleOrderController::class, 'updateSellerShippingMethod'])->name('update_shipping_method');
                Route::put('cancel-order/{id}', [SaleOrderController::class, 'cancelOrder'])->name('cancel_order');
                Route::post('upload-csv', [SaleOrderController::class, 'importOrders'])->name('import_order');
                Route::get('import-csv', [ImportOrderCsvController::class, 'getList'])->name('import_order_csv.view');
            });

            Route::name('claims.view.')->group(function () {
                Route::get('claims', [SaleOrderController::class, 'getClaimSupport'])->name('claim_support.view');
                Route::get('claims/{id}', [SaleOrderController::class, 'getClaimDetail'])->name('claim_detail.view');
            });
        });

        Route::prefix('catalog')->group(function () {
            Route::name('catalog.view.')->group(function () {
                Route::get('export-pricing', [ProductController::class, 'exportPricing'])->name('export_pricing');
                Route::get('list', [ProductController::class, 'getList'])->name('list_catalog.view');
                Route::get('detail/{id}', [ProductController::class, 'getCatalogDetail'])->name('catalog_detail.view');
            });

            Route::post('', [ProductController::class, 'sendWholesaleInfo'])->name('send_wholesale_info');
        });

        Route::prefix('pricing')->group(function () {
            Route::name('catalog.view.')->group(function () {
                Route::get('fetch', [StoreProductController::class, 'fetch'])->name('fetch_pricing');
                Route::get('print-price/{id}', [StoreProductController::class, 'getPrintPriceDetail']);
                Route::get('detail/{id}', [StoreProductController::class, 'getPricingDetail'])->name('pricing_detail.view');
                Route::get('styles/type', [ProductStyleController::class, 'getType']);
            });
        });

        Route::prefix('store')->group(function () {
            Route::name('catalog.view.')->group(function () {
                Route::get('{storeId}/styles', [ProductController::class, 'fetchStyles'])->name('catalog_store_style');
            });

            Route::name('api.view.')->group(function () {
                Route::get('api-key', [StoreController::class, 'getApiKey']);
            });
        });

        Route::prefix('invoices')->group(function () {
            Route::name('wallet_invoices.view.')->group(function () {
                Route::get('', [InvoiceController::class, 'index'])->name('list_invoice.view');
                Route::get('export/{id}/{type}', [InvoiceController::class, 'export'])->name('export_invoice');
            });
        });

        Route::prefix('topups')->group(function () {
            Route::name('wallet_invoices.view.')->group(function () {
                Route::get('', [TopupController::class, 'getListTopup'])->name('list_topup.view');
                Route::get('wallet', [WalletController::class, 'getWallet'])->name('wallet.view');
            });

            Route::name('wallet_invoices.edit.')->group(function () {
                Route::post('deposit', [TopupController::class, 'deposit'])->name('topups.deposit');
                Route::post('retry/{signature}', [TopupController::class, 'retryTopup'])->name('topups.retry');
            });
        });

        Route::prefix('{paymentGateway}')->group(function () {
            Route::name('wallet_invoices.view.')->group(function () {
                Route::get('payment-method', [TopupController::class, 'paymentMethods'])->name('payment_methods');
                Route::get('payment-fee', [TopupController::class, 'paymentFee'])->name('payment_fee');
                Route::get('key', [TopupController::class, 'getKey'])->name('get_key');
                Route::get('auto-refill', [TopupController::class, 'getAutoRefill'])->name('auto_refill.view');
            });

            Route::name('wallet_invoices.edit.')->group(function () {
                Route::post('payment-method', [TopupController::class, 'addPaymentMethod'])->name('add_payment_method');
                Route::delete('payment-method/{paymentMethodId}', [TopupController::class, 'removePaymentMethod'])->name('remove_payment_method');
                Route::post('connection', [TopupController::class, 'connection'])->name('connection');
                Route::post('auto-refill', [TopupController::class, 'autoRefill'])->name('auto_refill');
            });
        });

        Route::prefix('transactions')->group(function () {
            Route::name('wallet_invoices.view.')->group(function () {
                Route::get('', [WalletTransactionController::class, 'getListTransactions'])->name('list_transaction.view');
            });
        });

        Route::prefix('receipts')->group(function () {
            Route::name('wallet_invoices.view.')->group(function () {
                Route::get('{id}/download', [WalletReceiptController::class, 'downloadReceipt'])->name('download_receipt');
            });
        });

        Route::prefix('roles')->group(function () {
            Route::name('roles.view.')->group(function () {
                Route::get('', [TeamMemberRoleController::class, 'getRoles'])->name('list_role.view');
                Route::get('{id}', [TeamMemberRoleController::class, 'showRole'])->name('role_detail.view');
            });
            Route::name('roles.edit.')->group(function () {
                Route::post('', [TeamMemberRoleController::class, 'addRole'])->name('add_role');
                Route::put('{id}', [TeamMemberRoleController::class, 'updateRole'])->name('update_role');
                Route::delete('{id}', [TeamMemberRoleController::class, 'deleteRole'])->name('delete_role');
            });
        });

        Route::prefix('members')->group(function () {
            Route::name('members.view.')->group(function () {
                Route::get('', [TeamMemberController::class, 'getMembers'])->name('list_member.view');
                Route::get('{id}', [TeamMemberController::class, 'showMember'])->name('member_detail.view');
            });
            Route::name('members.edit.')->group(function () {
                Route::post('', [TeamMemberController::class, 'addMember'])->name('add_member');
                Route::put('{id}', [TeamMemberController::class, 'updateMember'])->name('update_member');
                Route::delete('{id}', [TeamMemberController::class, 'deleteMember'])->name('delete_member');
            });
        });
    });
});
