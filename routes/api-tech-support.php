<?php

use App\Http\Controllers\Tech\TechSupportController;
use Illuminate\Support\Facades\Route;

Route::prefix('tech')->middleware(['jwt', 'auth.tech'])->group(function () {
    Route::put('update-label-url/{id}', [TechSupportController::class, 'updateLabelUrl'])->name('tech.update-label-url');
    Route::put('update-ip-violation-image', [TechSupportController::class, 'updateIPViolationImage'])->name('tech.update-ip-violation-image');
    Route::put('mark-ip-violation-image', [TechSupportController::class, 'markIPViolationImage'])->name('tech.mark-ip-violation-image');
    Route::put('retry-visua-detect-image-by-session-id', [TechSupportController::class, 'retryVisuaDetectImageBySessionId'])->name('tech.retry-visua-detect-image-by-session-id');
    Route::put('update-order-status/{id}', [TechSupportController::class, 'updateOrderStatus'])->name('tech.update-order-status');
    Route::post('insert-product-tiktok', [TechSupportController::class, 'insertProductTiktok'])->name('tech.insert-product-tiktok');

    // Invoice
    Route::post('invoice/re-generate', [TechSupportController::class, 'reGenerateInvoice'])->name('tech.invoice.re-generate');

    //detect shipping method red version 2024-06-11
    Route::post('detect-shipping-method-rd', [TechSupportController::class, 'detectShippingMethodRb'])->name('tech.detech-shipping-method-rbv2');

    //Purchase-Order
    Route::put('purchase-order/{id}', [TechSupportController::class, 'updatePurchaseOrder'])->name('tech.update-purchase-order');

    Route::post('alert-qc', [TechSupportController::class, 'alertQc'])->name('tech.alert-qc');

    //dispatch update order status
    Route::post('dispatch-update-order-status', [TechSupportController::class, 'dispatchUpdateOrderStatus'])->name('tech.dispatch-update-order-status');
    Route::post('calculate-work-hour', [TechSupportController::class, 'calculateWorkedHour']);

    Route::put('dispatch-order-gelato/{id}', [TechSupportController::class, 'dispatchOrderGelato'])->name('tech.dispatch-update-order-status');

    Route::post('dispatch-order-shipped-rb', [TechSupportController::class, 'dispatchOrderShippedRB'])->name('tech.dispatch-update-order-status-RB');

    Route::post('dispatch-order-id-shipped-rb', [TechSupportController::class, 'dispatchOrderIdShippedRB'])->name('tech.dispatch-update-order-status-RB-order-id');

    Route::post('delete-shipment-label-s3', [TechSupportController::class, 'deleteShipmentLabelS3'])->name('tech.delete-shipment-label-s3');

    Route::get('update-shipment-url-for-store-hplg', [TechSupportController::class, 'updateShipmentUrlForStoreHPLG'])->name('tech.update-shipment-url');

    Route::put('dispatch-image-for-create-thumb/{imageId}', [TechSupportController::class, 'dispatchImageForCreateThumb'])->name('tech.dispatch-image-for-create-thumb');
    Route::put('dispatch-image-for-upload-s3/{imageId}', [TechSupportController::class, 'dispatchImageForUploadS3'])->name('tech.dispatch-image-for-upload-s3');
    Route::put('dispatch-image-for-detect-color/{imageId}', [TechSupportController::class, 'dispatchImageForDetectColor'])->name('tech.dispatch-image-for-detect-color');
    Route::post('ip-violations', [TechSupportController::class, 'markIpByFileUpload']);

    Route::post('void-shipment', [TechSupportController::class, 'voidShipment'])->name('tech.voidShipment');
});
