<?php

use App\Http\Controllers\Rbt\RbtSupportController;
use Illuminate\Support\Facades\Route;

Route::prefix('rbt')->middleware('auth.rbt')->group(function () {
    Route::post('/wip/{id}/pickup', [RbtSupportController::class, 'deduction']);
    Route::post('/wip/print', [RbtSupportController::class, 'print']);
    Route::get('/wip/print', [RbtSupportController::class, 'getPdfFile']);

    Route::post('/wip/confirm-print', [RbtSupportController::class, 'confirmBarcodePrinted']);

    Route::post('/create/orders', [RbtSupportController::class, 'createOrders']);

    Route::get('/wips', [RbtSupportController::class, 'getListWips']);
    Route::get('/wips/{id}', [RbtSupportController::class, 'getWip']);
    Route::post('/wips/{id}/receive', [RbtSupportController::class, 'updateReceivedWip']);
    Route::post('/wip/pre-confirmation-count', [RbtSupportController::class, 'preConfirmationCount']);

    Route::post('/receive-box', [RbtSupportController::class, 'receivedBox']);
    Route::get('/box/{id}', [RbtSupportController::class, 'getBox']);

    Route::post('/import-sku', [RbtSupportController::class, 'importSkus']);
    // inventory
    Route::post('/inventory/adjustment', [RbtSupportController::class, 'createAdjustment']);
    Route::post('/wip/create-internal-request', [RbtSupportController::class, 'createInternalRequest']);
    Route::post('/internal-requests', [RbtSupportController::class, 'createInternalRequest']);
    Route::get('/internal-requests', [RbtSupportController::class, 'getListInternalRequest']);
    Route::get('/inventory', [RbtSupportController::class, 'rbtFetchInventoryOverview']);

    Route::post('/kitting', [RbtSupportController::class, 'markAsKitted']);
    Route::post('/kitting-dispatched/{id}', [RbtSupportController::class, 'markAsDispatched']);

    Route::post('/unmark', [RbtSupportController::class, 'RbtUnmarkOrders']);

    Route::get('/shipping-label', [RbtSupportController::class, 'shippingLabel']);
    Route::post('/shipping-label-printed', [RbtSupportController::class, 'shippingLabelPrinted']);
});
