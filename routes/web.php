<?php

use App\Http\Controllers\BacklogController;
use App\Http\Controllers\Dl2400PrinterController;
use App\Http\Controllers\DtfPrintingController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\GiftMessageController;
use App\Http\Controllers\LatexPrintingController;
use App\Http\Controllers\MockupController;
use App\Http\Controllers\PerformanceReportController;
use App\Http\Controllers\PretreatController;
use App\Http\Controllers\PrintingController;
use App\Http\Controllers\SaleOrderController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\ToolController;
use App\Http\Controllers\Web\QuickBookAuthController;
use App\Http\Controllers\Web\QuickBookController;
use App\Http\Controllers\Web\SupplyController;
use App\Repositories\InvoiceRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});
//define router home for stripe
Route::get('/home', function () {
    return view('welcome');
})->name('home');

Route::prefix('quick-book')->group(function () {
    Route::get('/login-form', [QuickBookAuthController::class, 'showLoginForm'])
        ->name('quick_book.login.form');
    Route::post('/login', [QuickBookAuthController::class, 'login'])
        ->name('quick_book.login');
    Route::get('/callback', [QuickBookAuthController::class, 'callback'])
        ->name('quick_book.callback');
    Route::get('/company-info', [QuickBookAuthController::class, 'getCompanyInfo'])
        ->name('quick_book.company.info');
    Route::post('/webhook', [QuickBookController::class, 'handleWebhook']);

    Route::get('/bill-payment/{id}', function ($id) {
        dd(resolve(\App\Services\QBBillPaymentService::class)->FindById($id));
    });

    Route::get('/bill/{id}', function ($id) {
        dd(resolve(\App\Services\QBBillService::class)->FindById($id));
    });
});

Route::group(['middleware' => 'log.api.access'], function () {
    Route::get('/backlog/fetch', [BacklogController::class, 'fetchBackLog']);
    //log ip
    Route::get('/export-sku', [ToolController::class, 'exportSku']);
    Route::get('/export-inventory', [ToolController::class, 'exportInventory']);

    Route::get('/pretreat', [PretreatController::class, 'getPretreat'])->name('pretreat');
    Route::get('/all-in-one', [PretreatController::class, 'allInOne'])->name('allInOne');
    Route::get('/all-in-one/settings', [PretreatController::class, 'getSettings']);
    Route::post('/all-in-one/settings', [PretreatController::class, 'storeSettings']);
    Route::get('/all-in-one/two-items', [PretreatController::class, 'allInOneTwoItems']);
    Route::get('/all-in-one/four-items', [PretreatController::class, 'allInOneFourItems']);
    Route::post('/pretreat', [PretreatController::class, 'updatePretreat'])->name('updatePretreat');

    //log ip
    Route::get('/printing', [PrintingController::class, 'getPrintingNew']);
    Route::post('/printing', [PrintingController::class, 'updatePrinting']);
    Route::post('/printing/change-ink-color', [PrintingController::class, 'changeInkColor']);
    Route::post('/printing/ink', [PrintingController::class, 'updateInkCC']);

    Route::post('/printing/write-log-printer', [PrintingController::class, 'writeLogPrinter']);
    Route::post('/printing/send-mail-printer', [PrintingController::class, 'sendMailPrinter']);
    Route::get('/printing-embroidery', [PrintingController::class, 'getPrintingEmbroidery']);
    Route::get('/printing/scan-label', [\App\Http\Controllers\PrintingController::class, 'getPrintingEmbroideryByLabel']);

    Route::group(['middleware' => ['employee.access']], function () {
        Route::post('/printing/performance-report', [PerformanceReportController::class, 'fetchEmployeePerformance']);
    });

    //log ip
    Route::get('/employee/code/{id}', [EmployeeController::class, 'getEmployeeByCodeWithoutWarehouse']);
    Route::post('/employee/confirm-cleaned-machine/{id}', [EmployeeController::class, 'confirmCleanedMachine']);
    Route::post('/employee/time-tracking', [EmployeeController::class, 'timeChecking'])->name('time-tracking');

    Route::get('/storage/mockup/{sku}/{print_side}', [MockupController::class, 'generate']);

    Route::get('/sale-order/export-report', [SaleOrderController::class, 'exportReport']);

    Route::get('/shipment/export-report-store', [ShipmentController::class, 'exportReportShipmentStore']);

    Route::prefix('dtf')->group(function () {
        Route::get('/scan-label', [DtfPrintingController::class, 'scanLabel']);
        Route::get('/printing', [DtfPrintingController::class, 'printing']);
    });

    Route::prefix('latex-printing')->group(function () {
        Route::get('/scan-label', [LatexPrintingController::class, 'scanLabel']);
        Route::get('/generate-print-file/{labelId}', [LatexPrintingController::class, 'generatePrintFile']);
    });

    Route::prefix('/dl2400')->group(function () {
        Route::get('/device/{deviceId}', [Dl2400PrinterController::class, 'getDevice']);
        Route::put('/device/{deviceId}', [Dl2400PrinterController::class, 'updateDevice']);
        Route::post('/log/{deviceId}', [Dl2400PrinterController::class, 'pushLog']);
    });
});

Route::get('/invoice/re-generate-invoice', function (Request $request, InvoiceRepository $invoiceRepo) {
    $params = $request->all();
    if (empty($params['invoice_id'])) {
        return response()->json([
            'status' => false,
            'message' => 'Wrong params.',
        ]);
    }

    return $invoiceRepo->reGenerateInvoice($params);
});

//ko can
Route::get('/card-message', [GiftMessageController::class, 'cardGiftMessage'])->name('card-message');

Route::prefix('supply')->group(function () {
    Route::get('/', [SupplyController::class, 'index'])
        ->name('supply.report')
        ->middleware('auth.supply');
    Route::get('/forecast', [SupplyController::class, 'forecast'])
        ->name('supply.forecast')
        ->middleware('auth.supply');
    Route::get('/login', [SupplyController::class, 'loginForm'])
        ->name('supply.login_form');
    Route::post('/login', [SupplyController::class, 'login'])
        ->name('supply.login');
    Route::get('/logout', [SupplyController::class, 'logout'])
        ->name('supply.logout')
        ->middleware('auth.supply');
});
