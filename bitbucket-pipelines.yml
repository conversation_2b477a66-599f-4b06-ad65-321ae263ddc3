pipelines:
  pull-requests:
    '**':
      - step:
          name: master
          deployment: master
          image: kimcuong2407/swiftpod-php8.0-fpm
          script:
            - composer install
            - if [[ ${BITBUCKET_PR_DESTINATION_BRANCH} = 'master' ]]; then php artisan test --env=staging; fi
          services:
            - mariadb

  branches:
    production:
    - parallel:
      - step:
          name: production
          deployment: production
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: worker
          deployment: worker
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: worker2
          deployment: worker2
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: worker3
          deployment: worker3
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: worker4
          deployment: worker4
          image: alpine:latest
          script:
            - pipe: atlassian/rsync-deploy:0.12.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_SERVER
                REMOTE_PATH: $FTP_PATH
                SSH_PORT: $FTP_PORT
                LOCAL_PATH: './**'
                DELETE_FLAG: 'false'
                EXTRA_ARGS: '-arz --exclude=.git/* --exclude=tests/*'
      - step:
          name: worker1-redis
          deployment: worker1-redis
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: app1
          deployment: app1
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
      - step:
          name: app2
          deployment: app2
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH

    master:
      - step:
          name: master
          deployment: master
          image: alpine:latest
          script:
            - pipe: atlassian/rsync-deploy:0.12.0
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $SSH_MASTER_PATH
                SSH_PORT: $SSH_PORT
                LOCAL_PATH: './**'
                DELETE_FLAG: 'false'
                EXTRA_ARGS: '--exclude=storage/logs/* --exclude=storage/framework/*'

    test:
      - step:
          name: test
          deployment: test
          image: alpine:latest
          script:
            - pipe: atlassian/rsync-deploy:0.12.0
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $SSH_TEST_PATH
                SSH_PORT: $SSH_PORT
                LOCAL_PATH: './**'
                DELETE_FLAG: 'false'
                EXTRA_ARGS: '--exclude=storage/logs/* --exclude=storage/framework/*'
    rbt-test:
      - step:
          name: api-rbt-test
          deployment: rbt-test
          image: alpine:latest
          script:
            - pipe: atlassian/rsync-deploy:0.12.0
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $SSH_RBT_TEST_PATH
                SSH_PORT: $SSH_PORT
                LOCAL_PATH: './**'
                DELETE_FLAG: 'false'
                EXTRA_ARGS: '--exclude=storage/logs/* --exclude=storage/framework/*'
    sandbox:
      - step:
          name: sandbox
          deployment: sandbox
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
    beta:
    - parallel:
      - step:
          name: beta
          deployment: beta
          image: composer:2.0
          script:
            - apk add --update openssh-client rsync sshpass
            - sshpass -p "$FTP_PASSWORD" rsync -v -e "ssh -p $FTP_PORT -o StrictHostKeyChecking=no" -arz --exclude='.git/' --exclude='tests/' * $FTP_USER@$FTP_SERVER:$FTP_PATH
definitions:
  services:
    mariadb:
      image: mariadb:10.6
      variables:
        MARIADB_DATABASE: 'test_inventory'
        MARIADB_ROOT_PASSWORD: 'swiftpod'
        MARIADB_PASSWORD: 'swiftpod'
        MARIADB_USER: 'swiftpod'
