<?php

/**
 * Debug script để kiểm tra tại sao không lấy được hard goods
 */

echo "=== DEBUG HARD GOODS COLLECTION ===\n\n";

// Mô phỏng dữ liệu để debug
echo "1. Ki<PERSON>m tra ProductType data:\n";
echo "   - ProductType::all() để xem cấu trúc dữ liệu\n";
echo "   - Kiểm tra field 'is_hard_goods' có giá trị gì\n\n";

echo "2. Kiểm tra ProductStyle data:\n";
echo "   - ProductStyle::select('id', 'name', 'sku', 'type')->get()\n";
echo "   - Kiểm tra field 'type' có giá trị gì (ID hay name)\n\n";

echo "3. Các vấn đề có thể xảy ra:\n\n";

echo "   a) ProductType mapping:\n";
echo "      - \$productTypes map theo name: [APPAREL => 0, HARD_GOODS => 1]\n";
echo "      - \$productTypesByID map theo ID: [1 => 0, 2 => 1]\n";
echo "      - Cần kiểm tra \$currentStyle['type'] là gì\n\n";

echo "   b) Logic kiểm tra:\n";
echo "      - Nếu \$currentStyle['type'] = 'HARD_GOODS' -> dùng \$productTypes\n";
echo "      - Nếu \$currentStyle['type'] = 2 -> dùng \$productTypesByID\n\n";

echo "   c) Giá trị is_hard_goods:\n";
echo "      - Cần kiểm tra giá trị trong DB: 0/1 hay true/false\n";
echo "      - Logic hiện tại check == 1\n\n";

echo "4. Debug steps:\n";
echo "   a) Chạy API và check log file\n";
echo "   b) Xem 'ProductHardGoods Debug' log entry\n";
echo "   c) Kiểm tra:\n";
echo "      - productHardGoods_count: số lượng hard goods tìm được\n";
echo "      - productTypes: mapping theo name\n";
echo "      - productTypesByID: mapping theo ID\n";
echo "      - validData_count: tổng số valid products\n\n";

echo "5. Possible fixes:\n\n";

echo "   a) Nếu is_hard_goods là boolean:\n";
echo "      \$isHardGoods = \$productTypes[strtoupper(\$currentStyle['type'])] === true;\n\n";

echo "   b) Nếu type field không đúng:\n";
echo "      - Kiểm tra relationship giữa ProductStyle và ProductType\n";
echo "      - Có thể cần join để lấy đúng type info\n\n";

echo "   c) Nếu data không có hard goods:\n";
echo "      - Kiểm tra CSV input có products với type = hard goods không\n";
echo "      - Kiểm tra ProductType table có records với is_hard_goods = 1 không\n\n";

echo "=== NEXT STEPS ===\n";
echo "1. Run the API with debug enabled\n";
echo "2. Check Laravel log file for 'ProductHardGoods Debug' entries\n";
echo "3. Analyze the debug data to identify the issue\n";
echo "4. Apply appropriate fix based on findings\n";

?>
