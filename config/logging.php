<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Deprecations Log Channel
    |--------------------------------------------------------------------------
    |
    | This option controls the log channel that should be used to log warnings
    | regarding deprecated PHP and library features. This allows you to get
    | your application ready for upcoming major versions of dependencies.
    |
    */

    'deprecations' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'debug' => [
            'driver' => 'single',
            'path' => storage_path('logs/debug.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
        ],

        'daily_swp' => [
            'driver' => 'daily',
            'tap' => [App\Logging\Formatter\AppLogFormatter::class],
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 7,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => env('LOG_LEVEL', 'critical'),
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        'barcode' => [
            'driver' => 'daily',
            'path' => storage_path('logs/barcode.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],

        'image' => [
            'driver' => 'daily',
            'path' => storage_path('logs/image.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],

        'queue_create_thumb' => [
            'driver' => 'daily',
            'path' => storage_path('logs/queue_create_thumb.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'queue_detect_color' => [
            'driver' => 'daily',
            'path' => storage_path('logs/queue_detect_color.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'queue_create_detect_print_method' => [
            'driver' => 'daily',
            'path' => storage_path('logs/queue_create_detect_print_method.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'queue_upload_s3' => [
            'driver' => 'daily',
            'path' => storage_path('logs/queue_upload_s3.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'queue_update_item_color_status' => [
            'driver' => 'daily',
            'path' => storage_path('logs/queue_update_item_color_status.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_create_thumb' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_create_thumb.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_detect_color' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_detect_color.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_upload_s3' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_upload_s3.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_update_item_color_status' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_update_item_color_status.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_update_item_color_status_success' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_update_item_color_status_success.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_detect_print_method' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_detect_print_method.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'send_email_pricing_setting' => [
            'driver' => 'daily',
            'path' => storage_path('logs/send_email_pricing_setting.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'job_sub_firebase' => [
            'driver' => 'daily',
            'path' => storage_path('logs/job_sub_firebase.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'visua_detect_image_error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/visua_detect_image_error.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'reject_image_error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/reject_image_error.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'visua_webhook_error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/visua_webhook_error.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'visua_receive_response_error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/visua_receive_response_error.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'tech_support' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tech_support.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'order_desk' => [
            'driver' => 'daily',
            'path' => storage_path('logs/order_desk/sync.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'barcode_assign' => [
            'driver' => 'daily',
            'path' => storage_path('logs/barcode_assign.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
        'report_missing_scan_wip' => [
            'driver' => 'daily',
            'path' => storage_path('logs/report_missing_scan_wip.log'),
        ],
        'stripe' => [
            'driver' => 'daily',
            'path' => storage_path('logs/stripe.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'log_max_files' => env('LOG_REQUEST_FILE_MAX_DAYS', 7)
        ],
    ],

];
