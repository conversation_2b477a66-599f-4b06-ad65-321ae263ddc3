<?php

/**
 * Test script để kiểm tra logic validation hai chiều
 * Mô phỏng các trường hợp test khác nhau
 */

// Mô phỏng dữ liệu CSV
$csvData = [
    ['STYLE' => 'STYLE_A', 'COLOR' => 'RED'],
    ['STYLE' => 'STYLE_A', 'COLOR' => 'BLUE'],
    // Thiếu GREEN từ product data
];

// Mô phỏng dữ liệu Product
$productData = [
    ['style' => 'STYLE_A', 'color' => 'RED'],
    ['style' => 'STYLE_A', 'color' => 'BLUE'], 
    ['style' => 'STYLE_A', 'color' => 'GREEN'], // Thiếu trong CSV
];

echo "=== TEST VALIDATION LOGIC ===\n\n";

echo "CSV Data:\n";
foreach ($csvData as $row) {
    echo "- Style: {$row['STYLE']}, Color: {$row['COLOR']}\n";
}

echo "\nProduct Data:\n";
foreach ($productData as $product) {
    echo "- Style: {$product['style']}, Color: {$product['color']}\n";
}

echo "\n=== EXPECTED VALIDATION RESULTS ===\n";
echo "1. CSV → Product Data: OK (tất cả style/color trong CSV đều có trong product data)\n";
echo "2. Product Data → CSV: FAIL (GREEN từ product data không có trong CSV)\n";
echo "3. Expected Error: 'Product data contains Style 'STYLE_A' and Color 'GREEN' but this combination is missing in CSV file.'\n";

echo "\n=== LOGIC EXPLANATION ===\n";
echo "Với logic mới:\n";
echo "- Kiểm tra CSV → Product: Mọi combination trong CSV phải tồn tại trong product data\n";
echo "- Kiểm tra Product → CSV: Mọi combination trong product data phải tồn tại trong CSV\n";
echo "- Chỉ khi cả hai chiều đều OK thì validation mới pass\n";

?>
