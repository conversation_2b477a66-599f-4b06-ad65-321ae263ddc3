# Validation Classification Summary

## Overview
API `validateVariantsCsvProductSpec` đã được cải thiện để phân loại hoàn chỉnh valid/invalid data từ cả CSV và Product Data.

## Response Structure

### Valid Data Classification
```json
{
    "valid": {...},           // Tất cả valid data (tương thích với code cũ)
    "validCsv": {...},        // Valid data từ CSV
    "validProduct": {...}     // Valid data từ Product Data
}
```

### Invalid Data Classification
```json
{
    "invalid": [...],         // Tất cả invalid data (tương thích với code cũ)
    "invalidCsv": [...],      // Invalid data từ CSV
    "invalidProduct": [...]   // Invalid data từ Product Data
}
```

### Count Classification
```json
{
    "countValid": 0,          // Tổng số valid (tương thích với code cũ)
    "countValidCsv": 0,       // Số valid từ CSV
    "countValidProduct": 0,   // Số valid từ Product Data
    
    "countInvalid": 0,        // Tổng số invalid (tương thích với code cũ)
    "countInvalidCsv": 0,     // Số invalid từ CSV
    "countInvalidProduct": 0  // Số invalid từ Product Data
}
```

## Data Flow

### 1. CSV Processing
- **Input**: CSV file với columns STYLE, COLOR, SIZE, etc.
- **Valid**: Rows có style/color khớp với Product Data
- **Invalid**: Rows có style/color không khớp, duplicate, hoặc thiếu data

### 2. Product Data Processing
- **Input**: JSON array với style/color combinations
- **Valid**: Combinations có trong CSV và valid
- **Invalid**: Combinations không có trong CSV

### 3. Two-way Validation
- **CSV → Product**: Mọi CSV row phải khớp với Product Data
- **Product → CSV**: Mọi Product combination phải có trong CSV

## Error Types

### CSV Errors (`invalidCsv`)
1. **Missing Data**: Thiếu STYLE hoặc COLOR
2. **Style Mismatch**: Style không khớp với Product Data
3. **Color Not Allowed**: Color không có trong danh sách cho phép
4. **Duplicate**: Duplicate style/color trong CSV

### Product Data Errors (`invalidProduct`)
1. **Missing in CSV**: Product combination không có trong CSV file

## Usage Examples

### Basic Validation Check
```php
if (!$response['status']) {
    echo "Validation failed!";
    echo "Total errors: " . $response['countInvalid'];
}
```

### Detailed Analysis
```php
// Phân tích Valid data
echo "CSV valid records: " . $response['countValidCsv'];
echo "Product valid records: " . $response['countValidProduct'];

// Phân tích Invalid data
echo "CSV errors: " . $response['countInvalidCsv'];
echo "Product errors: " . $response['countInvalidProduct'];
```

### Processing Specific Types
```php
// Xử lý CSV valid data
foreach ($response['validCsv'] as $key => $data) {
    // Process valid CSV data
}

// Xử lý Product valid data
foreach ($response['validProduct'] as $key => $data) {
    // Process valid Product data
}

// Xử lý CSV errors
foreach ($response['invalidCsv'] as $error) {
    // Handle CSV errors
}

// Xử lý Product errors
foreach ($response['invalidProduct'] as $error) {
    // Handle Product errors
}
```

## Benefits

### 1. Complete Classification
- Phân loại hoàn chỉnh valid/invalid cho cả hai nguồn dữ liệu
- Không bỏ sót bất kỳ trường hợp nào

### 2. Detailed Statistics
- Count riêng biệt cho từng loại data
- Dễ dàng tạo báo cáo và thống kê

### 3. Backward Compatibility
- Vẫn giữ `valid`, `invalid`, `countValid`, `countInvalid` cho code cũ
- Không breaking changes

### 4. Better UX
- Người dùng hiểu rõ data nào valid/invalid từ nguồn nào
- Message lỗi chi tiết và rõ ràng

### 5. Easier Debugging
- Dễ dàng xác định vấn đề từ CSV hay Product Data
- Hỗ trợ troubleshooting hiệu quả

## HTTP Response

### Success (All Valid)
- **Status**: 200 OK
- **Body**: Response với `status: true`

### Validation Errors
- **Status**: 422 Unprocessable Entity
- **Body**: Response với `status: false` và chi tiết lỗi phân loại

## Migration Guide

### For Existing Code
Code cũ sử dụng `invalid` và `countInvalid` vẫn hoạt động bình thường.

### For New Features
Sử dụng các fields mới để có thông tin chi tiết hơn:
- `validCsv`, `validProduct` cho valid data
- `invalidCsv`, `invalidProduct` cho invalid data
- `countValidCsv`, `countValidProduct`, `countInvalidCsv`, `countInvalidProduct` cho statistics
