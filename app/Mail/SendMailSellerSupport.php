<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMailSellerSupport extends Mailable
{
    use Queueable, SerializesModels;

    public $details;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Subject: Claim Order '. $this->details['saleOrder']['external_number'])
        ->with('details', $this->details)
        ->cc($this->details['cc_to'])
        ->replyTo($this->details['customer_email'])
        ->view('emails.customer_support');
}
}
