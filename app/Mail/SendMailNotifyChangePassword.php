<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class SendMailNotifyChangePassword extends Mailable
{
    use Queueable, SerializesModels;

    private $details;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $mailType = $this->details['type'];
        $now = Carbon::now();

        switch ($mailType) {
            case 'monthly':
                $monthYear = $now->format('F Y');
                $subject = "New Monthly Password Update - $monthYear";
                $view = 'emails.monthly_password';
                break;
            case 'weekly':
                $date = $now->format('m/d/Y');
                $subject = "New Weekly Password Update  $date";
                $view = 'emails.weekly_password';
                break;
        }

        $mail = $this->subject($subject)
            ->to($this->details['to'])
            ->view($view)
            ->with('details', $this->details);

        try {
            if (!empty($this->details['pdfs'])) {
                foreach ($this->details['pdfs'] as $pdfPath) {
                    if (Storage::disk('public')->exists($pdfPath)) {
                        $mail->attach(storage_path('app/public/' . $pdfPath));
                    }
                }
            }
        } catch (\Throwable $th) {
            throw $th;
        }

        return $mail;
    }
}
