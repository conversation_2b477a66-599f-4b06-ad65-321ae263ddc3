<?php

namespace App\Mail;

use App\Models\Setting;
use App\Models\WalletTopup;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TopupMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    protected $topup;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($topup)
    {
        $this->topup = $topup;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $invoicingEmail = Setting::where('name', Setting::INVOICING_EMAIL)->first()->value ?? '';
        $itSupportEmail = Setting::where('name', Setting::SUPPORT_EMAIL)->first()->value ?? '';
        $accountingEmail = Setting::where('name', Setting::ACCOUNTING_EMAIL)->first()->value ?? '';
        $billingEmail = $this->topup->store->billing_email;
        $ccEmails = [$invoicingEmail, $itSupportEmail];

        if (!empty($billingEmail)) {
            $arrayBillingEmail = explode(',', $billingEmail);
            $arrayBillingEmail = array_slice($arrayBillingEmail, 1);
            $ccEmails = array_merge($ccEmails, $arrayBillingEmail);
        }

        switch ($this->topup->status) {
            case WalletTopup::STATUS_APPROVED:
                $subject = "SwiftPOD - Add Fund Successful #{$this->topup->topup_number}";
                $view = 'emails.topup.success';
                break;
            case WalletTopup::STATUS_FAILED:
                $subject = "SwiftPOD - Add Fund Failed #{$this->topup->topup_number}";
                $view = 'emails.topup.failed';
                break;
            default:
                $subject = "SwiftPOD - Top Up Processing #{$this->topup->topup_number}";
                $view = 'emails.topup.pending';
                break;
        }

        return $this->from($accountingEmail, 'SwiftPOD Accounting')
                    ->subject($subject)
                    ->cc($ccEmails)
                    ->with('topup', $this->topup)
                    ->view($view);
    }
}
