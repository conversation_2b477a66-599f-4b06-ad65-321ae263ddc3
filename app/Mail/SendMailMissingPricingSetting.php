<?php

namespace App\Mail;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMailMissingPricingSetting extends Mailable
{
    use Queueable, SerializesModels;

    const EMAIL_SEND = 'alert_pricing_email_to';
    const EMAIL_CC = 'alert_pricing_email_cc';
    const EMAIL_BCC = 'alert_pricing_email_bcc';

    public $fileName = '';
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($fileName)
    {
        $this->fileName = $fileName;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.pricing_missing')->attach(storage_path('app/public/') . $this->fileName);;
    }

}
