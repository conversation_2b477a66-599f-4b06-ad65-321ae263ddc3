<?php

namespace App\Rules;

use App\Models\UniversalReportTag;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Arr;

class UniversalSqlRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(
        protected ?array $tagValueTypes
    ) {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $query)
    {
        $tagIndex = Arr::get(explode('.', $attribute), 1);
        $currentTagValueType = $this->tagValueTypes[$tagIndex];

        if (
            $currentTagValueType === UniversalReportTag::VALUE_TYPE_SQL
            && preg_match('/\b(CREATE\s+?DATABASE|DROP\s+?DATABASE|ALTER\s+?DATABASE|ALTER\s+?SCHEMA|CREATE\s+?TABLE|DROP\s+?TABLE|ALTER\s+?TABLE|TRUNCATE\s+?TABLE|RENAME\s+?TABLE|DROP\s+?INDEX|UPDATE\s+[0-9A-Z\$_\\\`\'\"]+\s+SET|INSERT\s+?INTO|DELETE\s+?FROM)\b/i', $query)
        ) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Invalid SQL Query';
    }
}
