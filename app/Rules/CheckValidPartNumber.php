<?php

namespace App\Rules;

use App\Models\PartNumber;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckValidPartNumber implements Rule
{

    protected $request;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

        $partNumber = PartNumber::where('country', $this->request->country)
            ->where('fabric_content', $this->request->fabric_content)
            ->whereHas('product', function($q) {
                $q->where('sku', $this->request->sku);
            })
            ->first();

        return !$partNumber;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The SKU, Fabric content and country of origin already exist in another part number.';
    }
}
