<?php

namespace App\Rules;

use App\Models\UniversalReportTag;
use Carbon\Carbon;
use DateTime;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class UniversalSettingExportRule implements Rule
{
    protected string $errMessage = 'The tags value is invalid.';

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(
        protected Collection $tagUsed,
        protected ?array $tagInputIds
    ) {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $values)
    {
        $tagIndex = Arr::get(explode('.', $attribute), 1);
        $currentTag = $this->tagUsed->where('id', $this->tagInputIds[$tagIndex])->first();

        if (empty($currentTag)) {
            return true;
        }

        switch ($currentTag->type) {
            case UniversalReportTag::TYPE_MULTI_SELECT:
                if (count($values) < 1) {
                    $this->errMessage = "The $attribute must be an array with at least one value.";

                    return false;
                }
                break;

            case UniversalReportTag::TYPE_SINGLE_DATE:
                if (count($values) > 1) {
                    $this->errMessage = "The $attribute must be a single date.";

                    return false;
                }

                if (!DateTime::createFromFormat('Y-m-d H:i:s', $values[0])) {
                    $this->errMessage = "The $attribute must be a valid date in 'Y-m-d H:i:s' format.";

                    return false;
                }

                break;

            case UniversalReportTag::TYPE_RANGE_DATE:
                if (count($values) > 2) {
                    $this->errMessage = "The $attribute invalid date range";

                    return false;
                }

                if (count($values) === 1 && !in_array($values[0], UniversalReportTag::allowedRange())) {
                    $this->errMessage = "The $attribute must be a valid date range.";

                    return false;
                }

                if (count($values) === 2) {
                    [$startDate, $endDate] = $values;
                    if (!DateTime::createFromFormat('Y-m-d H:i:s', $startDate) || !DateTime::createFromFormat('Y-m-d H:i:s', $endDate)) {
                        $this->errMessage = "The $attribute must contain valid dates in 'Y-m-d H:i:s' format.";

                        return false;
                    }

                    $start = Carbon::createFromFormat('Y-m-d H:i:s', $startDate);
                    $end = Carbon::createFromFormat('Y-m-d H:i:s', $endDate);

                    if ($start->gt($end) || $start->diffInDays($end) > 30) {
                        $this->errMessage = "The $attribute date range must not exceed 30 days.";

                        return false;
                    }
                }
                break;

            case UniversalReportTag::TYPE_SINGLE_SELECT:
            case UniversalReportTag::TYPE_INPUT:
                if (count($values) > 1) {
                    $this->errMessage = "The $attribute must be a single value.";

                    return false;
                }
                break;

            default:
                break;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->errMessage;
    }
}
