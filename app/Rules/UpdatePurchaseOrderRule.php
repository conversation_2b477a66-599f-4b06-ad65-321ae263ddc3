<?php

namespace App\Rules;

use App\Models\PurchaseOrder;
use Illuminate\Contracts\Validation\Rule;

class UpdatePurchaseOrderRule implements Rule
{
    private string $orderId;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->orderId = $id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $data = PurchaseOrder::query()
            ->where('id', $this->orderId)
            ->whereIn('order_status', [
                PurchaseOrder::COMPLETED_STATUS,
            ])
            ->count();

        return !$data;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return "Can't update purchase order because order status is completed";
    }
}
