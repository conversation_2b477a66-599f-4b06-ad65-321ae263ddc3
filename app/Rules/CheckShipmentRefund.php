<?php

namespace App\Rules;

use App\Models\Shipment;
use Illuminate\Contracts\Validation\Rule;

class CheckShipmentRefund implements Rule
{
    protected $shipmentId;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $shipment = Shipment::query()
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->where('is_deleted', false)
            ->whereNull('refund_status')
            ->whereIn('tracking_status', [Shipment::PRE_TRANSIT, Shipment::UNKNOWN])
            ->where('id', $this->shipmentId)
            ->exists();
        if ($shipment) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return "Shipment #$this->shipmentId ";
    }
}
