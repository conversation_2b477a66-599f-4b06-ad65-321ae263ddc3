<?php

namespace App\Models\Tag;

use Illuminate\Support\Facades\DB;

class TagModel
{
    const TABLE_NAME = 'tag';

    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insert($rawData);
    }

    public function getAllTagByAccountId($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('account_id', $accountId)
            ->where('source', 'sale_order')
            ->pluck('id', 'external_id');
    }

    public function getItemTexas()
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', 'TEXAS')
            ->first();
    }

    public function getItemReadyTool($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', 'WIP READY TOOL')
            ->where('account_id', $accountId)
            ->first();
    }

    public function getItemWipPrintedTool($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', 'WIP PRINTED TOOL')
            ->where('account_id', $accountId)
            ->first();
    }

    public function findTagByNameAndAccount($accountId, $name)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', $name)
            ->where('account_id', $accountId)
            ->first()
        ;
    }

    public function getItemFrontPrint($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', '80. Front Print')
            ->where('account_id', $accountId)
            ->first();
    }

    public function getItemBackPrint($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', '81. Back Print')
            ->where('account_id', $accountId)
            ->first();
    }

    public function getItemFbPrint($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', '82. FB Print')
            ->where('account_id', $accountId)
            ->first();
    }

    public function getItemMixPrint($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', '83. Mix Prints')
            ->where('account_id', $accountId)
            ->first();
    }

    public function getItemBlackOnly($accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', 'like', '%Black Ink Only%')
            ->where('account_id', $accountId)
            ->first();
    }

    public function findTagByExternalId($externalId, $accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('external_id', $externalId)
            ->where('account_id', $accountId)
            ->where('source', 'sale_order')
            ->count();
    }

    public function findTagXqc($listId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->where('name', 'Sample XQC')
            ->count();
    }

    public function findTagDoNotPrintWip($listId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->where('name', 'DO NOT PRINT WIP')
            ->count();
    }

    public function findTagEps($listId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->where('name', 'UPGRADE TO EXPRESS SHIPPING')
            ->count();
    }
}

