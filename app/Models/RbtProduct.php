<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtProduct extends Model
{
    use HasFactory;

    protected $table = 'rbt_product';

    protected $fillable = ['product_id', 'is_active', 'created_at', 'updated_at', 'created_by'];

    const RBT_DETECT_ORDER = 'rbt-detect-order';

    protected $casts = [
        'is_active' => 'boolean',
    ];

    const RBT_EMPLOYEE_ID = 2005;

    const RBT_EMPLOYEE_CODE = 13579;

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
