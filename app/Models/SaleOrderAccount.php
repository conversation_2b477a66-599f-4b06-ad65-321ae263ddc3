<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class SaleOrderAccount extends Model
{
    use HasFactory;

    protected $table = 'sale_order_account';

    const ACTIVE = 1;

    const INACTIVE = 0;

    const SOURCE_ORDER_DESK = 'orderdesk';

    protected $fillable = [
        'name',
        'source',
        'api_key',
        'api_secret',
        'is_resize',
        'note',
        'folder',
        'partner_id',
        'sync_shipstation',
        'sync_orderdesk',
        'is_editing',
        'default_store_id',
    ];

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['source'])) {
            $query->where("$this->table.source", $request['source']);
        }

        return $query->where("$this->table.is_active", self::ACTIVE);
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'default_store_id');
    }
}
