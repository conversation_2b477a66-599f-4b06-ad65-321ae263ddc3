<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class InternalRequest extends Model
{
    use HasFactory;

    protected $table = 'internal_request';

    protected $fillable = [
        'warehouse_id',
        'product_id',
        'employee_create_id',
        'employee_receive_id',
        'received_at',
        'employee_fulfill_id',
        'fulfilled_at',
        'employee_confirm_id',
        'confirmed_at',
        'employee_reject_id',
        'rejected_at',
        'box_id',
        'priority',
        'status',
        'employee_complete_id',
        'employee_reject_id',
        'is_rbt',
        'dark_pod_quantity',
        'new_box_id',
        'count_sticker_id',
        'station_id'
    ];

    const PICKING_UP_TIMEOUT = 30; //phút

    const NEW_STATUS = 'new';

    const CHECKED_STATUS = 'checked';

    const UNCHECK_STATUS = 'uncheck';

    const PICKING_UP_STATUS = 'picking_up';

    const REJECTED_STATUS = 'rejected';

    const NOT_DELETED = 0;

    const DELETED = 1;

    const IS_RBT = 1;

    const HIGH_PRIORITY = 1;

    const LOW_PRIORITY = 0;

    const TIKTOK_PRIORITY = 2;

    const CREATE_TYPE = 'create';

    const RECEIVE_TYPE = 'receive';

    const FULFILL_TYPE = 'fulfill';

    const CONFIRM_TYPE = 'confirm';

    const DELETE_TYPE = 'delete';

    const EVENT_CREATE = 'CREATE_INTERNAL_REQUEST';

    const EVENT_RECEIVE = 'RECEIVE_INTERNAL_REQUEST';

    const EVENT_FULFILL = 'FULFILL_INTERNAL_REQUEST';

    const EVENT_REJECT = 'REJECT_INTERNAL_REQUEST';

    const EVENT_DELETE = 'DELETE_INTERNAL_REQUEST';

    const EVENT_RELEASE = 'RELEASE_INTERNAL_REQUEST';

    const EVENT_TIMEOUT = 'TIMEOUT_INTERNAL_REQUEST';

    const EVENT_CONFIRM = 'CONFIRM_INTERNAL_REQUEST';

    const EVENT_UPDATE_PRIORITY = 'UPDATE_PRIORITY_INTERNAL_REQUEST';

    const EVENT_REPORT_MISSING_BOX = 'REPORT_MISSING_BOX';

    const EVENTS = [
        self::EVENT_CREATE,
        self::EVENT_RECEIVE,
        self::EVENT_FULFILL,
        self::EVENT_REJECT,
        self::EVENT_DELETE,
        self::EVENT_RELEASE,
        self::EVENT_TIMEOUT,
        self::EVENT_CONFIRM,
        self::EVENT_UPDATE_PRIORITY,
        self::EVENT_REPORT_MISSING_BOX,
    ];

    const JOB_INTERNAL_REQUEST_EVENT = 'internal-request-event';

    const JOB_INTERNAL_REQUEST_TIMEOUT = 'internal-request-timeout';

    const TYPE_LOAD_MORE = 'load-more';

    const PULLING_DEPARTMENT = 'Pulling';

    const INVENTORY_DEPARTMENT = 'Inventory';

    const OUT_OF_STOCK = 'OOS';

    const INCOMING = 'Incoming';

    public function scopeSearch($query, $input)
    {
        if (!empty($input['key_word'])) {
            $query->whereHas('product', function ($q) use ($input) {
                $q->where('style', 'like', "%{$input['key_word']}%");
                $q->orWhere('color', 'like', "%{$input['key_word']}%");
                $q->orWhere('size', 'like', "%{$input['key_word']}%");
                $q->orWhere('sku', 'like', "%{$input['key_word']}%");
            });
        }

        if (!empty($input['box_id'])) {
            $query->whereHas('box', function ($q) use ($input) {
                $q->where('barcode', 'like', "%{$input['box_id']}%");
            });
        }

        if (!empty($input['employee_id'])) {
            $query->where(function ($q) use ($input) {
                $q->where('employee_create_id', $input['employee_id']);
                $q->orWhere('employee_receive_id', $input['employee_id']);
                $q->orWhere('employee_fulfill_id', $input['employee_id']);
                $q->orWhere('employee_confirm_id', $input['employee_id']);
                $q->orWhere('employee_reject_id', $input['employee_id']);
            });
        }

        if (!empty($input['start_date'])) {
            $query->whereDate('created_at', '>=', $input['start_date']);
        }

        if (!empty($input['end_date'])) {
            $query->whereDate('created_at', '<=', $input['end_date']);
        }

        if (!empty($input['status'])) {
            $query->whereIn('status', $input['status']);
        }

        if (!empty($input['warehouse_id'])) {
            $query->where('warehouse_id', $input['warehouse_id']);
        }

        if (!empty($input['location'])) {
            $lane = substr(strtoupper($input['location']), 0, 2);
            $query->whereHas('location', function ($q) use ($lane, $input) {
                if ($lane[0] != $lane[1] || $lane[0] < 'A' || $lane[0] > 'Z') {
                    $q->where(function ($qSub) {
                        $qSub->whereRaw('LEFT(`location`.`barcode`, 1) != MID(`location`.`barcode`, 2, 1)');
                        $qSub->orWhereRaw("LEFT(`location`.`barcode`, 1) < 'A'");
                        $qSub->orWhereRaw("LEFT(`location`.`barcode`, 1) > 'Z'");
                    });
                } else {
                    $q->where('location.barcode', 'like', $lane . '%');
                }
                $q->where('location.warehouse_id', $input['warehouse_id']);
                $q->where('location.is_deleted', 0);
            });
        }

        return $query;
    }

    public static function getTopicName($warehouseId)
    {
        return 'internal-request-' . $warehouseId;
    }

    public function histories()
    {
        return $this->hasMany(InternalRequestHistory::class, 'internal_request_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function box()
    {
        return $this->belongsTo(Box::class, 'box_id');
    }

    public function employeeCreate()
    {
        return $this->belongsTo(Employee::class, 'employee_create_id');
    }

    public function employeeReceive()
    {
        return $this->belongsTo(Employee::class, 'employee_receive_id');
    }

    public function employeeConfirm()
    {
        return $this->belongsTo(Employee::class, 'employee_confirm_id');
    }

    public function employeeFulfill()
    {
        return $this->belongsTo(Employee::class, 'employee_fulfill_id');
    }

    public function employeeComplete()
    {
        return $this->belongsTo(Employee::class, 'employee_complete_id');
    }

    public function employeeReject()
    {
        return $this->belongsTo(Employee::class, 'employee_reject_id');
    }

    public function location(): BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'box', 'product_id', 'location_id', 'product_id', 'id')
            ->wherePivot('quantity', '>', 0)
            ->wherePivot('is_deleted', 0)
            ->where('location.type', Location::RACK)
            ->where('location.is_deleted', 0)
            ->withPivot('quantity');
    }

    public function boxMoving()
    {
        return $this->hasMany(BoxMoving::class, 'box_id', 'box_id')
            ->orderByDesc('id');
    }

    public function countSticker()
    {
        return $this->belongsTo(RbtCountSticker::class, 'count_sticker_id', 'id');
    }

    public function newBox()
    {
        return $this->belongsTo(Box::class, 'new_box_id', 'id');
    }
}
