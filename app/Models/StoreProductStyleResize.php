<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class StoreProductStyleResize extends Authenticatable
{
    use HasFactory;

    protected $table = 'store_product_style_resize';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'store_id',
        'product_style_sku',
        'user_id',
    ];

    public $timestamps = true;

    const ACTIVED = 1;
    const INACTIVED = 0;
    
    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (is_numeric($request['is_active'])) {
            $query->where('is_active', $request['is_active']);
        }
        return $query;
    }
    
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    
}
