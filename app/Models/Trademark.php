<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Trademark extends Model
{
    use HasFactory;

    protected $table = 'trademarks';

    protected $fillable = [
        'name',
        'type',
        'created_by',
        'ownership_id',
        'is_active'
    ];



    //["logo", "brand", "singer", "celebrity", "character"]
    const LOGO_TYPE = 'logo';
    const BRAND_TYPE = 'brand';
    const SINGER_TYPE = 'singer';
    const CELEBRITY_TYPE = 'celebrity';
    const CHARACTER_TYPE = 'character';
    const TYPE_LIST = [
        self::BRAND_TYPE,
        self::CHARACTER_TYPE,
        self::LOGO_TYPE,
        self::SINGER_TYPE,
        self::CELEBRITY_TYPE,
    ];

    const IS_ACTIVE = 1;

    const INACTIVE = 0;

    //    protected static function boot()
    //    {
    //        parent::boot();
    //
    ////        static::addGlobalScope('active', function ($query) {
    ////            $query->where('is_deleted', 0);
    ////        });
    //    }

    public function ownership()
    {
        return $this->belongsTo(Ownership::class, 'ownership_id', 'id');
    }

    public function histories()
    {
        return $this->hasMany(TrademarkHistory::class, 'trademark_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
