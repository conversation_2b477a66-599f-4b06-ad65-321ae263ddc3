<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SupplyPurchaseOrder extends Model
{
    use HasFactory;

    protected $table = 'supply_purchase_order';

    const NEW_ORDER_STATUS = 'new_order';
    const NOT_SHIPPED_STATUS = 'not_shipped';
    const SHIPPED_STATUS = 'shipped';
    const DELIVERED_STATUS = 'delivered';
    const COMPLETED_STATUS = 'completed';
    const PARTIAL_RECEIVED_STATUS = 'partial_received';
    const CANCELLED_STATUS = 'cancelled';
    const INCOMPLETED_STATUS = 'incompleted';

    const ALLOWED_STATUS_CAN_UPDATE = [
        SupplyPurchaseOrder::SHIPPED_STATUS,
        SupplyPurchaseOrder::DELIVERED_STATUS,
        SupplyPurchaseOrder::PARTIAL_RECEIVED_STATUS,
        SupplyPurchaseOrder::CANCELLED_STATUS,
        SupplyPurchaseOrder::COMPLETED_STATUS,
        SupplyPurchaseOrder::INCOMPLETED_STATUS
    ];
    protected $fillable = [
        'po_number',
        'order_date',
        'order_number',
        'invoice_number',
        'vendor_id',
        'delivery_date',
        'note',
        'payment_terms',
        'warehouse_id',
        'status_updated_at',
        'user_id',
        'order_status',
        'shipping_status',
        'billing_status',
        'tracking_number',
        'tracking_carrier',
        'tag',
        'fee',
        'sub_total',
        'total',
    ];

    protected $casts = [
        'tag' => 'string',
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('warehouse', function ($query) {
            $request = request();
            if ($request->has('warehouse_id')) {
                $query->where('warehouse_id', $request->warehouse_id);
            } else {
                $query->where('warehouse_id', config('jwt.warehouse_id'));
            }
        });
    }

    public function setTagAttribute($value): void
    {
        $this->attributes['tag'] = $value ? implode(',', $value) : null;
    }

    public function items(): hasMany
    {
        return $this->hasMany(SupplyPurchaseOrderItem::class, 'po_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    public function history(): hasMany
    {
        return $this->hasMany(SupplyPurchaseOrderHistory::class, 'po_id');
    }

    public function scopeSearch($query, $request)
    {
        $tags = !empty($request['tag']) ? $request['tag'] : [];
        $data = !empty($request['order_date']) ? $request['order_date'] : [];
        $query->when(!empty($request['keyword']), function ($q) use ($request) {
            $q->where(function ($q) use ($request) {
                $q->where('po_number', 'like', '%' . $request['keyword'] . '%')
                    ->orWhere('invoice_number', 'like', '%' . $request['keyword'] . '%');
            });
        })
            ->when(!empty($request['po_number']), function ($q) use ($request) {
                $q->where('po_number', 'like', '%' . $request['po_number'] . '%');
            })
            ->when(!empty($request['invoice_number']), function ($q) use ($request) {
                $q->where('invoice_number', 'like', '%' . $request['invoice_number'] . '%');
            })
            ->when(!empty($request['order_number']), function ($q) use ($request) {
                $q->where('order_number', 'like', '%' . $request['order_number'] . '%');
            })
            ->when(!empty($request['tracking_number']), function ($q) use ($request) {
                $q->where('tracking_number', 'like', '%' . $request['tracking_number'] . '%');
            })
            ->when(!empty($request['vendor_id']), function ($q) use ($request) {
                $q->where('vendor_id', $request['vendor_id']);
            })
            ->when(!empty($request['status']), function ($q) use ($request) {
                $q->where('order_status', $request['status']);
            })
            ->when(!empty($tags), function ($q) use ($tags) {
                foreach ($tags as $tag) {
                    $q->whereRaw('FIND_IN_SET(?,tag)', $tag);
                }
            })
            ->when(!empty($data[0]), function ($q) use ($data) {
                $q->whereDate('order_date', '>=', $data[0]);
            })
            ->when(!empty($data[1]), function ($q) use ($data) {
                $q->whereDate('order_date', '<=', $data[1]);
            })
            ->when(!empty($request['sku']), function ($q) use ($request) {
                $q->whereHas('items', function ($q) use ($request) {
                    $q->whereHas('supply', function ($q) use ($request) {
                        $q->where('sku', 'like', '%' . $request['sku'] . '%');
                    });
                });
            })
            ->when(isset($request['incoming']) && $request['incoming'] === 'true', function ($q) {
                $q->whereNotIn('order_status', [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS]);
            });

        return $query;
    }
}
