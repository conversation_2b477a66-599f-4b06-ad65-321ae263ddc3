<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtSkuMovementImportHistory extends Model
{
    use HasFactory;

    protected $table = 'rbt_sku_movement_import_history';

    protected $fillable = [
        'status',
        'upload_by',
        'file_name',
        'link_url',
        'link_url_error',
        'created_at',
        'updated_at'
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'upload_by');
    }
}
