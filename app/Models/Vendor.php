<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'vendor';

    protected $fillable = [
        'name',
        'display_name',
        'email',
        'phone',
        'work_phone',
        'cell_phone',
        'skype',
        'department',
        'note',
        'is_deleted',
        'payment_term_id',
        'created_at',
        'updated_at',
        'type',
        'qb_ref',
    ];
}
