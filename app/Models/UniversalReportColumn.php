<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UniversalReportColumn extends Model
{
    use HasFactory;

    protected $table = 'universal_report_columns';

    protected $fillable = [
        'report_template_id',
        'virtual_column',
        'original',
        'table',
        'column',
        'alias',
    ];

    protected $casts = [
        'report_template_id' => 'integer',
        'virtual_column' => 'boolean',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
    ];
}
