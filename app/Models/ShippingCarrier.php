<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;

class ShippingCarrier extends Model
{
    use HasFactory;

    // public $timestamps = false;

    protected $table = 'shipping_carrier';

    protected $fillable = [
        'name',
        'code',
        'tracking_url',
        'is_deleted',
        'created_at',
        'updated_at',
        'is_manifest'
    ];

    const DHL_ECOMMERCE_CODE = 'DhlEcs';

    const UPS_MI_CODE = 'UPSMailInnovations';

    const USPS_ECOMMERCE_CODE = 'USPS';

    const FEDEX_CODE = 'FedEx';

    const CARRIER_NEED_SIZE_CUBIC = ['USPS'];

    const UPSDAP_CODE = 'UPSDAP';

    const ONTRACV3_CODE = 'OnTracV3';

    const PASSPORT_CODE = 'PassportGlobal';

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        }

        if (!empty($request['code'])) {
            $query->where("$this->table.code", 'LIKE', '%' . $request['code'] . '%');
        }

        if (!empty($request['all-code'])) {
            $query->whereNotNull("$this->table.code")->groupBy("$this->table.code");
        }

        return $query;
    }

    public function shippingCarrierPredefinedPackage(): HasMany
    {
        return $this->hasMany(ShippingCarrierPackage::class, 'carrier_id');
    }

    public function shippingIntegrationAccounts(): HasMany
    {
        return $this->hasMany(ShippingIntegrationAccount::class, 'carrier_id');
    }

    public function shippingCarrierEasyposts(): HasMany
    {
        return $this->hasMany(ShippingCarrierEasypost::class, 'carrier_id');
    }

    public function shippingMethods(): HasMany
    {
        return $this->hasMany(ShippingMethod::class, 'carrier_id');
    }
}
