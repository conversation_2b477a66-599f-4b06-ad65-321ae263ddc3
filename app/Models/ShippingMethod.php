<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShippingMethod extends Model
{
    use HasFactory;

    protected $table = 'shipping_method';

    protected $fillable = [
        'api_shipping_method',
        'shipping_carrier_service_id',
        'store_id',
        'name',
        'integrate_carrier_code',
        'integrate_service_code',
        'created_at',
        'updated_at',
        'carrier_id',
    ];

    const SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD = 'RB_UPSM01_ExpeditedMailInnovations';

    const SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS = 'RB_USPS01_Priority';

    const ECONOMY = 'economy';

    const EXPRESS = 'express';

    const SHIPPING_METHOD_FIRST_CLASS = 'letter_first_class';

    const SHIPPING_METHOD_REDBUBLE_DDP_PASSPORT = 'RB_passport01_epacketDdp';

    const SHIPPING_METHOD_STANDARD = 'standard';

    const SHIPPING_METHOD_PRINTIFY_USPS_GROUND = 'printify_usps01_GroundAdvantage';

    public function shippingCarrierService(): BelongsTo
    {
        return $this->belongsTo(ShippingCarrierService::class, 'shipping_carrier_service_id', 'id');
    }

    public function shippingCarrierEasypost(): HasMany
    {
        return $this->hasMany(ShippingCarrierEasypost::class, 'name', 'name');
    }

    public function shippingCarrier(): BelongsTo
    {
        return $this->belongsTo(ShippingCarrier::class, 'carrier_id', 'id');
    }
}
