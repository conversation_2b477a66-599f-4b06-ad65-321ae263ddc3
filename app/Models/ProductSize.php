<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSize extends Model
{
    use HasFactory;

    protected $table = 'product_size';

    const SIZE_11OZ_NAME = '110';

    const SIZE_15OZ_NAME = '150';

    const SIZE_20OZ_NAME = '20 oz';

    const SIZE_11OZ_SKU = '11O';

    const SIZE_15OZ_SKU = '15O';

    protected $fillable = [
        'name',
        'sku'
    ];

    const DIMENSION_MUG_11O_15O = [6.75, 4.75, 4.75];

    const DIMENSION_MUG_11O = [7, 4.75, 5];

    const DIMENSION_MUG_15O = [6.75, 4.75, 4.75];
}
