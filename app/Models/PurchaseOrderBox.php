<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrderBox extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'purchase_order_box';

    const LOST_STATUS = 'lost';

    protected $fillable = [
        'po_id',
        'box_number',
        'invoice_number',
        'tracking_number',
        'status',
        'created_at',
        'updated_at',
        'delivered_at',
        'coo_id'
    ];

    public function items()
    {
        return $this->hasMany(PurchaseOrderBoxItem::class, 'po_box_id', 'id');
    }

    public function purchase_order()
    {
        return $this->belongsTo(PurchaseOrder::class, 'po_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'coo_id', 'id');
    }
}
