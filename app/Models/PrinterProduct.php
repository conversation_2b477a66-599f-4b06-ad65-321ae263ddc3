<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PrinterProduct extends Model
{
    use HasFactory;

    protected $table = 'printer_product';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'style_sku',
        'color_sku',
        'printer_id',
    ];

    public function printer()
    {
        return $this->belongsTo(Printer::class);
    }
}
