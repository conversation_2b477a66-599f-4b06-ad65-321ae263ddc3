<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockTransferBoxLog extends Model
{
    use HasFactory;

    protected $table = 'stock_transfer_box_log';

    protected $fillable = [
        'stock_transfer_id',
        'stock_transfer_item_id',
        'box_id',
        'box_number',
        'product_id',
        'quantity',
        'coo_id',
        'employee_id',
    ];
    public $timestamps = true;

    public function coo(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'coo_id');
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
