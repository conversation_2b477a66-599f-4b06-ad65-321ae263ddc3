<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderItemProductImage extends Model
{
    use HasFactory;

    protected $table = 'sale_order_item_product_image';

    protected $fillable = [
        'order_id',
        'order_item_id',
        'image_url',
        'user_id'
    ];

    const FILE_PATH = 'itemProduct';

    const SEND_MAIL_CUSTOMER = 'send-mail-upload-image';
}
