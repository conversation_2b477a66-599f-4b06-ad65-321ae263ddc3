<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VisuaDetectImage extends Model
{
    use HasFactory;

    protected $table = 'visua_detect_image';

    protected $fillable = [
        'visua_session_id',
        'json',
        'gemini_json',
        'image_id',
        'order_id',
        'is_received_response',
        'is_ip_violation',
        'user_detect_id',
    ];

    protected $casts = [
        'gemini_json' => AsArrayObject::class,
    ];

    const DETECT_DESIGN_JOB = 'visua-detect-image';
    const RECEIVE_RESPONSE_DETECT_IMAGE_JOB = 'receive-response-detect-image';
    const DETECT_IMAGE_WEBHOOK_JOB = 'detect-image-webhook';
    const CONFIDENCE_ON_HOLD = 0.9;
    const IS_TRUE = 1;
    const IS_FALSE = 0;

    public function items()
    {
        return $this->hasMany(VisuaDetectImageItem::class, 'detect_design_id', 'id');
    }

    public function image()
    {
        return $this->belongsTo(SaleOrderItemImage::class, 'image_id', 'id');
    }

}
