<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductPrintSide extends Model
{
    use HasFactory;

    protected $table = 'product_print_side';

    protected $fillable = [
        'name',
        'description',
        'code',
        'code_name',
        'code_wip',
        'order'
    ];

    const DTF_NECK_SIDE_NAME = [
        'Inner Neck Label',
        'Outer Neck Label',
    ];

    const CODE_WIP_BLANK = 'Z';

    const CODE_WIP_FRONT = 'F';

    const CODE_WIP_DIE_CUT = 'Y';

    const CODE_WIP_BLEED_MUG = 'U';

    const CODE_WIP_MAX_PRINT = 'X';

    const CODE_WIP_SQUARE = '!';

    const PRINT_AREA_FRONT = 'Front';

    const PRINT_AREA_DIE_CUT = 'Die Cut';

    const PRINT_AREA_SQUARE = 'Square';

    public function productPrintArea(): HasMany
    {
        return $this->hasMany(ProductPrintArea::class, 'name', 'name');
    }

    public function firstArea()
    {
        return $this->hasOne(ProductPrintArea::class, 'name', 'name');
    }

    public function images()
    {
        return $this->hasMany(SaleOrderItemImage::class, 'print_side', 'code');
    }

    public static function findByCodeName($code_name)
    {
        return self::where('code_name', $code_name)->first();
    }

    public static function findByCode($code)
    {
        return self::where('code', $code)->first();
    }

    public static function findByName($name)
    {
        return self::where('name', $name)->first();
    }

    public static function getByArrayCodeWip($arrayCodeWip, $productStyleId)
    {
        return self::whereIn('code_wip', $arrayCodeWip)
            ->with(['firstArea' => function ($q) use ($productStyleId) {
                $q->where('product_style_id', $productStyleId);
            }])
            ->whereHas('firstArea', function ($query) use ($productStyleId) {
                $query->where('product_style_id', $productStyleId);
            })
            ->orderBy('order')
            ->get()
            ->pluck('firstArea.print_method', 'code_wip')
            ->toArray();
    }

    public static function listByPaginate($limit)
    {
        return self::orderBy('order')->paginate($limit);
    }

    public static function findNeckInnerByCode($code)
    {
        return self::where('code', $code)->whereIn('code_name', ['inner_neck_label', 'outer_neck_label'])->first();
    }

    public static function sleevePrintSideName()
    {
        return self::where('name', 'like', '%Sleeve%')->get()->pluck('name')->toArray();
    }
}
