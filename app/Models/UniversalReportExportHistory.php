<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class UniversalReportExportHistory extends Model
{
    use HasFactory;

    public const STATUS_PENDING = 'pending';

    public const STATUS_COMPLETED = 'completed';

    public const STATUS_FAILED = 'failed';

    protected $table = 'universal_report_export_histories';

    protected $fillable = [
        'user_id',
        'report_template_id',
        'name',
        'status',
        'file_path',
        'message',
        'start_time',
        'end_time',
        'report_setting_data',
    ];

    protected $casts = [
        'status' => 'string',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'report_setting_data' => 'array',
    ];

    protected $appends = [
        'file_url',
    ];

    protected $hidden = [
        'report_setting_data',
    ];

    public function template()
    {
        return $this->belongsTo(UniversalReportTemplate::class, 'report_template_id');
    }

    public function getFileUrlAttribute()
    {
        if (empty($this->file_path)) {
            return null;
        }

        return Storage::disk('s3')->temporaryUrl($this->file_path, now()->addMinutes(30));
    }
}
