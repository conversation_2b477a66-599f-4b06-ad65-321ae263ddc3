<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class UniversalReportExportHistory extends Model
{
    use HasFactory;

    public const STATUS_PENDING = 'pending';

    public const STATUS_COMPLETED = 'completed';

    public const STATUS_FAILED = 'failed';

    public const STATUS_EXPIRED = 'expired';

    protected $table = 'universal_report_export_histories';

    protected $fillable = [
        'user_id',
        'report_template_id',
        'name',
        'status',
        'file_path',
        'file_size',
        'show_in_popup',
        'message',
        'start_time',
        'end_time',
        'last_download_at',
        'report_setting_data',
    ];

    protected $casts = [
        'status' => 'string',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'last_download_at' => 'datetime',
        'report_setting_data' => 'array',
        'file_size' => 'integer',
        'show_in_popup' => 'boolean',
    ];

    protected $hidden = [
        'report_setting_data',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('isOwner', function (Builder $builder) {
            $builder->where('user_id', auth()->id());
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function template()
    {
        return $this->belongsTo(UniversalReportTemplate::class, 'report_template_id');
    }

    public function getFileUrlAttribute()
    {
        if (empty($this->file_path)) {
            return null;
        }

        return Storage::disk('s3')->temporaryUrl($this->file_path, now()->addMinutes(30));
    }
}
