<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SurchargeService extends Model
{
    use HasFactory;

    protected $table = 'surcharge_service';

    protected $fillable = [
        'name',
        'api_value',
        'per',
        'description',
        'product_type',
    ];

    const PER_ORDER = 'order';

    const PER_ITEM = 'item';

    const PER_PRODUCT_TYPE = 'product_type';

    const PER_PRINT_AREA = 'print_area';

    const TYPE_HANDLING = 'Handling';

    const TYPE_TIKTOK_ORDER_SERVICE = 'Tiktok Order Service';

    const TYPE_LABEL_PRINTING_FEE = 'Label Printing Fee';

    const TYPE_PLASTIC_BAG = 'Plastic Bag';

    const TYPE_MUG_PACKAGING = 'Mug Packaging';

    const TYPE_HOLOGRAM_STICKER = 'Hologram Stickers';

    const TYPE_STICKER_AND_PLASTIC_BAG = 'Sticker And Plastic Bag';

    const API_VALUE_PLASTIC_BAG = 'plastic_bag_fee';

    const API_VALUE_MUG_PACKAGING = 'mug_packaging';

    const API_VALUE_HOLOGRAM_STICKER = 'hologram_sticker';

    const API_VALUE_STICKER_AND_BAG = 'sticker_and_bag';

    const API_VALUE_HANDLING = 'handling_fee';

    const API_VALUE_TIKTOK_FEE = 'tiktok_line_fee';

    const API_VALUE_LABEL_PRINTING_FEE = 'labelling_fee';

    const TYPE_EMBROIDERY_10001_TO_15000_STITCHES = '10,001 to 15,000 stitches';

    const TYPE_EMBROIDERY_15001_TO_20000_STITCHES = '15,001 to 20,000 stitches';

    const TYPE_EMBROIDERY_20001_TO_25000_STITCHES = '20,001 to 25,000 stitches';

    const TYPE_EMBROIDERY_25001_TO_30000_STITCHES = '25,001 to 30,000 stitches';

    const API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES = '10001_to_15000_stitches';

    const API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES = '15001_to_20000_stitches';

    const API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES = '20001_to_25000_stitches';

    const API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES = '25001_to_30000_stitches';

    const TYPE_SHIPPING_LABEL_FEE = 'Shipping Label Fee';

    const API_VALUE_SHIPPING_LABEL_FEE = 'shipping_label_fee';
}
