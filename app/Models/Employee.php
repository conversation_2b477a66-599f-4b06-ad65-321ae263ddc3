<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

class Employee extends Model
{
    use HasFactory;

    protected $table = 'employee';

    const PERFORMANCE_REPORT = 1;

    const INACTIVE = 0;

    const ACTIVE = 1;

    const IS_LEADER = 1;

    const NOT_DELETED = 0;

    const JOB_SUBSCRIBE_FIREBASE = 'subscribe-firebase';

    const AVATAR_WIDTH = 500;

    protected $fillable = [
        'name',
        'email',
        'address_1',
        'address_2',
        'phone',
        'department',
        'performance_report',
        'job_title',
        'code',
        'created_at',
        'updated_at',
        'is_deleted',
        'warehouse_id',
        'is_resize_image',
        'avatar',
        'is_leader',
    ];

    public function reprintOrder()
    {
        return $this->hasMany(SaleOrderItemBarcode::class, 'employee_reprint_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function shipmentRefund()
    {
        return $this->hasOne(Shipment::class, 'shipment_refund_id', 'id');
    }

    public function convertPdf()
    {
        return $this->hasOne(PdfConverted::class, 'employee_convert_id', 'id');
    }

    public function internalRequests(): BelongsToMany
    {
        return $this->belongsToMany(InternalRequest::class, 'employee_internal_request', 'employee_id', 'internal_request_it');
    }

    public static function findEmployeeByDepartment($code, $department)
    {
        return DB::table('employee')
            ->where('code', $code)
            ->where('is_deleted', self::INACTIVE)
            ->where('department', $department)
            ->first();
    }

    public static function findLeaderByDepartment($code, $department)
    {
        return DB::table('employee')
            ->where('code', $code)
            ->where('is_deleted', self::INACTIVE)
            ->where('department', $department)
            ->where('is_leader', self::IS_LEADER)
            ->first();
    }

    public static function findById($id)
    {
        return DB::table('employee')
            ->where('id', $id)
            ->where('is_deleted', self::INACTIVE)
            ->first();
    }

    public static function findByCode($code)
    {
        return DB::table('employee')
            ->where('code', $code)
            ->where('is_deleted', self::INACTIVE)
            ->first();
    }
}
