<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ProductQuantity extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'product_quantity';

    protected $fillable = [
        'product_id',
        'quantity',
        'warehouse_id ',
        'incoming_stock',
        'stock_status',
        'alerted_at',
    ];

    const OUT_OF_STOCK = 'OOS';

    const INCOMING_STOCK = 'Incoming';

    const IN_STOCK = 'In_stock';

    const JOB_UPDATE_PRODUCT_QUANTITY = 'queue-send-webhook-notify';

    const JOB_UPDATE_PRODUCT_QUANTITY_INCOMING = 'queue-update-product-quantity-incoming';

    public static function countByStyleAndColor($styleName, $colorName, $warehouseId)
    {
        return DB::table('product_quantity')
            ->select(DB::raw('SUM(product_quantity.quantity) instock'))
            ->join('product', 'product_quantity.product_id', 'product.id')
            ->where('product.style', $styleName)
            ->where('product.color', $colorName)
            ->where('product.parent_id', '!=', Product::PARENT_PRODUCT)
            ->where('product_quantity.warehouse_id', $warehouseId)
            ->first();
    }
}
