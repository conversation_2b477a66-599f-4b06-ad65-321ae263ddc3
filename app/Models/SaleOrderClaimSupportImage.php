<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleOrderClaimSupportImage extends Model
{
    use HasFactory;

    protected $table = 'sale_order_claim_support_image';

    protected $fillable = [
        'sale_order_claim_support_id',
        'link_url',
    ];
    public function support(): BelongsTo
    {
        return $this->belongsTo(SaleOrderClaimSupport::class, 'sale_order_claim_support_id');
    }

}
