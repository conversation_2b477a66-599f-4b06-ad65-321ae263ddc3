<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenOrderHistory extends Model
{
    use HasFactory;

    protected $table = 'screen_order_history';

    protected $fillable = [
        'user_id',
        'screen_order_id',
        'type',
        'message',
        'created_at'
    ];

    public function screenOrder()
    {
        return $this->belongsTo(ScreenOrder::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
