<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Box extends Model
{
    use HasFactory;

    const NOT_DELETED = 0;

    const DELETED = 1;

    protected $table = 'box';

    protected $fillable = [
        'warehouse_id',
        'barcode',
        'is_deleted',
        'location_id',
        'product_id',
        'quantity',
        'employee_id',
        'cost_value',
        'po_id',
        'po_item_id',
        'country',
    ];

    const JOB_CONVERT_BOX_ID_TO_PDF = 'convert_generate_box_id_to_pdf';

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where("$this->table.is_deleted", self::NOT_DELETED);
    }

    public function workOrderItem()
    {
        return $this->hasOne(WorkOrderItem::class, 'box_number', 'barcode');
    }

    public function boxMoving()
    {
        $this->belongsTo(BoxMoving::class, 'box_id', 'id');
    }

    public function inventoryAddition()
    {
        return $this->hasOne(InventoryAddition::class, 'box_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country', 'iso2');
    }

    public function internalRequest()
    {
        return $this->hasOne(InternalRequest::class, 'box_id', 'id');
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }
}
