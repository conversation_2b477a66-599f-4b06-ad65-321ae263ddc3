<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChangeLog extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'change_log';

    protected $fillable = [
        'data',
        'source',
        'created_at',
        'updated_at'
    ];

    const SOURCE_CHANGE_LOG = ['spa', 'seller'];
}
