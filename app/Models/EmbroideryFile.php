<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EmbroideryFile extends Model
{
    protected $table = 'embroidery_file';

    protected $fillable = [
        'embroidery_hash_md5',
        'image_hash_id',
        'task_id ',
        'convert_status',
        'link_url',
    ];

    // Define any relationships if needed, for example:
    // public function saleOrderItem() {
    //     return $this->belongsTo(SaleOrderItem::class, 'sale_order_item_id');
    // }
}
