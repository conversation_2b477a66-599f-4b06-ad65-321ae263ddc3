<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryReference extends Model
{
    use HasFactory;

    protected $table = 'inventory_references';

    protected $fillable = [
        'inventory_id',
        'referenced_inventory_id',
        'quantity',
        'cost_total',
    ];

    public function inventory()
    {
        return $this->belongsTo(Inventory::class, 'inventory_id');
    }

    public function referencedInventory()
    {
        return $this->belongsTo(Inventory::class, 'referenced_inventory_id');
    }
}
