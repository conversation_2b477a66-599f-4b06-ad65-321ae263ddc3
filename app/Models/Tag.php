<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Tag extends Model
{
    use HasFactory;

    protected $table = 'tag';

    protected $fillable = [
        'name',
        'color',
        'source',
        'account_id',
        'external_id',
        'surcharge_service_id',
        'is_additional_service',
    ];

    public function account(): BelongsTo
    {
        return $this->belongsTo(SaleOrderAccount::class, 'account_id');
    }

    const UPGRADE_TO_EXPRESS_SHIPPING = 'UPGRADE TO EXPRESS SHIPPING';

    const UPGRADE_TO_PRIORITY_SHIPPING = 'UPGRADE TO PRIORITY SHIPPING';

    const UPGRADE_TO_UPS_2_DAY_AIR = 'UPGRADE TO UPS 2 DAY AIR';

    const VISUA_DETECTED_ID = 200;

    const SYSTEM_REJECT_ID = 201;

    const MANUAL_REJECT_ID = 202;

    const TAG_SAMPLE_CUSTOM_ID = 195;

    const TAG_SAMPLE_BOX_ID = 196;

    const TAG_SAMPLE_ID = 197;

    const LABEL_TAG_ID = 203;

    const TIKTOK_TAG_ID = 193;

    const TAG_DTG_DTF_ID = 210;

    const TAG_PLASTIC_BAG = 'Plastic Bag';

    const TAG_MUG_PACKAGING = 'Mug Packaging';

    const TAG_HOLOGRAM_STICKER = 'Hologram Stickers';

    const TAG_STICKER_AND_PLASTIC_BAG = 'Sticker And Plastic Bag';

    const TAG_LABEL = 'Label';

    const TAG_RBT_ID = 211;

    const TAG_PHOTO_REQUIRED = 204;

    const TAG_RBT_KITTING_ID = 215;

    const APPERAL_TAG = ['Tee', 'Fleece', 'SweatPants', 'BIB', 'Jogger', 'CAP', 'Short', 'Jacket', 'Tank'];

    const MUG_TAG = ['Mugs'];

    const TUMBLER_TAG = ['Tumbler'];

    const ACCESSORY_TAG = ['Dog leash', 'Coaster', 'Keychain', 'Phone case'];

    const BAG_TAG = ['bag', 'Tote bag'];

    const STICKER_TAG = ['Sticker'];

    const ORNAMENT_TAG = ['Ornament'];

    const CANVAS_TAG = ['Canvas'];

    const PLAQUE_TAG = ['Plaque'];

    const POSTER_TAG = ['Poster'];

    const CUSTOM_TAG = ['Standard SOB', 'Seasonal SOB', 'Custom', 'DTF', 'Insert', 'Coffee'];

    const EMBROIDERY_TAG = 'EMB';

    const BLANK_TAG = 'BLANK';

    const NEW_CUSTOMER_HANDLING_TAG_ID = 230;

    const NEW_CUSTOMER_HANDLING_TAG = 'NEW CUSTOMER HANDLING';

    public static function getProductStyleTypeTag()
    {
        return [
            'Apparel' => self::APPERAL_TAG,
            'Mug' => self::MUG_TAG,
            'Tumbler' => self::TUMBLER_TAG,
            'Accessory' => self::ACCESSORY_TAG,
            'Bag' => self::BAG_TAG,
            'Sticker' => self::STICKER_TAG,
            'Ornament' => self::ORNAMENT_TAG,
            'Canvas' => self::CANVAS_TAG,
            'Plaque' => self::PLAQUE_TAG,
            'Custom' => self::CUSTOM_TAG,
            'Poster' => self::POSTER_TAG
        ];
    }
}
