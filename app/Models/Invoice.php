<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Invoice extends Model
{
    use HasFactory;

    const QUEUE_MAKE_GENERAL_INVOICE = 'make-general-invoice';

    const QUEUE_MAKE_PRODUCTION_INVOICE = 'make-production-invoice';

    const QUEUE_MAKE_SHIPPING_INVOICE = 'make-shipping-invoice';

    const QUEUE_ALERT_DOWNLOAD_INVOICE = 'alert-download-invoice-google-chat';

    const QUEUE_MAKE_PRODUCTION_INVOICE_TEMP = 'make-production-invoice-temp';

    const QUEUE_GENERATE_INVOICE_SELLER = 'generate-invoice-seller';

    const QUEUE_REGENERATE_INVOICE = 'regenerate-invoice';

    const QUEUE_MAKE_INSERT_INVOICE = 'make-insert-invoice';

    const QUEUE_MAKE_SURCHARGE_INVOICE = 'make-surcharge-invoice';

    const QUEUE_MAKE_PROMOTION_INVOICE = 'make-promotion-invoice';

    protected $table = 'invoices';

    const TYPE_DAILY = 'daily';

    const TYPE_BI_WEEKLY = 'bi-weekly';

    const TYPE_WEEKLY = 'weekly';

    const TYPE_MONTHLY = 'monthly';

    const SOURCE_INVENTORY = 'inventory';

    const SOURCE_SELLER = 'seller';

    const TYPE_DOWNLOAD_GENERAL = 'general';

    const TYPE_DOWNLOAD_PRODUCTION = 'production';

    const TYPE_DOWNLOAD_SHIPPING = 'shipping';

    const TYPE_DOWNLOAD_INSERT = 'insert';

    const TYPE_DOWNLOAD_SURCHARGE = 'surcharge';

    const TYPE_DOWNLOAD_PROMOTION = 'promotion';

    const HAS_ERROR = 1;

    const NO_ERROR = 0;

    const HAS_INSERT_INVOICE = 1;

    const ACTIVE = 1;

    protected $fillable = [
        'name',
        'invoice_number',
        'type',
        'start_at',
        'end_at',
        'processing_general_at',
        'processing_production_at',
        'processing_shipping_at',
        'processing_insert_at',
        'processing_surcharge_at',
        'store_id',
        'has_error',
        'has_insert_invoice',
        'has_promotion_order_invoice',
        'has_error_insert_invoice',
        're_calculate_price_at',
    ];

    protected $casts = [
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'processing_general_at' => 'datetime',
        'processing_production_at' => 'datetime',
        'processing_shipping_at' => 'datetime',
        'processing_insert_at' => 'datetime',
    ];

    public function saleOrders(): BelongsToMany
    {
        return $this->belongsToMany(SaleOrder::class, 'invoice_sale_order', 'invoice_id', 'sale_order_id');
    }

    public function saleOrderErrors(): BelongsToMany
    {
        return $this->belongsToMany(SaleOrder::class, 'invoice_sale_order_error', 'invoice_id', 'sale_order_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    protected function getPeriodAttribute()
    {
        return $this->start_at->format('M d') . ' - ' . $this->end_at->format('M d, Y');
    }

    public function saleOrderInserts(): BelongsToMany
    {
        return $this->belongsToMany(SaleOrderInsertCalculatePrice::class, 'invoice_sale_order_insert', 'invoice_id', 'order_insert_id', 'id', 'order_insert_id');
    }

    public function surchargeService(): BelongsTo
    {
        return $this->belongsTo(SurchargeService::class, 'surcharge_id');
    }
}
