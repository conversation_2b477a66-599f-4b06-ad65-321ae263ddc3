<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupplyQuantity extends Model
{
    protected $table = 'supply_quantity';

    public const UPDATE_SUPPLY_INCOMING_QUANTITY_JOB = 'update-supply-incoming-quanity';

    public $timestamps = false;

    protected $fillable = [
        'supply_id',
        'quantity',
        'warehouse_id',
        'incoming_stock',
    ];

    use HasFactory;

    public function supply()
    {
        return $this->belongsTo(Supply::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }
}
