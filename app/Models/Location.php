<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Location extends Model
{
    use HasFactory;

    public $timestamps = true;

    const RACK = 0;

    const PULLING_SHELVES = 1;

    const PENDING_TYPE = 2;

    const MOVING_SHELVES = 3;

    const REPLENISHMENT_TYPE = 4;

    const IS_DELETED = 1;

    const NOT_DELETED = 0;

    const VIRTUAL_LOCATION = 2;

    protected $table = 'location';

    protected $fillable = [
        'warehouse_id',
        'barcode',
        'rbt_type',
        'rbt_sku',
        'created_at',
        'type',
        'rbt_sku',
        'rbt_type',
        'is_deleted',
    ];

    protected $casts = [
        'type' => 'int'
    ];

    public function scopePullingShelves(Builder $query): Builder
    {
        return $query->where("$this->table.type", self::PULLING_SHELVES);
    }

    public function scopeMovingShelves(Builder $query): Builder
    {
        return $query->where("$this->table.type", self::MOVING_SHELVES);
    }

    public function locationProducts(): HasMany
    {
        return $this->hasMany(LocationProduct::class);
    }

    public function boxes()
    {
        return $this->hasMany(Box::class);
    }

    public function product()
    {
        return $this->hasOne(Product::class, 'sku', 'rbt_sku');
    }

    public function latestInventory()
    {
        return $this->hasOne(Inventory::class, 'location_id', 'id')->latest('id');
    }
}
