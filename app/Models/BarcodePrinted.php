<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class BarcodePrinted extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'barcode_printed';

    protected $fillable = [
        'store_id',
        'employee_id',
        'warehouse_id',
        'user_id',
        'is_xqc',
        'style_sku',
        'quantity',
        'quantity_input',
        'convert_status',
        'print_status',
        'converted_at',
        'account_id',
        'convert_percent',
        'first_sku',
        'last_sku',
        'is_reprint',
        'is_manual',
        'print_method',
        'product_id',
        'created_at',
        'printed_at',
        'is_reroute',
        'is_fba',
        'color_sku',
        'is_error_print',
        'reprint_approved_by',
        'status_print',
        'is_insert',
        'is_deduct',
        'is_eps',
        'is_tiktok',
        'pulled_at',
        'is_bulk_order',
        'positions',
        'printed_at',
        'station_id',
        'is_bulk_order',
        'is_top_style'
    ];

    const EMB_PRINT_METHOD = 'EMB';

    const METHOD_DTG = 'DTG';

    const METHOD_DTF = 'DTF';

    const METHOD_DTF_FILM = 'FILM';

    const METHOD_DTF_NECK = 'NECK';

    const METHOD_MUGS = 'MUGS';

    const METHOD_UV3D = 'UV3D';

    const METHOD_UV3DS = 'UV3DS';

    const METHOD_BLANK = 'BLANK';

    const METHOD_PRETREATED = 'PRETREATED';

    const METHOD_UV = 'UV';

    const METHOD_POSTER = 'POSTER';

    const BARCODE_PRINTED_ID_DEFAULT = 0;

    const INACTIVE = 0;

    const ACTIVE = 1;

    const FAILED = 2;

    const STATUS_CONVERTING = 'converting';

    const STATUS_PRINTING = 'printing';

    const STATUS_PRINTED = 'printed';

    const STATUS_ERROR = 'error';

    const JOB_SEND_NOTIFICATION = 'send-notification';

    const JOB_ALERT_WIP_PENDING_SCAN_BATCH = 'wip-pending-scan-batch';

    const WIP_STATUS_PENDING_CONVERT = 'pending_convert';

    const WIP_STATUS_PENDING_PRINT = 'pending_print';

    const WIP_STATUS_DONE = 'done';

    const JOB_DEDUCTION_INVENTORY = 'deduction-inventory';

    const JOB_AUTO_DEDUCTION = 'auto-deduction';

    const JOB_RBT_AUTO_DEDUCTION = 'rbt-auto-deduction';

    const PRINTED_STATUS = 1;

    public function employeeConvert()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function style()
    {
        return $this->belongsTo(ProductStyle::class, 'style_sku', 'sku');
    }

    public function color()
    {
        return $this->belongsTo(ProductColor::class, 'color_sku', 'sku');
    }

    public function barcodes()
    {
        return $this->hasMany(SaleOrderItemBarcode::class, 'barcode_printed_id', 'id');
    }

    public static function findLastCreatedByProductId($product_id, $warehouse_id, $input = [])
    {
        $query = self::where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id);
        if (!empty($input['store_id']) && !empty($input['priorityStores']) && in_array($input['store_id'], $input['priorityStores'])) {
            $query->where('store_id', $input['store_id']);
        } elseif (!empty($args['priorityStores'])) {
            $query->whereNotIn('store_id', $input['priorityStores']);
        }
        if (!empty($input['is_tiktok'])) {
            $query->where('is_tiktok', true);
        } elseif (!empty($input['is_fba'])) {
            $query->where('is_fba', true);
        } elseif (!empty($input['is_reroute'])) {
            $query->where('is_reroute', true);
        } elseif (!empty($input['is_manual'])) {
            $query->where('is_manual', true);
        } elseif (!empty($input['is_reprint'])) {
            $query->where('is_reprint', true);
        } elseif (!empty($input['is_xqc'])) {
            $query->where('is_xqc', true);
        } elseif (!empty($input['is_eps'])) {
            $query->where('is_eps', true);
        } else {
            $query->whereNull('is_tiktok');
            $query->whereNull('is_fba');
            $query->whereNull('is_reroute');
            $query->whereNull('is_manual');
            $query->where('is_reprint', false);
            $query->where('is_xqc', false);
            $query->whereNull('is_eps');
        }

        return $query
            ->orderByDesc('id')
            ->first();
    }

    public static function findLastCreatedByStyleSku($styleSku, $warehouse_id, $input = [])
    {
        $query = self::where('style_sku', $styleSku)
            ->where('warehouse_id', $warehouse_id);
        if (!empty($input['store_id']) && !empty($input['priorityStores']) && in_array($input['store_id'], $input['priorityStores'])) {
            $query->where('store_id', $input['store_id']);
        } elseif (!empty($args['priorityStores'])) {
            $query->whereNotIn('store_id', $input['priorityStores']);
        }

        return $query->orderByDesc('id')
            ->first();
    }

    public static function listPdfMugs($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_MUGS)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAILED)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAILED);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listPdf3d($warehouse_id, $limit, $printMethod)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size,sku'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', $printMethod)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAILED)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAILED);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listPoster($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size,sku'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_POSTER)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAILED)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAILED);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function historyPdf($warehouse_id, $limit, $label_id)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->where('print_method', self::METHOD_MUGS)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function historyPdf3d($warehouse_id, $limit, $label_id, $printMethod)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->where('print_method', $printMethod)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function historyPdfPoster($warehouse_id, $limit, $label_id)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,color,size,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->where('print_method', self::METHOD_POSTER)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function findToConvert()
    {
        return self::where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_MUGS)
            ->first();
    }

    public static function findToPoster()
    {
        return self::where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_POSTER)
            ->first();
    }

    public static function find3DToConvert()
    {
        return self::where('convert_status', self::INACTIVE)
            ->whereIn('print_method', [self::METHOD_UV3D, self::METHOD_UV3DS])
            ->first();
    }

    public static function listPdfDtf($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'style:id,name,sku'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_DTF)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listBarcodeOrnament($warehouse_id, $limit)
    {
        $query = self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->whereHas('barcodes', function ($query) {
                $query->whereHas('orderItem', function ($q) {
                    $q->whereHas('productStyle', function ($q1) {
                        $q1->whereNotIn('type', ['Poster', 'Sticker']);
                    });
                });
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_UV);

        return $query->orderByDesc('id')
            ->paginate($limit);
    }

    public static function findToConvertDtf()
    {
        return self::where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_DTF)
            ->first();
    }

    public static function listHistoryBarcodeOrnament($warehouse_id, $limit, $label_id)
    {
        $query = self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                $query->whereHas('orderItem', function ($q) {
                    $q->whereHas('productStyle', function ($q1) {
                        $q1->whereNotIn('type', ['Poster', 'Sticker']);
                    });
                });
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_UV);

        return $query->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistoryDtf($warehouse_id, $limit, $label_id)
    {
        $query = self::with([
            'employeeConvert:id,name',
            'style:id,name,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', self::METHOD_DTF);

        return $query->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listBarcodeDtf($warehouse_id, $limit, $printMethod)
    {
        return self::with([
            'employeeConvert:id,name',
            'style:sku,name',
            'store:id,name,code'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', $printMethod)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistoryBarcodeDtf($warehouse_id, $limit, $label_id, $printMethod)
    {
        return self::with([
            'employeeConvert:id,name',
            'style:sku,name'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('label_id', 'LIKE', '%' . $label_id . '%');
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', self::ACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', $printMethod)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function checkWipPending($styleSku, $colorSku, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->where('style_sku', $styleSku)
            ->where('color_sku', $colorSku)
            ->where('convert_status', self::ACTIVE)
            ->where('print_status', self::INACTIVE)
            ->where('warehouse_id', $warehouseId)
            ->where('is_error_print', self::INACTIVE)
            ->first();
    }

    public static function checkWipPendingDeduct($styleSku, $colorSku, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->where('style_sku', $styleSku)
            ->where('color_sku', $colorSku)
            ->where('convert_status', self::ACTIVE)
            ->where('print_status', self::ACTIVE)
            ->where('warehouse_id', $warehouseId)
            ->where('is_error_print', self::INACTIVE)
            ->where('is_deduct', self::INACTIVE)
            ->first();
    }

    public static function checkWipError($styleSku, $colorSku, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->where('style_sku', $styleSku)
            ->where('color_sku', $colorSku)
            ->where('warehouse_id', $warehouseId)
            ->where(function ($query) {
                $query->where('convert_status', BarcodePrinted::FAILED)->orWhere('is_error_print', BarcodePrinted::ACTIVE);
            })
            ->first();
    }

    public static function listReprintBarcode($limit, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->select(DB::raw('barcode_printed.*, product_style.name as style_name, product_color.name as color_name, employee.name as employee_name, barcode_printed_time.printed_at'))
            ->join('product_style', 'product_style.sku', 'barcode_printed.style_sku')
            ->join('product_color', 'product_color.sku', 'barcode_printed.color_sku')
            ->join('employee', 'employee.id', 'barcode_printed.employee_id')
            ->leftJoin('barcode_printed_time', function ($query) use ($warehouseId) {
                $query->on('barcode_printed_time.style_sku', 'barcode_printed.style_sku')
                    ->where('barcode_printed_time.color_sku', 'barcode_printed.color_sku')
                    ->where('barcode_printed_time.warehouse_id', $warehouseId);
            })
            ->where('barcode_printed.is_error_print', self::ACTIVE)
            ->whereNotNull('barcode_printed.color_sku')
            ->where('barcode_printed.warehouse_id', $warehouseId)
            ->whereNotNull('barcode_printed.employee_id')
            ->paginate($limit);
    }

    public static function checkWipPendingByEmployee($employeeId, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->whereNotNull('color_sku')
            ->where('print_status', self::INACTIVE)
            ->where('is_error_print', self::INACTIVE)
            ->where('employee_id', $employeeId)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }

    public static function checkWipPendingDeductByEmployee($employeeId, $warehouseId)
    {
        return DB::table('barcode_printed')
            ->whereNotNull('color_sku')
            ->where('print_status', self::ACTIVE)
            ->where('is_deduct', self::INACTIVE)
            ->where('is_error_print', self::INACTIVE)
            ->where('employee_id', $employeeId)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }

    public static function countWipByStyleAndColorAndWarehouse($styleName, $colorName, $warehouseId)
    {
        return DB::table('sale_order_item_barcode')
            ->select(DB::raw('product_style.name as style_name, product_color.name as color_name, COUNT(DISTINCT sale_order_item_barcode.id) total, barcode_printed_time.printed_at as printed_at'))
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product_color', 'product_color.sku', 'sale_order_item.product_color_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouseId) {
                $join->on('barcode_printed_time.style_sku', 'sale_order_item.product_style_sku');
                $join->on('barcode_printed_time.color_sku', 'sale_order_item.product_color_sku');
                $join->where('barcode_printed_time.warehouse_id', $warehouseId);
            })
            ->where('product.style', $styleName)
            ->where('product.color', $colorName)
            ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
            ->where('sale_order.is_eps', SaleOrder::INACTIVE)
            ->where('sale_order.is_test', SaleOrder::INACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order.warehouse_id', $warehouseId)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
            ->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058))
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
            ->where('product_style.type', '!=', ProductStyle::TYPE_INSERT)
            ->first();
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }
}
