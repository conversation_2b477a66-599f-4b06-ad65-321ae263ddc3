<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\Request;

class Shipment extends Model
{
    use HasFactory;

    protected $table = 'shipment';

    protected $fillable = [
        'id',
        'order_id',
        'ship_date',
        'shipment_cost',
        'insurance_cost',
        'carrier_code',
        'tracking_number',
        'service_code',
        'is_return_label',
        'package_code',
        'weight_value',
        'weight_unit',
        'dimension_length',
        'dimension_width',
        'dimension_height',
        'dimension_unit',
        'shipment_quantity',
        'sync_tracking',
        'sync_tracking_retry',
        'shipment_quantity',
        'refund_status',
        'employee_refund_id',
        'label_url',
        'url_tracking_easypost',
        'provider',
        'is_auto_created',
        'tracking_status',
        'employee_printed_id',
        'created_at',
        'employee_create_id',
        'is_manual',
        'warehouse_id',
        'account_id',
        'store_id',
        'shipment_account',
        'account_shipping_easypost',
        'est_delivery_at',
    ];

    const DELIVERED = 'delivered';

    const RETURN_TO_SENDER = 'return_to_sender';

    const FAILURE = 'failure';

    const CANCELLED = 'cancelled';

    const ERROR = 'error';

    const PACKAGE_DEFAULT = 'package';

    const WEIGHT_DEFAULT = 'ounces';

    const DIMENSION_DEFAULT = 'inches';

    const UNKNOWN = 'unknown';

    const PRE_TRANSIT = 'pre_transit';

    const PROVIDER_SHIPSTATION = 'shipstation';

    const PROVIDER_EASYPOST = 'easypost';

    const PROVIDER_MANUAL = 'manual';

    const PROVIDER_AMAZON = 'amazon';

    const PROVIDER_MARKETPLACE = 'marketplace';

    const RATIO_POUNDS_TO_OZ = 16;

    const WEIGHT_UNIT_POUNDS = 'pounds';

    const VOID_SUBMITTED = 'submitted';

    const SHIPMENT_ACCOUNT_AMAZON = 'amazon';

    const SHIPMENT_ACCOUNT_SWIFTPOD = 'swiftpod';

    const SHIPMENT_ACCOUNT_SWIFTPOD_STORE = 'swiftpod_store';

    const SHIPMENT_ACCOUNT_STORE = 'store';

    const PROVIDER_GELATO = 'gelato';

    const SCANFORM_SWIFTPOD_NAME = 'SPALL';

    const SCANFORM_TIKTOK_NAME = 'SPTIKTOK';

    const SHIPMENT_STATUS = [
        'unknown' => 'Unknown',
        'pre_transit' => 'Pre transit',
        'in_transit' => 'In transit',
        'out_for_delivery' => 'Out for delivery',
        'delivered' => 'Delivered',
        'available_for_pickup' => 'Available for pickup',
        'return_to_sender' => 'Return to sender',
        'failure' => 'Failure',
        'cancelled' => 'Cancelled',
        'error' => 'Error',
    ];

    const IN_TRANSIT = 'in_transit';

    const CARRIER_CODE_ASENDIA = 'AsendiaUsa';

    const JOB_SHIPMMENT_REFUND = 'shipment-refund';

    const JOB_SYNC_TRACKING_TO_SHIPSTATION = 'sync-tracking-to-shipstation';

    const IS_MANUAL_TRACKING = 1;

    const JOB_UPLOAD_S3_LABEL_SHIPPING = 'upload-s3-label-shipping';

    const PAYMENT_SHIPMENT = [
        // PMALL
        '484276' => [
            'type' => 'THIRD_PARTY',
            'account' => '*********',
            'country' => 'US',
            'postal_code' => '60527'
        ],
        // Sam's Club
        '484284' => [
            'type' => 'THIRD_PARTY',
            'account' => '*********',
            'country' => 'US',
            'postal_code' => '72712'
        ]
    ];

    public function address(): HasOne
    {
        return $this->hasOne(ShipmentAddress::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(ShipmentItem::class);
    }

    public function saleOrderAddress(): HasMany
    {
        return $this->hasMany(SaleOrderAddress::class, 'order_id', 'order_id');
    }

    public function saleOrder(): BelongsTo
    {
        return $this->belongsTo(SaleOrder::class, 'order_id', 'id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    public function employeeRefund(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'employee_refund_id', 'id');
    }

    public function shipmentLabelPrinted(): HasMany
    {
        return $this->hasMany(ShipmentLabelPrinted::class, 'shipment_id', 'id');
    }

    public function shipmentLabelLastPrinted(): HasMany
    {
        return $this->hasMany(ShipmentLabelPrinted::class, 'shipment_id', 'id')->orderByDesc('printed_date');
    }

    public function sync(): HasOne
    {
        return $this->hasOne(ShipmentSync::class, 'shipment_id', 'id');
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where("$this->table.warehouse_id", $request['warehouse_id']);
        }

        if (!empty($request['warehouse'])) {
            $query->where("$this->table.warehouse_id", $request['warehouse']);
        }

        if ($request['shipment_status']) {
            $query->where("$this->table.tracking_status", $request['shipment_status']);
        }

        if (!empty($request['shipment_date_start'])) {
            $startDate = Carbon::parse($request['shipment_date_start'])->startOfDay()->format('Y-m-d H:i:s');
            $query->where("$this->table.created_at", '>=', $startDate);
        }

        if (!empty($request['shipment_date_end'])) {
            $endDate = Carbon::parse($request['shipment_date_end'])->endOfDay()->format('Y-m-d H:i:s');
            $query->where("$this->table.created_at", '<=', $endDate);
        }

        if (!empty($request['shipment_service'])) {
            $query->where("$this->table.service_code", $request['shipment_service']);
        }

        if (!empty($request['shipping_carrier'])) {
            $query->where("$this->table.carrier_code", $request['shipping_carrier']);
        }

        if (!empty($request['provider'])) {
            $query->where("$this->table.provider", $request['provider']);
        }

        if (!empty($request['store_id'])) {
            $query->where("$this->table.store_id", $request['store_id']);
        }

        if (isset($request['store_ids']) && is_array($request['store_ids'])) {
            $query->whereIn("$this->table.store_id", $request['store_ids']);
        }

        $keyword = $request['keyword'];

        if (!empty($keyword)) {
            $saleOrderIds = SaleOrder::where('order_number', $keyword)->orWhere('external_number', $keyword)->get('id')->pluck('id');
            $query->where(function ($q) use ($keyword, $saleOrderIds) {
                $q->where("$this->table.tracking_number", $keyword)
                    ->orWhereIn("$this->table.order_id", $saleOrderIds);
            });
        }

        if (!empty($request['create_by'])) {
            if ($request['create_by'] == 'auto') {
                $query->where("$this->table.is_auto_created", 1);
            } else {
                $query->where("$this->table.is_auto_created", 0);
            }
        }

        return $query;
    }

    public function shippingCarrierService(): BelongsTo
    {
        return $this->belongsTo(ShippingCarrierService::class, 'service_code', 'name');
    }

    public function shippingCarrier(): BelongsTo
    {
        return $this->belongsTo(ShippingCarrier::class, 'carrier_code', 'code');
    }

    public function saleOrderItem()
    {
        return $this->hasManyThrough(SaleOrderItem::class, SaleOrder::class, 'id', 'order_id', 'order_id', 'id');
    }

    public function employeeCreate(): HasOne
    {
        return $this->hasOne(Employee::class, 'id', 'employee_create_id');
    }

    public function employeePrinted(): HasOne
    {
        return $this->hasOne(Employee::class, 'id', 'employee_printed_id');
    }

    public function saleOrderAccount(): BelongsTo
    {
        return $this->belongsTo(SaleOrderAccount::class, 'account_id', 'id');
    }

    public function easypostLog(): HasOne
    {
        return $this->hasOne(EasyPostLog::class);
    }

    public static function findShipmentManualTracking($id)
    {
        return self::where('id', $id)
            ->where('is_manual', self::IS_MANUAL_TRACKING)
            ->first();
    }

    public static function countOrderByRangeShipTimeByQuery($startTime, $endTime, $warehouseId, $storeId)
    {
        $query = self::selectRaw('HOUR(created_at) as hour, COUNT(DISTINCT order_id) as count, warehouse_id, store_id, created_at')
            ->whereNull('employee_refund_id')
            ->where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime);
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->groupBy('hour')->orderBy('hour')->get();
    }

    public static function countOrderShippedByRange($startTime, $warehouseId = null, $storeId = null)
    {
        $query = self::selectRaw('COUNT(DISTINCT order_id) as count')
            ->where('created_at', '>=', $startTime)
            ->whereNull('employee_refund_id');
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->first();
    }

    public static function countTotalOrdersByRangeTime($startTime, $endTime, $warehouseId, $storeId)
    {
        $query = self::query()
            ->where('created_at', '<=', $endTime)
            ->where('created_at', '>=', $startTime)
            ->whereNull('employee_refund_id');
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->count();
    }

    public function shipmentItemLabels(): HasMany
    {
        return $this->hasMany(ShipmentItemLabel::class, 'shipment_id', 'id');
    }

    public function shipmentLabelFirstPrinted(): HasOne
    {
        return $this->hasOne(ShipmentLabelPrinted::class, 'shipment_id', 'id')->orderBy('printed_date');
    }

    public function orderAddressTo(): HasMany
    {
        return $this->hasMany(SaleOrderAddress::class, 'order_id', 'order_id')->where('type_address', SaleOrderAddress::TO_ADDRESS);
    }

    public function shipmentEasypost(): HasOne
    {
        return $this->hasOne(ShipmentEasypost::class, 'shipment_id', 'id');
    }

    public static function GetIdShipmentBeforeDays($days = 30)
    {
        // Find shipment id before ... days
        $result = self::selectRaw('MIN(id) as id')
            ->where('created_at', '>', now()->subDays($days))
            ->where('id', '>', env('ID_SHIPMENT_VALID', 6000000))
            ->first();

        if (!empty($result->id)) {
            return $result->id;
        }

        return env('ID_SHIPMENT_VALID', 6000000);
    }

    public function shippingIntegrationAccount()
    {
        return $this->hasOne(ShippingIntegrationAccount::class, 'carrier_account', 'account_shipping_easypost');
    }

    public function shipmentManifestTracking()
    {
        return $this->hasOne(ShipmentManifestTracking::class, 'tracking_number', 'tracking_number');
    }

    public function carrier()
    {
        return $this->hasOne(ShipmentCarrier::class, 'code', 'carrier_code');
    }
}
