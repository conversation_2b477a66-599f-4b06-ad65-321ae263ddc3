<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductStyle extends Model
{
    use HasFactory;

    protected $table = 'product_style';

    protected $fillable = [
        'name',
        'sku',
        'type',
        'gender',
        'print_method',
        'sleeve_length',
        'description',
        'icc_white_convert_status',
    ];

    const STYLE_MUGS = 'MUGS';

    const METHOD_DTG = 'DTG';

    const METHOD_MUGS = 'MUGS';

    const METHOD_DTF = 'DTF';

    const METHOD_UV = 'UV';

    const METHOD_EMB = 'EMB';

    const METHOD_UV3D = 'UV3D';

    const METHOD_POSTER = 'POSTER';

    const STYLE_THANKYOU_CARD = 'INTU';

    const STYLE_PACKING_SLIP = 'INPS';

    const TYPE_INSERT = 'Insert';

    const PACKING_SLIP_STYLE = 'INPS';

    const THANK_YOU_CARD_STYLE = 'INTU';

    const GIFT_MESSAGE_STYLE = 'INCM';

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        }

        if (!empty($request['sku'])) {
            $query->where("$this->table.sku", 'LIKE', '%' . $request['sku'] . '%');
        }

        if (!empty($request['type'])) {
            if ($request['type'] == 'empty') {
                $query->whereNull("$this->table.type");
            } else {
                $query->where("$this->table.type", 'LIKE', '%' . $request['type'] . '%');
            }
        }

        if (!empty($request['gender'])) {
            if ($request['gender'] == 'empty') {
                $query->whereNull("$this->table.gender");
            } else {
                $query->where("$this->table.gender", 'LIKE', '%' . $request['gender'] . '%');
            }
        }

        return $query;
    }

    public function productPrintAreas()
    {
        return $this->hasMany(ProductPrintArea::class, 'product_style_id');
    }

    public function product()
    {
        return $this->hasMany(Product::class, 'style', 'name');
    }

    public function saleOrderItems()
    {
        return $this->hasMany(SaleOrderItem::class, 'product_style_sku', 'sku');
    }

    public static function getSkuOrnament()
    {
        return self::where('print_method', self::METHOD_UV)->pluck('sku', 'name')->toArray();
    }

    public static function findBySku($style_sku)
    {
        return self::where('sku', $style_sku)
            ->where('print_method', self::METHOD_DTF)
            ->first();
    }

    public function storeShippingPrices()
    {
        return $this->hasManyThrough(StoreShipment::class, ProductType::class, 'name', 'product_type', 'type', 'name');
    }

    public function productType()
    {
        return $this->belongsTo(ProductType::class, 'type', 'name');
    }

    public function barcodePrinted()
    {
        return $this->hasMany(BarcodePrinted::class, 'style_sku', 'sku');
    }

    public static function getStyleDtf()
    {
        return self::whereHas('productPrintAreas', function ($query) {
            $query->where('product_print_area.print_method', self::METHOD_DTF);
        })->get();
    }

    public static function getStyleSKUsDtf()
    {
        return self::whereHas('productPrintAreas', function ($query) {
            $query->where('product_print_area.print_method', self::METHOD_DTF);
        })->get('sku')->toArray();
    }

    public static function findByName($styleName)
    {
        return DB::table('product_style')->where('name', $styleName)->first();
    }

    public static function findByStyleSku($sku)
    {
        return DB::table('product_style')->where('sku', $sku)->first();
    }

    public function printers()
    {
        return $this->belongsToMany(Printer::class, 'printer_product', 'style_sku', 'printer_id', 'sku', 'id')->withPivot('color_sku');
    }

    public function printerColors()
    {
        return $this->belongsToMany(ProductColor::class, 'printer_product', 'style_sku', 'color_sku', 'sku', 'sku')->withPivot('printer_id');
    }
}
