<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TmpInvoiceDownload extends Model
{
    protected $table = 'tmp_invoice_download';

    use HasFactory;

    protected $fillable = [
        'store_id',
        'date',
        'type',
        'link_url',
        'status',
        'created_at',
        'updated_at'
    ];

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }
}
