<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class WeightCubic extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'weight_cubic';

    protected $fillable = [
        'weight_start',
        'weight_end',
        'cubic',
        'name_cubic',
    ];

    const WEIGHT_START_HAS_CUBIC = 16;
    const WEIGHT_END_HAS_CUBIC = 60;

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name_cubic", 'LIKE', '%' . $request['name'] . '%');
        };

        return $query;
    }

}
