<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventory extends Model
{
    const DIRECTION_INPUT = 0;

    const DIRECTION_OUTPUT = 1;

    const TYPE_INPUT = 'input';

    const TYPE_OUTPUT = 'output';

    const TYPE_ADJUST = 'adjust';

    const TYPE_REVERT = 'revert';

    const OBJECT_ADDITION = 'addition';

    const OBJECT_ADJUST_PULLING_SHELVES = 'adjust pulling shelves';

    const OBJECT_ADJUST_SHELVE_FACE = 'adjust shelve face';

    const OBJECT_REVERT_ADDITION = 'revert_addition';

    const OBJECT_REVERT_DEDUCTION = 'revert_deduction';

    const OBJECT_DEDUCTION = 'deduction';

    const OBJECT_DEDUCTION_SPOILAGE = 'deduction_spoilage';

    const OBJECT_DEDUCTION_UNLINKED = 'deduction_unlinked';

    const OBJECT_TEST_COUNT = 'test count';

    const OBJECT_STOCK_TRANSFER = 'stock transfer';

    const OBJECT_INTERNAL_REQUEST = 'internal request';

    const OBJECT_CREATE_BOX = 'create box';

    const FIFO_NOT_CALCULATED = 0;

    use HasFactory;

    public $timestamps = false;

    protected $table = 'inventory';

    protected $fillable = [
        'direction',
        'type',
        'location_id',
        'product_id',
        'warehouse_id',
        'created_at',
        'is_deleted',
        'box_id',
        'user_id',
        'object_id',
        'object_name',
        'quantity',
        'new_product',
        'replicated_from_id',
        'fifo_calculated_at',
        'remaining_qty',
        'stock_qty_ending',
        'stock_value_ending',
        'cost_total',
    ];

    public function setStockValueEndingAttribute($value)
    {
        $this->attributes['stock_value_ending'] = round($value, 2);
    }

    public function adjustShelvesFace()
    {
        return $this->belongsTo(AdjustShelvesFace::class, 'object_id', 'id');
    }
}
