<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipmentManifestTracking extends Model
{
    use HasFactory;

    protected $table = 'shipment_manifest_tracking';

    protected $fillable = [
        'manifest_id',
        'service_code_id',
        'employee_scan_id',
        'tracking_number',
        'ship_id_partner',
        'created_at',
        'updated_at'
    ];

    public function shippingService(): BelongsTo
    {
        return $this->belongsTo(ShippingCarrierService::class, 'service_code_id', 'id');
    }

    public function shipmentManifest(): BelongsTo
    {
        return $this->belongsTo(ShipmentManifest::class, 'manifest_id', 'id');
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'employee_scan_id', 'id');
    }

    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class, 'tracking_number', 'tracking_number');
    }

}
