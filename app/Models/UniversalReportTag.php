<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UniversalReportTag extends Model
{
    use HasFactory;

    public const TYPE_INPUT = 'input';

    public const TYPE_SINGLE_SELECT = 'single_select';

    public const TYPE_MULTI_SELECT = 'multi_select';

    public const TYPE_SINGLE_DATE = 'single_date';

    public const TYPE_RANGE_DATE = 'range_date';

    public const VALUE_TYPE_FREE_TEXT = 'free_text';

    public const VALUE_TYPE_SQL = 'sql';

    public const RANGE_TODAY = 'today';

    public const RANGE_YESTERDAY = 'yesterday';

    public const RANGE_THIS_WEEK = 'this_week';

    public const RANGE_LAST_WEEK = 'last_week';

    public const RANGE_LAST_2_WEEKS = 'last_2_weeks';

    public const RANGE_THIS_MONTH = 'this_month';

    public const RANGE_LAST_MONTH = 'last_month';

    protected $table = 'universal_report_tags';

    protected $fillable = [
        'report_template_id',
        'is_used',
        'is_required',
        'code',
        'position',
        'type',
        'label',
        'value',
        'value_type',
        'placeholder_1',
        'placeholder_2',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'report_template_id' => 'integer',
        'is_used' => 'boolean',
        'is_required' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'position' => 'integer',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    public static function allowedRange(): array
    {
        return [
            self::RANGE_TODAY,
            self::RANGE_YESTERDAY,
            self::RANGE_THIS_WEEK,
            self::RANGE_LAST_WEEK,
            self::RANGE_LAST_2_WEEKS,
            self::RANGE_THIS_MONTH,
            self::RANGE_LAST_MONTH,
        ];
    }

    public function transformTagValues($tagSelected = [], $valueSelected = [])
    {
        switch ($this->type) {
            case UniversalReportTag::TYPE_INPUT:
            case UniversalReportTag::TYPE_SINGLE_DATE:
            case UniversalReportTag::TYPE_SINGLE_SELECT:
                $tagSelected[] = [
                    'type' => $this->type,
                    'code' => $this->code,
                    'is_used' => $this->is_used,
                    'value_selected' => collect($valueSelected)->map(function ($item) {
                        return substr(\Illuminate\Support\Facades\DB::getPdo()->quote($item), 1, -1);
                    })->implode(', '),
                ];
                break;
            case UniversalReportTag::TYPE_MULTI_SELECT:
                $tagSelected[] = [
                    'type' => $this->type,
                    'code' => $this->code,
                    'is_used' => $this->is_used,
                    'value_selected' => collect($valueSelected)->map(function ($item) {
                        return "'" . substr(\Illuminate\Support\Facades\DB::getPdo()->quote($item), 1, -1) . "'";
                    })->implode(', '),
                ];
                break;

            case UniversalReportTag::TYPE_RANGE_DATE:
                [$startDate, $endDate] = transformDateRange($valueSelected);

                $tagSelected[] = [
                    'type' => $this->type,
                    'code' => "{$this->code}.0",
                    'is_used' => $this->is_used,
                    'value_selected' => substr(\Illuminate\Support\Facades\DB::getPdo()->quote($startDate), 1, -1),
                ];
                $tagSelected[] = [
                    'type' => $this->type,
                    'code' => "{$this->code}.1",
                    'is_used' => $this->is_used,
                    'value_selected' => substr(\Illuminate\Support\Facades\DB::getPdo()->quote($endDate), 1, -1),
                ];
                break;
            default:
                throw new Exception('Unsupported tag type');
        }

        return $tagSelected;
    }
}
