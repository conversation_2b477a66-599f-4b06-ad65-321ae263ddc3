<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\ShipmentItemLabel;

class ShipmentItem extends Model
{

    use HasFactory;

    protected $table = 'shipment_item';

    protected $fillable = [
        'warehouse_id',
        'account_id',
        'store_id',
        'order_id',
        'order_item_id',
        'shipment_id',
        'sku',
        'quantity',
        'product_id',
        'product_sku',
    ];

    public function shipmentItemLabels():hasMany
    {
        return $this->hasMany(ShipmentItemLabel::class, 'shipment_item_id', 'id');
    }




}
