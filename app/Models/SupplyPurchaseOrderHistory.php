<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class SupplyPurchaseOrderHistory extends Model
{
    use HasFactory;

    protected $table = "supply_purchase_order_histories";

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $fillable = [
        'user_id',
        'po_id',
        'po_id',
        'type',
        'message',
        'old_data',
        'current_data',
    ];
    const TYPE_CREATE = "create";
    const TYPE_UPDATE = "update";
    const TYPE_CANCEL = "cancel";
    const TYPE_COMMENT = "comment";

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
