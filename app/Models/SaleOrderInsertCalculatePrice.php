<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderInsertCalculatePrice extends Model
{
    use HasFactory;

    protected $table = 'sale_order_insert_calculate_price';

    protected $fillable = [
        'order_id',
        'order_insert_id',
        'qty',
        'amount_paid',
        'unit_price',
        'blank_price',
        'handling_fee',
        'calculated_at',
        'reason',
        'size',
        'type',
        'product_id',
        'product_sku',
    ];
}
