<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'username',
        'password',
        'user_id',
    ];

    public const REDBUBBLE_CLIENT_ID = [94, 132];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function stores()
    {
        return $this->hasMany(Store::class, 'client_id');
    }
}
