<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductColor extends Model
{
    use HasFactory;

    protected $table = 'product_color';

    protected $fillable = [
        'name',
        'sku',
        'color_code',
        'neck_label_color',
        'icc_color',
    ];

    const NECK_LABEL_COLOR = [
        'black' => '#000000',
        'white' => '#ffffff',
    ];

    const WHITE_COLOR = 'WHITE';

    const BLACK_COLOR = 'BLACK';

    protected $casts = [
        'neck_label_color' => 'string',
    ];

    public function setNeckLabelColorAttribute($value)
    {
        $this->attributes['neck_label_color'] = self::NECK_LABEL_COLOR[strtolower($value)] ?? $value;
    }

    public function getNeckLabelColorAttribute($value)
    {
        $neckLabelFlip = array_flip(self::NECK_LABEL_COLOR);

        return $neckLabelFlip[$value] ?? $value;
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        }

        if (!empty($request['sku'])) {
            $query->where("$this->table.sku", 'LIKE', '%' . $request['sku'] . '%');
        }

        return $query;
    }

    public static function findByName($colorName)
    {
        return DB::table('product_color')->where('name', $colorName)->first();
    }

    public static function findBySku($sku)
    {
        return DB::table('product_color')->where('sku', $sku)->first();
    }

    public function printers()
    {
        return $this->belongsToMany(Printer::class, 'printer_product', 'color_sku', 'printer_id', 'sku', 'id')->withPivot('style_sku');
    }

    public function printerStyles()
    {
        return $this->belongsToMany(ProductStyle::class, 'printer_product', 'color_sku', 'style_sku', 'sku', 'sku')->withPivot('printer_id');
    }

    public static function getByNames($names)
    {
        return DB::table('product_color')
            ->whereIn('name', $names)
            ->get(['sku', 'name']);
    }
}
