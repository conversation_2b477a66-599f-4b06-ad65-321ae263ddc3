<?php

namespace App\Models\SaleOrderBarcode;

use App\Models\BarcodePrinted;
use App\Models\Claim;
use App\Models\Employee;
use App\Models\PartNumber;
use App\Models\PdfConverted;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\RbtWipReceived;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Models\Store;
use Carbon\Carbon;
use Database\Factories\SaleOrderItemBarcodeFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SaleOrderItemBarcode extends Model
{
    use HasFactory;

    protected $table = 'sale_order_item_barcode';

    protected static function newFactory()
    {
        return SaleOrderItemBarcodeFactory::new();
    }

    protected $fillable = [
        'order_id',
        'order_item_id',
        'sku',
        'barcode_number',
        'order_quantity',
        'is_deleted',
        'employee_pull_id',
        'pulled_at',
        'employee_pretreat_id',
        'pretreated_at',
        'employee_print_id',
        'printed_at',
        'employee_qc_id',
        'qc_at',
        'employee_ship_id',
        'shipped_at',
        'barcode_printed_id',
        'user_id',
        'print_barcode_at',
        'label_id',
        'staged_at',
        'employee_staging_id',
        'employee_folding_id',
        'folded_at',
        'reprinted_at',
        'employee_reprint_id',
        'reprint_status',
        'label_root_id',
        'employee_reroute_id',
        'retry_convert',
        'convert_pdf_status',
        'pdf_converted_id',
        'warehouse_id',
        'print_method',
        'store_id',
        'warehouse_id',
        'part_number_id',
        'employee_press_id',
        'pressed_at',
        'employee_kitted_id',
        'kitted_at',
        'employee_discard_id',
        'discarded_at',
    ];

    const TABLE_NAME = 'sale_order_item_barcode';

    const ACTIVE = 0;

    const INACTIVE = 1;

    const REPRINTED = 1;

    const NOT_REPRINTED = 0;

    const PRODUCTION_STATUS = [
        'pulled' => 'pulled_at',
        'pretreated' => 'pretreated_at',
        'printed' => 'employee_print_id',
        'qc' => 'qc_at',
        'pressed' => 'pressed_at',
        'staged' => 'staged_at',
        'folded' => 'folded_at',
        'shipped' => 'shipped_at',
        'reprinted' => 'reprinted_at'
    ];

    const CONVERT_PDF_FAIL = 0;

    const CONVERT_PDF_SUCCESS = 1;

    const MAX_RETRY = 5;

    const PDF_CONVERT_NOTYET = 0;

    const PDF_CONVERT_SUCCESS = 1;

    const PDF_CONVERT_FAIL = 2;

    const IS_DELETED = 1;

    const NOT_DELETED = 0;

    const METHOD_DTF = 'DTF';

    const METHOD_UV = 'UV';

    const DELETED = 1;

    const BARCODE_BLANK_VIEW = 'barcode_blank_shirt';

    const BARCODE_PRETREATED_VIEW = 'barcode_pretreated_shirt';

    public function scopeActive(Builder $query): Builder
    {
        return $query->where("$this->table.is_deleted", self::ACTIVE);
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where('warehouse_id', $request['warehouse_id']);
        }

        if (!empty($request['warehouse'])) {
            $query->where("$this->table.warehouse_id", $request['warehouse']);
        }

        if (is_numeric($request['store_id'])) {
            $query->where('store_id', $request['store_id']);
        }
        if (isset($request['store_ids']) && is_array($request['store_ids'])) {
            $query->whereIn("$this->table.store_id", $request['store_ids']);
        }

        if (!empty($request['order_date_start']) || !empty($request['order_date_end']) || !empty($request['keyword'])) {
            $query->whereHas('saleOrder', function ($q) use ($request, $query) {
                if (!empty($request['order_date_start'])) {
                    $q->whereDate('created_at', '>=', $request['order_date_start']);
                }

                if (!empty($request['order_date_end'])) {
                    $q->whereDate('created_at', '<=', $request['order_date_end']);
                }

                $keyword = $request['keyword'];

                if (!empty($keyword)) {
                    $shipmentIds = Shipment::where('tracking_number', $keyword)->get('id')->pluck('id');
                    $q->where(function ($q1) use ($keyword, $shipmentIds) {
                        $q1->where('sale_order.external_number', $keyword)
                            ->orWhereIn('sale_order.shipment_id', $shipmentIds);
                    });
                    $queryLabel = SaleOrderItemBarcode::where('label_id', $keyword);
                    $queryLabel->whereHas('saleOrder', function ($q1) use ($request) {
                        if (!empty($request['order_date_start'])) {
                            $q1->whereDate('created_at', '>=', $request['order_date_start']);
                        }

                        if (!empty($request['order_date_end'])) {
                            $q1->whereDate('created_at', '<=', $request['order_date_end']);
                        }
                    });
                    $query->union($queryLabel);
                }
            });
        }

        if (array_key_exists($request['production_status_name'], self::PRODUCTION_STATUS) && in_array($request['production_status'], [1, 0])) {
            $type = self::PRODUCTION_STATUS[$request['production_status_name']];

            if ($request['production_status'] == 1) {
                $query->whereNotNull("$this->table.$type");
            } else {
                $query->whereNull("$this->table.$type");
            }
        }

        $status = array_keys(self::PRODUCTION_STATUS);

        foreach ($status as $value) {
            $statusValue = $request[$value];
            if ($statusValue !== null) {
                $type = self::PRODUCTION_STATUS[$value];

                if ($statusValue == '1') {
                    $query->whereNotNull("$this->table.$type");
                } else {
                    $query->whereNull("$this->table.$type");
                }
            }
        }

        if (is_numeric($request['employee_id'])) {
            $columns = [
                'employee_pull_id', 'employee_pretreat_id', 'employee_print_id',
                'employee_qc_id', 'employee_staging_id', 'employee_folding_id',
                'employee_ship_id', 'employee_reprint_id'
            ];
            $query->where(function ($q) use ($request, $columns) {
                foreach ($columns as $column) {
                    $q->orWhere("$this->table.$column", $request['employee_id']);
                }
            });
        }

        return $query;
    }

    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)->insert($rawData);
    }

    public function updateBySKU($sku, $rawData)
    {
        return DB::table(self::TABLE_NAME)->where('sku', $sku)
            ->update($rawData);
    }

    public function updateByOrderId($orderId, $rawData)
    {
        return DB::table(self::TABLE_NAME)->where('order_id', $orderId)
            ->update($rawData);
    }

    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)->where('id', $id)
            ->update($rawData);
    }

    public function countOrderByOrderId($id)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $id)
            ->where('is_deleted', '<>', 1)
            ->count();
    }

    public function countOrderItemScan($idOrder)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $idOrder)
            ->whereNotNull('staged_at')
            ->where(self::TABLE_NAME . '.is_deleted', '<>', 1)
            ->count();
    }

    public function employeeReprint()
    {
        return $this->belongsTo(Employee::class, 'employee_reprint_id', 'id');
    }

    public function claim()
    {
        return $this->hasOne(Claim::class, 'label_id', 'label_root_id');
    }

    public function saleOrder()
    {
        return $this->belongsTo(SaleOrder::class, 'order_id', 'id');
    }

    public function orderItem()
    {
        return $this->belongsTo(SaleOrderItem::class, 'order_item_id', 'id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function account()
    {
        return $this->belongsTo(SaleOrderAccount::class, 'account_id');
    }

    public function images()
    {
        return $this->hasMany(SaleOrderItemImage::class, 'sku', 'sku');
    }

    public function pdfConverteds()
    {
        return $this->belongsToMany(PdfConverted::class, 'pdf_converted_item', 'barcode_id', 'pdf_converted_id')
            ->withPivot(['pdf_converted_id', 'barcode_id', 'print_side', 'print_method', 'convert_status'])
            ->withTimestamps();
    }

    public static function getItemsToConvertPdf($barcode_printed_id)
    {
        return self::with([
            'saleOrder',
            'orderItem.images:id,order_item_id,image_url,image_width,image_height,image_size,image_hash_id,upload_s3_status,order_date,sku,print_side,image_dpi',
            'orderItem.images.imageHash',
            'orderItem.product',
            'store'
        ])
            ->where('barcode_printed_id', $barcode_printed_id)
            ->where('is_deleted', self::ACTIVE)
            ->get();
    }

    public static function getListConvertErrors($input, $limit)
    {
        return self::select('id', 'convert_pdf_status', 'retry_convert', 'order_id', 'store_id', 'account_id', 'order_item_id')
            ->with([
                'store:id,name',
                'account:id,name',
                'saleOrder:id,order_number,external_id,external_number,order_date',
                'orderItem.images:id,order_item_id,image_url,print_side',
            ])
            ->whereHas('orderItem', function ($query) {
                $query->where('product_style_sku', Product::STYLE_MUGS);
            })
            ->whereHas('saleOrder', function ($query) use ($input) {
                if (!empty($input['order_number'])) {
                    $query->where('order_number', $input['order_number']);
                }

                if (!empty($input['order_date'])) {
                    $query->whereDate('order_date', $input['order_date']);
                }
            })
            ->whereHas('store', function ($query) use ($input) {
                if (!empty($input['store_id'])) {
                    $query->where('id', $input['store_id']);
                }
            })
            ->whereHas('account', function ($query) use ($input) {
                if (!empty($input['account_id'])) {
                    $query->where('id', $input['account_id']);
                }
            })
            ->where('convert_pdf_status', self::CONVERT_PDF_FAIL)
            ->where('retry_convert', '>=', self::MAX_RETRY)
            ->paginate($limit);
    }

    public static function updatePdfConvertId($barcode_printed, $product_id, $limit, $warehouse_id, $printMethod, $input = [])
    {
        $query = self::whereHas('saleOrder', function ($query) {
            $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('is_test', SaleOrder::NOT_TEST)
                ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
        })
            ->whereHas('orderItem', function ($query) use ($product_id) {
                $query->where('product_id', $product_id)
                    ->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE);
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********)) // 2022-11-01
            ->where('is_deleted', self::ACTIVE)
            ->where('print_method', $printMethod)
            ->where('barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT);

        if (!empty($input['store_id']) && !empty($input['priorityStores']) && in_array($input['store_id'], $input['priorityStores'])) {
            $query->where('store_id', $input['store_id']);
        } elseif (!empty($input['priorityStores'])) {
            $query->whereNotIn('store_id', $input['priorityStores']);
        }

        $query->orderBy('id', 'ASC');

        return $query->limit($limit)
            ->update([
                'barcode_printed_id' => $barcode_printed->id
            ]);
    }

    public static function getListBarcodeConverted($warehouse_id, $printMethod, $input = [])
    {
        $query = self::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product', 'sale_order_item.product_id', 'product.id')
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order.id', '>', env('ID_SALE_ORDER_VALID', 6411823))
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item.warehouse_id', $warehouse_id)
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********)) // 2022-11-01
            ->where('sale_order_item_barcode.is_deleted', self::ACTIVE)
            ->where('sale_order_item_barcode.print_method', $printMethod)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT);

        if (!empty($input['is_bulk_order'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_xqc', '<>', 1)
                ->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));
        } elseif (!empty($input['is_tiktok'])) {
            $query->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        } elseif (!empty($input['is_fba'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::ACTIVE);
            self::excludeBulkOrder($query);
        } elseif (!empty($input['is_reroute'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNotNull('sale_order_item_barcode.employee_reroute_id');
            self::excludeBulkOrder($query);
        } elseif (!empty($input['is_manual'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::ACTIVE);
            self::excludeBulkOrder($query);
        } elseif (!empty($input['is_reprint'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::INACTIVE)
                ->whereNotNull('sale_order_item_barcode.label_root_id');
            self::excludeBulkOrder($query);
        } elseif (!empty($input['is_xqc'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.label_root_id')
                ->where('sale_order.is_xqc', SaleOrder::ACTIVE);
        } elseif (!empty($input['is_eps'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.label_root_id')
                ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
                ->where('sale_order.is_eps', SaleOrder::ACTIVE);
            self::excludeBulkOrder($query);
        } elseif (!empty($input['product_id'])) { // style line
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.label_root_id')
                ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
                ->where('sale_order.is_eps', SaleOrder::INACTIVE)
                ->where('sale_order_item.product_id', $input['product_id']);
            self::excludeBulkOrder($query);
        } elseif (!empty($input['is_all'])) {
            $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_manual', SaleOrder::INACTIVE)
                ->whereNull('sale_order_item_barcode.label_root_id')
                ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
                ->where('sale_order.is_eps', SaleOrder::INACTIVE);
            self::excludeBulkOrder($query);
        }

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $query->where('sale_order.store_id', $input['store_id']);
            } else {
                $query->whereNotIn('sale_order.store_id', $input['priorityStores']);
            }
        } elseif (!empty($input['store_id'])) {
            $query->where('sale_order.store_id', $input['store_id']);
        }

        return $query;
    }

    public static function excludeBulkOrder(&$qr)
    {
        return $qr->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->where('sale_order.is_xqc', '=', 1);
                });
        });
    }

    public static function countDtfConvert($warehouse_id)
    {
        return self::whereHas('saleOrder', function ($query) {
            $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('is_test', SaleOrder::NOT_TEST)
                ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823));
        })
            ->whereHas('orderItem', function ($query) {
                $query->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
                    ->where('id', '>', env('ID_SALE_ORDER_ITEM_VALID', 6934193))
                    ->where(function ($item) {
                        // get style use DTF method
                        $item->where(function ($subQuery) {
                            $subQuery->whereHas('productStyle', function ($sq) {
                                $sq->where('print_method', ProductStyle::METHOD_DTF);
                            });
                        });
                    });
            })
            ->with([
                'orderItem' => function ($query) {
                    $query->with(['productStyle:sku,print_method,name', 'lastCreateDtf:style_sku,created_at'])
                        ->select('product_style_sku', DB::raw('count(*) as total'), 'id')
                        ->groupBy('product_style_sku');
                }
            ])
            ->where('is_deleted', self::ACTIVE)
            ->where('barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('retry_convert', '<', self::MAX_RETRY)
            ->where('warehouse_id', $warehouse_id)
            ->select('order_item_id')
            ->get()
            ->filter(function ($value) {
                return !is_null($value->orderItem);
            })
            ->values();
    }

    public static function updateBarcodePrintedIdBlankShirt($barcode_printed, $limit, $styleSku, $employee_id, $warehouse_id)
    {
        return self::whereHas('saleOrder', function ($query) {
            $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('is_test', SaleOrder::NOT_TEST)
                ->where('order_type', SaleOrder::ORDER_TYPE_BLANK)
                ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
        })
            ->whereHas('orderItem', function ($query) use ($styleSku) {
                $query->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
                    ->where('product_style_sku', $styleSku)
                    ->where('id', '>', env('ID_SALE_ORDER_ITEM_VALID', 6934193)); // 2022-11-01
            })
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********)) // 2022-11-01
            ->where('is_deleted', self::ACTIVE)
            ->where('barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('warehouse_id', $warehouse_id)
            ->limit($limit)
            ->update([
                'barcode_printed_id' => $barcode_printed->id,
                'employee_pull_id' => $employee_id,
                'print_barcode_at' => Carbon::now()
            ]);
    }

    public static function findByLabelPrinted($label_id, $product_id, $warehouse_id, $side)
    {
        return self::whereHas('orderItem', function ($query) use ($product_id) {
            $query->where('product_id', $product_id);
        })
            ->whereHas('saleOrder', function ($query) {
                $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                    ->where('is_test', SaleOrder::NOT_TEST)
                    ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
            })
            ->where(function ($query) use ($side) {
                $query->whereHas('pdfConverteds', function ($sq) use ($side) {
                    $sq->where('pdf_converted_item.print_side', $side)
                        ->where('pdf_converted_item.convert_status', self::PDF_CONVERT_SUCCESS)
                        ->where('pdf_converted_item.print_method', self::METHOD_UV);
                }, '=', 0);
                $query->orWhereDoesntHave('pdfConverteds', function ($q) {
                    $q->where('pdf_converted_item.print_method', self::METHOD_UV);
                });
            })
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********))
            ->where('label_id', $label_id)
            ->where('barcode_printed_id', '!=', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('is_deleted', self::ACTIVE)
            ->where('warehouse_id', $warehouse_id)
            ->where('print_method', self::METHOD_UV)
            ->first();
    }

    public static function findByLabelPrintedDtf($label_id, $printMethod, $warehouse_id)
    {
        return self::with('orderItem')
            ->whereHas('saleOrder', function ($query) {
                $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                    ->where('is_test', SaleOrder::NOT_TEST)
                    ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
            })
            ->where(function ($query) use ($label_id, $printMethod) {
                $query->whereHas('pdfConverteds', function ($sq) use ($label_id, $printMethod) {
                    $sq->where('pdf_converted_item.label_id', $label_id)
                        ->where('pdf_converted_item.convert_status', self::PDF_CONVERT_SUCCESS)
                        ->where('pdf_converted_item.print_method', $printMethod);
                }, '=', 0);
                $query->orWhereDoesntHave('pdfConverteds', function ($q) use ($printMethod) {
                    $q->where('pdf_converted_item.print_method', $printMethod);
                });
            })
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********))
            ->where('label_id', $label_id)
            ->where('barcode_printed_id', '!=', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('is_deleted', self::ACTIVE)
            ->where('warehouse_id', $warehouse_id)
            ->where('print_method', $printMethod)
            ->first();
    }

    public static function findByLabelPrintedNeckDtf($label_id, $warehouse_id, $side)
    {
        return self::whereHas('saleOrder', function ($query) {
            $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('is_test', SaleOrder::NOT_TEST)
                ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
        })
            ->where(function ($query) use ($side) {
                $query->whereHas('pdfConverteds', function ($sq) use ($side) {
                    $sq->where('pdf_converted_item.print_side', $side)
                        ->where('pdf_converted_item.convert_status', self::PDF_CONVERT_SUCCESS)
                        ->where('pdf_converted_item.print_method', PrintMethod::NECK);
                }, '=', 0);
                $query->orWhereDoesntHave('pdfConverteds', function ($q) {
                    $q->where('pdf_converted_item.print_method', PrintMethod::NECK);
                });
            })
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********))
            ->where('label_id', $label_id)
            ->where('barcode_printed_id', '!=', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('is_deleted', self::ACTIVE)
            ->where('warehouse_id', $warehouse_id)
            ->where('print_method', PrintMethod::NECK)
            ->first();
    }

    public static function findByLabelPrintedDtfNeck($labelId, $warehouse_id)
    {
        return self::whereHas('saleOrder', function ($query) {
            $query->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('is_test', SaleOrder::NOT_TEST)
                ->where('id', '>', env('ID_SALE_ORDER_VALID', 6411823)); // 2022-11-01
        })
            ->where('label_id', $labelId)
            ->where('is_deleted', self::ACTIVE)
            ->where('id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', *********))
            ->where('barcode_printed_id', '!=', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('warehouse_id', $warehouse_id)
            ->where('print_method', PrintMethod::NECK)
            ->first();
    }

    public static function getArrayOrderIds($barcode_printed_id)
    {
        return self::where('barcode_printed_id', $barcode_printed_id)->pluck('order_id')->toArray();
    }

    public static function findByLabelId($label_id)
    {
        return self::with('orderItem')->where('label_id', $label_id)->where('is_deleted', self::ACTIVE)->first();
    }

    public function partNumber()
    {
        return $this->belongsTo(PartNumber::class, 'part_number_id', 'id');
    }

    public function isProductSampleCustom()
    {
        $productArr = explode(',', env('CUSTOM_PRODUCT_NOT_DEDUCTION', 'CUSTNLXXX'));

        return !empty($this->orderItem) && in_array($this->orderItem->product_sku, $productArr);
    }

    public function receivedWip()
    {
        return $this->hasOne(RbtWipReceived::class, 'label_id', 'label_id');
    }
}
