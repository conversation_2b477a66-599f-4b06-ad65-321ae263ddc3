<?php

namespace App\Models;

use App\Repositories\StoreAddressRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\Request;
use Laravel\Cashier\Billable;

/**
 * @property mixed $code
 */
class Store extends Authenticatable
{
    use HasFactory, Billable;

    protected $table = 'store';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'order_desk_api_key',
        'order_desk_store_id',
        'code',
        'is_calculate_shipping',
        'is_calculate_price',
        'billing_contact',
        'days_until_bill_due',
        'has_note',
        'template_neck',
        'is_auto_create_shipping',
        'date_start_auto_label',
        'is_on_hold',
        'client_id',
        'token',
        'auto_shipping_pending_at',
        'sale_rep',
        'billing_address_id',
        'auto_shipping_pending_at',
        'payment_terms',
        'is_refilling',
        'in_stock_date',
        'on_demand_date',
    ];

    protected $hidden = [
        'password',
    ];

    public $timestamps = true;

    const AUTO_CREATE_SHIPPING = 1;

    const STATUS_ACTIVE = 1;

    const HAS_NOTE = 1;

    const TEMPLATE_NECK_SWIFTPOD = 0;

    const TEMPLATE_NECK_PRINTIFY = 1;

    const MODE_AUTO_CREATE_SHIPPING = [
        0 => 'Disable Auto Shipping',
        1 => 'Single Deduction & Multiple Staging',
        2 => 'Shirt Printed (Printing App)',
        3 => 'WIP Printed',
        4 => 'After Quality Control'
    ];

    const PRINTIFY_API_ID = 316905;

    const STORE_ON_HOLD_SETTING_TEST = 'store_on_hold_setting_test';

    const STORE_DEFAULT_SHIPPING = 1;

    const IS_ON_HOLD = 1;

    const IS_NOT_ON_HOLD = 0;

    const CARRIER = ['USPS', 'AsendiaUsa', 'UPS', 'UPSDAP'];

    const STORE_GOOD_MVMT = 333662;

    const STORE_GELATO = 484182;

    const STORE_REDBUBBLE = 484174;

    const STORE_DLS = 296998;

    const STORE_BONF = 311034;

    const STORE_BNFR_API = 484181;

    const STORE_80S_TEE = 484201;

    const STORE_SCALEFUL = 484277;

    const STORE_D2_AMERICA = 484144;

    const STORE_D2_LICENSED = 484243;

    const STORE_PMALL = 484276;

    const STORE_SAMCLUB = 484284;

    const STORE_EXCLUDE_CHECK_IP = 'store_exclude_check_ip';

    const STORE_POSTPAID = 'postpaid';

    const STORE_PREPAID = 'prepaid';

    const RETURN_ADDRESS = 'return_address';

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (is_numeric($request['is_active'])) {
            $query->where('is_active', $request['is_active']);
        }

        return $query;
    }

    public function storeAddress()
    {
        return $this->hasMany(StoreAddress::class);
    }

    public function storeAddressBilling()
    {
        return $this->hasOne(StoreAddress::class)->where('type_address', StoreAddressRepository::TYPE_ADDRESS_BILLING);
    }

    public function storeProducts()
    {
        return $this->hasMany(StoreProduct::class);
    }

    public function account()
    {
        return $this->belongsTo(SaleOrderAccount::class, 'account_id');
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public static function findByName($name)
    {
        return self::where('name', $name)->first();
    }

    public function shippingMethods(): HasMany
    {
        return $this->hasMany(ShippingMethod::class);
    }

    public function storeSurchargeFee(): HasMany
    {
        return $this->hasMany(SurchargeFee::class, 'store_id', 'id');
    }

    public function isStoreSample(): bool
    {
        $storeCodes = explode(',', env('STORE_CODE_SAMPLE_ORDER', 'STTR'));

        return in_array($this->code, $storeCodes);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function userSale()
    {
        return $this->belongsTo(User::class, 'sale_rep');
    }

    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    public function walletBillingAddress()
    {
        return $this->hasOne(WalletBillingAddress::class, 'id', 'billing_address_id');
    }

    public function autoRefill()
    {
        return $this->hasOne(AutoRefill::class);
    }

    public function promotions()
    {
        return $this->hasMany(Promotion::class, 'store_id');
    }
}
