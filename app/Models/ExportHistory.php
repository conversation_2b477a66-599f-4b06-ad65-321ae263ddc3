<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportHistory extends Model
{
    use HasFactory;

    public const STATUS_PENDING = 'pending';

    public const STATUS_COMPLETED = 'completed';

    public const STATUS_FAILED = 'failed';

    public const MODULE_TEST_COUNT = 'test_count';

    public const MODULE_ADJUST_PULLING_SHELVES = 'adjust_pulling_shelves';

    public const MODULE_COGS_REPORT = 'fifo_report_exported';

    public const MODULE_INVENTORY_MOVEMENT = 'inventory_movement';

    protected $fillable = [
        'module',
        'user_id',
        'warehouse_id',
        'start_date',
        'end_date',
        'status',
        'file_path',
        'exception',
        'other_conditions',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
