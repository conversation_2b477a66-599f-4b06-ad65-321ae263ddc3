<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UniversalReportSettingExport extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'universal_report_setting_exports';

    protected $fillable = [
        'report_setting_id',
        'model_type',
        'model_id',
        'model_key',
        'alias',
        'value',
    ];

    protected $casts = [
        'report_setting_id' => 'integer',
        'model_id' => 'integer',
    ];
}
