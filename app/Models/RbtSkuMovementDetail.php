<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtSkuMovementDetail extends Model
{
    use HasFactory;

    protected $table = 'rbt_sku_movement_detail';

    protected $fillable = [
        'rbt_sku_movement_id',
        'status',
        'product_id',
        'sku',
        'action',
        'current_change_rack',
        'total_change_rack',
        'old_barcode',
        'new_barcode',
        'picked_by',
        'confirm_by',
        'picked_at',
        'created_by',
        'confirm_at',
        'created_at',
        'type'
    ];

    const STATUS_COMPLETED = 'completed';

    const STATUS_NEW = 'new';

    const STATUS_IN_PROGRESS = 'in_progress';

    const ACTION_ADD = 'add';

    const ACTION_MOVE_SKU = 'move_sku';

    const ACTION_MOVE_RACK = 'move_rack';

    const ACTION_SWAP_RACK = 'swap_rack';

    const ACTION_SWAP_SKU = 'swap_sku';

    const ACTION_REMOVE = 'remove';

    const ACTION_PICK = 'PICK_TASK';

    const SKU_MOVEMENT = 'SKU_MOVEMENT';

    const ACTION_CONFIRM = 'CONFIRM_TASK';

    const EVENTS = [
        self::ACTION_PICK,
        self::ACTION_CONFIRM
    ];

    protected $appends = ['old_rbt_type', 'new_rbt_type'];

    public function getOldRbtTypeAttribute($query)
    {
        $oldBarcode = Location::where('barcode', 'like', $this->old_barcode . '%')->first();

        return $oldBarcode ? $oldBarcode->rbt_type : null;
    }

    public function getNewRbtTypeAttribute($query)
    {
        $newBarcode = Location::where('barcode', 'like', $this->new_barcode . '%')->first();

        return $newBarcode ? $newBarcode->rbt_type : null;
    }

    public function movement()
    {
        return $this->belongsTo(RbtSkuMovement::class, 'rbt_sku_movement_id');
    }

    public function newLocation()
    {
        return $this->belongsTo(Location::class, 'new_barcode', 'barcode');
    }

    public function oldLocation()
    {
        return $this->belongsTo(Location::class, 'old_barcode', 'barcode');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function pickedBy()
    {
        return $this->belongsTo(Employee::class, 'picked_by');
    }

    public function confirmedBy()
    {
        return $this->belongsTo(Employee::class, 'confirm_by');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function pullingShelvesQuantity()
    {
        $pullingShelves = Location::where('type', Location::PULLING_SHELVES)
            ->where('warehouse_id', config(('jwt.warehouse_id')))
            ->first();

        return $this->hasOne(LocationProduct::class, 'product_id', 'product_id')
            ->where('location_id', $pullingShelves->id);
    }
}
