<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StoreCallbackUrl extends Model
{
    use HasFactory;

    const ORDER_NOTIFY_EVENT = 'order_notify';

    const SHIPMENT_NOTIFY = 'shipment_notify';

    const STOCK_NOTIFY = 'stock_notify';

    const PRINTED_NOTIFY = 'printed_notify';

    protected $table = 'store_callback_url';

    protected $fillable = [
        'store_id',
        'event',
        'url',
        'name'
    ];

    public static function allEvents(): array
    {
        return [self::ORDER_NOTIFY_EVENT, self::SHIPMENT_NOTIFY, self::STOCK_NOTIFY];
    }
}
