<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BarcodeScanHistory extends Model
{
    use HasFactory;

    protected $table = 'barcode_scan_histories';

    const ARTWORK_TYPE_NORMAL = 'normal';

    const ALL_IN_ONE_STANDARD = 'ALL_IN_ONE_STANDARD';

    const ALL_IN_ONE_LONG_SLEEVE = 'ALL_IN_ONE_LONG_SLEEVE';

    const ALL_IN_ONE_SHORT_SLEEVE_NECK = 'ALL_IN_ONE_SHORT_SLEEVE_NECK';

    const SWIFTPOD_PRINTING = 'SWIFTPOD_PRINTING';

    protected $fillable = [
        'employee_id',
        'label_id',
        'side_code',
        'scanned_at',
        'artwork_type',
    ];
}
