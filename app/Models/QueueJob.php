<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QueueJob extends Model
{
    use HasFactory;

    const QUEUE_UPLOAD_S3 = 'upload-s3';

    const QUEUE_UPLOAD_S3_URGENT = 'upload-s3-urgent'; // xqc eps

    const QUEUE_UPLOAD_S3_LIST = [self::QUEUE_UPLOAD_S3, self::QUEUE_UPLOAD_S3_URGENT];

    const QUEUE_CREATE_THUMB_ARTWORK = 'create-thumb-artwork';

    const QUEUE_CREATE_THUMB_ARTWORK_URGENT = 'create-thumb-artwork-urgent';

    const QUEUE_CREATE_THUMB_ARTWORK_LOW_PRIORITY = 'create-thumb-artwork-low-priority';

    const QUEUE_CREATE_THUMB_ARTWORK_LIST = [self::QUEUE_CREATE_THUMB_ARTWORK, self::QUEUE_CREATE_THUMB_ARTWORK_LOW_PRIORITY,  self::QUEUE_CREATE_THUMB_ARTWORK_URGENT];

    const QUEUE_DETECT_COLOR_ARTWORK = 'detect-color-artwork';

    const QUEUE_UPDATE_ORDER_ITEM_COLOR_STATUS = 'update-order-item-color-status';

    const QUEUE_TRIM_TRANSPARENT_ARTWORK = 'trim-transparent-artwork';

    const QUEUE_DETECT_PRINT_METHOD = 'detect-print-method';

    const QUEUE_CONVERT_BARCODE_MOBILE = 'convert-barcode-mobile';

    const QUEUE_GET_SHIPMENT_STATUS_HISTORY = 'get-shipment-status-history';

    const CANCEL_EMBROIDERY_TASK = 'cancel-embroidery-task';

    const STORES_LOW_PRIORITY = [296998]; // DLS

    const CREATE_EMBROIDERY_TASK = 'create-embroidery-task';

    const SEND_NOTIFY_REJECT_IMAGE = 'send-notify-reject-image';

    const CONVERT_MUG_PDF = 'queue-convert-mugs-pdf';

    const CONVERT_IMAGE_DTF = 'queue-convert-image-dtf';

    const CONVERT_IMAGE_LATEX = 'queue-convert-image-latex';

    const AGGREGATE_SUPPLY_INVENTORY_REPORT = 'aggregate-supply-inventory-report';

    const QUEUE_SYNC_ORDER_DESK_ORDER = 'sync-order-desk-order';

    const QUEUE_SYNC_ORDER_DESK_TRACKING = 'sync-order-desk-tracking';

    const QUEUE_SYNC_ORDER_DESK_REJECT = 'sync-order-desk-reject';

    const QUEUE_SYNC_ORDER_DESK = 'sync-order-desk';

    const QUEUE_CREATE_SHIPPING_LABEL = 'create-shipping';

    const QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER = 'check-recycled-tracking-number';

    const QUEUE_GEMINI_DETECT_DESIGN = 'gemini-detect-design';

    public $timestamps = false;

    protected $table = 'queue_job';

    protected $fillable = [
        'id',
        'name',
        'created_at'
    ];

    public static function countQueue($name)
    {
        $model = new self();

        return $model->setwhere('name', $name)->count();
    }

    public static function bulkInsert($items, $name)
    {
        $data = [];
        foreach ($items as $item) {
            $data[] = ['id' => $item, 'name' => $name];
        }

        return self::insertOrIgnore($data);
    }
}
