<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamMemberRolePermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_member_role_id',
        'function_name',
        'permission',
    ];

    const VIEW_PERMISSION = 'view';

    const ALL_PERMISSION = 'all';

    const NO_PERMISSION = 'none';

    const SALE_REPORT_FUNCTION = 'sales_report';

    const FULFILLMENT_REPORT_FUNCTION = 'fulfillment_report';

    const ORDERS_FUNCTION = 'orders';

    const CATALOG_FUNCTION = 'catalog';

    const WALLET_INVOICE_FUNCTION = 'wallet_invoices';

    const CLAIM_FUNCTION = 'claims';

    const API_FUNCTION = 'api';

    const MEMBER_FUNCTION = 'members';

    const ROLE_FUNCTION = 'roles';

    public static function listFuction()
    {
        return [
            self::SALE_REPORT_FUNCTION,
            self::FULFILLMENT_REPORT_FUNCTION,
            self::ORDERS_FUNCTION,
            self::CATALOG_FUNCTION,
            self::WALLET_INVOICE_FUNCTION,
            self::CLAIM_FUNCTION,
            self::API_FUNCTION,
            self::MEMBER_FUNCTION,
            self::ROLE_FUNCTION,
        ];
    }

    public static function listPermission()
    {
        return [
            self::VIEW_PERMISSION,
            self::ALL_PERMISSION,
            self::NO_PERMISSION,
        ];
    }
}
