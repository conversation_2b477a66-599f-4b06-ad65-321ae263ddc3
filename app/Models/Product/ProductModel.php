<?php

namespace App\Models\Product;

use Illuminate\Support\Facades\DB;

class ProductModel
{
    const TABLE_NAME = 'product';

    public function getProductBySku($sku)
    {
        return DB::table(self::TABLE_NAME)->where('sku', $sku)->first();
    }

    public function findProductParentByName($name)
    {
        return DB::table(self::TABLE_NAME)
            ->where('name', $name)
            ->where('parent_id', 0)
            ->first();
    }

}
