<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UniversalReportSetting extends Model
{
    use HasFactory;

    public const EXPORT_STATUS_NONE = 'none';

    public const EXPORT_STATUS_PENDING = 'pending';

    public const EXPORT_STATUS_COMPLETED = 'completed';

    public const EXPORT_STATUS_FAILED = 'failed';

    protected $table = 'universal_report_settings';

    protected $fillable = [
        'report_template_id',
        'user_id',
        'is_default',
    ];

    protected $casts = [
        'report_template_id' => 'integer',
        'user_id' => 'integer',
        'is_default' => 'boolean',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('ownerReportSetting', function (Builder $builder) {
            $builder->where(function ($query) {
                $query->where('user_id', auth()->id())
                      ->orWhereNull('user_id');
            });
        });
    }

    public function settingExports()
    {
        return $this->hasMany(UniversalReportSettingExport::class, 'report_setting_id');
    }

    public function settingColumns()
    {
        return $this->hasMany(UniversalReportSettingExport::class, 'report_setting_id')
            ->where('model_type', UniversalReportColumn::class);
    }

    public function settingTags()
    {
        return $this->hasMany(UniversalReportSettingExport::class, 'report_setting_id')
            ->where('model_type', UniversalReportTag::class);
    }

    public function template()
    {
        return $this->belongsTo(UniversalReportTemplate::class, 'report_template_id');
    }
}
