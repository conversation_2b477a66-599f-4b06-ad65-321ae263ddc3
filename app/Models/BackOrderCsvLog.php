<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BackOrderCsvLog extends Model
{
    public $timestamps = true;

    use HasFactory;

    protected $table = 'backorder_csv_log';

    const SUCCESS = 1;
    const PENDING = 0;
    const FOLDER_UPLOAD = 'inventory-backorder-csv';

    protected $fillable = [
        'employee_id',
        'file',
        'original_file_name',
        'import_status',
        'warehouse_id',
        'total_row'
    ];

}
