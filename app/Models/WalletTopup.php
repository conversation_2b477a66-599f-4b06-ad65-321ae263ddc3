<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Str;

class WalletTopup extends Model
{
    use HasFactory;

    const BANK_TRANSFER_METHOD = 'us_bank_account';

    const CARD_METHOD = 'card';

    const FOLDER_LOCATION = 'topups';

    const STATUS_PENDING = 'pending';

    const STATUS_APPROVED = 'approved';

    const STATUS_PROCESSING = 'processing';

    const STATUS_FAILED = 'failed';

    const APPROVE_AUTO = 'auto';

    const APPROVE_MANUAL = 'manual';

    const CENT_CONVERT_RATIO = 100; // 1 cent = 0.01$

    const SEND_EMAIL_TOPUP = 'send-email-topup';

    const PAYMENT_GATEWAY_STRIPE = 'stripe';

    protected $fillable = [
        'store_id',
        'topup_number',
        'reference_number',
        'amount',
        'amount_requested',
        'amount_received',
        'status',
        'payment_fee',
        'payment_method',
        'note',
        'payment_method_id',
        'payment_method_data',
        'payment_gateway',
        'payment_account_id',
        'is_refill',
    ];

    protected $casts = [
        'payment_method_data' => 'array',
        'is_refill' => 'boolean',
    ];

    public static function booted()
    {
        parent::booted();

        static::creating(function ($model) {
            $model->signature = self::generateEncodeId();
        });
    }

    public static function listTopupMethod()
    {
        return [
            self::CARD_METHOD,
            self::BANK_TRANSFER_METHOD,
        ];
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    public function transaction()
    {
        return $this->belongsTo(WalletTransaction::class, 'id', 'object_id')->where('object_type', WalletTransaction::TRANSACTION_TYPE_TOPUP);
    }

    public static function generateEncodeId()
    {
        return 'wl_' . md5(Str::uuid());
    }
}
