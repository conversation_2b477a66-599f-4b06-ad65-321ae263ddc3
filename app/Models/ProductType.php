<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductType extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'product_type';

    protected $appends = ['icon_url'];

    const TEE = 'Tee';

    const FLEECE = 'Fleece';

    const ORNAMENT = 'Ornament';

    const STICKER = 'Sticker';

    const POSTER = 'Poster';

    const PLAQUE = 'Plaque';

    const MUGS = 'Mugs';

    const TUMBLER = 'Tumbler';

    const SPECIAL_TYPE = ['Mugs', 'Ornament', 'Tumbler', 'Sticker', 'Poster'];

    protected $fillable = [
        'name',
        'alias',
        'icon',
        'is_hard_goods',
    ];

    public function storeShipments()
    {
        return $this->hasMany(StoreShipment::class, 'product_type', 'name');
    }

    public function productStyle(): HasMany
    {
        return $this->hasMany(ProductStyle::class, 'type', 'name');
    }

    public function getIconUrlAttribute()
    {
        $url = env('APP_URL') . '/storage/IconProduct/';

        return $url . $this->icon;
    }
}
