<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenOrderItemImage extends Model
{
    use HasFactory;

    protected $table = 'screen_order_item_images';

    protected $fillable = [
        'screen_order_item_id',
        'screen_order_id',
        'print_side',
        'design_id',
        'mockup_id',
        'sku'
    ];

    public function item()
    {
        return $this->belongsTo(ScreenOrderItem::class, 'screen_order_item_id');
    }

    public function order()
    {
        return $this->belongsTo(ScreenOrder::class, 'screen_order_id');
    }

    public function design()
    {
        return $this->hasOne(ScreenDesign::class, 'id', 'design_id');
    }

    public function mockup()
    {
        return $this->hasOne(ScreenDesign::class, 'id', 'mockup_id');
    }
}
