<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenPackaging extends Model
{
    use HasFactory;

    protected $table = 'screen_packaging';

    protected $fillable = [
        'name',
        'screen_client_id',
        'order_type',
        'user_id'
    ];

    public function client()
    {
        return $this->belongsTo(ScreenClient::class, 'screen_client_id');
    }

    public function instructions()
    {
        return $this->hasMany(ScreenPackagingInstruction::class, 'screen_packaging_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function orderPackagings()
    {
        return $this->hasMany(ScreenOrderPackaging::class, 'screen_packaging_id', 'id');
    }
}
