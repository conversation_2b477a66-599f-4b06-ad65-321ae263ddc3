<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Oauth2Token extends Model
{
    use HasFactory;

    protected $table = 'oauth2_tokens';

    protected $fillable = [
        'platform',
        'token_type',
        'access_token',
        'refresh_token',
        'expires_in',
        'issued_at',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'issued_at' => 'datetime',
    ];
}
