<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SellbriteWarehouse extends Model
{
    use HasFactory;

    protected $table = 'sellbrite_warehouses';

    protected $fillable = [
        'uuid',
        'name',
        'warehouse_id',
    ];

    public function sellbriteSyncs()
    {
        return $this->hasMany(SellbriteSync::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }
}
