<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class Product extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'product';

    const NOT_DELETED = 0;

    const DELETED = 1;

    const PARENT_PRODUCT = 0;

    const PRODUCT_IMAGE_FOLDER = 'Product';

    const STYLE_MUGS = 'MUGS';

    const JOB_MAIL_WHOLESALE = 'mail-wholesale';

    const IN_STOCK = 1;

    const NOT_HIDE = 0;

    const SKU_ORMTALONE = 'ORMTALONE';

    const SKU_ORMRALONE = 'ORMRALONE';

    const SKU_PLQHTR150 = 'PLQHTR150';

    const SKU_PLQHTR125 = 'PLQHTR125';

    const TYPE_INSERT_PRINTING = 'Insert';

    const JOB_SEND_STOCK_NOTIFY = 'send-stock-notify';

    const JOB_SEND_FACILITY_NOTIFY = 'send-facility-notify';

    const SKU_DISCONTINUED = 'SKU Discontinued';

    const SKU_OUT_OF_STOCK = 'SKU Out of Stock';

    const SKU_BACK_IN_STOCK = 'SKU Back in Stock';

    const NEW_SKU_ADDED = 'New SKU Added';

    const STOCK_NOTIFY = 'stock_notify';

    const FACILITY_NOTIFY = 'facility_stock_notify';

    const STATUS_IN_STOCK = 'in_stock';

    const STATUS_OUT_OF_STOCK = 'out_of_stock';

    const STATUS_DISCONTINUED = 'discontinued';

    protected $fillable = [
        'name',
        'parent_id',
        'user_id',
        'title',
        'sku',
        'gtin',
        'gtin_case',
        'cost',
        'price',
        'size',
        'color',
        'style',
        'setting',
        'description',
        'tag',
        'in_stock',
        'is_deleted',
        'display_order',
        'brand_id',
        'image',
        'is_popular',
        'is_discontinued',
        'is_hide',
        'stock_status',
        'weight_single',
        'weight_multiple',
        'qb_ref',
        'is_on_demand',
        'discontinued_at',
    ];

    protected $casts = [
        'setting' => 'array',
        'is_popular' => 'boolean',
        'is_discontinued' => 'boolean',
        'is_hide' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function product_variants()
    {
        return $this->hasMany(Product::class, 'parent_id', 'id');
    }

    public function productParent()
    {
        return $this->belongsTo(Product::class, 'parent_id', 'id');
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where("$this->table.is_deleted", self::NOT_DELETED);
    }

    public function scopePrimary(Builder $query): Builder
    {
        return $query->where("$this->table.parent_id", '!=', self::PARENT_PRODUCT);
    }

    public function scopeParent(Builder $query): Builder
    {
        return $query->where("$this->table.parent_id", self::PARENT_PRODUCT);
    }

    public function location(): BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'box', 'product_id', 'location_id')
            ->wherePivot('quantity', '>', 0)
            ->wherePivot('is_deleted', 0)
            ->withPivot('quantity');
    }

    public function storeProducts(): HasMany
    {
        return $this->hasMany(StoreProduct::class);
    }

    public function productQuantities(): HasMany
    {
        return $this->hasMany(ProductQuantity::class);
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    public function barcodePrinted(): HasMany
    {
        return $this->hasMany(BarcodePrinted::class, 'product_id', 'id');
    }

    public function printingPresetSku()
    {
        return $this->hasOne(PrintingPresetSku::class, 'sku', 'sku');
    }

    public function style()
    {
        return $this->belongsTo(ProductStyle::class, 'style', 'name');
    }

    public function pdfConverted()
    {
        return $this->hasMany(PdfConverted::class, 'product_id', 'id');
    }

    public function presetSku()
    {
        return $this->hasOne(PrintingPresetSku::class, 'sku', 'sku');
    }

    public function productSkuMatching()
    {
        return $this->hasOne(ProductSkuMatching::class, 'swiftpod_sku', 'sku');
    }

    public static function findByIdAndPresetSku($id)
    {
        return self::with('printingPresetSku')
            ->whereHas('printingPresetSku')
            ->where('id', $id)
            ->first();
    }

    public static function findByIdAndPresetSkuUv($id)
    {
        return self::with([
            'printingPresetSku:id,sku,platen_size,front_size,back_size,background_color,platen_front_size',
            'uvPresetSku'
        ])
            ->whereHas('printingPresetSku')
            ->whereHas('uvPresetSku')
            ->where('id', $id)
            ->first();
    }

    public function uvPresetSku()
    {
        return $this->hasOne(UvPresetSku::class, 'product_sku', 'sku');
    }

    public function productStyle(): BelongsTo
    {
        return $this->belongsTo(ProductStyle::class, 'style', 'name');
    }

    public static function getMugProductId()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_MUGS);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get();
    }

    public static function getMugProductIds()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_MUGS);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get('id')->toArray();
    }

    public static function getProductIdByPrintMethod($printMethod)
    {
        return self::whereHas('style', function ($query) use ($printMethod) {
            $query->where('print_method', $printMethod);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get();
    }

    public function productColor(): BelongsTo
    {
        return $this->belongsTo(ProductColor::class, 'color', 'name');
    }

    public function productSize(): BelongsTo
    {
        return $this->belongsTo(ProductSize::class, 'size', 'name');
    }

    public function storeStyle()
    {
        return $this->hasOne(StoreStyle::class, 'style', 'style');
    }

    public static function getUVProductId()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_UV);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get();
    }

    public static function getUVProductIds()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_UV);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get('id')->toArray();
    }

    public static function getOrnamentProductId()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_UV)
                ->where('type', '<>', ProductType::STICKER);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get();
    }

    public static function getStickerProductId()
    {
        return self::whereHas('style', function ($query) {
            $query->where('print_method', ProductStyle::METHOD_UV)
                ->where('type', ProductType::STICKER);
        })
            ->where('parent_id', '!=', self::PARENT_PRODUCT)
            ->get();
    }

    public function children()
    {
        return $this->hasMany(Product::class, 'parent_id');
    }

    public function productPromotions()
    {
        return $this->hasMany(ProductPromotion::class);
    }

    public function scopeGtin($query, $keyword, $id = null)
    {
        $query->whereNotNull('gtin')
            ->where('gtin', '<>', '')
            ->where('is_deleted', Product::NOT_DELETED)
            ->where(function ($q) use ($keyword) {
                $q->whereRaw('COALESCE(INSTR(?, gtin)) > 0', [$keyword])
                    ->orWhereRaw('COALESCE(INSTR(gtin, ?)) > 0', [$keyword]);
            });
        if ($id) {
            $query->where('id', '<>', $id);
        }

        return $query;
    }

    public function storePromotions()
    {
        return $this->hasMany(StorePromotion::class);
    }

    public static function getProductIdsByType($type)
    {
        return self::whereHas('productStyle', function ($query) use ($type) {
            $query->where('type', $type);
        })->pluck('id');
    }

    public function productSpec()
    {
        return $this->hasOne(ProductSpec::class, 'product_id', 'id');
    }

    public function orderItems()
    {
        return $this->hasMany(SaleOrderItem::class, 'product_id', 'id');
    }

    public static function findParentByStyle($styleName)
    {
        return DB::table('product')
            ->where('style', $styleName)
            ->where('parent_id', Product::PARENT_PRODUCT)
            ->first();
    }

    public static function getSpecByProductSku($sku)
    {
        return self::whereHas('productSpec')
            ->where('sku', $sku)
            ->first();
    }

    public function printingLayoutTemplate($printMethod)
    {
        return $this->hasOne(PrintingLayoutTemplate::class, 'sku', 'sku')
            ->where('print_method', $printMethod);
    }

    public function productTiktok()
    {
        return $this->hasOne(ProductTiktok::class, 'product_id', 'id');
    }

    public function blankCostYear()
    {
        return $this->hasMany(ProductBlankCostYear::class, 'product_id', 'id');
    }

    public function ProductRbt()
    {
        return $this->belongsTo(RbtProduct::class, 'id', 'product_id');
    }

    public function internalRequests()
    {
        return $this->hasMany(InternalRequest::class, 'product_id', 'id');
    }

    public function rbtLocation()
    {
        return $this->belongsTo(Location::class, 'sku', 'rbt_sku');
    }
}
