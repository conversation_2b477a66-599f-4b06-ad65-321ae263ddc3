<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BarcodeStatus extends Model
{
    use HasFactory;

    protected $table = 'sale_order_item_barcode_status';

    protected $fillable = [
        'id',
        'label_id',
        'status',
        'sent_oos_email',
        'sent_reject_email',
        'created_at',
        'total_reject',
        'addition_cost',
        'last_qc_at',
        'updated_at',
    ];

    const DELETED = 1;

    const STATUS_QC_PASS = 'pass';

    public function listQcFailed()
    {
        return $this->hasMany(SaleOrderItemQualityControl::class, 'label_root_id', 'label_id')
            ->where('status', '!=', 'pass')   // Filter records where status is not 'pass'
            ->orderBy('created_at', 'asc');
    }
}
