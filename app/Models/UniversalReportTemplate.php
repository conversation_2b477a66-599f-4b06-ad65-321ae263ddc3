<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Schema;

class UniversalReportTemplate extends Model
{
    use HasFactory, SoftDeletes;

    public const STORAGE_FOLDER = 'universal_report';

    protected $table = 'universal_report_templates';

    protected $fillable = [
        'report_category_id',
        'status',
        'name',
        'description',
        'query',
    ];

    protected $casts = [
        'status' => 'boolean',
        'report_category_id' => 'integer',
    ];

    public function category()
    {
        return $this->belongsTo(UniversalReportCategory::class, 'report_category_id');
    }

    public function tags()
    {
        return $this->hasMany(UniversalReportTag::class, 'report_template_id');
    }

    public function columns()
    {
        return $this->hasMany(UniversalReportColumn::class, 'report_template_id');
    }

    public function settingDefault()
    {
        return $this->hasOne(UniversalReportSetting::class, 'report_template_id')
            ->whereNull('user_id')
            ->where('is_default', true);
    }

    public function settingCustom()
    {
        return $this->hasOne(UniversalReportSetting::class, 'report_template_id')
            ->where('user_id', auth()->id())
            ->where('is_default', false);
    }

    public function columnConfiguration()
    {
        $allColumns = [];
        foreach ($this->columns as $item) {
            if (empty($item->table)) {
                $allColumns[] = [
                    [
                        'original' => $item->getAttribute('original'),
                        'table' => null,
                        'column' => empty($item->alias) ? "`$item->column`.`$item->alias`" : "`$item->column`",
                        'alias' => empty($item->alias) ? "$item->column.$item->alias" : $item->column,
                    ]
                ];

                continue;
            }

            $allColumns[] = collect(Schema::getColumnListing($item->table))
                ->map(function ($schemaItem) use ($item) {
                    return [
                        'original' => $item->getAttribute('original'),
                        'table' => $item->table,
                        'column' => empty($item->alias) ? "`$item->table`.`$schemaItem`" : "`$item->alias`.`$schemaItem`",
                        'alias' => empty($item->alias) ? "$item->table.$schemaItem" : "$item->alias.$schemaItem",
                    ];
                })->toArray();
        }

        return collect($allColumns)->flatMap(function ($item) {
            return is_array($item) ? $item : [$item];
        })->values();
    }
}
