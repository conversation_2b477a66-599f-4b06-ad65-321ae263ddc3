<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentTransitReport extends Model
{
    use HasFactory;

    protected $table = 'shipment_transit_report';
    protected $fillable = [
        'shipment_id',
        'order_id',
        'warehouse_id',
        'start_time',
        'in_transit_at',
        'delivered_at',
        'transit_day',
        'fulfillment_day',
        'calculated_at',
        'shipment_service',
        'shipment_carrier',
        'destination_id',
    ];

    public function shipment()
    {
        return $this->belongsTo(Shipment::class, 'shipment_id');
    }

}
