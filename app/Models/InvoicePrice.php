<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoicePrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'amount',
        'type',
    ];

    const TYPE_GENERAL = 'general';

    const TYPE_PRODUCTION = 'production';

    const TYPE_SHIPPING = 'shipping';

    const TYPE_INSERT = 'insert';

    const TYPE_SURCHARGE = 'surcharge';

    const TYPE_PROMOTION = 'promotion';
}
