<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtCountStickerPrinted extends Model
{
    use HasFactory;

    protected $table = 'rbt_count_sticker_printed';

    protected $fillable = [
        'warehouse_id',
        'print_status',
        'convert_status',
        'user_id',
        'converted_at',
        'convert_percent',
        'first_barcode',
        'last_barcode',
        'url',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
