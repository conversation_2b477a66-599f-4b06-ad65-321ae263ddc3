<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletTransaction extends Model
{
    use HasFactory;

    // direction out -
    const TRANSACTION_TYPE_PAYMENT = 'payment';

    // direction in +
    const TRANSACTION_TYPE_TOPUP = 'topup';

    const TRANSACTION_TYPE_REFUND = 'refund';

    const TRANSACTION_TYPE_ADJUSTMENT = 'adjustment';

    const DIRECTION_IN = 'in';

    const DIRECTION_OUT = 'out';

    protected $fillable = [
        'store_id',
        'type',
        'amount',
        'direction',
        'new_balance',
        'object_type',
        'object_id',
        'object_number',
        'description',
        'note',
    ];

    public function receipts()
    {
        return $this->hasMany(WalletReceipt::class, 'transaction_id', 'id');
    }
}
