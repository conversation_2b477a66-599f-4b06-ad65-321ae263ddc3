<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\Request;

class SaleOrderItem extends Model
{
    use HasFactory;

    const PRINT_SIDE_FRONT = 0;

    const PRINT_SIDE_BACK = 1;

    protected $table = 'sale_order_item';

    protected $fillable = [
        'order_id',
        'ink_color',
        'ink_color_status',
        'external_id',
        'external_line_item_key',
        'sku',
        'name',
        'image_url',
        'weight',
        'quantity',
        'unit_price',
        'tax_amount',
        'shipping_amount',
        'options',
        'product_id',
        'product_sku',
        'product_style_sku',
        'product_color_sku',
        'product_size_sku',
        'barcode_item_status',
        'print_side',
        'print_sides',
        'image_convert_status',
        'is_deleted',
        'pdf_converted_id',
        'convert_pdf_status',
        'amount_paid',
        'promotion_id',
        'blank_price',
        'handling_fee',
        'store_id',
        'ink_color_detected_at',
        'is_rbt',
    ];

    protected $casts = [
        'print_side_code' => 'array'
    ];

    const DETECT_INK_COLOR_DONE = 1;

    const DETECT_INK_COLOR_FAIL = 2;

    const COLOR_BLACK = 1;

    const COLOR_WHITE_OR_MIX = 0;

    const INACTIVE = 0;

    const IS_DELETED = 1;

    const IS_NOT_RBT = 0;

    const IS_RBT = 1;

    const MISSING_PRINT_METHOD = 'missing_print_method';

    const DOWNLOAD_ERROR = 'download_error';

    const DETECT_COLOR_ERROR = 'detect_color_error';

    const UPLOAD_S3_ERROR = 'upload_s3_error';

    public const ACTIVE = 1;

    public const PRINT_FILES = 'PrintFiles';

    public const PREVIEW_FILES = 'PreviewFiles';

    public const PRODUCTION_CONFIG_LARGE = 'large';

    public function order(): BelongsTo
    {
        return $this->belongsTo(SaleOrder::class);
    }

    public function barcodes(): HasMany
    {
        return $this->hasMany(SaleOrderItemBarcode::class, 'order_item_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function productColor(): BelongsTo
    {
        return $this->belongsTo(ProductColor::class, 'product_color_sku', 'sku');
    }

    public function productStyle(): BelongsTo
    {
        return $this->belongsTo(ProductStyle::class, 'product_style_sku', 'sku');
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        $query->whereHas('order', function ($q) {
            $q->where('warehouse_id', config('jwt.warehouse_id'));
        });

        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        }

        if (!empty($request['sku'])) {
            $query->where("$this->table.sku", 'LIKE', '%' . $request['sku'] . '%');
        }

        if (!empty($request['product_sku'])) {
            $query->where("$this->table.product_sku", 'LIKE', '%' . $request['product_sku'] . '%');
        }

        if (!empty($request['order_date_start']) && !empty($request['order_date_end'])) {
            $query->whereHas('order', function ($q) use ($request) {
                $q->whereBetween('order_date', [$request['order_date_start'], $request['order_date_end']]);
            });
        }

        if (!empty($request['store_id'])) {
            $query->whereHas('order', function ($q) use ($request) {
                $q->where('store_id', $request['store_id']);
            });
        }

        return $query;
    }

    public static function updateInkColor($id, $ink_color, $detectStatus)
    {
        return self::where('id', $id)->update([
            'ink_color' => $ink_color,
            'ink_color_status' => $detectStatus
        ]);
    }

    public function shipmentItem(): HasMany
    {
        return $this->hasMany(ShipmentItem::class, 'order_item_id', 'id');
    }

    public function getTypeProduct(): HasOne
    {
        return $this->hasOne(ProductStyle::class, 'sku', 'product_style_sku');
    }

    public function images(): HasMany
    {
        return $this->hasMany(SaleOrderItemImage::class, 'order_item_id', 'id');
    }

    public function printingPresetSku()
    {
        return $this->hasOne(PrintingPresetSku::class, 'sku', 'product_sku');
    }

    public function lastCreateDtf()
    {
        return $this->hasOne(BarcodePrinted::class, 'style_sku', 'product_style_sku')
            ->where('warehouse_id', config('jwt.warehouse_id'))->orderByDesc('id');
    }

    public static function getMugProductId($warehouse_id)
    {
        return self::whereHas('order', function ($query) use ($warehouse_id) {
            $query->where('warehouse_id', $warehouse_id)
                ->whereDate('order_date', '>=', env('DATE_START_CONVERT_MUG', '2022-01-01'));
        })
            ->whereHas('product', function ($query) {
                $query->where('parent_id', '!=', Product::PARENT_PRODUCT);
            })
            ->where('product_style_sku', 'LIKE', '%' . Product::STYLE_MUGS . '%')
            ->whereNotNull('product_id')
            ->pluck('product_id')
            ->unique()
            ->toArray();
    }

    public function productSize()
    {
        return $this->belongsTo(ProductSize::class, 'product_size_sku', 'sku');
    }

    public function storeProducts()
    {
        return $this->hasManyThrough(StoreProduct::class, Product::class, 'id', 'product_id', 'product_id', 'id');
    }

    public function barcodePrintedTime()
    {
        return $this->hasOne(BarcodePrintedTime::class, 'style_sku', 'product_style_sku')
            ->where('warehouse_id', config('jwt.warehouse_id'));
    }

    public function promotion()
    {
        return $this->belongsTo(Promotion::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function invoiceErrors(): HasMany
    {
        return $this->hasMany(SaleOrderItemCalculateFailed::class, 'order_item_id', 'id');
    }

    public static function getIDBeforeDays($days = 2)
    {
        // Find sale order item id before 2 days
        $result = self::selectRaw('MIN(id) as id')
            ->where('created_at', '>', now()->subDays($days))
            ->where('id', '>', env('SKIP_ORDER_ITEM_ID', 6934193))
            ->first();

        if ($result) {
            return $result->id;
        }

        return env('SKIP_ORDER_ITEM_ID', 6934193);
    }

    public function itemErrors(): HasMany
    {
        return $this->hasMany(SaleOrderItemError::class, 'order_item_id', 'id');
    }

    public function surchargeFee(): HasMany
    {
        return $this->hasMany(SaleOrderItemSurchargeFee::class, 'order_item_id', 'id');
    }

    public function productImages()
    {
        return $this->hasMany(SaleOrderItemProductImage::class, 'order_item_id', 'id');
    }

    public function emailCustomers()
    {
        return $this->hasMany(SaleOrderCustomerEmail::class, 'order_id', 'order_id');
    }

    public function embroideryTask(): HasMany
    {
        return $this->hasMany(EmbroideryTask::class, 'order_item_id', 'id');
    }

    public function licensedDesign()
    {
        return $this->belongsTo(LicensedDesign::class, 'licensed_design_id', 'licensed_design_id');
    }
}
