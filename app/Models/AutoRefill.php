<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AutoRefill extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'status',
        'payment_gateway',
        'amount',
        'limit',
        'reason',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'status' => 'boolean',
        'reason' => 'array',
    ];

    public function autoRefill()
    {
        return $this->hasOne(AutoRefill::class);
    }
}
