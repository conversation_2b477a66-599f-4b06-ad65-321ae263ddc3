<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class ProductTypeWeight extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $table = 'product_type_weight';

    protected $fillable = [
        'name',
        'size',
        'weight',
        'weight_unit',
        'weight_oz'
    ];

    const LB_OZ = 15.99;

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        };

        if (!empty($request['size'])) {
            $query->where("$this->table.size", 'LIKE', '%' . $request['size'] . '%');
        };

        if (!empty($request['weight_unit'])) {
            $query->where("$this->table.weight_unit", $request['weight_unit']);
        };

        return $query;
    }
}
