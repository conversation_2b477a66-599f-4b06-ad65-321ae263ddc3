<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, HasRoles;

    protected $table = 'user';

    const ADMIN = 1;

    const SYSTEM = 0;

    const IS_ACTIVE = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'seller_password',
        'is_admin',
        'department_id',
        //        'store_ids',
        'is_all_warehouse',
        'expired_at',
        'is_lavender_editor',
        'is_bypass_restriction',
        'is_all_store',
        'change_app_password_status',
        'change_seller_password_status',
        'app_send_to',
        'seller_send_to',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'seller_password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $appends = ['store_ids'];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function toArray()
    {
        $array = parent::toArray();
        $array['store_ids'] = $this->pivotStores()->pluck('store_id')->toArray();

        return $array;
    }

    public function getStoreIdsAttribute()
    {
        return $this->pivotStores()->pluck('store_id')->toArray();
    }

    public function warehouses()
    {
        return $this->belongsToMany(Warehouse::class,
            'user_warehouse', 'user_id', 'warehouse_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function departmentDefault()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function latestAccessLog()
    {
        return $this->hasOne(UserAccessLog::class, 'user_id')->latest('last_active_at');
    }

    public function stores()
    {
        return $this->belongsToMany(Store::class, 'user_stores', 'user_id', 'store_id');
    }

    public function pivotStores()
    {
        return $this->hasMany(UserStore::class, 'user_id');
    }
}
