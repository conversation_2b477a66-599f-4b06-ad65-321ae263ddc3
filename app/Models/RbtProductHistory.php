<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtProductHistory extends Model
{
    use HasFactory;

    protected $table = 'product_tiktok_history';

    protected $fillable = [
        'product_id',
        'user_id',
        'is_active',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
