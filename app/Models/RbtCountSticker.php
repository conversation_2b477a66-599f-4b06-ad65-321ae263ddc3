<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtCountSticker extends Model
{
    use HasFactory;

    const STICKER_PATH = 'rbt_count_stickers';

    const CONVERT_PDF_JOB = 'convert_rbt_count_sticker_pdf';

    protected $table = 'rbt_count_stickers';

    protected $fillable = [
        'warehouse_id',
        'rbt_count_sticker_printed_id',
        'convert_pdf_status',
        'retry_convert',
        'user_id',
        'barcode',
        'label'
    ];
}
