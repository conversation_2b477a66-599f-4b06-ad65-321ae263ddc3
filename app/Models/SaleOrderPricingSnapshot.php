<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderPricingSnapshot extends Model
{
    use HasFactory;

    protected $table = 'sale_order_pricing_snapshot';

    protected $fillable = [
        'order_id',
        'snapshot',
    ];

    protected $casts = [
        'snapshot' => 'array',
    ];

    public $timestamps = true;

    public function order()
    {
        return $this->belongsTo(SaleOrder::class, 'order_id');
    }
}
