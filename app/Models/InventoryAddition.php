<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryAddition extends Model
{
    public $timestamps = false;

    protected $table = 'inventory_addition';

    protected $fillable = [
        'vendor_id',
        'po_id',
        'po_number',
        'invoice_number',
        'location_id',
        'gtin',
        'quantity',
        'product_id',
        'box_id',
        'deleted_at',
        'user_id',
        'warehouse_id',
        'tracking_number',
        'employee_id',
        'country',
        'cost_value',
    ];

    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country', 'iso2');
    }
}
