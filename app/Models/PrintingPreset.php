<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrintingPreset extends Model
{
    use HasFactory;

    protected $table = 'printing_preset';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'data',
        'priority',
        'user_id'
    ];

    protected $appends = [
        'is_active_time',
        'time'
    ];

    public function getIsActiveTimeAttribute()
    {
        $data = str_replace('xmlns', ' xmlns', $this->data);
        $fileChange = str_replace(["\n", "\r", "\t"], '', $data);
        $fileTrim = trim(str_replace('"', "'", $fileChange));
        $fileTrim = str_replace('&', '&amp;', $fileTrim);
        $resultXml = simplexml_load_string($fileTrim);

        return $resultXml->bPause == 'true';
    }

    public function getTimeAttribute()
    {
        $data = str_replace('xmlns', ' xmlns', $this->data);
        $fileChange = str_replace(["\n", "\r", "\t"], '', $data);
        $fileTrim = trim(str_replace('"', "'", $fileChange));
        $fileTrim = str_replace('&', '&amp;', $fileTrim);
        $resultXml = simplexml_load_string($fileTrim);

        return (int) $resultXml->byPauseSpan ?? 0;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function convertPresetForRedBubble($data)
    {
        $presets = [
            'FALLPURPLEQCBKB' => 'FALLPURPLEXQCDL',
            'TALLPURPLEQCBKB' => 'TALLPURPLEXQCDL',
            'TDALL1200BKB' => 'TDALL1200DL',
            'FALL1200BKB' => 'FALL1200DL',
            'ALL1200BKB' => 'ALL1200DB',
        ];

        return $presets[$data] ?? $data;
    }

    public static function convertPresetDtgColorForTee($color)
    {
        return match ($color) {
            'WHITE' => 'TEEWHITEDTG',
            'BLACK' => 'TEEBLACKDTG',
            default => 'TEECOLORDTG',
        };
    }

    public static function convertPresetDtgColorForFleece($color)
    {
        return match ($color) {
            'WHITE' => 'FLEECEWHITEDTG',
            'BLACK' => 'FLEECEBLACKDTG',
            default => 'FLEECECOLORDTG',
        };
    }
}
