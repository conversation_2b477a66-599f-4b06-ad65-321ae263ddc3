<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RbtSkuMovement extends Model
{
    use HasFactory;

    protected $table = 'rbt_sku_movement';

    protected $fillable = [
        'employee_id',
        'status',
        'link_url',
        'link_url_error',
        'total_add',
        'total_remove',
        'total_change',
    ];

    const LIMIT = 25;

    const STATUS_COMPLETED = 'completed';

    const STATUS_NEW = 'new';

    const STATUS_IN_PROGRESS = 'in_progress';

    public function details()
    {
        return $this->hasMany(RbtSkuMovementDetail::class, 'rbt_sku_movement_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'employee_id');
    }
}
