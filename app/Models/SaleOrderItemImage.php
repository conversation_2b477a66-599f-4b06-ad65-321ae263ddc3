<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderItemImage extends Model
{
    public $timestamps = false;

    use HasFactory;

    protected $table = 'sale_order_item_image';

    const ACTIVE = 0;

    const INACTIVE = 1;

    const SYNC_S3_NOT_YET = 0;

    const SYNC_S3_SUCCESS = 1;

    const SYNC_S3_FAIL = 2;

    const SYNC_S3_REMOVED = 3;

    const COLOR_BLACK = 1;

    const COLOR_WHITE = 0;

    const COLOR_MIX = 2;

    const COLOR_ERROR = 3;

    const IS_DELETED = 1;

    const NO_RESIZE = 1;

    const IS_PURPLE = 1;

    const IS_OG = 1;

    const EMB_EXTENSION_FILE_LOWER_CASE = '.dst';

    const EMB_EXTENSION_FILE_UPPER_CASE = '.DST';

    const VALID_EMB_EXTENSIONS = [
        '.dst',
        '.DST',
        '.ct0',
        '.CT0',
        '.tbf',
        '.TBF',
        '.dgt',
        '.DGT',
        '.EMB',
        '.emb',
        '.pxf',
        '.PXF',
    ];

    const NO_BYPASS_EMB_EXTENSIONS = ['.dst', '.DST'];

    const IMAGE_FOLDER = 'order-item-image';

    public const FOLDER_UPLOAD = 'sale-order-item-image/';

    protected $fillable = [
        'color_new',
        'manual_color',
        'ink_color_cc',
        'ink_white_cc',
        'worker',
        'warehouse_id',
        'image_hash_id',
        'image_size',
        'upload_s3_status',
        'is_purple',
        'print_side',
        'product_sku',
        'order_item_id',
        'order_id',
        'store_id',
        'account_id',
        'image_url',
        'image_ext',
        'no_resize',
        'thumb_250',
        'thumb_750',
        'skip_retry',
        'retry_download_manual_count',
        'retry_detect_color_count',
        'last_retry_detect_color',
        'retry_count',
        'is_purple',
    ];

    public function imageHash()
    {
        return $this->belongsTo(ImageHash::class, 'image_hash_id', 'id');
    }

    public static function getByOrderItemId($order_item_id)
    {
        return self::where('order_item_id', $order_item_id)->get();
    }

    public function orderItem()
    {
        return $this->belongsTo(SaleOrderItem::class, 'order_item_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(SaleOrder::class, 'order_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function account()
    {
        return $this->belongsTo(SaleOrderAccount::class, 'account_id');
    }

    public function printSizeType()
    {
        return $this->belongsTo(ProductPrintSide::class, 'print_side', 'code');
    }

    public function uploadLogs()
    {
        return $this->hasMany(SaleOrderItemImageUpload::class, 'order_item_image_id');
    }

    public function printSide()
    {
        return $this->belongsTo(ProductPrintSide::class, 'print_side', 'code');
    }

    public function printingPresetSku()
    {
        return $this->belongsTo(PrintingPresetSku::class, 'product_sku', 'sku');
    }

    public function barcode()
    {
        return $this->belongsTo(SaleOrderItemBarcode::class, 'sku', 'sku');
    }

    public static function findByOrderItemAndSide($order_item_id, $side)
    {
        return self::where('order_item_id', $order_item_id)
            ->where('print_side', $side)
            ->first();
    }

    public static function getSkuByPdfConvertedId($pdf_converted_id)
    {
        return self::where('pdf_converted_id', $pdf_converted_id)->pluck('sku')->toArray();
    }

    public static function getSkuByIds($ids)
    {
        return self::whereIn('id', $ids)->pluck('sku')->toArray();
    }

    public static function findByIdAndSide($id, $side)
    {
        return self::where('id', $id)->where('print_side', $side)->first();
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_sku', 'sku');
    }

    public function visuaDetect()
    {
        return $this->hasOne(VisuaDetectImage::class, 'image_id', 'id');
    }

    public function trademarks()
    {
        return $this->hasManyThrough(
            Trademark::class,
            TrademarkImage::class,
            'image_hash_id',
            'id',
            'image_hash_id',
            'trademark_id'
        );
    }

    public function trademarkImages()
    {
        return $this->hasMany(TrademarkImage::class, 'image_hash_id', 'image_hash_id');
    }

    public function tags()
    {
        return $this->hasMany(SaleOrderItemImageTag::class, 'sale_order_item_image_id', 'id');
    }
}
