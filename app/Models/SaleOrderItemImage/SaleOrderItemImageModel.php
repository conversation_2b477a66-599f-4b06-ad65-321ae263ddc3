<?php

namespace App\Models\SaleOrderItemImage;

use Illuminate\Support\Facades\DB;

class SaleOrderItemImageModel
{
    const TABLE_NAME = 'sale_order_item_image';

    const ITEM_OF_PAGE = 20;

    const DELETE_STATUS = 0;

    const DELETE_STATUS_DELETE = 1;

    const DELETE_STATUS_DELETE_SUCCESS = 2;

    /**
     * @return int
     */
    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insert($rawData);
    }

    public function getItemImageError()
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('download_status', [2, 3])
            ->pluck('id');
    }

    public function getAll($rawData)
    {
        $query = DB::table(self::TABLE_NAME)
            ->select('sale_order_item_image.*', 'sale_order.external_number as external_id', 'sale_order.order_number as order_number')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id');

        if (isset($rawData['sku']) && $rawData['sku']) {
            $query->where('sale_order_item_image.sku', $rawData['sku']);
        }
        if (isset($rawData['orderId']) && $rawData['orderId']) {
            $query->where('sale_order.external_id', (int) $rawData['orderId']);
        }
        if (isset($rawData['color']) && $rawData['color'] != -1) {
            $query->where('sale_order_item_image.color_new', (int) $rawData['color']);
        }
        if (isset($rawData['status']) && $rawData['status'] != -1) {
            if ($rawData['status'] == 0) {
                $query->where('sale_order_item_image.download_status', (int) $rawData['status']);
            }
            if ($rawData['status'] == 1) {
                $query->where('sale_order_item_image.download_status', (int) $rawData['status']);
            }
            if ($rawData['status'] == 2) {
                $query->whereIn('sale_order_item_image.download_status', [2, 3]);
            }
        }
        if (isset($rawData['externalNumber']) && $rawData['externalNumber']) {
            $query->where('sale_order.external_number', $rawData['externalNumber']);
        }
        if (isset($rawData['deleteStatus']) && $rawData['deleteStatus'] != -1) {
            if ($rawData['deleteStatus'] == 0) {
                $query->where('sale_order_item_image.delete_status', (int) $rawData['deleteStatus']);
            }

            if ($rawData['deleteStatus'] == 1) {
                $query->whereIn('sale_order_item_image.delete_status', [1, 2]);
            }
        }
        if (isset($rawData['warehouse']) && $rawData['warehouse'] != -1) {
            $query->where('sale_order_item_image.warehouse_id', (int) $rawData['warehouse']);
        }
        if (isset($rawData['date']) && $rawData['date']) {
            $query->whereBetween('sale_order_item_image.order_date', $rawData['date']);
        }
        $itemOfPage = self::ITEM_OF_PAGE;
        if (isset($rawData['itemOfPage']) && $rawData['itemOfPage']) {
            $itemOfPage = $rawData['itemOfPage'];
        }

        return count($query->wheres) > 0 ? $query->orderBy('sale_order.id', 'DESC')->paginate($itemOfPage)
            : $query->optimizePaginate($itemOfPage, ['*'], 'page', null, DB::table(self::TABLE_NAME)->count());
    }

    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawData);
    }

    public function getItemByOrderId($orderId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $orderId)
            ->get();
    }

    public function findBySku($sku)
    {
        return DB::table(self::TABLE_NAME)
            ->where('sku', $sku)
            ->first();
    }

    public function getItemById($id)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->first();
    }

    public function findItemByOrderSkuFront($orderId, $sku, $front)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $orderId)
            ->where('sku', $sku)
            ->where('print_side', $front)
            ->first();
    }

    public function findOrderItemImageByLinkAndOrderItemId($link, $orderItemId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('image_url', $link)
            ->where('order_item_id', $orderItemId)
            ->count();
    }

    public function getItemFirst()
    {
        return DB::table(self::TABLE_NAME)
            ->where('download_status', 0)
            // ->where('image_width', '<>', 0)
            ->whereNotNull('image_width')
//            ->where('id', 32)
            ->orderBy('id', 'DESC')
            ->first();
    }

    public function checkItemAccess()
    {
    }



    public function updateByListSku($listSku, $rawDataUpdate)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('sku', $listSku)
            ->update($rawDataUpdate);
    }

    public function updateByListOrderId($listOrderId, $rawDataUpdate)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('order_id', $listOrderId)
            ->update($rawDataUpdate);
    }

    public function updateByOrderId($orderId, $rawDataUpdate)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $orderId)
            ->update($rawDataUpdate);
    }



    public function getItemBySku($sku)
    {
        return DB::table(self::TABLE_NAME)
            ->where('sku', $sku)
            ->get();
    }

    public function updateItemById($id, $rawDataUpdate)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawDataUpdate);
    }

    public function randomCheck($color = '', $sku = '')
    {
        $st = DB::table(self::TABLE_NAME)
            ->select('sale_order_item_image.*');
        if ($color !== '') {
            $st->where('color_new', $color);
        } else {
            $st->whereNotNull('color_new');
        }
        $st->whereDate('created_at', '>', date('Y-m-d', time() - 3600 * 24 * 15));
        if ($sku != '') {
            $skus = explode("\n", $sku);
            $skus = array_map('trim', $skus);
            $st->whereIn('sku', $skus);
        } else {
            $st->limit(50);
        }

        $st->orderBy(DB::raw('RAND()'));

        return $st->get();
    }

    public function getStatusConvertSkuAndColorAndSide($sku)
    {
        $items = DB::table(self::TABLE_NAME)
            ->select(['color_new', 'convert_status', 'is_double_side', 'print_side'])
            ->where('sku', $sku)->get();
        if (count($items) == 0) {
            return false;
        }
        $ink_color = 1;
        $convert_status = 1;
        $print_side = 0;
        foreach ($items as $item) {
            if ($item->color_new != 1) {
                $ink_color = 0;
            }
            if ($item->convert_status != 1) {
                $convert_status = 0;
            }
            if ($item->is_double_side == 1) {
                $print_side = 2;
            } elseif ($item->print_side == 0 || $item->print_side == 2) {
                $print_side = 0;
            } elseif ($item->print_side == 1) {
                $print_side = 1;
            }
        }

        return ['image_convert_status' => $convert_status, 'print_side' => $print_side, 'ink_color' => $ink_color];
    }

    public function updateByListId($listId, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->update($rawData);
    }
}
