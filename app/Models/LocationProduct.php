<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocationProduct extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $table = 'location_product';

    protected $fillable = [
        'location_id',
        'product_id',
        'quantity',
        'created_at',
        'updated_at',
    ];

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }
}
