<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\Request;

class SaleOrder extends Model
{
    use HasFactory;

    protected $table = 'sale_order';

    protected $fillable = [
        'warehouse_id',
        'store_id',
        'customer_note',
        'shipping_method',
        'is_eps',
        'is_xqc',
        'cancelled_at',
        'rejected_at',
        'rejected_reason',
        'calculated_at',
        'external_key',
        'employee_manual_id',
        'is_manual',
        'manual_process_at',
        'external_number',
        'external_id',
        'shipping_calculate',
        'order_status',
        'order_date',
        'is_test',
        'tag',
        'shipment_id',
        'is_fba_order',
        'fba_shipping_label',
        'fba_item_label',
        'is_create_manual',
        'ioss_number',
        'encode_id',
        'order_pulled_status',
        'order_pulled_at',
        'tax_id_type',
        'order_quantity',
        'production_status',
        'is_error',
        'plastic_bag',
        'order_production_at',
        'order_folding_at',
        'order_folding_status',
        'is_rbt',
        'payment_status',
        'paid_at',
        'order_type'
    ];

    const ACTIVE = 1;

    const INACTIVE = 0;

    const NOT_TEST = 0;

    const IS_MANUAL = 1;

    const IS_NOT_MANUAL = 0;

    const SHIPSTATION = 'shipstation';

    const API = 'api';

    const PRODUCTION_STATUS = [
        'labelled' => 'order_shipping_status',
        'folded' => 'order_folding_status',
        'staged' => 'order_staged_status',
        'qc' => 'order_qc_status',
        'printed' => 'barcode_printed_status',
        'pretreated' => 'order_pretreated_status',
        'pulled' => 'order_pulled_status'
    ];

    const JOB_NOTIFY_STATUS_ORDER = 'update-status';

    const JOB_NOTIFY_MULTIPLE_STATUS_ORDER = 'multiple-update-status';

    const JOB_NOTIFY_TRACKING_SHIPMENT = 'update-tracking';

    const JOB_NOTIFY_MULTIPLE_TRACKING_SHIPMENT = 'multiple-update-tracking';

    const JOB_UPDATE_PRODUCTION_STATUS = 'update-production-status';

    const JOB_SEND_ORDER_ITEM_PRINTED_TO_REDBUBBLE = 'send-order-item-printed-to-redbubble';

    const JOB_DETECT_SHIPPING_METHOD_REDBUBBLE = 'detect-shipping-method-redbubble';

    const JOB_SEND_MAIL_ALERT_OOS = 'sent-mail-qc-oos';

    const JOB_SEND_MAIL_ALERT_FAILED_TIMES = 'sent-mail-qc-warning';

    const SHIPPED = 'shipped';

    const NEW_ORDER = 'new_order';

    const CANCELLED = 'cancelled';

    const DRAFT = 'draft';

    const REJECTED = 'rejected';

    const COMPLETED = 'completed';

    const ON_HOLD = 'on_hold';

    const IN_PRODUCTION = 'in_production';

    const MANUAL_PROCESS = 'manual_process';

    const STATUS_NEW_ORDER = 'new_order';

    const STATUS_CANCELLED = 'cancelled';

    const STATUS_LATE_CANCELLED = 'in_production_cancelled';

    const STATUS_MANUAL_PROCESS = 'manual_process';

    const STATUS_SHIPPED = 'shipped';

    const STATUS_IN_PRODUCTION_CANCELLED = 'in_production_cancelled';

    const STATUS_IN_PRODUCTION = 'in_production';

    const STATUS_ON_HOLD = 'on_hold';

    const STATUS_REJECT = 'rejected';

    const ARRAY_STATUS_INACTIVE = [
        self::STATUS_CANCELLED,
        self::STATUS_ON_HOLD,
        self::STATUS_REJECT,
        self::DRAFT,
        self::STATUS_LATE_CANCELLED,
    ];

    const BARCODE_NOT_PRINTED = 0;

    const BARCODE_PRINTED = 1;

    const ALL_ITEM_BARCODE_FOLDING = 1;

    const FILTER_TAB = [
        'late_production' => 'late_production',
        'late_shipment' => 'late_shipment',
        'late_deduction' => 'late_deduction',
        'invalid_address' => 'invalid_address',
    ];

    const IS_SHIPMENT_CREATE_ERROR = 1;

    const ORDER_NOT_PRINTED = 0;

    const ORDER_NOT_WIP_PRINTED = 0;

    const ORDER_NOT_PULLED = 0;

    const ORDER_NOT_STAGED = 0;

    const ORDER_NOT_QC = 0;

    const JOB_ERROR_EXCEPTION_API_SEND_MESS_SLACK = 'exception-mess-slack';

    const WEIGHT_FOR_FIRST_CLASS = 16; // < 16 is first

    const PHONE_DEFAULT = '************';

    const SHIPPING_METHOD_EXPRESS = 'express';

    const SHIPPING_METHOD_STANDARD = 'standard';

    const SHIPPING_METHOD_PRIORITY = 'priority';

    const SHIPPING_METHOD_ECONOMY = 'economy';

    const SHIPPING_METHOD_FIRST_CLASS = 'letter_first_class';

    const ORDER_TYPE_NORMAL = 1;

    const ORDER_TYPE_PRETREATED = 2;

    const ORDER_TYPE_BLANK = 3;

    const ORDER_TYPE_LABEL_ORDER = 5;

    const ORDER_TYPE_TIKTOK_ORDER = 6;

    const ORDER_TYPE_LICENSE_ORDER = 7;

    const ACCEPTED_CSV_IMPORTING_ORDER_TYPES = [
        SaleOrder::ORDER_TYPE_NORMAL,
        SaleOrder::ORDER_TYPE_LABEL_ORDER,
        SaleOrder::ORDER_TYPE_TIKTOK_ORDER
    ];

    const INVALID_CSV_IMPORTING_ORDER_TYPES = -1;

    const ARRAY_ORDER_TYPE = [
        self::ORDER_TYPE_PRETREATED => 'PRETREATED',
        self::ORDER_TYPE_BLANK => 'BLANK',
    ];

    const ARRAY_SHIPPING_METHOD_DEFAULT = [
        self::SHIPPING_METHOD_EXPRESS,
        self::SHIPPING_METHOD_STANDARD,
        self::SHIPPING_METHOD_PRIORITY,
        self::SHIPPING_METHOD_ECONOMY,
        self::SHIPPING_METHOD_FIRST_CLASS,
    ];

    const PACKING_SLIP = 'packing_slip';

    const THANKYOU_CARD = 'thankyou_card';

    const TAX_ID_TYPE_IOSS = 'IOSS';

    const REPRINT = 'reprint';

    const ORDER_ID_START_GET_ORDER_CREATE_AUTO_LABEL = 5629058;

    const SWIFT_POD_PATH = 'api/sale-order/report-by-day';

    const IP_VIOLATION_REJECTED_REASON = 'ip_violation';

    const UNABLE_TO_BE_DIGITIZED_REJECTED_REASON = 'unable_to_be_digitized';

    const STATUS_ORDER_REDBUBBLE_PICKED = 'picked';

    const STATUS_ORDER_REDBUBBLE_PRINTED = 'printed';

    const STATUS_ORDER_REDBUBBLE_SHIPPED = 'shipped';

    const STATUS_ORDER_REDBUBBLE_DELAYED = 'delayed';

    const STATUS_ORDER_REDBUBBLE_ERROR = 'error';

    const STATUS_ORDER_REDBUBBLE_INFO = 'info';

    const REJECT_INVALID_DOWNLOAD_URL = 'invalid_label_url';

    const JOB_DETECT_SHIPPING_METHOD_REDBUBBLE_V2 = 'detect-shipping-method-redbubble-carrier-service-shipping';

    const INVALID_ARTWORK_REJECTED_REASON = 'invalid_artwork';

    const INVALID_ADDRESS_REJECTED_REASON = 'invalid_address';

    const TONE_ON_TONE_REJECTED_REASON = 'tone_on_tone';

    const OOS_REJECTED_REASON = 'out_of_stock';

    const CUSTOMER_CHANGED_MIND_REJECTED_REASON = 'customer_changed_mind';

    const MANUAL_ORDER_FOLDER = 'manual_order';

    const ACCEPTED_TAX_TYPES = ['CNP', 'DAN', 'DTF', 'DUN', 'EIN', 'EOR', 'EORI', 'FED', 'FTZ', 'GST', 'HMRC', 'IOSS', 'LVG', 'MRN', 'OTHER', 'PAN', 'PID', 'SDT', 'SSN', 'STA', 'TAN', 'TIN', 'VAT', 'VOEC'];

    const STORE_RBT = 484229;

    const ORDER_SOURCE_ORDERDESK = 'Orderdesk';

    const ORDER_SOURCE_CSV = 'CSV';

    const JOB_REFUND_ORDER = 'refund-order';

    const PAYMENT_STATUS_PENDING = 'pending';

    const PAYMENT_STATUS_PAID = 'paid';

    const PAYMENT_STATUS_REFUNDED = 'refunded';

    const PAYMENT_STATUS_PARTIAL_REFUNDED = 'partial_refunded';

    const QUEUE_UPDATE_SALE_ORDER_SLA_STATUS = 'sale-order-sla-status';

    const QUEUE_PRINTIFY_SEND_NOTIFY_TRACKING = 'printify-send-notify-tracking';

    const JOB_STORE_PRICING_SNAPSHOT = 'queue-store-sale-order-pricing-snapshot';

    const LIST_ORDER_TYPE = [
        '1' => 'normal order',
        '2' => 'pretreated order',
        '3' => 'blank order',
        '5' => 'label order',
        '6' => 'tiktok order',
        '7' => 'license order',
    ];

    const LABEL_TAG_ID = 203;

    public static function statusCallbackWebhook()
    {
        return [self::STATUS_SHIPPED, self::STATUS_IN_PRODUCTION, self::STATUS_CANCELLED, self::STATUS_REJECT, self::STATUS_IN_PRODUCTION_CANCELLED, self::STATUS_ON_HOLD, self::NEW_ORDER];
    }

    protected static function booted()
    {
        static::updated(function ($saleOrder) {
            if (
                $saleOrder->original['order_status'] !== $saleOrder->attributes['order_status']
                && in_array($saleOrder->attributes['order_status'], SaleOrder::statusCallbackWebhook())
            ) {
                if (!in_array($saleOrder->attributes['order_status'], [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_NEW_ORDER, SaleOrder::STATUS_ON_HOLD, SaleOrder::DRAFT])) {
                    handleJob(SaleOrder::QUEUE_UPDATE_SALE_ORDER_SLA_STATUS, $saleOrder->id);
                }
                if (in_array($saleOrder->attributes['order_status'], [SaleOrder::STATUS_REJECT, SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_IN_PRODUCTION_CANCELLED])) {
                    handleJob(self::JOB_REFUND_ORDER, $saleOrder->id);
                    handleJob(QueueJob::QUEUE_SYNC_ORDER_DESK_REJECT, $saleOrder->id);
                    handleJob(QueueJob::CANCEL_EMBROIDERY_TASK, $saleOrder->id);
                }

                handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $saleOrder->id);
            }
            if ($saleOrder->original['shipment_id'] !== $saleOrder->attributes['shipment_id']) {
                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $saleOrder->id);
            }
        });
    }

    public function orderInsert(): HasMany
    {
        return $this->hasMany(SaleOrderInsert::class, 'order_id', 'id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(SaleOrderItem::class, 'order_id');
    }

    public function imageItems(): HasMany
    {
        return $this->hasMany(SaleOrderItemImage::class, 'order_id');
    }

    public function barcodeItems(): HasMany
    {
        return $this->hasMany(SaleOrderItemBarcode::class, 'order_id');
    }

    public function timeline(): HasMany
    {
        return $this->hasMany(SaleOrderHistory::class, 'order_id');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(SaleOrderAccount::class);
    }

    public function shipment(): HasMany
    {
        return $this->hasMany(Shipment::class, 'order_id');
    }

    public function shipmentDefault(): BelongsTo
    {
        return $this->belongsTo(Shipment::class, 'shipment_id', 'id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(SaleOrderAddress::class, 'order_id');
    }

    public function returnAddress(): HasMany
    {
        return $this->hasMany(SaleOrderAddress::class, 'order_id')->where('type_address', SaleOrderAddress::RETURN_ADDRESS);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function wareHouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id', 'id');
    }

    public function tags(): HasMany
    {
        //for this time. this contain seller tags
        return $this->hasMany(SaleOrderTag::class, 'order_id');
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where("$this->table.warehouse_id", $request['warehouse_id']);
        }

        if (!empty($request['warehouse'])) {
            $query->where("$this->table.warehouse_id", $request['warehouse']);
        }

        if (array_key_exists($request['production_status'], self::PRODUCTION_STATUS)) {
            $query->where(function ($q) use ($request) {
                foreach (self::PRODUCTION_STATUS as $status => $column) {
                    if ($status === $request['production_status']) {
                        $q->where("$this->table.$column", self::ACTIVE);
                        break;
                    }

                    $q->where("$this->table.$column", self::INACTIVE);
                }
            });
        }

        $keyword = $request['keyword'];

        if (!empty($keyword)) {
            $queryOrderIds = SaleOrder::where('order_number', $keyword)->orWhere('external_number', $keyword)
                ->orWhere('encode_id', $keyword)->select('id');
            $queryShipments = Shipment::where('tracking_number', $keyword)->select('order_id as id');
            $queryOrderIds->union($queryShipments);
            $queryItems = SaleOrderItem::select('order_id as id');

            if (strlen($keyword) == 4) {
                $queryItems->where('product_style_sku', $keyword);
            } elseif (strlen($keyword) == 6) {
                $queryItems->where('product_style_sku', substr($keyword, 0, 4))
                    ->where('product_color_sku', substr($keyword, 4, 2));
            } elseif (strlen($keyword) == 9) {
                $queryItems->where('product_sku', $keyword);
            } else {
                $queryItems->where('sku', $keyword);
            }

            $queryOrderIds->union($queryItems);
            $query->joinSub($queryOrderIds, 'order_ids', 'sale_order.id', 'order_ids.id');
        }

        $joinShipment = false;
        if (!empty($request['tracking_status'])) {
            $joinShipment = true;
            $trackingStatus = $request['tracking_status'];
            $query->where('shipment.tracking_status', $trackingStatus);
        }

        if (!empty($request['est_delivery_start'])) {
            $joinShipment = true;
            $estDeliveryStart = \Illuminate\Support\Carbon::parse($request['est_delivery_start'])->startOfDay();
            $query->where('shipment.est_delivery_at', '>=', $estDeliveryStart);
        }

        if (!empty($request['est_delivery_end'])) {
            $joinShipment = true;
            $estDeliveryEnd = \Illuminate\Support\Carbon::parse($request['est_delivery_end'])->endOfDay();
            $query->where('shipment.est_delivery_at', '<=', $estDeliveryEnd);
        }

        if ($joinShipment) {
            $query->join('shipment', 'sale_order.shipment_id', '=', 'shipment.id');
        }

        if (!empty($request['order_type'])) {
            $orderTypes = is_array($request['order_type']) ? $request['order_type'] : [$request['order_type']];
            $query->whereIn('sale_order.order_type', $orderTypes);
        }

        if (!empty($request['shipping_method'])) {
            $query->where("$this->table.shipping_method", $request['shipping_method']);
        }

        if (!isset($request['is_test']) || empty($request['is_test'])) {
            $request['is_test'] = 0;
        }

        if ($request['is_test'] != -1) {
            $query->where("$this->table.is_test", $request['is_test']);
        }

        if (!empty($request['is_xqc'])) {
            $query->where("$this->table.is_xqc", $request['is_xqc']);
        }

        if (!empty($request['is_manual'])) {
            $query->where("$this->table.is_manual", $request['is_manual']);
        }

        if (!empty($request['is_create_manual'])) {
            $query->where("$this->table.is_create_manual", $request['is_create_manual']);
        }

        if (!empty($request['is_fba_order'])) {
            $query->where("$this->table.is_fba_order", $request['is_fba_order']);
        }

        $destination = $request['destination'];

        if (!empty($destination)) {
            if ($destination == '1') {
                $query->whereHas('address', function ($q) {
                    $q->where('country', 'US');
                });
            } elseif ($destination == '2') {
                $query->whereHas('address', function ($q) {
                    $q->whereNotNull('country')->where('country', '!=', 'US');
                });
            }
        }

        if (!empty($request['order_number'])) {
            $query->where("$this->table.order_number", 'LIKE', '%' . $request['order_number'] . '%');
        }

        if (!empty($request['external_number'])) {
            $query->where("$this->table.external_number", 'LIKE', '%' . $request['external_number'] . '%');
        }

        if (is_numeric($request['account_id'])) {
            $query->where("$this->table.account_id", $request['account_id']);
        }

        if (is_numeric($request['store_id'])) {
            $query->where("$this->table.store_id", $request['store_id']);
        }

        if (isset($request['store_ids']) && is_array($request['store_ids'])) {
            $query->whereIn("$this->table.store_id", $request['store_ids']);
        }

        if (!empty($request['order_status'])) {
            $query->where("$this->table.order_status", $request['order_status']);
        }

        if (!empty($request['source'])) {
            $query->where("$this->table.source", $request['source']);
        }

        if (!empty($request['order_date_start'])) {
            $query->whereDate("$this->table.created_at", '>=', $request['order_date_start']);
        }

        if (!empty($request['order_date_end'])) {
            $query->whereDate("$this->table.created_at", '<=', $request['order_date_end']);
        }

        if (is_array($request['tag']) && count($request['tag']) > 0) {
            $query->where(function ($q) use ($request) {
                foreach ($request['tag'] as $key => $tag) {
                    if (!is_numeric($tag)) {
                        continue;
                    }
                    $q->whereRaw("FIND_IN_SET(?, $this->table.tag)", $tag);
                }
            });
        }

        if (!empty($request['filter_name'])) {
            switch ($request['filter_name']) {
                case self::FILTER_TAB['late_deduction']:
                    $query
                        ->whereNotNull('order_number')
                        ->where('order_status', self::IN_PRODUCTION)
                        ->whereDate('order_date', '>', Carbon::now()->subDay(10)->format('Y-m-d'))
                        ->whereHas('barcodeItems', function ($q) {
                            $q->whereNull('pulled_at')->select('id');
                        })
                        ->where('created_at', '<', Carbon::now()->subHour(12));
                    break;
                case self::FILTER_TAB['late_production']:
                    $query
                        ->where('created_at', '<', Carbon::now()->subHour(12))
                        ->whereNotNull('order_number')
                        ->where('order_status', self::NEW_ORDER)
                        ->whereDate('order_date', '>', Carbon::now()->subDay(10)->format('Y-m-d'))
                        ->whereDoesntHave('items', function ($q) {
                            $q->where('ink_color_status', 0);
                        });
                    break;
                case self::FILTER_TAB['late_shipment']:
                    $query
                        ->where('order_status', self::STATUS_IN_PRODUCTION)
                        ->where(function ($q) {
                            $q->where('order_production_at', '<', Carbon::now()->subHour(24))
                                ->orWhere(function ($sQ) {
                                    $sQ->where('order_production_at', '<', Carbon::now()->subHour(12))
                                        ->where('order_quantity', 1);
                                });
                        })
                        ->whereNull('shipment_id');
                    break;
                case self::FILTER_TAB['invalid_address']:
                    $query->whereNotIn('sale_order.order_status', [SaleOrder::STATUS_REJECT, SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_ON_HOLD, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_LATE_CANCELLED])
                        ->WhereHas('shipmentCreateErrorLog');
                    break;
            }
        }

        if (!empty($request['tracking_number'])) {
            $query->whereHas('shipment', function ($q) use ($request) {
                $q->where('shipment.tracking_number', $request['tracking_number']);
            });
        }

        $customerName = $request['customer_name'] ?? '';
        $customerEmail = $request['customer_email'] ?? '';
        $customerAddress = $request['customer_address'] ?? '';

        if (!empty($customerName) || !empty($customerEmail) || !empty($customerAddress)) {
            $query->whereHas('addressSaleOrder', function ($q) use ($customerName, $customerEmail, $customerAddress) {
                if (!empty($customerName)) {
                    $q->where('name', 'LIKE', '%' . $customerName . '%');
                }

                if (!empty($customerEmail)) {
                    $q->where('email', 'LIKE', '%' . $customerEmail . '%');
                }

                if (!empty($customerAddress)) {
                    $q->where(function ($subQ) use ($customerAddress) {
                        $subQ->where('street1', 'LIKE', '%' . $customerAddress . '%');
                        $subQ->orWhere('street2', 'LIKE', '%' . $customerAddress . '%');
                    });
                }

                $q->where('type_address', SaleOrderAddress::TO_ADDRESS);
            });
        }

        //for Seller
        if (!empty($request['payment_status'])) {
            $query->where('payment_status', $request['payment_status']);
        }

        $keySearch = $request['key_search'];

        if (!empty($keySearch)) {
            $shipmentIds = Shipment::where('tracking_number', $keySearch)->get('id')->pluck('id');
            $query->where(function ($q) use ($keySearch, $shipmentIds) {
                $q->whereIn('shipment_id', $shipmentIds)
                    ->orWhere("$this->table.encode_id", $keySearch)
                    ->orWhere("$this->table.external_number", $keySearch)
                    ->orWhere("$this->table.external_key", $keySearch);
            });
        }

        $sku = $request['sku'] ?? null;
        $hasLicensedHolder = !empty($request['licensed_holder']);

        if ($sku || $hasLicensedHolder) {
            $query->join('sale_order_item', function ($join) use ($request, $sku, $hasLicensedHolder) {
                $join->on('sale_order_item.order_id', '=', 'sale_order.id');

                // Điều kiện theo SKU
                if (!empty($sku)) {
                    $join->where(function ($query) use ($sku) {
                        switch (strlen($sku)) {
                            case 4:
                                $query->where('product_style_sku', $sku);
                                break;
                            case 6:
                                $query->where('product_style_sku', substr($sku, 0, 4))
                                    ->where('product_color_sku', substr($sku, 4, 2));
                                break;
                            case 9:
                                $query->where('product_sku', $sku);
                                break;
                            default:
                                $query->where('sku', $sku);
                                break;
                        }
                    });
                }

                // Điều kiện theo Licensed Holder
                if ($hasLicensedHolder) {
                    $licensedHolders = is_array($request['licensed_holder']) ? $request['licensed_holder'] : [$request['licensed_holder']];
                    $licensedDesignIds = LicensedDesign::whereIn('licensed_holder_id', $licensedHolders)->pluck('licensed_design_id');
                    $join->whereIn('sale_order_item.licensed_design_id', $licensedDesignIds);
                }
            });

            // Nếu có điều kiện theo Licensed Holder, thêm groupBy để tránh trùng lặp dữ liệu
            if ($hasLicensedHolder) {
                $query->groupBy('sale_order.id');
            }
        }

        if (!empty($request['payment_status'])) {
            $query->where('payment_status', $request['payment_status']);
        }

        if (!empty($request['paid_start'])) {
            $query->where('paid_at', '>=', Carbon::parse($request['paid_start'])->startOfDay());
        }

        if (!empty($request['paid_end'])) {
            $query->where('paid_at', '<=', Carbon::parse($request['paid_end'])->endOfDay());
        }

        if (!empty($request['refunded_start'])) {
            $query->where('refunded_at', '>=', Carbon::parse($request['refunded_start'])->startOfDay());
        }

        if (!empty($request['refunded_end'])) {
            $query->where('refunded_at', '<=', Carbon::parse($request['refunded_end'])->endOfDay());
        }

        if (!empty($request['seller_tag'])) {
            $query->whereHas('tags', function ($q) use ($request) {
                $q->where('tag_id', $request['seller_tag']);
            });
        }

        return $query;
    }

    public function shippingCarrierEasypost(): HasMany
    {
        return $this->hasMany(ShippingCarrierEasypost::class, 'store_id', 'store_id');
    }

    public function addressSaleOrder(): HasMany
    {
        return $this->hasMany(SaleOrderAddress::class, 'order_id');
    }

    public function storeAddress(): HasMany
    {
        return $this->hasMany(StoreAddress::class, 'store_id', 'store_id');
    }

    public function storeReturnAddress(): HasMany
    {
        return $this->hasMany(StoreAddress::class, 'store_id', 'store_id')->where('type_address', SaleOrderAddress::RETURN_ADDRESS);
    }

    public function shipmentCreateErrorLogLast(): HasMany
    {
        return $this->hasMany(ShipmentCreateErrorLog::class, 'order_id', 'id')->orderByDesc('id');
    }

    public function invoices(): BelongsToMany
    {
        return $this->belongsToMany(Invoice::class, 'invoice_sale_order', 'sale_order_id', 'invoice_id');
    }

    public static function changeStatusToInProduction($saleOrderIds)
    {
        $newOrderIds = SaleOrder::whereIn('id', array_unique($saleOrderIds))->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();

        if (!empty($newOrderIds)) {
            handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
        }

        $quantity = self::whereIn('id', array_unique($saleOrderIds))
            ->where('order_status', SaleOrder::NEW_ORDER)
            ->update([
                'order_status' => SaleOrder::IN_PRODUCTION,
                'order_production_at' => Carbon::now()
            ]);

        return $quantity;
    }

    public function histories(): HasMany
    {
        return $this->hasMany(SaleOrderHistory::class, 'order_id');
    }

    public static function findOrderMissShipped($id)
    {
        return self::where('id', $id)
            ->whereIn('order_status', [self::STATUS_NEW_ORDER, self::STATUS_IN_PRODUCTION, self::STATUS_SHIPPED])
            ->first();
    }

    public function shipmentCreateErrorLog(): HasMany
    {
        return $this->hasMany(ShipmentCreateErrorLog::class, 'order_id', 'id');
    }

    public function shippingMethods(): HasMany
    {
        return $this->hasMany(ShippingMethod::class, 'api_shipping_method', 'shipping_method');
    }

    public function partNumberHistory(): HasMany
    {
        return $this->hasMany(PartNumberHistory::class, 'order_id', 'id');
    }

    public function scopeSearchV2(Builder $mainQuery, Request $request): Builder
    {
        $query = self::selectId($request);
        $mainQuery->rightJoinSub($query, 'subQuery', "$this->table.id", 'subQuery.id');

        return $mainQuery;
    }

    public static function selectId(Request $request): Builder
    {
        $query = self::select('sale_order.id');

        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where('sale_order.warehouse_id', $request['warehouse_id']);
        }

        if (!empty($request['warehouse'])) {
            $query->where('sale_order.warehouse_id', $request['warehouse']);
        }

        if (!empty($request['order_issue'])) {
            $query->where('sale_order.is_error', SaleOrder::ACTIVE);
        }

        if (!empty($request['keyword'])) {
            $keyword = $request['keyword'];
            $queryOrderIds = SaleOrder::where('order_number', $keyword)->orWhere('external_number', 'like', "$keyword%")->orWhere('external_key', $keyword)
                ->orWhere('encode_id', $keyword)->select('id');
            $queryShipments = Shipment::where('tracking_number', $keyword)->select('order_id as id');
            $queryOrderIds->union($queryShipments);
            $queryItems = SaleOrderItem::select('order_id as id');

            if (strlen($keyword) == 4) {
                $queryItems->where('product_style_sku', $keyword);
            } elseif (strlen($keyword) == 6) {
                $queryItems->where('product_style_sku', substr($keyword, 0, 4))
                    ->where('product_color_sku', substr($keyword, 4, 2));
            } elseif (strlen($keyword) == 9) {
                $queryItems->where('product_sku', $keyword);
            } else {
                $queryItems->where('sku', $keyword);
            }

            $queryOrderIds->union($queryItems);
            $query->joinSub($queryOrderIds, 'order_ids', 'sale_order.id', 'order_ids.id');
        }
        if (!empty($request['sla_status'])) {
            $slaStatus = $request['sla_status'];
            $slaOrder = null;

            switch ($slaStatus) {
                case SaleOrderSla::STATUS_FILTER_ON_ALERT:
                    $slaOrder = SaleOrderSla::whereNull('completed_at')
                        ->whereRaw('TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), "UTC", "America/Los_Angeles"), CONVERT_TZ(expired_at, "UTC", "America/Los_Angeles")) BETWEEN 1 AND 720')
                        ->select('order_id');
                    break;

                case SaleOrderSla::STATUS_FILTER_ON_1_DAY:
                    $slaOrder = SaleOrderSla::whereNull('completed_at')
                        ->whereRaw('TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), "UTC", "America/Los_Angeles"), CONVERT_TZ(expired_at, "UTC", "America/Los_Angeles")) BETWEEN -1440 AND 0')
                        ->select('order_id');
                    break;

                case SaleOrderSla::STATUS_FILTER_ON_1_3_DAYS:
                    $slaOrder = SaleOrderSla::whereNull('completed_at')
                        ->whereRaw('TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), "UTC", "America/Los_Angeles"), CONVERT_TZ(expired_at, "UTC", "America/Los_Angeles")) BETWEEN -4320 AND -1440')
                        ->select('order_id');
                    break;

                case SaleOrderSla::STATUS_FILTER_ON_3_DAYS:
                    $slaOrder = SaleOrderSla::whereNull('completed_at')
                        ->whereRaw('TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), "UTC", "America/Los_Angeles"), CONVERT_TZ(expired_at, "UTC", "America/Los_Angeles")) < -4320')
                        ->select('order_id');
                    break;

                default:
                    break;
            }

            if ($slaOrder) {
                $query->whereIn('sale_order.order_status', [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::NEW_ORDER]);
                $query->joinSub($slaOrder, 'sla_order', 'sale_order.id', '=', 'sla_order.order_id');
            }
        }

        if (!empty($request['tracking_status'])) {
            $trackingStatus = $request['tracking_status'];
            $query->join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
                ->where('shipment.tracking_status', $trackingStatus);
        }

        if (!empty($request['order_type'])) {
            $query->where('sale_order.order_type', $request['order_type']);
        }

        if (!empty($request['shipping_method'])) {
            $shippingMethods = explode(',', $request['shipping_method']);
            $query->wherein('sale_order.shipping_method', $shippingMethods);
        }

        if (!isset($request['is_test']) || empty($request['is_test'])) {
            $request['is_test'] = 0;
        }

        if ($request['is_test'] != -1) {
            $query->where('sale_order.is_test', $request['is_test']);
        }

        if (!empty($request['is_xqc'])) {
            $query->where('sale_order.is_xqc', $request['is_xqc']);
        }

        if (!empty($request['is_manual'])) {
            $query->where('sale_order.is_manual', $request['is_manual']);
        }

        if (!empty($request['is_create_manual'])) {
            $query->where('sale_order.is_create_manual', $request['is_create_manual']);
        }

        if (!empty($request['is_fba_order'])) {
            $query->where('sale_order.is_fba_order', $request['is_fba_order']);
        }

        if (!empty($request['is_error'])) {
            $query->where('sale_order.is_error', SaleOrder::ACTIVE);
        }

        $hasPrintMethod = !empty($request['print_method']) && is_array($request['print_method']);
        if ($hasPrintMethod) {
            $query->join('sale_order_item_barcode', function ($join) use ($request) {
                $join->on('sale_order_item_barcode.order_id', '=', 'sale_order.id')
                    ->whereIn('sale_order_item_barcode.print_method', $request['print_method']);
            });
        }

        $hasPrintArea = !empty($request['print_area']) && is_array($request['print_area']);
        if ($hasPrintArea) {
            $query->join('sale_order_item_image', function ($join) use ($request) {
                $join->on('sale_order_item_image.order_id', '=', 'sale_order.id')
                    ->whereIn('sale_order_item_image.print_side', $request['print_area']);
            });
        }
        $hasTradeMarkIds = !empty($request['trademark_id']);
        if ($hasTradeMarkIds) {
            $query->whereHas('items.images.trademarks', function ($query) use ($request) {
                $query->where('trademark_id', $request['trademark_id']);
            })->whereHas('items.images.imageHash', function ($query) {
                $query->where('is_ip_violation', 1);
            });
        }
        $hasLicensedHolder = !empty($request['licensed_holder']) && is_array($request['licensed_holder']);
        if ($hasLicensedHolder) {
            $licensedDesignIds = LicensedDesign::whereIn('licensed_holder_id', $request['licensed_holder'])->pluck('licensed_design_id');
            $query->join('sale_order_item', function ($join) use ($licensedDesignIds) {
                $join->on('sale_order_item.order_id', '=', 'sale_order.id')
                    ->whereIn('sale_order_item.licensed_design_id', $licensedDesignIds);
            });
        }

        $destination = $request['destination'];

        if (!empty($destination)) {
            if ($destination == '1') {
                $query->whereHas('address', function ($q) {
                    $q->where('country', 'US')
                        ->where('type_address', SaleOrderAddress::TO_ADDRESS);
                });
            } elseif ($destination == '2') {
                $query->whereHas('address', function ($q) {
                    $q->whereNotNull('country')->where('country', '!=', 'US')
                        ->where('type_address', SaleOrderAddress::TO_ADDRESS);
                });
            }
        }

        if (!empty($request['order_number'])) {
            $query->where('sale_order.order_number', 'LIKE', '%' . $request['order_number'] . '%');
        }

        if (!empty($request['external_number'])) {
            $query->where('sale_order.external_number', 'LIKE', '%' . $request['external_number'] . '%');
        }

        if (is_numeric($request['account_id'])) {
            $query->where('sale_order.account_id', $request['account_id']);
        }

        if (is_numeric($request['store_id'])) {
            $query->where('sale_order.store_id', $request['store_id']);
        }
        if (isset($request['store_ids']) && is_array($request['store_ids'])) {
            $query->whereIn('sale_order.store_id', $request['store_ids']);
        }

        if (!empty($request['order_status'])) {
            $query->where('sale_order.order_status', $request['order_status']);
        }

        if (!empty($request['source'])) {
            $query->where('sale_order.source', $request['source']);
        }

        if (!empty($request['order_date_start'])) {
            $start_date = Carbon::parse($request['order_date_start'])->startOfDay();
            $query->where('sale_order.created_at', '>=', $start_date->format('Y-m-d H:i:s'));
        }

        if (!empty($request['order_date_end'])) {
            $end_date = Carbon::parse($request['order_date_end'])->endOfDay();
            $query->where('sale_order.created_at', '<=', $end_date->format('Y-m-d H:i:s'));
        }

        if (is_array($request['tag']) && count($request['tag']) > 0) {
            $query->where(function ($q) use ($request) {
                foreach ($request['tag'] as $key => $tag) {
                    if (!is_numeric($tag)) {
                        continue;
                    }
                    $q->whereRaw('FIND_IN_SET(?, sale_order.tag)', $tag);
                }
            });
        }

        if (!empty($request['production_status'])) {
            $query->where('sale_order.production_status', $request['production_status']);
        }

        if (!empty($request['payment_status'])) {
            $query->where('sale_order.payment_status', $request['payment_status']);
        }

        if (!empty($request['filter_name'])) {
            switch ($request['filter_name']) {
                case self::FILTER_TAB['late_deduction']:
                    $query
                        ->whereNotNull('order_number')
                        ->where('order_status', self::IN_PRODUCTION)
                        ->whereDate('order_date', '>', Carbon::now()->subDay(10)->format('Y-m-d'))
                        ->whereHas('barcodeItems', function ($q) {
                            $q->whereNull('pulled_at')->select('id');
                        })
                        ->where('created_at', '<', Carbon::now()->subHour(12));
                    break;
                case self::FILTER_TAB['late_production']:
                    $query
                        ->where('created_at', '<', Carbon::now()->subHour(12))
                        ->whereNotNull('order_number')
                        ->where('order_status', self::NEW_ORDER)
                        ->whereDate('order_date', '>', Carbon::now()->subDay(10)->format('Y-m-d'))
                        ->whereDoesntHave('items', function ($q) {
                            $q->where('ink_color_status', 0);
                        });
                    break;
                case self::FILTER_TAB['late_shipment']:
                    $query
                        ->where('order_status', self::STATUS_IN_PRODUCTION)
                        ->where(function ($q) {
                            $q->where('order_production_at', '<', Carbon::now()->subHour(24))
                                ->orWhere(function ($sQ) {
                                    $sQ->where('order_production_at', '<', Carbon::now()->subHour(12))
                                        ->where('order_quantity', 1);
                                });
                        })
                        ->whereNull('shipment_id');
                    break;
                case self::FILTER_TAB['invalid_address']:
                    $query->whereNotIn('sale_order.order_status', [SaleOrder::STATUS_REJECT, SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_ON_HOLD, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_LATE_CANCELLED])
                        ->WhereHas('shipmentCreateErrorLog');
                    break;
            }
        }

        if (!empty($request['tracking_number'])) {
            $query->whereHas('shipment', function ($q) use ($request) {
                $q->where('shipment.tracking_number', $request['tracking_number']);
            });
        }

        $customerName = $request['customer_name'] ?? '';
        $customerEmail = $request['customer_email'] ?? '';
        $customerAddress = $request['customer_address'] ?? '';

        if (!empty($customerName) || !empty($customerEmail) || !empty($customerAddress)) {
            $query->whereHas('addressSaleOrder', function ($q) use ($customerName, $customerEmail, $customerAddress) {
                if (!empty($customerName)) {
                    $q->where('name', 'LIKE', '%' . $customerName . '%');
                }

                if (!empty($customerEmail)) {
                    $q->where('email', 'LIKE', '%' . $customerEmail . '%');
                }

                if (!empty($customerAddress)) {
                    $q->where(function ($subQ) use ($customerAddress) {
                        $subQ->where('street1', 'LIKE', '%' . $customerAddress . '%');
                        $subQ->orWhere('street2', 'LIKE', '%' . $customerAddress . '%');
                    });
                }

                $q->where('type_address', SaleOrderAddress::TO_ADDRESS);
            });
        }

        //for Seller
        $keySearch = $request['key_search'];

        if (!empty($keySearch)) {
            $shipmentIds = Shipment::where('tracking_number', $keySearch)->get('id')->pluck('id');
            $query->where(function ($q) use ($keySearch, $shipmentIds) {
                $q->whereIn('shipment_id', $shipmentIds)
                    ->orWhere('sale_order.encode_id', $keySearch)
                    ->orWhere('sale_order.external_key', $keySearch)
                    ->orWhere('sale_order.external_number', $keySearch);
            });
        }

        if (!isset($request['get_total']) || !$request['get_total']) {
            $sortBy = (!empty($request['sort_by'])) ? $request['sort_by'] : ((!empty($request['order_status'])) && in_array($request['order_status'], [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]) ? 'asc' : 'desc');
            $sortColumn = (!empty($request['sort_column'])) ? $request['sort_column'] : ((!empty($request['order_status'])) && in_array($request['order_status'], [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]) ? 'created_at' : 'id');

            if (empty($request['order_status']) || ($request['order_status'] == SaleOrder::SHIPPED && $sortColumn == 'id')) {
                $page = $request['page'] ?? 1;
                $perPage = $request['limit'] ?? 25;
                $query->orderBy("sale_order.$sortColumn", $sortBy)->skip(($page - 1) * $perPage)->take($perPage + 1);
            }
        }

        if ($hasPrintMethod || $hasPrintArea || $hasLicensedHolder) {
            $query->groupBy('sale_order.id');
        }

        return $query;
    }

    public function saleOrderInsertCalculatePrice(): HasMany
    {
        return $this->hasMany(SaleOrderInsertCalculatePrice::class, 'order_id');
    }

    public static function countAllOrderByRangeTimeByQuery($startTime, $endTime, $warehouseId, $storeId)
    {
        $query = self::selectRaw('HOUR(created_at) as hour, COUNT(*) as count, warehouse_id, store_id, created_at')
            ->where('created_at', '<=', $endTime)
            ->where('created_at', '>=', $startTime)
            ->where('is_test', self::INACTIVE);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->groupBy('hour')->orderBy('hour')->get();
    }

    public static function countTotalOrdersByRangeTime($startTime, $endTime, $warehouseId, $storeId)
    {
        $query = self::query()
            ->where('created_at', '<=', $endTime)
            ->where('created_at', '>=', $startTime)
            ->where('is_test', self::INACTIVE);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->count();
    }

    public static function countOrderByStatus($warehouseId = null, $storeId = null)
    {
        $query = self::selectRaw('COUNT(*) as count, order_status')
            ->whereIn('order_status', [self::STATUS_NEW_ORDER, self::STATUS_IN_PRODUCTION])
            ->where('is_test', self::INACTIVE);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        return $query->groupBy('order_status')->pluck('count', 'order_status');
    }

    public static function getIDBeforeDays($days = 30)
    {
        // Find sale order item id before 2 days
        $result = self::selectRaw('MIN(id) as id')
            ->where('created_at', '>', now()->subDays($days))
            ->where('id', '>', env('ID_SALE_ORDER_VALID', 5629058))
            ->first();

        if ($result) {
            return $result->id;
        }

        return env('ID_SALE_ORDER_VALID', 5629058);
    }

    public function orderItemErrors(): HasMany
    {
        return $this->hasMany(SaleOrderItemError::class, 'order_id');
    }

    public function orderReprints(): HasMany
    {
        return $this->hasMany(SaleOrderReprint::class, 'parent_id', 'id');
    }

    public function orderSurchargeFee(): HasMany
    {
        return $this->hasMany(SaleOrderItemSurchargeFee::class, 'order_id', 'id');
    }

    public function integrateLog()
    {
        return $this->hasOne(IntegrateLog::class, 'order_id');
    }

    public function visualDetectImages(): HasMany
    {
        return $this->hasMany(VisuaDetectImage::class, 'order_id', 'id');
    }

    public function orderOnHold()
    {
        return $this->hasOne(SaleOrderOnHold::class, 'order_id', 'id');
    }

    public function shipments()
    {
        return $this->hasMany(Shipment::class, 'order_id', 'id');
    }

    public function customerEmail()
    {
        return $this->hasMany(SaleOrderCustomerEmail::class, 'order_id', 'id');
    }

    public static function transformOrderType($typeNumber, $tag = null)
    {
        if (strpos($tag, Tag::LABEL_TAG_ID) !== false && $typeNumber == self::ORDER_TYPE_NORMAL) {
            $typeNumber = self::ORDER_TYPE_LABEL_ORDER;
        }

        switch ($typeNumber) {
            case self::ORDER_TYPE_NORMAL:
                return 'Normal';
            case self::ORDER_TYPE_PRETREATED:
                return 'Pretreated';
            case self::ORDER_TYPE_BLANK:
                return 'Blank';
            case self::ORDER_TYPE_LABEL_ORDER:
                return 'Label';
            case self::ORDER_TYPE_TIKTOK_ORDER:
                return 'Tiktok';
            case self::ORDER_TYPE_LICENSE_ORDER:
                return 'License';
        }
    }

    public function orderDetail()
    {
        return $this->hasOne(SaleOrderDetail::class, 'order_id', 'id');
    }

    public function orderRbt()
    {
        return $this->hasOne(RbtReceived::class, 'order_id', 'id');
    }

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    public function peakShippingFee(): HasOne
    {
        return $this->hasOne(SaleOrderSurchargeFee::class, 'order_id', 'id')
            ->where('type', PeakShippingFee::PEAK_SHIPPING_FEE);
    }

    public function refunds()
    {
        return $this->hasMany(SaleOrderRefund::class, 'order_id', 'id');
    }

    public function surchargeFees()
    {
        return $this->hasMany(SaleOrderSurchargeFee::class, 'order_id', 'id');
    }

    public function itemSurchargeFees()
    {
        return $this->hasMany(SaleOrderItemSurchargeFee::class, 'order_id', 'id');
    }

    public function sla()
    {
        return $this->belongsTo(SaleOrderSla::class, 'id', 'order_id');
    }

    public function promotions()
    {
        return $this->hasMany(SaleOrderPromotion::class, 'order_id');
    }

    public function pricingSnapshot()
    {
        return $this->hasOne(SaleOrderPricingSnapshot::class, 'order_id');
    }
}
