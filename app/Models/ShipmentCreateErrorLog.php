<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ShipmentCreateErrorLog extends Model
{
    protected $table = 'shipment_create_error_log';

    protected $fillable = [
        'id',
        'order_id',
        'note',
        'fix'
    ];

    public function shipment():HasOne
    {
        return $this->hasOne(shipment::class, 'order_id', 'order_id');
    }

    public function saleOrder():BelongsTo
    {
        return $this->belongsTo(SaleOrder::class, 'id', 'order_id');
    }
}
