<?php

namespace App\Exports;
use App\Models\Invoice;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ProductInvoiceRedBubbleExport implements WithMultipleSheets
{
    protected Invoice $invoice;
    protected $printSide;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function sheets(): array
    {
        return [
            'Sheet1' => new RedBubbleDataExport($this->invoice),
            'Sheet2' => new RedBubbleDataExport($this->invoice, RedBubbleDataExport::CANCEL_ORDER_SHEET_TYPE),
        ];
    }
}
