<?php

namespace App\Exports;

use App\Repositories\TestCountAdjustmentRepository;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
class ExportTestCount implements FromCollection, WithHeadings, WithMapping
{
    protected array $requestData;
    protected bool $isAdmin = false;

    public function __construct($requestData)
    {
        $this->requestData = $requestData;

        $this->isAdmin = $requestData['user']['is_admin'] == 1 ? true : false;
    }

    public function collection()
    {
        $result = resolve(TestCountAdjustmentRepository::class)->fetchTestCount($this->requestData, true);

        return collect($result);
    }

    public function headings(): array
    {
        $headings = [
            'ID',
            'Date',
            'Location',
            'Boxes Available',
            'Boxes On-hand',
            'Adjustment',
            'Quantity On-hand',
        ];

        if ($this->isAdmin) {
            $headings[] = 'Total Value';
        }

        $headings[] = 'User';
        $headings[] = 'Employee';

        return $headings;
    }

    public function map($item): array
    {
        $row = [
            $item->id,
            \Carbon\Carbon::parse($item->created_at)->timezone('America/Los_Angeles')->format('M d, Y g:i A'),
            $item->location,
            (string) $item->box_available,
            (string) $item->box_on_hand,
            (string) ($item->box_on_hand - $item->box_available),
            (string) $item->quantity_on_hand,
        ];

        if ($this->isAdmin) {
            $row[] = (string) $item->total_value;
        }

        $row[] = $item->username;
        $row[] = $item->employee_name;

        return $row;
    }
}
