<?php

namespace App\Exports;

use App\Models\Tag;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

ini_set('memory_limit', '3072M');

class SupplyPurchaseOrderExport implements FromCollection, WithHeadings, WithEvents
{
    protected $orders;
    protected $isAdmin;

    public function __construct($orders, $isAdmin = false)
    {
        $this->orders = $orders;
        $this->isAdmin = $isAdmin;
    }

    public function collection()
    {
        $data = [];
        $tagPo = Tag::where('source', 'purchase_order')->pluck('name', 'id');
        $this->orders->each(function ($order) use (&$data, $tagPo) {
            $tagIds = explode(',', $order->tag ?? '');
            $tagNames = $tagPo->only($tagIds)->values()->implode(',');

            $row = [
                'po_number' => $order->po_number,
                'order_number' => $order->order_number,
                'invoice_number' => $order->invoice_number,
                'order_date' => $order->order_date,
                'vendor' => $order->vendor?->name ?? null,
                'order_status' => $order->order_status,
                'tracking_number' => $order->tracking_number,
                'delivery_date' => $order->delivery_date,
            ];

            if ($this->isAdmin) {
                $row['amount'] = $order->sub_total;
                $row['fee'] = $order->fee;
            }

            $row['tag'] = $tagNames;
            $row['user'] = $order->user?->username ?? null;

            $data[] = $row;
        });

        return collect($data);
    }

    public function headings(): array
    {
        $headings = ['PO#', 'Order#', 'Invoice#', 'Order Date', 'Vendor', 'Status', 'Tracking#', 'ETA'];

        if ($this->isAdmin) {
            $headings[] = 'Sub Total';
            $headings[] = 'Other fees & charges';
        }

        $headings[] = 'Tag';
        $headings[] = 'User';

        return $headings;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                if ($this->isAdmin) {
                    $event->sheet->getDelegate()
                    ->getStyle('I2:I10000')
                    ->getNumberFormat()
                    ->setFormatCode('"$"#,##0.00');
                    $event->sheet->getDelegate()
                    ->getStyle('J2:J10000')
                    ->getNumberFormat()
                    ->setFormatCode('"$"#,##0.00');
                }
            },
        ];
    }
}
