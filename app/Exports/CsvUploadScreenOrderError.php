<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CsvUploadScreenOrderError implements FromCollection, WithHeadings
{
    protected $data;

    protected $customHeader;

    public function __construct($data, $customHeader = null)
    {
        $this->data = $data;
        $this->customHeader = $customHeader;
    }

    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        if ($this->customHeader) {
            return $this->customHeader;
        }

        return $this->data[0]->keys()->toArray();
    }
}
