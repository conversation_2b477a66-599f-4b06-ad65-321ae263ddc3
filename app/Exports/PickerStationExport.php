<?php

namespace App\Exports;

use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PickerStationExport implements FromCollection, WithHeadings
{
    protected $params;

    protected $headings = [];

    public function __construct($params)
    {
        $this->params = $params;
    }

    public function collection(): Collection
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('barcode_printed', 'sale_order_item_barcode.barcode_printed_id', '=', 'barcode_printed.id')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->whereNotNull('barcode_printed.station_id')
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::NOT_DELETED)
            ->where('sale_order.is_test', '=', false)
            ->whereNotIn('sale_order.order_status', [SaleOrder::REJECTED, SaleOrder::ON_HOLD, SaleOrder::DRAFT])
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->selectRaw('barcode_printed.station_id, count(IF(barcode_printed.print_status = 1 , 1, null)) as total_completed');

        if (!empty($input['warehouse_id'])) {
            $query->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id']);
        }

        if (!empty($input['start_date'])) {
            $query->where('sale_order_item.ink_color_detected_at', '>=', $input['start_date']);
        }
        $data = $query->groupBy('barcode_printed.station_id')->get();
        $result = [];
        foreach ($data as $index => $item) {
            $this->headings[] = $item->station_id;
            $result[0][$index] = $item->total_completed;
        }
        Log::debug('PickerStationExport', [
            'params' => $this->params,
            'headings' => $this->headings,
            'data' => $data,
        ]);

        return collect($data);
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
