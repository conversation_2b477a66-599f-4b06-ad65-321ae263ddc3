<?php

namespace App\Exports;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '2048M');

class AdditionExport implements FromCollection, WithHeadings
{
    protected $request;

    protected $employees;

    protected $users;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $employeeModel = new Employee();
        $this->employees = $employeeModel::all()->pluck('name', 'code');

        $userModel = new User();
        $this->users = $userModel::all()->pluck('username', 'id');

        $query = DB::table('inventory_addition')
        ->select('inventory_addition.*', 'location.barcode as location', 'product.name as product_name', 'product.sku as product_sku', 'box.barcode as box_id', 'inventory_addition.employee_id as employee_id')
        ->join('product', function ($join) {
            $join->on('product.id', '=', 'inventory_addition.product_id');
            if (!empty($this->request['product'])) {
                $keyword = $this->request['product'];
                $join->where('product.name', 'LIKE', '%' . $keyword . '%');
            }
        })
        ->join('location', function ($join) {
            $join->on('location.id', '=', 'inventory_addition.location_id');
            if (!empty($this->request['location'])) {
                $keyword = $this->request['location'];
                $join->where('location.barcode', 'LIKE', '%' . $keyword . '%');
            }
        })
        ->leftJoin('box', function ($join) {
            $join->on('box.id', '=', 'inventory_addition.box_id');
            if (!empty($this->request['box'])) {
                $keyword = $this->request['box'];
                $join->where('box.barcode', 'LIKE', '%' . $keyword . '%');
            }
        });

        if (!empty($this->request['po_number'])) {
            $keyword = $this->request['po_number'];
            $query->where('po_number', 'LIKE', '%' . $keyword . '%');
        }
        if (!empty($this->request['invoice_number'])) {
            $keyword = $this->request['invoice_number'];
            $query->where('invoice_number', 'LIKE', '%' . $keyword . '%');
        }
        if (!empty($this->request['tracking_number'])) {
            $keyword = $this->request['tracking_number'];
            $query->where('tracking_number', 'LIKE', '%' . $keyword . '%');
        }
        $start_date = isset($this->request['start_date']) ? $this->request['start_date'] : '';
        $end_date = isset($this->request['end_date']) ? $this->request['end_date'] : '';
        if (!empty($start_date) && !empty($end_date)) {
            $query->whereDate('inventory_addition.created_at', '>=', $start_date);
            $query->whereDate('inventory_addition.created_at', '<=', $end_date);
        }

        $query->where('inventory_addition.warehouse_id', $this->request['warehouse_id']);
        $data = [];
        $query->chunkById(5000, function ($items) use (&$data) {
            $newData = $this->convertData($items->toArray());
            $data = array_merge($data, $newData);
        }, 'inventory_addition.id', 'id');

        return collect($data);
    }

    public function headings(): array
    {
        $headings = ['Purchase Order#', 'Invoice#', 'Tracking#', 'Date', 'Box ID', 'Location', 'SKU', 'Product', 'Quantity'];

        if (!empty($this->request['is_admin'])) {
            $headings[] = 'Cost Value';
        }

        $headings[] = 'User';
        $headings[] = 'Employee';

        return $headings;
    }

    public function convertData($data = [])
    {
        $newData = [];
        foreach ($data as $item) {

            $row = [
                'po_number' => $item->po_number ?? 'N/A',
                'invoice_number' => $item->invoice_number ?? 'N/A',
                'tracking_number' => $item->tracking_number ?? 'N/A',
                'created_at' => $item->created_at ?? '',
                'box_id' => $item->box_id ?? '',
                'location' => $item->location ?? '',
                'product_sku' => $item->product_sku ?? '',
                'product_name' => $item->product_name ?? '',
                'quantity' => $item->quantity ?? '',
            ];

            if (!empty($this->request['is_admin'])) {
                $row['cost_value'] = !empty($item->cost_value) ? '$' . $item->cost_value : '$0.00';
            }

            $row['username'] = !empty($item->user_id) && isset($this->users[$item->user_id]) ? $this->users[$item->user_id] : '';
            $row['employee'] = !empty($item->employee_id) && isset($this->employees[$item->employee_id]) ? $this->employees[$item->employee_id] : '';

            $newData[] = $row;
        }

        return $newData;
    }
}
