<?php

namespace App\Exports;

use App\Models\ReportHistory;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Shipment;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class SaleOrderExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    public array $requestData;

    public array $header;

    public array $selectedSlugs;

    public array $selects;

    public array $tables = [];

    public array $stringColumns = ['External Order Number', 'Customer Item ID', 'Item Name'];

    public array $indexStringColumns = [];

    const LIST_COLUMN = [
        [
            'label' => 'Store Name',
            'column' => 'store.name',
            'slug' => 'store_name',
            'table' => 'store',
        ],
        [
            'label' => 'Order Date',
            'column' => 'sale_order.created_at',
            'table' => 'sale_order',
            'slug' => 'order_date',
        ],
        [
            'label' => 'Internal Order Number',
            'column' => 'sale_order.order_number',
            'table' => 'sale_order',
            'slug' => 'order_number',

        ],
        [
            'label' => 'External Order Number',
            'column' => 'sale_order.external_number',
            'table' => 'sale_order',
            'slug' => 'external_number',

        ],
        [
            'label' => 'Warehouse',
            'column' => 'warehouse.name',
            'table' => 'warehouse',
            'slug' => 'warehouse_name',
        ],
        [
            'label' => 'Item Name',
            'column' => 'sale_order_item.name',
            'table' => 'sale_order_item',
            'slug' => 'sale_order_item_name',
        ],
        [
            'label' => 'Customer Item ID',
            'column' => 'sale_order_item.external_id',
            'table' => 'sale_order_item',
            'slug' => 'external_id',
        ],
        [
            'label' => 'Item Quantity',
            'column' => 'sale_order_item.quantity',
            'table' => 'sale_order_item',
            'slug' => 'quantity',
        ],
        [
            'label' => 'Print Side',
            'column' => 'sale_order_item.print_sides',
            'table' => 'sale_order_item',
            'slug' => 'print_sides',
        ],
        [
            'label' => 'Product SKU',
            'column' => 'sale_order_item.product_sku',
            'table' => 'sale_order_item',
            'slug' => 'product_sku',
        ],
        [
            'label' => 'Product Style',
            'column' => 'product_style.name',
            'table' => 'product_style',
            'slug' => 'product_style_name',
        ],
        [
            'label' => 'Product Color',
            'column' => 'product_color.name',
            'table' => 'product_color',
            'slug' => 'product_color_name',
        ],
        [
            'label' => 'Product Size',
            'column' => 'product_size.name',
            'table' => 'product_size',
            'slug' => 'product_size_name',
        ],
        [
            'label' => 'Product Description',
            'column' => 'product.description',
            'table' => 'product',
            'slug' => 'product_description',
        ],
        [
            'label' => 'Product Type',
            'column' => 'product_style.type',
            'table' => 'product_style',
            'slug' => 'product_type',
        ],
        [
            'label' => 'Production Status',
            'column' => 'sale_order.production_status',
            'table' => 'sale_order',
            'slug' => 'production_status',
        ],
        [
            'label' => 'Order Status',
            'column' => 'sale_order.order_status',
            'table' => 'sale_order',
            'slug' => 'order_status',
        ],
        [
            'label' => 'Order Type',
            'column' => "CASE
                WHEN sale_order.order_type = 1
                     AND sale_order.tag LIKE '%203%' THEN 'Label'
                WHEN sale_order.order_type = 1 THEN 'Normal'
                WHEN sale_order.order_type = 2 THEN 'Pretreated'
                WHEN sale_order.order_type = 3 THEN 'Blank'
                WHEN sale_order.order_type = 5 THEN 'Label'
                WHEN sale_order.order_type = 6 THEN 'Tiktok'
                WHEN sale_order.order_type = 7 THEN 'Licensed'
                ELSE ''
            END",
            'table' => 'sale_order',
            'slug' => 'order_type',
        ],
        [
            'label' => 'Rejected Reason',
            'column' => 'sale_order.rejected_reason',
            'table' => 'sale_order',
            'slug' => 'rejected_reason',
        ],
        [
            'label' => 'Rejected Date',
            'column' => 'sale_order.rejected_at',
            'table' => 'sale_order',
            'slug' => 'rejected_at',
        ],
        [
            'label' => 'Print Price',
            'column' => '(sale_order_item.unit_price - sale_order_item.handling_fee - sale_order_item.blank_price) * sale_order_item.quantity',
            'table' => 'sale_order_item',
            'slug' => 'print_price',
        ],
        [
            'label' => 'Blank Price',
            'column' => 'sale_order_item.blank_price * sale_order_item.quantity',
            'table' => 'sale_order_item',
            'slug' => 'blank_price',
        ],
        [
            'label' => 'Total price',
            'column' => 'sale_order_item.amount_paid',
            'table' => 'sale_order_item',
            'slug' => 'amount_paid',
        ],
        [
            'label' => 'Shipping Price',
            'column' => 'sale_order.shipping_amount',
            'table' => 'sale_order',
            'slug' => 'shipping_amount',
        ],
        [
            'label' => 'Order Price',
            'column' => 'sale_order.order_total',
            'table' => 'sale_order',
            'slug' => 'order_total',
        ],
        [
            'label' => 'Ship Date',
            'column' => 'shipment.ship_date',
            'table' => 'shipment',
            'slug' => 'ship_date',
        ],
        [
            'label' => 'Ship From Name',
            'column' => 'COALESCE(return_addr.name, store_return_addr.name)',
            'table' => 'sale_order_address',
            'slug' => 'ship_from_name',
        ],
        [
            'label' => 'Ship From Address Line 1',
            'column' => 'COALESCE(return_addr.street1, store_return_addr.street1)',
            'table' => 'sale_order_address',
            'slug' => 'ship_from_address1',
        ],
        [
            'label' => 'Ship From Address Line 2',
            'column' => 'COALESCE(return_addr.street2, store_return_addr.street2)',
            'table' => 'sale_order_address',
            'slug' => 'ship_from_address2',
        ],
        [
            'label' => 'Ship From State',
            'column' => 'COALESCE(return_addr.state, store_return_addr.state)',
            'table' => 'sale_order_address',
            'slug' => 'ship_from_state',
        ],
        [
            'label' => 'Ship From City',
            'column' => 'COALESCE(return_addr.city, store_return_addr.city)',
            'table' => 'sale_order_address',
            'slug' => 'sale_from_city',
        ],
        [
            'label' => 'Ship From country',
            'column' => 'COALESCE(return_addr.country, store_return_addr.country)',
            'table' => 'sale_order_address',
            'slug' => 'sale_from_country',
        ],
        [
            'label' => 'Ship From Zipcode',
            'column' => 'COALESCE(return_addr.zip, store_return_addr.zip)',
            'table' => 'sale_order_address',
            'slug' => 'sale_from_zip',
        ],
        [
            'label' => 'Ship To Name',
            'column' => 'to_addr.name',
            'table' => 'sale_order_address',
            'slug' => 'ship_to_name',
        ],
        [
            'label' => 'Ship To Address Line 1',
            'column' => 'to_addr.street1',
            'table' => 'sale_order_address',
            'slug' => 'ship_to_address1',
        ],
        [
            'label' => 'Ship To Address Line 2',
            'column' => 'to_addr.street2',
            'table' => 'sale_order_address',
            'slug' => 'ship_to_address2',
        ],
        [
            'label' => 'Ship To State',
            'column' => 'to_addr.state',
            'table' => 'sale_order_address',
            'slug' => 'ship_to_state',
        ],
        [
            'label' => 'Ship To City',
            'column' => 'to_addr.city',
            'table' => 'sale_order_address',
            'slug' => 'sale_to_city',
        ],
        [
            'label' => 'Ship To country',
            'column' => 'to_addr.country',
            'table' => 'sale_order_address',
            'slug' => 'sale_to_country',
        ],
        [
            'label' => 'Ship To Zipcode',
            'column' => 'to_addr.zip',
            'table' => 'sale_order_address',
            'slug' => 'sale_to_zip',
        ],
        [
            'label' => 'Tracking Number',
            'column' => 'shipment.tracking_number',
            'table' => 'shipment',
            'slug' => 'tracking_number',
        ],
        [
            'label' => 'Carrier',
            'column' => 'shipment.carrier_code',
            'table' => 'shipment',
            'slug' => 'carrier_code',
        ],
        [
            'label' => 'Shipping Method',
            'column' => 'sale_order.shipping_method',
            'table' => 'sale_order',
            'slug' => 'shipping_method',
        ],
        [
            'label' => 'Shipping Service',
            'column' => 'shipment.service_code',
            'table' => 'shipment',
            'slug' => 'service_code',
        ]
    ];

    public function bindValue(Cell $cell, $value)
    {
        $colIndex = Coordinate::columnIndexFromString($cell->getColumn());
        if (in_array($colIndex, $this->indexStringColumns)) {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function __construct($requestData)
    {
        $this->requestData = $requestData;
        foreach ($requestData['columns'] as $item) {
            if (!empty($item['slug']) && empty($item['children'])) {
                $this->selectedSlugs[] = $item['slug'];
            }
        }
        if (count(array_intersect($this->tables, ['product_style', 'product_color', 'product_size', 'product'])) > 0) {
            $this->tables[] = 'sale_order_item';
        }
        if (!empty($this->requestData['order_status'])) {
            $this->selectedSlugs = array_merge($this->selectedSlugs, ['order_status']);
            if (in_array('rejected', $this->requestData['order_status'])) {
                $this->selectedSlugs = array_merge($this->selectedSlugs, ['rejected_reason', 'rejected_at']);
            }
        }
        if (!empty($this->requestData['production_status'])) {
            $this->selectedSlugs = array_merge($this->selectedSlugs, ['production_status']);
        }
        if (!empty($this->requestData['product_types'])) {
            $this->selectedSlugs[] = 'product_type';
        }
        if (!empty($this->requestData['order_type'])) {
            $this->selectedSlugs[] = 'order_type';
        }
        $listColumnSelected = [];
        $index = 1;
        foreach (self::LIST_COLUMN as $item) {
            if (in_array($item['slug'], $this->selectedSlugs)) {
                $this->header[] = $item['label'];
                if (in_array($item['label'], $this->stringColumns)) {
                    $this->indexStringColumns[] = $index;
                }
                $index++;
                $listColumnSelected[] = $item;
            }
        }
        $reportHistory = ReportHistory::find($this->requestData['report_history_id']);
        if (!empty($reportHistory)) {
            $reportHistory->columns = json_encode($listColumnSelected);
            $reportHistory->save();
        }
        $listColumnSelected[] = [
            'label' => 'ID',
            'column' => 'sale_order.id',
            'table' => 'sale_order',
            'slug' => 'id',
        ];

        $this->selects = [];
        $this->selectedSlugs = [];
        foreach ($listColumnSelected as $column) {
            $this->selects[] = DB::raw("{$column['column']} AS {$column['slug']}");
            $this->selectedSlugs[] = $column['slug'];
            $this->tables[] = $column['table'];
        }
        $this->tables = array_unique($this->tables);
    }

    public function collection()
    {
        $query = SaleOrder::select($this->selects);

        if (count(array_intersect($this->tables, ['sale_order_item', 'product_style', 'product_size', 'product_color', 'product'])) > 0) {
            $query->from(DB::raw('sale_order STRAIGHT_JOIN sale_order_item ON sale_order.id = sale_order_item.order_id'));
        }
        if (in_array('warehouse', $this->tables)) {
            $query->join('warehouse', 'warehouse.id', 'sale_order.warehouse_id');
        }
        if (in_array('store', $this->tables)) {
            $query->join('store', 'store.id', 'sale_order.store_id');
        }
        if (in_array('shipment', $this->tables)) {
            $query->leftJoin('shipment', 'shipment.id', 'sale_order.shipment_id');
        }
        if (in_array('product_style', $this->tables)) {
            $query->leftJoin('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        }
        if (in_array('product_color', $this->tables)) {
            $query->leftJoin('product_color', 'sale_order_item.product_color_sku', 'product_color.sku');
        }
        if (in_array('product_size', $this->tables)) {
            $query->leftJoin('product_size', 'sale_order_item.product_size_sku', 'product_size.sku');
        }
        if (in_array('product', $this->tables)) {
            $query->leftJoin('product', 'sale_order_item.product_id', 'product.id');
        }
        if (in_array('sale_order_address', $this->tables)) {
            $query->leftJoin('sale_order_address as to_addr', function ($join) {
                $join->on('to_addr.order_id', '=', 'sale_order.id')
                    ->where('to_addr.type_address', '=', SaleOrderAddress::TO_ADDRESS);
            });

            $query->leftJoin('sale_order_address as return_addr', function ($join) {
                $join->on('return_addr.order_id', '=', 'sale_order.id')
                    ->where('return_addr.type_address', '=', SaleOrderAddress::RETURN_ADDRESS);
            });

            $query->leftJoin('store_address as store_return_addr', function ($join) {
                $join->on('store_return_addr.store_id', '=', 'sale_order.store_id')
                    ->where('store_return_addr.type_address', '=', Store::RETURN_ADDRESS);
            });
        }

        if (!empty($this->requestData['order_date'][0])) {
            $startTime = Carbon::parse($this->requestData['order_date'][0])->startOfDay()->toDateTimeString();
            if (count(array_intersect($this->selectedSlugs, ['ship_date'])) > 0) {
                $minId = Shipment::where('created_at', '>=', $startTime)->min('id');
                $startOrderTime = Carbon::parse($this->requestData['order_date'][0])->subDays(14)->startOfDay()->toDateTimeString();
                $minOrderId = SaleOrder::where('created_at', '>=', $startOrderTime)->min('id');
                if (!empty($minId)) {
                    $query->where('shipment.id', '>=', $minId)
                        ->where('sale_order.id', '>=', $minOrderId);
                }
                $query->where('shipment.created_at', '>=', $startTime);
            } else {
                $minId = SaleOrder::where('created_at', '>=', $startTime)->min('id');
                if (!empty($minId)) {
                    $query->where('sale_order.id', '>=', $minId);
                }
                $query->where('sale_order.created_at', '>=', $startTime);
            }
        }

        if (!empty($this->requestData['order_date'][1])) {
            $endTime = Carbon::parse($this->requestData['order_date'][1])->endOfDay()->toDateTimeString();
            if (count(array_intersect($this->selectedSlugs, ['ship_date'])) > 0) {
                $query->where('shipment.created_at', '<=', $endTime);
            } else {
                $query->where('sale_order.created_at', '<=', $endTime);
            }
        }

        if (!empty($this->requestData['product_types'])) {
            $query->whereIn('product_style.type', $this->requestData['product_types']);
        }

        if (!empty($this->requestData['warehouse'])) {
            $query->where('sale_order.warehouse_id', $this->requestData['warehouse']);
        }
        if (!empty($this->requestData['order_type'])) {
            $query->whereIn('sale_order.order_type', $this->requestData['order_type']);
        }
        if (!empty($this->requestData['store_id']) && empty($this->requestData['is_all_store'])) {
            $query->where('sale_order.store_id', $this->requestData['store_id']);
        }

        if (!empty($this->requestData['order_status'])) {
            $query->whereIn('sale_order.order_status', $this->requestData['order_status']);
        }

        if (!empty($this->requestData['production_status'])) {
            $query->whereIn('sale_order.production_status', $this->requestData['production_status']);
        }

        $data = collect();
        $i = 1;
        $query->chunkById(5000, function ($chunk) use ($data, &$i) {
            echo "Chunk $i has " . $chunk->count() . ' items, time: ' . Carbon::now()->format('Y-m-d H:i:s') . PHP_EOL;
            try {
                foreach ($chunk as $item) {
                    $data->push($this->transformData($item));
                }
            } catch (\Throwable $th) {
                \Log::error($th->getMessage());
            }
            $i++;
        }, 'sale_order.id', 'id');

        return $data;
    }

    public function transformData($item): array
    {
        $data = array_map(fn ($slug) => data_get($item, $slug), $this->selectedSlugs);
        array_pop($data);

        return $data;
    }

    public function headings(): array
    {
        return $this->header;
    }
}
