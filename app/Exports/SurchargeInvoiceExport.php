<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\SaleOrder;
use App\Models\SurchargeService;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

// TODO: Change to queue for handle large data
class SurchargeInvoiceExport implements WithMultipleSheets
{
    protected Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function sheets(): array
    {
        $allSaleOrderIds = collect(InvoiceSaleOrder::where('invoice_id', $this->invoice->id)->pluck('sale_order_id'));
        $chunkSaleOrderIds = $allSaleOrderIds->chunk(500);
        $embroideryApiValues = [
            SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES,
            SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES,
            SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES,
            SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES,
        ];
        $hasEmbroiderySurcharge = false;
        foreach ($chunkSaleOrderIds as $saleOrderIds) {
            $hasEmbroiderySurcharge = SaleOrder::query()
                ->join('sale_order_item_surcharge', 'sale_order.id', '=', 'sale_order_item_surcharge.order_id')
                ->join('store_surcharge', 'store_surcharge.id', '=', 'sale_order_item_surcharge.surcharge_id')
                ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
                ->whereIn('sale_order.id', $saleOrderIds)
                ->whereIn('surcharge_service.api_value', $embroideryApiValues)
                ->exists();
            if ($hasEmbroiderySurcharge) {
                break;
            }
        }
        $sheets = [
            'Sheet1' => new SurchargeInvoiceStandardExport($this->invoice),
        ];
        if ($hasEmbroiderySurcharge) {
            $sheets['Sheet2'] = new SurchargeInvoiceEmbExport($this->invoice);
        }

        return $sheets;
    }
}
