<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrderInsert;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// TODO: Change to queue for handle large data
class InsertInvoiceExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'A') {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        $data = collect();
        InvoiceSaleOrderInsert::query()
            ->select(
                'sale_order_insert_calculate_price.*',
                'sale_order.store_id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order.order_status',
                'sale_order.order_date',
                'sale_order.created_at as order_created_at',
                'sale_order.order_time',
                'shipment.created_at as shipment_created_at',
                'shipment.tracking_number',
                'sale_order_address.state',
                'sale_order_address.country',
            )
            ->join('sale_order', 'invoice_sale_order_insert.sale_order_id', '=', 'sale_order.id')
            ->leftJoin('shipment', 'sale_order.shipment_id', 'shipment.id')
            ->join('sale_order_address', function ($join) {
                $join->on('sale_order.id', 'sale_order_address.order_id')
                    ->where('sale_order_address.type_address', '=', 'to_address');
            })
            ->join('sale_order_insert_calculate_price', 'sale_order_insert_calculate_price.order_insert_id', '=', 'invoice_sale_order_insert.order_insert_id')
            ->where('invoice_sale_order_insert.invoice_id', $this->invoice->id)
            ->orderBy('sale_order_insert_calculate_price.id', 'ASC')
            ->chunkById(1000, function ($chunk) use (&$data, &$hasErrorInsertInvoice) {
                try {
                    foreach ($chunk as $item) {
                        $data->push($this->transformData($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
            }, 'sale_order_insert_calculate_price.id', 'id');

        return $data;
    }

    public function headings(): array
    {
        $header = [
            $this->invoice->store->name . ' Order #',
            'Print Provider Order #',
            'Status',
            'Ordered SKU',
            'Shipped SKU',
            'Order Created',
            'Ship Date',
            'Item Quantity',
            'Blank costs',
            'Print costs',
            'Handling costs',
            'Total product costs',
            'Ship State',
            'Country',
            'Tracking Number',
            'Note',
        ];

        return $header;
    }

    private function transformData($row)
    {
        $quantity = $row->qty;
        $printPrice = $row->unit_price - $row->blank_price - $row->handling_fee;
        $blankCosts = $row->blank_price * $quantity;
        $printCosts = $printPrice * $quantity;
        $handlingCosts = $row->handling_fee * $quantity;

        $data = [
            $row->external_number,
            $row->order_number,
            $row->order_status,
            $row->product_sku,
            $row->product_sku,
            date('m/d/Y H:i', strtotime($row->order_created_at)),
            $row->shipment_created_at ? date('m/d/Y H:i', strtotime($row->shipment_created_at)) : '',
            $row->qty,
            $blankCosts,
            $printCosts,
            $handlingCosts,
            $row->amount_paid,
            $row->state,
            $row->country,
            $row->tracking_number,
            $row->reason,
        ];

        return $data;
    }
}
