<?php

namespace App\Exports;

use App\Repositories\AdjustPullingShelvesRepository;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportAdjustPullingShelves implements FromCollection, WithHeadings, WithMapping
{
    protected array $requestData;
    protected bool $isAdmin = false;

    public function __construct($requestData)
    {
        $this->requestData = $requestData;

        $this->isAdmin = $requestData['user']['is_admin'] == 1 ? true : false;
    }

    public function collection()
    {
        $result = resolve(AdjustPullingShelvesRepository::class)->getList($this->requestData, true);

        return collect($result);
    }

    public function headings(): array
    {
        $headings = [
            'ID',
            'Date',
            'Product',
            'Available',
            'On Hand',
            'Adjust',
        ];

        if ($this->isAdmin) {
            $headings[] = 'Cost Value On Hand';
            $headings[] = 'Cost Value Adjusted';
        }

        $headings[] = 'User';
        $headings[] = 'Employee';

        return $headings;
    }

    public function map($item): array
    {
        $row = [
            $item->id,
            \Carbon\Carbon::parse($item->created_at)->timezone('America/Los_Angeles')->format('M d, Y g:i A'),
            $item->product->name ?? '',
            (string) $item->product_available,
            (string) $item->product_on_hand,
            (string) ($item->product_on_hand - $item->product_available),
        ];

        if ($this->isAdmin) {
            $costValueOnHand = trim('$' . $item->cost_value_on_hand);
            $costValueAdjusted = trim('$' . $item->cost_value_adjusted);

            $row[] = $costValueOnHand != '$' ? $costValueOnHand : '';
            $row[] = $costValueAdjusted != '$' ? $costValueAdjusted : '';
        }

        $row[] = $item->user->username ?? '';
        $row[] = $item->employee->name ?? '';

        return $row;
    }
}
