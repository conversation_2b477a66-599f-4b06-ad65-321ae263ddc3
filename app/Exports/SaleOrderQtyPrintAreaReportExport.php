<?php

namespace App\Exports;

use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use App\Models\Warehouse;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '4096M');

class SaleOrderQtyPrintAreaReportExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function collection()
    {
        $query = SaleOrder::from('sale_order as so')
            ->selectRaw("
                DATE_FORMAT(soib.print_barcode_at, '%Y-%m') as month_report,
                so.warehouse_id as so_warehouse_id,
                COUNT(*) as printing_volume
            ")
            ->join('sale_order_item_barcode as soib', 'so.id', '=', 'soib.order_id')
            ->join('sale_order_item_image as soii', 'soib.order_item_id', '=', 'soii.order_item_id')
            ->where('so.is_test', SaleOrder::NOT_TEST)
            ->where('so.order_status', SaleOrder::SHIPPED);

        if (!empty($this->input['start_date'])) {
            $startDate = Carbon::parse(trim($this->input['start_date']))->startOfMonth()->format('Y-m-d H:i:s');
            $item = SaleOrderItemBarcode::selectRaw('MIN(id) as id')
                ->where('print_barcode_at', '>=', $startDate)
                ->first();

            if (empty($item->id)) {
                return collect([]);
            }

            $query->where('soib.id', '>=', $item->id);
            $query->where('soib.print_barcode_at', '>=', $startDate);
        }

        if (!empty($this->input['end_date'])) {
            $endDate = Carbon::parse(trim($this->input['end_date']))->endOfMonth()->format('Y-m-d H:i:s');
            $item = SaleOrderItemBarcode::selectRaw('MAX(id) as id')
                ->where('print_barcode_at', '<=', $endDate)
                ->first();

            if (empty($item->id)) {
                return collect([]);
            }

            $query->where('soib.id', '<=', $item->id);
            $query->where('soib.print_barcode_at', '<=', $endDate);
        }

        $data = [];
        $items = $query->groupBy(['so.warehouse_id', 'month_report'])
            ->orderBy('month_report')
            ->orderBy('so_warehouse_id')
            ->get();

        if (!empty($this->input['warehouse_ids'])) {
            $rawWarehouseIds = explode(',', $this->input['warehouse_ids']);
            $rawWarehouseIds = array_map('trim', $rawWarehouseIds);
            $warehouseIds = Warehouse::whereIn('id', $rawWarehouseIds)
                ->pluck('name', 'id')
                ->toArray();
        } else {
            $warehouseIds = Warehouse::all()
                ->pluck('name', 'id')
                ->toArray();
        }

        foreach ($items as $item) {
            if (array_key_exists($item->so_warehouse_id, $warehouseIds)) {
                $data[] = [
                    'month_report' => $item->month_report,
                    'w_name' => $warehouseIds[$item->so_warehouse_id],
                    'printing_volume' => $item->printing_volume,
                ];
            }
        }

        return collect($data);
    }

    public function headings(): array
    {
        return ['Month', 'Name', 'Printing volume'];
    }
}
