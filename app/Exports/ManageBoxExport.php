<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ManageBoxExport implements FromView
{
    protected $collection;
    protected $isAdmin;

    public function __construct($collection, $isAdmin = false)
    {
        $this->collection = $collection;
        $this->isAdmin = $isAdmin;
    }

    public function view(): View
    {
        $data = $this->collection
            ->groupBy('sku')
            ->map(function ($items, $sku) {
                $name = $items->first()['name'];
                $boxQuantity = $items->count();
                $itemQuantity = $items->sum('quantity');
                $costValue = number_format($items->sum(fn ($item) => $item['cost_value']), 2, '.', '');

                $locations = $items->groupBy('location_id')->map(function ($locationItems, $locationId) {
                    $barcode = $locationItems->first()['barcode'];
                    $boxQuantity = $locationItems->count();
                    $itemQuantity = $locationItems->sum('quantity');
                    $costValue = number_format($locationItems->sum(fn ($item) => $item['cost_value']), 2, '.', '');

                    return [
                        'location_id' => $locationId,
                        'barcode' => $barcode,
                        'box_quantity' => $boxQuantity,
                        'item_quantity' => $itemQuantity,
                        'cost_value' => $costValue,
                    ];
                })->values();

                return [
                    'product_sku' => $sku,
                    'product_name' => $name,
                    'box_quantity' => $boxQuantity,
                    'item_quantity' => $itemQuantity,
                    'cost_value' => $costValue,
                    'location' => 'TOTAL',
                    'location_items' => $locations,
                ];
            })
            ->values()
            ->toArray();

        return view('manage_box_export', [
            'data' => $data,
            'is_admin' => $this->isAdmin,
        ]);
    }
}
