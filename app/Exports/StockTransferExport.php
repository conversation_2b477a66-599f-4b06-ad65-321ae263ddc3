<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class StockTransferExport implements FromCollection, WithHeadings
{
    protected $stockTransferId;
    protected $isAdmin;

    public function __construct($stockTransferId, $isAdmin = false)
    {
        $this->stockTransferId = $stockTransferId;
        $this->isAdmin = $isAdmin;
    }

    public function collection()
    {
        $data = collect();
        DB::table('stock_transfer as s')
            ->join('stock_transfer_box_log as log', 's.id', '=', 'log.stock_transfer_id')
            ->join('box', 'log.box_id', '=', 'box.id')
            ->leftJoin('inventory_addition as ia', 'ia.box_id', '=', 'box.id')
            ->leftJoin('purchase_order_item as poi', function ($join) {
                $join->on('poi.product_id', '=', 'ia.product_id')
                    ->on('ia.po_id', '=', 'poi.po_id');
            })
            ->join('product as p', 'p.id', '=', 'box.product_id')
            ->join('warehouse as w', 'w.id', '=', 's.destination_warehouse_id')
            ->leftJoin('employee as e', 'e.id', '=', 's.fulfill_by')
            ->where('s.id', $this->stockTransferId)
            ->select(
                's.updated_at as fulfill_at',
                'p.name as product_name',
                'p.sku as product_sku',
                'box.barcode as box_id',
                'log.quantity',
                'poi.price',
                DB::raw('poi.price * log.quantity as total_value'),
                'e.name as employee',
                'w.name as destination',
            )
            ->orderBy('s.id')
            ->chunk(500, function ($chunk) use ($data) {
                foreach ($chunk as $item) {
                    $row = [
                        $item->fulfill_at,
                        $item->product_name,
                        $item->product_sku,
                        $item->box_id,
                        $item->quantity,
                    ];

                    if ($this->isAdmin) {
                        $row[] = $item->price;
                        $row[] = $item->total_value;
                    }

                    $row[] = $item->employee;
                    $row[] = $item->destination;

                    $data->push($row);
                }
            });

        return $data;
    }

    public function headings(): array
    {
        $headings = ['Date', 'Product', 'SKUs', 'Box ID', 'Quantity'];

        if ($this->isAdmin) {
            $headings[] = 'Price';
            $headings[] = 'Total Value';
        }

        $headings[] = 'Employee';
        $headings[] = 'Destination';

        return $headings;
    }
}
