<?php

namespace App\Exports;

use App\Http\Service\AlertService;
use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// TODO: Change to queue for handle large data
class ProductInvoiceExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected Invoice $invoice;

    protected $printSide;

    const PRINT_AREA_HEADER = [
        'Front', 'Back', 'Left Sleeve', 'Right Sleeve', 'Outer Neck Label',
        'Inner Neck Label', 'Pocket', 'Above Pocket', 'DTF B', 'DTF F', 'Front DTF', 'DTF Pocket',
        'DTF Above Pocket', '3DF', 'Bleed Mug', 'Maxprint', 'Die Cut', 'Poster', 'Film', 'Full Print',
        'White Border', 'Blank', 'Emb Back Large', 'Emb Back Medium', 'Emb Back Small', 'Emb Front Large',
        'Emb Front Medium', 'Emb Front Small', 'EMB Gelato Back Large', 'EMB Gelato Front Large', 'Emb Left Chest', 'EMB Left Sleeve',
        'EMB Left Wrist', 'EMB Right Sleeve', 'EMB Right Wrist'
    ];

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->invoice->load(['store']);
        $storeId = $invoice->store_id;

        $globalPrintSideQuery = DB::table('product_print_side')
            ->select('name', 'code_wip')
            ->whereNotIn('id', function ($query) {
                $query->select('product_print_side_id')
                    ->from('product_print_side_store');
            });

        $customPrintSideQuery = DB::table('product_print_side as pps')
            ->select('pps.name', 'pps.code_wip')
            ->join('product_print_side_store as ppss', 'pps.id', '=', 'ppss.product_print_side_id')
            ->where('ppss.store_id', $storeId);

        $printSide = $globalPrintSideQuery
            ->union($customPrintSideQuery)
            ->get()
            ->pluck('name', 'code_wip');

        $flipped = $printSide->flip();
        $sorted = collect(self::PRINT_AREA_HEADER)
            ->filter(fn ($name) => $flipped->has($name))
            ->mapWithKeys(fn ($name) => [$flipped[$name] => $name]);
        $remaining = $printSide->diffKeys($sorted);
        $this->printSide = $sorted
            ->merge($remaining)
            ->map(function ($name) {
                return in_array($name, ['EMB Gelato Back Large', 'EMB Gelato Front Large'])
                    ? $name
                    : $name . ' Print';
            })
            ->toArray();
    }

    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'A') {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        $data = collect();
        if ($this->invoice->has_error) {
            $this->makeDataInvoiceErrors($data);
            $this->makeDataItemCorrectForSaleOrderError($data);
        }
        $i = 1;
        $allSaleOrderIds = InvoiceSaleOrder::where('invoice_id', $this->invoice->id)->pluck('sale_order_id');
        $chunkSaleOrderIds = $allSaleOrderIds->chunk(500);
        foreach ($chunkSaleOrderIds as $saleOrderIds) {
            SaleOrderItem::whereIn('sale_order.id', $saleOrderIds)
                ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
                ->leftJoin('shipment', 'sale_order.shipment_id', 'shipment.id')
                ->leftJoin('promotion', 'sale_order_item.promotion_id', 'promotion.id')
                ->join('sale_order_address', function ($join) {
                    $join->on('sale_order.id', 'sale_order_address.order_id')
                        ->where('sale_order_address.type_address', '=', 'to_address');
                })
                ->select([
                    'sale_order_item.id as id',
                    'sale_order_item.external_id as external_id',
                    'sale_order_item.order_id',
                    'sale_order_item.quantity',
                    'sale_order_item.product_id',
                    'sale_order_item.sku',
                    'sale_order_item.product_sku',
                    'sale_order_item.options',
                    'sale_order_item.unit_price',
                    'sale_order_item.blank_price',
                    'sale_order_item.print_sides',
                    'sale_order_item.handling_fee',
                    'sale_order.id as sale_order_id',
                    'sale_order.store_id',
                    'sale_order.order_type',
                    'sale_order.tag',
                    'sale_order.order_number',
                    'sale_order.external_number',
                    'sale_order.order_status',
                    'sale_order.order_date',
                    'shipment.ship_date',
                    'sale_order.created_at',
                    'shipment.created_at as shipment_created_at',
                    'shipment.tracking_number',
                    'promotion.discount',
                    'sale_order_address.state',
                    'sale_order_address.country',
                ])
                ->chunkById(1000, function ($chunk) use ($data, &$i) {
                    echo "Chunk $i has " . $chunk->count() . ' items, time: ' . Carbon::now()->format('Y-m-d H:i:s') . PHP_EOL;
                    try {
                        foreach ($chunk as $item) {
                            $data->push($this->transformData($item));
                        }
                    } catch (\Throwable $th) {
                        \Log::error($th->getMessage());
                    }
                    $i++;
                }, 'sale_order_item.id', 'id');
        }

        $this->alertGenerateInvoice($this->invoice);

        return $data;
    }

    private function alertGenerateInvoice($invoice)
    {
        try {
            $timePeriod = Carbon::parse($invoice->start_at)->format('M j, Y') . ' - ' . Carbon::parse($invoice->end_at)->format('M j, Y');
            $storeName = $invoice->store->name;
            $invoiceType = ucfirst($invoice->type);

            if ($invoice->has_error) {
                $message = "Store: $storeName - $invoiceType invoice: $timePeriod" . PHP_EOL;
                $message .= 'Error occurred while generating the invoice. Please check the SwiftPOD app for further details.';
            } elseif ($invoice->created_at != $invoice->updated_at) {
                $message = "Store: $storeName - $invoiceType invoice: $timePeriod" . PHP_EOL;
                $message .= 'The invoice regeneration process is complete. Please review it in the SwiftPOD app.';
            }

            if (!empty($message)) {
                $alertService = new AlertService();
                $alertService->alertInvoice($message);
            }
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }
    }

    public function headings(): array
    {
        $header = [
            $this->invoice->store->name . ' Order #',
            'Print Provider Order #',
            'Order Type',
            'Status',
            'External ID',
            'Ordered SKU',
            'Shipped SKU',
            'Order Created',
            'Ship Date',
            'Item Quantity',
            'Blank costs',
            'Print costs',
        ];
        if (!empty($this->printSide)) {
            $header = array_merge($header, array_values($this->printSide));
        }
        $header = array_merge($header, [
            'Discount',
            'Total product costs',
            'Tax Rate',
            'Tax',
            'Total product with cost with tax',
            'Ship State',
            'Country',
            'Tracking Number',
            'Note',
        ]);

        return $header;
    }

    private function transformData($row)
    {
        $quantity = $row->quantity;
        $printPrice = $row->unit_price - $row->blank_price - $row->handling_fee;

        $blankCosts = $row->blank_price * $quantity;
        $printCosts = $printPrice * $quantity;
        $handlingCosts = $row->handling_fee * $quantity;
        $totalCosts = ($blankCosts * (1 - ($row->discount / 100))) + $printCosts;
        // TODO: clear to tax rate
        $taxRate = 0;
        $tax = $totalCosts * $taxRate;

        $data = [
            $row->external_number,
            $row->order_number,
            SaleOrder::transformOrderType($row->order_type, $row->tag),
            $row->order_status,
            $row->external_id,
            $row->product_sku,
            $row->product_sku,
            date('m/d/Y H:i', strtotime($row->created_at)),
            $row->shipment_created_at ? date('m/d/Y H:i', strtotime($row->shipment_created_at)) : '',
            $quantity,
            $blankCosts,
            $printCosts,
        ];

        if (!empty($this->printSide)) {
            $printSideQuantity = [];
            foreach (array_keys($this->printSide) as $key => $side) {
                $printSideQuantity[] = strpos($row->print_sides, $side) !== false ? $quantity : '';
            }
            $data = array_merge($data, $printSideQuantity);
        }

        $data = array_merge($data, [
            $row->discount,
            $totalCosts,
            '', // TODO: tax rate
            '', // TODO: tax
            $totalCosts + $tax,
            $row->state,
            strtoupper($row->country),
            $row->tracking_number,
            ''
        ]);

        return $data;
    }

    private function makeDataInvoiceErrors(&$data)
    {
        $i = 1;
        SaleOrderItem::with('invoiceErrors:order_item_id,reason')
            ->whereHas('invoiceErrors')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('invoice_sale_order_error', 'invoice_sale_order_error.sale_order_id', '=', 'sale_order.id')
            ->where('invoice_sale_order_error.invoice_id', $this->invoice->id)
            ->select([
                'sale_order_item.id as id',
                'sale_order_item.external_id as external_id',
                'sale_order_item.order_id',
                'sale_order_item.quantity',
                'sale_order_item.product_id',
                'sale_order_item.sku',
                'sale_order_item.product_sku',
                'sale_order.id as sale_order_id',
                'sale_order.store_id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order.order_status',
                'sale_order.order_date',
                'sale_order.created_at',
            ])->chunkById(1000, function ($chunk) use ($data, &$i) {
                try {
                    echo "Chunk $i has " . $chunk->count() . ' items' . PHP_EOL;
                    foreach ($chunk as $item) {
                        $data->push($this->transformDataErrors($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
                $i++;
            }, 'sale_order_item.id', 'id');

        return $data;
    }

    private function makeDataItemCorrectForSaleOrderError(&$data)
    {
        $i = 1;
        SaleOrderItem::query()
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('invoice_sale_order_error', 'invoice_sale_order_error.sale_order_id', '=', 'sale_order.id')
            ->leftJoin('sale_order_item_calculate_failed', 'sale_order_item.id', '=', 'sale_order_item_calculate_failed.order_item_id')
            ->leftJoin('shipment', 'sale_order.shipment_id', 'shipment.id')
            ->leftJoin('promotion', 'sale_order_item.promotion_id', 'promotion.id')
            ->join('sale_order_address', function ($join) {
                $join->on('sale_order.id', 'sale_order_address.order_id')
                    ->where('sale_order_address.type_address', '=', 'to_address');
            })
            ->where('invoice_sale_order_error.invoice_id', $this->invoice->id)
            ->whereNull('sale_order_item_calculate_failed.order_item_id')
            ->select([
                'sale_order_item.id as id',
                'sale_order_item.external_id as external_id',
                'sale_order_item.order_id',
                'sale_order_item.quantity',
                'sale_order_item.product_id',
                'sale_order_item.sku',
                'sale_order_item.product_sku',
                'sale_order_item.options',
                'sale_order_item.unit_price',
                'sale_order_item.blank_price',
                'sale_order_item.print_sides',
                'sale_order_item.handling_fee',
                'sale_order.id as sale_order_id',
                'sale_order.store_id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order.order_status',
                'sale_order.order_date',
                'shipment.ship_date',
                'sale_order.created_at',
                'shipment.created_at as shipment_created_at',
                'shipment.tracking_number',
                'promotion.discount',
                'sale_order_address.state',
                'sale_order_address.country',
            ])
            ->chunkById(1000, function ($chunk) use ($data, &$i) {
                echo "Chunk $i has " . $chunk->count() . ' items' . PHP_EOL;
                try {
                    foreach ($chunk as $item) {
                        $data->push($this->transformData($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
                $i++;
            }, 'sale_order_item.id', 'id');

        return $data;
    }

    private function transformDataErrors($row): array
    {
        $reasonString = $row->invoiceErrors->pluck('reason')->implode(', ');
        $data = [
            $row->external_number,
            $row->order_number,
            $row->order_status,
            $row->external_id,
            $row->product_sku ?? '',
            $row->product_sku ?? '',
            date('m/d/Y H:i', strtotime($row->created_at)),
            '',
            '',
            '',
            '',
        ];
        if (!empty($this->printSide)) {
            $printSideQuantity = array_fill(0, count($this->printSide), '');
            $data = array_merge($data, $printSideQuantity);
        }

        $data = array_merge($data, [
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            $reasonString
        ]);

        return $data;
    }
}
