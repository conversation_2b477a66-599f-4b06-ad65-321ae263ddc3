<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\RedbubblePacking;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

// TODO: Change to queue for handle large data
class PackingInvoiceRedBubbleExport implements FromCollection, WithHeadings
{
    protected Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->invoice->load(['store']);
    }

    public function collection()
    {
        setTimezone();
        $groupedData = [];
        $redBubblePacking = RedbubblePacking::all();
        DB::table('invoice_sale_order')
            ->leftJoin('sale_order', 'invoice_sale_order.sale_order_id', '=', 'sale_order.id')
            ->leftJoin('shipment_package', 'shipment_package.order_id', '=', 'sale_order.id')
            ->leftJoin('warehouse', 'warehouse.id', '=', 'sale_order.warehouse_id')
            ->select([
                'order_quantity',
                'shipment_package.sku',
                'warehouse.city',
                'warehouse.state',
                'warehouse.country'
            ])
            ->where('invoice_id', $this->invoice->id)
            ->orderBy('invoice_sale_order.sale_order_id')
            ->chunk(1000, function ($packingDataChunk) use ($redBubblePacking, &$groupedData) {
                foreach ($packingDataChunk as $item) {
                    $check = $redBubblePacking->first(function ($itemPacking) use ($item) {
                        if ($item->sku == RedbubblePacking::CUSTOM_BOX_SKU) {
                            return $item->sku == $itemPacking->rb_sku
                                && $item->order_quantity >= $itemPacking->min_quantity
                                && $item->order_quantity <= $itemPacking->max_quantity;
                        }

                        return $item->sku == $itemPacking->rb_sku;
                    });
                    if (!$check) {
                        echo "sku $item->sku not found" . PHP_EOL;

                        continue;
                    }
                    $key = $check->rb_sku . '_' . ($check->size ?? '');
                    $facility = $item->city . '-' . $item->state . '-' . $item->country;
                    if (!isset($groupedData[$key])) {
                        $groupedData[$key] = [
                            'date' => date('Y-m-d', strtotime($this->invoice->created_at)),
                            'invoice_number' => $this->invoice->invoice_number,
                            'fulfiller_name' => 'Swiftpod',
                            'facility' => $facility,
                            'packaging_id' => $check->packaging_id,
                            'rb_sku' => $check->rb_sku,
                            'size' => $check->size ?? null,
                            'preferred_packaging' => $check->preferred_packaging,
                            'units_used' => 0,
                            'currency' => 'USD',
                            'packaging_supplier_cost' => $check->packaging_supplier_cost,
                            'taxes' => '0',
                            'inbound_cost' => '0',
                            'surcharge_cost' => '0',
                            'discount' => '0',
                        ];
                    }
                    $groupedData[$key]['units_used'] += 1;
                    $groupedData[$key]['packaging_supplier_cost'] = $check->packaging_supplier_cost;
                }
            });
        foreach ($groupedData as &$item) {
            $item['totalBilled'] = $item['units_used'] * $item['packaging_supplier_cost'];
        }

        return collect(array_values($groupedData));
    }

    public function headings(): array
    {
        return [
            'invoice_date',
            'invoice_number',
            'fulfiller_name',
            'facility',
            'packaging_id',
            'rb_sku',
            'size',
            'preferred_packaging',
            'Packaging_Units_Used',
            'currency',
            'unit_cost',
            'taxes',
            'inbound_cost',
            'surcharge_cost',
            'discount',
            'total_billed',
        ];
    }
}
