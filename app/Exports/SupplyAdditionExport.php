<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '3072M');

class SupplyAdditionExport implements FromCollection, WithHeadings
{
    protected $collection;

    public function __construct($collection)
    {
        $this->collection = $collection;
    }

    public function collection()
    {
        $data = [];
        $this->collection->each(function ($item) use (&$data) {
            $data[] = [
                'date' => $item->created_at,
                'supply_name' => $item->supply?->name,
                'sku' => $item->supply?->sku,
                'po_number' => $item->supplyPurchaseOrder->po_number,
                'quantity' => $item->quantity,
                'user' => $item->user?->username,
                'employee' => $item->employee?->name,
            ];
        });

        return collect($data);
    }

    public function headings(): array
    {
        return ['Date', 'Supply Name', 'SKU', 'PO#', 'Quantity', 'User', 'Employee'];
    }
}
