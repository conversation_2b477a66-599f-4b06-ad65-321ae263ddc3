<?php

namespace App\Exports;

use App\Models\SaleOrder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '3072M');

class SaleOrderReportIExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function collection()
    {
        setTimezone();
        $query = SaleOrder::query()
            ->selectRaw('
                store.name as `customer`,
                sale_order_item.product_sku as `sku`,
                SUM(sale_order_item.quantity) as `qty_sold`,
                SUM(sale_order_item.amount_paid / sale_order_item.quantity) / COUNT(sale_order_item.id)  as `selling_price`,
                product.name as `product_name`,
                product_style.type as `product_type`
            ')
            ->join(DB::raw('sale_order_item FORCE INDEX (created_at)'), 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('store', 'sale_order.store_id', '=', 'store.id')
            ->leftJoin('product', 'product.id', '=', 'sale_order_item.product_id')
            ->leftJoin('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->whereIn('sale_order.order_status', [SaleOrder::SHIPPED, SaleOrder::STATUS_IN_PRODUCTION_CANCELLED])
            ->where('sale_order_item.amount_paid', '>', 0);

        if (!empty($this->input['start_date'])) {
            $startDate = Carbon::parse(trim($this->input['start_date']))->startOfDay()->format('Y-m-d H:i:s');
            $query->where('sale_order_item.created_at', '>=', $startDate);
        }

        if (!empty($this->input['end_date'])) {
            $endDate = Carbon::parse(trim($this->input['end_date']))->endOfDay()->format('Y-m-d H:i:s');
            $query->where('sale_order_item.created_at', '<=', $endDate);
        }

        if (!empty($this->input['store_ids'])) {
            $storeIds = explode(',', $this->input['store_ids']);
            $storeIds = array_map('trim', $storeIds);
            $query->whereIn('sale_order_item.store_id', $storeIds);
        }

        $data = [];
        $grandTotalQty = 0;
        $grandTotalSellingPrice = 0;
        $grandTotalPrice = 0;
        $query->groupBy(['sale_order_item.store_id', 'sale_order_item.product_sku']);

        if (empty($this->input['store_ids'])) {
            $query->orderBy('store.name', 'asc');
        }

        $query->chunk(10000, function ($items) use (&$data, &$grandTotalQty, &$grandTotalSellingPrice, &$grandTotalPrice) {
            foreach ($items as $item) {
                $qtySold = $item->qty_sold ? round($item->qty_sold, 2) : 0;
                $sellingPrice = $item->selling_price ? round($item->selling_price, 2) : 0;
                $totalPrice = round($qtySold * ($sellingPrice > 0 ? $sellingPrice : 1), 2);
                $data[] = [
                    'customer' => $item->customer ?? '',
                    'sku' => $item->sku ?? '',
                    'product_name' => $item->product_name,
                    'product_type' => $item->product_type,
                    'qty_sold' => $qtySold,
                    'selling_price' => $sellingPrice,
                    'total_price' => $totalPrice,
                ];

                // Cộng dồn tổng số lượng và giá bán
                $grandTotalQty += $qtySold;
                $grandTotalSellingPrice += $sellingPrice;
                $grandTotalPrice += $totalPrice;
            }
        });
        // Sau khi hoàn thành tất cả chunks, thêm dòng GRAND TOTAL
        if (!empty($data)) {
            $data[] = [
                'customer' => 'GRAND TOTAL',
                'sku' => '',
                'product_name' => '',
                'product_type' => '',
                'qty_sold' => $grandTotalQty,
                'selling_price' => $grandTotalSellingPrice,
                'total_price' => $grandTotalPrice,
            ];
        }

        return collect($data);
    }

    public function headings(): array
    {
        return ['Customer', 'SKU', 'Product Name', 'Product Type', 'Qty sold', 'Selling price/unit', 'Total Price'];
    }
}
