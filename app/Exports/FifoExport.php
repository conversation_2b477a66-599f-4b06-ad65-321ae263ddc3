<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '2048M');

class FifoExport implements FromCollection, WithHeadings
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $data = $this->setUpData();

        return collect($data);
    }

    public function headings(): array
    {
        return ['ID', 'SKU', 'Style', 'Color', 'Size', 'Product Description', 'Total', 'Start Date', 'End Date', 'Start Unit', 'End Unit', 'Cost Avg'];
    }

    public function setUpData(): array
    {
        if (empty($this->request['start_date']) || empty($this->request['end_date']) || empty($this->request['warehouse_id'])) {
            $data = [];
        } else {
            $inventoryData = DB::table('fifo_inventory')
            ->where('warehouse_id', $this->request['warehouse_id'])
            ->whereDate('start_date', $this->request['start_date'])
            ->whereDate('end_date', $this->request['end_date'])
            ->join('product', 'product.id', '=', 'fifo_inventory.product_id')
            ->selectRaw('
                product.sku,
                product.style,
                product.color as product_color,
                product.size as product_size,
                product.description as product_description,
                fifo_inventory.*
            ')
            ->get();

            $data = [];
            foreach ($inventoryData as $inventory) {
                $data[] = [
                    'product_id' => $inventory->product_id,
                    'sku' => $inventory->sku,
                    'product_style' => $inventory->product_style,
                    'product_color' => $inventory->product_color,
                    'product_size' => $inventory->product_size,
                    'product_description' => $inventory->product_description,
                    'value' => strval($inventory->value),
                    'start_date' => $inventory->start_date,
                    'end_date' => $inventory->end_date,
                    'start_unit' => strval($inventory->start_unit),
                    'end_unit' => strval($inventory->end_unit),
                    'cost_avg' => !empty($inventory->end_unit) ? strval(round($inventory->value / $inventory->end_unit, 2)) : '0',
                ];
            }
        }

        return $data;
    }
}
