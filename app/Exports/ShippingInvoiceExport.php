<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\PeakShippingFee;
use App\Models\SaleOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// TODO: Change to queue for handle large data
class ShippingInvoiceExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->invoice->load(['store']);
    }

    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'A') {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        $data = collect();
        SaleOrder::query()
            ->join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
            ->join('sale_order_address', function ($join) {
                $join->on('sale_order.id', 'sale_order_address.order_id')
                    ->where('sale_order_address.type_address', 'to_address');
            })
            ->join('invoice_sale_order', 'invoice_sale_order.sale_order_id', '=', 'sale_order.id')
            ->leftJoinSub(
                DB::table('sale_order_surcharge')
                    ->select('order_id', DB::raw('MAX(id) as max_id'))
                    ->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)
                    ->groupBy('order_id'),
                'max_surcharge',
                function ($join) {
                    $join->on('sale_order.id', '=', 'max_surcharge.order_id');
                },
            )
            ->leftJoin('sale_order_surcharge', 'sale_order_surcharge.id', '=', 'max_surcharge.max_id')
            ->where('invoice_sale_order.invoice_id', $this->invoice->id)
            ->select([
                'shipment.id as id',
                'shipment.ship_date',
                'shipment.created_at as shipment_created_at',
                'shipment.shipment_cost',
                'shipment.tracking_number',
                'shipment.carrier_code',
                'shipment.service_code',
                'shipment.weight_value',
                'shipment.weight_unit',
                'sale_order_address.city',
                'sale_order_address.state',
                'sale_order_address.zip',
                'sale_order_address.country',
                'sale_order.id as sale_order_id',
                'sale_order.store_id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order.order_status',
                'sale_order.order_date',
                'sale_order.created_at',
                'sale_order.shipping_calculate',
                'sale_order_address.street1',
                'sale_order_address.street2',
                'sale_order.shipping_method',
                'sale_order_surcharge.value as peak_shipping_fee'
            ])
            ->chunkById(1000, function ($chunk) use ($data) {
                try {
                    foreach ($chunk as $item) {
                        $data->push($this->transformData($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
            }, 'shipment.id', 'id');

        return $data;
    }

    public function headings(): array
    {
        return [
            $this->invoice->store->name . ' Order #',
            'Print Provider Order #',
            'Order Created',
            'Ship Date',
            'Ship Cost',
            'Tax',
            'Ship cost with tax',
            'Tracking Number',
            'City',
            'State',
            'Zip',
            'Street',
            'Country',
            'Weight (lbs)',
            'Carrier',
            'Service',
            'Shipping Method',
            'Peak Shipping Surcharge'
        ];
    }

    private function transformData($row)
    {
        // Calculate oz to lb and g to lb
        $weight = 0;
        switch ($row->weight_unit) {
            case 'ounces':
                $weight = ($row->weight_value ?? 0) * 0.0625;
                break;

            case 'grams':
                $weight = ($row->weight_value ?? 0) * 0.0022046;
                break;

            default:
                $weight = $row->weight_value ?? 0;
                break;
        }
        $peakShippingSurcharge = $row->order_status != SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
        ? ($row->peak_shipping_fee ?? number_format(0, 2))
        : number_format(0, 2);

        return [
            $row->external_number,
            $row->order_number,
            Carbon::parse($row->created_at)->format('m/d/Y H:i'),
            Carbon::parse($row->shipment_created_at)->format('m/d/Y H:i'),
            $row->shipping_calculate,
            '', // TODO: tax
            $row->shipping_calculate, // TODO: shipment with tax
            $row->tracking_number,
            $row->city,
            $row->state,
            $row->zip,
            $row->street1 . '-' . $row->street2,
            strtoupper($row->country),
            $weight,
            $row->carrier_code,
            $row->service_code,
            $row->shipping_method,
            $peakShippingSurcharge
        ];
    }
}
