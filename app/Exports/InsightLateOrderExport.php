<?php

namespace App\Exports;

use App\Models\SaleOrder;
use App\Models\StoreShipment;
use App\Repositories\InsightLateOrderRepository;
use App\Repositories\ShippingMethodRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class InsightLateOrderExport implements FromCollection, WithHeadings
{
    protected array $params;

    protected $shippingMethods;

    public function __construct($request)
    {
        $this->params = $request;
    }

    public function collection(): Collection
    {
        $dataExport = [];
        ['data' => $data] = resolve(InsightLateOrderRepository::class)->getList($this->params, false);
        $this->shippingMethods = resolve(ShippingMethodRepository::class)->fetchName([]);
        $tags = DB::table('tag')
            ->where('source', 'sale_order')
            ->get();

        foreach ($data as $item) {
            $itemTags = explode(',', $item->tag);
            $tagStr = '';

            if (!empty($itemTags)) {
                $dataTags = [];

                foreach ($itemTags as $_val) {
                    if (empty($_val)) {
                        continue;
                    }

                    $r = $tags->where('id', $_val)->first();
                    !empty($r) && $dataTags[] = $r->name;
                }

                $tagStr = !empty($dataTags) ? implode(', ', $dataTags) : '';
            }

            $dataExport[] = [
                'store_name' => $item->store_name,
                'order_number' => $item->order_number,
                'sale_order_item_sku' => $item->sale_order_item_sku,
                'order_date' => $item->order_date,
                'sale_order_item_quantity' => $item->sale_order_item_quantity,
                'order_status_label' => $item->order_status_label,
                'production_status_label' => $item->production_status_label,
                'age' => getAge($item->created_at) ?? '',
                'order_type' => $this->getOrderType($item),
                'tag' => $tagStr,
                'tracking_number' => $item->tracking_number,
            ];
        }

        return collect($dataExport);
    }

    public function headings(): array
    {
        return [
            "Client's Name",
            'Order Number',
            'Item SKU',
            'Order Date',
            'Quantity',
            'Order Status',
            'Production Status',
            'Age',
            'Order Type',
            'Tags',
            'Tracking',
        ];
    }

    public function getOrderType($item): string
    {
        $orderTypes = [];

        if ($item->order_type = SaleOrder::ORDER_TYPE_PRETREATED) {
            $orderTypes[] = 'Pretreated';
        } elseif ($item->order_type = SaleOrder::ORDER_TYPE_BLANK) {
            $orderTypes[] = 'Blank';
        } elseif ($item->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER) {
            $orderTypes[] = 'Label Order';
        }

        if ($item->is_create_manual) {
            $orderTypes[] = 'Manual Order';
        }

        if ($item->is_manual) {
            $orderTypes[] = 'Manual Process';
        }

        if ($item->is_fba_order) {
            $orderTypes[] = 'FBA';
        }

        if ($item->is_xqc) {
            $orderTypes[] = 'XQC';
        }

        if ($item->shipping_method == StoreShipment::SERVICE_EXPRESS) {
            $orderTypes[] = 'Shipping Express';
        } elseif ($item->shipping_method == StoreShipment::SERVICE_STANDARD) {
            $orderTypes[] = 'Shipping Standard';
        } elseif ($item->shipping_method == StoreShipment::SERVICE_PRIORITY) {
            $orderTypes[] = 'Shipping Priority';
        } elseif (!empty($this->shippingMethods)) {
            $method = $this->shippingMethods->where('api_shipping_method', $item->shipping_method)->first();
            !empty($method) && $orderTypes[] = trim("{$method['shipping_carrier']} {$method['shipping_carrier_service']}");
        }

        if (!empty($orderTypes)) {
            return implode(', ', $orderTypes);
        }

        return '';
    }
}
