<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '2048M');

class IPViolationOrderExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function collection()
    {
        $query = DB::table('sale_order_item')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item.id')
            ->join('image_hash', 'sale_order_item_image.image_hash_id', '=', 'image_hash.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('store', 'sale_order_item.store_id', '=', 'store.id')
            ->join('sale_order_address', 'sale_order_address.order_id', '=', 'sale_order_item.order_id')
            ->where('sale_order.order_status', 'rejected')
            ->where('sale_order.rejected_reason', 'ip_violation')
            ->where('sale_order_address.type_address', 'to_address')
            ->where('image_hash.is_ip_violation', 1)
            ->where('sale_order.is_test', 0);
        if (!empty($this->input['start_date'])) {
            $startTime = Carbon::parse($this->input['start_date'])->startOfDay()->toDateTimeString();
            $query->where('sale_order.created_at', '>=', $startTime);
        }
        if (!empty($this->input['end_date'])) {
            $endTime = Carbon::parse($this->input['end_date'])->endOfDay()->toDateTimeString();
            $query->where('sale_order.created_at', '<=', $endTime);
        }
        if (!empty($this->input['store_ids'])) {
            $storeIds = trim($this->input['store_ids']);
            $storeIds = explode(',', $storeIds);
            $storeIds = array_map('trim', $storeIds);
            $query->whereIn('store.id', $storeIds);
        }
        $query->orderBy('sale_order_item.store_id')
            ->orderBy('sale_order_item.id')
            ->groupBy('sale_order_item.id')
            ->selectRaw('
            sale_order.order_number,
            sale_order.external_number,
            sale_order.created_at,
            sale_order.rejected_at,
            sale_order_item.product_sku AS sku,
            sale_order_item.external_id,
            sale_order_item.id,
            sale_order_address.name as customer_name,
            store.name as store_name,
            product.color as product_color_name,
            product.size as product_size_name
        ');
        $data = [];
        $query->chunk(5000, function ($items) use (&$data) {
            $newData = $this->convertData($items->toArray());
            $data = array_merge($data, $newData);
        });

        return collect($data);
    }

    public function headings(): array
    {
        return ['Store', 'Order number', 'Customer’s item ID', 'Print Provider SKU', 'Order date (PST time)', 'Rejected date (PST time)', 'Recipient name', 'Item Color', 'Item Size'];
    }

    public function convertData($data = [])
    {
        $newData = [];
        foreach ($data as $item) {
            $newData[] = [
                'store_name' => $item->store_name ?? '',
                'order_number' => $item->external_number ?? '',
                'external_id' => $item->external_id ?? '',
                'sku' => $item->sku ?? '',
                'order_time' => $item->created_at ?? '',
                'rejected_at' => $item->rejected_at ?? '',
                'customer_name' => $item->customer_name ?? '',
                'product_color_name' => $item->product_color_name ?? '',
                'product_size_name' => $item->product_size_name ?? '',
            ];
        }

        return $newData;
    }
}
