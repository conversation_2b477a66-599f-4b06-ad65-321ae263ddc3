<?php

namespace App\Exports;

use App\Repositories\UniversalReportRepository;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class UniversalReportExport implements FromCollection, WithHeadings
{
    public function __construct(protected string $query)
    {
        
    }

    public function collection()
    {
        $data = DB::connection('mysql_report')->select($this->query);

        return collect($data);
    }

    public function headings(): array
    {
        $reportRepository = app(UniversalReportRepository::class);
        $sql = $reportRepository->appendOuterLimitOffset($this->query, 1);

        $data = DB::connection('mysql_report')->select($sql);

        if (!empty($data)) {
            return array_keys((array) $data[0]);
        }

        return [];
    }
}
