<?php

namespace App\Exports;

use App\Models\UniversalReportTemplate;
use App\Repositories\UniversalReportRepository;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class UniversalReportExport implements FromCollection, WithHeadings, WithMapping
{
    public function __construct(
        protected int $templateId,
        protected array $settingExportColumns,
        protected string $query
    ) {
        
    }

    public function collection()
    {
        $reportRepository = app(UniversalReportRepository::class);
        $reportTemplate = UniversalReportTemplate::find($this->templateId);

        $data = $reportRepository->executeQueryWithConnectionReport($this->query, $reportTemplate->statement ?? null);

        return collect($data);
    }

    public function map($row): array
    {
        return array_map(function ($item) {
            return (string) $item;
        }, (array) $row);
    }

    public function headings(): array
    {
        $reportRepository = app(UniversalReportRepository::class);
        $sql = $reportRepository->appendOuterLimitOffset($this->query, 1);
        $reportTemplate = UniversalReportTemplate::find($this->templateId);

        $data = $reportRepository->executeQueryWithConnectionReport($sql, $reportTemplate->statement ?? null);

        if (empty($data)) {
            return [];
        }

        $virtualColumns = $reportTemplate->virtualColumnMap();

        $customColumns = collect($this->settingExportColumns)->map(function ($item) use ($virtualColumns) {
            $currentVirtualColumn = $virtualColumns->firstWhere('name', $item['name']);
            
            if (empty($currentVirtualColumn)) {
                return [
                    'original_column' => $item['name'],
                    'original_alias' => $item['alias'],
                    'custom_alias' => $item['custom_alias'],
                ];
            }

            return [
                'original_column' => $currentVirtualColumn['name'],
                'original_alias' => $currentVirtualColumn['alias'],
                'custom_alias' => $item['custom_alias'],
            ];
        })->filter();

        $headers = [];
        foreach (array_keys((array) $data[0]) as $item) {
            $currentCustomColumn = $customColumns->firstWhere('original_alias', $item);
            if (!empty($currentCustomColumn)) {
                $headers[] = $currentCustomColumn['custom_alias'];
                continue;
            }

            $headers[] = $item;
        }

        return $headers;
    }
}
