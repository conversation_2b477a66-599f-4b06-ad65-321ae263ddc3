<?php

namespace App\Exports;

use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

ini_set('memory_limit', '4048M');

class ShipmentExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected $request;

    const COLUMN_STRING = ['A', 'B', 'C', 'D'];

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), self::COLUMN_STRING)) {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        if (!empty($this->request['carrier_service'])) {
            $carrierService = ShippingCarrierService::with('shippingCarrier:id,name,code')
                ->whereHas('shippingCarrier', function ($q) {
                    $q->where('is_deleted', '<>', 1);
                })
                ->get();
            foreach ($carrierService as $key => $item) {
                if ($this->request['carrier_service'] == $item->shippingCarrier->name . ' - ' . $item->display_name) {
                    $this->request['shipment_service'] = $item->name;
                    $this->request['shipping_carrier'] = $item->shippingCarrier->code;
                    break;
                }
            }
        }

        $startDate = !empty($this->request['shipment_date_start']) ? Carbon::parse($this->request['shipment_date_start'])->startOfDay()->format('Y-m-d H:i:s') : Carbon::now()->startOfDay()->format('Y-m-d H:i:s');
        $endDate = !empty($this->request['shipment_date_end']) ? Carbon::parse($this->request['shipment_date_end'])->endOfDay()->format('Y-m-d H:i:s') : Carbon::now()->endOfDay()->format('Y-m-d H:i:s');

        // TODO: Implement collection() method.
        $data = [];
        $query = DB::table('shipment')
            ->join('sale_order', 'sale_order.id', '=', 'shipment.order_id')
            ->leftJoin('store', 'store.id', '=', 'sale_order.store_id')
            ->leftJoin('sale_order_address', 'sale_order_address.order_id', 'shipment.order_id')
            ->where('shipment.warehouse_id', $this->request['warehouse_id'])
//            ->whereNull('shipment.refund_status')
            ->where('sale_order_address.type_address', 'to_address')
            ->whereBetween('shipment.created_at', [$startDate, $endDate]);
        if (!empty($this->request['type_test']) && $this->request['type_test'] != 'all') {
            if ($this->request['type_test'] == 'is_test') {
                $query->where('sale_order.is_test', 1);
            } else {
                $query->where('sale_order.is_test', 0);
            }
        }
        if (!empty($this->request['shipment_status'])) {
            $query->where('shipment.tracking_status', $this->request['shipment_status']);
        }

        if (!empty($this->request['store_id'])) {
            $query->where('sale_order.store_id', $this->request['store_id']);
        }
        if (!empty($this->request['shipment_service'])) {
            $query->where('shipment.service_code', $this->request['shipment_service']);
        }

        if (!empty($this->request['shipping_carrier'])) {
            $query->where('shipment.carrier_code', $this->request['shipping_carrier']);
        }
        if (!empty($this->request['provider'])) {
            $query->where('shipment.provider', $this->request['provider']);
        }

        $keyword = $this->request['keyword'];
        if (!empty($keyword)) {
            $query->where(function ($q1) use ($keyword) {
                $q1->where('shipment.tracking_number', 'LIKE', '%' . $keyword . '%');
                $q1->orWhere(function ($q) use ($keyword) {
                    $q->where('sale_order.order_number', 'LIKE', '%' . $keyword . '%');
                    $q->orWhere('sale_order.external_number', 'LIKE', '%' . $keyword . '%');
                });
            });
        }
        $sortColumn = !empty($this->request['sort_column']) ? $this->request['sort_column'] : 'id';
        $sortBy = !empty($this->request['sort_by']) ? $this->request['sort_by'] : 'desc';
        $query->orderBy($sortColumn, $sortBy);
        $query->select('shipment.id', 'store.name', 'sale_order.external_number', 'sale_order.order_date', 'sale_order.order_status', 'sale_order_address.name as ship_name', 'sale_order.order_quantity',
            'sale_order_address.state', 'sale_order_address.zip', 'sale_order_address.country', 'shipment.weight_value', 'shipment.weight_unit',
            'shipment.carrier_code', 'shipment.service_code', 'shipment.tracking_number', 'shipment.ship_date', 'shipment.shipment_cost', 'sale_order_address.city as ship_city', 'shipment.provider', 'sale_order.created_at as order_date_pst', 'shipment.created_at as shipment_date_pst', );
        $query->chunk(5000, function ($item) use (&$data) {
            $newData = $this->refactorData($item->toArray());
            $data = array_merge($data, $newData);
        });

        return collect($data);
    }

    public function headings(): array
    {
        // TODO: Implement headings() method.
        $headings = [
            'Store Name',
            "Client's Order No.",
            'Order Created Date',
            'Order Status',
            'Ship Name',
            'Ship Date',
            'Ship City',
            'Ship State',
            'Ship Postal Code',
            'Ship Country',
            'Quantity Shipped',
            'Weight',
        ];

        if (!empty($this->request['is_admin'])) {
            $headings[] = 'Amount';
        }

        $headings = array_merge($headings, [
            'Carrier',
            'Shipping Service',
            'Tracking No.',
            'Provider',
        ]);

        return $headings;
    }

    public function refactorData($data): array
    {
        $newData = [];
        $services = ShippingCarrierService::get()->pluck('display_name', 'name');
        $carriers = ShippingCarrier::get()->pluck('name', 'code');
        $isAdmin = !empty($this->request['is_admin']);

        foreach ($data as $item) {
            $row = [
                'name' => $item->name,
                'external_number' => $item->external_number,
                'order_date' => date('Y-m-d', strtotime($item->order_date_pst)),
                'order_status' => $item->order_status,
                'ship_name' => $item->ship_name,
                'ship_date' => date('Y-m-d', strtotime($item->shipment_date_pst)),
                'ship_city' => $item->ship_city,
                'state' => $item->state,
                'zip' => $item->zip,
                'country' => strtoupper($item->country),
                'order_quantity' => $item->order_quantity,
                'weight_value' => $item->weight_value . ' ' . $item->weight_unit,
            ];

            if ($isAdmin) {
                $row['amount'] = $item->shipment_cost ?? 0;
            }

            $row['carrier_code'] = $carriers[$item->carrier_code] ?? $item->carrier_code;
            $row['service_code'] = $services[$item->service_code] ?? $item->service_code;
            $row['tracking_number'] = $item->tracking_number;
            $row['provider'] = $item->provider;

            $newData[] = $row;
        }

        return $newData;
    }

    public function getStatusShipment($dataStatus)
    {
        $status = $dataStatus;
        foreach (Shipment::SHIPMENT_STATUS as $key => $item) {
            if ($dataStatus === $key) {
                $status = $item;
                break;
            }
        }

        return $status;
    }

    public function getService($carrier, $service, $services, $carriers): string
    {
        $nameServer = isset($services[$service]) ? $services[$service] : $service;
        $nameCarrier = isset($carriers[$carrier]) ? $carriers[$carrier] : $carrier;

        return $nameCarrier . ' - ' . $nameServer;
    }
}
