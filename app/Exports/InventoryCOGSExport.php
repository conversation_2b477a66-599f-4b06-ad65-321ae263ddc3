<?php

namespace App\Exports;

use App\Models\Inventory;
use App\Models\Product;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '3072M');

class InventoryCOGSExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function collection()
    {
        setTimezone();
        $startDate = Carbon::parse(trim($this->input['start_date']))->startOfDay();
        $endDate = Carbon::parse(trim($this->input['end_date']))->endOfDay();
        $warehouseId = $this->input['warehouse_id'];

        $rawBeginningSub = DB::table('inventory')
            ->selectRaw('
                id,
                product_id,
                direction,
                quantity,
                cost_total,
                created_at,
                stock_qty_ending,
                stock_value_ending,
                ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY created_at DESC, id DESC) AS row_begin
            ')
            ->where('warehouse_id', $warehouseId)
            ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->where('created_at', '<', $startDate->clone()->format('Y-m-d H:i:s'));

        $beginningInventories = DB::table(DB::raw("({$rawBeginningSub->toSql()}) as sub"))
            ->mergeBindings($rawBeginningSub)
            ->where('row_begin', 1)
            ->get()
            ->keyBy('product_id');

        $rawEndingSub = DB::table('inventory')
            ->selectRaw('
                id,
                product_id,
                direction,
                quantity,
                cost_total,
                created_at,
                stock_qty_ending,
                stock_value_ending,
                ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY created_at DESC, id DESC) AS row_end
            ')
            ->where('warehouse_id', $warehouseId)
            ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->where('created_at', '<=', $endDate->clone()->format('Y-m-d H:i:s'));

        $endingInventories = DB::table(DB::raw("({$rawEndingSub->toSql()}) as sub"))
            ->mergeBindings($rawEndingSub)
            ->orWhere('row_end', 1)
            ->get()
            ->keyBy('product_id');

        $inventories = Inventory::query()
            ->selectRaw("
                inventory.product_id,
                SUM(
                    CASE
                        WHEN inventory.direction = '" . Inventory::DIRECTION_OUTPUT . "'
                            THEN inventory.quantity
                        ELSE 0
                    END
                ) as total_qty_used,
                SUM(
                    CASE
                        WHEN inventory.direction = '" . Inventory::DIRECTION_OUTPUT . "'
                            THEN inventory.cost_total
                        ELSE 0
                    END
                ) as cogs,
                SUM(
                    CASE
                        WHEN inventory.object_name = '" . Inventory::OBJECT_ADDITION . "'
                            THEN inventory.quantity
                        ELSE 0
                    END
                ) as total_qty_addition,
                SUM(
                    CASE
                        WHEN inventory.object_name = '" . Inventory::OBJECT_ADDITION . "'
                            THEN inventory.cost_total
                        ELSE 0
                    END
                ) as total_cost_addition
            ")
            ->where('inventory.fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->whereBetween('inventory.created_at', [
                $startDate->clone()->format('Y-m-d H:i:s'),
                $endDate->clone()->format('Y-m-d H:i:s'),
            ])
            ->where('inventory.warehouse_id', $warehouseId)
            ->groupBy('inventory.product_id')
            ->get()
            ->keyBy('product_id');

        $data = [];
        $productReport = Product::query()
            ->joinSub(
                Inventory::query()
                    ->select('product_id')
                    ->where('warehouse_id', $warehouseId)
                    ->groupBy('product_id'),
                'inventory_tmp',
                'product.id',
                '=',
                'inventory_tmp.product_id',
            )
            ->select([
                'product.id',
                'product.sku',
                'product.style',
                'product.color',
                'product.size',
                'product.description',
            ])
            ->orderBy('product.sku')
            ->get();

        foreach ($productReport as $product) {
            $productId = $product->id;
            $item = $inventories->has($productId) ? $inventories->get($productId) : null;
            $itemStart = $beginningInventories->has($productId) ? $beginningInventories->get($productId) : null;
            $itemEnd = $endingInventories->has($productId) ? $endingInventories->get($productId) : null;

            $data[] = [
                'sku' => $product->sku,
                'style' => $product->style,
                'color' => $product->color,
                'size' => $product->size,
                'product_description' => $product->description,
                'beginning_period' => $startDate->clone()->format('Y/m/d'),
                'ending_period' => $endDate->clone()->format('Y/m/d'),
                'beginning_qty' => (string) ($itemStart->stock_qty_ending ?? 0),
                'beginning_value' => (string) ($itemStart->stock_value_ending ?? 0),
                'ending_qty' => (string) ($itemEnd->stock_qty_ending ?? 0),
                'ending_value' => (string) ($itemEnd->stock_value_ending ?? 0),
                'total_qty_used' => (string) ($item->total_qty_used ?? 0),
                'cogs' => (string) ($item->cogs ?? 0),
                'total_qty_addition' => (string) ($item->total_qty_addition ?? 0),
                'total_cost_addition' => (string) ($item->total_cost_addition ?? 0),
            ];
        }

        return collect($data);
    }

    public function headings(): array
    {
        return [
            'SKU',
            'Style',
            'Color',
            'Size',
            'Product Description',
            'Beginning Period',
            'Ending Period',
            'Beginning Qty',
            'Beginning Value',
            'Ending Qty',
            'Ending Value',
            'Total Qty Used',
            'Total Value',
            'Addition Qty',
            'Addition Amount',
        ];
    }
}
