<?php

namespace App\Exports;

use App\Models\FifoInventory;
use App\Models\Inventory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '3072M');

class InventoryCOGSExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function collection()
    {
        setTimezone();
        $startDate = Carbon::parse(trim($this->input['start_date']))->startOfDay();
        $endDate = Carbon::parse(trim($this->input['end_date']))->endOfDay();
        $warehouseId = $this->input['warehouse_id'];

        $rawBeginningSub = DB::table('inventory')
            ->selectRaw('
                id,
                product_id,
                direction,
                quantity,
                cost_total,
                created_at,
                stock_qty_ending,
                stock_value_ending,
                ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY created_at DESC, id DESC) AS row_begin
            ')
            ->where('warehouse_id', $warehouseId)
            ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->where('created_at', '<', $startDate->clone()->format('Y-m-d H:i:s'));

        $beginningInventories = DB::table(DB::raw("({$rawBeginningSub->toSql()}) as sub"))
            ->mergeBindings($rawBeginningSub)
            ->where('row_begin', 1)
            ->get()
            ->keyBy('product_id');

        $rawEndingSub = DB::table('inventory')
            ->selectRaw('
                id,
                product_id,
                direction,
                quantity,
                cost_total,
                created_at,
                stock_qty_ending,
                stock_value_ending,
                ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY created_at DESC, id DESC) AS row_end
            ')
            ->where('warehouse_id', $warehouseId)
            ->where('fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->where('created_at', '<=', $endDate->clone()->format('Y-m-d H:i:s'));

        $endingInventories = DB::table(DB::raw("({$rawEndingSub->toSql()}) as sub"))
            ->mergeBindings($rawEndingSub)
            ->orWhere('row_end', 1)
            ->get()
            ->keyBy('product_id');

        $inventories = Inventory::query()
            ->selectRaw("
                inventory.product_id,
                SUM(
                    CASE
                        WHEN inventory.direction = '" . Inventory::DIRECTION_OUTPUT . "'
                            THEN inventory.quantity
                        ELSE 0
                    END
                ) as total_qty_used,
                SUM(
                    CASE
                        WHEN inventory.direction = '" . Inventory::DIRECTION_OUTPUT . "'
                            THEN inventory.cost_total
                        ELSE 0
                    END
                ) as cogs
            ")
            ->where('inventory.fifo_calculated_at', '>', Inventory::FIFO_NOT_CALCULATED)
            ->whereBetween('inventory.created_at', [
                $startDate->clone()->format('Y-m-d H:i:s'),
                $endDate->clone()->format('Y-m-d H:i:s'),
            ])
            ->where('inventory.warehouse_id', $warehouseId)
            ->groupBy('inventory.product_id')
            ->get()
            ->keyBy('product_id');

        $data = [];
        $fifoInventory = FifoInventory::query()
            ->selectRaw('
                product.sku,
                product.style,
                product.color as product_color,
                product.size as product_size,
                product.description as product_description,
                fifo_inventory.*
            ')
            ->join('product', 'product.id', '=', 'fifo_inventory.product_id')
            ->where('warehouse_id', $warehouseId)
            ->whereDate('start_date', $startDate->clone()->format('Y-m-d'))
            ->whereDate('end_date', $endDate->clone()->format('Y-m-d'))
            ->orderBy('product.sku')
            ->get();

        foreach ($fifoInventory as $inventory) {
            $productId = $inventory->product_id;
            $item = $inventories->has($productId) ? $inventories->get($productId) : null;
            $itemStart = $beginningInventories->has($productId) ? $beginningInventories->get($productId) : null;
            $itemEnd = $endingInventories->has($productId) ? $endingInventories->get($productId) : null;

            $data[] = [
                'sku' => $inventory->sku,
                'style' => $inventory->style,
                'color' => $inventory->product_color,
                'size' => $inventory->product_size,
                'product_description' => $inventory->product_description,
                'beginning_period' => $startDate->clone()->format('Y/m/d'),
                'ending_period' => $endDate->clone()->format('Y/m/d'),
                'beginning_qty' => (string) ($itemStart->stock_qty_ending ?? 0),
                'beginning_value' => (string) ($itemStart->stock_value_ending ?? 0),
                'ending_qty' => (string) ($itemEnd->stock_qty_ending ?? 0),
                'ending_value' => (string) ($itemEnd->stock_value_ending ?? 0),
                'total_qty_used' => (string) ($item->total_qty_used ?? 0),
                'cogs' => (string) ($item->cogs ?? 0),
            ];
        }

        return collect($data);
    }

    public function headings(): array
    {
        return [
            'SKU',
            'Style',
            'Color',
            'Size',
            'Product Description',
            'Beginning Period',
            'Ending Period',
            'Beginning Qty',
            'Beginning Value',
            'Ending Qty',
            'Ending Value',
            'Total Qty Used',
            'Total Value',
        ];
    }
}
