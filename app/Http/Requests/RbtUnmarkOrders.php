<?php

namespace App\Http\Requests;

use App\Models\SaleOrderItem;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RbtUnmarkOrders extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => [
                'nullable',
                'array',
                'required_without:wip_id',
                'prohibited_if:wip_id,*',
            ],
            'order_id.*' => [
                'string',
                Rule::exists('sale_order', 'id')->where('is_rbt', SaleOrderItem::IS_RBT),
            ],
            'wip_id' => [
                'nullable',
                'array',
                'required_without:order_id',
                'prohibited_if:order_id,*',
            ],
            'wip_id.*' => [
                'string',
                'exists:sale_order_item_barcode,label_id',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'order_id.exists' => "The provided 'order_id' does not exist.",
            'wip_id.exists' => "The provided 'wip_id' does not exist.",
            'order_id.required_without' => "Either 'order_id' or 'wip_id' must be provided.",
            'wip_id.required_without' => "Either 'order_id' or 'wip_id' must be provided.",
            'order_id.prohibited_if' => "'order_id' cannot be provided when 'wip_id' is present.",
            'wip_id.prohibited_if' => "'wip_id' cannot be provided when 'order_id' is present.",
            'order_id.*.exists' => "One or more 'order_id' values do not exist in the database.",
            'wip_id.*.exists' => "One or more 'wip_id' values do not exist in the database.",
        ];
    }
}
