<?php

namespace App\Http\Requests\SupplyLocation;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UpdateSupplyLocation extends FormRequest
{
    protected $barcode;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $this->barcode = $request->barcode;

        return [
            'barcode' => [
                'required',
                Rule::unique('supply_locations', 'barcode')
                    ->where('warehouse_id', config('jwt.warehouse_id'))
                    ->whereNull('deleted_at')
                    ->ignore($request->id)
            ]
        ];
    }

    public function messages()
    {
        return [
            'barcode.required' => 'Location ID is required.',
            'barcode.unique' => $this->barcode . ' is already existing in Supply Locations.'
        ];
    }
}
