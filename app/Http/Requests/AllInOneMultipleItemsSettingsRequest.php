<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AllInOneMultipleItemsSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
            ],
            'settings.layout' => [
                'required',
                'array',
                'size:4',
            ],
            'settings.layout.*.top' => [
                'required',
                'numeric',
            ],
            'settings.layout.*.left' => [
                'required',
                'numeric',
            ],
            'settings.testPrint' => [
                'nullable',
                'boolean',
            ],
        ];
    }
}
