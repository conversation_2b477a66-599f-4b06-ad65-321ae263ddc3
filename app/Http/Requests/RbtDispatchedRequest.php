<?php

namespace App\Http\Requests;

use App\Models\Employee;
use App\Models\SaleOrder;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class RbtDispatchedRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'wip_ids' => [
                'required',
                'array',
            ],
            'wip_ids.*' => [
                'string',
                function ($attribute, $value, $fail) {
                    $invalidStatuses = [
                        SaleOrder::CANCELLED,
                        SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
                        SaleOrder::REJECTED,
                        SaleOrder::ON_HOLD,
                    ];

                    $order = SaleOrder::query()
                        ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
                        ->whereIn('sale_order.order_status', $invalidStatuses)
                        ->where('sale_order_item_barcode.label_id', $value)
                        ->first();

                    if ($order) {
                        $fail("WIP ID '{$value}' belongs to an order in status: {$order->order_status} and is not allowed.");
                    }
                }
            ],
            'employee_code' => [
                'required',
                function ($attribute, $value, $fail) {
                    $employee = Employee::where('code', $value)->where('is_deleted', false)
                        ->first();

                    if (!$employee) {
                        $fail('Employee code is invalid or inactive.');
                    }
                },
            ],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors();

        $missingFields = [];

        if ($this->missing('wip_ids') || $this->filled('wip_ids') && is_array($this->wip_ids) && empty(array_filter($this->wip_ids))) {
            $missingFields[] = 'wip_ids';
        }

        if (trim((string) $this->input('employee_code')) === '') {
            $missingFields[] = 'employee_code';
        }

        if (!empty($missingFields)) {
            throw new HttpResponseException(response()->json([
                'message' => 'Missing required fields: ' . implode(', ', $missingFields) . '.'
            ], 422));
        }

        // fallback to default response
        throw new HttpResponseException(response()->json([
            'errors' => $errors,
        ], 422));
    }
}
