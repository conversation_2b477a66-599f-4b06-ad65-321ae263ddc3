<?php

namespace App\Http\Requests\InternalRequest;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeLoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'code' => [
                'required',
                Rule::exists('employee', 'code')
                    ->where('is_deleted', false)
            ]
        ];
    }

    public function messages()
    {
        return [
            'code.exists' => 'Employee not found.',
        ];
    }
}
