<?php

namespace App\Http\Requests;

use App\Models\SaleOrder;
use App\Models\SaleOrderKitting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RbtKittingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'event_type' => [
                'nullable',
                Rule::in([
                    SaleOrderKitting::EVENT_TYPE_START,
                    SaleOrderKitting::EVENT_TYPE_END,
                ]),
            ],
            'wip_id' => [
                'required',
                'string',
                'exists:sale_order_item_barcode,label_id',
                function ($attribute, $value, $fail) {
                    $invalidStatuses = [
                        SaleOrder::CANCELLED,
                        SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
                        SaleOrder::REJECTED,
                        SaleOrder::ON_HOLD,
                    ];

                    $order = SaleOrder::query()
                        ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
                        ->whereIn('sale_order.order_status', $invalidStatuses)
                        ->where('sale_order_item_barcode.label_id', $value)
                        ->first();

                    if ($order) {
                        $fail("WIP ID belongs to an order in status: {$order->order_status} and is not allowed.");
                    }
                }
            ],
            'employee_code' => [
                'required',
                'exists:employee,code',
            ],
            'ring' => [
                'required',
                'regex:/^R\d+$/',
            ],
            'column' => [
                'required',
                'regex:/^[A-Z]$/',
            ],
            'row' => [
                'required',
                'numeric',
            ],
        ];
    }
}
