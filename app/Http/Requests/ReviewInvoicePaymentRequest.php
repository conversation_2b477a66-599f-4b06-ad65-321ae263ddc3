<?php

namespace App\Http\Requests;

use App\Models\InvoiceTransaction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReviewInvoicePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'amount' => [
                'required_if:status,' . InvoiceTransaction::STATUS_APPROVED,
                'numeric',
                'min:0'
            ],
            'note' => 'nullable|string|max:255',
            'attachments' => 'nullable|array|max:3',
            'attachments.*' => 'nullable|file|mimes:pdf,png,jpg,jpeg',
            'status' => [
                'required',
                Rule::in(InvoiceTransaction::STATUS_APPROVED, InvoiceTransaction::STATUS_FAILED),
            ]
        ];
    }
}
