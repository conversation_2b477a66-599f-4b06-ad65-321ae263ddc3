<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class PromotionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        return [
            'store_id' => 'required|exists:store,id',
            'promotion_type_id' => 'required|exists:promotion_types,id',
            'is_public' => 'required|in:0,1',
            'start_time' => 'required|date_format:Y-m-d H:i:s',
            'amount' => ['required', 'min:0'],
            'end_time' => 'nullable|date_format:Y-m-d H:i:s|after_or_equal:start_time',
        ];
    }
}
