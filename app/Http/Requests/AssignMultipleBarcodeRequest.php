<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssignMultipleBarcodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_label_id' => ['required', 'exists:sale_order_item_barcode,label_id'],
            'last_label_id' => ['required', 'exists:sale_order_item_barcode,label_id'],
            'employee_id' => ['required', 'exists:employee,id']
        ];
    }
}
