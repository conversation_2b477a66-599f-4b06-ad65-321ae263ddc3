<?php

namespace App\Http\Requests\ManualTracking;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateManualTrackingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'carrier_code' => [
                'nullable',
                Rule::exists('shipping_carrier', 'code')->where('is_deleted', 0)
            ],
            'ship_date' => 'nullable|date|before_or_equal:today',
            'price' => 'nullable|numeric'
        ];
    }
}
