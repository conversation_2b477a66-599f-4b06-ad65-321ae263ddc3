<?php

namespace App\Http\Requests\ManualTracking;

use App\Models\Exportation;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckTotalTrackingForExportation;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class GenerateExportationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        return [
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))->where('is_deleted', false)
            ],
            'exportation_id' => [
                'required',
                Rule::exists('exportation', 'id')->where('status', Exportation::SCANNING),
                new CheckTotalTrackingForExportation($request->exportation_id)
            ],
        ];
    }

    public function messages()
    {
        return [
            'exportation_id.required' => 'ExportationExport field is required',
            'exportation_id.exists' => 'ExportationExport not found or has been generate!',
        ];
    }

}
