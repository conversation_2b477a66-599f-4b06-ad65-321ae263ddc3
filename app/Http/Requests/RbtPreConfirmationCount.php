<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RbtPreConfirmationCount extends FormRequest
{
    /**
     * Authorize the request for all users.
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Define validation rules.
     */
    public function rules()
    {
        return [
            'last_wip_id' => 'required|exists:sale_order_item_barcode,label_id',
            'employee_id' => 'required|exists:employee,code',
        ];
    }

    /**
     * Custom error messages for validation.
     */
    public function messages()
    {
        return [
            'last_wip_id.required' => 'The last WIP ID is required.',
            'last_wip_id.exists' => 'The selected WIP ID is invalid.',
            'employee_id.required' => 'Employee ID is required.',
            'employee_id.exists' => 'The provided Employee ID is invalid.',
        ];
    }
}
