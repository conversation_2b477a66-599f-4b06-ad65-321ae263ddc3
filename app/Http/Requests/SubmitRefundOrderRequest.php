<?php

namespace App\Http\Requests;

use App\Models\SaleOrder;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderRefund;
use App\Models\SaleOrderSurchargeFee;
use Illuminate\Foundation\Http\FormRequest;

class SubmitRefundOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'amount' => 'required|numeric|gt:0',
            'reason' => 'required|max:255|string',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $orderId = $this->route('id');
            $order = SaleOrder::with('items')->findOrFail($orderId);

            if (!$order) {
                $validator->errors()->add('order', 'Order not found.');

                return;
            }

            if ($order->order_status != SaleOrder::STATUS_SHIPPED) {
                $validator->errors()->add('order', 'Order status must be shipped.');

                return;
            }

            if (!in_array($order->payment_status, [SaleOrder::PAYMENT_STATUS_PAID, SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED])) {
                $validator->errors()->add('order', 'Invalid payment status.');

                return;
            }

            $totalRefund = SaleOrderRefund::where(['order_id' => $orderId])->sum('amount');
            $totalInsertPrice = SaleOrderInsertCalculatePrice::where('order_id', $order->id)->sum('amount_paid');
            $totalOrderSurchageFee = SaleOrderSurchargeFee::where('order_id', $order->id)->sum('value');
            $totalItemSurchageFee = SaleOrderItemSurchargeFee::where('order_id', $order->id)->sum('value');
            $totalAmount = $totalInsertPrice + $totalOrderSurchageFee + $totalItemSurchageFee + $order->order_total;
            $totalRefundRequest = $totalRefund + $this->input('amount');
            request()->merge([
                'partial_refund' => $totalRefundRequest == $totalAmount ? false : true,
                'total_refund' => $totalRefundRequest,
            ]);

            if ($totalRefundRequest > $totalAmount) {
                $validator->errors()->add(
                    'amount',
                    'The refund amount must be less than or equal to the total amount.',
                );
            }
        });
    }
}
