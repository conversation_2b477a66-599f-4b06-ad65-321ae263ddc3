<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateInternalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'employee_id' => ['required', 'exists:employee,id'],
            'barcode' => ['required', 'exists:box,barcode'],
            'id_time_checking' => ['required'],
            'count_sticker' => ['nullable', 'sometimes', 'exists:rbt_count_stickers,barcode'],
        ];
    }

    public function messages()
    {
        return [
            'barcode.exists' => 'The selected Box ID not found.',
            'employee_id.exists' => 'The selected employee not found.',
        ];
    }
}
