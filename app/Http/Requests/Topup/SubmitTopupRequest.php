<?php

namespace App\Http\Requests\Topup;

use App\Models\WalletTopup;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubmitTopupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:1'
            ],
            'payment_method' => [
                'required',
                'string',
                Rule::in([
                    WalletTopup::CARD_METHOD,
                    WalletTopup::BANK_TRANSFER_METHOD
                ])
            ],
            'payment_method_id' => [
                'sometimes',
                'string',
            ],
        ];
    }
}
