<?php

namespace App\Http\Requests\ProductPrintSide;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => ['nullable', 'unique:product_print_side,name,' . $this['id'], 'string', 'max:255'],
            'code' => ['nullable', 'unique:product_print_side,code,' . $this['id'], 'numeric'],
            'description' => ['nullable', 'string', 'max:255'],
            'code_wip' => ['nullable', 'string', 'max:1']
        ];
    }
}
