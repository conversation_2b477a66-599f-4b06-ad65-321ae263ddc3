<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class Dl2400PrinterPushLogRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'tracking_uuid' => [
                'nullable',
                'max:255',
            ],
            'client_id' => [
                'nullable',
                'max:255',
            ],
            'order_id' => [
                'required',
                'max:255',
            ],
            'employee_id' => [
                'required',
                'max:255',
            ],
            'label_id' => [
                'required',
                'max:255',
            ],
            'side' => [
                'required',
                'max:255',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [];
    }
}
