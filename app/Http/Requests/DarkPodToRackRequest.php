<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DarkPodToRackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'employee_id' => 'required|exists:employee,id',
            'barcode' => 'required|string|max:255',
            'product_id' => 'required|exists:product,id',
            'quantity' => 'required|integer|min:1',
            'location_id' => 'required|exists:location,id',
            'cost_value' => 'nullable|sometimes'
        ];
    }

    public function attributes()
    {
        return [
            'internal_request_id' => 'Internal Request ID',
            'box_id' => 'Old Box ID',
            'box_barcode' => 'New Box ID',
            'quantity' => 'Quantity',
            'location_id' => 'Location',
            'employee_id' => 'Employee',
        ];
    }

    public function messages()
    {
        return [
            'box_id.required_without' => 'The old box ID is required',
        ];
    }
}
