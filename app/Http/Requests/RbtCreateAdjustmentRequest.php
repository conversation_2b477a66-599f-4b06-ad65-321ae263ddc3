<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class RbtCreateAdjustmentRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'location' => ['required'],
            'quantity_on_hand' => ['required', 'integer', 'min:0'],
            'employee_code' => ['nullable', 'exists:employee,code'],
        ];
    }

    public function messages()
    {
        return [
            'location.required' => 'Location is required.',
            'quantity_on_hand.required' => 'Quantity on hand is required.',
            'quantity_on_hand.integer' => 'Quantity on hand must be an integer.',
            'quantity_on_hand.min' => 'Quantity on hand must be greater than or equal to 0.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors()->first(); // Get the first validation error
        throw new HttpResponseException(response()->json([
            'status' => 'error',
            'message' => $errors,
        ], 422));
    }
}
