<?php

namespace App\Http\Requests\StoreProduct;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class ClonePricingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
//        {
//            "source_store_id" : 1,
        //	"store_id" : 3,
//    "product_pricing": {
//            "include_all": true,
//        "product_styles": []
//    },
//    "shipping_pricing": {
//            "include_all": true,
//        "product_types": []
//    },
//    "surcharge_pricing": {
//            "include_all": true,
//        "surcharges": []
//    }
        //}
        return [
            'source_store_id' => 'required|integer|different:store_id|exists:store,id',
            'store_id' => 'required|exists:store,id',

            'product_pricing' => 'required_without_all:shipping_pricing,surcharge_pricing|array',
            'shipping_pricing' => 'required_without_all:product_pricing,surcharge_pricing|array',
            'surcharge_pricing' => 'required_without_all:product_pricing,shipping_pricing|array',

            'product_pricing.include_all' => 'required_if:product_pricing,array|boolean',
            'product_pricing.product_styles' => 'nullable|array|min:0',

            'shipping_pricing.include_all' => 'required_if:shipping_pricing,array|boolean',
            'shipping_pricing.product_types' => 'required_if:shipping_pricing,array|array',

            'surcharge_pricing.include_all' => 'required_if:surcharge_pricing,array|boolean',
            'surcharge_pricing.surcharges' => 'required_if:surcharge_pricing,array|array',
            'effective_from' => [
                'nullable',
                'date_format:Y-m-d H:i:s',
                function ($attribute, $value, $fail) {
                    $pstTime = Carbon::createFromFormat('Y-m-d H:i:s', $value, 'America/Los_Angeles');
                    $utcTime = $pstTime->setTimezone('UTC');
                    $nowUtc = Carbon::now('UTC');
                    if ($utcTime->lessThanOrEqualTo($nowUtc)) {
                        $fail("The $attribute must be greater than the current UTC time.");
                    }
                }
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'store_id.different' => 'The target store ID must be different from the source store ID.',
            'product_pricing.required_without' => 'At least one of product pricing, shipping pricing, or surcharge pricing must be provided.',
            'shipping_pricing.required_without' => 'At least one of product pricing, shipping pricing, or surcharge pricing must be provided.',
            'surcharge_pricing.required_without' => 'At least one of product pricing, shipping pricing, or surcharge pricing must be provided.',
            'product_pricing.product_styles.min' => 'The product_styles field must be an empty array if include_all is true.',
            'product_pricing.product_styles.required' => 'The product_styles field is required when include_all is false.',
            'product_pricing.product_styles.not_empty' => 'The product_styles field must not be empty when include_all is false.',
        ];
    }

    public function withValidator($validator)
    {
        $data = $this->all();

        $productValid = !isset($data['product_pricing']['include_all']) || !$data['product_pricing']['include_all'];
        $shippingValid = !isset($data['shipping_pricing']['include_all']) || !$data['shipping_pricing']['include_all'];
        $surchargeValid = !isset($data['surcharge_pricing']['include_all']) || !$data['surcharge_pricing']['include_all'];

        $hasValidProductStyles = !empty($data['product_pricing']['product_styles'] ?? []);
        $hasValidProductTypes = !empty($data['shipping_pricing']['product_types'] ?? []);
        $hasValidSurcharges = !empty($data['surcharge_pricing']['surcharges'] ?? []);

        $validator->after(function ($validator) use (
            $data,
            $productValid,
            $shippingValid,
            $surchargeValid,
            $hasValidProductStyles,
            $hasValidProductTypes,
            $hasValidSurcharges
        ) {
            if (
                ($productValid && !$hasValidProductStyles)
                && ($shippingValid && !$hasValidProductTypes)
                && ($surchargeValid && !$hasValidSurcharges)
            ) {
                $validator->errors()->add(
                    'validation_error',
                    "At least one of 'product_styles', 'product_types', or 'surcharges' must contain data if 'include_all' is false or missing.",
                );
            }

            foreach ([
                'product_pricing' => 'product_styles',
                'shipping_pricing' => 'product_types',
                'surcharge_pricing' => 'surcharges'
            ] as $key => $field) {
                if (!empty($data[$key])) {
                    $this->validateIncludeAll(
                        $validator,
                        $data[$key],
                        "{$key}.{$field}",
                        $field,
                    );
                }
            }
        });
    }

    private function validateIncludeAll($validator, $data, $fieldPath, $fieldKey)
    {
        $includeAll = $data['include_all'] ?? null;
        $fieldValue = $data[$fieldKey] ?? null;

        if ($includeAll === true) {
            if (!is_null($fieldValue) && (is_array($fieldValue) && count($fieldValue) > 0)) {
                $validator->errors()->add(
                    $fieldPath,
                    "The $fieldKey field must be an empty array or null when include_all is true.",
                );
            }
        }
    }
}
