<?php

namespace App\Http\Requests;

use App\Models\Invoice;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateInvoiceRequest extends FormRequest
{
    protected $invoice;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => ['required', Rule::in([Invoice::STATUS_UNPAID, Invoice::STATUS_CANCELLED])],
            'cancel_reason' => 'required_if:status,' . Invoice::STATUS_CANCELLED,
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->invoice = Invoice::findOrFail($this->route('id'));

            if (($this->invoice->status != Invoice::STATUS_DRAFT) && $this->input('status') == Invoice::STATUS_CANCELLED) {
                $validator->errors()->add('status', 'Cannot cancel invoice.');
            }
        });
    }

    public function getInvoice()
    {
        return $this->invoice;
    }
}
