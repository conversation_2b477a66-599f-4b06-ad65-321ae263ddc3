<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RbtReceivedBoxRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'box_id' => 'required',
            'employee_id' => 'nullable|sometimes|exists:employee,code',
            'quantity' => 'nullable|sometimes|integer|min:0',
            'station_id' => 'nullable|sometimes',
            'id_time_checking' => 'nullable|sometimes|exists:time_tracking,id',
        ];
    }
}
