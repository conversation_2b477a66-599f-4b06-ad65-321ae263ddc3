<?php

namespace App\Http\Requests\Ornament;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GeneratePdfRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'options' => 'required|array',
            'product_id' => 'required|numeric',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
        ];
    }

    public function messages()
    {
        return [
            'employee_id.exists' => 'You are not in this warehouse!',
        ];
    }
}
