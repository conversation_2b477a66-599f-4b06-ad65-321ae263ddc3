<?php

namespace App\Http\Requests;

use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TeamMemberRoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $roleId = $this->route('id');
        $clientId = auth()->user()->client_id;

        if (strtolower($this->input('name')) === 'admin') {
            return false;
        }

        if ($roleId) {
            $role = \App\Models\TeamMemberRole::where('id', $roleId)->where('client_id', $clientId)->first();

            if (!$role) {
                return false;
            }

            // Prevent editing the 'Admin' role
            if (strtolower($role->name) === 'admin') {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $roleId = $this->route('id');
        $clientId = auth()->user()->client_id;

        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('team_member_roles')->where(function ($query) use ($clientId, $roleId) {
                    return $query->where('client_id', $clientId)
                                 ->when($roleId, function ($query) use ($roleId) {
                                     return $query->where('id', '<>', $roleId);
                                 });
                }),
            ],
            'permissions' => 'required|array',
            'permissions.*.function_name' => [
                'required',
                Rule::in(TeamMemberRolePermission::listFuction())
            ],
            'permissions.*.permission' => [
                'required',
                Rule::in(TeamMemberRolePermission::listPermission())
            ],
        ];
    }
}
