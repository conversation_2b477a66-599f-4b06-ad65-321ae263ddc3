<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AllInOneMultipleItemsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'get_print_file' => [
                'nullable',
            ],
            'setting_name' => [
                'nullable',
            ],
            'country_of_origin' => [
                'nullable',
            ],
            'label' => [
                'required',
                'array',
            ],
            'label.*' => [
                'nullable',
            ],
            'employee_id' => [
                'required',
                'exists:employee,id',
            ]
        ];
    }
}
