<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CompleteSupplyTestCountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'location_id' => 'required',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'code')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'id_time_checking' => 'required'
        ];
    }
}
