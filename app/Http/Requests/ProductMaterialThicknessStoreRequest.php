<?php

namespace App\Http\Requests;

use App\Models\ProductType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductMaterialThicknessStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $productType = ProductType::where('id', $this->input('product_type_id') ?? '')->first();

        return [
            'product_type_id' => 'required|exists:product_type,id',
            'product_style_id' => [
                'nullable',
                Rule::exists('product_style', 'id')
                    ->where('type', $productType->name ?? null)
            ],
            'product_print_side_id' => [
                'nullable',
                'exists:product_print_side,id'
            ],
            'material_thickness' => 'required|string',
            'heat_press' => 'required|integer',
        ];
    }
}
