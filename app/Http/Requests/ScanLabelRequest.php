<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ScanLabelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'label' => 'required',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('is_deleted', 0)->where('warehouse_id', config('jwt.warehouse_id'))],
            'id_time_checking' => ['required', Rule::exists('time_tracking', 'id')]
        ];
    }
}
