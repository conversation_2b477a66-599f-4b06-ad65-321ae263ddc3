<?php

namespace App\Http\Requests\UniversalReport;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DuplicateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('universal_report_templates', 'name')->withoutTrashed(),
            ],
            'description' => [
                'required',
                'string',
                'max:255',
            ],
        ];
    }
}
