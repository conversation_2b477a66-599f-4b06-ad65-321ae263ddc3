<?php

namespace App\Http\Requests\UniversalReport;

use App\Models\UniversalReportTag;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $tagIds = $this->route('template')->tags->pluck('id') ?? [];

        return [
            'template' => [
                'required',
                'array',
                'min:1',
            ],
            'template.name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('universal_report_templates', 'name')
                    ->ignore($this->route('template')->id)
                    ->withoutTrashed(),
            ],
            'template.description' => [
                'required',
                'string',
                'max:255',
            ],
            'template.query' => [
                'required',
                'string',
            ],
            'template.report_category_id' => [
                'required',
                'integer',
                'exists:universal_report_categories,id',
            ],
            'template.status' => [
                'required',
                'boolean',
            ],
            'tags' => [
                'sometimes',
                'array',
                'min:1',
            ],
            'tags.*.is_required' => [
                'required',
                'boolean',
            ],
            'tags.*.id' => [
                'sometimes',
                'integer',
                Rule::in($tagIds),
            ],
            'tags.*.code' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9_]+$/',
            ],
            'tags.*.type' => [
                'required',
                'string',
                Rule::in([
                    UniversalReportTag::TYPE_INPUT,
                    UniversalReportTag::TYPE_SINGLE_SELECT,
                    UniversalReportTag::TYPE_MULTI_SELECT,
                    UniversalReportTag::TYPE_SINGLE_DATE,
                    UniversalReportTag::TYPE_RANGE_DATE,
                ]),
            ],
            'tags.*.label' => [
                'nullable',
                'string',
                'max:255',
            ],
            'tags.*.value' => [
                'nullable',
                'string',
                'max:255',
            ],
            'tags.*.value_type' => [
                'required',
                'string',
                Rule::in([
                    UniversalReportTag::VALUE_TYPE_FREE_TEXT,
                    UniversalReportTag::VALUE_TYPE_SQL,
                ]),
            ],
            'tags.*.placeholder_1' => [
                'nullable',
                'string',
                'max:255',
            ],
            'tags.*.placeholder_2' => [
                'nullable',
                'string',
                'max:255',
            ],
        ];
    }
}
