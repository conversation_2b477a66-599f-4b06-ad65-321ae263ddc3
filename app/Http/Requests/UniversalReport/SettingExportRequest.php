<?php

namespace App\Http\Requests\UniversalReport;

use App\Models\User;
use App\Rules\UniversalSettingExportRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SettingExportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $columnConfiguration = $this->template->columnConfiguration();
        $allTags = $this->template->tags;
        $tagUsed = $this->template->tags->where('is_used', true);
        $isRequiredTag = $tagUsed->where('is_required', true)->isNotEmpty();

        $rules = [
            'columns' => [
                'required',
                'array',
                'min:1',
            ],
            'columns.*.name' => [
                'required',
                'string',
                'max:255',
                Rule::in($columnConfiguration->pluck('column')),
            ],
            'columns.*.alias' => [
                'nullable',
                'string',
                'max:255',
            ],
            'tags' => [
                Rule::requiredIf($isRequiredTag),
                'array',
                'min:1',
                function ($attribute, $value, $fail) use ($tagUsed) {
                    $requestIds = $this->input('tags.*.id');
                    $tagRequired = $tagUsed->where('is_required', true)->pluck('id')->toArray();

                    if ($missing = array_diff($tagRequired, $requestIds)) {
                        $fail('The tags missing: ' . implode(', ', $missing));
                    }
                },
            ],
            'tags.*.id' => [
                'required',
                'integer',
                Rule::in($allTags->pluck('id')),
            ],
            'tags.*.value' => [
                'required',
                'array',
                'min:1',
                new UniversalSettingExportRule($tagUsed, $this->input('tags.*.id'))
            ],
            'tags.*.value.*' => [
                'required',
            ],
        ];

        if (auth()->user()->is_admin == User::ADMIN) {
            $rules['is_default'] = [
                'sometimes',
                'boolean',
            ];
        }

        return $rules;
    }
}
