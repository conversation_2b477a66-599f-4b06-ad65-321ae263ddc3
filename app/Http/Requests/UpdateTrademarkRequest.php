<?php

namespace App\Http\Requests;

use App\Models\Trademark;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTrademarkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'type' => ['required', Rule::in(Trademark::TYPE_LIST)],
            'ownership_id' => ['nullable', 'exists:ownerships,id'],
            'is_active' => ['required', Rule::in([0, 1])],

        ];
    }

    public function attributes()
    {
        return [
            'ownership_id' => 'ownership',
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => 'Error! Duplicate Trademark.'
        ];
    }
}
