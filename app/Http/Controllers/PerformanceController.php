<?php

namespace App\Http\Controllers;

use App\Http\Requests\PretreatSkuLogsRequest;
use App\Http\Requests\PrintedInsertRequest;
use App\Http\Requests\ScanLabelRequest;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Repositories\EmployeeRepository;
use App\Repositories\PerformanceRepository;
use App\Repositories\SaleOrderInsertRepository;
use App\Repositories\SaleOrderItemBarcodeRepository;
use App\Repositories\SaleOrderItemRepository;
use App\Repositories\SaleOrderRepository;
use App\Repositories\TimeCheckingRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PerformanceController extends Controller
{
    private SaleOrderRepository $saleOrderRepository;

    private SaleOrderInsertRepository $saleOrderInsertRepository;

    private PerformanceRepository $performanceRepository;

    private EmployeeRepository $employeeRepository;

    public function __construct(SaleOrderRepository $saleOrderRepository, SaleOrderInsertRepository $saleOrderInsertRepository,
        PerformanceRepository $performanceRepository, EmployeeRepository $employeeRepository
    ) {
        $this->saleOrderRepository = $saleOrderRepository;
        $this->saleOrderInsertRepository = $saleOrderInsertRepository;
        $this->performanceRepository = $performanceRepository;
        $this->employeeRepository = $employeeRepository;
    }

    public function logInsertPrinted(PrintedInsertRequest $request)
    {
        $input = $request->validated();

        return response()->json($this->saleOrderInsertRepository->logPrinted($input));
    }

    public function pretreatSKU(PretreatSkuLogsRequest $request)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();
            $input['warehouse_id'] = config('jwt.warehouse_id');

            $data = $this->performanceRepository->scanPretreat($input, 'employee_pretreat_id');
            if (empty($data)) {
                return response()->json(['message' => ['Error! Invalid Label ID.']], 422);
            }

            if (!empty($data['on_hold']) && $data['on_hold']) {
                return response()->json(['message' => ['Sale Order is on hold.']], 422);
            }

            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);
            DB::commit();

            return response()->json(['data' => $data], 200);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->handleException($e);
        }
    }

    public function foldingSku(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))],
            'id_time_checking' => ['required', Rule::exists('time_tracking', 'id')]
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $performanceRepository = new PerformanceRepository();

        $dataFromTracking = $performanceRepository->getDataTrackingForFolding($input);
        if (!$dataFromTracking) {
            return response()->json(['message' => ['Tracking number not found']], 422);
        }

        if (empty($dataFromTracking->saleOrder)) {
            return response()->json(['message' => ['Tracking number not map with sale order']], 422);
        }

        if (!empty($dataFromTracking->saleOrder->order_status) && in_array($dataFromTracking->saleOrder->order_status, [SaleOrder::DRAFT, SaleOrder::ON_HOLD, SaleOrder::CANCELLED, SaleOrder::REJECTED, SaleOrder::STATUS_IN_PRODUCTION_CANCELLED])) {
            return response()->json(['message' => ['Sale order is ' . str_replace('_', ' ', $dataFromTracking->saleOrder->order_status)]], 422);
        }

        if (empty($dataFromTracking->saleOrder->barcodeItems)) {
            return response()->json(['message' => ['Tracking number not map with sale order barcode']], 422);
        }

        $saleOrderItemRepo = new SaleOrderItemRepository();
        $dataIconSaleOrder = $saleOrderItemRepo->getAllIconFromProductItem($dataFromTracking->saleOrder->items);

        $saleOrderItemBarcodeRepo = new SaleOrderItemBarcodeRepository();
        $saleOrderItemBarcodeRepo->updateBarcodeEmployeeFoldingBySaleOrderId($input['employee_id'], $dataFromTracking->saleOrder->id);
        $this->saleOrderRepository->updateFoldingForSaleOrder($dataFromTracking->saleOrder->id);
        $orderInsert = $this->saleOrderInsertRepository->getOrderInsert($dataFromTracking->saleOrder->id);

        //todo:  update time end for time checking
        if (!$dataFromTracking->saleOrder->order_folding_status) {
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);
        }

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $dataFromTracking->saleOrder->id);

        saleOrderHistory(auth()->user()?->id, $input['employee_id'], $dataFromTracking->saleOrder->id, SaleOrderHistory::UPDATE_ORDER_FOLDING_TYPE, 'Folding completed successfully');

        return response()->json([
            'iconProduct' => $dataIconSaleOrder,
            'data' => $dataFromTracking,
            'orderInsert' => $orderInsert,
            'code' => 'tracking',
            'folded' => $dataFromTracking->saleOrder->order_folding_status,
        ], 200);
    }

    public function pressLabel(ScanLabelRequest $request)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();
            $input['warehouse_id'] = config('jwt.warehouse_id');

            $data = $this->performanceRepository->scanLabel($input);
            if (empty($data)) {
                return response()->json(['message' => ['Error! Invalid Label ID.']], 422);
            }

            if (!empty($data['on_hold']) && $data['on_hold']) {
                return response()->json(['message' => ['Sale Order is on hold.']], 422);
            }

            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);
            DB::commit();

            return response()->json(['data' => $data], 200);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->handleException($e);
        }
    }
}
