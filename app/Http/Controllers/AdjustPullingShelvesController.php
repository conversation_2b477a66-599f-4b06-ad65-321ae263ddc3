<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAdjustPullingShelvesRequest;
use App\Repositories\Contracts\AdjustPullingShelvesRepositoryInterface;
use Illuminate\Http\Request;

class AdjustPullingShelvesController extends Controller
{
    private AdjustPullingShelvesRepositoryInterface $adjustPullingShelvesRepository;

    public function __construct(AdjustPullingShelvesRepositoryInterface $adjustPullingShelvesRepository)
    {
        $this->adjustPullingShelvesRepository = $adjustPullingShelvesRepository;
    }

    public function getAdjustPullingShelvesList(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return $this->adjustPullingShelvesRepository->getList($input);
    }

    public function createAdjustPullingShelves(StoreAdjustPullingShelvesRequest $request)
    {
        return $this->adjustPullingShelvesRepository->create($request);
    }
}
