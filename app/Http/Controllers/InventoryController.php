<?php

namespace App\Http\Controllers;

use App\Exports\FifoExport;
use App\Exports\FifoExportTemp;
use App\Exports\InventoryDetailExport;
use App\Exports\InventoryOverviewExport;
use App\Exports\PurchaseByVendorExport;
use App\Exports\SaleByCustomerExport;
use App\Http\Requests\FetchHistoryInventoryRequest;
use App\Models\Product;
use App\Models\SaleOrderFulfillmentReport;
use App\Repositories\InventoryRepository;
use App\Repositories\WarehouseRepository;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class InventoryController extends Controller
{
    protected InventoryRepository $inventory;

    protected WarehouseRepository $warehouseRepository;

    public function __construct(InventoryRepository $inventory, WarehouseRepository $warehouseRepository)
    {
        $this->inventory = $inventory;
        $this->warehouseRepository = $warehouseRepository;
    }

    public function fetchInventoryDetail(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;
        $deduction = $this->inventory->fetchDetail($input);

        return response()->json($deduction, 200);
    }

    public function fetchInventoryTotalDetail(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;
        $deduction = $this->inventory->fetchTotalDetail($input);

        return response()->json($deduction, 200);
    }

    public function fetchInventoryOverview(Request $request)
    {
        $input = $this->sanitizeInput($request->all());
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;
        $deduction = $this->inventory->fetchInventoryOverview($input);

        return response()->json($deduction, 200);
    }

    public function fetchInventoryTotalOverview(Request $request)
    {
        $input = $this->sanitizeInput($request->all());
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;
        $deduction = $this->inventory->fetchTotalOverview($input);

        return response()->json($deduction, 200);
    }

    public function inventoryDetailExportExcel(Request $request)
    {
        $start_date = !empty($request['start_date']) ? $request['start_date'] : Carbon::now()->subMonths(3)->toDateString();
        $end_date = !empty($request['end_date']) ? $request['end_date'] : Carbon::now()->toDateString();
        $time = $start_date . '_' . $end_date;

        return Excel::download(new InventoryDetailExport($request), "inventory_details_$time.xlsx");
    }

    public function exportInventoryOverview(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;
        $query = $this->inventory->buildQueryInventoryOverview($input);

        if (!empty($input['sort_column'])) {
            $sortColumn = in_array($input['sort_column'], ['pulling_quantity', 'rack_quantity']) ? $input['sort_column'] : 'pulling_quantity';
            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'asc';
            $query .= " ORDER BY $sortColumn $sortBy";
        }

        $time = Carbon::now()->toDateTimeString();

        return Excel::download(new InventoryOverviewExport($query), "inventory_overview_$time.xlsx");
    }

    public function report($warehouseCode, $startDate, $endDate)
    {
        $warehouse = $this->warehouseRepository->findByCode($warehouseCode);
        $dateTime = now('America/Los_Angeles')->format('Ymd_His');

        return Excel::download(new FifoExport([
            'warehouse_id' => empty($warehouse) ? 0 : $warehouse->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]), "reversed_fifo_report_exported_$dateTime.xlsx");
    }

    public function reportPurchaseByVendor($warehouseCode, $startDate, $endDate)
    {
        $warehouse = $this->warehouseRepository->findByCode($warehouseCode);

        return Excel::download(new PurchaseByVendorExport([
            'warehouse_id' => empty($warehouse) ? 0 : $warehouse->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]), "monthly-by-vendor-$startDate-$endDate-$warehouseCode.xlsx");
    }

    public function reportSaleByCustomer($startDate, $endDate)
    {
        setTimezone();

        return Excel::download(new SaleByCustomerExport([
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]), "monthly-by-customer-$startDate-$endDate.xlsx");
    }

    public function reportTemp($warehouseCode, $startDate, $endDate)
    {
        SaleOrderFulfillmentReport::whereNotNull('printed_business_day')->update(
            [
                'printed_business_day' => null,
                'printed_to_fulfill_business_day' => null,
            ],
        );
        $startDate = $startDate ?? '2022-01-01';
        $endDate = $endDate ?? '2023-12-27';
        $warehouse = $this->warehouseRepository->findByCode($warehouseCode);

        return Excel::download(new FifoExportTemp([
            'warehouse_id' => empty($warehouse) ? 0 : $warehouse->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]), "$startDate-$endDate-fifo-$warehouseCode.xlsx");
    }

    private function sanitizeInput($input)
    {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }

        return is_string($input) ? preg_replace("/[^\w\d&\-\s]/", '', trim($input)) : $input;
    }

    public function fetchHistory($sku, FetchHistoryInventoryRequest $request): JsonResponse
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = config('jwt.warehouse_id');
        $product = Product::query()
            ->where('sku', $sku)
            ->first();

        if (empty($product)) {
            return response()->json(['status' => false, 'message' => 'Sku not found.']);
        }

        $input['product_id'] = $product->id;

        return response()->json($this->inventory->fetchHistory($input));
    }
}
