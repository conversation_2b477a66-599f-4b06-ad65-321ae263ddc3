<?php

namespace App\Http\Controllers;

use App\Jobs\PrintCountTrackerJob;
use App\Models\SaleOrder;
use App\Models\Store;
use App\Models\TimeTracking;
use App\Repositories\PrintingRepository;
use App\Repositories\TimeCheckingRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PrintingController extends Controller
{
    public string $employeesUseNewLogicPrinting = 'employees_use_new_logic_printing';

    private PrintingRepository $printingRepository;

    public function __construct(PrintingRepository $printingRepository)
    {
        $this->printingRepository = $printingRepository;
    }

    public function getPrintingNew()
    {
        $skuRaw = request()->input('sku');
        $employeeId = request()->input('employee_id');
        $printRepo = new PrintingRepository();

        $label = $printRepo->removeLabelSide($skuRaw);
        $barcode = $printRepo->getBarcodeLabel($label);

        if (empty($barcode)) {
            $result = [
                'status' => false,
                'message' => 'Sku not found'
            ];

            return response()->json($result);
        }

        $side = $printRepo->getLabelPrintSide($skuRaw);

        if ($side == -1) {
            $result = [
                'status' => false,
                'message' => 'Print area not allowed'
            ];

            return response()->json($result);
        }

        $result = $printRepo->getImageBySkuSideNew($barcode, $side, $employeeId);

        return response()->json($result);
    }

    public function updatePrinting()
    {
        $employee_id = request()->input('employee_id');
        $sku = request()->input('sku');
        $label_id = request()->input('label_id');
        $is_label = request()->input('is_label');
        $status = request()->input('status');
        $time_tracking_id = request()->input('time_tracking_id');
        $warehouse_id = request()->input('warehouse_id');
        $hostName = request()->input('host_name');
        $deviceId = request()->input('device_id');
        $printer = request()->input('printer');
        $iccUsed = request()->input('icc_used');
        $printRepo = new PrintingRepository();
        $side = request()->input('side');

        if ($status == 1) {
            if ($is_label) {
                $label = $printRepo->removeLabelSide($label_id);
                $dataBarcode = $printRepo->updateEmployeePrintingLabel($employee_id, $label);
                $printRepo->insertPrinterBarcodeLog($warehouse_id, $employee_id, $label, $hostName, $side, $deviceId, $printer, $iccUsed);
            } else {
                $dataBarcode = $printRepo->updateEmployeePrinting($employee_id, $sku);
            }
            if ($time_tracking_id) {
                $timeCheckingRepository = new TimeCheckingRepository();
                $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $time_tracking_id);
            }
            if (!empty($dataBarcode?->order_id)) {
                $printRepo->updateForOrderItemHasInsertPrinting($dataBarcode?->order_id);
                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $dataBarcode?->order_id);
            }

            if (!empty($dataBarcode?->store_id) && $dataBarcode->store_id === Store::STORE_REDBUBBLE) {
                handleJob(SaleOrder::JOB_SEND_ORDER_ITEM_PRINTED_TO_REDBUBBLE, $dataBarcode->order_item_id);
            }

            return response()->json([
                'status' => true,
                'time_tracking_id' => $time_tracking_id,
                'employee_id' => $employee_id,
                'label_id' => $label_id,
                'is_label' => $is_label
            ]);
        }

        return response()->json(['status' => false]);
    }

    public function updateInkCC()
    {
        $image_id = request()->input('image_id');
        $ink_color_cc = request()->input('ink_color_cc');
        $ink_white_cc = request()->input('ink_white_cc');
        $printRepo = new PrintingRepository();
        $printRepo->updateInkCC($image_id, $ink_color_cc, $ink_white_cc);

        return response()->json(['status' => true]);
    }

    public function changeInkColor()
    {
        $image_id = request()->input('image_id');
        $ink_color = request()->input('ink_color');
        $printRepo = new PrintingRepository();
        $printRepo->changeInkColor($image_id, $ink_color);

        return response()->json(['status' => true]);
    }

    public function changeSizeImage()
    {
        $image_id = request()->input('image_id');
        $image_width = request()->input('image_width');
        $image_height = request()->input('image_height');
        $image_offset_left = request()->input('image_offset_left');
        $image_offset_top = request()->input('image_offset_top');
        $employee_id = request()->input('employee_id');

        $printRepo = new PrintingRepository();
        $printRepo->changeSizeImage($image_id, $image_width, $image_height, $image_offset_left, $image_offset_top, $employee_id);

        return response()->json(['status' => true]);
    }

    public function writeLogPrinter(Request $request)
    {
        $printRepo = new PrintingRepository();
        $printRepo->doWriteLogPrinter($request);

        return response()->json(['status' => true]);
    }

    public function sendMailPrinter(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'start_date' => 'required',
            'end_date' => 'required',
            'warehouse_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        dispatch(new PrintCountTrackerJob($request->all()))->onQueue('print-count-tracker-job');

        return response()->json(['status' => true]);
    }

    public function getPrintingEmbroidery()
    {
        $skuRaw = request()->input('label_id');
        $employeeId = request()->input('employee_id');
        $timeTrackingId = request()->input('time_tracking_id');
        $printRepo = new PrintingRepository();

        $label = $printRepo->removeLabelSideNew($skuRaw);
        if (!empty($timeTrackingId)) {
            TimeTracking::where('id', $timeTrackingId)->update(['end_time' => now()]);
        }
        $barcode = $printRepo->getBarcodeLabel($label);
        if (empty($barcode)) {
            $result = [
                'status' => false,
                'message' => 'Label not found'
            ];

            return response()->json($result);
        }

        if ($barcode && $barcode->print_method !== 'EMB') {
            $result = [
                'status' => false,
                'message' => 'Label is not embroidery'
            ];

            return response()->json($result);
        }

        $side = $printRepo->getLabelPrintSide($skuRaw);

        if ($side == -1) {
            $result = [
                'status' => false,
                'message' => 'Print area not allowed'
            ];

            return response()->json($result);
        }
        $result = $printRepo->getImageBySkuSideEmbroidery($barcode, $side, $employeeId);

        return $result;
    }

    public function getPrintingEmbroideryByLabel()
    {
        $skuRaw = request()->input('label_id');
        $employeeId = request()->input('employee_id');
        $timeTrackingId = request()->input('time_tracking_id');
        if (!empty($timeTrackingId)) {
            $timeTrackingRepository = new TimeCheckingRepository();
            $timeTrackingRepository->updateTimeCheckingForLogout(['end_time' => Carbon::now()->toDateTimeString()], $timeTrackingId);
        }
        $printRepo = new PrintingRepository();

        $label = $printRepo->removeLabelSideNew($skuRaw);
        $barcode = $printRepo->getBarcodeLabel($label);
        if (empty($barcode)) {
            $result = [
                'status' => false,
                'message' => 'Label not found'
            ];

            return response()->json($result);
        }

        if ($barcode && $barcode->print_method !== 'EMB') {
            $result = [
                'status' => false,
                'message' => 'Label is not embroidery'
            ];

            return response()->json($result);
        }

        $result = $printRepo->getImageByLabelEmbroidery($barcode, $employeeId);

        return $result;
    }
}
