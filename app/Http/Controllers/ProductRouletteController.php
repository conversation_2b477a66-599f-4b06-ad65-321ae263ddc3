<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProductRouletteRequest;
use App\Http\Requests\UpdateProductRouletteRequest;
use App\Repositories\ProductRouletteRepository;
use Illuminate\Http\Request;

class ProductRouletteController extends Controller
{
    protected ProductRouletteRepository $productRouletteRepository;

    public function __construct(ProductRouletteRepository $productRouletteRepository)
    {
        $this->productRouletteRepository = $productRouletteRepository;
    }

    public function index(Request $request)
    {
        setTimezone();
        $response = $this->productRouletteRepository->getList($request->all());

        return response()->json($response);
    }

    public function store(StoreProductRouletteRequest $request)
    {
        $response = $this->productRouletteRepository->store($request->validated());

        return response()->json($response);
    }

    public function update(UpdateProductRouletteRequest $request, $id)
    {
        $response = $this->productRouletteRepository->update($id, $request->validated());

        return response()->json($response);
    }
}
