<?php

namespace App\Http\Controllers;

use App\Exports\ReportExportLabelShipping;
use App\Exports\ShipmentExport;
use App\Http\Requests\ManualTracking\UpdateManualTrackingRequest;
use App\Repositories\ShipmentRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ShipmentController extends Controller
{
    protected $shipmentRepository;

    public function __construct(ShipmentRepository $shipmentRepository)
    {
        $this->shipmentRepository = $shipmentRepository;
    }

    public function getList(Request $request): JsonResponse
    {
        $input = $request;
        $input['warehouse_id'] = $request->warehouse_id;
        $input['is_all_warehouse'] = auth()->user()->is_all_warehouse ?? 0;
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        $shipmentRepository = new ShipmentRepository();
        $data = $shipmentRepository->getList($input, $request->get('get_total', false));

        return response()->json($data);
    }

    public function exportExcel(Request $request)
    {
        return Excel::download(new ShipmentExport($request), 'shipment.xlsx');
    }

    public function getCount(Request $request)
    {
        $input = $request;
        $input['warehouse_id'] = $request->warehouse_id;
        $input['is_all_warehouse'] = auth()->user()->is_all_warehouse ?? 0;
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        $data = $this->shipmentRepository->getCount($input);

        return response()->json($data);
    }

    public function exportReportShipmentStore(Request $request)
    {
        $dataRequest = $request->only(['shipDate', 'storeId', 'info']);
        if (!empty($dataRequest['shipDate']) && !empty($dataRequest['storeId'])) {
            $shipmentRepository = new ShipmentRepository();
            if ($dataRequest['info'] == 1) {
                return $shipmentRepository->GetDataExportReportForShipment($dataRequest['shipDate'], $dataRequest['storeId'], true);
            }
            $data = $shipmentRepository->GetDataExportReportForShipment($dataRequest['shipDate'], $dataRequest['storeId'], false);

            try {
                $export = new ReportExportLabelShipping($data);

                return Excel::download($export, $dataRequest['shipDate'] . '.xlsx');
            } catch (\Exception $e) {
                echo $e->getMessage();
            }
        } else {
            echo 'Datetime is invalid.';
        }
    }

    public function updateManualTracking(UpdateManualTrackingRequest $request, $id)
    {
        try {
            $inputs = $request->only(['carrier_code', 'tracking_number', 'ship_date', 'price']);
            $result = $this->shipmentRepository->updateManualTracking($inputs, $id);

            return $this->responseSuccess($result);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function fixShipDateShipment($id)
    {
        try {
            $result = $this->shipmentRepository->fixShipDateShipment($id);

            return $this->responseSuccess($result);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
