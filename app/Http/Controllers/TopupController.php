<?php

namespace App\Http\Controllers;

use App\Http\Requests\AddPaymentMethodRequest;
use App\Http\Requests\GetPaymentMethodRequest;
use App\Http\Requests\SubmitAutoRefillRequest;
use App\Http\Requests\SubmitRetryTopupRequest;
use App\Http\Requests\Topup\ListTopupRequest;
use App\Http\Requests\Topup\SubmitTopupRequest;
use App\Http\Resources\TopupCollection;
use App\Http\Resources\TopupResource;
use App\Models\AutoRefill;
use App\Repositories\TopupRepository;
use Illuminate\Support\Facades\DB;
use illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class TopupController extends Controller
{
    protected $topupRepository;

    public function __construct(TopupRepository $topupRepository)
    {
        $this->topupRepository = $topupRepository;
    }

    public function getListTopup(ListTopupRequest $request)
    {
        $data = $this->topupRepository->getListTopup($request);
        $showId = true;
        if (strpos($request->path(), 'seller') !== false) {
            $showId = false;
        }

        return response()->json(new TopupCollection($data, $showId));
    }

    public function deposit(SubmitTopupRequest $request)
    {
        try {
            $store = auth()->user();
            $topup = $this->topupRepository->createTopup($store, $request);
            $paramIntents = $this->getMandateData($request);
            $data = $this->topupRepository->getPaymentGateway($topup->payment_gateway)
                ->createPayment($topup, $paramIntents);

            return response()->json($data);
        } catch (\Throwable $th) {
            Log::error('TopupController.deposit: ', [$th]);

            return response()->json(['message' => 'Deposit Failed.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getPendingTopups()
    {
        $data = $this->topupRepository->countPending();

        return response()->json($data);
    }

    public function getTotalTopups(ListTopupRequest $request)
    {
        $data = $this->topupRepository->countTotalTopupByMethod($request);

        return response()->json($data);
    }

    public function retryTopup(SubmitRetryTopupRequest $request)
    {
        DB::beginTransaction();
        try {
            $params = $request->validated();
            $topupOriginal = $request->attributes->get('topup');
            $store = $topupOriginal->store;

            /** Update data auto refill */
            $autoRefill = $store->autoRefill;
            $autoRefill->update([
                'amount' => $params['amount'] ?? $autoRefill->amount,
                'limit' => $params['limit'] ?? $autoRefill->limit,
                'reason' => null,
            ]);

            /** Create new topup */
            $topupPayload = $this->topupRepository->makeDataTopupRefill(
                $store,
                $topupOriginal->payment_gateway,
                $params['payment_method_id'] ?? $topupOriginal->payment_method_id,
            );
            $topup = $this->topupRepository->createTopup($store, $topupPayload);

            /** Recharge */
            $mandateData = $this->getMandateData($request);
            $this->topupRepository->getPaymentGateway($topupOriginal->payment_gateway)
                ->charge($topup, [], $mandateData);

            DB::commit();

            return response()->json(TopupResource::make($topup));
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('stripe')->error('TopupController.retryTopup: ', [$th]);

            return response()->json(['message' => 'Retry Failed. Please try update the payment method.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function paymentFee($paymentGateway)
    {
        $paymentFee = $this->topupRepository->getPaymentGateway($paymentGateway)
            ->getPaymentFee();

        return response()->json($paymentFee);
    }

    public function paymentMethods(GetPaymentMethodRequest $request)
    {
        $paymentService = $this->topupRepository->getPaymentGateway($request->paymentGateway);
        $data = $paymentService->getPaymentMethods();

        return response()->json($data);
    }

    public function addPaymentMethod(AddPaymentMethodRequest $request, $paymentGateway)
    {
        try {
            $params = $request->validated();
            $paymentService = $this->topupRepository->getPaymentGateway($paymentGateway);
            $data = $paymentService->addPaymentMethod($params);

            return response()->json($data);
        } catch (\Throwable $th) {
            Log::channel('stripe')->error('TopupController.addPaymentMethod: ', [$th]);

            return response()->json(['message' => $th->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function removePaymentMethod($paymentGateway, $paymentMethodId)
    {
        try {
            $paymentService = $this->topupRepository->getPaymentGateway($paymentGateway);
            $paymentService->removePaymentMethod($paymentMethodId);

            return response()->json(['message' => 'Payment method removed successfully.']);
        } catch (\Throwable $th) {
            Log::channel('stripe')->error('TopupController.removePaymentMethod: ', [$th]);

            return response()->json(['message' => 'Cannot remove payment method.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getKey($paymentGateway)
    {
        return response()->json($this->topupRepository->getKey($paymentGateway));
    }

    public function autoRefill(SubmitAutoRefillRequest $request, $paymentGateway)
    {
        $params = $request->validated();
        $autoRefill = AutoRefill::updateOrCreate([
            'store_id' => auth()->id(),
            'payment_gateway' => $paymentGateway,
        ], [
            'amount' => $params['amount'],
            'status' => $params['status'] ?? false,
            'limit' => $params['limit'] ?? 0,
        ]);

        if (isset($autoRefill->reason)) {
            $autoRefill->update([
                'status' => false,
            ]);
        }

        return response()->json($autoRefill);
    }

    public function getAutoRefill($paymentGateway)
    {
        $autoRefill = AutoRefill::where('store_id', auth()->id())
            ->where('payment_gateway', $paymentGateway)
            ->first();

        return response()->json($autoRefill);
    }

    public function connection($paymentGateway)
    {
        try {
            $store = auth()->user();
            $paymentService = $this->topupRepository->getPaymentGateway($paymentGateway);
            $data = $paymentService->connection($store);

            return response()->json($data);
        } catch (\Throwable $th) {
            Log::channel('stripe')->error('TopupController.connection: ', [$th]);

            return response()->json(['message' => 'Cannot connect.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getMandateData($request)
    {
        /** Rule OF ACH Direct Debit */
        /** https://docs.stripe.com/financial-connections/other-data-powered-products?platform=web#create-a-payment-intent-or-setup-intent */
        $cfConnectingIp = $request->header('CF-Connecting-IP');
        $xForwardedFor = $request->header('X-Forwarded-For');

        return [
            'user_agent' => $request->header('User-Agent'),
            'ip_address' => $cfConnectingIp ?: ($xForwardedFor ?? '127.0.0.1'),
        ];
    }
}
