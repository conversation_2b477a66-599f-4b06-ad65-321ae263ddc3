<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreClaimMessageRequest;
use App\Http\Resources\ClaimMessageResource;
use App\Http\Resources\ClaimStatusResource;
use App\Models\SaleOrderClaimSupport;
use App\Repositories\ClaimMessageRepository;
use Exception;
use Illuminate\Http\Request;
use Carbon\Carbon;
class ClaimMessageController extends Controller
{
    protected $repository;

    public function __construct(ClaimMessageRepository $repository)
    {
        $this->repository = $repository;
        // Set timezone to PST
        setTimezone();
    }

    public function index(Request $request, SaleOrderClaimSupport $claim)
    {
        $this->authorize('view', $claim);

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);

        $result = $this->repository->getMessages($claim, $page, $limit)['output']['data'];

        // Convert timestamps to PST timezone if needed
        if (isset($result['messages'])) {
            foreach ($result['messages'] as &$message) {
                if (isset($message['created_at'])) {
                    $message['created_at'] = Carbon::parse($message['created_at']);
                }
            }
        }

        return [
            'data' => ClaimMessageResource::collection($result['messages']),
            'meta' => $result['pagination']
        ];
    }

    public function store(StoreClaimMessageRequest $request, SaleOrderClaimSupport $claim)
    {
        $this->authorize('chat', $claim);

        $message = $this->repository->store(
            $claim,
            $request->validated()
        );

        if ($message['status']) {
            // Convert timestamp to PST timezone if needed
            if (isset($message['output']['data']['created_at'])) {
                $message['output']['data']['created_at'] = Carbon::parse($message['output']['data']['created_at']);
            }
            return new ClaimMessageResource($message['output']['data']);
        } else {
            throw new Exception($message['output']['message']);
        }
    }

    public function getStatus($id)
    {
        $res = $this->repository->getStatus($id);
        
        // Convert timestamps to PST timezone if needed
        if (isset($res['output']['data'])) {
            foreach ($res['output']['data'] as &$status) {
                if (isset($status['created_at'])) {
                    $status['created_at'] = Carbon::parse($status['created_at']);
                }
            }
        }
        
        return ClaimStatusResource::collection($res['output']['data']);
    }
}
