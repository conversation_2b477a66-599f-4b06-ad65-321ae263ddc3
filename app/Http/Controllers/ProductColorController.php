<?php

namespace App\Http\Controllers;

use App\Repositories\ProductColorRepository;
use Illuminate\Http\Request;

class ProductColorController extends Controller
{
    private ProductColorRepository $productColorRepository;

    public function __construct(ProductColorRepository $productColorRepository)
    {
        $this->productColorRepository = $productColorRepository;
    }

    public function getList(Request $request)
    {
        return $this->productColorRepository->getList($request);
    }

    public function fetchAll()
    {
        return $this->productColorRepository->fetchAll();
    }

    public function create(Request $request)
    {
        return $this->productColorRepository->create($request);
    }

    public function update(int $id, Request $request)
    {
        return $this->productColorRepository->update($id, $request);
    }
}
