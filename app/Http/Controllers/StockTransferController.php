<?php

namespace App\Http\Controllers;

use App\Exports\StockTransferExport;
use App\Exports\StockTransferPackingListExport;
use App\Models\StockTransfer;
use App\Repositories\StockTransferRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class StockTransferController extends Controller
{
    protected $stockTransferRepository;

    public function __construct(StockTransferRepository $stockTransferRepository)
    {
        $this->stockTransferRepository = $stockTransferRepository;
    }

    public function index(Request $request)
    {
        return $this->stockTransferRepository->fetchAll($request);
    }

    public function requestNumber(Request $request)
    {
        $requestNumber = $this->stockTransferRepository->generateRequestNumber($request->warehouse_id);

        return response()->json($requestNumber);
    }

    public function fetch($id)
    {
        $stockTransfer = $this->stockTransferRepository->fetchDetail($id);

        return response()->json($stockTransfer);
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id_time_checking' => 'required',
            'destination_warehouse_id' => 'required|different:from_warehouse_id',
            'from_warehouse_id' => 'required',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', $request->from_warehouse_id)
            ],
            'items' => 'required|array',
            'items.*.product_id' => [
                'required'
            ],
            'items.*.request_box' => [
                'required',
                'integer',
                'min:1',
            ],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->stockTransferRepository->create($request);
    }

    public function scanBoxNumber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'stock_transfer_id' => [
                'required',
                Rule::exists('stock_transfer', 'id')->where(function ($query) use ($request) {
                    $query->where('from_warehouse_id', $request->warehouse_id)
                        ->whereIn('status', [
                            StockTransfer::PENDING_STATUS,
                            StockTransfer::PARTIAL_COMPLETED_STATUS
                        ]);
                })
            ],
            'box_number' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->stockTransferRepository->doScanBox($request);
    }

    public function confirmFulfill(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', $request->warehouse_id)
            ],
            'id_time_checking' => 'required',
            'stock_transfer_id' => 'required',
            'box_scanned' => 'required|array'
        ],
        );
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->stockTransferRepository->fulfill($request);
    }

    public function countRequestPending(Request $request)
    {
        $requestNumber = $this->stockTransferRepository->getTotalRequestPending($request->warehouse_id);

        return response()->json($requestNumber);
    }

    public function exportPackingSlip(Request $request)
    {
        $day = Carbon::now()->format('m-d-y');

        return Excel::download(new StockTransferPackingListExport($request), "packing-list-$day.xlsx");
    }

    public function exportStockTransfer($stockTransferId, Request $request)
    {
        $stockTransfer = StockTransfer::find($stockTransferId);

        if (!$stockTransfer) {
            return response()->json(['message' => 'stock transfer not found'], 404);
        }

        $isAdmin = $request->is_admin;

        return Excel::download(new StockTransferExport($stockTransferId, $isAdmin), "Stock transfer detail-{$stockTransfer->request_number}.xlsx");
    }

    public function save(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', $request->warehouse_id)
            ],
            'id_time_checking' => 'required',
            'stock_transfer_id' => 'required',
        ],
        );
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->stockTransferRepository->saveTemporaryFulfill($request);
    }
}
