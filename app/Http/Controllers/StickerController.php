<?php

namespace App\Http\Controllers;

use App\Http\Requests\Sticker\GenerateStickerRequest;
use App\Repositories\OrnamentRepository;
use App\Repositories\StickerRepository;
use Exception;
use Illuminate\Http\Request;
use Validator;

class StickerController extends Controller
{
    private OrnamentRepository $ornamentRepository;

    private StickerRepository $stickerRepository;

    public function __construct(OrnamentRepository $ornamentRepository, StickerRepository $stickerRepository)
    {
        $this->ornamentRepository = $ornamentRepository;
        $this->stickerRepository = $stickerRepository;
    }

    public function count(Request $request)
    {
        try {
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->stickerRepository->count($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function generate(GenerateStickerRequest $request)
    {
        try {
            $input = $request->only(['limit', 'employee_id', 'product_id']);
            $data = $this->stickerRepository->generatePdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function list(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->stickerRepository->list($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function history(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->stickerRepository->history($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function preset(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['product_id']);
            $data = $this->stickerRepository->preset($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function convertToAi(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'pdf_converted_id' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['pdf_converted_id']);
            $data = $this->stickerRepository->convertToAi($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
