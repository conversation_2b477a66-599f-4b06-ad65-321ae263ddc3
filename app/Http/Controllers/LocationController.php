<?php

namespace App\Http\Controllers;

use App\Repositories\Contracts\LocationRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Validator;

class LocationController extends Controller
{
    protected $locationRepository;

    public function __construct(LocationRepositoryInterface $locationRepository)
    {
        $this->locationRepository = $locationRepository;
    }

    public function fetchLocation(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $locations = $this->locationRepository->fetchAll($input);

        return response()->json($locations);
    }

    public function getLocation($id)
    {
        $location = $this->locationRepository->fetch($id);

        return response()->json($location);
    }

    public function storeLocation(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        $validator = Validator::make($input, [
            'type' => 'required',
            'num' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->locationRepository->create($input);

        return response()->json($res, 201);
    }

    public function updateLocation(Request $request, $id)
    {
        $input = $request->all();
        $warehouse_id = $request->warehouse_id;
        $validator = Validator::make($input, [
            'type' => 'required',
            'barcode' => ['required',
                Rule::unique('location')->where(function ($query) use ($input, $warehouse_id) {
                    return $query->where('warehouse_id', $warehouse_id)
                        ->where('barcode', $input['barcode']);
                })->ignore($id)]
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->locationRepository->update($id, $input);

        return response()->json($res, 200);
    }

    public function deleteLocation($id)
    {
        return $this->locationRepository->delete($id);
    }

    public function importLocation(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        $validator = Validator::make($input, [
            'items' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->locationRepository->bulkInsert($input);
    }

    public function getLocationForInventory(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $validator = Validator::make($input, [
            'barcode' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $products = $this->locationRepository->getLocationByParams($input);

        return response()->json($products);
    }

    public function checkMovingShelvesLocation($locationBarcode)
    {
        return response()->json($this->locationRepository->checkMovingShelvesLocation($locationBarcode));
    }

    public function fetchDarkPodByLocation($location)
    {
        return $this->locationRepository->fetchDarkPodByLocation($location);
    }

    public function fetchLocationDarkPod(Request $request)
    {
        return $this->locationRepository->fetchLocationDarkPod($request->all());
    }
}
