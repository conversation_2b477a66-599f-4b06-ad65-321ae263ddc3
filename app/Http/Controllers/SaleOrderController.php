<?php

namespace App\Http\Controllers;

use App\Exports\InsertOrderExport;
use App\Exports\IPViolationOrderExport;
use App\Exports\ReportExport;
use App\Exports\SaleOrderOpenExport;
use App\Exports\SaleOrderQtyPrintAreaReportExport;
use App\Exports\SaleOrderReportIExport;
use App\Http\Requests\InputEmailSampleProductRequest;
use App\Http\Requests\ManualProcessRequest;
use App\Http\Requests\ManualTracking\CreateManualTrackingRequest;
use App\Http\Requests\ReportSaleOrderAgingRequest;
use App\Http\Requests\ReportSaleOrderRequest;
use App\Http\Requests\ReportSaleOrderSlaRequest;
use App\Http\Requests\ReprintRequest;
use App\Http\Requests\SaleOrder\RerouteRequest;
use App\Http\Requests\SaleOrderQtyPrintAreaReportRequest;
use App\Http\Requests\SaleOrderReportIRequest;
use App\Http\Requests\StoreSaleOrderAddressRequest;
use App\Http\Requests\SubmitRefundOrderRequest;
use App\Http\Requests\UploadItemImageRequest;
use App\Http\Service\GetOrderService;
use App\Http\Service\ShipstationService;
use App\Models\SaleOrder;
use App\Models\SaleOrder\SaleOrderModel;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderClaimSupport;
use App\Models\SaleOrderItemImage;
use App\Models\StoreShipment;
use App\Repositories\ImportOrderCsvRepository;
use App\Repositories\SaleOrderApiRepository;
use App\Repositories\SaleOrderRepository;
use App\Repositories\StoreRepository;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Session;
use Throwable;

class SaleOrderController extends Controller
{
    private SaleOrderRepository $saleOrderRepository;

    protected StoreRepository $storeRepository;

    public function __construct(SaleOrderRepository $saleOrderRepository, StoreRepository $storeRepository)
    {
        $this->saleOrderRepository = $saleOrderRepository;
        $this->storeRepository = $storeRepository;
    }

    public function getList(Request $request): JsonResponse
    {
        setTimezone();
        $input = $request;
        $input['warehouse_id'] = $request->warehouse_id;
        $input['is_all_warehouse'] = auth()->user()->is_all_warehouse ?? 0;
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        $data = $this->saleOrderRepository->getList($input, $request->get('get_total', false));

        return response()->json($data);
    }

    public function getCount(Request $request): JsonResponse
    {
        $input = $request;
        $input['warehouse_id'] = $request->warehouse_id;
        $input['is_all_warehouse'] = auth()->user()->is_all_warehouse ?? 0;
        $data = $this->saleOrderRepository->getCount($input);

        return response()->json($data);
    }

    public function getCountInvalidAddress(Request $request): JsonResponse
    {
        $input = $request;
        $input['warehouse_id'] = $request->warehouse_id;
        $input['is_all_warehouse'] = auth()->user()->is_all_warehouse ?? 0;
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        $data = $this->saleOrderRepository->getCountInvalidAddress($input);

        return response()->json($data);
    }

    public function getBarcodeItemsNotReprint(Request $request): JsonResponse
    {
        $data = $this->saleOrderRepository->getBarcodeItemsNotReprint($request);

        return response()->json($data);
    }

    public function getBarcodeItemsNotManual(Request $request): JsonResponse
    {
        $data = $this->saleOrderRepository->getBarcodeItemsNotManual($request);

        return response()->json($data);
    }

    public function getBarcodeItemsNotManualByListOrderNumbers(Request $request): JsonResponse
    {
        $data = $this->saleOrderRepository->getBarcodeItemsNotManualByListOrderNumbers($request);

        return response()->json($data);
    }

    public function getDetail(int $id): JsonResponse
    {
        setTimezone();
        $saleOrder = $this->saleOrderRepository->getDetail($id);
        if (!$saleOrder) {
            return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
        }

        return response()->json($saleOrder);
    }

    public function getOrderItems(int $id): JsonResponse
    {
        $saleOrder = $this->saleOrderRepository->getOrderItems($id);
        if (!$saleOrder) {
            return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
        }

        return response()->json($saleOrder);
    }

    public function createComment(int $id, Request $request): JsonResponse
    {
        try {
            $saleOrder = $this->saleOrderRepository->findById($id);
            if (!$saleOrder) {
                return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
            }
            $res = $this->saleOrderRepository->createComment($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateOrderStatus(int $id, Request $request): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->updateOrderStatus($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateShippingMethod(int $id, Request $request): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->updateShippingMethod($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateTag(int $id, Request $request): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->updateTag($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateInternalNote(int $id, Request $request): JsonResponse
    {
        try {
            $saleOrder = $this->saleOrderRepository->findById($id);
            if (!$saleOrder) {
                return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
            }
            $res = $this->saleOrderRepository->updateInternalNote($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateNote(int $id, Request $request): JsonResponse
    {
        try {
            $saleOrder = $this->saleOrderRepository->findById($id);
            if (!$saleOrder) {
                return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
            }
            $res = $this->saleOrderRepository->updateNote($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateAddress(int $id, StoreSaleOrderAddressRequest $request): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->updateAddress($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateIossNumber(int $id, Request $request): JsonResponse
    {
        try {
            $data = $request->all();
            if (isset($data['tax_id_type'])) {
                $data['tax_id_type'] = strtoupper($data['tax_id_type']);
            }
            $validator = Validator::make($data, [
                'ioss_number' => 'required|string',
                'tax_id_type' => ['required', 'string', Rule::in(SaleOrder::ACCEPTED_TAX_TYPES)],
            ], [
                'ioss_number.required' => 'The tax id field is required when tax id type is present',
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $response = $this->saleOrderRepository->updateIossNumber($id, $request);

            return response()->json($response);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function verifyAddress(int $id): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->verifyAddress($id);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function reprint(ReprintRequest $request): JsonResponse
    {
        return $this->saleOrderRepository->reprint($request);
    }

    public function manualProcess(ManualProcessRequest $request): JsonResponse
    {
        return $this->saleOrderRepository->manualProcess($request);
    }

    public function reroute(RerouteRequest $request)
    {
        try {
            $res = $this->saleOrderRepository->reroute($request);

            return response()->json($res);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function checkBeforeReroute(Request $request)
    {
        try {
            $res = $this->saleOrderRepository->checkBeforeReroute($request->all());

            return response()->json($res);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function checkReroute(Request $request)
    {
        $data['orderNumber'] = $request->get('orderNumber');
        if (!empty($data['orderNumber'])) {
            $order = $this->saleOrderRepository->getSaleOrderByOrderNumber($data['orderNumber']);
            if (!empty($order) && !empty($order->items)) {
                $getOrderService = new GetOrderService();
                $saleOrderModel = new SaleOrderModel();
                $listSku = $order->items->pluck('product_sku');
                $automationRuleList = $this->saleOrderRepository->getReRouteRule();
                if (!$automationRuleList) {
                    return;
                }
                $order->imageItems = SaleOrderItemImage::with('printSide')->where('order_id', $order->id)->get();
                foreach ($automationRuleList as $automationRule) {
                    $startDate = date('Y-m-d', time());
                    $endDate = date('Y-m-d', strtotime('+1 days')); // + 1 ngày

                    $automationRule->count_sale_order = $saleOrderModel->countTotalQuantitySaleOrderByWarehouse($startDate, $endDate, $automationRule->warehouse_id);
                    $isCheck = $getOrderService->checkMoveWarehouse($listSku, $order, $order->address->zip, $order->address->country, $automationRule);
                    $automationRule->rule = json_decode($automationRule->rule);

                    if ($isCheck) {
                        $data['warehouse'] = $automationRule;
                        break;
                    }
                }
            }
            $data['saleOrder'] = $order;
        }

        return view('sale_order_reroute', $data);
    }

    public function exportReport(Request $request)
    {
        $dataGet = $request->only(['startTime', 'endTime']);
        if (!empty($dataGet['startTime']) && !empty($dataGet['endTime']) && DateTime::createFromFormat('Y-m-d,H:i:s', $dataGet['startTime']) !== false && DateTime::createFromFormat('Y-m-d,H:i:s', $dataGet['endTime']) !== false) {
            $timestampStart = strtotime($dataGet['startTime']);
            $timestampEnd = strtotime($dataGet['endTime']);
            $timestampStartPST = strtotime($dataGet['startTime']) - 25200;
            $timestampEndPST = strtotime($dataGet['endTime']) - 25200;
            $marginTime = 60 * 60;
            $startTimeSPA = date('Y-m-d H:i:s', $timestampStart - $marginTime);
            $endTimeSPA = date('Y-m-d H:i:s', $timestampEnd + $marginTime);
            $startTimeShipstation = date('Y-m-d,H:i:s', $timestampStartPST - $marginTime);
            $endTimeShipstation = date('Y-m-d,H:i:s', $timestampEndPST + $marginTime);
            $saleOrderSPA = SaleOrder::where('source', 'api')
                ->where('store_id', 316905)
                ->where('order_time', '>', $startTimeSPA)
                ->where('order_time', '<=', $endTimeSPA)
                ->get();
            $account = SaleOrderAccount::find(3);
            $shipStationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);
            $page = 1;
            $totalPage = 1;
            $saleOrderShipstation = [];
            while ($page <= $totalPage) {
                $result = $shipStationService->getDataByRangeDate($startTimeShipstation, $endTimeShipstation, $page);
                $saleOrderShipstation = array_merge($saleOrderShipstation, $result->orders);
                $page++;
                $totalPage = $result->pages;
            }
            ob_end_clean();

            $arraySPA = [];
            $arrayShipstation = [];

            foreach ($saleOrderShipstation as $orderShipstation) {
                $checkExist = false;
                $itemShipstation = [
                    'external_number' => $orderShipstation->orderNumber,
                    'order_time' => $orderShipstation->orderDate,
                    'missing' => -1
                ];
                foreach ($saleOrderSPA as $orderSPA) {
                    if ($orderShipstation->orderNumber == $orderSPA->external_number) {
                        if (
                            (strtotime($orderSPA->order_time) > $timestampStart && strtotime($orderSPA->order_time) <= $timestampEnd)
                            || (strtotime($orderShipstation->orderDate) > $timestampStartPST && strtotime($orderShipstation->orderDate) <= $timestampEndPST)
                        ) {
                            $itemShipstation['missing'] = 0;
                            $orderSPA->is_exist = true;
                        }
                        $checkExist = true;
                        break;
                    }
                }
                if (!$checkExist && strtotime($orderShipstation->orderDate) > $timestampStartPST && strtotime($orderShipstation->orderDate) <= $timestampEndPST) {
                    $itemShipstation['missing'] = 1;
                } else {
                    // echo strtotime($orderShipstation->orderDate);
                    // echo "-";
                    // echo $timestampStartPST;
                    // echo "-";
                    // echo $timestampEndPST;
                    // echo "-";
                    // echo date('Y-m-d H:i:s', strtotime($orderShipstation->orderDate));
                    // echo "-";
                    // echo date('Y-m-d H:i:s', $timestampStartPST);
                    // echo "-";
                    // echo date('Y-m-d H:i:s', $timestampEndPST);
                    // echo "-";
                    // dd($orderShipstation);
                }
                if ($itemShipstation['missing'] >= 0) {
                    $arrayShipstation[] = $itemShipstation;
                }
            }

            foreach ($saleOrderSPA as $orderSPA) {
                $itemSPA = [
                    'external_number' => $orderSPA->external_number,
                    'order_time' => $orderSPA->order_time,
                    'missing' => -1
                ];
                if (empty($orderSPA->is_exist)) {
                    if (strtotime($orderSPA->order_time) > $timestampStart && strtotime($orderSPA->order_time) <= $timestampEnd) {
                        $itemSPA['missing'] = 1;
                    }
                } else {
                    $itemSPA['missing'] = 0;
                }
                if ($itemSPA['missing'] >= 0) {
                    $arraySPA[] = $itemSPA;
                }
            }

            $data = [
                'swiftpod' => $arraySPA,
                'shipstation' => $arrayShipstation
            ];

            $name = date('Y-m-d-H-i-s', $timestampStart) . '__' . date('Y-m-d-H-i-s', $timestampEnd) . '_utc';

            try {
                $export = new ReportExport($data);

                return Excel::download($export, $name . '.xlsx');
            } catch (\Exception $e) {
                echo $e->getMessage();
            }
        } else {
            echo 'Datetime is invalid.';
        }
    }

    public function getListOrder(Request $request): JsonResponse
    {
        return response()->json($this->saleOrderRepository->getListOrder($request));
    }

    public function getOrderDetail($id, Request $request): JsonResponse
    {
        return response()->json($this->saleOrderRepository->getOrderDetail($id, $request));
    }

    public function getSummary(Request $request): JsonResponse
    {
        return response()->json($this->saleOrderRepository->getSummary($request));
    }

    public function verifyAddressV1(int $id): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->verifyAddressV1($id);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updateAddressV1(int $id, StoreSaleOrderAddressRequest $request): JsonResponse
    {
        try {
            $res = $this->saleOrderRepository->updateAddressV1($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return $this->handleException($exception);
        }

        return response()->json($res['output']);
    }

    public function cancelOrder($id): JsonResponse
    {
        return response()->json($this->saleOrderRepository->cancelOrder($id));
    }

    public function rerouteVerifyCsvFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse' => [
                'required',
                'exists:warehouse,id',
                Rule::notIn([$request->warehouse_id])
            ],
            'file' => 'required|mimes:csv,txt,xlsx,xls',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', $request->warehouse_id)
            ],
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return response()->json($this->saleOrderRepository->rerouteVerifyCsvFile($request));
    }

    public function rerouteImportCsvFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse' => [
                'required',
                'exists:warehouse,id',
                Rule::notIn([$request->warehouse_id])
            ],
            'file' => 'required|mimes:csv,txt,xlsx,xls',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', $request->warehouse_id)
            ],
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->saleOrderRepository->rerouteImportCsvFile($request);
    }

    public function getReportByDay(ReportSaleOrderRequest $request)
    {
        setTimezone();
        $input = $request->all();
        $input['path'] = $request->path();
        if ($request->path() == SaleOrder::SWIFT_POD_PATH) {
            if (empty(auth()->user()->is_all_store)) {
                $storeIds = auth()->user()->store_ids ?? [];
            }
        } else {
            if (isset(auth()->user()->is_login_support) && auth()->user()->is_login_support == 1) {
                if (auth()->user()->is_admin != 1) {
                    $storeIds = auth()->user()->store_ids;
                }
            } else {
                $storeIds = [Auth::id()];
            }
        }
        if (isset($storeIds)) {
            if (empty($input['store_id'])) {
                $input['store_id'] = $storeIds;
            } elseif (in_array($input['store_id'], $storeIds)) {
                $input['store_id'] = [$input['store_id']];
            } else {
                $newData = [];
                $startDate = Carbon::parse($input['start_date']);
                $endDate = Carbon::parse($input['end_date']);
                for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
                    $subMonthFormat = $this->saleOrderRepository->getDateFromLastMonth($date);
                    $newData[] = [
                        'date' => $date->format('m-d'),
                        'year' => $date->format('Y'),
                        'new_order' => 0,
                        'subMonthFormat' => $subMonthFormat,
                        'order_shipped' => 0,
                        'new_order_sub_year' => 0,
                        'new_order_sub_month' => 0,
                        'order_shipped_sub_month' => 0,
                        'order_shipped_sub_year' => 0,
                        'order_items_sales' => '0.00',
                        'order_items_sales_sub_month' => 0,
                        'order_items_sales_sub_year' => 0,
                    ];
                }

                return $newData;
            }
        } else {
            if ($request->path() == SaleOrder::SWIFT_POD_PATH && empty(auth()->user()->is_all_store)) {
                $input['store_id'] = [];
            } elseif (!empty($input['store_id'])) {
                $input['store_id'] = [$input['store_id']];
            }
        }

        return response()->json($this->saleOrderRepository->getReportByDay($input));
    }

    public function getAging(ReportSaleOrderAgingRequest $request)
    {
        setTimezone();
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $storeIds = [];
            }
        }
        $input = $request->all();
        if (isset($storeIds)) {
            if (empty($input['store_id'])) {
                $input['store_id'] = $storeIds;
            } elseif (in_array($input['store_id'], $storeIds)) {
                $input['store_id'] = [$input['store_id']];
            } else {
                $newData = [];
                for ($day = 0; $day < 6; $day++) {
                    $newData[] = [
                        'name' => $day == 5 ? '5+' : $day,
                        'total' => 0,
                    ];
                }

                return $newData;
            }
        } else {
            if (!empty($input['store_id'])) {
                $input['store_id'] = [$input['store_id']];
            }
        }
        // Define the different types of orders (TikTok and non-TikTok)
        $orderTypes = [false, true];
        $totals = [];

        foreach ($orderTypes as $isTiktok) {
            $input['type_order_tiktok'] = $isTiktok;
            $totals[] = $this->saleOrderRepository->getAging($input);
        }

        return response()->json([
            'total' => $totals[0],
            'total_tiktok' => $totals[1]
        ]);
    }

    public function manualTracking(CreateManualTrackingRequest $request, $id)
    {
        try {
            $inputs = $request->only(['carrier_code', 'tracking_number', 'ship_date', 'price']);
            $result = $this->saleOrderRepository->manualTracking($inputs, $id);

            return $this->responseSuccess($result);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function create(Request $request, SaleOrderApiRepository $saleOrderApiRepository)
    {
        $response = $saleOrderApiRepository->createManualOrder($request);

        return response()->json($response);
    }

    public function pushJobNotify(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'job' => [
                    'required',
                    Rule::in([SaleOrder::JOB_NOTIFY_STATUS_ORDER, SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT])
                ],
                'order_ids' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }

            $inputs = $request->only(['order_ids', 'job']);

            foreach ($inputs['order_ids'] as $orderId) {
                handleJob($inputs['job'], $orderId);
            }

            return response()->json([
                'message' => 'push job success',
            ], 200);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function retryCreateThumbForOrder($id)
    {
        try {
            return $this->saleOrderRepository->retryCreateThumbForOrder($id);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function forecast(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'date' => 'required|date',
                'warehouse_id' => 'required|numeric',
                'store_id' => 'required|numeric',
                'type' => 'required|numeric|in:0,1'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $inputs = $request->only(['date', 'warehouse_id', 'store_id', 'type']);
            $result = $this->saleOrderRepository->forecast($inputs);

            return $this->responseSuccess($result);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function exportInsert(Request $request)
    {
        $input = $request->all();
        if (empty($input['start_date']) || empty($input['end_date'])) {
            return 'Start date and end date is required!';
        }
        $filename = $input['start_date'] == $input['end_date'] ? "insert_order_{$input['start_date']}.xlsx" : "insert_order_{$input['start_date']}_{$input['end_date']}.xlsx";

        return Excel::download(new InsertOrderExport($input), $filename);
    }

    //tool update product status

    public function toolProductionStatus(Request $request)
    {
        if (!session::get('email')) {
            return view('login');
        }

        return view('production_status');
    }

    public function updateProductionStatus(Request $request)
    {
        if (!session::get('email')) {
            return view('login');
        }
        try {
            $input = $request->all();
            if (empty($input['order_ids'])) {
                return redirect()->back()->withErrors('error', 'Invalid Order Ids.');
            }
            $orderIds = trim($input['order_ids']);
            $orderIds = explode(',', $orderIds);
            $orderIds = array_map('trim', $orderIds);

            foreach ($orderIds as $id) {
                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $id);
            }

            return redirect()->back()->with('success', 'Orders add to queue successfully.');
        } catch (Exception $e) {
            return redirect()->back()->withErrors('error', 'Invalid Order Ids.');
        }
    }

    public function importOrders(Request $request)
    {
        $input = $request->all();
        $input['store_id'] = auth()->user();

        $rules = [
            'file' => 'required|file|mimes:csv,txt|max:1048'
        ];

        $customMessages = [
            'file.max' => 'The uploaded file must not exceed 1MB',
            'file.mimes' => 'The file must be a file of type: csv',
        ];

        $validator = Validator::make($input, $rules, $customMessages);
        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => 'Invalid data.', 'errors' => $validator->errors()], 422);
        }

        if (empty($input['store_id']['token'])) {
            $validator->errors()->add('token', 'Token is empty.');

            return response()->json(['status' => false, 'message' => 'Invalid data.', 'errors' => $validator->errors()], 422);
        }

        try {
            $data = Excel::toCollection(new Collection(), $request->file('file'));
            $validator->after(function ($validator) use ($data) {
                if (!isset($data[0][0])) {
                    $validator->errors()->add('header', 'Invalid columns.');

                    return;
                }
                $header = $data[0][0];
                if (empty($header)) {
                    $validator->errors()->add('header', 'Invalid columns.');

                    return;
                }
                // check items is empty or only header show message empty line item
                if ($data[0]->count() <= 1) {
                    $validator->errors()->add('header', 'Empty line items.');

                    return;
                }
                $this->checkMissing($header, $validator);
                $this->checkDuplicateColumn($header, $validator);
                $this->checkArtwork($header, $validator);
            });

            if ($validator->stopOnFirstFailure()->fails()) {
                return response()->json(['status' => false, 'message' => 'Invalid data.', 'errors' => $validator->errors()], 422);
            }
            // map header as key of data
            $sheet1 = $data[0];

            $items = $sheet1->map(function ($item) use ($sheet1) {
                return $item->mapWithKeys(function ($item, $key) use ($sheet1) {
                    $headerKey = $sheet1[0][$key];

                    return [$headerKey => $item];
                });
            });
            // remove  header row
            // header is first of items
            $header = $items[0];

            $items->shift();
            // check different
            $this->checkDifferent($items);
            $this->checkRequired($items);

            $orderIds = $items->unique('Order reference ID *');
            $saleOrderApiRepository = app()->make(SaleOrderApiRepository::class);
            $orderIds->each(function ($item) use ($items, $header, $input, $saleOrderApiRepository) {
                if (!isset($item['Status'])) {
                    $orderReferenceId = $item['Order reference ID *'];
                    $orderItems = $items->where('Order reference ID *', $orderReferenceId)->values();
                    $orders = $this->saleOrderRepository->transformOrder($header, $orderItems);
                    $res = $saleOrderApiRepository->sendRequest('orders', $input['store_id']['token'], $orders);
                    if (empty($res)) {
                        throw new Exception('Error when create order');
                    }
                    if (isset($res->status) && $res->status == false) {
                        $messageError = [];
                        foreach ($res->errors as $key => $error) {
                            if (strpos($key, '.') !== false) {
                                // TH address
                                if (strpos($key, 'address') !== false) {
                                    foreach ($error as $valueAddress) {
                                        $errColumn = ucfirst(explode('.', $key)[1]);
                                        if ($errColumn == 'Street1') {
                                            $errColumn = 'Street 1';
                                        }
                                        if (strpos($valueAddress, 'address.country') !== false) {
                                            $valueAddress = str_replace('address.country', 'address country', $valueAddress);
                                        }
                                        $messageError[] = str_replace('The ' . $key, $errColumn, $valueAddress);
                                    }
                                }
                                // TH insert
                                if (strpos($key, 'insert') !== false) {
                                    foreach ($error as $valueInsert) {
                                        $messageError[] = 'Packing slip or Gift message is invalid';
                                    }
                                }
                                // TH line_items
                                if (strpos($key, 'line_items') !== false) {
                                    $arrayKey = explode('.', $key);

                                    if ($arrayKey[2] === 'sku') {
                                        foreach ($error as $valueErrorOrderItemId) {
                                            $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key, 'line item SKU', $valueErrorOrderItemId . ','));
                                        }
                                    }
                                    if ($arrayKey[2] === 'order_item_id') {
                                        foreach ($error as $valueErrorOrderItemId) {
                                            $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key, 'line item id', $valueErrorOrderItemId . ','));
                                        }
                                    }

                                    if ($arrayKey[2] === 'preview_files') {
                                        foreach ($error as $valueErrorPreviewFile) {
                                            $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key, 'artwork URL', $valueErrorPreviewFile . ','));
                                        }
                                    }

                                    if ($arrayKey[2] === 'print_files') {
                                        foreach ($error as $valueErrorPreviewFile) {
                                            if (str_contains($valueErrorPreviewFile, $key . '.key')) {
                                                $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key . '.key', 'print area key', $valueErrorPreviewFile . ','));
                                            } else {
                                                $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key, 'print area key', $valueErrorPreviewFile . ','));
                                            }
                                        }
                                    }

                                    if ($arrayKey[2] === 'quantity') {
                                        foreach ($error as $valueErrorPreviewFile) {
                                            $this->setItemError($orderItems->get($arrayKey[1]), str_replace($key, 'quantity', $valueErrorPreviewFile . ','));
                                        }
                                    }
                                }
                            } else {
                                // Xử lý logic cho các key không có dấu chấm
                                foreach ($error as $key1 => $value1) {
                                    if (is_array($value1)) {
                                        $messageError[] = implode(';', $value1);
                                    } else {
                                        if (strpos($value1, 'order id')) {
                                            $messageError[] = str_replace('order id', 'order reference id', $value1 . ',');
                                        } elseif ($key == 'order_type') {
                                            $messageError[] = $value1 . ' (accept normal, label and Tiktok orders only)';
                                        } else {
                                            $messageError[] = $value1;
                                        }
                                    }
                                }
                            }
                        }
                        $orderItems->each(function ($item) use ($messageError) {
                            $uniqueErrors = array_unique($messageError);
                            if (empty($uniqueErrors)) {
                                $this->setItemError($item, '');
                            }
                            foreach ($uniqueErrors as $uniqueError) {
                                $this->setItemError($item, $uniqueError . ', ');
                            }
                        });
                    }
                }
            });

            $exportItems = $items->where('Status', 'Failed')->map(function ($item) {
                $item['Error'] = rtrim(str_replace(['.'], '', $item['Error']), ', ');

                return $item;
            });
            $importOrderCsvRepo = app()->make(ImportOrderCsvRepository::class);
            $fileOriginal = $importOrderCsvRepo->uploadCsvToS3Original($input, $request);

            // Lưu file lỗi lên S3
            $filePathError = null;
            if ($exportItems->count() > 0) {
                $exportItems = $exportItems
                    ->prepend(collect($header)
                        ->prepend('Error')
                        ->prepend('Status'));
                $filePathError = $importOrderCsvRepo->uploadCsvToS3Error($input, $exportItems);
            }

            // save import orders
            $save = [
                'store_id' => $input['store_id']['id'],
                'file_name' => $request->file('file')->getClientOriginalName(),
                'order_total' => $orderIds->count(),
                'order_imported' => $orderIds->count() - $orderIds->where('Status', 'Failed')->count(),
                'order_failed' => $orderIds->where('Status', 'Failed')->count(),
                'file_imported' => $fileOriginal,
                'file_failed' => $filePathError,
            ];
            $importOrderCsvRepo->create($save);

            return response()->json(['status' => true, 'message' => 'Import orders success.']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function checkMissing($header, $validator)
    {
        $requiredHeader = collect($this->saleOrderRepository->header)->keys();
        // find missing $header in $requiredHeader
        $diff = $requiredHeader->diff($header);
        if ($diff->count() > 0) {
            $validator->errors()->add('header', 'Missing or incorrect columns: ' . $diff->implode(', '));
        }
    }

    public function checkDuplicateColumn($header, $validator)
    {
        $duplicate = $header->duplicates();

        if ($duplicate->isNotEmpty()) {
            $duplicates = $duplicate->unique()->implode(', ');
            $validator->errors()->add('header', 'Duplicate columns: ' . $duplicates);
        }
    }

    public function checkRequired($items)
    {
        $items->each(function ($item) {
            $requiredFields = collect($this->saleOrderRepository->header)->filter(function ($value, $key) {
                return $value;
            })->keys();
            $requiredFields->each(function ($requiredField) use ($item) {
                if ((!in_array($item[$requiredField], [true, false]) && strlen($item[$requiredField]) != 0) || $item[$requiredField] === null) {
                    $this->setItemError($item, $requiredField . ' is required.');
                }
                if ($requiredField === 'Test order *' && gettype($item[$requiredField]) !== 'boolean' && !(in_array(strtolower($item[$requiredField]), ['true', 'false']))) {
                    $this->setItemError($item, $requiredField . ' must be TRUE or FALSE.');
                }
            });
        });
    }

    public function checkArtwork($header, $validator)
    {
        // validate artwork header
        // find contain Print area key 1. and Print area key 2 or more
        $artworkHeader = $header->filter(function ($item) {
            return Str::contains($item, 'Print area key');
        });
        //  nếu có  Print area key 1 thì phải có 2 trường còn lại là Artwork URL 1 và Preview URL 1
        $indexArtwork = 0;
        $artworkHeader->each(function ($item) use ($header, $validator, $indexArtwork) {
            //$index = Str::substr($item, -1);
            $index = $indexArtwork + 1;
            $artworkUrl = 'Artwork URL ' . $index . ' *';
            $previewUrl = 'Preview URL ' . $index;
            if (!$header->contains($artworkUrl)) {
                $validator->errors()->add('header', 'Missing header: ' . $artworkUrl);
            }
            if (!$header->contains($previewUrl)) {
                $validator->errors()->add('header', 'Missing header: ' . $previewUrl);
            }
        });
    }

    public function checkDifferent($items)
    {
        // if line item of same order have address different => error
        $orderIds = $items->unique('Order reference ID *');

        // check status order different
        $orderIds->each(function ($item) use ($items) {
            $orderReferenceId = $item['Order reference ID *'];
            $orderItems = $items->where('Order reference ID *', $orderReferenceId);

            $checkColumns = ['Order status *', 'Order Type', 'Label URL', 'Test order *', 'Name *', 'Email', 'Company', 'Phone', 'Street 1', 'Street 2',
                'City', 'State', 'Country', 'Zip', 'Phone', 'Shipping method *', 'Packing slip URL',
                'Packing slip size', 'Gift message URL', 'Gift message content', 'Tax ID', 'Tax ID type'];

            foreach ($checkColumns as $checkColumn) {
                $status = $orderItems->unique(function ($item) use ($checkColumn) {
                    return implode([
                        $item[$checkColumn],
                    ]);
                });
                if ($status->count() > 1) {
                    // set items error is true
                    $orderItems->each(function ($item) use ($checkColumn) {
                        $this->setItemError($item, $checkColumn . ' is different,');
                    });
                }
            }
        });
    }

    public function setItemError($item, $message)
    {
        $item['Status'] = 'Failed';
        $item['Error'] = !empty($item['Error']) ? $item['Error'] . ' ' . $message : $message;
        // move status and error to first column
        $item->prepend($item['Error'], 'Error');
        $item->prepend($item['Status'], 'Status');
    }

    public function exportExcel(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $filename = $input['start_date'] == $input['end_date'] ? "{$input['start_date']}_Rejected_IPViolation.xlsx" : "{$input['start_date']}_{$input['end_date']}_Rejected_IPViolation.xlsx";

        return Excel::download(new IPViolationOrderExport($input), $filename);
    }

    public function uploadFile(Request $request)
    {
        try {
            // Validate
            $request->validate([
                'issue' => $request->input('type') == SaleOrderClaimSupport::TYPE_ORDER_NEED_SOME_CARE ? 'required|numeric' : 'numeric',
                'solution' => 'required|string',
                'type' => 'required|numeric', // Adjust validation rules as needed
                'desc' => 'max:255',
                'email' => 'required|email',
                'order_item_id' => $request->input('type') == SaleOrderClaimSupport::TYPE_ORDER_NEED_SOME_CARE ? 'required|string' : '',
                'store_id' => 'required|numeric',
                'order_id' => 'required|string',
                'uploadedFiles.*' => 'required|file|mimes:jpeg,png,pdf|max:2048', // Adjust file validation as needed
            ]);
            $orderId = SaleOrder::where('encode_id', $request->order_id)->firstOrFail(['id'])->id;
            $request->merge(['order_id' => $orderId]);

            $this->saleOrderRepository->insertSaleOrderClaimSupport($request);

            return response()->json(['message' => 'Files uploaded successfully']);
        } catch (\Exception $e) {
            // Handle any exceptions
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getClaimOrder(Request $request)
    {
        $data = $this->saleOrderRepository->getClaimOrder($request);

        return response()->json($data);
    }

    public function saveComment(Request $request)
    {
        $data = $this->saleOrderRepository->saveComment($request);

        return $data;
    }

    public function getComment(Request $request)
    {
        $data = $this->saleOrderRepository->getComment($request);

        return $data;
    }

    public function insertFeedback(Request $request)
    {
        try {
            $this->saleOrderRepository->insertSaleOrderClaimSupportFeedback($request);

            return response()->json(['message' => 'Submit successfully']);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function generateName()
    {
        try {
            $this->saleOrderRepository->generateName();

            return response()->json(['message' => 'Submit successfully']);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function assignClaim(Request $request)
    {
        $this->saleOrderRepository->assignClaim($request);

        return response()->json(['message' => 'Assign successfully']);
    }

    public function updateStatusClaimOrder(Request $request)
    {
        $this->saleOrderRepository->updateStatusClaimOrder($request);

        return response()->json(['message' => 'Update Status successfully']);
    }

    public function getClaimSupport(Request $request)
    {
        $data = $this->saleOrderRepository->getListClaim($request);

        return response()->json($data);
    }

    public function getClaimDetail($id, Request $request): JsonResponse
    {
        return response()->json($this->saleOrderRepository->getDetailClaim($id, $request));
    }

    public function getCountClaim()
    {
        $data = $this->saleOrderRepository->getCountClaim();

        return response()->json($data);
    }

    public function getCountClaimStatus()
    {
        $data = $this->saleOrderRepository->getCountClaimStatus();

        return response()->json($data);
    }

    public function salesReport(SaleOrderReportIRequest $request)
    {
        return Excel::download(new SaleOrderReportIExport($request->validated()), 'sale_report_1_' . now()->format('Ymd_His') . '.xlsx');
    }

    public function quantityPrintAreaReport(SaleOrderQtyPrintAreaReportRequest $request)
    {
        return Excel::download(new SaleOrderQtyPrintAreaReportExport($request->validated()), 'print_quantity_report.xlsx');
    }

    public function statisticOrderSla(request $request)
    {
        $data = $this->saleOrderRepository->statisticOrderSla($request);

        return response()->json($data);
    }

    public function getOrderSla(request $request)
    {
        $data = $this->saleOrderRepository->getOrderSla($request);

        return response()->json($data);
    }

    public function uploadSampleImage(UploadItemImageRequest $request)
    {
        return $this->saleOrderRepository->uploadSampleImage($request);
    }

    public function submitEmail(InputEmailSampleProductRequest $request)
    {
        return $this->saleOrderRepository->submitEmail($request);
    }

    public function updateSellerShippingMethod(request $request)
    {
        $request->validate([
            'order_id' => 'string|required',
            'shipping_method' => 'string|in:standard,priority,express',
        ]);
        $order = SaleOrder::select('sale_order.*', 'sale_order_address.country')
            ->join('sale_order_address', 'sale_order_address.order_id', '=', 'sale_order.id')
            ->where('sale_order.encode_id', $request->order_id)
            ->first();
        if (!$order) {
            return response()->json(['status' => false, 'message' => 'Order not found.'], 422);
        }

        if (!in_array($order->order_status, [SaleOrder::NEW_ORDER, SaleOrder::DRAFT, SaleOrder::IN_PRODUCTION])) {
            return response()->json(['status' => false, 'message' => 'You can only change the shipping method when the order status is New Order, In Production, or Draft.'], 422);
        }
        if ($order->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER) {
            return response()->json(['status' => false, 'message' => "You can't change the shipping method with this type of order."], 422);
        }
        $isDomestic = true;
        if (!in_array(strtoupper($order->country), StoreShipment::DOMESTIC_SHIPPING)) {
            $isDomestic = false;
        }
        if (!$isDomestic && in_array($request->shipping_method, [StoreShipment::SERVICE_PRIORITY, StoreShipment::SERVICE_EXPRESS])) {
            return response()->json(['status' => false, 'message' => 'Shipping method not available for this country.'], 422);
        }
        if ($order->shipping_method == StoreShipment::SERVICE_PRIORITY && in_array($request->shipping_method, [StoreShipment::SERVICE_STANDARD, StoreShipment::SERVICE_PRIORITY]) || $order->shipping_method == StoreShipment::SERVICE_EXPRESS && in_array($request->shipping_method, [StoreShipment::SERVICE_EXPRESS, StoreShipment::SERVICE_STANDARD, StoreShipment::SERVICE_PRIORITY])) {
            return response()->json(['status' => false, 'message' => 'The shipping method cannot be downgraded to a lower shipping tier.'], 422);
        }

        DB::beginTransaction();
        try {
            $res = $this->saleOrderRepository->updateShippingMethodForSeller($request);
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('SaleOrderController@updateSellerShippingMethod: ' . $th->getMessage());

            return response()->json(['status' => false, 'message' => $th->getMessage()], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        if (!$res['status']) {
            return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return response()->json(['message' => 'Update shipping method successfully']);
    }

    public function printTypeStatistic(Request $request)
    {
        $input = $request->all();
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        if (!empty($input['store_id'])) {
            $input['store_id'] = [$input['store_id']];
        } elseif (isset($input['store_ids'])) {
            $input['store_id'] = $input['store_ids'];
        }

//        $storeIds = auth()->user()->store_ids;
//        if ($storeIds) {
//            $input['store_id'] = empty($input['store_id']) ? json_decode($storeIds) : [$input['store_id']];
//        } else {
//            $input['store_id'] = !empty($input['store_id']) ? [$input['store_id']] : [];
//        }

        $data = $this->saleOrderRepository->printTypeStatistic($input);

        return response()->json($data);
    }

    public function getListOrderStorePrepaid(Request $request)
    {
        return response()->json($this->saleOrderRepository->getListOrderStorePrepaid($request));
    }

    public function getPrePaidOrderDetail($id)
    {
        return response()->json($this->saleOrderRepository->getPrePaidOrderDetail($id));
    }

    public function getAccountingOrderDetail($encodeId)
    {
        auth()->user()->is_login_support = 1;

        return response()->json($this->saleOrderRepository->getOrderDetail($encodeId, []));
    }

    public function refundPrePaidOrder($id, SubmitRefundOrderRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $this->saleOrderRepository->refundPrePaidOrder($id, $request);
            DB::commit();

            return response()->json($data);
        } catch (Throwable $th) {
            DB::rollBack();
            Log::error('SaleOrderController->refundPrePaidOrder: ' . $th->getMessage());

            return response()->json(['message' => 'Cannot refund order.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function sellerSubmitOrder($id)
    {
        $response = $this->saleOrderRepository->sellerSubmitOrder($id);

        return response()->json(['message' => $response['message']], $response['code']);
    }

    public function exportSaleOrderOpen(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $filename = ($input['type'] ?? 'OpenOrders') . '.xlsx';

        return Excel::download(new SaleOrderOpenExport($input), $filename);
    }

    public function getSlaReport(ReportSaleOrderSlaRequest $request)
    {
        setTimezone();
        $input = $request->validated();
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                return response()->json([
                    'order_type' => [
                        ['order_type' => 'Standard', 'total' => 0],
                        ['order_type' => 'Tiktok', 'total' => 0],
                        ['order_type' => 'Label', 'total' => 0],
                    ],
                    'summary' => [
                        'on_1_day' => 0,
                        'on_1_3_days' => 0,
                        'on_3_days' => 0,
                        'total' => 0
                    ]
                ]);
            } else {
                if (empty($input['store_id'])) {
                    $input['store_id'] = $storeIds;
                } elseif (in_array($input['store_id'], $storeIds)) {
                    $input['store_id'] = [$input['store_id']];
                }
            }
        } else {
            if (!empty($input['store_id'])) {
                $input['store_id'] = [$input['store_id']];
            }
        }
        // Call repository method
        $result = $this->saleOrderRepository->getSlaReport($input);

        return response()->json($result);
    }

    public function getSkuReport(ReportSaleOrderSlaRequest $request)
    {
        setTimezone();
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $storeIds = [];
            }
        }
        $input = $request->validated();

        if (isset($storeIds)) {
            if (empty($input['store_id'])) {
                $input['store_id'] = $storeIds;
            } elseif (in_array($input['store_id'], $storeIds)) {
                $input['store_id'] = [$input['store_id']];
            } else {
                return response()->json(['message' => 'Invalid Store Id'], 400);
            }
        } else {
            if (!empty($input['store_id'])) {
                $input['store_id'] = [$input['store_id']];
            }
        }

        // Call repository method
        $result = $this->saleOrderRepository->getSkuReport($input);

        return response()->json($result);
    }

    public function getClientReport(ReportSaleOrderSlaRequest $request)
    {
        setTimezone();
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $storeIds = [];
            }
        }
        $input = $request->validated();

        if (isset($storeIds)) {
            if (empty($input['store_id'])) {
                $input['store_id'] = $storeIds;
            } elseif (in_array($input['store_id'], $storeIds)) {
                $input['store_id'] = [$input['store_id']];
            } else {
                return response()->json(['message' => 'Invalid store id'], 422);
            }
        } else {
            if (!empty($input['store_id'])) {
                $input['store_id'] = [$input['store_id']];
            }
        }

        // Call repository method
        $result = $this->saleOrderRepository->getClientReport($input);

        return response()->json($result);
    }

    public function duplicateOrder(Request $request)
    {
        return $this->saleOrderRepository->duplicateOrder($request->all());
    }

    public function getOrderQuantityReport(ReportSaleOrderAgingRequest $request)
    {
        setTimezone();
        $params = $request->all();
        $user = auth()->user();
        $storeIds = $user->store_ids ?? [];
        $isAllStore = $user->is_all_store ?? false;

        //prevent query store not allowed
        if (!empty($params['store_id']) && !in_array($params['store_id'], $storeIds) && !$isAllStore) {
            $params['store_id'] = [0];
        }

        if (!empty($params['store_id']) && !is_array($params['store_id'])) {
            $params['store_id'] = [$params['store_id']];
        }

        if (empty($params['store_id'])) {
            $params['store_id'] = $storeIds;
        }

        if (empty($params['warehouse'])) {
            $params['warehouse'] = [config('app.warehouse_id')];
        } elseif ($params['warehouse'] != 'all') {
            $params['warehouse'] = [$params['warehouse']];
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $warehouseIds = $user->warehouses->pluck('id')->toArray();
                $params['warehouse'] = $warehouseIds;
            }
        }

        return $this->saleOrderRepository->getOrderQuantityReport($params);
    }
}
