<?php

namespace App\Http\Controllers;


use App\Exports\PretreatPresetExport;
use App\Exports\PretreatPresetSKUExport;
use App\Http\Requests\CreatePretreatPresetSKURequest;
use App\Http\Requests\StorePrintingPresetSkuRequest;
use App\Http\Requests\UpdatePretreatPresetSKURequest;
use App\Repositories\Contracts\PrintingPresetSkuRepositoryInterface;
use App\Repositories\PretreatPresetSkuRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class PretreatPresetSkuController extends Controller
{
    private PretreatPresetSkuRepository $petreatPresetSkuRepository;

    public function __construct(PretreatPresetSkuRepository $petreatPresetSkuRepository)
    {
        $this->petreatPresetSkuRepository = $petreatPresetSkuRepository;
    }

    public function getList(Request $request)
    {
        return $this->petreatPresetSkuRepository->getList($request);
    }

    public function getDetail($id)
    {
        return $this->petreatPresetSkuRepository->getDetail($id);
    }

    public function create(CreatePretreatPresetSKURequest $request)
    {
        return $this->petreatPresetSkuRepository->create($request);
    }

    public function update(int $id, UpdatePretreatPresetSKURequest $request)
    {
        return $this->petreatPresetSkuRepository->update($id, $request);
    }

    public function delete(int $id)
    {
        return $this->petreatPresetSkuRepository->delete($id);
    }

    public function verifyFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return response()->json($this->petreatPresetSkuRepository->verifyCsvFile($request));
    }

    public function importCsv(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->petreatPresetSkuRepository->importCsv($request);
    }

    public function exportCsv(Request $request)
    {
        $date = date("Y-m-d");
        $mode = $request['mode'];
        return Excel::download(new PretreatPresetSKUExport([
            'mode' => $mode,
        ]), "pretreat_preset_sku_$date-$mode.xlsx");
    }
}
