<?php

namespace App\Http\Controllers;

use App\Exports\SupplyPurchaseOrderExport;
use App\Models\SupplyPurchaseOrder;
use App\Repositories\SupplyPurchaseOrderRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class SupplyPurchaseOrderController extends Controller
{
    protected $repository;

    public function __construct(SupplyPurchaseOrderRepository $repository)
    {
        $this->repository = $repository;
    }

    public function fetchAll(Request $request)
    {
        $items = $this->repository->fetchAll($request);

        return response()->json($items);
    }

    public function fetchSummary(Request $request)
    {
        setTimezone();
        $items = $this->repository->fetchSummary($request);

        return response()->json($items);
    }

    public function fetch($id)
    {
        $item = $this->repository->fetch($id);

        return response()->json($item);
    }

    public function store(Request $request)
    {
        $request->validate([
            'vendor_id' => 'required|exists:vendor,id',
            'po_number' => 'required|unique:supply_purchase_order,po_number,NULL,id,invoice_number,' . $request->input('invoice_number'),
            'invoice_number' => 'required|unique:supply_purchase_order,invoice_number,NULL,id,po_number,' . $request->input('po_number'),
            'order_date' => 'required',
            'items' => 'required|array|min:1',
            'items.*.supply_id' => 'required|numeric|exists:supply,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'sub_total' => 'required|numeric|min:0',
            'total' => 'required|numeric|min:0',
        ]);

        return $this->repository->create($request);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'vendor_id' => 'required|exists:vendor,id',
            'po_number' => 'required|unique:supply_purchase_order,po_number,' . $id . ',id,invoice_number,' . $request->invoice_number,
            'invoice_number' => 'required|unique:supply_purchase_order,invoice_number,' . $id . ',id,po_number,' . $request->po_number,
            'order_date' => 'required',
            'items' => 'required|array|min:1',
            'items.*.supply_id' => 'required|numeric',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'sub_total' => 'required|numeric|min:0',
            'total' => 'required|numeric|min:0',
        ]);

        return $this->repository->update($id, $request);
    }

    public function updateManual(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:supply_purchase_order,id',
            'field' => 'required|in:' . implode(',', (new SupplyPurchaseOrder())->getFillable()),
            'value' => 'required',
        ]);

        return $this->repository->updateOnlyField($request);
    }

    public function addMessage(Request $request)
    {
        $request->validate([
            'po_id' => 'required|exists:supply_purchase_order,id',
            'message' => 'required',
        ]);
        $this->repository->addMessage($request);

        return response()->json([
            'message' => 'add message successfully.'
        ], 201);
    }

    public function delete($id)
    {
        $res = $this->repository->delete($id);
        if (!$res) {
            return response()->json([
                'message' => 'Supply not found!'
            ], 422);
        }

        return response()->json([
            'message' => 'Delete supply successfully.'
        ], 200);
    }

    public function exportExcel(Request $request)
    {
        $request['export'] = 1;
        $orders = $this->repository->fetchAll($request);
        $fileName = 'supply_purchase_orders.xlsx';
        if (!empty($request['start_date']) && !empty($request['end_date'])) {
            $fileName = $request['start_date'] == $request['end_date'] ? "supply_purchase_orders_{$request['start_date']}.xlsx" : "supply_purchase_orders_{$request['start_date']}__{$request['end_date']}.xlsx";
        }
        $isAdmin = isset($request->is_admin) ? (bool) $request->is_admin : false;

        ///viet unit test cho SupplyPurchaseOrderExport
        return Excel::download(new SupplyPurchaseOrderExport($orders, $isAdmin), $fileName);
    }
}
