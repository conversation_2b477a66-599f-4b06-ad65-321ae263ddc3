<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Repositories\SettingRepository;
use App\Services\QBAuthService;
use Illuminate\Http\Request;

class QuickBookAuthController extends Controller
{
    protected QBAuthService $QBAuthService;
    protected SettingRepository $settingRepository;

    public function __construct(SettingRepository $settingRepository, QBAuthService $QBAuthService)
    {
        $this->settingRepository = $settingRepository;
        $this->QBAuthService = $QBAuthService;
    }

    public function showLoginForm()
    {
        return view('quick_book.login');
    }

    public function login(Request $request)
    {
        $setting = $this->settingRepository->getSettingByName(Setting::QUICK_BOOK_LOGIN_TOKEN_VERIFY);
        $token = $setting->value ?? 'SGSMONPVGJ';

        if ($token != $request->input('token_verify')) {
            return redirect()->route('quick_book.login.form')->withErrors('Login verification failed.');
        }

        return redirect()->route('quick_book.login.form')->with('url', $this->QBAuthService->login());
    }

    public function callback(Request $request)
    {
        $assigns['callback'] = $this->QBAuthService->callback($request->all());

        return view('quick_book.callback', $assigns);
    }

    public function getCompanyInfo()
    {
        $assigns['company'] = $this->QBAuthService->getCompanyInfo();

        return view('quick_book.company-info', $assigns);
    }
}
