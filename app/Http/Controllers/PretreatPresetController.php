<?php

namespace App\Http\Controllers;

use App\Exports\PretreatPresetExport;
use App\Http\Requests\PretreatPresetRequest;
use App\Repositories\PretreatPresetRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class PretreatPresetController extends Controller
{
    private PretreatPresetRepository $pretreatPresetRepository;
    public function __construct(PretreatPresetRepository $pretreatPresetRepository)
    {
        $this->pretreatPresetRepository = $pretreatPresetRepository;
    }

    public function fetch(Request $request)
    {
        return $this->pretreatPresetRepository->fetch($request->all());
    }

    public function fetchAll()
    {
        return $this->pretreatPresetRepository->fetchAll();
    }

    public function create(PretreatPresetRequest $request)
    {
        return $this->pretreatPresetRepository->create($request->all());
    }

    public function update(int $id, PretreatPresetRequest $request)
    {
        return $this->pretreatPresetRepository->update($id, $request->all());
    }

    public function verifyCsvFile(Request $request)
    {
        $validator = Validator::make(
            [
                'file'      => $request->file,
                'extension' => strtolower($request->file->getClientOriginalExtension()),
            ],
            [
                'file' => 'required',
                'extension' => 'required|in:csv,xlsx,xls',
            ],
            [
                'required' => 'The file is required',
                'in' => 'The file must be a file of type: csv, xlsx, xls.',
            ]
          );

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return response()->json($this->pretreatPresetRepository->verifyCsvFile($request));
    }

    public function importCsv(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->pretreatPresetRepository->importCsv($request);
    }

    public function exportCsv(Request $request)
    {
        $date = date("Y-m-d");
        $mode = $request['mode'];
        return Excel::download(new PretreatPresetExport([
            'mode' => $mode,
        ]), "$date-$mode.xlsx");
    }
}
