<?php

namespace App\Http\Controllers;

use App\Jobs\MakeProductionInvoiceTemp;
use App\Models\Invoice;
use App\Models\Store;
use App\Repositories\InvoiceRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    protected $invoiceRepository;

    public function __construct(
        InvoiceRepository $invoiceRepository
    ) {
        $this->invoiceRepository = $invoiceRepository;
    }

    public function index(Request $request)
    {
        return response()->json($this->invoiceRepository->getAll($request->all()));
    }

    public function export($id, $type)
    {
        $response = $this->invoiceRepository->exportInvoice($id, $type);
        if (!$response) {
            return response()->json('Invoice is generating.', Response::HTTP_NOT_FOUND);
        }

        return response()->json($response['url']);
    }

    public function generateInvoice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|exists:store,id',
            'invoice_ids' => 'sometimes|exists:invoices,id',
            'type' => 'required|in:error,general,production,shipping,insert',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $response = $this->invoiceRepository->makeGenerateInvoice($request);

        return response()->json($response);
    }

    public function downloadProductionInvoice(Request $request)
    {
        $params = $request->all();

        // Validate required parameters
        if (empty($params['start_date']) || empty($params['end_date'])) {
            return response()->json([
                'status' => false,
                'message' => 'Missing required parameters: start_date or end_date.',
            ], 400); // Bad Request
        }

        // Fetch store and validate
        $store = Store::find($params['store_id'] ?? null);
        if (!$store) {
            return response()->json([
                'status' => false,
                'message' => 'Store not found.',
            ], 404);
        }

        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $printSide = $params['print_side'] ?? null;
        $params['ip'] = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $params['type'] = Invoice::TYPE_DOWNLOAD_PRODUCTION;

        $currentTime = Carbon::now();
        $oneMinuteAgo = $currentTime->copy()->subMinute();
        $checkDuplicate = DB::table('download_invoice_history')
            ->where('store_id', $params['store_id'])
            ->where('ip', $params['ip'])
            ->whereBetween('created_at', [$oneMinuteAgo, $currentTime])
            ->exists();

        if ($checkDuplicate) {
            return response()->json([
                'status' => false,
                'message' => 'Please wait for at least 1 minute before making another request.',
            ], 429);
        }

        $this->invoiceRepository->createDownloadHistory($params);
        $downloadFulfill = $params['download_fulfill'] ?? null;
        dispatch(new MakeProductionInvoiceTemp($store, $startDate, $endDate, $printSide, $downloadFulfill))
            ->onQueue(Invoice::QUEUE_MAKE_PRODUCTION_INVOICE_TEMP);

        return response()->json([
            'status' => true,
            'message' => 'Your invoice request has been received. It may take up to 60 minutes to process due to data size. You will be notified once it’s ready.',
        ], 200);
    }

    public function getProductionInvoiceTemp(Request $request)
    {
        $data = $this->invoiceRepository->ListProductionInvoiceTemp($request);

        return response()->json($data);
    }
}
