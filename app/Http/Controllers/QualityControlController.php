<?php

namespace App\Http\Controllers;

use App\Http\Requests\QualityControlRequest;
use App\Http\Service\QualityControlService;
use App\Models\BarcodeStatus;
use App\Models\Country;
use App\Models\Employee;
use App\Models\PartNumber;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\QualityControl\QualityControlModel;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemQualityControl;
use App\Models\SaleOrderSla;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use App\Repositories\Contracts\SaleOrderRepositoryInterface;
use App\Repositories\EmployeeRepository;
use App\Repositories\PartNumberRepository;
use App\Repositories\QualityControlRepository;
use App\Repositories\SaleOrderItemQualityControlRepository;
use App\Repositories\SaleOrderItemRepository;
use App\Repositories\TimeCheckingRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Validator;

class QualityControlController extends Controller
{
    private $qualityControlService;

    private $qualityControlRepository;

    private SaleOrderRepositoryInterface $saleOrderRepository;

    private $timeCheckingRepository;

    private $partNumberRepository;

    private $employeeRepository;

    protected $saleOrderItemRepo;

    // Inject the repository through the constructor
    public function __construct(
        QualityControlService $qualityControlService,
        PartNumberRepository $partNumberRepository,
        SaleOrderRepositoryInterface $saleOrderRepository,
        TimeCheckingRepository $timeCheckingRepository,
        EmployeeRepository $employeeRepository,
        SaleOrderItemRepository $saleOrderItemRepo,
        QualityControlRepository $qualityControlRepository

    ) {
        $this->saleOrderItemRepo = $saleOrderItemRepo;
        $this->qualityControlService = $qualityControlService;
        $this->partNumberRepository = $partNumberRepository;
        $this->saleOrderRepository = $saleOrderRepository;
        $this->timeCheckingRepository = $timeCheckingRepository;
        $this->employeeRepository = $employeeRepository;
        $this->qualityControlRepository = $qualityControlRepository;
    }

    public function get(QualityControlRequest $request)
    {
        $input = $request->all();
        $saleOrderItem = $this->qualityControlService->getOrderItemByLabel($input);

        if (!$saleOrderItem) {
            return response()->json(['message' => ['Label not found']], 422);
        }

        if (!empty($saleOrderItem->order_id)) {
            $saleOrder = SaleOrder::where('id', $saleOrderItem->order_id)->with('items.productStyle:sku,type')->first();

            if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, [SaleOrder::DRAFT, SaleOrder::ON_HOLD, SaleOrder::CANCELLED, SaleOrder::REJECTED])) {
                return response()->json(['message' => ['Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)]], 422);
            }
        }

        // get all icon of order
        $saleOrderItemRepo = new SaleOrderItemRepository();
        $dataIconSaleOrder = $saleOrderItemRepo->getAllIconFromProductItem($saleOrder->items);

        // todo: insert log to sale_order_item_quality_control
        $checkExist = DB::table('sale_order_item_quality_control')->where('label_id', $input['label'])->where('status', '=', 'pass')->first();

        $qualityControlId = $this->qualityControlService->insertLogCheckQualityControl($saleOrderItem, $input['employee_id']);
        $this->qualityControlService->updateBarcodeQcAt($saleOrderItem, $input['employee_id']);

        //todo:  update time end for time checking
        if (!$checkExist) {
            $endTime = date('Y-m-d H:i:s');
            foreach ($input['id_time_checking'] as $item) {
                $this->timeCheckingRepository->updateTimeChecking(['end_time' => $endTime], $item);
            }
        }
        $saleOrderItemBarcode = new SaleOrderItemBarcode();
        $totalItemOrder = $saleOrderItemBarcode->countOrderByOrderId($saleOrderItem->order_id);
        $totalItemOrderScan = $saleOrderItemBarcode->countOrderItemScan($saleOrderItem->order_id);

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItem->order_id);

        //log timeline for label
        saleOrderHistory(auth()->user()->id, $input['employee_id'], $saleOrderItem->order_id, SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE, 'Label ID ' . $saleOrderItem->label_id . ' has been approved by quality control');

        return response()->json([
            'data' => $saleOrderItem,
            'qualityControlId' => $qualityControlId,
            'iconProduct' => $dataIconSaleOrder,
            'totalItem' => $totalItemOrder,
            'totalItemScan' => $totalItemOrderScan,
            'isCount' => !$checkExist
        ]);
    }

    public function buildLinkImageThumb($row, $is750 = false)
    {
        $nameSku = $row->sku . '-' . $row->print_side;
        if ($is750) {
            return env('AWS_S3_URL', '') . '/thumb/750/' . $row->order_date . '/' . $nameSku . '.png';
        }

        return env('AWS_S3_URL', '') . '/thumb/250/' . $row->order_date . '/' . $nameSku . '.png';
    }

    public function fetchAll(Request $request)
    {
        $rawData = $request->all();

        $list = $this->qualityControlService->getAll($rawData);

        return response()->json($list);
    }

    public function report()
    {
        $rawData = request()->input();
        $rawData['warehouse_id'] = config('jwt.warehouse_id');

        $employeeList = Employee::all()->pluck('name', 'id');

        $saleOrderItemQualityControl = new SaleOrderItemQualityControlRepository();
        $listItem = $saleOrderItemQualityControl->list($rawData, $rawData['get_total'] ?? null);
        if (!empty($rawData['get_total'])) {
            return $listItem;
        }
        $time_format = 'h:i m-d-Y';

        foreach ($listItem as &$detail) {
            $detail->employee_pull_id_name = 'N/A';
            $detail->employee_pretreat_id_name = 'N/A';
            $detail->employee_print_id_name = 'N/A';
            $detail->qc_employee_id_name = 'N/A';

            $detail->qc_created_at = date_format(date_create($detail->qc_created_at), $time_format);

            $detail->employee_pull_id_name = $employeeList[$detail->employee_pull_id] ?? '';
            $detail->pulled_at = date_format(date_create($detail->pulled_at), $time_format);

            $detail->employee_pretreat_id_name = $employeeList[$detail->employee_pretreat_id] ?? '';
            $detail->pretreated_at = date_format(date_create($detail->pretreated_at), $time_format);

            $detail->employee_print_id_name = $employeeList[$detail->employee_print_id] ?? '';
            $detail->printed_at = date_format(date_create($detail->printed_at), $time_format);

            $detail->qc_employee_id_name = $employeeList[$detail->qc_employee_id] ?? '';
            $detail->qc_at = date_format(date_create($detail->qc_at), $time_format);
            $detail->status = $this->generateStatus($detail->status);
        }

        return response()->json($listItem);
    }

    public function reportByTeam(Request $request)
    {
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        if (is_null($startDate) || is_null($endDate)) {
            $sevenDaysAgoPST = now()->subDays(7)->startOfDay();
            $sevenDaysAgo = $sevenDaysAgoPST->addHours(7);
            $startDate = $sevenDaysAgo->toDateTimeString();
            $endDate = now()->endOfDay()->addHours(7)->toDateTimeString();
        } else {
            $startDate = Carbon::parse($startDate)->startOfDay()->addHours(7)->toDateTimeString();
            $endDate = Carbon::parse($endDate)->endOfDay()->addHours(7)->toDateTimeString();
        }
        $warehouseId = $request->warehouse_id;
        $report = $this->qualityControlService->handleQcWarehouseReport($warehouseId, $startDate, $endDate);

        return response()->json($report);
    }

    public function generateStatus($status)
    {
        return match ($status) {
            'out of tolerance' => 'size is not within -/+ 1 inch tolerance',
            'misaligned print' => 'skewed/ off centered print',
            default => $status,
        };
    }

    public function getDataQualityControl(QualityControlRequest $request)
    {
        $input = $request->all();
        $saleOrderItem = $this->qualityControlService->getOrderItemByLabel($input);

        if (!$saleOrderItem) {
            return response()->json(['message' => ['Label not found']], 422);
        }

        if (!empty($saleOrderItem->order_id)) {
            $saleOrder = SaleOrder::where('id', $saleOrderItem->order_id)->with('items.productStyle:sku,type')->first();

            if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, [SaleOrder::DRAFT, SaleOrder::ON_HOLD, SaleOrder::CANCELLED, SaleOrder::REJECTED])) {
                return response()->json(['message' => ['Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)]], 422);
            }
        }
        // get all icon of order
        $dataIconSaleOrder = $this->saleOrderItemRepo->getAllIconFromProductItem($saleOrder->items);
        $saleOrderItemBarcode = new SaleOrderItemBarcode();
        $totalItemOrder = $saleOrderItemBarcode->countOrderByOrderId($saleOrderItem->order_id);
        $totalItemOrderScan = $saleOrderItemBarcode->countOrderItemScan($saleOrderItem->order_id);
        //log history for scan label in QC
        saleOrderHistory(
            auth()->user()?->id,
            $input['employee_id'],
            $saleOrderItem->order_id,
            SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
            'Label ID ' . $input['label'] . ' scanned by QC',
        );

        setTimezone();
        $slaOrder = SaleOrderSla::where('order_id', $saleOrderItem->order_id)->first();
        if ($slaOrder) {
            $slaOrder->sla_expired_at_utc = $slaOrder->expired_at ? shiftTimezoneToUTC($slaOrder->expired_at) : null;
            $slaOrder->order_status = $saleOrder->order_status ?? null;
        }

        return response()->json([
            'data' => $saleOrderItem,
            'iconProduct' => $dataIconSaleOrder,
            'totalItem' => $totalItemOrder,
            'totalItemScan' => $totalItemOrderScan,
            'slaOrder' => $slaOrder
        ]);
    }

    public function passQualityControl(QualityControlRequest $request)
    {
        $input = $request->all();
        $warehouseId = config('jwt.warehouse_id');
        $labelId = $input['label'];
        $employeeId = $input['employee_id'];

        $checkExist = SaleOrderItemQualityControl::where('label_id', $labelId)->orderBy('id', 'desc')->first();
        if ($checkExist && $checkExist->status == SaleOrderItemQualityControl::STATUS_PASS_QC) {
            return response()->json(['message' => ['Label ID ' . $labelId . ' has been passed']], 422);
        }

        $itemBarcode = SaleOrderItemBarcode::where('label_id', $labelId)->where('is_deleted', 0)->first();
        $orderId = $itemBarcode->order_id;

        $rootLabelId = (isset($itemBarcode->label_root_id) && $itemBarcode->label_root_id != '0') ? $itemBarcode->label_root_id : $itemBarcode->label_id;
        $saleOrder = SaleOrder::where('id', $orderId)->first();

        if (in_array($saleOrder->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
            return response()->json(['message' => ['Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)]], 422);
        }

        $employeeIds = TimeTracking::whereIn('id', $input['id_time_checking'])->pluck('employee_id')->unique()->toArray();

        $checkExist = SaleOrderItemQualityControl::select('employee_id')->where('label_id', $input['label'])
            ->whereIn('employee_id', $employeeIds)->orderBy('id', 'desc')->first();

        SaleOrderItemQualityControl::insert([
            'order_id' => $orderId,
            'order_item_id' => $itemBarcode->order_item_id,
            'user_id' => auth()->user()->id ?? null,
            'sku' => $itemBarcode->sku,
            'label_id' => $labelId,
            'created_at' => now(),
            'updated_at' => now(),
            'employee_id' => $employeeId,
            'warehouse_id' => $warehouseId,
            'label_root_id' => $rootLabelId
        ]);

        $this->qualityControlRepository->updateBarcodeStatus($rootLabelId);

        // update staged_at when pass QC
        SaleOrderItemBarcode::where('label_id', $labelId)->update([
            'staged_at' => now(),
            'employee_staging_id' => $employeeId,
            'qc_at' => now(),
            'employee_qc_id' => $employeeId
        ]);

        saleOrderHistory(
            auth()->user()->id,
            $employeeId,
            $orderId,
            SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
            'Label ID ' . $labelId . ' approved by QC');

        if (!$checkExist) {
            $endTime = now();
            foreach ($input['id_time_checking'] as $item) {
                $this->timeCheckingRepository->updateTimeChecking(['end_time' => $endTime], $item);
            }
        }

        $totalItemBarcode = SaleOrderItemBarcode::where('order_id', $orderId)->where('is_deleted', 0)->count();
        // use stage status to check if all item in order has been staged ( only pass QC so stage not null )
        $totalItemBarcodeQced = SaleOrderItemBarcode::where('order_id', $orderId)->where('is_deleted', 0)->whereNotNull('staged_at')->count();
        $checkAllItemPassQc = $totalItemBarcode == $totalItemBarcodeQced;

        if ($checkAllItemPassQc) {
            SaleOrder::where('id', $orderId)->update([
                'order_staged_status' => true,
                'order_staged_at' => now(),
                'order_qc_status' => true,
                'order_qc_at' => now(),
            ]);
            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $orderId);
        }

        return response()->json([
            'is_count' => !$checkExist,
            'isCount' => !$checkExist,
            'status' => $checkAllItemPassQc,
            'data' => $itemBarcode,
            'total_item' => $totalItemBarcode,
            'totalItem' => $totalItemBarcode,
            'total_item_scan' => $totalItemBarcodeQced,
            'totalItemScan' => $totalItemBarcodeQced,

        ]);
    }

    public function rejectQc(Request $request)
    {
        $input = $request->all();
        // convert employeeId to employee_id but if employeeId is not exist then use employee_id
        $input['employee_id'] = $input['employeeId'] ?? $input['employee_id'] ?? null;
        // convert hasReprint to has_reprint but if hasReprint is not exist then use has_reprint
        $input['has_reprint'] = $input['hasReprint'] ?? $input['has_reprint'] ?? null;

        $validator = Validator::make($input, [
            'label' => ['required', Rule::exists('sale_order_item_barcode', 'label_id')->where('warehouse_id', config('jwt.warehouse_id'))->where('is_deleted', false)],
            'status' => 'required',
            'has_reprint' => ['required', Rule::in([true, false, 'true', 'false'])],
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'image' => 'sometimes|nullable|file|mimes:jpeg,png,jpg|mimetypes:image/jpeg,image/jpg,image/png|max:1024',
            'id_time_checking' => 'required|array',
            'id_time_checking.*' => 'exists:time_tracking,id'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $labelId = $input['label'];

        $itemBarcode = SaleOrderItemBarcode::where('label_id', $labelId)->where('is_deleted', 0)->first();
        $orderId = $itemBarcode->order_id;
        $employeeId = $input['employee_id'];
        $hasReprint = $input['has_reprint'];
        $rootLabelId = (isset($itemBarcode->label_root_id) && $itemBarcode->label_root_id != '0') ? $itemBarcode->label_root_id : $itemBarcode->label_id;

        $orderItem = SaleOrderItem::where('id', $itemBarcode->order_item_id)->first();

        $partNumberId = $itemBarcode->part_number_id;

        //Mexico warehouse
        if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
            if (empty($input['country']) || !Country::where('iso2', $input['country'])->exists()) {
                return response()->json(['message' => ['Country not found !']], 422);
            }

            if (!$partNumberId) {
                $partNumber = PartNumber::where('product_id', $orderItem->product_id)
                    ->where('country', $input['country'])
                    ->first();
                if (!$partNumber) {
                    return response()->json(['message' => ['Part number not found !']], 422);
                }

                $partNumberId = $partNumber->id;
            }
        }

        $employeeIds = TimeTracking::whereIn('id', $input['id_time_checking'])
            ->pluck('employee_id')
            ->unique()
            ->toArray();

        $checkExist = SaleOrderItemQualityControl::select('employee_id')->where('label_id', $labelId)
            ->whereIn('employee_id', $employeeIds)->first();

        $latestOrder = PurchaseOrder::select('purchase_order_item.*', 'purchase_order.id as po_id')
        ->join('purchase_order_item', 'purchase_order_item.po_id', '=', 'purchase_order.id')
        ->whereNotNull('purchase_order_item.price')
        ->where('purchase_order_item.product_id', $orderItem->product_id)
        // ->where('purchase_order.order_status', 'completed')
        ->orderBy('purchase_order.id', 'desc')
        ->first();

        $newQc = SaleOrderItemQualityControl::create([
            'order_id' => $orderId,
            'order_item_id' => $itemBarcode->order_item_id,
            'user_id' => auth()->user()->id ?? null,
            'sku' => $itemBarcode->sku,
            'status' => $input['status'],
            'label_id' => $labelId,
            'created_at' => now(),
            'updated_at' => now(),
            'employee_id' => $employeeId,
            'warehouse_id' => config('jwt.warehouse_id'),
            'label_root_id' => $rootLabelId,
            'addition_cost' => $latestOrder ? $latestOrder->price : 0.00,
            'part_number_id' => $partNumberId,
        ]);

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = $newQc->id . '.' . $file->getClientOriginalExtension();
            $path = Storage::disk('s3')->putFileAs(
                QualityControlModel::QC_FAILURE_FOLDER,
                $file,
                $filename,
            );
            $newQc->image = $path;
        }

        $this->qualityControlRepository->updateBarcodeStatus($rootLabelId, $input['status']);

        // update barcode qc at
        SaleOrderItemBarcode::where('label_id', $labelId)->update([
            'qc_at' => now(),
            'employee_qc_id' => $employeeId,
            'part_number_id' => $partNumberId
        ]);

        $newQc->save();

        $message = 'Label ID ' . $itemBarcode->label_id . ' rejected by QC.';

        if ($hasReprint === true || $hasReprint === 'true') {
            $inputReprint = [
                'label_ids' => [$labelId],
                'type' => 'QC',
                'employee_id' => $employeeId,
                'reason' => $input['status'],
            ];
            $message = 'Label ID ' . $labelId . ' rejected by QC, request reprint.';
            saleOrderHistory(
                auth()->user()->id,
                $employeeId,
                $itemBarcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
                $message,
            );
            $this->saleOrderRepository->reprint($inputReprint);
        } else {
            saleOrderHistory(
                auth()->user()->id,
                $employeeId,
                $itemBarcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
                $message,
            );
        }

        rejectQualityControlEmit($employeeId);

        if (!$checkExist) {
            $endTime = now();
            foreach ($input['id_time_checking'] as $item) {
                $this->timeCheckingRepository->updateTimeChecking(['end_time' => $endTime], $item);
            }
        }

        // @todo move check out of stock to service background
        $quantity = ProductQuantity::where('product_id', $orderItem->product_id)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->get()
            ->sum(function ($item) {
                return $item->incoming_stock + $item->quantity;
            });

        if ($quantity <= 0) {
            $barcodeStatus = BarcodeStatus::where('label_id', $labelId)->first();

            if (isset($barcodeStatus) && !$barcodeStatus->sent_oos_email) {
                handleJob(SaleOrder::JOB_SEND_MAIL_ALERT_OOS, [
                    'id' => $itemBarcode->id,
                    'warehouse_id' => config('jwt.warehouse_id'),
                ]);
            }
        }
        // lấy addition cost cho phase 3 qc - sla report
        $this->qualityControlService->insertBarcodeAdditionCost($orderItem->product_id, $rootLabelId);

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $itemBarcode->order_id);

        return response()->json(['message' => 'success',
            'isCount' => !$checkExist,
            'is_count' => !$checkExist,
            'data' => $input,
        ]);
    }

    public function getSlaQc(request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required',
            'end_date' => 'required',
            'type' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $input = $request->all();
        $saleOrderItem = $this->qualityControlService->getSlaQc($input);

        return response()->json([
            'data' => $saleOrderItem,
        ]);
    }

    public function getListSlaQc(request $request)
    {
        $input = $request->all();
        if (!empty($input['total']) && $input['total'] == true) {
            $saleOrderItem = $this->qualityControlService->getListSlaQc($input, true);
        } else {
            $saleOrderItem = $this->qualityControlService->getListSlaQc($input);
        }

        return response()->json([
            'data' => $saleOrderItem,
        ]);
    }

    public function getReportSlaQc(request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required',
            'current_date_start' => 'required',
            'current_date_end' => 'required',
            'previous_date_start' => 'required',
            'previous_date_end' => 'required',

        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $request->all();
        $report = $this->qualityControlService->getReportSlaQc($data);

        return response()->json($report);
    }

    public function getWeeklySlaQc(request $request)
    {
        $data = $request->all();
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',

        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $startDate = $data['start_date'];
        $endDate = $data['end_date'];
        if (is_null($startDate) || is_null($endDate)) {
            $sevenDaysAgoPST = now()->subDays(7)->startOfDay();
            $sevenDaysAgo = $sevenDaysAgoPST->addHours(7);
            $startDate = $sevenDaysAgo->toDateTimeString();
            $endDate = now()->endOfDay()->addHours(7)->toDateTimeString();
        } else {
            $startDate = Carbon::parse($startDate)->startOfDay()->toDateTimeString();
            $endDate = Carbon::parse($endDate)->endOfDay()->toDateTimeString();
        }
        $warehouseId = $data['warehouse_id'];
        $report = $this->qualityControlService->getWeeklySlaQc($warehouseId, $startDate, $endDate);

        return response()->json($report);
    }
}
