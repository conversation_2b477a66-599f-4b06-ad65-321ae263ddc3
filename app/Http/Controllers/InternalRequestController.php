<?php

namespace App\Http\Controllers;

use App\Http\Requests\CheckValidBySkuRequest;
use App\Http\Requests\ConfirmInternalRequest;
use App\Http\Requests\CreateInternalRequest;
use App\Http\Requests\InternalRequest\AvailableBoxRequest;
use App\Http\Requests\InternalRequest\EmployeeLoginRequest;
use App\Http\Requests\InternalRequest\MissingBoxRequest;
use App\Http\Requests\ReceiveInternalRequest;
use App\Http\Requests\ReleaseInternalRequest;
use App\Http\Requests\UpdateInternalRequest;
use App\Http\Requests\UpdatePriorityRequest;
use App\Models\InternalRequest;
use App\Repositories\EmployeeRepository;
use App\Repositories\InternalRequestRepository;
use App\Repositories\LocationRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InternalRequestController extends Controller
{
    protected InternalRequestRepository $internalRequestRepository;

    protected EmployeeRepository $employeeRepository;

    protected LocationRepository $locationRepository;

    public function __construct(
        InternalRequestRepository $internalRequestRepository,
        EmployeeRepository $employeeRepository,
        LocationRepository $locationRepository
    ) {
        $this->internalRequestRepository = $internalRequestRepository;
        $this->employeeRepository = $employeeRepository;
        $this->locationRepository = $locationRepository;
    }

    public function getList(Request $request)
    {
        setTimezone();
        $input = $request->all();
        if (!empty($input['employee'])) {
            $employee = $this->employeeRepository->fetch($input['employee']);
            if (empty($employee)) {
                return response()->json(['message' => 'Employee not exist'], 422);
            }
            if (!empty($input['warehouse_id']) && $input['warehouse_id'] != $employee->warehouse_id) {
                return response()->json(['message' => 'Employee belongs to another warehouse'], 422);
            }
            $input['warehouse_id'] = !empty($input['warehouse_id']) ? $input['warehouse_id'] : $employee->warehouse_id;
        }
        if (!empty($input['location'])) {
            $location = $this->locationRepository->findByBarcode($input['location']);
            if (empty($location)) {
                return response()->json(['message' => 'Location not exist'], 422);
            }
            $input['warehouse_id'] = !empty($input['warehouse_id']) ? $input['warehouse_id'] : $location->warehouse_id;
            if ($location->warehouse_id != $input['warehouse_id']) {
                return response()->json(['message' => 'This location belongs to another warehouse'], 422);
            }
        }

        if (!empty($input['app']) && $input['app'] == InternalRequest::INVENTORY_DEPARTMENT) {
            $input['exclude_stock_level'] = [InternalRequest::OUT_OF_STOCK, InternalRequest::INCOMING];
            $input['is_suggest_location'] = true;
        }
        if (empty($input['app'])) {
            $input['not_paginate'] = false;
        } else {
            $input['not_paginate'] = true;
        }

        $data = $this->internalRequestRepository->getList($input);

        return response()->json($data);
    }

    public function getPickingUp($employee_id, Request $request)
    {
        setTimezone();
        $input = $request->all();
        $employee = $this->employeeRepository->fetch($employee_id);

        if (empty($employee)) {
            return response()->json(['message' => 'Employee not found'], 404);
        }

        if (!empty($input['location'])) {
            $location = $this->locationRepository->findByBarcode($input['location']);
            if (empty($location)) {
                return response()->json(['message' => 'Location not exist'], 422);
            }

            if ($location->warehouse_id != $employee->warehouse_id) {
                return response()->json(['message' => 'This location belongs to another warehouse'], 422);
            }
        }

        $data = $this->internalRequestRepository->getCurrentPickingUp($employee_id, $input);

        return response()->json($data);
    }

    public function confirm($id, ConfirmInternalRequest $request)
    {
        $input = $request->validated();
        try {
            $res = $this->internalRequestRepository->confirm($id, $input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function updatePriority($id, UpdatePriorityRequest $request)
    {
        try {
            $input = $request->validated();
            $res = $this->internalRequestRepository->updatePriority($id, $input['type']);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function receiveRequest($id, ReceiveInternalRequest $request)
    {
        $input = $request->validated();
        try {
            $res = $this->internalRequestRepository->receiveRequest($id, $input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            setTimezone();
            $res['output']['data'] = $this->internalRequestRepository->getDetail($id);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function fulfillRequest($id, UpdateInternalRequest $request)
    {
        $input = $request->validated();
        try {
            $res = $this->internalRequestRepository->fulfillRequest($id, $input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            setTimezone();
            $res['output']['data'] = $this->internalRequestRepository->getDetail($id);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function getCount(Request $request)
    {
        return response()->json($this->internalRequestRepository->getCount($request->all()));
    }

    public function checkValidTypeBySku(CheckValidBySkuRequest $request)
    {
        $input = $request->validated();

        return response()->json($this->internalRequestRepository->checkValidTypeBySku($input));
    }

    public function getCountStatus(Request $request)
    {
        $input['status'] = $request->status ?? InternalRequest::NEW_STATUS;
        $input['warehouse_id'] = config('jwt.warehouse_id');

        return response()->json($this->internalRequestRepository->getCountStatus($input));
    }

    public function delete($id, Request $request)
    {
        try {
            $res = $this->internalRequestRepository->delete($id, $request->all());
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res);
    }

    public function reject($id, Request $request)
    {
        try {
            $res = $this->internalRequestRepository->reject($id, $request->all());
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res);
    }

    public function checkExistRequest(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = config('jwt.warehouse_id');

        return response()->json($this->internalRequestRepository->checkExistRequest($input));
    }

    public function prepareData()
    {
        return response()->json($this->internalRequestRepository->prepareData());
    }

    public function create(CreateInternalRequest $request)
    {
        try {
            $input = $request->validated();
            $res = $this->internalRequestRepository->create($input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res);
    }

    public function missingBox(MissingBoxRequest $request)
    {
        try {
            $input = $request->validated();
            $res = $this->internalRequestRepository->missingBox($input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res);
    }

    public function allBoxMissing(MissingBoxRequest $request)
    {
        try {
            $input = $request->validated();
            $res = $this->internalRequestRepository->allBoxMissing($input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res);
    }

    public function login(EmployeeLoginRequest $request)
    {
        try {
            $input = $request->validated();
            $res = $this->internalRequestRepository->login($input);

            return response()->json($res);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function release($id, ReleaseInternalRequest $request)
    {
        $input = $request->validated();
        try {
            $res = $this->internalRequestRepository->releaseRequest($id, $input);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            return response()->json($res['output'], Response::HTTP_OK);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function availableBox(AvailableBoxRequest $request)
    {
        setTimezone();
        $input = $request->validated();
        try {
            $res = $this->internalRequestRepository->availableBox($input);

            return response()->json($res);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getLocationAndRequest($barcode = null)
    {
        return response()->json(
            $this->internalRequestRepository->getLocationAndRequest($barcode),
        );
    }

    public function getCountRbtRequests(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return response()->json($this->internalRequestRepository->getCountRbtRequests($input));
    }
}
