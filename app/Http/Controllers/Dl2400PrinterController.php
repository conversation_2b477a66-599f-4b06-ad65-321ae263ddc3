<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dl2400PrinterPushLogRequest;
use App\Http\Requests\Dl2400PrinterStoreRequest;
use App\Repositories\Dl2400PrinterRepository;
use Illuminate\Http\JsonResponse;

class Dl2400PrinterController extends Controller
{
    protected Dl2400PrinterRepository $dl2400PrinterRepository;

    public function __construct(Dl2400PrinterRepository $dl2400PrinterRepository)
    {
        $this->dl2400PrinterRepository = $dl2400PrinterRepository;
    }

    public function getDevice($deviceId): JsonResponse
    {
        $data = $this->dl2400PrinterRepository->getDevice($deviceId);

        return response()->json($data);
    }

    public function updateDevice($deviceId, Dl2400PrinterStoreRequest $request): JsonResponse
    {
        $data = $this->dl2400PrinterRepository->updateDevice($deviceId, $request->validated());

        return response()->json($data);
    }

    public function pushLog($deviceId, Dl2400PrinterPushLogRequest $request): JsonResponse
    {
        $data = $this->dl2400PrinterRepository->pushLog($deviceId, $request->validated());

        return response()->json($data);
    }
}
