<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    private UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function fetchUser(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $users = $this->userRepository->fetchAll($input);

        return response()->json($users);
    }

    public function getListAllUser(Request $request)
    {
        return $this->userRepository->getListAllUser($request);
    }

    public function create(StoreUserRequest $request): JsonResponse
    {
        return $this->userRepository->create($request);
    }

    public function update(int $id, StoreUserRequest $request): JsonResponse
    {
        return $this->userRepository->update($id, $request);
    }

    public function delete(int $id)
    {
        return $this->userRepository->delete($id);
    }
}
