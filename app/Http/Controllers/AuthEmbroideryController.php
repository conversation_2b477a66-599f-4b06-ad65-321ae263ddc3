<?php

namespace App\Http\Controllers;

use App\Repositories\EmbroideryRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthEmbroideryController extends Controller
{
    protected $embroideryRepository;

    public function __construct(
        EmbroideryRepository $embroideryRepository
    ) {
        $this->embroideryRepository = $embroideryRepository;
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), array_merge(
            [
                'password' => 'required|string|min:6',
                'username' => 'required|string|max:255',
            ],
        ));

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        return $this->embroideryRepository->authenticate($validator->validated());
    }
}
