<?php

namespace App\Http\Controllers;

use App\Exports\PurchaseOrderExport;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderHistory;
use App\Models\PurchaseOrderItem;
use App\Repositories\Contracts\PurchaseOrderRepositoryInterface;
use App\Repositories\ProductQuantityRepository;
use App\Rules\UpdatePurchaseOrderRule;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class PurchaseOrderController extends Controller
{
    protected $purchaseRepository;

    public function __construct(PurchaseOrderRepositoryInterface $purchaseRepository)
    {
        $this->purchaseRepository = $purchaseRepository;
    }

    public function fetchPurchaseOrder(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $data = $this->purchaseRepository->fetchAll($input);

        return response()->json($data);
    }

    public function fetchPurchaseOrderSummary(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $data = $this->purchaseRepository->getPurchaseOrderSummary($input);

        return response()->json($data);
    }

    public function getPurchaseOrder($id)
    {
        $data = $this->purchaseRepository->fetch($id);

        return response()->json($data);
    }

    public function storePurchaseOrder(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        $validator = Validator::make($input, [
            'po_number' => 'required_without:invoice_number',
            'invoice_number' => 'required_without:po_number',
            'order_date' => 'required',
            'total' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        if (!empty($input['po_number']) && !empty($input['invoice_number'])) {
            $validator = Validator::make($input, [
                'po_number' => ['required',
                    Rule::unique('purchase_order')->where(function ($query) use ($input) {
                        return $query->where('po_number', $input['po_number'])
                            ->where('invoice_number', $input['invoice_number']);
                    })]
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
        }

        $input['tag'] = !empty($input['tag']) && is_array($input['tag']) && count($input['tag']) > 0 ? implode(',', $input['tag']) : '';

        return $this->purchaseRepository->create($input);
    }

    public function updatePurchaseOrder(Request $request, $id)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        $validator = Validator::make($input, [
            'po_number' => 'required_without:invoice_number',
            'invoice_number' => 'required_without:po_number',
            'order_status' => new UpdatePurchaseOrderRule($id),
            'order_date' => 'required',
            'total' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:product,id',
            'items.*.quantity' => 'required|numeric|min:0',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        if (!empty($input['po_number']) && !empty($input['invoice_number'])) {
            $validator = Validator::make($input, [
                'po_number' => ['required',
                    Rule::unique('purchase_order')->where(function ($query) use ($input) {
                        return $query->where('po_number', $input['po_number'])
                            ->where('invoice_number', $input['invoice_number']);
                    })->ignore($id)]
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
        }
        $items = $input['items'] ?? [];
        $errors = [];

        foreach ($items as $index => $item) {
            $existingItem = PurchaseOrderItem::where('po_id', $id)
                ->where('product_id', $item['product_id'])
                ->first();
            if ($existingItem && $item['quantity'] < $existingItem->quantity_onhand) {
                $errors["items.$index.quantity"] = "Quantity for product ID {$item['product_id']} cannot be greater than quantity on hand ({$existingItem->quantity_onhand}).";
            }
        }
        if (!empty($errors)) {
            return response()->json(['errors' => $errors], 422);
        }
        $input['tag'] = !empty($input['tag']) && is_array($input['tag']) && count($input['tag']) > 0 ? implode(',', $input['tag']) : '';

        return  $this->purchaseRepository->update($id, $input);
    }

    public function cancelPurchaseOrder($id)
    {
        return $this->purchaseRepository->cancelOrder($id);
    }

    public function commentPurchaseOrder(Request $request)
    {
        $input = $request->all();
        $input['user_id'] = auth()->user()->id;
        $validator = Validator::make($input, [
            'po_id' => 'required',
            'message' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $comment = $this->purchaseRepository->comment($input);

        return response()->json($comment);
    }

    public function getPurchaseOrderHistory(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'po_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = $this->purchaseRepository->history($input);

        return response()->json($data);
    }

    public function fetchPurchaseOrderByPoNumber(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'po_number' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $vendorId = !empty($input['vendor_id']) ? $input['vendor_id'] : null;
        $order = $this->purchaseRepository->fetchOrderByPoNumber($input['po_number'], $vendorId);

        return response()->json($order);
    }

    public function fetchPurchaseOrderByVendor(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $order = $this->purchaseRepository->fetchOrderByVendor($input['vendor_id']);

        return response()->json($order);
    }

    public function fetchPurchaseOrderByInvoice(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'invoice_number' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $order = $this->purchaseRepository->fetchOrderByInvoiceNumber($input['invoice_number']);

        return response()->json($order);
    }

    public function getPurchaseOrderBoxDetail(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'po_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $order = $this->purchaseRepository->fetchPurchaseOrderBoxDetail($input['po_id']);

        return response()->json($order);
    }

    public function storePurchaseOrderBox(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        //$rule['invoice_number'] ="required";
        $rule['po_id'] = 'required';
        if (!empty($input['tracking_number'])) {
            $rule['tracking_number'] = 'unique:purchase_order_box,tracking_number';
        }
        $validator = Validator::make($input, $rule);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->purchaseRepository->createOrderBox($input);
    }

    public function updatePurchaseOrderBox(Request $request, $id)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        // $rule['invoice_number'] ="required";
        $rule['po_id'] = 'required';
        if (!empty($input['tracking_number'])) {
            $rule['tracking_number'] = 'unique:purchase_order_box,tracking_number,' . $id;
        }
        if (!empty($input['items'])) {
            $rule['items.*.product_id'] = 'required';
            $rule['items.*.quantity'] = 'required';
            $rule['items.*.sku'] = 'required';
        }

        $messages = [
            'items.*.product_id.required' => 'The product_id in items is invalid. Please check again.',
            'items.*.quantity.required' => 'The quantity in items is invalid. Please check again.',
            'items.*.sku.required' => 'The sku in items is invalid. Please check again.',
        ];

        $validator = Validator::make($input, $rule, $messages);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->purchaseRepository->updateOrderBox($id, $input);
    }

    public function removeProductPurchaseOrderBox($id)
    {
        return $this->purchaseRepository->removeProductPurchaseOrderBox($id);
    }

    public function removeProductPurchaseOrderBoxItem($id)
    {
        return $this->purchaseRepository->removeProductPurchaseOrderBoxItem($id);
    }

    public function fetchProductPurchaseOrder($id)
    {
        $data = $this->purchaseRepository->fetchProductFromOrder($id);

        return response()->json($data);
    }

    public function fetchTrackingNumberByOrder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'po_number' => 'required_without:invoice_number',
            'invoice_number' => 'required_without:po_number',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = $this->purchaseRepository->fetchTrackingNumberByOrder($input);

        return response()->json($data);
    }

    public function updatePurchaseOrderManual(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'po_id' => 'required|exists:purchase_order,id',
            'order_status' => ['required', new UpdatePurchaseOrderRule($input['po_id'])],
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $purchaseOrder = PurchaseOrder::where('id', $input['po_id'])
            ->first();
        if (
            $purchaseOrder->order_status == PurchaseOrder::COMPLETED_STATUS
            || $purchaseOrder->order_status == PurchaseOrder::CANCELLED_STATUS
            || $purchaseOrder->order_status == PurchaseOrder::CLOSED_STATUS
        ) {
            return response()->json([
                'PurchaseOrder' => 'The purchase order has been completed or canceled and cannot be updated!'
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        $dataInsertPurchaseOrderHistory = [
            'user_id' => auth()->user()['id'],
            'po_id' => $input['po_id'],
            'type' => PurchaseOrderHistory::TYPE_UPDATE,
            'message' => 'order status: ' . $purchaseOrder->order_status . ' change to ' . $input['order_status'],
        ];

        DB::beginTransaction();

        $dataUpdate = [
            'order_status' => $input['order_status'],
        ];

        if ($input['order_status'] == PurchaseOrder::CLOSED_STATUS) {
            /** Update purchase order item */
            $purchaseOrder->items()->update([
                'total' => DB::raw('COALESCE(quantity_onhand, 0) * COALESCE(price, 0)')
            ]);

            /** Calculate purchase order total */
            $dataUpdate['total'] = $this->purchaseRepository->calculateTotalByClosedStatus($purchaseOrder);
        }

        $purchaseOrder->update($dataUpdate);
        PurchaseOrderHistory::create($dataInsertPurchaseOrderHistory);
        if (
            $input['order_status'] == PurchaseOrder::COMPLETED_STATUS
            || $input['order_status'] == PurchaseOrder::CANCELLED_STATUS
            || $input['order_status'] == PurchaseOrder::CLOSED_STATUS
        ) {
            ProductQuantityRepository::updateIncomingByPurchaseOrder($purchaseOrder->id);
        }

        DB::commit();

        return response()->json(['success' => ['update success']]);
    }

    public function exportExcel(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return Excel::download(new PurchaseOrderExport($input), 'purchase_orders.xlsx');
    }

    public function getPurchaseOrderHasProduct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->purchaseRepository->fetchOrdersHasProduct($request);
    }

    public function getProductInPurchaseOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gtin' => 'required',
            'po_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->purchaseRepository->getProductInPurchaseOrderByGtin($request->po_id, $request->gtin);
    }
}
