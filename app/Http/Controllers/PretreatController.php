<?php

namespace App\Http\Controllers;

use App\Http\Requests\AllInOneMultipleItemsRequest;
use App\Http\Requests\AllInOneMultipleItemsSettingsRequest;
use App\Http\Service\NeckService;
use App\Models\Dl2400PrinterSetting;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemBarcode;
use App\Repositories\Dl2400PrinterRepository;
use App\Repositories\PretreatRepository;
use App\Repositories\PrintingRepository;
use App\Repositories\SettingRepository;
use App\Repositories\TimeCheckingRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PretreatController extends Controller
{
    private PretreatRepository $pretreatRepository;

    private SettingRepository $settingRepository;

    private PrintingRepository $printingRepository;

    private NeckService $neckService;

    private Dl2400PrinterRepository $dl2400PrinterRepository;

    public function __construct(
        PretreatRepository $pretreatRepository,
        PrintingRepository $printingRepository,
        NeckService $neckService,
        SettingRepository $settingRepository,
        Dl2400PrinterRepository $dl2400PrinterRepository
    ) {
        $this->pretreatRepository = $pretreatRepository;
        $this->printingRepository = $printingRepository;
        $this->neckService = $neckService;
        $this->settingRepository = $settingRepository;
        $this->dl2400PrinterRepository = $dl2400PrinterRepository;
    }

    public function allInOne(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $label = $this->printingRepository->removeLabelSide($input['label']);
        $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);

        if (!$itemBarcode) {
            return response()->json(['message' => 'Label not found'], 422);
        }

        $side = $this->printingRepository->getLabelPrintSide($input['label']);
        $side = max($side, 0);
        $data = $this->pretreatRepository->getDataAllInOne($itemBarcode, $side, $input['employee_id']);

        if ($data['status']) {
            return response()->json(['data' => $data['data']]);
        }

        return response()->json(['message' => $data['message']], 422);
    }

    public function getSettings(Request $request): JsonResponse
    {
        $name = $request->get('name');
        $defaultSetting = [
            'layout' => PretreatRepository::SETTING_MARGIN_LAYOUT_FOUR_ITEM,
            'testPrint' => false,
        ];
        $setting = Dl2400PrinterSetting::query()->where('name', $name)->first();

        if (empty($name) || empty($setting->four_item_layout_setting)) {
            return response()->json([
                'name' => '',
                'data' => $defaultSetting,
            ]);
        }

        return response()->json([
            'name' => $name,
            'data' => json_decode($setting->four_item_layout_setting),
        ]);
    }

    public function storeSettings(AllInOneMultipleItemsSettingsRequest $request): JsonResponse
    {
        $setting = json_encode($request->input('settings'));

        Dl2400PrinterSetting::query()->updateOrCreate(['name' => $request->input('name')], [
            'four_item_layout_setting' => $setting
        ]);

        return response()->json([
            'name' => $request->input('name'),
            'data' => $request->input('settings'),
        ]);
    }

    public function allInOneTwoItems(AllInOneMultipleItemsRequest $request): JsonResponse
    {
        $input = $request->validated();
        $inputLabel = [];
        $inputStyleColor = [];

        if (count($input['label']) > 2) {
            return response()->json(['message' => 'The label must not have more than 2 items.'], 422);
        }

        foreach ($input['label'] as $index => $label) {
            if (empty($label)) {
                continue;
            }

            $labelOrigin = $this->printingRepository->removeLabelSide($label);
            $printSide = $this->printingRepository->getLabelPrintSide($label);
            $printSide = max($printSide, 0);

            if (!in_array($printSide, [3, 4])) {
                return response()->json(['message' => "The print side for label $label is invalid."], 422);
            }

            $labelInfo = SaleOrderItemBarcode::query()
                ->selectRaw('
                    sale_order_item.product_style_sku,
                    COALESCE(product_color.icc_color, "default") AS color
                ')
                ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('product', 'product.id', '=', 'sale_order_item.product_id')
                ->leftJoin('product_color', 'product_color.sku', '=', 'sale_order_item.product_color_sku')
                ->where('sale_order_item_barcode.label_id', $labelOrigin)
                ->where('sale_order_item_image.print_side', $printSide)
                ->first();

            if (empty($labelInfo)) {
                return response()->json(['message' => "The label $label not found."], 422);
            }

            $inputStyleColor[$labelInfo->product_style_sku . $labelInfo->color] = true;
            $inputLabel[] = [
                'index' => $index,
                'label' => $labelOrigin,
                'side' => $printSide,
            ];
        }

        $input['labels'] = $inputLabel;

        if (empty($input['labels'])) {
            return response()->json(['message' => 'At least one label must have a value.'], 422);
        }

        if (count($inputStyleColor) > 1) {
            return response()->json(['message' => 'All labels are required to have the same style SKU and color SKU.'], 422);
        }

        $data = $this->pretreatRepository->generateGroupPhotoTwoItems($input);

        if (empty($data['status'])) {
            return response()->json($data, 422);
        }

        return response()->json($data);
    }

    public function allInOneFourItems(AllInOneMultipleItemsRequest $request): JsonResponse
    {
        $input = $request->validated();
        $inputLabel = [];
        $inputStyleColor = [];

        if (count($input['label']) > 4) {
            return response()->json(['message' => 'The label must not have more than 4 items.'], 422);
        }

        foreach ($input['label'] as $index => $label) {
            if (empty($label)) {
                continue;
            }

            $labelOrigin = $this->printingRepository->removeLabelSide($label);
            $printSide = $this->printingRepository->getLabelPrintSide($label);
            $printSide = max($printSide, 0);

            if (!in_array($printSide, [3, 4, 5, 6])) {
                return response()->json(['message' => "The print side for label $label is invalid."], 422);
            }

            if (in_array($printSide, [5, 6])) {
                $generateFile = $this->neckService->generateFileNeck($label, $input['country_of_origin'] ?? '');

                if (empty($generateFile['status'])) {
                    return response()->json($generateFile, 422);
                }
            }

            $labelInfo = SaleOrderItemBarcode::query()
                ->selectRaw('
                    sale_order_item.product_style_sku,
                    COALESCE(product_color.icc_color, "default") AS color
                ')
                ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('product', 'product.id', '=', 'sale_order_item.product_id')
                ->leftJoin('product_color', 'product_color.sku', '=', 'sale_order_item.product_color_sku')
                ->where('sale_order_item_barcode.label_id', $labelOrigin)
                ->where('sale_order_item_image.print_side', $printSide)
                ->first();

            if (empty($labelInfo)) {
                return response()->json(['message' => "The label $label not found."], 422);
            }

            $inputStyleColor[$labelInfo->product_style_sku . $labelInfo->color] = true;
            $inputLabel[] = [
                'index' => $index,
                'label' => $labelOrigin,
                'side' => $printSide,
            ];
        }

        $input['labels'] = $inputLabel;

        if (empty($input['labels'])) {
            return response()->json(['message' => 'At least one label must have a value.'], 422);
        }

        if (count($inputStyleColor) > 1) {
            return response()->json(['message' => 'All labels are required to have the same style SKU and color SKU.'], 422);
        }

        $data = $this->pretreatRepository->generateGroupPhotoFourItems($input);

        if (empty($data['status'])) {
            return response()->json($data, 422);
        }

        return response()->json($data);
    }

    public function getPretreat(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
            'id_time_checking' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $label = $this->printingRepository->removeLabelSide($input['label']);
        $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);

        if (!$itemBarcode) {
            return response()->json(['message' => 'Label not found'], 422);
        }

        $side = $this->printingRepository->getLabelPrintSide($input['label']);
        $side = max($side, 0);
        $data = $this->pretreatRepository->getDataOnlyPretreat($itemBarcode, $side, $input['employee_id'], $input['version'] ?? 1);

        if ($data['status']) {
            return response()->json(['data' => $data['data']], 200);
        } else {
            return response()->json(['message' => $data['message']], 422);
        }
    }

    public function updatePretreat(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
            'id_time_checking' => 'required',
            'fluid_grams' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        DB::beginTransaction();

        try {
            $label = $this->printingRepository->removeLabelSide($input['label']);
            $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);

            if (!$itemBarcode) {
                DB::rollBack();

                return response()->json(['message' => 'Label not found'], 422);
            }

            $side = $this->printingRepository->getLabelPrintSide($input['label']);
            $side = max($side, 0);

            $this->pretreatRepository->updateFluidGrams($itemBarcode, $side, $input['fluid_grams']);
            $this->pretreatRepository->updatePretreat($input['employee_id'], $itemBarcode->id);

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

            $sideName = ProductPrintSide::findByCode($side)?->name;
            saleOrderHistory(
                null,
                $input['employee_id'],
                $itemBarcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_PRETREAT_TYPE,
                "Label ID $label pretreated $sideName",
            );

            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $itemBarcode->order_id);

            DB::commit();

            return response()->json(['message' => 'update pretreat success'], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json(['message' => $th->getMessage()], 422);
        }
    }
}
