<?php

namespace App\Http\Controllers;

use App\Http\Requests\CostReport\CreateLaborRequest;
use App\Http\Requests\CostReport\CreateRequest;
use App\Http\Requests\CostReport\DetailLaborRequest;
use App\Http\Requests\CostReport\FetchHistoryRequest;
use App\Http\Requests\CostReport\FetchRequest;
use App\Http\Requests\CostReport\GetDetailRequest;
use App\Models\CostReportLabor;
use App\Models\ProductType;
use App\Repositories\CostReportRepository;
use Carbon\Carbon;

class CostReportController extends Controller
{
    private $costReportRepository;

    public function __construct(CostReportRepository $costReportRepository)
    {
        $this->costReportRepository = $costReportRepository;
    }

    public function fetchAll(FetchRequest $request)
    {
        $parseRequest = $request->validated();
        $params['warehouse_id'] = config('jwt.warehouse_id');
        $params['year'] = $parseRequest['year'] ?? Carbon::now()->format('Y');
        $result = $this->costReportRepository->fetchCostReport($params);
        return response()->json($result);
    }

    public function createOrUpdate(CreateRequest $request)
    {
        $data = $request->validated();
        $data['warehouse_id'] = config('jwt.warehouse_id');
        $data['affected_year_at'] = Carbon::parse($data['affected_at'])->format('Y');
        $data['affected_month_at'] = Carbon::parse($data['affected_at'])->format('m');
        $data['product_type_id'] = ProductType::where('name', $data['product_type'])->first()->id;
        unset($data['product_type'], $data['affected_at']);
        $result = $this->costReportRepository->create($data);
        if (isset($result['status']) && !$result['status']) {
            return response()->json($result, 500);
        }
        return $this->responseSuccess($result, 'Add Cost Report success');
    }

    public function fetchAllHistory(FetchHistoryRequest $request)
    {
        $paramsParse = $request->validated();
        $data['warehouse_id'] = config('jwt.warehouse_id');
        $data['affected_year_at'] = empty($paramsParse['affected_at']) ? null : Carbon::parse($paramsParse['affected_at'])->format('Y');
        $data['affected_month_at'] = empty($paramsParse['affected_at']) ? null : Carbon::parse($paramsParse['affected_at'])->format('m');
        $data['cost_type'] = $paramsParse['cost_type'] ?? null;
        $data['employee_id'] = $paramsParse['employee_id'] ?? null;
        $data['limit'] = $paramsParse['limit'] ?? 25;
        $data['page'] = $paramsParse['page'] ?? 1;
        $result = $this->costReportRepository->fetchCostReportHistory($data);
        return response()->json($result);
    }

    public function getDetailCostReport(GetDetailRequest $request)
    {
        $params = $request->validated();
        $params['warehouse_id'] = config('jwt.warehouse_id');
        $params['affected_year_at'] = empty($params['affected_at']) ? null : Carbon::parse($params['affected_at'])->format('Y');
        $params['affected_month_at'] = empty($params['affected_at']) ? null : Carbon::parse($params['affected_at'])->format('m');
        $result = $this->costReportRepository->getDetailCostReport($params);
        return response()->json($result);
    }

    public function createOrUpdateLabor(CreateLaborRequest $request)
    {
        $body = $request->validated();
        $costLabel = CostReportLabor::updateOrCreate([
            'warehouse_id' => config('jwt.warehouse_id'),
            'pay_period_begin' => $body['pay_period_begin'],
            'pay_period_end' => $body['pay_period_end'],
        ], [
            'cost' => $body['cost'],
            'employee_id' => $body['employee_id'],
        ]);
        return response()->json($costLabel);
    }

    public function detailCostLabel(DetailLaborRequest $request)
    {
        $limit = $request->get('limit') ?? 25;
        $query = CostReportLabor::with('employee')->where('warehouse_id', config('jwt.warehouse_id'));
        if ($request->employee_id) {
            $query->where('employee_id', $request->employee_id);
        }
        if ($request->pay_period_begin) {
            $query->where('pay_period_begin', $request->pay_period_begin)->where('pay_period_end', $request->pay_period_end);
        }
        if ($request->year) {
            $query->whereYear('pay_period_begin', '=', $request->year);
        }
        $data = $query->paginate($limit);
        return response()->json($data);
    }
}
