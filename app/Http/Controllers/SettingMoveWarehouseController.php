<?php

namespace App\Http\Controllers;

use App\Models\SettingMoveWarehouseModel;
use App\Repositories\WarehouseRerouteRepository;
use Illuminate\Http\Request;
use Validator;

class SettingMoveWarehouseController extends Controller
{
    public function __construct(WarehouseRerouteRepository $warehouseRerouteRepository)
    {
        $this->warehouseRerouteRepository = $warehouseRerouteRepository;
    }

    public function store()
    {
        $rawData = request()->input();
        $validator = Validator::make($rawData, [
            'warehouse_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->warehouseRerouteRepository->create($rawData);
    }

    public function updatePriority(Request $request)
    {
        return $this->warehouseRerouteRepository->updatePriority($request->all());
    }

    public function list()
    {
        $settingMoveWarehouseModel = new SettingMoveWarehouseModel();
        $listEvent = $settingMoveWarehouseModel->list();
        foreach ($listEvent as &$item) {
            if ($item->rule) {
                $item->rule = json_decode($item->rule);
            }
        }

        return response()->json($listEvent);
    }

    public function getItemReroute(Request $request)
    {
        return $this->warehouseRerouteRepository->getItemReroute($request->all());
    }

    public function getItemRerouteFail($id)
    {
        return $this->warehouseRerouteRepository->getItemRerouteFail($id);
    }
}
