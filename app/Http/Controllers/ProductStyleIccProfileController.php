<?php

namespace App\Http\Controllers;

use App\Repositories\ProductStyleIccProfileRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductStyleIccProfileController extends Controller
{
    protected $repository;

    public function __construct(ProductStyleIccProfileRepository $repository)
    {
        $this->repository = $repository;
    }

    public function storeIccProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_style' => 'required|exists:product_style,name|unique:product_style_icc_profiles,product_style',
            'black_url' => 'nullable|url',
            'white_url' => 'nullable|url',
            'colored_url' => 'nullable|url',
            'black_name' => 'nullable|string',
            'white_name' => 'nullable|string',
            'colored_name' => 'nullable|string',
        ], [
            'product_style.unique' => 'This style already has ICC profiles',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->repository->storeProfile($request);
    }

    public function getIccProfile(Request $request)
    {
        return $this->repository->getList($request->all());
    }

    public function generateSignedUrl(Request $request)
    {
        $request->validate([
            'file_name' => 'required|string',
        ]);

        return $this->repository->generateSignedUrl($request);
    }
}
