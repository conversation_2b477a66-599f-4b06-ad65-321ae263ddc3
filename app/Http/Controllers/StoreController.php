<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSlaExpirationRequest;
use App\Models\Client;
use App\Models\Store;
use App\Models\User;
use App\Models\WalletBillingAddress;
use App\Repositories\StoreAddressRepository;
use App\Repositories\StoreRepository;
use App\Repositories\WalletBillingAddressRepository;
use App\Repositories\WalletRepository;
use App\Rules\CheckValidEmails;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class StoreController extends Controller
{
    private StoreRepository $storeRepository;

    public function __construct(StoreRepository $storeRepository)
    {
        $this->storeRepository = $storeRepository;
    }

    public function getList(Request $request): JsonResponse
    {
        $input = $request;
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $input['store_ids'] = [];
            } else {
                $input['store_ids'] = $storeIds;
            }
        }
        $data = $this->storeRepository->getList($input);

        return response()->json($data);
    }

    public function getAll(Request $request): JsonResponse
    {
        $data = $this->storeRepository->getAll($request->all());

        return response()->json($data);
    }

    public function getStore($id)
    {
        return StoreRepository::getDataStoreById($id);
    }

    public function storeInfo(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required|unique:store',
            'code' => 'nullable|max:10|unique:store',
            'client_id' => 'required',
            'sale_rep' => 'required|exists:user,id',
            'payment_terms' => 'required',
            'billing_email' => ['nullable', 'required_if:payment_terms,0', new CheckValidEmails()],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $saleRep = User::find($input['sale_rep']);
        if (!$saleRep->hasRole(['Sales Team'])) {
            return response()->json([
                'message' => "User sale rep must have the 'Sales Team' role.",
            ], 422);
        }

        DB::beginTransaction();
        try {
            $storeId = StoreRepository::storeInfo($input);
            if (!empty($input['billing_address'])) {
                StoreAddressRepository::store($input['billing_address'], $storeId, StoreAddressRepository::TYPE_ADDRESS_BILLING);
                $walletAdressData = [
                    'store_id' => $storeId,
                    'name' => $input['billing_address']['name'] ?? null,
                    'email' => $input['billing_address']['email'] ?? null,
                    'company' => $input['billing_address']['company'] ?? null,
                    'phone' => $input['billing_address']['phone'] ?? null,
                    'street1' => $input['billing_address']['street1'] ?? null,
                    'street2' => $input['billing_address']['street2'] ?? null,
                    'city' => $input['billing_address']['city'] ?? null,
                    'state' => $input['billing_address']['state'] ?? null,
                    'zip' => $input['billing_address']['zip'] ?? null,
                    'country' => $input['billing_address']['country'] ?? null,
                ];
            } else {
                $walletAdressData = [
                    'store_id' => $storeId,
                    'name' => $input['name'] ?? null,
                    'email' => empty($input['billing_email']) ? $input['contact_email'] : $input['billing_email'] ?? null,
                    'company' => $input['company'] ?? null,
                    'phone' => $input['phone'] ?? null,
                    'street1' => $input['street1'] ?? null,
                    'street2' => $input['street2'] ?? null,
                    'city' => $input['city'] ?? null,
                    'state' => $input['state'] ?? null,
                    'zip' => $input['zip'] ?? null,
                    'country' => $input['country'] ?? null,
                ];
            }
            $walletAdress = WalletBillingAddressRepository::storeBillingAddress($walletAdressData);
            Store::where('id', $storeId)->update(['billing_address_id' => $walletAdress->id]);

            if (isset($input['payment_terms']) && $input['payment_terms'] != 1) {
                $dataWallet = [
                    'store_id' => $storeId,
                    'balance' => 0,
                ];
                WalletRepository::store($dataWallet);
            }
            $store = $this->storeRepository->getStoreById($storeId);
            if (!$store) {
                return response()->json(['data' => 'Store not found'], 422);
            }
            $token = StoreRepository::generateApiKey($storeId);
            $this->storeRepository->saveLogGenerateApiKey($store, $token);
            DB::commit();

            return response()->json(['data' => $storeId], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function editStoreInfo(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required',
            'id' => 'required|exists:store,id',
            'code' => 'nullable|max:10|unique:store,code,' . ($input['id'] ?? null),
            'client_id' => 'required',
            'sale_rep' => 'required|exists:user,id',
            'billing_email' => ['nullable', 'required_if:payment_terms,0', new CheckValidEmails()],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $saleRep = User::find($input['sale_rep']);
        if (!$saleRep->hasRole(['Sales Team'])) {
            return response()->json([
                'message' => "User sale rep must have the 'Sales Team' role.",
            ], 422);
        }

        return $this->storeRepository->editStore($input);
    }

    public function storeInfoShipping(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => ['required', 'exists:store,id'],
            'is_auto_create_shipping' => ['required', Rule::in(array_keys(Store::MODE_AUTO_CREATE_SHIPPING))],
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($input['id'] == Store::STORE_GELATO) {
            return response()->json(['message' => ['Store does not apply auto shipping label creation']], 422);
        }

        return $this->storeRepository->editStoreShipping($input);
    }

    public function storeShipAddress(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required',
            'id' => 'required|exists:store,id'
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = StoreAddressRepository::store($input, $input['id'], StoreAddressRepository::TYPE_ADDRESS_FROM);

        return response()->json(['data' => $data], 200);
    }

    public function storeReturnAddress(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required',
            'id' => 'required|exists:store,id'
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = StoreAddressRepository::store($input, $input['id'], StoreAddressRepository::TYPE_ADDRESS_RETURN);

        $dataUpdate = [
            'non_delivery_option' => !empty($input['deliveryOption']) ? StoreRepository::DELIVERY_OPTION_ABANDON : StoreRepository::DELIVERY_OPTION_RETURN
        ];

        StoreRepository::updateStoreById($dataUpdate, $input['id']);

        return response()->json(['data' => $data], 200);
    }

    public function storeInfoAccount(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'id' => 'required|exists:store,id',
            'username' => 'sometimes|nullable|unique:store,username,' . $input['id'] . ',id',
            'email' => 'sometimes|nullable|email|unique:store,email,' . $input['id'] . ',id',
            'order_desk_api_key' => 'nullable|max:255',
            'order_desk_store_id' => 'nullable|numeric',
            'api_manual' => 'boolean',
            'api_sample' => 'boolean',
            'api_merchant_name' => 'boolean',
            'is_calculate_shipping' => 'boolean',
            'is_calculate_price' => 'boolean',
            'days_until_bill_due' => 'numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $idStore = $input['id'];
        if (!empty($input['password'])) {
            $input['password'] = bcrypt($input['password']);
        }
        if (empty($input['order_desk_api_key'])) {
            unset($input['order_desk_api_key']);
        }
        if (empty($input['order_desk_store_id'])) {
            unset($input['order_desk_store_id']);
        }
        unset($input['id']);
        DB::beginTransaction();
        try {
            $walletBillingAddress = WalletBillingAddress::where([
                'store_id' => $idStore,
            ])->orderBy('id', 'desc')->first();
            $store = Store::with('storeAddressBilling')->where('id', $idStore)->first();
            $billingName = $store->storeAddressBilling?->name ?? $store?->name;
            $billingEmail = $store->storeAddressBilling?->email ?? $store?->email;
            $billingPhone = $store->storeAddressBilling?->phone ?? $store?->phone;
            $billingStreet1 = $store->storeAddressBilling?->street1 ?? $store?->street1;
            $billingStreet2 = $store->storeAddressBilling?->street2 ?? $store?->street2;
            $billingCity = $store->storeAddressBilling?->city ?? $store?->city;
            $billingState = $store->storeAddressBilling?->state ?? $store?->state;
            $billingZip = $store->storeAddressBilling?->zip ?? $store?->zip;
            $billingCountry = $store->storeAddressBilling?->country ?? $store?->country;
            $billingCompany = $store->storeAddressBilling?->company ?? $store?->company;

            if (!$walletBillingAddress || $walletBillingAddress?->billing_contact != ($input['billing_contact'] ?? null)) {
                $walletBillingAddress = WalletBillingAddress::create([
                    'store_id' => $idStore,
                    'name' => $billingName,
                    'email' => $billingEmail,
                    'company' => $billingCompany,
                    'phone' => $billingPhone,
                    'street1' => $billingStreet1,
                    'street2' => $billingStreet2,
                    'state' => $billingState,
                    'city' => $billingCity,
                    'zip' => $billingZip,
                    'country' => $billingCountry,
                    'billing_contact' => $input['billing_contact'] ?? null,
                ]);
                $input['billing_address_id'] = $walletBillingAddress->id;
            }

            $this->storeRepository->saveLog($idStore, $input);
            StoreRepository::updateStoreById($input, $idStore);

            DB::commit();

            return response()->json(['data' => 'Save info success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['data' => $e->getMessage()], 500);
        }
    }

    public function generateApiKey($id)
    {
        DB::beginTransaction();
        try {
            $store = StoreRepository::checkStoreById($id);
            if (!$store) {
                return response()->json(['data' => 'Store not found'], 422);
            }

            $token = StoreRepository::generateApiKey($id);
            $this->storeRepository->saveLogGenerateApiKey($store, $token);
            DB::commit();

            return response()->json(['data' => $token], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['data' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getApiKey()
    {
        return $this->storeRepository->getApiKey();
    }

    public function getStoreUser(Request $request)
    {
        if ($request->client_id) {
            return $this->storeRepository->fetchStoreByClientId($request);
        }

        return $this->storeRepository->getStoreUser(auth()->user()->user_login_id);
    }

    public function selectStore(int $id, Request $request)
    {
        return $this->storeRepository->selectStore($id, $request->user_id);
    }

    public function autoGenerateAPIKey()
    {
        try {
            $this->storeRepository->autoGenerateAPIKey();
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getPriorityStores()
    {
        return $this->storeRepository->getPriorityStores();
    }

    public function getClients()
    {
        return Client::all();
    }

    public function changeStore(Request $request)
    {
        return $this->storeRepository->changeStore($request);
    }

    public function storeSlaExpiration(StoreSlaExpirationRequest $request)
    {
        return $this->storeRepository->editStoreSlaExpiration($request->validated());
    }
}
