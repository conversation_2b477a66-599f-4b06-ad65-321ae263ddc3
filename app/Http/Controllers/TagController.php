<?php

namespace App\Http\Controllers;

use App\Repositories\Contracts\TagRepositoryInterface;
use Illuminate\Http\Request;
use Validator;

class TagController extends Controller
{
    protected $tagRepository;

    public function __construct(TagRepositoryInterface $tagRepository)
    {
        $this->tagRepository = $tagRepository;
    }

    public function fetchAll(Request $request)
    {
        $input = $request->all();
        $items = $this->tagRepository->fetchAll($input);

        return response()->json($items);
    }

    public function store(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->tagRepository->create($input);

        return response()->json($res, 201);
    }

    public function update(Request $request, $id)
    {
        $input = $request->all();
        $warehouse_id = $request->warehouse_id;
        $validator = Validator::make($input, [
            'name' => 'required|unique:tag,name,' . $id,
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->tagRepository->update($id, $input);

        return response()->json($res, 200);
    }

    public function delete($id)
    {
        $data = $this->tagRepository->delete($id);

        return response()->json($data);
    }

    public function tagRestrictRemove()
    {
        $tags = $this->tagRepository->tagRestrictRemove();

        return response()->json($tags, 200);
    }
}
