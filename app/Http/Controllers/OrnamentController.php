<?php

namespace App\Http\Controllers;

use App\Http\Requests\Ornament\GenerateBarcodeRequest;
use App\Http\Requests\Ornament\GeneratePdfRequest;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Repositories\OrnamentRepository;
use App\Repositories\StoreRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Validator;

class OrnamentController extends Controller
{
    private OrnamentRepository $ornamentRepository;

    private StoreRepository $storeRepository;

    public function __construct(OrnamentRepository $ornamentRepository, StoreRepository $storeRepository)
    {
        $this->ornamentRepository = $ornamentRepository;
        $this->storeRepository = $storeRepository;
    }

    public function countPendingPriorityStore(Request $request)
    {
        $input = $request->all();
        $input['inPriorityStores'] = $this->storeRepository->getPriorityStores();

        try {
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->countPendingPriorityStore($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function count(Request $request)
    {
        try {
            $input = $request->all();
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->count($input);
            $data['tiktok'] = $this->ornamentRepository->countPendingTiktok($input);
            $data['bulk'] = $this->ornamentRepository->countPendingBulkOrder($input);
            $data['reprint'] = $this->ornamentRepository->countPendingReprintOrder($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'store_id' => 'nullable|numeric|exists:store,id',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->all();
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->index($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function generateBarcode(GenerateBarcodeRequest $request)
    {
        try {
            $input = $request->all();
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $data = $this->ornamentRepository->generateBarcode($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function history(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string',
                'store_id' => 'nullable|numeric|exists:store,id',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->all();
            $input['warehouse_id'] = $request->warehouse_id;
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $data = $this->ornamentRepository->history($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function countPdf(Request $request)
    {
        try {
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->countPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function scanLabel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'label_id' => 'required|string',
                'employee_id' => [
                    'required',
                    'numeric',
                    Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
                ],
                'product_id' => 'required|numeric',
            ], [
                'employee_id.exists' => 'You are not in this warehouse!'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['label_id', 'employee_id', 'product_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->scanLabel($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function generatePdf(GeneratePdfRequest $request)
    {
        try {
            $input = $request->only(['options', 'employee_id', 'product_id']);
            $data = $this->ornamentRepository->generatePdf($input, ProductStyle::METHOD_UV, ProductType::ORNAMENT);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function listPdf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->listPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function historyPdf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->ornamentRepository->historyPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function preset(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['product_id']);
            $data = $this->ornamentRepository->preset($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function download(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'pdf_converted_id' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['pdf_converted_id']);
            $data = $this->ornamentRepository->download($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
