<?php

namespace App\Http\Controllers;

use App\Exports\LicensedDesignErrorExport;
use App\Http\Requests\ImportLicensedDesignRequest;
use App\Http\Requests\SubmitLicensedDesignRequest;
use App\Http\Requests\UpdateLicensedDesignRequest;
use App\Repositories\LicensedDesignRepository;
use Excel;
use Illuminate\Http\Request;

class LicensedDesignController extends Controller
{
    protected LicensedDesignRepository $licensedDesignRepository;

    public function __construct(LicensedDesignRepository $licensedDesignRepository)
    {
        $this->licensedDesignRepository = $licensedDesignRepository;
    }

    public function getLicensedDesigns(Request $request)
    {
        $data = $this->licensedDesignRepository->getLicensedDesigns($request);

        return response()->json($data);
    }

    public function import(ImportLicensedDesignRequest $request)
    {
        $data = $this->licensedDesignRepository->validateImportLicensedDesign($request->import_licenses_designs);

        return response()->json($data);
    }

    public function downloadError(Request $request)
    {
        $data = $request['data'];

        return Excel::download(new LicensedDesignErrorExport($data), 'licensed_design_error.xlsx');
    }

    public function storeDesigns(SubmitLicensedDesignRequest $request)
    {
        $success = $this->licensedDesignRepository->createLicensedDesign($request->validated());

        if ($success) {
            return response()->json(['message' => 'Create licensed designs successfully'], 200);
        }

        return response()->json(['message' => 'Create licensed designs failed'], 400);
    }

    public function updateDesigns($id, UpdateLicensedDesignRequest $request)
    {
        $design = $this->licensedDesignRepository->updateLicensedDesign($id, $request->validated());

        if ($design) {
            return response()->json(['message' => 'Update licensed design successfully'], 200);
        }

        return response()->json(['message' => 'Update licensed design failed'], 400);
    }
}
