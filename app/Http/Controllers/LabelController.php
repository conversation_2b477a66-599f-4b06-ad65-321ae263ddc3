<?php

namespace App\Http\Controllers;

use App\Http\Requests\LabelShipping\CreateLabelFbaRequest;
use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Repositories\LabelRepository;
use App\Repositories\TimeCheckingRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Validator;

class LabelController extends Controller
{
    protected $labelRepository;

    public function __construct(LabelRepository $labelRepository)
    {
        $this->labelRepository = $labelRepository;
    }

    public function getDataByLabel(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'label_id' => 'required',
            'time_tracking_id' => 'nullable|exists:time_tracking,id',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        if (!empty($input['time_tracking_id'])) {
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => now()], $input['time_tracking_id']);
        }

        $data = $this->labelRepository->getDataByLabel($input['label_id']);

        if (!$data) {
            return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
        }

        if (!empty($data) && isset($data['status']) && !$data['status']) {
            return response()->json(['message' => $data['message']], Response::HTTP_NOT_FOUND);
        }

        if (!empty($data) && $data->is_fba_order) {
            return response()->json(['message' => 'The label ID you scanned is associated with an FBA order. Please scan the other label ID'], 422);
        }

        if (!empty($data->order_status) && in_array($data->order_status, [SaleOrder::DRAFT, SaleOrder::ON_HOLD, SaleOrder::CANCELLED, SaleOrder::REJECTED])) {
            return response()->json(['message' => 'Sale order is ' . str_replace('_', ' ', $data->order_status)], 422);
        }

        return response()->json($data);
    }

    public function createShipment(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'order_id' => 'required|exists:sale_order,id',
            'weight_value' => 'required',
            'weight_unit' => 'required',
            'predefinedPackageId' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = $this->labelRepository->createShipment($input);

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['order_id']);

        return response()->json($data, $data['code']);
    }

    public function scanLabelIdToVerify(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_id' => 'required|exists:sale_order,id',
            'label_id' => 'required',
            'shipment_id' => ['required',
                Rule::exists('shipment', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
                    ->where('is_deleted', false)
                    ->whereNull('refund_status')
            ],
            'employee_id' => 'required',
            'id_time_checking' => 'required|exists:time_tracking,id'
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = $this->labelRepository->scanLabelIdToVerify($input);
//        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['order_id']);
        if (!$data) {
            return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
        }

        return response()->json($data);
    }

    public function buyAShipment(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'rate' => 'required',
            'id_shipment' => 'required',
            'order_id' => 'required',
            'id_time_checking' => 'required|exists:time_tracking,id',
            'employeeId' => 'required|exists:employee,id',
            'is_shipment' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $this->labelRepository->buyAShipment($input);

        if (!$data) {
            return response()->json(['message' => 'create label error'], Response::HTTP_NOT_FOUND);
        }

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['order_id']);

        return $data;
    }

    public function downloadLabel($id)
    {
        $shipment = $this->labelRepository->generateLabel($id);

        if (!$shipment) {
            return view('label/404');
        }

        if ($shipment->provider == Shipment::PROVIDER_SHIPSTATION) {
            return view('label/shipstation');
        }

        if (!empty($shipment->refund_status)) {
            return view('label/voidShipment');
        }

        return redirect(env('AWS_URL') . "/label/{$id}.pdf?v=" . time());
    }

    public function logPrintedLabelShipment(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'shipmentId' => 'required',
            'employeeId' => 'required',
            'id_time_checking' => 'required|exists:time_tracking,id',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $this->labelRepository->logPrintedLabelShipment($input);

        if (!$data) {
            return response()->json(['message' => 'Log printed label shipment error'], Response::HTTP_NOT_FOUND);
        }

        return response()->json($data);
    }

    public function refundShipment(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'shipmentId' => ['required',
                Rule::exists('shipment', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
                    ->where('is_deleted', false)
                    ->whereNull('refund_status')
            ],
            'orderId' => 'required',
            'employeeId' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        try {
            $res = $this->labelRepository->refundShipment($input);

            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['orderId']);

            return response()->json($res);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function webhooks(Request $request)
    {
        try {
            $this->labelRepository->webhooks($request);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function downloadLabelZpl($id)
    {
        return $this->labelRepository->getUrlZpl($id);
    }

    public function createShippingFba(CreateLabelFbaRequest $request)
    {
        try {
            $input = $request->only(['label_id', 'employee_id', 'time_tracking_id']);
            $data = $this->labelRepository->createShippingFba($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function getPrintLabelHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'labels' => 'required|array',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $response = $this->labelRepository->getLabelByLabelIds($request->labels);

        return response()->json($response);
    }

    public function createShippingGelato(Request $request)
    {
        try {
            $input = $request->only(['order_id', 'employee_id']);
            $data = $this->labelRepository->createShippingGelato($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function buyAShipmentOneCall(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'order_id' => 'required',
            'id_time_checking' => 'required|exists:time_tracking,id',
            'employeeId' => 'required|exists:employee,id',
            'is_shipment' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $this->labelRepository->buyAShipmentOneCall($input);

        if (!$data) {
            return response()->json(['message' => 'create label error'], Response::HTTP_NOT_FOUND);
        }

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $input['order_id']);

        return response()->json($data, $data['code']);
    }
}
