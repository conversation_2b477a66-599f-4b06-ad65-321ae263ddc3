<?php

namespace App\Http\Controllers;

use App\Exports\SupplyAdditionExport;
use App\Repositories\Contracts\SupplyAdditionRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class SupplyInventoryAdditionController extends Controller
{
    protected $repository;

    public function __construct(SupplyAdditionRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function fetchAll(Request $request)
    {
        $items = $this->repository->fetchAll($request);

        return response()->json($items);
    }

    public function fetch($id)
    {
        $item = $this->repository->fetch($id);

        return response()->json($item);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'po_id' => 'required',
            'supply_id' => 'required',
            'quantity' => 'required|numeric|min:1',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'id_time_checking' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->repository->create($request);
    }

    public function revert($id)
    {
        return $this->repository->revert($id);
    }

    public function exportExcel(Request $request)
    {
        $request['export'] = 1;
        $orders = $this->repository->fetchAll($request);
        $fileName = 'supply_addition.xlsx';
        if (!empty($request['start_date']) && !empty($request['end_date'])) {
            $fileName = $request['start_date'] == $request['end_date'] ? "additions_{$request['start_date']}.xlsx" : "additions_{$request['start_date']}__{$request['end_date']}.xlsx";
        }

        return Excel::download(new SupplyAdditionExport($orders), $fileName);
    }
}
