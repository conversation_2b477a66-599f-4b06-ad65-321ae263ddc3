<?php

namespace App\Http\Controllers;

use App\Exports\ReportExportLabelShipping;
use App\Exports\ShipmentExport;
use App\Http\Requests\ShipmentDeliveryMappingRequest;
use App\Repositories\ShipmentMappinngDeliveryRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ShipmentMappingDeliveryController extends Controller
{
    protected $shipmentMappingDeliveryRepository;

    public function __construct(ShipmentMappinngDeliveryRepository $shipmentMappingDeliveryRepository)
    {
        $this->shipmentMappingDeliveryRepository = $shipmentMappingDeliveryRepository;
    }

    public function reportByRank(ShipmentDeliveryMappingRequest $request)
    {

        $input = $request->validated();

        $result = $this->shipmentMappingDeliveryRepository->reportByRank($input);

        return response()->json($result);
    }

    public function statistic(ShipmentDeliveryMappingRequest $request)
    {

        $input = $request->validated();

        $result = $this->shipmentMappingDeliveryRepository->statistic($input);

        return response()->json($result);
    }

    public function reportByState(ShipmentDeliveryMappingRequest $request)
    {

        $input = $request->validated();

        $result = $this->shipmentMappingDeliveryRepository->reportByState($input);

        return response()->json($result);
    }

}
