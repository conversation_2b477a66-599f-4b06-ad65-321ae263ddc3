<?php

namespace App\Http\Controllers;

use App\Repositories\FilterSaleOrderRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class FilterSaleOrderController extends Controller
{
    protected $filterSaleOrder;

    public function __construct(FilterSaleOrderRepository $filterSaleOrder)
    {
        $this->filterSaleOrder = $filterSaleOrder;
    }

    public function index(Request $request): JsonResponse
    {
        $data = $this->filterSaleOrder->fetchAll($request->all());

        return response()->json($data);
    }

    public function show($id): JsonResponse
    {
        $res = $this->filterSaleOrder->get($id);

        return response()->json($res['output']);
    }

    public function delete($id): JsonResponse
    {
        $res = $this->filterSaleOrder->delete($id);

        return response()->json($res['output']);
    }

    public function create(Request $request): JsonResponse
    {
        try {
            $res = $this->filterSaleOrder->create($request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }

    public function update($id, Request $request): JsonResponse
    {
        try {
            $res = $this->filterSaleOrder->update($id, $request);
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($res['output']);
    }
}
