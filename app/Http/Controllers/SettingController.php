<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSettingRequest;
use App\Models\Setting;
use App\Repositories\Contracts\SettingRepositoryInterface;
use Illuminate\Http\Request;
use Validator;

class SettingController extends Controller
{
    protected $settingRepository;

    public function __construct(SettingRepositoryInterface $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    public function fetch(Request $request)
    {
        $input = $request->all();

        $data = $this->settingRepository->fetchAll($input);

        return response()->json($data);
    }

    public function fetchByKey()
    {
        $data = $this->settingRepository->fetchByKey();

        return response()->json($data);
    }

    public function destroy($id)
    {
        $isSetting = Setting::where('id', $id)->first();
        if (!$isSetting) {
            return response()->json(['message' => ['Setting not found !']], 422);
        }

        return $this->settingRepository->delete($id);
    }

    public function store(StoreSettingRequest $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'label' => 'required',
            'name' => 'required',
            'value' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->settingRepository->create($input);
    }

    public function show($id)
    {
        $isSetting = Setting::where('id', $id)->first();
        if (!$isSetting) {
            return response()->json(['message' => ['Setting not found !']], 422);
        }

        return $this->settingRepository->fetch($id);
    }

    public function update($id, StoreSettingRequest $request)
    {
        $isSetting = Setting::where('id', $id)->first();
        if (!$isSetting) {
            return response()->json(['message' => ['Setting not found !']], 422);
        }

        $input = $request->all();
        $validator = Validator::make($input, [
            'label' => 'required',
            'name' => 'required',
            'value' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->settingRepository->update($id, $input);
    }

    public function getSettingByName(Request $request)
    {
        $input = $request->all();
        $name = $input['name'];
        $data = $this->settingRepository->getSettingByName($name);

        return response()->json($data);
    }

    public function checkIpBacklog()
    {
        $ipAddress = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $data = $this->settingRepository->checkIpBacklog($ipAddress);

        return response()->json($data);
    }
}
