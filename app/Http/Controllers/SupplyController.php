<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSupplyRequest;
use App\Repositories\SupplyRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SupplyController extends Controller
{
    protected $repository;

    public function __construct(SupplyRepository $repository)
    {
        $this->repository = $repository;
    }

    public function fetchAll(Request $request)
    {
        $items = $this->repository->fetchAll($request);

        return response()->json($items);
    }

    public function fetch($id)
    {
        $item = $this->repository->fetch($id);

        return response()->json($item);
    }

    public function search(Request $request)
    {
        $searchParams = $request->only(['id', 'sku']);
        if (empty($searchParams)
              || (empty($searchParams['id']) && empty($searchParams['sku']))) {
            return response()->json(['message' => 'Invalid params!'], Response::HTTP_NOT_FOUND);
        }

        $res = $this->repository->search($searchParams);
        if (!$res || empty($res)) {
            return response()->json([
                'message' => 'Supply not found!'
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'message' => 'Supply found!',
            'data' => $res
        ], Response::HTTP_OK);
    }

    public function store(StoreSupplyRequest $request)
    {
        $res = $this->repository->create($request->validated());

        return response()->json(['message' => 'Supply has been created successfully', 'data' => $res], 201);
    }

    public function update(StoreSupplyRequest $request, $id)
    {
        $this->repository->update($id, $request->validated());

        return response()->json(['message' => 'Update supply successfully.']);
    }

    public function delete($id)
    {
        $res = $this->repository->delete($id);
        if (!$res) {
            return response()->json([
                'message' => 'Supply not found!'
            ], 422);
        }

        return response()->json([
            'message' => 'Delete supply successfully.'
        ], 200);
    }

    public function fetchQuantity($id)
    {
        $item = $this->repository->fetchInventoryQuantity($id);

        return response()->json($item);
    }
}
