<?php

namespace App\Http\Controllers;

use App\Http\Requests\Mug\ConvertPdfRequest;
use App\Http\Requests\Mug\DownloadPdfRequest;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\Product;
use App\Repositories\BarcodeRepository;
use App\Repositories\PosterRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Validator;

class PosterController extends Controller
{
    private PosterRepository $posterRepository;

    private BarcodeRepository $barcodeRepository;

    public function __construct(PosterRepository $posterRepository, BarcodeRepository $barcodeRepository)
    {
        $this->posterRepository = $posterRepository;
        $this->barcodeRepository = $barcodeRepository;
    }

    public function count(Request $request)
    {
        try {
            $input = $request->all();
            //$input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $input['warehouse_id'] = $request->warehouse_id;
            $tiktok = $this->posterRepository->countPendingTiktok($input);
            $fba = $this->posterRepository->countPendingFBA($input);
            $reroute = $this->posterRepository->countPendingReroute($input);
            $manual = $this->posterRepository->countPendingManualProcess($input);
            $reprint = $this->posterRepository->countPendingReprint($input);
            $xqc = $this->posterRepository->countPendingXQC($input);
            $eps = $this->posterRepository->countPendingEPS($input);
            $styles = $this->posterRepository->countPendingStyle($input);
            $bulkOrder = $this->posterRepository->countPendingBulkOrder($input);
            $barcodePrintedTimes = BarcodePrintedTime::where('print_method', BarcodePrinted::METHOD_POSTER)
                ->where('warehouse_id', $input['warehouse_id'])
                ->get();
            $tiktok['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'tiktok', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $xqc['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'xqc', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $eps['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'eps', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $reprint['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'reprint', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $reroute['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'reroute', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $fba['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'fba', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $manual['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'manual', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $bulkOrder['printed_at'] = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'bulk_order', $input['warehouse_id'], $input['store_id'] ?? null, $input['account_id'] ?? null);
            $results['tiktok'] = $tiktok;
            $results['fba'] = $fba;
            $results['reroute'] = $reroute;
            $results['manual'] = $manual;
            $results['reprint'] = $reprint;
            $results['xqc'] = $xqc;
            $results['eps'] = $eps;
            $results['style'] = $styles;
            $results['bulk_order'] = $bulkOrder;

            return $this->responseSuccess($results);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function pdf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->posterRepository->listPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function convertPdf(ConvertPdfRequest $request)
    {
        try {
            $input = $request->all();
            if (!empty($input['product_id'])) {
                $product = Product::with(['productStyle', 'printingPresetSku'])
                    ->whereHas('printingPresetSku')
                    ->find($input['product_id']);

                if (!$product?->printingPresetSku) {
                    throw new Exception('Product not found or not set preset yet!', Response::HTTP_NOT_FOUND);
                }
                if (empty($product->printingPresetSku->poster_size)) {
                    throw new Exception('Product set preset poster!', Response::HTTP_NOT_FOUND);
                }
            }
            $data = $this->posterRepository->convertPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function downloadPdf(DownloadPdfRequest $request)
    {
        try {
            $input = $request->only(['is_history', 'barcode_printed_id']);
            $data = $this->posterRepository->downloadPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function historyPdf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->posterRepository->historyPdf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function convertToAi(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'barcode_id' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }
            $data = $this->posterRepository->convertToAi($request);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function fetchLayoutsConfig(Request $request)
    {
        $data = $this->posterRepository->fetchAllPosterLayoutsConfig();

        return $this->responseSuccess($data);
    }
}
