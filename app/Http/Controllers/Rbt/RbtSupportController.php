<?php

namespace App\Http\Controllers\Rbt;

use App\Http\Controllers\Controller;
use App\Http\Requests\DarkPodToRackRequest;
use App\Http\Requests\GenerateRbtBatchRequest;
use App\Http\Requests\RbtConfirmBarcodePrintedRequest;
use App\Http\Requests\RbtCreateAdjustmentRequest;
use App\Http\Requests\RbtCreateInternalRequest;
use App\Http\Requests\RbtCreateOrdersRequest;
use App\Http\Requests\RbtDispatchedRequest;
use App\Http\Requests\RbtGetPdfFileRequest;
use App\Http\Requests\RbtImportSkuRequest;
use App\Http\Requests\RbtInventoryOverviewRequest;
use App\Http\Requests\RbtKittingRequest;
use App\Http\Requests\RbtPreConfirmationCount;
use App\Http\Requests\RbtPrintRequest;
use App\Http\Requests\RbtReceivedBoxRequest;
use App\Http\Requests\RbtShippingLabelPrintedRequest;
use App\Http\Requests\RbtUnmarkOrders;
use App\Http\Requests\ReplenismentToRackRequest;
use App\Http\Requests\StoreRbtProductRequest;
use App\Http\Requests\UpdatRbtProductRequest;
use App\Models\BarcodePrinted;
use App\Models\Box;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\Location;
use App\Models\Product;
use App\Models\RbtBatchNumber;
use App\Models\RbtProduct;
use App\Models\RbtWipReceived;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemQualityControl;
use App\Models\SaleOrderKitting;
use App\Models\Shipment;
use App\Models\User;
use App\Models\Warehouse;
use App\Repositories\BarcodeRepository;
use App\Repositories\InternalRequestRepository;
use App\Repositories\InventoryDeductionRepository;
use App\Repositories\LabelRepository;
use App\Repositories\QualityControlRepository;
use App\Repositories\RbtProductRepository;
use App\Repositories\RbtRepository;
use App\Repositories\SaleOrderItemBarcodeRepository;
use App\Repositories\SaleOrderRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class RbtSupportController extends Controller
{
    protected BarcodeRepository $barcodeRepository;

    protected InternalRequestRepository $internalRequestRepository;

    protected InventoryDeductionRepository $inventoryDeduction;

    protected SaleOrderItemBarcodeRepository $saleOrderItemBarcodeRepository;

    protected RbtProductRepository $rbtProductRepository;

    protected SaleOrderRepository $saleOrderRepository;

    protected QualityControlRepository $qualityControlRepository;

    protected RbtRepository $rbtRepository;

    protected LabelRepository $labelRepository;

    public function __construct(
        RbtRepository $rbtRepository,
        InventoryDeductionRepository $inventoryDeduction,
        SaleOrderItemBarcodeRepository $saleOrderItemBarcodeRepository,
        BarcodeRepository $barcodeRepository,
        InternalRequestRepository $internalRequestRepository,
        RbtProductRepository $rbtProductRepository,
        SaleOrderRepository $saleOrderRepository,
        QualityControlRepository $qualityControlRepository,
        LabelRepository $labelRepository,
    ) {
        $this->rbtRepository = $rbtRepository;
        $this->rbtProductRepository = $rbtProductRepository;
        $this->internalRequestRepository = $internalRequestRepository;
        $this->inventoryDeduction = $inventoryDeduction;
        $this->saleOrderItemBarcodeRepository = $saleOrderItemBarcodeRepository;
        $this->barcodeRepository = $barcodeRepository;
        $this->saleOrderRepository = $saleOrderRepository;
        $this->qualityControlRepository = $qualityControlRepository;
        $this->labelRepository = $labelRepository;
    }

    public function deduction($label_id, Request $request)
    {
        $barcode = SaleOrderItemBarcode::where('label_id', $label_id)->first();

        if (!$barcode) {
            return response()->json([
                'message' => 'Order Not Found',
                'status' => 'error'
            ], 400);
        }

        $input = $request->all();
        $input['label_id'] = $label_id;
        $input['user_id'] = RbtProduct::RBT_EMPLOYEE_ID;
        $barcodeItem = $this->saleOrderItemBarcodeRepository->getByLabelId($input['label_id']);

        if (!empty($barcodeItem) && $barcodeItem->isProductSampleCustom()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Warning! Custom SKU. Cannot deduct.'
            ], 400);
        }

        $receivedOrder = DB::table('rbt_received')->where('order_id', $barcode->order_id)->first();

        if (!$receivedOrder) {
            return response()->json([
                'message' => 'Order Not Received',
                'status' => 'error'
            ], 400);
        }

        $input['warehouse_id'] = $barcodeItem->warehouse_id;
        $deduction = $this->inventoryDeduction->create($input);
        $content = $deduction->getContent();
        $data = json_decode($content, true);

        return response()->json([
            'status' => 'success',
            'wip_id' => $data['label_id'], // Assuming $deduction is an object
            'item_id' => $data['sale_order_sku'],
            'picked_at' => $data['created_at']
        ]);
    }

    public function print(RbtPrintRequest $request)
    {
        $data = $request->validated();
        $barcodesWithSku = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->whereIn('sale_order_item_barcode.label_id', $data['wip_ids'])
            ->get(['sale_order_item_barcode.label_id', 'sale_order_item.product_sku', 'sale_order_item_barcode.barcode_printed_id']);
        $printedBarcodes = $barcodesWithSku->filter(function ($item) {
            return $item->barcode_printed_id != 0;
        });
        // Extract the label IDs and SKUs
        $skus = $barcodesWithSku->pluck('product_sku');

        // Check if any barcodes have already been printed
        if ($printedBarcodes->isNotEmpty()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Some barcodes have already been printed.',
                'failed_label_ids' => $printedBarcodes->pluck('label_id')
            ], 422);
        }

        // Ensure all values in $sku are the same
        if ($skus->isNotEmpty() && $skus->unique()->count() > 1) {
            return response()->json([
                'status' => 'error',
                'message' => 'All WIP IDs must have the same SKU.'
            ], 400);
        }

        // Check if any orders have not been picked up yet
        $barcodesWithUnreceivedOrders = RbtWipReceived::whereIn('label_id', $data['wip_ids'])
            ->pluck('label_id');
        $failedLabelIds = collect($data['wip_ids'])->diff($barcodesWithUnreceivedOrders);

        if ($failedLabelIds->isNotEmpty()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Some barcodes have orders that have not been picked up yet.',
                'failed_label_ids' => $failedLabelIds->values()
            ], 422);
        }

        $params = $request->all();
        $params['limit'] = $params['limit'] ?? 10;
        $params['warehouse_id'] = $request->warehouse_id;
        $newBarcodePrinted = [
            'employee_id' => RbtProduct::RBT_EMPLOYEE_ID,
            'warehouse_id' => $params['warehouse_id'] ?? 1,
            'store_id' => null,
            'style_sku' => $params['style_sku'] ?? null,
            'account_id' => $params['account_id'] ?? null,
            'quantity_input' => $params['limit'] ?? 0,
            'is_xqc' => $params['is_xqc'] ?? null,
            'is_eps' => $params['is_eps'] ?? null,
            'is_manual' => $params['is_manual'] ?? null,
            'is_reprint' => $params['is_reprint'] ?? null,
            'is_reroute' => $params['is_reroute'] ?? null,
            'is_fba' => $params['is_fba'] ?? null,
            'is_insert' => $params['is_insert'] ?? null,
            'is_tiktok' => $params['is_tiktok'] ?? null,
            'is_bulk_order' => $params['is_bulk_order'] ?? null,
            'user_id' => RbtProduct::RBT_EMPLOYEE_ID,
            'created_at' => date('Y-m-d H:i:s'),
            'print_method' => BarcodePrinted::METHOD_DTG,
            'station_id' => $params['station_id']
        ];

        DB::beginTransaction();

        try {
            $id = $this->barcodeRepository->insertBarcodePrinted($newBarcodePrinted);
            $params['barcode_printed_id'] = $id;
            $updateWips = $this->rbtRepository->updateBarcodePrintedItem($params);

            if (!$updateWips) {
                DB::rollBack();
                throw new Exception('Wips cant be printed');
            }

            $this->rbtRepository->updateLastBarcodePrintedTime($params);
            // Update barcode position by number
            $arrLabel = [];
            $i = 1;

            // check invalid wip
            $listBarcode = $data['wip_ids'];
            $confirmBarcode = SaleOrderItemBarcode::where('barcode_printed_id', $id)->pluck('label_id')->toArray();
            $labelPrinted = array_intersect($listBarcode, $confirmBarcode);
            $labelNotPrinted = array_diff($listBarcode, $confirmBarcode);
            $formatLabelNotPrinted = $this->rbtRepository->formatLabelNotPrinted($labelNotPrinted);

            foreach ($labelPrinted as $barcode) {
                $arrLabel[] = [
                    'batch_id' => $id,
                    'label_id' => $barcode,
                    'batch_number' => $i++,
                ];
            }

            // Insert the array into the database
            if (!empty($arrLabel)) {
                RbtBatchNumber::insert($arrLabel);
            }

            $barcodeService = $this->rbtRepository->convertBarcodeV5($id);
            BarcodePrinted::where('id', $id)->update(['convert_status' => BarcodePrinted::ACTIVE]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'batch_id' => $id,
                'last_wip_id' => $barcodeService['last_wip_id'],
                'pdf_url' => env('AWS_S3_URL') . '/barcode/' . $id . '.pdf',
                'inactive_wips' => $formatLabelNotPrinted

            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('RbtSupportController.print', [
                'request' => $request->all(),
                $e,
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    public function confirmBarcodePrinted(RbtConfirmBarcodePrintedRequest $request)
    {
        $data = $request->validated();
        $isDestroy = $data['destroy'] ?? false;
        $wip = SaleOrderItemBarcode::with('orderItem')
            ->where('label_id', $data['last_wip_id'])
            ->whereHas('rbtWipReceived', function ($query) {
                $query->whereNotNull('label_id');
            })
            ->first();
        if (!$wip) {
            return response()->json([
                'status' => 'error',
                'message' => 'Some barcodes have orders that have not been picked up yet.',
            ], 422);
        }
        //check dubplicate barcode
        $location = Location::where('is_deleted', Location::NOT_DELETED)
            ->where('rbt_sku', $wip->orderItem->product_sku)
            ->where('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->first();

        if (empty($location)) {
            return response()->json([
                'status' => 'error',
                'message' => "No location found containing SKU '{$wip->orderItem->product_sku}'"
            ], 422);
        }

        if ($location->type != Location::MOVING_SHELVES) {
            return response()->json([
                'status' => 'error',
                'message' => 'A duplicate shelve face barcode already exists in the pulling shelve.',
            ], 422);
        }

        $barcodePrinted = BarcodePrinted::where('id', $wip->barcode_printed_id)->first();

        if (!$barcodePrinted) {
            return response()->json([
                'status' => 'Error',
                'message' => 'Barcode printed not found.',
            ], 422);
        }

        if ($barcodePrinted->print_status) {
            return response()->json([
                'status' => 'Error',
                'message' => 'Barcode already printed.',
            ], 422);
        }

        $employee = Employee::where('code', $data['employee_id'])
            ->where('is_deleted', Employee::INACTIVE)
            ->first();

        if (!$employee) {
            return response()->json([
                'status' => 'error',
                'message' => 'Employee not found or has been deleted.'
            ], 422);
        }

        if ($employee->warehouse_id != Warehouse::WAREHOUSE_SANJOSE_ID) {
            return response()->json([
                'status' => 'error',
                'message' => 'The employee does not exist in this warehouse.'
            ], 422);
        }

        $id = $barcodePrinted->id;
        $product = SaleOrderItem::find($wip->order_item_id);
        $params = [
            'employee_id' => $employee->id ?? RbtProduct::RBT_EMPLOYEE_ID,
            'product_id' => $product->product_id,
            'box_quantity' => 1,
            'warehouse_id' => 1,
            'quantity' => 0,
            'location_id' => $location->id,
            'batch_id' => $id,
        ];

        if ($isDestroy) {
            $response = $this->rbtRepository->destroyBatch($id, $data['employee_id']);
            $this->rbtRepository->createAdjustment($params);
        } else {
            //remove batch not printed
            $wipId = $wip->label_id;
            $listWip = RbtBatchNumber::where('batch_id', $id)->orderBy('batch_number', 'asc')->get();
            $notPrintedWip = $listWip->skipUntil(function ($value) use ($wipId) {
                return $value->label_id === $wipId;
            })->skip(1)->values();
            $discardedWips = $notPrintedWip->pluck('label_id')->toArray();
            SaleOrderItemBarcode::whereIn('label_id', $discardedWips)->update(['barcode_printed_id' => 0, 'print_barcode_at' => null]);
            $this->barcodeRepository->updateBarcodePrinted($id, ['print_status' => true, 'employee_id' => $employee->id]);
            $this->barcodeRepository->logTimeLineOrderByBarcodePrintedId($id, $employee->id);
            $this->rbtRepository->discard([
                'label_ids' => $discardedWips,
                'employee_id' => $data['employee_id'],
                'batch_id' => $id
            ]);
            SaleOrderItemBarcode::where('barcode_printed_id', $id)->update([
                'employee_pull_id' => $employee->id,
            ]);
            $input = [
                'barcode_id' => $id,
                'location_id' => $location->id,
                'employee_id' => $employee->id,
            ];
            $this->rbtRepository->deduction($input);

            if (count($notPrintedWip) > 0) {
                // to go adjusment
                $this->rbtRepository->createAdjustment($params);
            }

            $response = $this->rbtRepository->countConfirmWip($id);
            $data['total_confirmed_wips'] = $response['total_confirmed_wips'];
            $this->rbtRepository->InsertRbtPerformance($data);
        }

        return response()->json($response, 200);
    }

    public function getListInternalRequest(Request $request)
    {
        $data = $this->internalRequestRepository->getListRBT($request->all())->toArray();
        unset($data['links']);

        return response()->json($data);
    }

    public function createInternalRequest(RbtCreateInternalRequest $request)
    {
        try {
            $data = $request->validated();

            if (!empty($data['wip_id'])) {
                $label = SaleOrderItemBarcode::select('sale_order_item.*')
                    ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                    ->where('sale_order_item_barcode.label_id', $data['wip_id'])
                    ->first();

                if (!$label) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'The print batch has not been printed.',
                    ], 400);
                }

                $data['product_id'] = $label->product_id;
            } elseif (!empty($data['sku'])) {
                $product = Product::query()
                    ->where('sku', $data['sku'])
                    ->first();

                if (!$product) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Sku not found.',
                    ], 400);
                }

                $data['product_id'] = $product->id;
            }
            $isRbtProduct = RbtProduct::where('product_id', $data['product_id'])
                ->where('is_active', true)
                ->exists();
            if (!$isRbtProduct) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid SKU: Not available in rbt_skus',
                ], 400);
            }

            $data['box_quantity'] = $data['box_quantity'] ?? 1;
            $res = $this->rbtRepository->createInternal($data);

            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        } catch (\Exception $exception) {
            Log::error('RbtSupportController.createInternalRequest', [
                $exception,
                'request' => $request->all(),
            ]);

            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'status' => 'success',
            'internal_request_id' => $res['output']['data'],
            'sku' => !empty($data['wip_id']) && !empty($label->sku) ? substr($label->sku, -9) : $product->sku ?? '',
        ]);
    }

    public function importSkus()
    {
        DB::beginTransaction();

        try {
            $fileName = 'rbt_skus.csv';
            $emptyFileMsg = 'CSV file is empty!';
            $nameColumnConfig = 0;

            // Open the CSV file for reading
            $file = fopen(storage_path('app/public/csv-template/' . $fileName), 'r');
            $productSkus = [];
            $isFirstRow = true;

            // Read each line of the CSV file until the end
            while (($row = fgetcsv($file)) !== false) {
                // Skip the first title row
                if ($isFirstRow) {
                    $isFirstRow = false;

                    continue;
                }

                if (!empty($row) && isset($row[$nameColumnConfig])) {
                    $productSkus[] = $row[$nameColumnConfig];
                }
            }

            // Close the file
            fclose($file);

            if (empty($productSkus)) {
                $this->info($emptyFileMsg);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid data, Import Sku failed.',
                ], 422);
            }

            $products = Product::select('id', 'sku')
                ->whereIn('sku', $productSkus)
                ->get()
                ->keyBy('sku');
            $insertData = []; // Initialize an array to hold the data for bulk insert

            foreach ($productSkus as $style => $sku) {
                if (isset($products[$sku])) {
                    $insertData[] = [
                        'product_id' => $products[$sku]->id,
                        'is_active' => true,
                    ];
                }
            }

            // Perform a bulk insert
            if (!empty($insertData)) {
                RbtProduct::insert($insertData); // Insert all rows at once
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
            ], 200);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('RbtSupportController.immportSkus', [$e]);

            return response()->json([
                'status' => 'error',
                'message' => 'Invalid data, Import Sku failed.',
            ], 422);
        }
    }

    public function createOrders(RbtCreateOrdersRequest $request)
    {
        $data = $request->validated(); // Get validated data
        $res = $this->rbtRepository->createOrders($data);

        if (!$res) {
            return response()->json('Invalid data', 422);
        }

        return response()->json([
            'status' => 'success',
        ], 200);
    }

    public function getPdfFile(RbtGetPdfFileRequest $request)
    {
        $data = $request->only(['batch_id', 'label_id']);

        // Prioritize batch_id if provided
        if (!empty($data['batch_id'])) {
            $barcodePrinted = BarcodePrinted::find($data['batch_id']);

            if (!$barcodePrinted) {
                return $this->errorResponse('Batch not found.');
            }

            $id = $barcodePrinted->id;
        } elseif (!empty($data['label_id'])) { // Check label_id if batch_id is not provided
            $barcode = SaleOrderItemBarcode::where('label_id', $data['label_id'])
                ->whereNotNull('barcode_printed_id')
                ->first();

            if (!$barcode) {
                return $this->errorResponse('Batch not found.');
            }

            $id = $barcode->barcode_printed_id;
        }

        // Validate resolved ID
        if (empty($id)) {
            return $this->errorResponse('No valid batch ID or label ID found.');
        }

        $barcodePrinted = BarcodePrinted::find($id);
        $listBarcode = RbtBatchNumber::where('batch_id', $id)->pluck('label_id')->toArray();
        $confirmBarcode = SaleOrderItemBarcode::where('barcode_printed_id', $id)->pluck('label_id')->toArray();
        $labelPrinted = array_intersect($listBarcode, $confirmBarcode);
        $labelNotPrinted = array_diff($listBarcode, $confirmBarcode);
        $response = [
            'status' => 'success',
            'batch_id' => (string) $id,
            'pdf_url' => env('AWS_S3_URL') . '/barcode/' . $id . '.pdf',
            'unconfirmed_wips' => [],
            'confirmed_wips' => [],
            'discarded_wips' => []
        ];

        if ($barcodePrinted->print_status) {
            $response['confirmed_wips'] = array_values($labelPrinted);
            $response['discarded_wips'] = array_values($labelNotPrinted);
        } else {
            $response['unconfirmed_wips'] = array_values($listBarcode);
        }

        return response()->json($response, 200);
    }

    private function errorResponse(string $message)
    {
        return response()->json([
            'status' => 'error',
            'message' => $message,
        ], 422);
    }

    public function getListWips(request $request)
    {
        $input = $request->all();
        $input['page'] = !empty($input['page']) ? (int) $input['page'] : 1;
        $input['limit'] = min($request->input('limit', 25), 500);
        $orders = $this->rbtRepository->fetchWips($input);

        return response()->json($orders);
    }

    public function getWip($id)
    {
        $item = $this->rbtRepository->getWip($id);

        if (empty($item)) {
            return response()->json(['status' => 'error', 'message' => 'Wip not found'], 404);
        }

        return response()->json($item);
    }

    public function updateReceivedWip($id)
    {
        try {
            // Tìm order theo id
            $barcode = SaleOrderItemBarcode::where('label_id', $id)->first();

            if (!$barcode) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'WIP Not Found',
                ], 400);
            }

            // Kiểm tra nếu đã có record RbtWipReceived
            $barcodeReceived = RbtWipReceived::where('label_id', $id)->first();

            if ($barcodeReceived) {
                return response()->json([
                    'status' => 'error',
                    'message' => "$id cannot be marked as received because it has already been received.",
                ], 400);
            }

            // Nếu không có record, tạo mới
            $newRecord = new RbtWipReceived();
            $newRecord->label_id = $id;
            $newRecord->updated_at = now();
            $newRecord->save();

            return response()->json([
                'status' => 'success',
                'label_id' => $id,
                'received_at' => now(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function receivedBox(RbtReceivedBoxRequest $request)
    {
        $data = $request->validated();
        $box = Box::with('product')->where('barcode', $data['box_id'])
            ->where('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->first();

        if (!$box) {
            return response()->json([
                'status' => 'Error',
                'message' => 'Box is not found or deleted.',
            ], 422);
        }
        $internalRequest = InternalRequest::where('box_id', $box->id)
            ->first();
        if (!$internalRequest) {
            return response()->json([
                'status' => 'Error',
                'message' => 'Cannot confirm box reception: The box has not been assigned to any internal request.',
            ], 422);
        }

        if ($internalRequest->status == InternalRequest::CHECKED_STATUS) {
            return response()->json([
                'status' => 'Error',
                'message' => 'This internal request has already been confirmed. Please proceed with another request.',
            ], 422);
        }

        if ($internalRequest->status != InternalRequest::UNCHECK_STATUS) {
            return response()->json([
                'status' => 'Error',
                'message' => 'Only "Uncheck" request can be confirmed by Pulling team.',
            ], 422);
        }

        $location = Location::where('is_deleted', Location::NOT_DELETED)
            ->where('rbt_sku', $box->product->sku)
            ->where('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->first();

        if (empty($location)) {
            return response()->json([
                'status' => 'error',
                'message' => "No location found containing SKU '{$box->product->sku}'"
            ], 422);
        }

        if ($location->type != Location::MOVING_SHELVES) {
            return response()->json([
                'status' => 'error',
                'message' => 'A duplicate shelve face barcode already exists in the pulling shelve.',
            ], 422);
        }
        if (!empty($data['quantity']) && $data['quantity'] > $box->quantity) {
            return response()->json([
                'status' => 'error',
                'message' => "Quantity cannot be greater than {$box->quantity}."
            ], 422);
        }

        $this->rbtRepository->receivedBox($box, $internalRequest, $location, $data);

        return response()->json([
            'status' => 'success',
            'sku' => $box->product->sku,
            'units' => $box->quantity
        ]);
    }

    public function listSku(Request $request)
    {
        $response = $this->rbtProductRepository->getList($request->all());

        return response()->json($response);
    }

    public function storeSku(StoreRbtProductRequest $request)
    {
        $response = $this->rbtProductRepository->store($request->validated());

        return response()->json($response);
    }

    public function updateSku(UpdatRbtProductRequest $request, $id)
    {
        $response = $this->rbtProductRepository->update($id, $request->validated());

        return response()->json($response);
    }

    public function getBox($id)
    {
        $item = $this->rbtRepository->getBox($id);

        if (empty($item)) {
            return response()->json(['status' => 'error', 'message' => 'Box not found'], 404);
        }

        return response()->json($item);
    }

    public function createAdjustment(RbtCreateAdjustmentRequest $request)
    {
        try {
            $data = $request->validated();
            $employeeId = RbtProduct::RBT_EMPLOYEE_ID;
            $location = Location::where('is_deleted', Location::NOT_DELETED)
                ->where('barcode', $data['location'])
                ->first();
            if (!$location) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Location not found or has been deleted.'
                ], 400);
            }

            if ($location->type != 3) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Adjustment not allowed for this location.'
                ], 400);
            }

            if (empty($location->rbt_sku)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Location does not contain any SKU.'
                ], 400);
            }

            if (!empty($data['employee_code'])) {
                $employee = Employee::where('code', $data['employee_code'])
                    ->where('is_deleted', Employee::INACTIVE)
                    ->first();
                if (empty($employee)) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Employee not found or has been deleted.'
                    ], 400);
                }
                $employeeId = $employee->id;
            }

            // Validate SKU
            $product = Product::where('sku', $location->rbt_sku)->first();

            if (!$product) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'SKU not found.'
                ], 404);
            }

            $params = [
                'employee_id' => $employeeId,
                'product_id' => $product->id,
                'warehouse_id' => Warehouse::WAREHOUSE_SANJOSE_ID,
                'quantity' => $data['quantity_on_hand'],
                'location_id' => $location->id,
            ];
            $adjustmentQuantiy = $this->rbtRepository->createAdjustment($params, true);

            return response()->json([
                'status' => 'success',
                'location' => $location->barcode,
                'sku' => $product->sku,
                'previous_stock' => $adjustmentQuantiy['previous_stock'],
                'quantity_adjusted' => $adjustmentQuantiy['quantity_adjusted'],
                'updated_stock' => $adjustmentQuantiy['updated_stock'],

            ]);
        } catch (\Exception $exception) {
            Log::error('RbtSupportController.createAdjustmentRequest', [
                $exception,
                'request' => $request->all(),
            ]);

            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function markAsKitted(RbtKittingRequest $request): JsonResponse
    {
        $now = now('UTC');
        $data = $request->validated();
        $eventType = !empty($data['event_type']) ? $data['event_type'] : SaleOrderKitting::EVENT_TYPE_START;

        $lockKey = 'kitting_lock_' . $data['wip_id'] . '_' . $data['event_type'];
        $lock = Cache::lock($lockKey, 10);
        if (!$lock->get()) {
            return response()->json([
                'status' => 'error',
                'message' => 'This WIP ID is currently being processed by another request. Please try again later.',
            ], 423);
        }

        try {
            $saleOrderItemBarcode = SaleOrderItemBarcode::query()
                ->where('label_id', $data['wip_id'])
                ->where('is_deleted', SaleOrderItemBarcode::NOT_DELETED)
                ->first();
            if (empty($saleOrderItemBarcode)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'The selected wip id is invalid.',
                ], 400);
            }
            $employee = Employee::query()
                ->where('code', $data['employee_code'])
                ->first();

            DB::beginTransaction();

            if (empty($saleOrderItemBarcode->employee_kitted_id)) {
                $saleOrderItemBarcode->employee_kitted_id = $employee->id;
                $saleOrderItemBarcode->kitted_at = $now;
                $saleOrderItemBarcode->employee_staging_id = $employee->id;
                $saleOrderItemBarcode->staged_at = $now;

                saleOrderHistory(
                    auth()?->user()?->id ?? null,
                    $employee->id,
                    $saleOrderItemBarcode->order_id,
                    SaleOrderHistory::UPDATE_ORDER_KITTED_TYPE,
                    'Label ID ' . $saleOrderItemBarcode->label_id . ' was successfully kitted');
            }
            $isPassQc = SaleOrderItemQualityControl::query()
                ->where('label_id', $saleOrderItemBarcode->label_id)
                ->where('status', SaleOrderItemQualityControl::STATUS_PASS_QC)
                ->first();

            if (empty($isPassQc)) {
                $saleOrderItemBarcode->employee_qc_id = $employee->id;
                $saleOrderItemBarcode->qc_at = $now;
            }
            $saleOrderItemBarcode?->save();

            $saleOrderKitting = SaleOrderKitting::query()
                ->where('sale_order_item_barcode_id', $saleOrderItemBarcode->id)
                ->first();

            if (!empty($saleOrderKitting)
                && (
                    ($eventType == SaleOrderKitting::EVENT_TYPE_START && !empty($saleOrderKitting->employee_id_start))
                    || ($eventType == SaleOrderKitting::EVENT_TYPE_END && !empty($saleOrderKitting->employee_id_end))
                )) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'This event type has already been recorded for the given WIP ID.',
                ], 400);
            }

            if (!empty($saleOrderKitting)
                && (
                    $saleOrderKitting->ring_label != $data['ring']
                    || $saleOrderKitting->column_label != $data['column']
                    || $saleOrderKitting->row_label != $data['row']
                )) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Mismatch in ring/column/row between start and end event',
                ], 400);
            }

            $saleOrderKitting = !empty($saleOrderKitting) ? $saleOrderKitting : new SaleOrderKitting();

            if ($eventType == SaleOrderKitting::EVENT_TYPE_START && empty($saleOrderKitting->employee_id_start)) {
                $saleOrderKitting->employee_id_start = !empty($employee) ? $employee->id : null;
                $saleOrderKitting->kitting_started_at = $now;
            }

            if ($eventType == SaleOrderKitting::EVENT_TYPE_END && empty($saleOrderKitting->employee_id_end)) {
                $saleOrderKitting->employee_id_end = !empty($employee) ? $employee->id : null;
                $saleOrderKitting->kitting_ended_at = $now;
            }

            $saleOrderKitting->sale_order_item_barcode_id = $saleOrderItemBarcode->id;
            $saleOrderKitting->ring_label = $data['ring'];
            $saleOrderKitting->column_label = $data['column'];
            $saleOrderKitting->row_label = $data['row'];
            $saleOrderKitting->save();

            $notYetKitted = SaleOrderItemBarcode::query()
                ->where('order_id', $saleOrderItemBarcode->order_id)
                ->where('is_deleted', SaleOrderItemBarcode::NOT_DELETED)
                ->whereNull('employee_kitted_id')
                ->exists();

            if (!$notYetKitted) {
                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->order_id);
            }

            // auto pass QC
            if (empty($isPassQc)) {
                $rootLabelId = (isset($saleOrderItemBarcode->label_root_id) && $saleOrderItemBarcode->label_root_id != '0') ? $saleOrderItemBarcode->label_root_id : $saleOrderItemBarcode->label_id;
                SaleOrderItemQualityControl::query()
                    ->insert([
                        'order_id' => $saleOrderItemBarcode->order_id,
                        'order_item_id' => $saleOrderItemBarcode->order_item_id,
                        'user_id' => User::SYSTEM,
                        'sku' => $saleOrderItemBarcode->sku,
                        'label_id' => $saleOrderItemBarcode->label_id,
                        'created_at' => $now,
                        'updated_at' => $now,
                        'employee_id' => $employee->id,
                        'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                        'label_root_id' => $rootLabelId
                    ]);
                $this->qualityControlRepository->updateBarcodeStatus($rootLabelId);

                saleOrderHistory(
                    auth()?->user()?->id ?? null,
                    $employee->id,
                    $saleOrderItemBarcode->order_id,
                    SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
                    'Label ID ' . $saleOrderItemBarcode->label_id . ' approved QC automatically via kitting.');
            }

            $pendingQc = SaleOrderItemBarcode::query()
                ->where('order_id', $saleOrderItemBarcode->order_id)
                ->where('is_deleted', SaleOrderItemBarcode::NOT_DELETED)
                ->whereNull('staged_at')     // chưa QC (hoặc dùng qc_at / employee_qc_id)
                ->exists();                  // true nếu TỒN TẠI item chưa QC

            if (!$pendingQc) {               // tức là QC hết rồi
                SaleOrder::whereKey($saleOrderItemBarcode->order_id)
                    ->update([
                        'order_qc_status' => SaleOrder::ACTIVE,
                        'order_qc_at' => $now,
                    ]);
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'wip_id' => $data['wip_id'],
                'kitted_at' => $now,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('RbtSupportController.markAsKitted', [$exception]);

            return response()->json([
                'status' => 'error',
                'message' => $exception->getMessage(),
            ], 400);
        } finally {
            optional($lock->release());
        }
    }

    public function preConfirmationCount(RbtPreConfirmationCount $request)
    {
        $data = $request->validated();
        $barcode = SaleOrderItemBarcode::where('label_id', $data['last_wip_id'])
            ->whereNotNull('barcode_printed_id')
            ->first();

        if (!$barcode) {
            return $this->errorResponse('Batch not found.');
        }

        $id = $barcode->barcode_printed_id;

        if (empty($id)) {
            return $this->errorResponse('No valid label ID found.');
        }

        $data['batch_id'] = $id;
        $response = $this->rbtRepository->preConfirmationCount($data);

        return response()->json($response, 200);
    }

    public function importSku(RbtImportSkuRequest $request)
    {
        return $this->rbtRepository->rerouteImportCsvFile($request);
    }

    public function removeSkus()
    {
        return $this->rbtRepository->removeSkus();
    }

    public function RbtUnmarkOrders(RbtUnmarkOrders $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            if (empty($data['order_id']) && empty($data['wip_id'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => "Invalid 'order_id' or 'wip_id'. Please check and try again.",
                ], 400);
            }

            $result = $this->rbtRepository->unMarkSaleOrderRbt($data);
            DB::commit();

            return response()->json($result);
        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function movingReplenishmentToRack(ReplenismentToRackRequest $request)
    {
        $data = $request->validated();
        $data['warehouse_id'] = Warehouse::WAREHOUSE_SANJOSE_ID;
        try {
            $result = $this->rbtRepository->movingReplenishmentToRack($data);
            if (!$result['status']) {
                throw new Exception($result['output']['message'] ?? 'Failed to move replenishment to rack');
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Moving replenishment to rack successfully.',
                'data' => $result,
            ]);
        } catch (Exception $e) {
            Log::error('RbtSupportController.movingReplenishmentToRack', [
                'request' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getSkuByWip(Request $request)
    {
        try {
            $input = $request->all();
            if (empty($input['wip_id'])) {
                throw new Exception('WIP ID is required.');
            }
            $data = $this->rbtRepository->getSkuByWip($input);

            return response()->json([
                'status' => 'success',
                'data' => $data,
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    public function shippingLabel(Request $request)
    {
        try {
            $validated = $request->validate([
                'wip_id' => 'nullable|string',
                'order_number' => 'nullable|string',
            ]);

            if (empty($validated['wip_id']) && empty($validated['order_number'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Either wip_id or order_number must be provided.',
                ], 422);
            }

            if (!empty($validated['order_number'])) {
                $saleOrder = SaleOrder::where('order_number', $validated['order_number'])->first();
                if (!$saleOrder) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'WIP ID or order number does not exist in the system.',
                    ], 422);
                }
                $dataValidateOrder = $this->validateOrder($saleOrder);
                if (!$dataValidateOrder['status']) {
                    return response()->json([
                        'status' => 'error',
                        'message' => $dataValidateOrder['message'],
                    ], 422);
                }
                $saleOrderId = $saleOrder->id;
            } else {
                $saleOrderItemBarcode = SaleOrderItemBarcode::with('order')->where('label_id', $validated['wip_id'])->first();
                if (!$saleOrderItemBarcode) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'WIP ID or order number does not exist in the system.',
                    ], 422);
                }
                $dataValidateOrder = $this->validateOrder($saleOrderItemBarcode->order);
                if (!$dataValidateOrder['status']) {
                    return response()->json([
                        'status' => 'error',
                        'message' => $dataValidateOrder['message'],
                    ], 422);
                }
                if ($saleOrderItemBarcode->is_deleted) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'WIP ID not found.',
                    ], 422);
                }
                $saleOrderId = $saleOrderItemBarcode->order_id;
                $saleOrder = $saleOrderItemBarcode->order;
            }

            $shipment = Shipment::where('order_id', $saleOrderId)
                ->whereNull('refund_status')
                ->whereNotIn('provider', [Shipment::PROVIDER_SHIPSTATION, Shipment::PROVIDER_GELATO, Shipment::PROVIDER_MANUAL])
                ->get();

            //No shipping label yet
            if ($shipment->count() == 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No shipping label yet.',
                ], 422);
            }
            //If multiple tracking numbers are found, the API will return the following error:
            if ($shipment->count() > 1) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Orders with multiple tracking numbers are currently not supported.',
                ], 422);
            }
            $shipmentId = $shipment->first()->id;

            if (Storage::disk('s3')->exists("/label/$shipmentId.pdf")) {
                $shipment->label_pdf_url = $shipmentId . '.pdf';
                $labelUrl = env('AWS_URL') . "/label/{$shipmentId}.pdf?v=" . time();
            } else {
                $this->labelRepository->generateLabel($shipment->first()->id);
                $maxAttempts = 5;
                $attempt = 0;

                $checkLabelExists = function () use (&$checkLabelExists, $shipmentId, &$attempt, $maxAttempts) {
                    $attempt++;
                    if (Storage::disk('s3')->exists("/label/$shipmentId.pdf")) {
                        return true;
                    }
                    if ($attempt >= $maxAttempts) {
                        return false;
                    }
                    usleep(200000); // wait 0.2 seconds before retrying

                    return $checkLabelExists();
                };
                if (!$checkLabelExists()) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Label not found after multiple attempts.',
                    ], 422);
                }
                $labelUrl = env('AWS_URL') . "/label/{$shipmentId}.pdf?v=" . time();
            }

            return response()->json([
                'status' => 'success',
                'order_number' => $saleOrder->order_number,
                'tracking_number' => $shipment->first()->tracking_number,
                'label_url' => $labelUrl,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    private function validateOrder($saleOrder)
    {
        if (!$saleOrder) {
            return [
                'status' => false,
                'message' => 'Order not found.',
            ];
        }

        $ineligibleStatuses = [
            SaleOrder::STATUS_CANCELLED,
            SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
            SaleOrder::STATUS_REJECT,
            SaleOrder::STATUS_ON_HOLD,
        ];
        if (in_array($saleOrder->order_status, $ineligibleStatuses)) {
            return [
                'status' => false,
                'message' => 'Associated order is not eligible (cancelled, rejected, in production cancelled, or on hold).',
            ];
        }

        return [
            'status' => true,
        ];
    }

    public function shippingLabelPrinted(RbtShippingLabelPrintedRequest $request)
    {
        try {
            $validated = $request->validated();
            $orderNumber = $validated['order_number'];
            $employeeCode = $validated['employee_code'];

            $saleOrder = SaleOrder::where('order_number', $orderNumber)->first();

            if (!$saleOrder) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order number does not exist in the system.',
                ], 404);
            }
            $dataValidateOrder = $this->validateOrder($saleOrder);

            if (!$dataValidateOrder['status']) {
                return response()->json([
                    'status' => 'error',
                    'message' => $dataValidateOrder['message'],
                ], 422);
            }
            $shipment = Shipment::where('order_id', $saleOrder->id)
                ->whereNull('refund_status')
                ->whereNotIn('provider', [Shipment::PROVIDER_SHIPSTATION, Shipment::PROVIDER_GELATO, Shipment::PROVIDER_MANUAL])
                ->get();

            if (!$shipment) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No shipment found for this order.',
                ], 404);
            }

            //No shipping label yet
            if ($shipment->count() == 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No shipping label yet.',
                ], 422);
            }
            //If multiple tracking numbers are found, the API will return the following error:
            if ($shipment->count() > 1) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Orders with multiple tracking numbers are currently not supported.',
                ], 422);
            }

            $employee = Employee::where('code', $employeeCode)->where('is_deleted', 0)->first();

            $dataInputLogPrinted = [
                'shipmentId' => $shipment->first()->id,
                'employeeId' => $employee->id,
            ];
            $dataPrinted = $this->labelRepository->logPrintedLabelShipment($dataInputLogPrinted, true);

            return response()->json([
                'status' => 'success',
                'message' => 'Shipping label has been printed.',
                'tracking_number' => $shipment->first()->tracking_number,
                'printed_at' => $dataPrinted['created_at'],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    public function markAsDispatched(RbtDispatchedRequest $request, $orderNumber): JsonResponse
    {
        DB::beginTransaction();

        try {
            $now = now('UTC');
            $data = $request->validated();
            $validWips = [];
            $missingWips = [];
            $validSaleOrderKittingIds = [];

            // lấy barcode theo wip_ids từ payload (cột label_id)
            $saleOrderItemBarcodes = SaleOrderItemBarcode::query()
                ->whereIn('label_id', $data['wip_ids'])
                ->where('is_deleted', SaleOrderItemBarcode::NOT_DELETED)
                ->get();

            // Nếu có nhiều hơn 1 order_id thì lỗi
            $orderIds = $saleOrderItemBarcodes->pluck('order_id')->unique();
            $orderId = $saleOrderItemBarcodes->first()->order_id ?? null;
            $order = SaleOrder::where('order_number', $orderNumber)->first();

            if (!$order) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Order not found with the given order number.',
                ], 422);
            }

            if ($orderIds->count() > 1 || $orderId != $order->id) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Provided WIP IDs do not belong to the same order.',
                ], 422);
            }

            $barcodeIds = $saleOrderItemBarcodes->pluck('id');

            $employee = Employee::where('code', $data['employee_code'])->first();
            if (!$employee) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Employee code is invalid or inactive.',
                ], 422);
            }

            $saleOrderKittings = SaleOrderKitting::query()
                ->with(['itemBarcode' => function ($query) {
                    $query->where('is_deleted', 0);
                }])
                ->whereIn('sale_order_item_barcode_id', $barcodeIds)
                ->get();
            // phân loại valid và invalid WIP dựa vào sự tồn tại của kitting_started_at hoặc kitting_ended_at
            foreach ($saleOrderKittings as $kitting) {
                $itemBarcode = $kitting->itemBarcode;
                // check đã dc kitting hay chưa
                if (is_null($kitting->kitting_started_at) && is_null($kitting->kitting_ended_at)) {
                    if (!in_array($itemBarcode->label_id, haystack: $missingWips)) {
                        $missingWips[] = $itemBarcode->label_id;
                    }

                    continue;
                }

                $validWips[] = $itemBarcode->label_id;
                $validSaleOrderKittingIds[] = $kitting->id;
            }
            if (empty($validWips)) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'No valid WIP ID has been kitted.',
                ], 422);
            }

            // update các kitting đã được valid
            if (!empty($validSaleOrderKittingIds)) {
                SaleOrderKitting::whereIn('id', $validSaleOrderKittingIds)
                    ->update([
                        'employee_id_dispatch' => $employee->id,
                        'kitting_dispatched_at' => $now,
                    ]);
            }
            // get wip chưa kitting
            $WipsNotKitting = SaleOrderItemBarcode::whereNotIn('label_id', $validWips)
                ->where('order_id', $orderId)
                ->where('is_deleted', false)
                ->pluck('label_id');

            if ($WipsNotKitting->isNotEmpty()) {
                $missingWips = collect($missingWips)
                    ->merge($WipsNotKitting)
                    ->unique()
                    ->values()
                    ->toArray();
            }
            // log
            $message = !empty($validWips)
                ? 'Kitting dispatched: Label ID: ' . implode(', ', $validWips) . '.'
                : 'Kitting dispatched: Label ID: NULL.';

            if ($orderId) {
                saleOrderHistory(
                    auth()?->user()?->id ?? null,
                    $employee->id,
                    $orderId,
                    SaleOrderHistory::UPDATE_ORDER_KITTED_TYPE,
                    $message,
                );
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'dispatched_wip_ids' => $validWips,
                'missing_wip_ids' => $missingWips,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('RbtSupportController.markAsDispatched', [$exception]);

            return response()->json([
                'status' => 'error',
                'message' => $exception->getMessage(),
            ], 400);
        }
    }

    public function rbtFetchInventoryOverview(RbtInventoryOverviewRequest $request)
    {
        $input = $request->validated();
        $input['warehouse_id'] = Warehouse::WAREHOUSE_SANJOSE_ID;

        $result = $this->rbtRepository->fetchRbtInventoryOverview($input);

        if ($result->isEmpty()) {
            return response()->json([
                'status' => 'error',
                'message' => 'All provided SKUs are invalid.'
            ], 422);
        }

        return response()->json($result, 200);
    }

    public function generateBatch(GenerateRbtBatchRequest $request)
    {
        $input = $request->validated();
        $input['warehouse_id'] = $request->warehouse_id;

        return $this->rbtRepository->generateBatch($input);
    }

    public function getSKUInLocation(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->input('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID);

        return $this->rbtRepository->getSKUInLocation($input);
    }

    public function darkPodToRack(DarkPodToRackRequest $request)
    {
        $data = $request->validated();
        $data['warehouse_id'] = config('jwt.warehouse_id');

        try {
            $result = $this->rbtRepository->darkPodToRack($data);

            return response()->json([
                'status' => 'success',
                'message' => 'Dark pod moved to rack successfully.',
                'data' => $result,
            ]);
        } catch (Exception $e) {
            Log::error('RbtSupportController.darkPodToRack', [
                'request' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
