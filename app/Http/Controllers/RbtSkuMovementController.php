<?php

namespace App\Http\Controllers;

use App\Http\Requests\PickMovementTaskRequest;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\RbtSkuMovement;
use App\Models\RbtSkuMovementDetail;
use App\Models\RbtSkuMovementImportHistory;
use App\Models\Warehouse;
use App\Repositories\RbtSkuMovementRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class RbtSkuMovementController extends Controller
{
    //
    protected $rbtMovementRepository;

    public function __construct(RbtSkuMovementRepository $rbtMovementRepository)
    {
        $this->rbtMovementRepository = $rbtMovementRepository;
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
        ]);
        $originalFileName = $request->file('file')->getClientOriginalName();

        $path = $request->file('file')->store('uploads');
        $data = array_map('str_getcsv', file(storage_path('app/' . $path)));
        $header = array_map('trim', array_shift($data));
        $listOrigin = [];
        $errorRows = [];
        $validRows = [];

        foreach ($data as $index => $row) {
            $rowData = array_combine($header, $row);
            if ($rowData['Action'] != RbtSkuMovementDetail::ACTION_ADD) {
                continue;
            }
            $listOrigin[] = $rowData['Origin'];
        }
        $existPullingShelves = LocationProduct::select([
            'location.id as location_id',
            'location.barcode as location_barcode',
            'product.id as product_id',
            'product.sku as product_sku',
            'location_product.id as location_product_id'
        ])
            ->join('product', 'product.id', '=', 'location_product.product_id')
            ->join('location', 'location.id', '=', 'location_product.location_id')
            ->where('location.type', Location::PULLING_SHELVES)
            ->whereIn('product.sku', $listOrigin)
            ->where('location.is_deleted', false)
            ->where('product.is_deleted', false)
            ->where('location.warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->orderByDesc('location_product.id') // mới nhất nằm trên
            ->get()
            ->groupBy('product_sku');

        $allDarkPods = Location::select('location.rbt_sku as product_sku', 'location.barcode as location_barcode', 'location.id as location_id', 'product.id as product_id')
            ->leftjoin('location_product', 'location.id', '=', 'location_product.location_id')
            ->leftjoin('product', 'product.sku', '=', 'location.rbt_sku')
            ->where('location.type', Location::MOVING_SHELVES)
            ->where('location.is_deleted', false)
            ->get();

        $existDarkPods = $allDarkPods->pluck('product_sku')->toArray();
        $existTasks = RbtSkuMovementDetail::where('status', '!=', RbtSkuMovementDetail::STATUS_COMPLETED)
            ->pluck('sku')
            ->toArray();

        $allLocation = Location::with('locationProducts')
            ->where('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->where('is_deleted', false)
            ->get();

        $locationBarcode = $allLocation->pluck('barcode')->toArray();

        foreach ($data as $index => $row) {
            $rowData = array_combine($header, $row);
            if (empty(array_filter($rowData, fn ($value) => trim($value) !== ''))) {
                continue;
            }

            $validationResult = $this->rbtMovementRepository->validateRow(
                $rowData, $existDarkPods, $existTasks, $allLocation,
                $locationBarcode, $existPullingShelves, $allDarkPods,
            );
            if (isset($validationResult['errors'])) {
                $errorRows[] = [
                    'row' => $index + 2,
                    'errors' => $validationResult['errors'],
                    'data' => $rowData
                ];
            } elseif (isset($validationResult['data'])) {
                $validRows[] = $validationResult['data'];
            }
        }
        // Ghi file lỗi nếu có
        if (count($errorRows)) {
            $csvString = "Error,Origin,Action,Destination,Rack,Rack type\n";

            foreach ($errorRows as $errorRow) {
                $row = $errorRow['data'];
                $errorCell = '"' . str_replace('"', '""', implode(' | ', $errorRow['errors'])) . '"';

                $csvString .= implode(',', [
                    $errorCell,
                    $row['Origin'] ?? '',
                    $row['Action'] ?? '',
                    $row['Destination'] ?? '',
                    $row['Rack'] ?? '',
                    $row['Rack type'] ?? '',
                ]) . "\n";
            }
            $prefix = 'rbt_sku_movement/error';
            $filename = 'movement_errors_' . time() . '.csv';
            $path = $prefix . '/' . $filename;

            Storage::disk('s3')->put($path, $csvString);

            // Format lại tên file
            $filename = pathinfo($originalFileName, PATHINFO_FILENAME);
            $extension = pathinfo($originalFileName, PATHINFO_EXTENSION);
            $filename = strtolower($filename);
            $filename = preg_replace('/[^a-z0-9]+/', '_', $filename);
            $filename = trim($filename, '_');
            $timestamp = time();
            $formattedFileName = $filename . '_' . $timestamp . '.' . $extension;

            $storedFileName = 'rbt_sku_movement/original/' . $formattedFileName;
            $uploadedFile = $request->file('file');

            Storage::disk('s3')->put($storedFileName, file_get_contents($uploadedFile));

            // Save to history
            RbtSkuMovementImportHistory::create([
                'upload_by' => auth()->user()->id,
                'status' => 'error',
                'file_name' => $originalFileName,
                'link_url_error' => $path,
                'link_url' => $storedFileName,
                'created_at' => now(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'There are errors in the uploaded file. Please check the error file.',
                'error_file_url' => env('AWS_S3_URL', '') . '/' . $path,
                'error_count' => count($errorRows),
            ], 422);
        } if (!empty($validRows)) {
            DB::beginTransaction();
            try {
                // Format lại tên file
                $filename = pathinfo($originalFileName, PATHINFO_FILENAME);
                $extension = pathinfo($originalFileName, PATHINFO_EXTENSION);
                $filename = strtolower($filename);
                $filename = preg_replace('/[^a-z0-9]+/', '_', $filename);
                $filename = trim($filename, '_');
                $timestamp = time();
                $formattedFileName = $filename . '_' . $timestamp . '.' . $extension;

                $storedFileName = 'rbt_sku_movement/success/' . $formattedFileName;
                $uploadedFile = $request->file('file');

                Storage::disk('s3')->put($storedFileName, file_get_contents($uploadedFile));

                $movement = RbtSkuMovement::create([
                    'employee_id' => auth()->user()->id,
                    'link_url' => $storedFileName,
                    'link_url_error' => null,
                ]);

                foreach ($validRows as &$row) {
                    $row['rbt_sku_movement_id'] = $movement->id;
                    $row['created_by'] = auth()->user()->id;

                    if (!array_key_exists('product_id', $row)) {
                        $row['product_id'] = null;
                    }
                    if (!array_key_exists('sku', $row)) {
                        $row['sku'] = null;
                    }
                }
                unset($row);

                RbtSkuMovementDetail::insert($validRows);

                // ✅ Save history when success
                RbtSkuMovementImportHistory::create([
                    'upload_by' => auth()->user()->id,
                    'status' => 'success',
                    'file_name' => $originalFileName,
                    'link_url' => $storedFileName,
                    'link_url_error' => null,
                    'created_by' => auth()->user()->id,
                    'created_at' => now(),
                ]);

                DB::commit();

                return response()->json([
                    'status' => 'success',
                    'message' => 'File uploaded and processed successfully.',
                    'rbt_sku_movement_id' => $movement->id,
                    'valid_count' => count($validRows),
                    'file_url' => env('AWS_S3_URL', '') . '/' . $storedFileName,
                ]);
            } catch (\Exception $e) {
                DB::rollBack();

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to process the file due to internal error.',
                    'error' => $e->getMessage(),
                ], 500);
            }
        }
    }

    public function listTasks(Request $request)
    {
        setTimezone();
        $movements = $this->rbtMovementRepository->listTasks($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $movements,
        ]);
    }

    public function pickTask(PickMovementTaskRequest $request, $taskId)
    {
        try {
            $task = $this->rbtMovementRepository->pickTask($taskId, $request->validated());

            return response()->json([
                'status' => 'success',
                'data' => $task,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function confirmTask(PickMovementTaskRequest $request, $taskId)
    {
        try {
            $input = $request->validated();
            $input['warehouse_id'] = $request->warehouse_id;
            $task = $this->rbtMovementRepository->confirmTask($taskId, $input);

            return response()->json([
                'status' => 'success',
                'data' => $task,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function getCount(Request $request)
    {
        $count = $this->rbtMovementRepository->getCount($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $count,
        ]);
    }

    public function listImports(Request $request)
    {
        $input = $request->all();
        $movements = $this->rbtMovementRepository->listImports($input);

        return response()->json([
            'status' => 'success',
            'data' => $movements,
        ]);
    }

    public function listHistories(Request $request)
    {
        $input = $request->all();
        $histories = $this->rbtMovementRepository->listHistories($input);

        return response()->json([
            'status' => 'success',
            'data' => $histories,
        ]);
    }
}
