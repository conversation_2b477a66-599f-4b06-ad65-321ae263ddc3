<?php

namespace App\Http\Controllers;

use App\Repositories\WorkOrderRepository;
use http\Env\Response;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Validator;


class WorkOrderController extends Controller
{
    protected $workOrderRepository;

    public function __construct(WorkOrderRepository $workOrderRepository)
    {
        $this->workOrderRepository = $workOrderRepository;
    }

    public function fetchAll(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        $data = $this->workOrderRepository->fetchWorkOrder($input);
        return response()->json($data, 200);

    }


    public function pendingWorkOrder(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        $data = $this->workOrderRepository->countPendingWorkOrder($input);
        return response()->json($data, 200);

    }

    public function createWorkOrder(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $validator = Validator::make($request->all(), [
            'employee_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->workOrderRepository->createWorkOrder($input);

    }

    public function assignWorkOrder(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $validator = Validator::make($request->all(), [
            'employee_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        return $this->workOrderRepository->assignWorkOrder( $input['warehouse_id'], $input['employee_id']);
    }

    public function changeWorkOrderItem($id)
    {
        $res = $this->workOrderRepository->changeItem($id);
        if ($res) {
            return response()->json(['status' => true, 'message' => 'Success']);
        }
        return response()->json(['status' => false, 'message' => 'No similar box'], 422);

    }

    public function confirmWorkOrderItem($id)
    {
        $res = $this->workOrderRepository->confirmItem($id);

        if ($res) {
            return response()->json(['status' => true, 'message' => 'Success']);
        }
        return response()->json(['status' => false, 'message' => 'Fail'], 422);

    }

    public function confirmWorkOrder($id, Request $request)
    {
        try {
            $input = $request->all();
            $validator = Validator::make($input, [
                'time_checking_id' => [
                    'required',
                    Rule::exists('time_tracking', 'id')
                ]
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
            $res = $this->workOrderRepository->confirmWorkOrder($id, $input['time_checking_id']);
            if ($res) {
                return response()->json(['status' => true, 'message' => 'Success']);
            }
            return response()->json(['status' => false, 'message' => 'Fail'], 422);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 422);
        }
    }

    public function countSku(Request $request)
    {
        $data = $this->workOrderRepository->countSku($request->warehouse_id);

        return response()->json($data, 200);
    }

    public function getHistory(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        $data = $this->workOrderRepository->getHistory($input);

        return response()->json($data, 200);
    }
}
