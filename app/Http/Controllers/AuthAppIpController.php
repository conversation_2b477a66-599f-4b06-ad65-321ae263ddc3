<?php

namespace App\Http\Controllers;

use App\Http\Requests\AddBypassUsersRequest;
use App\Http\Requests\ManualUpdateIpRequest;
use App\Http\Requests\UpdateIpRestrictionRequest;
use App\Models\AppIps;
use App\Models\AppUsers;
use App\Models\Setting;
use App\Repositories\AppUsersRepository;
use App\Repositories\AuthAppIpRepository;
use Auth;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthAppIpController extends Controller
{
    protected $repository;

    public function __construct(AuthAppIpRepository $repository)
    {
        $this->repository = $repository;
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), array_merge(
            [
                'password' => 'required|string|min:6',
                'username' => 'required|string|max:255',
            ],
        ));

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->authenticate($request);
    }

    public function authenticate($request)
    {
        $credentials = [
            'username' => $request['username'],
            'password' => $request['password'],
        ];
        if (Auth::guard('employee')->attempt($credentials)) {
            $user = Auth::guard('employee')->user();
            if (!$user->is_active) {
                return response()->json(['error' => 'Your account is inactive. Please contact support.'], 401);
            }
        } else {
            return response()->json(['error' => 'Incorrect username or password. Please try again.'], 401);
        }

        $cfConnectingIp = $request->header('CF-Connecting-IP');
        $xForwardedFor = $request->header('X-Forwarded-For');
        // Check ip gửi lên
        $ipAddress = $cfConnectingIp ?: $xForwardedFor;

        $payload = [
            'iss' => 'SwiftPod Authentication Service', // Nguồn gốc phát hành token
            'sub' => $user->id,
            'iat' => time(),
        ];

        $token = JWT::encode($payload, env('JWT_SECRET'), 'HS256');
        $country = $request->header('CF-IPCountry') ?? null;
        $cfConnectingIp = $request->header('CF-Connecting-IP');
        $xForwardedFor = $request->header('X-Forwarded-For');
        $ipAddress = $xForwardedFor ?? '127.0.0.1';
        $input = [
            'ip_address' => $ipAddress,
            'country' => $country,
            'user' => $user->id
        ];

        AppIps::logAccess($input['ip_address'], $input['country'], $input['user']);

        // handleJob(AppIps::JOB_INSERT_LOG_TO_DATABASE, $input);

        return response()->json([
            'access_token' => $token,
            'user' => [
                'user' => $user->username,
                'ip' => $ipAddress ?? '127.0.0.1'
            ],
        ]);
    }

    public function update(Request $request)
    {
        try {
            if (!($token = $request->bearerToken())) {
                throw new Exception('Unauthorized');
            }
            $decoded = JWT::decode($token, new Key(env('JWT_SECRET'), 'HS256'));
            $user = AppUsers::find($decoded->sub);
            if (!$user) {
                throw new Exception('Unauthorized');
            } else {
                $country = $request->header('CF-IPCountry') ?? null;
                $cfConnectingIp = $request->header('CF-Connecting-IP');
                $xForwardedFor = $request->header('X-Forwarded-For');
                $ipAddress = $cfConnectingIp ?: $xForwardedFor ?? '127.0.0.1';
                $input = [
                    'ip_address' => $ipAddress,
                    'country' => $country,
                    'user' => $user->id
                ];
                AppIps::logAccess($input['ip_address'], $input['country'], $input['user']);

                // handleJob(AppIps::JOB_INSERT_LOG_TO_DATABASE, $input);

                return response()->json($input);
            }
        } catch (\Throwable $th) {
            return response()->json([
                'error' => $th->getMessage()
            ], 401);
        }
    }

    public function list(Request $request)
    {
        return response()->json($this->repository->list($request->all()));
    }

    public function countIp()
    {
        return response()->json($this->repository->countIp());
    }

    public function updateIp(Request $request)
    {
        return $this->repository->updateIp($request->all());
    }

    public function reviewIp(Request $request)
    {
        return $this->repository->reviewIp($request->all());
    }

    public function listUsers(Request $request)
    {
        $data = $request->all();
        $collection = AppUsersRepository::getList($data);

        return response()->json($collection);
    }

    public function createUser(Request $request)
    {
        return response()->json($this->repository->createUser($request->all()));
    }

    public function updateUser(Request $request)
    {
        return response()->json($this->repository->updateUser($request->all()));
    }

    public function createIp(Request $request)
    {
        return response()->json($this->repository->createIp($request->all()));
    }

    public function deleteIp($id)
    {
        return response()->json($this->repository->deleteIp($id));
    }

    public function deleteUser($id)
    {
        return response()->json($this->repository->deleteUser($id));
    }

    public function listBypass(Request $request)
    {
        return response()->json($this->repository->listBypass($request->all()));
    }

    public function disableBypass($id)
    {
        return response()->json($this->repository->disableBypass($id));
    }

    public function manualUpdateIp(ManualUpdateIpRequest $request)
    {
        return $this->repository->manualUpdate($request->validated());
    }

    public function addByPassUsers(AddBypassUsersRequest $request)
    {
        return response()->json($this->repository->addByPassUser($request->validated()));
    }

    public function getStatus()
    {
        $status = Setting::where('label', Setting::IP_RESTRICTION)->first();

        return response()->json(['status' => $status->value], 200);
    }

    public function updateIpRestriction(UpdateIpRestrictionRequest $request)
    {
        $updated = $this->repository->updateIpRestriction($request->validated());

        if (!$updated) {
            return response()->json(['error' => 'Setting not found'], 404);
        }

        return response()->json(['message' => 'IP restriction updated successfully'], 200);
    }
}
