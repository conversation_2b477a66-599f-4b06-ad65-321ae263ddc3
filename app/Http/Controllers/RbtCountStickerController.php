<?php

namespace App\Http\Controllers;

use App\Http\Requests\GenerateCountStickerRequest;
use App\Models\Warehouse;
use App\Repositories\RbtCountStickerRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class RbtCountStickerController extends Controller
{
    protected $countStickerRepository;

    public function __construct(RbtCountStickerRepository $countStickerRepository)
    {
        $this->countStickerRepository = $countStickerRepository;
        $this->batch_id = 2;
    }

    public function fetchCountSticker(Request $request)
    {
        try {
            setTimezone();
            $input = request()->all();
            if ($input['warehouse_id'] != Warehouse::WAREHOUSE_SANJOSE_ID) {
                throw new \Exception('Warehouse not allowed', 403);
            }
            $countStickers = $this->countStickerRepository->fetchAll($input);

            return response()->json($countStickers);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ], $e->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function generate(GenerateCountStickerRequest $request)
    {
        $input = $request->validated();
        $input['warehouse_id'] = Warehouse::WAREHOUSE_SANJOSE_ID;

        return $this->countStickerRepository->generate($input);
    }

    public function printed(Request $request)
    {
        $input = $request->all();

        if (!isset($input['batch_id'])) {
            return response()->json([
                'message' => 'Batch not found!',
                'code' => Response::HTTP_UNPROCESSABLE_ENTITY
            ], 422);
        }

        return $this->countStickerRepository->printed($input['batch_id']);
    }
}
