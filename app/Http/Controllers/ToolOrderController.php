<?php

namespace App\Http\Controllers;
use App\Repositories\ToolOrderRepository;
use Illuminate\Http\Request;
use Validator;


class ToolOrderController extends Controller
{
    protected $toolOrderRepository;

    public function __construct(ToolOrderRepository $toolOrderRepository)
    {
        $this->toolOrderRepository = $toolOrderRepository;
    }

    public function syncPurchaseOrder(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            "order_number" => "required_without:invoice_number",
            "invoice_number" => "required_without:order_number",
            "vendor_id" => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
       return $this->toolOrderRepository->sync($input);
    }
}
