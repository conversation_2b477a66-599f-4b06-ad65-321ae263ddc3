<?php

namespace App\Http\Controllers;

use App\Exports\AdditionExport;
use App\Repositories\Contracts\InventoryAdditionRepositoryInterface;
use App\Rules\AdditionPurchaseOrderItemsUnique;
use App\Rules\CheckCountryInAddition;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class InventoryAdditionController extends Controller
{
    protected $inventoryAdditionRepository;

    public function __construct(InventoryAdditionRepositoryInterface $inventoryAdditionRepository)
    {
        $this->inventoryAdditionRepository = $inventoryAdditionRepository;
    }

    public function fetchInventoryAddition(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        $data = $this->inventoryAdditionRepository->fetchAll($input);

        return response()->json($data, 200);
    }

    public function storeInventoryAddition(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        $validator = Validator::make($input, [
            'po_number' => 'required',
            'product_id' => 'required',
            'location_id' => 'required',
            'quantity' => 'required',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'code')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'id_time_checking' => [
                'required',
                Rule::exists('time_tracking', 'id')
            ],
            'country' => [new CheckCountryInAddition],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->inventoryAdditionRepository->create($input);
    }

    public function addInventoryAdditionDistributor(Request $request)
    {
        $input = $request->all();

        $input['warehouse_id'] = $request->warehouse_id;
        $input['user_id'] = auth()->user()->id;

        $validator = Validator::make($input, [
            'po_number' => 'required',
            'invoice_number' => 'required',
            'items' => ['required', new AdditionPurchaseOrderItemsUnique],
            'employee_id' => [
                'required',
                Rule::exists('employee', 'code')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'id_time_checking' => [
                'required',
                Rule::exists('time_tracking', 'id')
            ],
            'country' => [new CheckCountryInAddition],
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->inventoryAdditionRepository->createInventoryAdditionDistributor($input);
    }

    public function revertInventoryAddition(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->inventoryAdditionRepository->revertInventoryAddition($request->id);
    }

    public function exportExcel(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $fileName = 'additions.xlsx';
        if (!empty($input['start_date']) && !empty($input['end_date'])) {
            $fileName = $input['start_date'] == $input['end_date'] ? "additions_{$input['start_date']}.xlsx" : "additions_{$input['start_date']}__{$input['end_date']}.xlsx";
        }

        return Excel::download(new AdditionExport($input), $fileName);
    }
}
