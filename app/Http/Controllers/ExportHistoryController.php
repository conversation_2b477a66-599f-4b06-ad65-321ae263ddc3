<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDownloadHistoryRequest;
use App\Models\ExportHistory;
use App\Repositories\ExportHistoryRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExportHistoryController extends Controller
{
    protected ExportHistoryRepository $repository;

    const PREFIX = 'export_history_folder';

    public function __construct(ExportHistoryRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request)
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;

        if (
            $request->module == ExportHistory::MODULE_TEST_COUNT ||
            $request->module == ExportHistory::MODULE_ADJUST_PULLING_SHELVES ||
            str_starts_with($request->module, ExportHistory::MODULE_INVENTORY_MOVEMENT)
        ) {
            $params['user_id'] = Auth::id();
        }

        $histories = $this->repository->getLatest($params);

        return response()->json([
            'data' => $histories,
        ]);
    }

    public function store(StoreDownloadHistoryRequest $request)
    {
        $validated = $request->validated();
        $dateExport = now('America/Los_Angeles')->format('Ymd_His');
        $fileName = "{$validated['module']}_{$dateExport}.xlsx";
        $filePath = self::PREFIX . "/{$validated['module']}/{$fileName}";
        $history = $this->repository->create([
            'module' => $validated['module'],
            'user_id' => Auth::id(),
            'warehouse_id' => request()->warehouse_id,
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'other_conditions' => $validated['other_conditions'] ?? '',
            'status' => ExportHistory::STATUS_PENDING,
            'file_path' => $filePath,
        ]);

        return response()->json([
            'message' => 'Export log created successfully.',
            'data' => $history,
        ], 201);
    }
}
