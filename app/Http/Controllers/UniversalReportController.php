<?php

namespace App\Http\Controllers;

use App\Http\Requests\UniversalReport\CreateTemplateRequest;
use App\Http\Requests\UniversalReport\DuplicateTemplateRequest;
use App\Http\Requests\UniversalReport\SettingExportRequest;
use App\Http\Requests\UniversalReport\UpdateTemplateRequest;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use App\Repositories\UniversalReportRepository;
use Illuminate\Http\Request;

class UniversalReportController extends Controller
{
    public function __construct(
        protected UniversalReportRepository $universalReportRepository
    ) {
        //
    }

    public function index(Request $request)
    {
        return $this->universalReportRepository->getList($request);
    }

    public function store(CreateTemplateRequest $request)
    {
        return $this->universalReportRepository->create($request->validated());
    }

    public function show(UniversalReportTemplate $template, Request $request)
    {
        return $this->universalReportRepository->show($template, $request);
    }

    public function update(UpdateTemplateRequest $request, UniversalReportTemplate $template)
    {
        return $this->universalReportRepository->update($template, $request->validated());
    }

    public function destroy(UniversalReportTemplate $template)
    {
        return $this->universalReportRepository->delete($template);
    }

    public function duplicate(UniversalReportTemplate $template, DuplicateTemplateRequest $request)
    {
        return $this->universalReportRepository->duplicate($template, $request->validated());
    }

    public function categories(Request $request)
    {
        return $this->universalReportRepository->getCategories($request);
    }

    public function previewTemplate(UniversalReportTemplate $template, SettingExportRequest $request)
    {
        return $this->universalReportRepository->previewTemplate($template, $request->validated());
    }

    public function exportTemplate(UniversalReportTemplate $template)
    {
        return $this->universalReportRepository->exportTemplate($template);
    }

    public function exportHistories(Request $request)
    {
        return $this->universalReportRepository->getExportHistories($request);
    }

    public function revertTemplate(UniversalReportTemplate $template)
    {
        return $this->universalReportRepository->revertTemplate($template);
    }

    public function getTags(UniversalReportTemplate $template, UniversalReportTag $tag, Request $request)
    {
        return $this->universalReportRepository->getTags($template, $tag, $request);
    }

    public function createSettingExport(SettingExportRequest $request, UniversalReportTemplate $template)
    {
        return $this->universalReportRepository->updateOrCreateSettingExport($template, $request->validated());
    }
}
