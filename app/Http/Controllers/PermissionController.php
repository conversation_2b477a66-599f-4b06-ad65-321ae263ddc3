<?php


namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Repositories\PermissionRepository;
use Validator;

class PermissionController extends Controller
{

    private $permissionRepository;

    public function __construct(PermissionRepository $permissionRepository)
    {
        $this->permissionRepository = $permissionRepository;
    }

    public function store(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required|unique:permissions,name',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->permissionRepository->create($input);
    }

    public function update($id, Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'name' => 'required|unique:permissions,name',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return  $this->permissionRepository->edit($input, $id);
    }

    public function destroy($id)
    {
        return $this->permissionRepository->delete($id);
    }

    public function fetchAll(Request $request)
    {
        return $this->permissionRepository->fetchAll();
    }
}
