<?php

namespace App\Http\Controllers;

use App\Exports\PickerStationExport;
use App\Repositories\PickerStationRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PickerStationController extends Controller
{
    protected PickerStationRepository $pickerStationRepository;

    public function __construct(PickerStationRepository $pickerStationRepository)
    {
        $this->pickerStationRepository = $pickerStationRepository;
    }

    public function getOldestOrderItem(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return response()->json($this->pickerStationRepository->getOldestOrderItem($input));
    }

    public function throughput(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['employee_id'] = $request->employee_id;

        return response()->json($this->pickerStationRepository->throughput($input));
    }

    public function getCountWIPWithStatus(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return response()->json($this->pickerStationRepository->getCountWIPWithStatus($input));
    }

    public function getCountWIPByStation(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;

        return response()->json($this->pickerStationRepository->getCountWIPByStation($input));
    }

    public function export(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $input['file_name'] = 'picked_wip_' . now()->timestamp . '.csv';

        return Excel::download(new PickerStationExport($input), $input['file_name'], \Maatwebsite\Excel\Excel::CSV);
    }
}
