<?php

namespace App\Http\Controllers;

use App\Exports\CatalogPricingExport;
use App\Http\Requests\CatalogPricingExportRequest;
use App\Http\Requests\GetColorByStyleRequest;
use App\Http\Requests\GetNewestPoPriceRequest;
use App\Http\Requests\ImportProductVariantCSVRequest;
use App\Http\Requests\ImportProductWeightsCsvRequest;
use App\Http\Requests\StoreProductRequest;
use App\Http\Requests\UpdatePopularOrHideCatalogRequest;
use App\Http\Requests\UploadImageRequest;
use App\Http\Requests\WholesaleRequest;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\Product\ProductModel;
use App\Repositories\Contracts\ProductRepositoryInterface;
use App\Repositories\ProductStyleRepository;
use App\Repositories\PurchaseOrderRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Validator;

class ProductController extends Controller
{
    protected ProductRepositoryInterface $productRepository;

    protected PurchaseOrderRepository $purchaseOrderRepository;

    protected ProductModel $productModel;

    protected Location $location;

    protected LocationProduct $locationProduct;

    protected ProductStyleRepository $productStyleRepository;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        PurchaseOrderRepository $purchaseOrderRepository,
        ProductModel $productModel,
        Location $location,
        LocationProduct $locationProduct,
        ProductStyleRepository $productStyleRepository
    ) {
        $this->productRepository = $productRepository;
        $this->purchaseOrderRepository = $purchaseOrderRepository;
        $this->productModel = $productModel;
        $this->location = $location;
        $this->locationProduct = $locationProduct;
        $this->productStyleRepository = $productStyleRepository;
    }

    public function fetchProduct(Request $request): JsonResponse
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id ?? 0;
        $data = $this->productRepository->fetchAll($params);

        return response()->json($data);
    }

    public function getList(Request $request): JsonResponse
    {
        return response()->json($this->productRepository->getList($request->all()));
    }

    public function getProductListWithAttribute(Request $request)
    {
        return $this->productRepository->getProductListWithAttribute($request);
    }

    public function update(int $id, StoreProductRequest $request)
    {
        $response = $this->productRepository->update($id, $request);
        Artisan::call('qb:sync-product', ['--ids' => $id]);

        return $response;
    }

    public function upload($style, UploadImageRequest $request)
    {
        return $this->productRepository->upload($style, $request);
    }

    public function updateByStyle($style, UpdatePopularOrHideCatalogRequest $request)
    {
        return $this->productRepository->updateByStyle($style, $request->validated());
    }

    public function updateById($id, UpdatePopularOrHideCatalogRequest $request)
    {
        return $this->productRepository->updateById($id, $request->validated());
    }

    public function getProduct($id): JsonResponse
    {
        $product = $this->productRepository->fetch($id);

        return response()->json($product);
    }

    public function getProductByAttribute(Request $request): JsonResponse
    {
        $rules = [
            'style' => 'required',
            'color' => 'required',
            'size' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $product = $this->productRepository->getProductByAttribute($request->all());

        return response()->json($product);
    }

    public function fetchProducts(Request $request): JsonResponse
    {
        $product = $this->productRepository->fetchProducts($request->all());

        return response()->json($product);
    }

    public function fetchProductByAttribute(Request $request): JsonResponse
    {
        $product = $this->productRepository->fetchProductByAttribute($request->all());

        return response()->json($product);
    }

    public function getProductByGTIN(Request $request): JsonResponse
    {
        $product = $this->productRepository->getProductByGTIN($request->all());

        return response()->json($product);
    }

    public function deleteProduct($id)
    {
        exit;

        return $this->productRepository->delete($id);
    }

    public function fetchProductForCreateOrder(Request $request): JsonResponse
    {
        $products = $this->productRepository->fetchProductForCreateOrder($request->all());

        return response()->json($products);
    }

    public function getProductForInventory(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'gtin' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $products = $this->productRepository->getProductByParams($input);

        return response()->json($products);
    }

    public function getProductAttributeList()
    {
        return $this->productRepository->getProductAttributeList();
    }

    public function getPullingShelvesProductQuantity(Request $request)
    {
        return $this->productRepository->getPullingShelvesProductQuantity($request);
    }

    public function getProductBySku(Request $request): JsonResponse
    {
        $rules = ['sku' => 'required'];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $this->productRepository->getProductBySku($request->all());

        if (!$data) {
            return response()->json(['message' => 'Product not found'], 422);
        }

        return response()->json($data);
    }

    public function getProductBySkuOrLabel(Request $request): JsonResponse
    {
        $rules = ['keyword' => 'required'];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $data = $this->productRepository->getProductBySkuOrLabel($request->all());

        if (!$data) {
            return response()->json(['message' => 'Product not found'], 422);
        }

        return response()->json($data);
    }

    public function sendWholesaleInfo(WholesaleRequest $request): JsonResponse
    {
        $params = $request->all();
        $params['storeId'] = Auth::id();
        $params['storeName'] = Auth::user()->name;
        handleJob(Product::JOB_MAIL_WHOLESALE, $params);

        return response()->json('success');
    }

    public function getCatalogDetail(int $id)
    {
        return $this->productRepository->getCatalogDetail($id);
    }

    public function checkExist(Request $request): JsonResponse
    {
        return response()->json($this->productRepository->existsSku($request['sku']));
    }

    public function getInfo(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'sku' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $products = $this->productRepository->getInfo($input);

        return response()->json($products);
    }

    public function create(Request $request): JsonResponse
    {
        $input = $request->all();

        if (count($input) <= 0) {
            return response()->json(['message' => 'Invalid data'], 422);
        }

        $response = $this->productRepository->create($request);

        if (!empty($response['data']['product_ids'])) {
            Artisan::call('qb:sync-product', ['--ids' => implode(',', $response['data']['product_ids'])]);
        }

        return response()->json($response['data'], $response['status']);
    }

    public function existsSkus(Request $request): JsonResponse
    {
        $skus = $request->input('skus', []);

        // Validate the input
        $request->validate(['skus' => 'required|array', 'skus.*' => 'string']);

        // Get the SKUs that exist in the database
        $existingSkus = Product::query()->whereIn('sku', $skus)->pluck('sku')->toArray();

        return response()->json($existingSkus);
    }

    public function getNewestPoPrice(GetNewestPoPriceRequest $request): JsonResponse
    {
        $input = $request->validated();
        $input['warehouse_id'] = config('jwt.warehouse_id');
        $response = $this->purchaseOrderRepository->getNewestPoPriceByProduct($input['gtin'], $input['warehouse_id']);

        return response()->json($response['data'], $response['status']);
    }

    public function validateVariantsCsv(ImportProductVariantCSVRequest $request): JsonResponse
    {
        $response = $this->productRepository->validateVariantsCsv($request->file('file'));

        if (isset($response['status']) && !$response['status']) {
            return response()->json($response, 500);
        }

        return response()->json($response);
    }

    public function importVariants(ImportProductVariantCSVRequest $request): JsonResponse
    {
        $response = $this->productRepository->importVariants($request->file('file'));

        return response()->json($response);
    }

    public function validateWeightsCsv(ImportProductWeightsCsvRequest $request): JsonResponse
    {
        $response = $this->productRepository->validateWeightsCsv($request->file('file'));

        if (isset($response['status']) && !$response['status']) {
            return response()->json($response, 500);
        }

        return response()->json($response);
    }

    public function importWeights(ImportProductWeightsCsvRequest $request): JsonResponse
    {
        $response = $this->productRepository->importWeights($request->file('file'));

        return response()->json($response);
    }

    public function getColorsByStyle(GetColorByStyleRequest $request): JsonResponse
    {
        $response = $this->productRepository->getColorsByStyle($request->validated());

        return response()->json($response);
    }

    public function getProductHistoryBlankCostYear($id)
    {
        $response = $this->productRepository->getProductHistoryBlankCostYear($id);

        return response()->json($response);
    }
    public function fetchStyles($storeId): JsonResponse
    {
        $data = $this->productStyleRepository->getStyleByStore($storeId);

        return response()->json($data);
    }

    public function exportPricing(CatalogPricingExportRequest $request): BinaryFileResponse
    {
        $collection = new CatalogPricingExport($request->validated());

        return Excel::download($collection, 'catalog-' . $request->store_id . '.xlsx');
    }
}
