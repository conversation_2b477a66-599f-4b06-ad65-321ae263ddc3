<?php

namespace App\Http\Controllers;

use App\Http\Requests\SaleReportRequest;
use App\Repositories\ReportRepository;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public $reportRepository;

    public function __construct(ReportRepository $reportRepository)
    {
        $this->reportRepository = $reportRepository;
    }

    public function index(Request $request)
    {
        setTimezone();
        $data = $this->reportRepository->getList($request->all());

        return response()->json($data);
    }

    public function saleReport(SaleReportRequest $request)
    {
        return $this->reportRepository->saleReport($request->all());
    }
}
