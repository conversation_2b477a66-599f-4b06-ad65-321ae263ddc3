<?php

namespace App\Http\Controllers;

use App\Repositories\LavenderRepository;
use Exception;
use Illuminate\Http\Request;

class LavenderController extends Controller
{
    private LavenderRepository $lavenderRepository;

    public function __construct(
        LavenderRepository $lavenderRepository,
    ) {
        $this->lavenderRepository = $lavenderRepository;
    }

    public function rejectDesign(Request $request)
    {
        try {
            $input = $request->all();
            $res = $this->lavenderRepository->rejectDesign($input);

            return \response()->json([
                'status' => true,
                'data' => $res,
                'message' => 'Success'
            ], 200);
        } catch (Exception $e) {
            return \response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function passDesign(Request $request)
    {
        try {
            $input = $request->all();
            $res = $this->lavenderRepository->passDesign($input);

            return \response()->json([
                'status' => true,
                'data' => $res,
                'message' => 'Success'
            ]);
        } catch (Exception $e) {
            return \response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function confirmRejectDesign(Request $request)
    {
        try {
            $input = $request->all();
            $res = $this->lavenderRepository->confirmRejectDesign($input);

            return \response()->json([
                'status' => true,
                'message' => 'Success'
            ], 200);
        } catch (Exception $e) {
            return \response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function confirmPassDesign(Request $request)
    {
        try {
            $input = $request->all();
            $res = $this->lavenderRepository->confirmPassDesign($input);

            return \response()->json([
                'status' => true,
                'data' => $res,
                'message' => 'Success'
            ]);
        } catch (Exception $e) {
            return \response()->json(['message' => $e->getMessage()], 500);
        }
    }
}
