<?php

namespace App\Http\Controllers;

use App\Exports\ManageBoxExport;
use App\Http\Requests\StoreBoxRequest;
use App\Models\Warehouse;
use App\Repositories\Contracts\BoxRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class BoxController extends Controller
{
    protected $boxRepository;

    public function __construct(BoxRepositoryInterface $boxRepository)
    {
        $this->boxRepository = $boxRepository;
    }

    public function fetchBox(Request $request): JsonResponse
    {
        $input = $request->all();
        $vendors = $this->boxRepository->fetchAll($input);

        return response()->json($vendors);
    }

    public function getBox($id)
    {
        $vendor = $this->boxRepository->fetch($id);

        return response()->json($vendor);
    }

    public function storeBox(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $validator = Validator::make($request->all(), [
            'num' => 'required'

        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->boxRepository->create($input);

        return response()->json($res, 201);
    }

    public function createBox(StoreBoxRequest $request)
    {
        return $this->boxRepository->create($request);
    }

    public function updateBox(Request $request, $id)
    {
        $input = $request->all();
        $warehouse_id = $request->warehouse_id;
        $validator = Validator::make($input, [
            'barcode' => ['required',
                Rule::unique('box')->where(function ($query) use ($input, $warehouse_id) {
                    return $query->where('warehouse_id', $warehouse_id)
                        ->where('barcode', $input['barcode']);
                })->ignore($id)]
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->boxRepository->update($id, $input);

        return response()->json($res, 200);
    }

    public function deleteBox($id)
    {
        $data = $this->boxRepository->delete($id);

        return response()->json($data);
    }

    public function importBox(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'barcode' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $input['warehouse_id'] = $request->warehouse_id;

        return $this->boxRepository->bulkInsert($input);
    }

    public function getBoxForInventory(Request $request)
    {
        $input = $request->all();
        $input['warehouse_id'] = $request->warehouse_id;
        $validator = Validator::make($input, [
            'barcode' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $box = $this->boxRepository->getBoxByParams($input);

        return response()->json($box);
    }

    public function getOrderBoxByTrackingNumber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tracking_number' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->boxRepository->getOrderBoxByTrackingNumber($request->tracking_number, $request->warehouse_id);
    }

    public function getBoxByBarcode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'barcode' => 'required|exists:box,barcode',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->boxRepository->getBoxByBarcode($request->barcode);
    }

    public function fetchBoxInRack(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->boxRepository->fetchBoxInRack($request);
    }

    public function generateBoxIDs(Request $request)
    {
        $input = $request->all();
        if (!isset($input['warehouse_id'])) {
            return response()->json(['message' => 'Error warehouse not found!'], 422);
        }
        $generatedIDs = $this->boxRepository->generateBoxIDs($input['warehouse_id']);

        return response()->json(['message' => 'Generate Box Id succesful'], 200);
    }

    public function fetchBarCodePrinted(Request $request)
    {
        $data = $this->boxRepository->ListGenerateBox($request);

        return response()->json($data);
    }

    public function updatePrintStatus(Request $request)
    {
        $input = $request->all();
        if (!isset($input['box_id'])) {
            return response()->json(['message' => 'Error Batch not found!'], 422);
        }
        $box = $this->boxRepository->updatePrintStatusBoxId($input['box_id']);

        return response()->json(['message' => 'Update print status successful'], 200);
    }

    public function searchBox(Request $request, $barcode)
    {
        $listBox = $this->boxRepository->searchBoxByBarcode($barcode, $request->warehouse_id);

        return response()->json($listBox);
    }

    public function exportBox(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required|exists:warehouse,id',
            'type' => 'required|in:product_style,product_sku',
            'keyword' => 'required_if:type,product_sku|exists:product,sku',
            'style' => 'required_if:type,product_style',
            'size' => 'nullable',
            'color' => 'nullable',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $day = Carbon::now()->format('mdY');
        $warehouse = Warehouse::findOrFail($request->input('warehouse_id'));
        $fileName = 'Manage_Boxes_' . $warehouse->code . '_' . $day . '.xlsx';

        $collection = $this->boxRepository->buildCollectionExportBox($request->all());
        if ($collection->isEmpty()) {
            return response()->json(['message' => 'Box not found!'], 422);
        }

        $isAdmin = (bool) $request->input('is_admin', false); // lấy is_admin từ request

        return Excel::download(new ManageBoxExport($collection, $isAdmin), $fileName);
    }

    public function getBoxNeedMovingToRack(Request $request)
    {
        setTimezone();
        $input = $request->all();
        $data = $this->boxRepository->getBoxNeedMovingToRack($input);

        return response()->json($data);
    }
}
