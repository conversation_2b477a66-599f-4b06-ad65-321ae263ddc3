<?php

namespace App\Http\Controllers;

use App\Repositories\Contracts\CountryRepositoryInterface;
use Illuminate\Http\Request;

class CountryController extends Controller
{
   protected $countryRepository;

   public function __construct(CountryRepositoryInterface $countryRepository)
   {
       $this->countryRepository = $countryRepository;
   }

   public function fetchCountry(Request $request)
   {
       $countries = $this->countryRepository->fetchAll($request->all());
       return response()->json($countries, 200);
   }

   public function fetchStateByCountry($country_id)
   {
       $state = $this->countryRepository->fetchStateByCountryId($country_id);
       return response()->json($state, 200);
   }

   public function getCountryPartNumber(Request $request)
   {
       $countries = $this->countryRepository->getCountryPartNumber($request->all());
       return response()->json($countries, 200);
   }

   public function updateCountry($id, Request $request)
   {
       return $this->countryRepository->update($id, $request->all());
   }

    public function validateCodeCountry(Request $request)
    {
        $status = $this->countryRepository->validateCode($request->id, $request->code);
        if (!$status) {
            return response()->json(['status' => false], 422);
        }
        return response()->json(['status' => true], 200);
    }


}
