<?php

namespace App\Http\Service;

use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\ProductSpec;
use App\Models\ProductStyleIccProfile;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\PrintingRepository;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManagerStatic as Image;

ini_set('memory_limit', '8G');

class NeckService
{
    const DPI = 300;

    private PrintingRepository $printingRepository;

    private string $iconPath;

    public function __construct(PrintingRepository $printingRepository)
    {
        $this->printingRepository = $printingRepository;
        $this->iconPath = storage_path('app/public/icon/');
    }

    public function convertDtfNeck($pdfConverted)
    {
        echo 'start pdfConverted_id = ' . $pdfConverted->id . "\n";

        $widthFrame = 4500; // px
        $dpi = 300;
        $quantity = count($pdfConverted->options);
        $dtfPath = storage_path('app/public/dtf/');

        echo 'Step 1: ' . memory_get_usage() . PHP_EOL;

        $pdfItems = PdfConvertedItem::findByPdfConvertedId($pdfConverted->id);

        if (!count($pdfItems)) {
            echo "Empty pdf converted items \n";
            $this->updateConvertFail($pdfConverted);

            return false;
        }

        try {
            if (!is_dir($dtfPath)) {
                mkdir($dtfPath, 0777, true);
            }

            $images = [];
            $convertSuccess = [];
            $convertFail = [];
            $convertPercent = 0;
            $images['width'] = 2.5 * $dpi;
            $images['height'] = 3.5 * $dpi;
            $dataImportHistory = [];
            $maxItemInLine = 5;
            $lineCount = ceil($quantity / $maxItemInLine);
            $marginBottom = 69; //px
            $pageHeight = ($lineCount * 1095) + ($lineCount - 1) * $marginBottom;
            $pageHeight += (186 + 207); // 186 là margin top line item đầu tiên, 207 là margin bottom của line cuối cùng

            foreach ($pdfConverted->options as $key => $option) {
                if ($option['is_deleted'] == 1) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Label has been removed \n";

                    continue;
                }

                $barcode = SaleOrderItemBarcode::findByLabelId($option['label_id']);
                if (!$barcode) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Not found label \n";

                    continue;
                }

                $image = SaleOrderItemImage::with(['imageHash', 'store'])->where('id', $option['image_id'])->first();

                if (!$image) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Not found image \n";

                    continue;
                }

                $orderItem = SaleOrderItem::with(['productColor', 'productSize', 'product.brand'])->where('id', $barcode->order_item_id)->first();

                if (!$orderItem) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Not found sale order item \n";

                    continue;
                }

                $productSpec = ProductSpec::where('product_id', $orderItem->product_id)->first();
                if (!$productSpec) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Product spec not found with productId = $orderItem->product_id \n";
                    if (!isset($dataImportHistory[$orderItem->order_id])) {
                        $dataImportHistory[$orderItem->order_id] = [
                            'user_id' => null,
                            'employee_id' => null,
                            'order_id' => $orderItem->order_id,
                            'type' => SaleOrderHistory::UPDATE_ORDER_CONVERT_NECK_TYPE,
                            'message' => 'Printing neck failed due to a missing fabric content.',
                            'created_at' => Carbon::now()->toDateTimeString()
                        ];
                    }

                    continue;
                }

                $icons = [];
                foreach ($productSpec->getAttributes() as $attr => $value) {
                    if (in_array($attr, ['id', 'product_id', 'fabric_content']) || $productSpec->{$attr} == 0) {
                        continue;
                    }

                    $svg = file_get_contents($this->iconPath . $attr . '.svg');
                    $svg = str_replace('stroke:#000000', 'stroke:' . $orderItem->productColor?->neck_label_color, $svg);
                    $svg = str_replace('fill:#000000', 'fill:' . $orderItem->productColor?->neck_label_color, $svg);
                    $svg = str_replace('stroke="#000000"', 'stroke="' . $orderItem->productColor?->neck_label_color . '"', $svg);
                    $svg = str_replace('fill="#000000"', 'fill="' . $orderItem->productColor?->neck_label_color . '"', $svg);

                    $svg64 = sprintf('data:%s;base64,%s',
                        'image/svg+xml',
                        base64_encode($svg),
                    );

                    $icons[] = $svg64;
                }

                $file = null;
                echo 'Start download image id: ' . $image->id . "\n";
                if ($image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && $image->store?->template_neck != Store::TEMPLATE_NECK_PRINTIFY) {
                    $file = file_get_contents(env('AWS_URL') . '/artwork/' . $image->order_date . '/' . $image->sku . '-' . $image->print_side . '.png');
                } else {
                    if ($image->store?->template_neck != Store::TEMPLATE_NECK_PRINTIFY) {
                        $file = file_get_contents($this->addHttp($image->image_url));
                    } else {
                        if (Str::contains($image->image_url, 'swiftpod.s3.us-west-1.amazonaws.com')) {
                            //Truong hop : update order, sua image url khong dung api gen Neck cua printify
                            echo "image printify: {$image->image_url}\n";
                            $file = file_get_contents($this->addHttp($image->image_url));
                        } else {
                            echo "image printify: {$image->image_url}?country_of_origin={$option['country']} \n";
                            $file = file_get_contents($image->image_url . '?country_of_origin=' . $option['country']);
                        }
                    }
                }

                if (!$file || is_null($file)) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo 'download fail image_id: ' . $image->id . "\n";

                    continue;
                }

                if ($image->store?->template_neck != Store::TEMPLATE_NECK_PRINTIFY) {
                    $fileResize = Image::make($file);
                    $fileResize->resize(750, 600, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                }

                $convertPercent++;
                $convertSuccess[] = [
                    'label_id' => $option['label_id'],
                    'print_side' => $option['side']
                ];
                echo "{$orderItem->sku} | {$orderItem->productColor->neck_label_color} \n";
                $images['info'][] = [
                    'image' => $image->store?->template_neck != Store::TEMPLATE_NECK_PRINTIFY ? (string) $fileResize->encode('data-url') : 'data:image/png;base64, ' . base64_encode($file),
                    'size' => $orderItem->productSize?->name,
                    'icons' => $icons,
                    'rn' => $orderItem->product?->brand?->rn_number,
                    'order' => $option['order'],
                    'fabric' => $productSpec->fabric_content,
                    'country' => $option['country'],
                    'text_color' => $orderItem->productColor->neck_label_color,
                    'width' => $image->store?->template_neck != Store::TEMPLATE_NECK_PRINTIFY ? ($fileResize->width() ?? 750) : 825,
                    'is_printify' => $image->store?->template_neck == Store::TEMPLATE_NECK_PRINTIFY ? true : false
                ];
            }

            if (!array_key_exists('info', $images)) {
                echo "not found items to convert of pdf_converted_id: {$pdfConverted->id} \n";
                $this->updateConvertFail($pdfConverted);

                return false;
            }

            echo 'Step 2: ' . memory_get_usage() . PHP_EOL;

            app()->make('view.engine.resolver')->register('blade', function () {
                return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
            });

            $htmlPath = $dtfPath . $pdfConverted->id . '.html';
            File::put($htmlPath,
                view('neck')
                    ->with([
                        'images' => $images,
                        'width' => $widthFrame,
                        'batch_number' => $pdfConverted->batch_number,
                        'page_height' => $pageHeight,
                    ])
                    ->render(),
            );

            $fileWk = $dtfPath . $pdfConverted->id . '-wk.png';
            $fileIm = $dtfPath . $pdfConverted->id . '-im.png';
            exec("wkhtmltoimage --transparent --format png $htmlPath $fileWk");
            exec("convert $fileWk -set units pixelsperinch -density 300 $fileIm");

            echo 'Step 3: ' . memory_get_usage() . PHP_EOL;

            $s3 = Storage::disk('s3')->put('/dtf/' . $pdfConverted->id . '.png', file_get_contents($fileIm));

            ////Set cho version 2
            $marginBottom = 135; //px
            $pageHeight = ($lineCount * 1050) + ($lineCount - 1) * $marginBottom + 297; // 297 là margin top line item đầu tiên
            $topMarkBottom = $pageHeight + (0.2 * 300);
            $pageHeight += 220;
            $topMark = 60; //px
            $top = 297; //px
            if ($lineCount == 5) {
                $topMarkBottom = 6045; //px
                $pageHeight = 6150; //px
                $topMark = 30; //px
                $top = 237; //px
            }

            echo 'Step 4: make new print file ' . PHP_EOL;

            $imagesReversed = $images;

            $imagesReversed['info'] = array_reverse($images['info']);

            $htmlPathReverse = $dtfPath . $pdfConverted->id . '-reverse.html';
            File::put($htmlPathReverse,
                view('neck_v2')
                    ->with([
                        'images' => $imagesReversed,
                        'width' => $widthFrame,
                        'batch_number' => $pdfConverted->batch_number,
                        'new_print_file' => true,
                        'page_height' => $pageHeight,
                        'top_mark' => $topMark,
                        'top_mark_bottom' => $topMarkBottom,
                        'top' => $top,
                    ])
                    ->render(),
            );

            $fileWkReverse = $dtfPath . $pdfConverted->id . '-wk-reverse.png';
            $fileImReverse = $dtfPath . $pdfConverted->id . '-im-reverse.png';
            exec("wkhtmltoimage --transparent --format png --width 4473 --height $pageHeight $htmlPathReverse $fileWkReverse");
            // exec("wkhtmltoimage --transparent --format png $htmlPathReverse $fileWkReverse");
            exec("convert $fileWkReverse -set units pixelsperinch -density 300 $fileImReverse");
            Storage::disk('s3')->put('/dtf/' . $pdfConverted->id . '-new.png', file_get_contents($fileImReverse));

            $pdfItems = null;

            if (file_exists($htmlPath)) {
                File::delete($htmlPath);
            }

            if (file_exists($fileWk)) {
                File::delete($fileWk);
            }

            if (file_exists($fileIm)) {
                File::delete($fileIm);
            }

            if (file_exists($htmlPathReverse)) {
                File::delete($htmlPathReverse);
            }

            if (file_exists($fileWkReverse)) {
                File::delete($fileWkReverse);
            }

            if (file_exists($fileImReverse)) {
                File::delete($fileImReverse);
            }

            $htmlPath = $fileWk = $fileIm = $htmlPathReverse = $fileWkReverse = $fileImReverse = null;

            DB::beginTransaction();
            if ($s3) {
                $pdfConverted->convert_percent = $convertPercent;
                $pdfConverted->convert_status = PdfConverted::ACTIVE;
                $pdfConverted->convert_at = Carbon::now();
                $pdfConverted->convert_percent = $convertPercent;
                $pdfConverted->quantity = $quantity;
                $pdfConverted->save();

                if (!empty($convertSuccess)) {
                    foreach ($convertSuccess as $success) {
                        PdfConvertedItem::where('label_id', $success['label_id'])
                            ->where('print_side', $success['print_side'])
                            ->where('pdf_converted_id', $pdfConverted->id)
                            ->update([
                                'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_SUCCESS
                            ]);
                    }
                }

                if (!empty($convertFail)) {
                    foreach ($convertFail as $fail) {
                        PdfConvertedItem::where('label_id', $fail['label_id'])
                            ->where('print_side', $fail['print_side'])
                            ->where('pdf_converted_id', $pdfConverted->id)
                            ->update([
                                'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
                            ]);
                    }
                }

                echo 'done ==> pdfConverted_id = ' . $pdfConverted->id . "\n";
            } else {
                echo 'upload to s3 fail pdfConverted_id = ' . $pdfConverted->id . "\n";
            }

            if (!empty($dataImportHistory)) {
                // Lấy các giá trị từ mảng $dataImportHistory
                SaleOrderHistory::insert(array_values($dataImportHistory));
            }

            DB::commit();
            $convertSuccess = $convertFail = $convertPercent = $s3 = $pdfConverted = null;
            echo 'Step 5 : ' . memory_get_usage() . PHP_EOL;
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdfConverted);
        }
    }

    protected function updateConvertFail($pdfConverted)
    {
        PdfConvertedItem::where('pdf_converted_id', $pdfConverted->id)->update([
            'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
        ]);

        $pdfConverted->convert_status = PdfConverted::FAIL;
        $pdfConverted->save();
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }

    public function generateFileNeck($label, $country)
    {
        $labelId = $this->printingRepository->removeLabelSide($label);
        $printSide = $this->printingRepository->getLabelPrintSide($label);
        $printSide = max($printSide, 0);

        $barcode = SaleOrderItemBarcode::findByLabelId($labelId);

        if (!$barcode) {
            return [
                'status' => false,
                'message' => "Label $label not found."
            ];
        }

        $image = SaleOrderItemImage::query()
            ->with(['imageHash', 'store'])
            ->where('order_item_id', $barcode->order_item_id)
            ->where('print_side', $printSide)
            ->first();

        if (!$image) {
            return [
                'status' => false,
                'message' => "Image for label $label not found."
            ];
        }

        $orderItem = SaleOrderItem::query()
            ->with(['productColor', 'productSize', 'product.brand'])
            ->where('id', $barcode->order_item_id)
            ->first();

        if (!$orderItem) {
            return [
                'status' => false,
                'message' => "Item for label $label not found."
            ];
        }

        $productSpec = ProductSpec::query()
            ->where('product_id', $orderItem->product_id)
            ->first();

        if (!$productSpec) {
            return [
                'status' => false,
                'message' => "Product spec for label $label not found."
            ];
        }

        $templateStoreNeck = $image->store->template_neck ?? null;
        $imageName = "{$image->sku}-{$image->print_side}-{$country}.png";
        $printFilePath = "/artwork/neck_converted/{$image->order_date}/{$image->image_hash_id}/{$imageName}";

        if (Storage::disk('s3')->exists($printFilePath)) {
            return [
                'status' => true,
                'file' => Storage::disk('s3')->url($printFilePath)
            ];
        }

        if (!empty($image->icc_converted_at) && $templateStoreNeck != Store::TEMPLATE_NECK_PRINTIFY) {
            $file = file_get_contents(Storage::disk('s3')->url("/artwork/icc/{$image->order_date}/{$image->sku}-{$image->print_side}.png"));
        } else {
            if ($templateStoreNeck != Store::TEMPLATE_NECK_PRINTIFY) {
                $file = file_get_contents($this->addHttp($image->image_url));
            } elseif (Str::contains($image->image_url, 'swiftpod.s3.us-west-1.amazonaws.com')) {
                $file = file_get_contents($this->addHttp($image->image_url));
            } else {
                $file = file_get_contents("{$image->image_url}?country_of_origin={$country}");
            }

            $product = $orderItem->product;
            $outputICCProfile = ProductStyleIccProfile::query()
                ->where('product_style', $product->style)
                ->first();
            $inputProfile = storage_path('app/icc_profile/Color Space Profile.icm');
            $productColor = $orderItem->productColor->icc_color ?? '';

            if ($product && $productColor && $outputICCProfile) {
                $outputProfilePath = match (strtolower($productColor)) {
                    'white' => $outputICCProfile->getAttribute('white_url'),
                    'black' => $outputICCProfile->getAttribute('black_url'),
                    default => $outputICCProfile->getAttribute('colored_url'),
                };

                if (!empty($outputProfilePath)) {
                    try {
                        $file = colorProfileConverter($file, $inputProfile, $outputProfilePath);
                    } catch (\Throwable $throwable) {
                        Log::error('NeckService.generateFileNeck', [$throwable]);

                        return [
                            'status' => false,
                            'message' => $throwable->getMessage(),
                        ];
                    }
                }
            }
        }

        if (empty($file)) {
            return [
                'status' => false,
                'message' => "Failed to download image for label $label."
            ];
        }

        $imageFile = new \Imagick();
        $imageFile->readImageBlob($file);
        $imageFile->setImageFormat('png');
        $maxW = min($imageFile->getImageWidth(), 750);
        $maxH = min($imageFile->getImageWidth(), $templateStoreNeck == Store::TEMPLATE_NECK_PRINTIFY ? 750 : 600);
        $imageFile->resizeImage($maxW, $maxH, \Imagick::FILTER_LANCZOS, 1, true);

        if ($templateStoreNeck == Store::TEMPLATE_NECK_PRINTIFY) {
            $this->trimTop($imageFile);
            $w = $imageFile->getImageWidth();
            $h = $imageFile->getImageHeight();
            $wScale = 2.7 * self::DPI;
            $hScale = $wScale / $w * $h;
            $imageFile->resizeImage($wScale, $hScale, \Imagick::FILTER_LANCZOS, 1, true);

            Storage::disk('s3')->put($printFilePath, $imageFile->getImageBlob());
            $imageFile->clear();
            $imageFile->destroy();

            return [
                'status' => true,
                'file' => Storage::disk('s3')->url($printFilePath),
            ];
        }

        $neckLabelColor = $orderItem->productColor->neck_label_color ?? '';
        $canvasW = 750;
        $canvasH = 1050;
        $canvas = new \Imagick();
        $canvas->newImage($canvasW, $canvasH, new \ImagickPixel('transparent'));
        $canvas->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);
        $canvas->setImageResolution(300, 300);
        $canvas->resampleImage(300, 300, \Imagick::FILTER_UNDEFINED, 1);
        $canvas->setImageFormat('png');

        $lastOffsetTop = 0;
        $left = ($canvasW - $imageFile->getImageWidth()) / 2;
        $canvas->compositeImage($imageFile, \Imagick::COMPOSITE_OVERLAY, $left, $lastOffsetTop);
        $lastOffsetTop = $imageFile->getImageHeight();
        $imageFile->clear();
        $imageFile->destroy();

        $marginTop = 20;
        $fontSize = 80;
        $textColor = new \ImagickPixel($neckLabelColor);
        $draw = new \ImagickDraw();
        $draw->setFontSize($fontSize);
        $draw->setStrokeColor($textColor);
        $draw->setStrokeWidth(3);
        $draw->setFillColor($textColor);
        $draw->setTextAlignment(\Imagick::ALIGN_CENTER);
        $textY = $lastOffsetTop + $fontSize + $marginTop;
        $lastOffsetTop = $textY;
        $canvas->annotateImage($draw, $canvasW / 2, $textY, 0, $orderItem->productSize->name ?? '');

        $marginTop = 15;
        $draw->setFillColor($textColor);
        $draw->setStrokeColor($textColor);
        $draw->setStrokeWidth(3);
        $lastOffsetTop += $marginTop;
        $draw->line(0, $lastOffsetTop, $canvasW, $lastOffsetTop);
        $canvas->drawImage($draw);
        $draw->clear();
        $draw->destroy();

        $icons = [];
        $iconLayerWidth = 0;
        $iconLayerHeight = 0;

        foreach ($productSpec->getAttributes() as $attr => $value) {
            if (in_array($attr, ['id', 'product_id', 'fabric_content']) || empty($productSpec->{$attr})) {
                continue;
            }

            $svg = file_get_contents($this->iconPath . $attr . '.svg');
            $svg = str_replace('stroke:#000000', 'stroke:' . $neckLabelColor, $svg);
            $svg = str_replace('fill:#000000', 'fill:' . $neckLabelColor, $svg);
            $svg = str_replace('stroke="#000000"', 'stroke="' . $neckLabelColor . '"', $svg);
            $svg = str_replace('fill="#000000"', 'fill="' . $neckLabelColor . '"', $svg);

            $icon = new \Imagick();
            $icon->setBackgroundColor(new \ImagickPixel('transparent'));
            $icon->setResolution(300, 300);
            $icon->readImageBlob($svg);
            $icon->setImageFormat('png');
            $icon->resizeImage(min($icon->getImageWidth(), 100), min($icon->getImageHeight(), 100), \Imagick::FILTER_LANCZOS, 1, true);
            $iconLayerWidth += $icon->getImageWidth();
            $iconLayerHeight = max($icon->getImageHeight(), $iconLayerHeight);
            $icons[] = $icon;
        }

        if (!empty($icons)) {
            $iconLayer = new \Imagick();
            $iconLayer->newImage($iconLayerWidth, $iconLayerHeight, new \ImagickPixel('transparent'));
            $iconLayer->setImageFormat('png');
            $left = 0;

            foreach ($icons as $icon) {
                $iconLayer->compositeImage($icon, \Imagick::COMPOSITE_OVER, $left, $iconLayerHeight - $icon->getImageHeight());
                $left += $icon->getImageWidth();
                $icon->clear();
                $icon->destroy();
            }

            $topOffset = $lastOffsetTop + 10;
            $leftOffset = ($canvasW - $iconLayerWidth) / 2;

            $canvas->compositeImage($iconLayer, \Imagick::COMPOSITE_OVER, $leftOffset, $topOffset);
            $lastOffsetTop += $iconLayer->getImageHeight();
            $iconLayer->clear();
            $iconLayer->destroy();
        }

        $textColor = new \ImagickPixel($neckLabelColor);
        $fontSizeSmall = 40;
        $marginTop = 50;
        $textDraw = new \ImagickDraw();
        $textDraw->setStrokeColor($textColor);
        $textDraw->setStrokeWidth(1);
        $textDraw->setFontSize($fontSizeSmall);
        $textDraw->setFillColor($textColor);
        $textDraw->setTextAlignment(\Imagick::ALIGN_CENTER);

        $texts = [
            [
                'margin_top' => $marginTop,
                'txt' => 'RN# ' . ($orderItem->product->brand->rn_number ?? '')
            ],
            [
                'margin_top' => $marginTop,
                'txt' => $productSpec->fabric_content ?? '',
            ],
            [
                'margin_top' => $marginTop,
                'txt' => strtoupper("MADE IN $country"),
            ],
        ];

        $fontSizeSmallChanged = 0;
        $lineWidth = 60;

        preprocessTxt:
        $finalTexts = [];

        foreach ($texts as $item) {
            $metrics = $canvas->queryFontMetrics($textDraw, $item['txt']);

            if ($metrics['textWidth'] > $canvasW && !$fontSizeSmallChanged) {
                $fontSizeSmallChanged = 30;
                $textDraw->setFontSize($fontSizeSmallChanged);
                goto preprocessTxt;
            }

            if ($metrics['textWidth'] > $canvasW) {
                $txtArray = explode(' ', $item['txt']);
                $length = ceil($lineWidth * count($txtArray) / 100);
                $chunks = array_chunk($txtArray, $length);
                $chunks = array_map(function ($txtArr) {
                    return implode(' ', $txtArr);
                }, $chunks);

                foreach ($chunks as $index => $txt) {
                    $metrics = $canvas->queryFontMetrics($textDraw, $txt);

                    if ($metrics['textWidth'] > $canvasW) {
                        $lineWidth -= 10;
                        goto preprocessTxt;
                    } else {
                        $finalTexts[] = [
                            'margin_top' => $index ? $item['margin_top'] - 10 : $item['margin_top'],
                            'txt' => $txt,
                        ];
                    }
                }
            } else {
                $finalTexts[] = $item;
            }
        }

        foreach ($finalTexts as $item) {
            $lastOffsetTop += $item['margin_top'];

            if (!$fontSizeSmallChanged) {
                $lastOffsetTop += 10;
            }

            $canvas->annotateImage($textDraw, $canvasW / 2, $lastOffsetTop, 0, $item['txt']);
        }

        $textDraw->clear();
        $textDraw->destroy();

        $this->trimTop($canvas);

        Storage::disk('s3')->put($printFilePath, $canvas->getImageBlob());
        $canvas->clear();
        $canvas->destroy();

        return [
            'status' => true,
            'file' => Storage::disk('s3')->url($printFilePath)
        ];
    }

    private function trimTop(\Imagick &$imagick): void
    {
        $trimmed = clone $imagick;
        $trimmed->setImageAlphaChannel(\Imagick::ALPHACHANNEL_ACTIVATE);
        $trimmed->trimImage(0);
        $geometry = $trimmed->getImagePage();
        $topTrim = $geometry['y'] ?? 0;

        if ($topTrim > 0) {
            $width = $imagick->getImageWidth();
            $height = $imagick->getImageHeight();

            $imagick->cropImage($width, $height - $topTrim, 0, $topTrim);
            $imagick->setImagePage(0, 0, 0, 0);
        }
    }
}
