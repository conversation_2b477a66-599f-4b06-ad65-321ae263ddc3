<?php

namespace App\Http\Service;

use App\Models\Employee;
use App\Models\PrintMethod;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderInsert;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\Store;
use App\Repositories\BarcodeRepository;
use App\Repositories\SaleOrderChangePrintMethodRepository;
use App\Repositories\SaleOrderRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Koerel\PdfUnite\PdfUnite;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class BarcodeService
{
    public function convertBarcodeV5($id, $view = 'barcode', $isMobile = false): bool
    {
        set_time_limit(0);
        ini_set('memory_limit', '10G');
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        $logError = storage_path('logs/pdf.txt');

        try {
            $barcodeRepository = new BarcodeRepository();
            $barcodePrinted = $barcodeRepository->getBarcodePrintedById($id);
            $items = $barcodeRepository->fetchBarcodeByPrintedID($id);
            echo "id : $id\n";
            $total_item = count($items);

            if ($barcodePrinted->quantity == 0) {
                echo $barcodePrinted->id . " quantity = 0 skip\n";
                $barcodeRepository->updateBarcodePrinted($id, [
                    'convert_status' => 1,
                    'print_status' => 1,
                    'note' => 'skipped',
                    'converted_at' => date('Y-m-d H:i:s')
                ]);

                return false;
            }

            if ($total_item == 0) {
                echo "bug error item == 0\n";

                return false;
            }

            $page = ceil($total_item / 50);
            echo "count: $total_item\n";
            $format_side = [
                0 => 'F',
                1 => 'B',
                2 => 'FB'
            ];
            $render = [];
            $file_number = 1;
            $merge_file = [];
            $path = storage_path('app/public/barcode');
            $first_sku = '';
            $last_sku = '';
            $styleName = null;
            $colorName = null;

            foreach ($items as $key => &$item) {
                $isThankYouCardOrder = SaleOrderInsert::where([
                    'order_id' => $item->order_id,
                    'type' => SaleOrderInsert::THANKYOU_CARD_TYPE
                ])->exists();
                $item->isThankYouCardOrder = $isThankYouCardOrder;
                $item->isHexa = $this->checkHexa($item);
                $styleName = is_null($styleName) ? $item->style_formatted : $styleName;
                $colorName = is_null($colorName) ? $item->color_formatted : $colorName;
                $item->isLargePlaten = $this->checkPlaten16x21($item);
                $item->isCustomPlaten16x18 = $this->checkPlaten16x18($item);
                $item->counter = $this->counter($item);

                if ($key == 0) {
                    $first_sku = $item->sku;

                    if ($total_item == 1) {
                        $last_sku = $item->sku;
                    }
                } elseif ($key == ($total_item - 1)) {
                    $last_sku = $item->sku;
                }

                $item->print_side_formatted = ProductPrintSide::getByArrayCodeWip(str_split($format_side[$item->print_side]), $item->product_style_id);

                // new print area
                if (!is_null($item->print_sides)) {
                    $arraySides = str_split($item->print_sides);
                    $item->print_side_formatted = ProductPrintSide::getByArrayCodeWip($arraySides, $item->product_style_id);
                }

                $isConvertedDtgToDtf = $item->order_quantity == 1 && resolve(SaleOrderChangePrintMethodRepository::class)->isConvertedDtgToDtf($item->order_id);

                if ($isConvertedDtgToDtf) {
                    if (array_key_exists('F', $item->print_side_formatted)) {
                        unset($item->print_side_formatted['F']);
                        $item->print_side_formatted['C'] = 'DTF';
                    }

                    if (array_key_exists('B', $item->print_side_formatted)) {
                        unset($item->print_side_formatted['B']);
                        $item->print_side_formatted['G'] = 'DTF';
                    }
                }

                if ($item->order_date == null) {
                    $item->order_date = date('Y-m-d');
                }

                $item->order_date_formatted = Carbon::parse($item->created_at, 'UTC')->setTimezone('America/Los_Angeles')->format('m/d');
                $item->ink_color_detected_at = Carbon::parse($item->ink_color_detected_at, 'UTC')->setTimezone('America/Los_Angeles')->format('mdH');
                $item->isSingle = false;
                $item->labelIdText = '';

                if ($item->label_id) {
                    $labelIdText = explode('-', $item->label_id);

                    if (!isset($labelIdText[2])) {
                        print_r($item);
                    }

                    if ($labelIdText[2] == 'S') {
                        $item->isSingle = true;
                    }

                    $labelIdText = $labelIdText[3] . '-' . $labelIdText[4];
                    $item->labelIdText = $labelIdText;
                }

                if ($item->account_id == 1) {
                    $qr = QrCode::size(125)->generate($item->label_id);
                    $item->label_2 = null;
                    $item->label_1 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr) . '"  width="125" height="125" />';
                    $item->barcode_qr = $item->label_1;
                } else {
                    $qr = QrCode::size(120)->generate($item->label_id);
                    $item->label_2 = null;
                    $item->label_1 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr) . '"  width="120" height="120" />';
                    $item->barcode_qr = $item->label_1;
                }

                if ($item->print_side == 2) {
                    $qr2 = QrCode::size(120)->generate($item->label_id . '-1');
                    $item->label_2 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr2) . '"  width="120" height="120" />';
                }

                if ($item->print_method == PrintMethod::UV) {
                    $hardGoodAndShirt = resolve(SaleOrderRepository::class)->getHardGoodAndShirt($item->order_id);
                    $item->is_hard_good_and_shirt = count($hardGoodAndShirt) >= 2;
                    $item->shirt_quantity = $item->is_hard_good_and_shirt ? $hardGoodAndShirt[0] : null;
                }

                $render[] = $item;

                if (count($render) == 50 || $key + 1 == $total_item) {
                    app()->make('view.engine.resolver')->register('blade', function () {
                        return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
                    });

                    !empty(env('APP_DEBUG')) && Log::info('BarcodeService.convertBarcodeV5 item render pdf', $render);
                    $pdf = PDF::loadView($view, ['items' => $render])
                        ->setOptions(['dpi' => 203, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => storage_path('logs/')])
                        ->setPaper([0, 0, 2.25 * 72, 1.25 * 72]); // inch to point

                    if ($page == 1) {
                        $save_file = "$path/{$id}.pdf";
                    } else {
                        $save_file = "$path/{$id}-{$file_number}.pdf";
                    }

                    $pdf->setWarnings(true)->save($save_file);
                    echo "page: $file_number/$page\n";
                    $pdf = null;
                    $merge_file[] = $save_file;
                    $barcodeRepository->updateBarcodePrinted($id, ['convert_percent' => $key + 1]);
                    $file_number++;
                    $render = [];
                }
            }

            if (count($merge_file) > 1) {
                $unite = new PdfUnite();
                $merge_file[] = "$path/$id.pdf"; // out put
                $unite->join(...$merge_file);

                foreach ($merge_file as $key => $item) {
                    if ($key + 1 == count($merge_file)) {
                        break;
                    }

                    echo $item . "\n";
                    unlink($item);
                }
            }

            // Add QR Batch ID
            if ($isMobile) {
                $data = [];
                $data['batch_id'] = $barcodePrinted->id;
                $batch = QrCode::size(170)->generate($id);
                $data['batch'] = '<img src="data:image/svg+xml;base64,' . base64_encode($batch) . '"  width="170" height="170" />';
                $time = Carbon::parse($barcodePrinted->created_at)->shiftTimezone('UTC')->setTimezone('America/Los_Angeles');
                $data['date'] = $time->format('Y/m/d');
                $data['time'] = $time->format('H:i:s');
                $data['style'] = $styleName;
                $data['color'] = $colorName;
                $data['quantity'] = $barcodePrinted->quantity;
                $employee = Employee::findById($barcodePrinted->employee_id);
                $data['employee'] = $employee ? $employee->name : null;

                app()->make('view.engine.resolver')->register('blade', function () {
                    return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
                });

                $pdfBatch = PDF::loadView('batch', ['data' => $data])
                    ->setOptions(['dpi' => 203, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => storage_path('logs/')])
                    ->setPaper([0, 0, 2.25 * 72, 1.25 * 72]);
                $saveFileBatch = "$path/{$id}-batch.pdf";
                $pdfBatch->setWarnings(true)->save($saveFileBatch);
                $pdfBatch = null;
                $unite = new PdfUnite();
                $unite->join("$path/$id.pdf", $saveFileBatch, "$path/$id-merge.pdf");
                unlink($saveFileBatch);
            }

            $pathPdf = $isMobile ? "$path/$id-merge.pdf" : "$path/$id.pdf";

            if (file_exists("$path/$id.pdf")) {
                $s3 = Storage::disk('s3')->put("/barcode/$id.pdf", file_get_contents($pathPdf));

                if ($s3) {
                    echo "upload to s3 done $id\n";
                    $barcodeRepository->updateBarcodePrinted($id, [
                        'convert_status' => 1,
                        'last_sku' => $last_sku,
                        'first_sku' => $first_sku,
                        'converted_at' => date('Y-m-d H:i:s')
                    ]);
                    unset($s3);
                    File::delete($pathPdf);
                } else {
                    echo "upload to s3 fail $id\n";
                }
            } else {
                echo "error convert $id\n";
                $barcodeRepository->updateBarcodePrinted($id, [
                    'convert_status' => 2,
                    'converted_at' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Exception $e) {
            if (!empty($barcodeRepository)) {
                $barcodeRepository->updateBarcodePrinted($id, [
                    'convert_status' => 2,
                    'converted_at' => date('Y-m-d H:i:s')
                ]);
            }

            echo $e->getMessage();
            file_put_contents($logError, date('Y-m-d H:i:s'), FILE_APPEND);
            file_put_contents($logError, $e->getMessage(), FILE_APPEND);
            file_put_contents($logError, $e->getTraceAsString(), FILE_APPEND);
        }

        echo "done $id\n";

        return true;
    }

    public function checkPlaten16x21($item): bool
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('custom_platen', '16x21')
            ->first();

        return !empty($image) && $item->store_id != Store::PRINTIFY_API_ID;
    }

    public function checkPlaten16x18($item): bool
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('custom_platen', '16x18')
            ->first();

        return !empty($image);
    }

    public function checkHexa($item): bool
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('is_og', SaleOrderItemImage::IS_OG)
            ->where('is_purple', '!=', SaleOrderItemImage::IS_PURPLE)
            ->first();

        return !empty($image);
    }

    public function counter($item): ?int
    {
        $settingOrderDate = Setting::query()->where('name', 'order_date_counter_wip')->first();
        $saleOrder = SaleOrder::find($item->order_id);
        $orderDate = convertTimeUTCToPST($saleOrder->created_at);
        $startOrderDate = Carbon::parse($settingOrderDate->value . ' 00:00:00', 'America/Los_Angeles');

        if ($startOrderDate->greaterThan($orderDate)) {
            return null;
        } else {
            $diffHour = $startOrderDate->diffInHours($orderDate);
            $result = (intval($diffHour / 6) + 1) % 40;

            return $result == 0 ? 40 : $result;
        }
    }
}
