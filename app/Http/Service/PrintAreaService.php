<?php

namespace App\Http\Service;

use App\Models\ProductPrintSide;

class PrintAreaService
{
    public function mapOptionSaleOrderItem($options, $sku, $order_date, $sides = [])
    {
        $map_options = [];
        foreach ($options as $option) {
            $array_name = explode('.', $option->name);
            if (!empty($array_name[1])) {
                // $code_name = strtolower($array_name[1]);
                $code_name = strtolower(trim(implode('_', preg_split('/(?=[A-Z])/', str_replace(' ', '', $array_name[1]))), '_'));
                $print_side = ProductPrintSide::findByCodeName($code_name);
                $code_names = array_column($map_options, 'name');
                $code = $print_side && isset($print_side->code) ? $print_side->code : '';
                $nameSku = $sku . '-' . $code;
                if (in_array($code_name, $code_names)) {
                    $key = array_search($code_name, $code_names);
                    $map_options[$key] = array_merge($map_options[$key], [
                        $array_name[0] => $option->value,
                        'thumb_250' => env('AWS_URL', '') . '/thumb/250/' . $order_date . '/' . $nameSku . '.png',
                        'order' => $print_side->order ?? null,
                        'artwork' => env('AWS_URL', '') . '/artwork/' . $order_date . '/' . $nameSku . '.png',
                        'print_side' => $code,
                        'is_purple' => empty($sides[$code_name]) ? 0 : $sides[$code_name]
                    ]);
                } else {
                    $map_options[] = [
                        'name' => $code_name,
                        $array_name[0] => $option->value,
                        'artwork' => env('AWS_URL', '') . '/artwork/' . $order_date . '/' . $nameSku . '.png',
                        'print_side' => $code,
                        'order' => null,
                        'is_purple' => empty($sides[$code_name]) ? 0 : $sides[$code_name]
                    ];
                }
            }
        }
        usort($map_options, fn ($a, $b) => strcmp($a['order'], $b['order']));

        return $map_options;
    }

    public function getPrintSideDTGInfo($previewFileOptions, $sku, $orderDate, $dtgPrintSides)
    {
        $printSides = [];
        foreach ($dtgPrintSides as $side) {
            $code = $side->code ?? '';
            $nameSku = $sku . '-' . $code;
            $sideName = str_replace(' ', '', $side->name);
            $previewFile = $previewFileOptions->first(function ($item) use ($sideName) {
                return strtolower($item->name) === strtolower('PreviewFiles.' . $sideName);
            });

            array_push($printSides, [
                'print_side_code' => $side->code_name,
                'print_side_name' => $side->name,
                'preview_file' => $previewFile ? $previewFile->value : null,
                'thumb_250' => env('AWS_URL', '') . '/thumb/250/' . $orderDate . '/' . $nameSku . '.png',
                'order' => $side->order ?? null,
                'print_side' => $code,
            ]);
        }
        usort($printSides, fn ($a, $b) => strcmp($a['order'], $b['order']));

        return $printSides;
    }

    // for print_side of sale_order_item
    public function getCodePrintSide()
    {
        $sides = ProductPrintSide::orderBy('order')->pluck('code_wip', 'code')->toArray();

        return $sides;
    }

    public function getCodeNameBySide($side)
    {
        $print_side = ProductPrintSide::findByCode($side);
        $code_wip = $print_side ? $print_side->code_wip : '';

        return $code_wip;
    }
}
