<?php

namespace App\Http\Service;

use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\PrintingPresetSku;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\UvPresetSku;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

ini_set('memory_limit', '16G');

class StickerService
{
    public function convertSticker($pdfConverted)
    {
        echo 'start pdfConverted_id = ' . $pdfConverted->id . "\n";
        $dpi = 300;

        $product = Product::find($pdfConverted->product_id);
        if (!$product) {
            echo "Not found product \n";
            $this->updateConvertFail($pdfConverted);

            return false;
        }

        $uvPreset = UvPresetSku::where('product_sku', $product->sku)->first();
        if (!$uvPreset) {
            echo "Not define uv preset yet | product sku: {$product->sku} \n";
            $this->updateConvertFail($pdfConverted);

            return false;
        }

        $printingPreset = PrintingPresetSku::where('sku', $product->sku)->first();
        if (!$printingPreset) {
            echo "Not define printing preset yet | product sku: {$product->sku} \n";
            $this->updateConvertFail($pdfConverted);

            return false;
        }

        $pdfItems = PdfConvertedItem::findByPdfConvertedId($pdfConverted->id);

        if ($pdfItems->isEmpty()) {
            echo "Empty pdf converted items \n";
            $this->updateConvertFail($pdfConverted);

            return false;
        }

        echo 'Step 1: ' . memory_get_usage() . PHP_EOL;
        $quantity = count($pdfConverted->options);
        $codeWip = $pdfConverted->code_wip ?? ProductPrintSide::CODE_WIP_FRONT; // những item cũ đều là Front
        $productPrintSide = ProductPrintSide::where('code_wip', $codeWip)->first();
        $codeName = $productPrintSide->code_name;
        $platenSize = explode('x', $printingPreset->{'platen_' . strtolower($codeName) . '_size'});
        $safeZone = explode('x', $printingPreset->{strtolower($codeName) . '_size'});

        $widthItem = ((float) $platenSize[0]) * $dpi;
        $heightItem = ((float) $platenSize[1]) * $dpi;
        $widthSafeZone = ((float) $safeZone[0]) * $dpi;
        $heightSafeZone = ((float) $safeZone[1]) * $dpi;
        $marginLeft = ($widthItem - $widthSafeZone) / 2;
        $marginTop = ($heightItem - $heightSafeZone) / 2;

        $widthFrame = $uvPreset->page_width * $dpi; // px
        $elementSpaceX = $uvPreset->margin_x; // px
        $elementSpaceY = $uvPreset->margin_y; // px
        $maxItemsRow = $uvPreset->max_item_on_row;
        $kissCutSpace = 1 / 8 * $dpi; // px
        $results = [];
        $point = 72;
        $pxToPt = $dpi / $point;
        $stickerPath = storage_path('app/public/sticker/');

        $safeZoneHeightIllus = (float) $safeZone[1] * 72;

        $idConvertSuccess = [];
        $positionIllus = [];
        $positionIllus['id'] = $pdfConverted->id;
        $positionIllus['width_die_cut'] = (float) $safeZone[0] * 72;

        if ($codeWip == ProductPrintSide::CODE_WIP_FRONT) {
            $positionIllus['height_die_cut'] = $safeZoneHeightIllus + 15;
            $positionIllus['print_area'] = ProductPrintSide::PRINT_AREA_FRONT;
        } elseif ($codeWip == ProductPrintSide::CODE_WIP_DIE_CUT) {
            $positionIllus['height_die_cut'] = $safeZoneHeightIllus;
            $positionIllus['print_area'] = ProductPrintSide::PRINT_AREA_DIE_CUT;
        } else {
            $positionIllus['height_die_cut'] = $safeZoneHeightIllus;
            $positionIllus['print_area'] = ProductPrintSide::PRINT_AREA_SQUARE;
        }
        try {
            if (!is_dir($stickerPath)) {
                mkdir($stickerPath, 0777, true);
            }

            $images = [];
            $convertSuccess = [];
            $convertFail = [];
            $convertPercent = 0;

            $row = ceil($quantity / $maxItemsRow);

            foreach ($pdfConverted->options as $key => $option) {
                echo "Start label ID: {$option['label_id']} \n";
                $barcode = SaleOrderItemBarcode::with('saleOrder:id,order_status')->where('label_id', $option['label_id'])->first();
                if (!$barcode) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Not found label \n";

                    continue;
                }

                $image = SaleOrderItemImage::with(['imageHash', 'store'])->where('id', $option['image_id'])->first();
                if (!$image) {
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];
                    echo "Not found image \n";

                    continue;
                }

                $file = null;
                if ($image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS && Storage::disk('s3')->exists("/artwork/{$image->order_date}/{$image->sku}-{$image->print_side}.png")) {
                    $s3_url = env('AWS_URL') . '/artwork/' . $image->order_date . '/' . $image->sku . '-' . $image->print_side . '.png';
                    $file = file_get_contents($s3_url);
                    echo 'download from s3 : ' . $s3_url . "\n";
                } else {
                    $file = file_get_contents($image->image_url);
                    echo 'download from origin : ' . $image->image_url . "\n";
                }

                if (!$file || is_null($file)) {
                    echo 'download fail image_id: ' . $image->id . "\n";
                    $convertFail[] = [
                        'label_id' => $option['label_id'],
                        'print_side' => $option['side']
                    ];

                    continue;
                }

                // optimize image
                $imagick = new \Imagick();
                $imagick->readImageBlob($file);
                $imagick->stripImage();
                $file = $imagick->getImageBlob();

                $rowOfItem = ceil(($key + 1) / $maxItemsRow);
                $top = ($elementSpaceY * 2) * $rowOfItem + $heightItem * ($rowOfItem - 1) + ($rowOfItem * 175);
                $topQR = ($elementSpaceY * 2) * $rowOfItem + $heightItem * ($rowOfItem - 1) + (($rowOfItem - 1) * 175) - 180;

                $left = $elementSpaceX * ($key % $maxItemsRow + 1) + $widthItem * ($key % $maxItemsRow);
                $fileString = 'data:image/png;base64, ' . base64_encode($file);
                if ($codeWip == ProductPrintSide::CODE_WIP_FRONT) {
                    if ($image->image_width > ($widthSafeZone - ($kissCutSpace * 2)) || $image->image_height > ($heightSafeZone - ($kissCutSpace * 2))) {
                        $fileResize = Image::make($file);
                        $fileResize->resize($widthSafeZone - ($kissCutSpace * 2), $heightSafeZone - ($kissCutSpace * 2), function ($constraint) {
                            $constraint->aspectRatio();
                            $constraint->upsize();
                        });
                        $image->image_width = $fileResize->width();
                        $image->image_height = $fileResize->height();
                        $fileString = (string) $fileResize->encode('data-url');
                    }
                } else {
                    if ($image->image_width > $widthSafeZone || $image->image_height > $heightSafeZone) {
                        $fileResize = Image::make($file);
                        $fileResize->resize($widthSafeZone, $heightSafeZone);
                        $image->image_width = $fileResize->width();
                        $image->image_height = $fileResize->height();
                        $fileString = (string) $fileResize->encode('data-url');
                    }
                }
                $qr_label_id = QrCode::size(100)->generate($option['label_id']);
                $images[] = [
                    'image' => $fileString,
                    'width_image' => $image->image_width,
                    'height_image' => $image->image_height,
                    'width_item' => $widthItem,
                    'height_item' => $heightItem,
                    'width_safe_zone' => $widthSafeZone,
                    'height_safe_zone' => $heightSafeZone,
                    'top' => $top,
                    'left' => $left,
                    'top_qr' => $topQR,
                    'qrCode' => '<img src="data:image/svg+xml;base64,' . base64_encode($qr_label_id) . '"  width="150" height="150" />',
                    'label' => $option['label_id'],
                    'itemNumber' => $barcode->barcode_number . '/' . $barcode->order_quantity,
                ];
                $convertPercent++;
                $convertSuccess[] = [
                    'label_id' => $option['label_id'],
                    'print_side' => $option['side']
                ];

                $positionIllus['position'][] = [
                    'top' => ($top + $marginTop) / $pxToPt,
                    'left' => ($left + $marginLeft) / $pxToPt
                ];

                if ($barcode->saleOrder->order_status == SaleOrder::STATUS_NEW_ORDER) {
                    $idConvertSuccess[] = $barcode->order_id;
                }
            }

            app()->make('view.engine.resolver')->register('blade', function () {
                return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
            });

            $htmlPath = $stickerPath . 'result.html';

            //dd($images);
            File::put($htmlPath,
                view('sticker')
                    ->with(['images' => $images, 'widthFrame' => $widthFrame, 'marginTop' => $marginTop, 'marginLeft' => $marginLeft])
                    ->render(),
            );

            $fileWk = $stickerPath . 'wk.png';
            $fileIm = $stickerPath . 'im.png';
            exec("wkhtmltoimage --transparent --format png $htmlPath $fileWk");
            exec("convert $fileWk -set units pixelsperinch -density 300 $fileIm");

            echo 'Step 3: ' . memory_get_usage() . PHP_EOL;

            $s3 = Storage::disk('s3')->put('/sticker/' . $pdfConverted->id . '.png', file_get_contents($fileIm));

            $heightFrame = getimagesize($fileIm)[1] / $pxToPt;
            $positionIllus['width'] = $widthFrame / $pxToPt;
            $positionIllus['height'] = $heightFrame + 15;

            $pdfItems = null;

            if (file_exists($htmlPath)) {
                File::delete($htmlPath);
            }

            if (file_exists($fileWk)) {
                File::delete($fileWk);
            }

            if (file_exists($fileIm)) {
                File::delete($fileIm);
            }

            $htmlPath = $fileWk = $fileIm = null;
            // Storage::disk('public')->put('sticker/result.png', file_get_contents($fileIm));
            // Storage::disk('public')->delete('sticker/result.html');
            // Storage::disk('public')->delete('sticker/wk.png');
            // Storage::disk('public')->delete('sticker/im.png');

            DB::beginTransaction();
            if ($s3) {
                $pdfConverted->convert_percent = $convertPercent;
                $pdfConverted->convert_status = PdfConverted::ACTIVE;
                $pdfConverted->convert_at = Carbon::now();
                $pdfConverted->convert_percent = $convertPercent;
                $pdfConverted->quantity = $quantity;
                $pdfConverted->position_illustrator = $positionIllus;
                $pdfConverted->save();

                if (!empty($idConvertSuccess)) {
                    SaleOrder::whereIn('id', array_unique($idConvertSuccess))
                        ->where('order_status', SaleOrder::STATUS_NEW_ORDER)
                        ->update(
                            [
                                'order_status' => SaleOrder::IN_PRODUCTION,
                                'order_production_at' => Carbon::now()
                            ],
                        );
                    handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, array_unique($idConvertSuccess));
                }

                if (!empty($convertSuccess)) {
                    foreach ($convertSuccess as $success) {
                        PdfConvertedItem::where('label_id', $success['label_id'])
                            ->where('print_side', $success['print_side'])
                            ->where('pdf_converted_id', $pdfConverted->id)
                            ->update([
                                'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_SUCCESS
                            ]);
                    }
                }

                if (!empty($convertFail)) {
                    foreach ($convertFail as $fail) {
                        PdfConvertedItem::where('label_id', $fail['label_id'])
                            ->where('print_side', $fail['print_side'])
                            ->where('pdf_converted_id', $pdfConverted->id)
                            ->update([
                                'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
                            ]);
                    }
                }

                echo 'done ==> pdfConverted_id = ' . $pdfConverted->id . "\n";
            } else {
                echo 'upload to s3 fail pdfConverted_id = ' . $pdfConverted->id . "\n";
            }
            DB::commit();
            $convertSuccess = $convertFail = $convertPercent = $s3 = $pdfConverted = null;
            echo 'Step 4: ' . memory_get_usage() . PHP_EOL;
        } catch (Exception $e) {
            DB::rollBack();
            echo 'error ==> ' . $e->getMessage() . "\n";
            $this->updateConvertFail($pdfConverted);
        }
    }

    protected function updateConvertFail($pdfConverted)
    {
        PdfConvertedItem::where('pdf_converted_id', $pdfConverted->id)->update([
            'convert_status' => SaleOrderItemBarcode::PDF_CONVERT_FAIL
        ]);

        $pdfConverted->convert_status = PdfConverted::FAIL;
        $pdfConverted->save();
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }
}
