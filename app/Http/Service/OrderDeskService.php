<?php

namespace App\Http\Service;

use GuzzleHttp\Client;

class OrderDeskService
{
    private string $urlOrderDesk = 'https://app.orderdesk.me/api/v2/';

    private $client;

    const PREFIX_ORDER_SHIP_STATION = 'OD';

    public function __construct()
    {
        $this->client = new Client();
    }

    public function syncTrackingOrder($orderId, $shipment)
    {
        if (str_starts_with($orderId, OrderDeskService::PREFIX_ORDER_SHIP_STATION)) {
            $orderId = substr($orderId, 2);
        }

        return $this->client->request(
            'POST',
            $this->urlOrderDesk . 'orders/' . $orderId . '/shipments',
            [
                'timeout' => 30,
                'connect_timeout' => 5,
                'headers' => [
                    'ORDERDESK-STORE-ID' => $shipment->saleOrder->store->order_desk_store_id,
                    'ORDERDESK-API-KEY' => $shipment->saleOrder->store->order_desk_api_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'tracking_number' => $shipment->tracking_number,
                    'carrier_code' => $shipment->shippingCarrier?->name,
                    'shipment_method' => $shipment->service_code,
                    'tracking_url' => str_replace('{tracking_code}', $shipment->tracking_number, $shipment->shippingCarrier->tracking_url)
                ]),
            ],
        );
    }

    public function getDetailShipment($externalKey, $store): \Psr\Http\Message\ResponseInterface
    {
        if (str_starts_with($externalKey, OrderDeskService::PREFIX_ORDER_SHIP_STATION)) {
            $externalKey = substr($externalKey, 2);
        }

        return $this->client->request(
            'GET',
            $this->urlOrderDesk . 'orders/' . $externalKey . '/shipments',
            [
                'timeout' => 30,
                'connect_timeout' => 5,
                'headers' => [
                    'ORDERDESK-STORE-ID' => $store->order_desk_store_id,
                    'ORDERDESK-API-KEY' => $store->order_desk_api_key,
                    'Content-Type' => 'application/json'
                ]
            ],
        );
    }

    public function updateDetailOrder($externalKey, $store, $shipment, $shipmentId): \Psr\Http\Message\ResponseInterface
    {
        if (str_starts_with($externalKey, OrderDeskService::PREFIX_ORDER_SHIP_STATION)) {
            $externalKey = substr($externalKey, 2);
        }

        return $this->client->request(
            'PUT',
            $this->urlOrderDesk . 'orders/' . $externalKey . '/shipments/' . $shipmentId,
            [
                'timeout' => 30,
                'connect_timeout' => 5,
                'headers' => [
                    'ORDERDESK-STORE-ID' => $store->order_desk_store_id,
                    'ORDERDESK-API-KEY' => $store->order_desk_api_key,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode([
                    'tracking_number' => $shipment->tracking_number ?? '',
                    'carrier_code' => $shipment->shippingCarrier?->name ?? '',
                    'shipment_method' => $shipment->service_code ?? '',
                    'tracking_url' => str_replace('{tracking_code}', $shipment->tracking_number, $shipment->shippingCarrier->tracking_url)
                ]),
            ],
        );
    }
}
