<?php

namespace App\Http\Middleware;

use App\Models\Employee;
use Closure;
use Illuminate\Http\Request;

class EmployeeAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (empty($request->employee_id)) {
                throw new \Exception('Missing employee');
            }
            $employee = Employee::where('id', $request->employee_id)->where('is_deleted', false)->first();
            if (empty($employee)) {
                throw new \Exception('Employee not found');
            }

            return $next($request);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'status' => false
            ], 401);
        }
    }
}
