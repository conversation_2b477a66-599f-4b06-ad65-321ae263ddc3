<?php

namespace App\Http\Middleware;

use App\Models\EmbroideryUser;
use Closure;
use Exception;
use Illuminate\Http\Request;

class AuthenticateEmbroidery
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (!($token = $request->bearerToken()))
                throw new Exception('Unauthorized');

            $tokenDecrypt = decryptSalt($token);
            [$userId, $createdTime] = explode(':', $tokenDecrypt);
            $user = EmbroideryUser::whereId($userId)->where('is_active', true)->first();

            if (!$user)
                throw new Exception('Unauthorized');

            auth()->setUser($user);
        } catch (\Throwable $th) {
            return response()->json([
                'error' => $th->getMessage()
            ], 401);
        }

        return $next($request);
    }
}
