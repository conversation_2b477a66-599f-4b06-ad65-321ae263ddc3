<?php

namespace App\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\JWTAuth;

class JwtMiddleware
{
    protected $auth;

    protected $except = ['select_warehouse', 'auth_userProfile'];

    public function __construct(JWTAuth $auth)
    {
        $this->auth = $auth;
    }

    public function handle(Request $request, Closure $next)
    {
        try {
            if (!$this->auth->parser()->setRequest($request)->hasToken()) {
                return response()->json([
                    'jwt-auth' => 'Token not provided'
                ], 401);
            }

            if (!$this->auth->parseToken()->authenticate()) {
                return response()->json([
                    'jwt-auth' => 'User not found'
                ], 401);
            }
            $user = $this->auth->user();
            $issuedAt = Carbon::createFromTimestamp($this->auth->getClaim('iat'));
            if ($user->expired_at && $issuedAt->isBefore($user->expired_at)) {
                return response()->json([
                    'jwt-auth' => 'User not active'
                ], 401);
            }
            if (!in_array($request->route()->getName(), $this->except)) {
                $payload = \Tymon\JWTAuth\Facades\JWTAuth::parseToken()->getPayload();
                $warehouse = $payload->get('warehouse');
                if (!isset($warehouse)) {
                    return response()->json([
                        'jwt-auth' => 'Warehouse is empty'
                    ], 403);
                }
                $warehouse = (array) $warehouse;
                $request->warehouse_id = $warehouse['id'];
                config(['jwt.warehouse_id' => $warehouse['id']]);
            }
        } catch (JWTException $e) {
            return response()->json([
                'jwt-auth' => $e->getMessage()
            ], 401);
        }

        return $next($request);
    }
}
