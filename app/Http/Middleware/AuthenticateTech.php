<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use <PERSON>mon\JWTAuth\JWTAuth;

class AuthenticateTech
{
    public function __construct(protected JWTAuth $auth)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $user = $this->auth->user();
            if ($user->email != '<EMAIL>') {
                throw new Exception('Unauthorized');
            }
        } catch (\Throwable $th) {
            return response()->json([
                'error' => $th->getMessage()
            ], 401);
        }

        return $next($request);
    }
}
