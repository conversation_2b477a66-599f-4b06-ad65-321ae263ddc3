<?php

namespace App\Listeners;

use App\Models\Setting;
use App\Models\WalletReceipt;
use App\Models\WalletTopup;
use App\Models\WalletTopupLog;
use App\Models\WalletTransaction;
use App\Repositories\TransactionRepository;
use App\Repositories\WalletRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Cashier;
use Stripe\Event;

class StripeEventListener
{
    protected TransactionRepository $transactionRepository;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(TransactionRepository $transactionRepository)
    {
        $this->transactionRepository = $transactionRepository;
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        switch ($event->payload['type']) {
            case Event::PAYMENT_INTENT_SUCCEEDED:
                $this->handleInvoicePaymentIntentSuccess($event->payload);
                break;

            case Event::PAYMENT_INTENT_REQUIRES_ACTION:
                $this->handleInvoicePaymentIntentRequiresAction($event->payload);
                break;

            case Event::PAYMENT_INTENT_PAYMENT_FAILED:
                $this->handleInvoicePaymentIntentFail($event->payload);
                break;
        }
    }

    protected function handleInvoicePaymentIntentSuccess(array $payload)
    {
        $data = $payload['data']['object'];
        $store = Cashier::findBillable($data['customer']);

        if (!$store) {
            return;
        }

        $store->load('client');
        DB::beginTransaction();
        try {
            Log::channel('stripe')->info('Payment Success By store: ' . $store->id);
            $meta = $data['metadata'];
            $topup = WalletTopup::where('signature', $meta['signature'])
                ->where('store_id', $store->id)
                ->where('status', WalletTopup::STATUS_PENDING)
                ->where('amount_requested', floatval($meta['requested_amount']))
                ->firstOrFail();
            $topup->update([
                'status' => WalletTopup::STATUS_APPROVED,
                'reference_number' => $data['id'],
            ]);
            WalletTopupLog::create([
                'wallet_topup_id' => $topup->id,
                'data' => json_encode($payload),
                'action' => WalletTopupLog::RESPONSE_ACTION,
            ]);

            if ($topup->is_refill) {
                $store->autoRefill->update([
                    'reason' => null
                ]);
            }

            $walletRepository = resolve(WalletRepository::class);
            $transaction = WalletTransaction::where('object_id', $topup->id)->where('object_type', WalletTransaction::TRANSACTION_TYPE_TOPUP)->first();

            if (!$transaction) {
                $newBalance = $walletRepository->update(WalletTransaction::TRANSACTION_TYPE_TOPUP, $topup->amount, $store->id);
                $transaction = WalletTransaction::create([
                    'store_id' => $store->id,
                    'amount' => $topup->amount,
                    'new_balance' => $newBalance,
                    'direction' => WalletTransaction::DIRECTION_IN,
                    'type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_id' => $topup->id,
                    'object_type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_number' => $topup->topup_number,
                    'description' => 'Topup #' . $topup->topup_number,
                    'note' => 'Top up successful',
                ]);
            } else {
                $transaction->note = 'Top up successful';
                $transaction->save();
            }

            WalletReceipt::create([
                'store_id' => $store->id,
                'order_id' => null,
                'transaction_id' => $transaction->id,
                'billing_address_id' => $store->billing_address_id,
                'amount' => $topup->amount,
                'receipt_number' => getSequenceNumber('receipt', $store->id),
            ]);
            DB::commit();
            Log::channel('stripe')->info('Topup Success For store: ' . $store->id);
            handleJob(WalletTopup::SEND_EMAIL_TOPUP, $topup->id);
            $this->sendMessageToGoogleChat($topup, $store);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('stripe')->error('StripeEventListener.handleInvoicePaymentIntentSuccess catch', [$th]);
        }
    }

    protected function handleInvoicePaymentIntentRequiresAction(array $payload)
    {
        $data = $payload['data']['object'];
        $store = Cashier::findBillable($data['customer']);

        if (!$store) {
            return;
        }

        DB::beginTransaction();
        try {
            Log::channel('stripe')->info('Payment Requires Action By store: ' . $store->id);
            $meta = $data['metadata'];
            $topup = WalletTopup::where('signature', $meta['signature'])
                ->where('store_id', $store->id)
                ->where('status', WalletTopup::STATUS_PENDING)
                ->firstOrFail();
            $topup->update([
                'reference_number' => $data['id'],
            ]);
            WalletTopupLog::create([
                'wallet_topup_id' => $topup->id,
                'data' => json_encode($payload),
                'action' => WalletTopupLog::RESPONSE_ACTION,
            ]);

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('stripe')->error('StripeEventListener.handleInvoicePaymentIntentRequiresAction catch', [$th]);
        }
    }

    protected function handleInvoicePaymentIntentFail(array $payload)
    {
        $data = $payload['data']['object'] ?? [];
        $paymentError = $data['last_payment_error'] ?? [];
        $store = Cashier::findBillable($data['customer']);

        if (!$store) {
            return;
        }

        $store->load('client');
        DB::beginTransaction();
        try {
            Log::channel('stripe')->info('Payment Failed By Store: ' . $store->id);
            $meta = $data['metadata'];

            $topup = WalletTopup::where('signature', $meta['signature'])
                ->where('store_id', $store->id)
                ->where('status', WalletTopup::STATUS_PENDING)
                ->firstOrFail();

            $transaction = WalletTransaction::where('object_id', $topup->id)->where('object_type', WalletTransaction::TRANSACTION_TYPE_TOPUP)->first();
            if ($transaction) {
                $walletRepository = app(WalletRepository::class);
                $amount = -abs($transaction->amount);
                $newBalance = $walletRepository->update(WalletTransaction::TRANSACTION_TYPE_TOPUP, $amount, $topup->store_id);
                WalletTransaction::create([
                    'store_id' => $store->id,
                    'amount' => $amount,
                    'new_balance' => $newBalance,
                    'direction' => WalletTransaction::DIRECTION_IN,
                    'type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_id' => $topup->id,
                    'object_type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_number' => $topup->topup_number,
                    'description' => 'Topup #' . $topup->topup_number,
                    'note' => 'Top up failed',
                ]);
            }

            $topup->update([
                'status' => WalletTopup::STATUS_FAILED,
                'reference_number' => $data['id'] ?? null,
                'note' => $paymentError['message'] ?? 'Top up failed',
            ]);
            WalletTopupLog::create([
                'wallet_topup_id' => $topup->id,
                'data' => json_encode($payload),
                'action' => WalletTopupLog::RESPONSE_ACTION,
            ]);

            if ($topup->is_refill) {
                $store->autoRefill->update([
                    'status' => false,
                    'reason' => [
                        'topup' => [
                            'signature' => $topup->signature,
                            'amount_requested' => $topup->amount_requested,
                        ],
                        'payment_method_data' => $topup->payment_method_data,
                    ]
                ]);
            }

            DB::commit();
            handleJob(WalletTopup::SEND_EMAIL_TOPUP, $topup->id);
            $this->sendMessageToGoogleChat($topup, $store);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::channel('stripe')->error('StripeEventListener.handleInvoicePaymentIntentFail catch', [$th]);
        }
    }

    public function sendMessageToGoogleChat($topup, $store)
    {
        $storeName = $store->name ?? '';
        $clientName = $store->client ? $store->client->name : '';
        $paymentMethod = $topup->payment_method_data ?? null;
        $method = null;

        if ($paymentMethod && $paymentMethod['type'] == WalletTopup::CARD_METHOD) {
            $method = $paymentMethod['card']['brand'] . '****' . $paymentMethod['card']['last4'];
        }

        if ($paymentMethod && $paymentMethod['type'] == WalletTopup::BANK_TRANSFER_METHOD) {
            $method = 'Bank Account' . '****' . $paymentMethod['us_bank_account']['last4'];
        }

        $message = "Top up successful #{$topup->topup_number} \n"
            . "Store: {$storeName} - Client: {$clientName} \n"
            . "Amount: {$topup->amount_requested} \n"
            . "Method: {$method} \n"
            . "Deposited: {$topup->amount} \n";

        if ($topup->status == WalletTopup::STATUS_FAILED) {
            $message = "Top up failed #{$topup->topup_number} \n"
                . "Store: {$storeName} - Client: {$clientName} \n"
                . "Amount: {$topup->amount_requested} \n"
                . "Method: {$method} \n";
        }

        $url = Setting::where('name', Setting::TOPUP_ALERT_GOOGLE_SPACE)->first()->value ?? '';
        sendGoogleChat($message, $url);

        return true;
    }
}
