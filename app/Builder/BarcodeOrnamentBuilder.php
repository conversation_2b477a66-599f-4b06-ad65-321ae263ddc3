<?php

namespace App\Builder;

use App\Models\PrintMethod;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Repositories\BarcodeRepository;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class BarcodeOrnamentBuilder
{
    protected Builder|null $query = null;

    private function buildQuery($args): BarcodeOrnamentBuilder
    {
        if (!empty($args['start']) || !empty($args['end'])) {
            setTimezone();
        }

        $this->query = DB::table('sale_order_item_barcode')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('sale_order', 'sale_order.id', 'sale_order_item.order_id')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->where('sale_order.is_test', 0)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('product_style.print_method', PrintMethod::UV)
            ->whereNotIn('product_style.type', ['Poster', 'Sticker'])
            ->where('sale_order_item_barcode.retry_convert', '<', SaleOrderItemBarcode::MAX_RETRY)
            ->where('product.parent_id', '<>', 0);
        resolve(BarcodeRepository::class)->optimizeFilter($this->query);

        if (!empty($args['warehouse_id'])) {
            $this->query->where('sale_order_item_barcode.warehouse_id', $args['warehouse_id']);
        }

        if (!empty($args['start'])) {
            $this->query->where('sale_order.created_at', '>=', Carbon::parse($args['start'])->startOfDay()->format('Y-m-d H:i:s'));
        }

        if (!empty($args['end'])) {
            $this->query->where('sale_order.created_at', '<=', Carbon::parse($args['end'])->endOfDay()->format('Y-m-d H:i:s'));
        }

        if (!empty($args['product_id'])) {
            $this->query->where('sale_order_item.product_id', $args['product_id']);
        }

        if (!empty($args['priorityStores'])) {
            if (!empty($args['store_id']) && in_array($args['store_id'], $args['priorityStores'])) {
                $this->query->where('sale_order_item_barcode.store_id', $args['store_id']);
            } else {
                $this->query->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        if (!empty($args['inPriorityStores'])) {
            $this->query->whereIn('sale_order_item_barcode.store_id', $args['inPriorityStores']);
        }

        return $this;
    }

    public function getQuery(): Builder
    {
        return $this->query;
    }

    private function hasTiktok(): BarcodeOrnamentBuilder
    {
        $this->query->where(function ($q) {
            $q->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('product_style.type', 'Ornament');
        });

        return $this;
    }

    private function hasBulk(): BarcodeOrnamentBuilder
    {
        $this->query->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
            ->where('product_style.type', 'Ornament');

        return $this;
    }

    private function excludeBulkOrder()
    {
        $this->query->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere('product_style.type', '<>', 'Ornament');
        });

        return $this;
    }

    private function hasReprint()
    {
        $this->query->whereNotNull('sale_order_item_barcode.label_root_id')
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('product_style.type', 'Ornament');

        return $this;
    }

    private function excludeReprint()
    {
        $this->query->where(function ($q) {
            $q->whereNull('sale_order_item_barcode.label_root_id')
                ->orWhere('product_style.type', '<>', 'Ornament')
                ->orWhere(function ($subCondition) {
                    $subCondition->whereNotNull('sale_order_item_barcode.label_root_id')
                        ->whereNotNull('sale_order_item_barcode.employee_reroute_id');
                });
        });

        return $this;
    }

    private function excludeTiktok(): BarcodeOrnamentBuilder
    {
        $this->query->where(function ($q) {
            $q->where('product_style.type', '<>', 'Ornament')
                ->orWhere(function ($subQ) {
                    $subQ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                        ->where('product_style.type', 'Ornament');
                });
        });

        return $this;
    }

    public function selectPendingTiktok($args): BarcodeOrnamentBuilder
    {
        return $this->buildQuery($args)
            ->hasTiktok();
    }

    public function selectPendingBulkOrder($args): BarcodeOrnamentBuilder
    {
        return $this->buildQuery($args)
            ->hasBulk()
            ->excludeTiktok();
    }

    public function selectPendingReprintOrder($args): BarcodeOrnamentBuilder
    {
        return $this->buildQuery($args)
            ->hasReprint()
            ->excludeTiktok()
            ->excludeBulkOrder();
    }

    public function selectPendingProductSku($args): BarcodeOrnamentBuilder
    {
        return $this->buildQuery($args)
            ->excludeTiktok()
            ->excludeBulkOrder()
            ->excludeReprint();
    }

    public function selectPendingByStore($args): BarcodeOrnamentBuilder
    {
        return $this->buildQuery($args);
    }
}
