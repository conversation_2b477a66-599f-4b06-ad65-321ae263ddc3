<?php

namespace App\Builder;

use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class BarcodeDTFBuilder
{
    protected Builder|null $query = null;

    private function buildQuery($args)
    {
        $this->query = DB::table('sale_order_item_barcode')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->where('sale_order.is_test', false)
            ->where('sale_order.warehouse_id', $args['warehouse_id'])
            ->where('sale_order_item_barcode.barcode_printed_id', false)
            ->where('sale_order_item_barcode.is_deleted', false)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.ink_color_status', true)
            ->where('sale_order_item_barcode.print_method', $args['print_method'])
            ->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)) // '2022-02-17'
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)) // '2022-02-17'
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);

        if (!empty($args['store_id'])) {
            $this->query->where('sale_order.store_id', $args['store_id']);
        }
        if (!empty($args['priorityStores']) && $args['print_method'] === PrintMethod::NECK && empty($args['store_id'])) {
            $this->query->whereNotIn('sale_order.store_id', $args['priorityStores']);
        }

        return $this;
    }

    public function getQuery(): Builder
    {
        return $this->query;
    }

    private function hasBulkOrder()
    {
        $this->query->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));

        return $this;
    }

    private function hasFBA()
    {
        $this->query->where('sale_order.is_fba_order', true);

        return $this;
    }

    private function hasReroute()
    {
        $this->query->whereNotNull('sale_order_item_barcode.employee_reroute_id');

        return $this;
    }

    private function hasTiktok()
    {
        $this->query->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);

        return $this;
    }

    private function hasManual()
    {
        $this->query->where('sale_order.is_manual', true);

        return $this;
    }

    private function hasReprint()
    {
        $this->query->whereNotNull('sale_order_item_barcode.label_root_id');

        return $this;
    }

    private function hasXQC()
    {
        $this->query->where('sale_order.is_xqc', true);

        return $this;
    }

    private function hasExpress()
    {
        $this->query->where('sale_order.is_eps', true);

        return $this;
    }

    private function excludeBulkOrder()
    {
        $this->query->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->where('sale_order.is_xqc', true);
                });
        });

        return $this;
    }

    private function excludeFBA()
    {
        $this->query->where('sale_order.is_fba_order', '<>', true);

        return $this;
    }

    private function excludeReroute()
    {
        $this->query->whereNull('sale_order_item_barcode.employee_reroute_id');

        return $this;
    }

    private function excludeTiktok()
    {
        $this->query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);

        return $this;
    }

    private function excludeManual()
    {
        $this->query->where('sale_order.is_manual', '<>', true);

        return $this;
    }

    private function excludeReprint()
    {
        $this->query->whereNull('sale_order_item_barcode.label_root_id');

        return $this;
    }

    private function excludeXQC()
    {
        $this->query->where('sale_order.is_xqc', '<>', true);

        return $this;
    }

    private function excludeExpress()
    {
        $this->query->where('sale_order.is_eps', '<>', true);

        return $this;
    }

    public function selectPendingBulkOrder($args)
    {
        $query = $this->buildQuery($args)
            ->hasBulkOrder()
            ->excludeXQC();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingFBA($args)
    {
        return $this->buildQuery($args)
            ->hasFBA()
            ->excludeBulkOrder();
    }

    public function selectPendingReroute($args)
    {
        $query = $this->buildQuery($args)
            ->hasReroute()
            ->excludeBulkOrder()
            ->excludeFBA();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingTiktok($args)
    {
        return $this->buildQuery($args)
            ->hasTiktok()
            ->excludeFBA();
    }

    public function selectPendingManual($args)
    {
        $query = $this->buildQuery($args)
            ->hasManual()
            ->excludeBulkOrder()
            ->excludeFBA()
            ->excludeReroute();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingReprint($args)
    {
        $query = $this->buildQuery($args)
            ->hasReprint()
            ->excludeBulkOrder()
            ->excludeFBA()
            ->excludeReroute()
            ->excludeManual();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingXQC($args)
    {
        $query = $this->buildQuery($args)
            ->hasXQC()
            ->excludeFBA()
            ->excludeReroute()
            ->excludeManual()
            ->excludeReprint();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingExpress($args)
    {
        $query = $this->buildQuery($args)
            ->hasExpress()
            ->excludeBulkOrder()
            ->excludeFBA()
            ->excludeReroute()
            ->excludeManual()
            ->excludeReprint()
            ->excludeXQC();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }

    public function selectPendingStyles($args)
    {
        $query = $this->buildQuery($args)
            ->excludeBulkOrder()
            ->excludeFBA()
            ->excludeReroute()
            ->excludeManual()
            ->excludeReprint()
            ->excludeXQC()
            ->excludeExpress();
        if (!empty($args['print_method']) && $args['print_method'] === PrintMethod::NECK) {
            $query = $query->excludeTiktok();
        }

        return $query;
    }
}
