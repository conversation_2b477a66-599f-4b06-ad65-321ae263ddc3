<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('release:order-on-hold')
            ->timezone('America/Los_Angeles')
            ->dailyAt('00:00')
            ->runInBackground();
        $schedule->command('worklog:calculate-qc',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString()
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:00')
            ->runInBackground();

        $schedule->command('worklog:calculate-printing',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString()
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:02')
            ->runInBackground();

        $schedule->command('worklog:calculate-folding',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString()
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:04')
            ->runInBackground();

        $schedule->command('worklog:calculate-labeling',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString()
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:06')
            ->runInBackground();

        $schedule->command('worklog:calculate-pretreat',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString(),
                1
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:10')
            ->runInBackground();

        $schedule->command('worklog:calculate-pulling',
            [
                now()->subDays(7)->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString(),
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:12')
            ->runInBackground();

        $schedule->command('worklog:calculate-inventory',
            [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString(),
            ])
            ->timezone('America/Los_Angeles')
            ->dailyAt('01:20')
            ->runInBackground();

        $schedule->command('export:work-log-weekly')
            ->weeklyOn(1, '1:30')
            ->runInBackground()
            ->timezone('America/Los_Angeles');

        $schedule->command('command:make-manifest-in-a-day')
            ->timezone('America/Los_Angeles') // Đặt múi giờ PST
            ->dailyAt('13:00')
            ->runInBackground();
        $schedule->command('calculate-supply-ink-report')
            ->timezone('America/Los_Angeles')
            ->dailyAt('02:00')
            ->runInBackground();

        $schedule->command('calculate-production-report')
            ->timezone('America/Los_Angeles')
            ->dailyAt('02:15')
            ->runInBackground();

        $schedule->command('calculate-report-offender-processing-time')
            ->timezone('America/Los_Angeles')
            ->dailyAt('03:00')
            ->runInBackground();

        $schedule->command('export:WeeklyLicensedSalesFor80sTee')
            ->weeklyOn(1, '1:00')
            ->runInBackground()
            ->timezone('America/Los_Angeles');

        $schedule->command('cache:id-barcode-printing')
            ->timezone('America/Los_Angeles')
            ->weekly()
            ->runInBackground();

        //$schedule->command('alphabroder:pull-orders')->hourly();

        $schedule->command('inventory:report')
            ->timezone('America/Los_Angeles')
            ->monthly();
        $schedule->command('monthly:calculate-cost-report')->monthly();
        $schedule->command('supply-inventory:aggregate')->monthly();
        $schedule->command('alert:order-image-error')->daily();
        $schedule->command('command:auto-tag-mugs-ornament-plaque')->everyThirtyMinutes();
        $schedule->command('command:auto-update-order-printed')->everyFiveMinutes();
        $schedule->command('command:auto-update-forecast')->daily();

//        $schedule->command('command:scan-form-tiktok-order-in-a-day')->hourlyAt(45);
        $schedule->command('command:scan-form-tiktok-order-hourly')
            ->hourly()
            ->runInBackground();
        $schedule->command('command:scan-form-USPS-order-in-a-day')->hourlyAt(45);

        $schedule->command('clear:failed-jobs')->daily();
        $schedule->command('clear:callback-log')->daily();
        $schedule->command('command:insert-sla-order')->everyFiveMinutes();

        // Support for retry create thumb for order item image
        $schedule->command('command:retry-create-thumb')->daily();

        $schedule->command('easypost:shipment-invoice-report')->daily();
        $schedule->command('quality-control:alert-qc-failed-3rd-times')->hourly();
        $schedule->command('sync:order-desk-order')
            ->everyFiveMinutes()
            ->runInBackground()
            ->withoutOverlapping();
        $schedule->command('command:report-missing-scan-pulling:')
            ->timezone('America/Los_Angeles')
            ->daily();
        $schedule->command('command:report-missing-scan-printing:')
            ->timezone('America/Los_Angeles')
            ->daily();
        $schedule->command('command:report-missing-scan-QC:')
            ->timezone('America/Los_Angeles')
            ->daily();
        $schedule->command('command:report-missing-scan-label:')
            ->timezone('America/Los_Angeles')
            ->daily();
        $schedule->command('command:report-missing-scan-folding:')
            ->timezone('America/Los_Angeles')
            ->daily();

        // employee work logs run group command

        $schedule->command('sendMail:ipViolationPrintify')
            ->timezone('America/Los_Angeles')
            ->daily()
            ->runInBackground();

        $schedule->command('update:visua-late-response')
            ->timezone('America/Los_Angeles')
            ->everyFiveMinutes()
            ->runInBackground();
        $schedule->command('calculate:back-log')->everyMinute();
        $schedule->command('calculate:forecast-sale-order')->hourly();
        $schedule->command('cover-daily:sale-order-print-method-report')
            ->timezone('America/Los_Angeles')
            ->daily();
        $schedule->command('export:shipping-invoice-markup --type=monthly')
            ->timezone('America/Los_Angeles')
            ->cron('0 1 1-4 * *');
        $schedule->command('export:shipping-invoice-markup --type=weekly')
            ->timezone('America/Los_Angeles')
            ->cron('0 1-3 * * 1-3');
        $schedule->command('cover-yearly:product-blank-cost-year')
            ->timezone('America/Los_Angeles')
            ->yearly();

        /* $schedule->command('qb:sync-bill')
             ->everyTwoHours()
             ->runInBackground();

         $schedule->command('qb:sync-bill-status')
             ->everyThirtyMinutes()
             ->runInBackground();*/

        $schedule->command('inventory:report-product-rank')
            ->timezone('America/Los_Angeles')
            ->quarterly();
        $schedule->command('command:insert-sale-order-sla-detail')->everyFiveMinutes();
        $schedule->command('sales:report')
            ->daily()
            ->timezone('America/Los_Angeles')
            ->runInBackground();
        $schedule->command('shipment:retry-upload-missing')
            ->everyThirtyMinutes()
            ->runInBackground();

        $schedule->command('notify:automate-change-password weekly')
            ->weeklyOn(1, '00:00')
            ->timezone('America/Los_Angeles')
            ->runInBackground();

        $schedule->command('command:generate-label-urgent')
            ->hourly()
            ->timezone('America/Los_Angeles')
            ->runInBackground();

        $schedule->command('notify:automate-change-password monthly')
            ->monthlyOn(1, '00:00')
            ->timezone('America/Los_Angeles')
            ->runInBackground();

        $schedule->command('command:insert-delivery-map-detail')
            ->daily();
        $schedule->command('order:generate-seller-tags')->everyFiveMinutes();

        $schedule->command('sale-order-pricing-snapshot:recalculate-by-order-date')
            ->daily()
            ->timezone('America/Los_Angeles')
            ->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
