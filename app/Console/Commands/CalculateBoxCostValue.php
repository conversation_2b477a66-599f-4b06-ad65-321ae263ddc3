<?php

namespace App\Console\Commands;

use App\Models\Box;
use App\Models\InventoryAddition;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use Illuminate\Console\Command;

class CalculateBoxCostValue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'box:calculate-cost-value';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate all boxes that do not have cost value';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Box::where('cost_value', 0)
            ->where('is_deleted', 0)
            ->whereNull('po_item_id')
            ->chunkById(500, function ($boxList) {
                foreach ($boxList as $box) {
                    $this->info("→ Đang tính cost cho Box: $box->barcode");
//                    $additionInfo = InventoryAddition::where('box_id', $box->id)->first();
//                    // 1. Nếu có cost_value trực tiếp trong InventoryAddition
//                    if ($additionInfo && $additionInfo->cost_value > 0) {
//                        $box->cost_value = $additionInfo->cost_value;
//                        $box->save();
//                        $this->info("→ DONE cost cho Box: $box->barcode");
//
//                        continue;
//                    }
//                    // 2. Nếu có InventoryAddition nhưng cost_value = 0, tìm từ POItem theo po_id đó
//                    if ($additionInfo && $additionInfo->po_id) {
//                        $poItem = PurchaseOrderItem::where('product_id', $box->product_id)
//                            ->where('po_id', $additionInfo->po_id)
//                            ->where('price', '>', 0)
//                            ->select('price')
//                            ->first();
//                        if ($poItem) {
//                            $box->cost_value = round($poItem->price * $box->quantity, 2);
//                            $box->save();
//                            $this->info("→ DONE cost cho Box: $box->barcode");
//
//                            continue;
//                        }
//                    }

                    // 3. Nếu không có gì thì fallback sang PO mới nhất
                    $latestPOItem = PurchaseOrderItem::where('product_id', $box->product_id)
                        ->where('price', '>', 0)
                        ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
                        ->where('purchase_order.order_status', '!=', PurchaseOrder::CANCELLED_STATUS)
                        ->orderBy('purchase_order.order_date', 'DESC')
                        ->orderBy('purchase_order.id', 'DESC')
                        ->select('purchase_order_item.price')
                        ->first();

                    if ($latestPOItem) {
                        $box->cost_value = round($latestPOItem->price * $box->quantity, 2);
                        $box->save();
                        $this->info("→ DONE cost cho Box: $box->barcode");

                        continue;
                    }

                    $this->info("→ Khong tinh duoc cost cho Box: $box->barcode");
                }
            });

        $this->info('✅ Hoàn thành cập nhật cost_value cho các Box.');
    }
}
