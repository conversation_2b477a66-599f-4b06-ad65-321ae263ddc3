<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ProductStyleIccProfile;
use App\Repositories\ProductStyleIccProfileRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;

class IccUploadProfiles extends Command
{
    protected $signature = 'icc:upload-profiles {filePath}';

    protected $description = 'Check and upload ICC profiles to S3';

    public function handle()
    {
        $path = $this->argument('filePath');
        $spreadsheet = IOFactory::load($path);
        $sheet = $spreadsheet->getSheet(0);
        $rows = $sheet->toArray();
        $tempPath = storage_path('app/icc_profile/temp');
        if (!file_exists($tempPath)) {
            mkdir($tempPath, 0755, true);
        }

        $headers = array_map(fn ($h) => strtolower(trim($h)), $rows[0]);
        $data = array_slice($rows, 1);

        foreach ($data as $row) {
            $rowData = array_combine($headers, $row);
            $isEmptyRow = collect($rowData)->every(function ($value) {
                return is_null($value) || trim($value) === '';
            });

            if ($isEmptyRow) {
                continue;
            }

            $style = trim($rowData['style']);
            $links = [
                'white' => $rowData['white'] ?? null,
                'black' => $rowData['black'] ?? null,
                'colored' => $rowData['colour'] ?? null,
            ];

            // Kiểm tra style có tồn tại không
            if (!Product::where('style', $style)->exists()) {
                $msg = "[SKIP] Style '$style' không tồn tại trong bảng product.";
                $this->warn($msg);

                continue;
            }

            $record = ProductStyleIccProfile::firstOrNew(['product_style' => $style]);

            foreach ($links as $key => $url) {
                if (!$url) {
                    continue;
                }

                try {
                    preg_match('/\/d\/(.*?)\//', $url, $matches);
                    $fileId = $matches[1] ?? null;
                    if (!$fileId) {
                        $this->error("[ERROR] Không tìm thấy fileId trong URL: $url");

                        continue;
                    }

                    $filename = "{$style}_{$key}.icm";
                    $safeFilename = app(ProductStyleIccProfileRepository::class)->sanitizeFileName($filename);
                    $localFilePath = $tempPath . DIRECTORY_SEPARATOR . $safeFilename;

                    // Download from Google Drive
                    $downloadUrl = "https://drive.google.com/uc?export=download&id=$fileId";
                    $content = Http::timeout(60)->get($downloadUrl)->body();
                    file_put_contents($localFilePath, $content);

                    // Upload to S3
                    $s3Path = ProductStyleIccProfile::PRODUCT_STYLE_ICC_PROFILE_FOLDER . "/$safeFilename";
                    Storage::disk('s3')->put($s3Path, file_get_contents($localFilePath));

                    // Cập nhật vào model
                    $record->{$key . '_name'} = $safeFilename;
                    $record->{$key . '_url'} = Storage::disk('s3')->url($s3Path);
                    $record->{$key . '_md5'} = md5_file($localFilePath);

                    $this->info("[UPLOAD] $safeFilename uploaded and saved.");
                } catch (\Exception $e) {
                    $this->error("[ERROR] File '$style - $key' lỗi: " . $e->getMessage());
                }
            }

            $record->save();
            $this->info("[DONE] Style '$style' đã được cập nhật.");
        }

        $this->info('Hoàn tất quá trình upload.');
    }
}
