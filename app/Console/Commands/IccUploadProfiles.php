<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ProductStyleIccProfile;
use App\Repositories\ProductStyleIccProfileRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;

class IccUploadProfiles extends Command
{
    protected $signature = 'icc:upload-profiles {filePath}';

    protected $description = 'Check and upload ICC profiles to S3';

    public function handle()
    {
        $path = $this->argument('filePath');
        $spreadsheet = IOFactory::load($path);
        $sheet = $spreadsheet->getSheet(0);
        $rows = $sheet->toArray();
        $tempPath = storage_path('app/icc_profile/temp');
        if (!file_exists($tempPath)) {
            mkdir($tempPath, 0755, true);
        }

        $headers = array_map(fn ($h) => strtolower(trim($h)), $rows[0]);
        $data = array_slice($rows, 1);

        foreach ($data as $row) {
            $rowData = array_combine($headers, $row);
            $isEmptyRow = collect($rowData)->every(function ($value) {
                return is_null($value) || trim($value) === '';
            });

            if ($isEmptyRow) {
                continue;
            }

            $style = trim($rowData['style']);
            $links = [
                'white' => $rowData['white'] ?? null,
                'black' => $rowData['black'] ?? null,
                'coloured' => $rowData['colour'] ?? null,
            ];

            // Kiểm tra style có tồn tại không
            if (!Product::where('style', $style)->exists()) {
                $msg = "[SKIP] Style '$style' không tồn tại trong bảng product.";
                $this->warn($msg);

                continue;
            }

            // Kiểm tra đủ 3 file không
            if (array_filter($links) !== $links) {
                $msg = "[SKIP] Style '$style' thiếu cả 3 file WHITE, BLACK, COLOURED.";
                $this->warn($msg);

                continue;
            }

            $s3Links = [];
            foreach ($links as $key => $url) {
                $filename = "{$style}_{$key}.icm";
                $fileUploadName = app(ProductStyleIccProfileRepository::class)->sanitizeFileName($filename);

                $localFilePath = $tempPath . '/' . $filename;
                try {
                    preg_match('/\/d\/(.*?)\//', $url, $matches);
                    $fileId = $matches[1] ?? null;
                    if (!$fileId) {
                        $msg = "[ERROR] Không tìm thấy fileId trong URL: $url";
                        $this->error($msg);

                        continue;
                    }
                    $downloadUrl = "https://drive.google.com/uc?export=download&id=$fileId";
                    $fileContent = Http::timeout(60)->get($downloadUrl)->body();
                    file_put_contents($localFilePath, $fileContent);
                    $s3Path = ProductStyleIccProfile::PRODUCT_STYLE_ICC_PROFILE_FOLDER . "/$fileUploadName";
                    Storage::disk('s3')->put($s3Path, file_get_contents($localFilePath));
                    $s3Links[$key]['url'] = Storage::disk('s3')->url($s3Path);
                    $s3Links[$key]['name'] = $filename;
                    $this->info("[UPLOAD] $filename uploaded to $s3Path");
                } catch (\Exception $e) {
                    $msg = "[ERROR] Tải hoặc upload file '$filename' thất bại: " . $e->getMessage();
                    $this->error($msg);
                }
            }

            if (count($s3Links) > 0) {
                ProductStyleIccProfile::updateOrCreate(
                    ['product_style' => $style],
                    [
                        'white_name' => $s3Links['white']['name'] ?? null,
                        'black_name' => $s3Links['black']['name'] ?? null,
                        'colored_name' => $s3Links['coloured']['name'] ?? null,
                        'white_url' => $s3Links['white']['url'] ?? null,
                        'black_url' => $s3Links['black']['url'] ?? null,
                        'colored_url' => $s3Links['coloured']['url'] ?? null,
                    ],
                );
                $this->info("[DONE] Style '$style' đã được cập nhật ICC profiles.");
            }
        }

        $this->info('Hoàn tất quá trình upload.');
    }
}
