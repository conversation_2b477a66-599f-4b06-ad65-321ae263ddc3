<?php

namespace App\Console\Commands;

use App\Models\SaleOrderAddress;
use App\Models\Shipment;
use App\Models\ShipmentMappingDelivery;
use Carbon\Carbon;
use Illuminate\Console\Command;

class InsertDeliveryMapDetail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:insert-delivery-map-detail {date? : date in Y-m-d format (e.g., 2025-05-01)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Shipment base on state';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $inputDate = $this->argument('date');
        if ($inputDate === 'all') {
            // Chạy từ 2023-01-01 đến hôm qua
            $start = Carbon::parse('2024-01-01');
            $end = Carbon::now()->startOfDay();
            while ($start->lte($end)) {
                $date = $start->toDateString();
                $from = Carbon::parse($date . ' 00:00:00')->toDateTimeString();
                $to = Carbon::parse($date . ' 23:59:59')->toDateTimeString();
                $this->info("Calculating for: $date");
                $this->calculate($from, $to);
                $start->addDay();
            }
        } elseif ($inputDate) {
            // Chạy cho một ngày cụ thể
            $date = Carbon::parse($inputDate)->toDateString();
            $from = Carbon::parse($date . ' 00:00:00')->toDateTimeString();
            $to = Carbon::parse($date . ' 23:59:59')->toDateTimeString();
            $this->info("Calculating for: $date");
            $this->calculate($from, $to);
        } else {
            // Mặc định: chạy cho 15 ngày gần nhất
            $start = Carbon::now()->subDays(15)->startOfDay();
            $end = Carbon::now()->startOfDay(); // hôm nay

            while ($start->lte($end)) {
                $date = $start->toDateString();
                $from = Carbon::parse($date . ' 00:00:00')->toDateTimeString();
                $to = Carbon::parse($date . ' 23:59:59')->toDateTimeString();
                $this->info("Scheduled run for date: $date", "from : {$from}-> {$to}");
                $this->calculate($from, $to);
                $start->addDay();
            }
        }

        $this->info('---------------------------End------------------------------------');
    }

    public function calculate($startDate, $endDate)
    {
        $this->info("Filtering data between $startDate and $endDate");

        $stateMap = [
            'alabama' => 'AL',
            'alaska' => 'AK',
            'arizona' => 'AZ',
            'arkansas' => 'AR',
            'california' => 'CA',
            'colorado' => 'CO',
            'connecticut' => 'CT',
            'delaware' => 'DE',
            'district of columbia' => 'DC',
            'florida' => 'FL',
            'georgia' => 'GA',
            'hawaii' => 'HI',
            'idaho' => 'ID',
            'illinois' => 'IL',
            'indiana' => 'IN',
            'iowa' => 'IA',
            'kansas' => 'KS',
            'kentucky' => 'KY',
            'louisiana' => 'LA',
            'maine' => 'ME',
            'maryland' => 'MD',
            'massachusetts' => 'MA',
            'michigan' => 'MI',
            'minnesota' => 'MN',
            'mississippi' => 'MS',
            'missouri' => 'MO',
            'montana' => 'MT',
            'nebraska' => 'NE',
            'nevada' => 'NV',
            'new hampshire' => 'NH',
            'new jersey' => 'NJ',
            'new mexico' => 'NM',
            'new york' => 'NY',
            'north carolina' => 'NC',
            'north dakota' => 'ND',
            'ohio' => 'OH',
            'oklahoma' => 'OK',
            'oregon' => 'OR',
            'pennsylvania' => 'PA',
            'rhode island' => 'RI',
            'south carolina' => 'SC',
            'south dakota' => 'SD',
            'tennessee' => 'TN',
            'texas' => 'TX',
            'utah' => 'UT',
            'vermont' => 'VT',
            'virginia' => 'VA',
            'washington' => 'WA',
            'west virginia' => 'WV',
            'wisconsin' => 'WI',
            'wyoming' => 'WY',
            'puerto rico' => 'PR',
            'guam' => 'GU',
            'american samoa' => 'AS',
            'armed forces americas' => 'AA',
            'armed forces europe' => 'AE',
            'armed forces pacific' => 'AP',
            'virgin islands' => 'VI',
        ];

        $reverseStateMap = array_flip($stateMap);

        setTimezone();

        $data = Shipment::join('sale_order', 'sale_order.id', '=', 'shipment.order_id')
            ->join('shipment_transit', 'shipment_transit.shipment_id', '=', 'shipment.id')
            ->join('sale_order_address', 'sale_order_address.order_id', '=', 'shipment.order_id')
            ->selectRaw('
            DATE(shipment_transit.delivered_at) as delivery_date,
            sale_order_address.state,
            LEFT(sale_order_address.zip, 3) as zipcode_prefix,
            shipment.shipment_account,
            shipment.warehouse_id,
            COUNT(*) as delivered_count
        ')
            ->where('tracking_status', 'delivered')
            ->whereBetween('shipment_transit.delivered_at', [$startDate, $endDate])
            ->where('sale_order_address.country', 'US')
            ->where('sale_order_address.type_address', SaleOrderAddress::TO_ADDRESS)
            ->where('sale_order.is_test', 0)
            ->whereNotNull('shipment_transit.delivered_at')
            ->groupBy('delivery_date', 'sale_order_address.state', 'zipcode_prefix', 'shipment.shipment_account', 'shipment.warehouse_id')
            ->orderBy('delivery_date')
            ->orderBy('sale_order_address.state')
            ->orderBy('zipcode_prefix')
            ->orderBy('shipment.shipment_account')
            ->get();

        $groupedData = $data
            ->map(function ($item) use ($stateMap, $reverseStateMap) {
                $stateKey = strtolower($item->state);
                $item->state = $stateMap[$stateKey] ?? strtoupper($item->state);
                $item->state_name = $reverseStateMap[strtoupper($item->state)] ?? null;
                $item->state_name = $item->state_name ? ucwords($item->state_name) : null;

                return $item;
            })
            ->groupBy(function ($item) {
                return $item->delivery_date . '|' . $item->state . '|' . $item->zipcode_prefix . '|' . $item->shipment_account . '|' . $item->warehouse_id;
            })
            ->map(function ($group) {
                return [
                    'delivery_date' => $group->first()->delivery_date,
                    'state' => $group->first()->state,
                    'state_name' => $group->first()->state_name,
                    'zipcode_prefix' => $group->first()->zipcode_prefix,
                    'shipment_account' => $group->first()->shipment_account,
                    'delivered_count' => $group->sum('delivered_count'),
                    'warehouse_id' => $group->first()->warehouse_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })
            ->values();

        $chunks = array_chunk($groupedData->toArray(), 200);
        $totalUpserted = 0;

        foreach ($chunks as $index => $chunk) {
            ShipmentMappingDelivery::upsert(
                $chunk,
                ['delivery_date', 'state', 'zipcode_prefix', 'shipment_account', 'warehouse_id'],
                ['delivered_count', 'state_name', 'updated_at'],
            );
            $count = count($chunk);
            $totalUpserted += $count;

            $this->info('Chunk ' . ($index + 1) . " upserted: $count records");
        }
        $this->info("Total records upserted: $totalUpserted");

        return 1;
    }
}
