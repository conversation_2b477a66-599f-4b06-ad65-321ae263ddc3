<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Service\ConvertService;
use App\Models\BarcodePrinted;

class Convert3dToPdf extends Command
{
    protected ConvertService $convertService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert:3d-pdf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert uv3d to PDF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ConvertService $convertService)
    {
        parent::__construct();
        $this->convertService = $convertService;
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $job = BarcodePrinted::find3DToConvert();
            if (!$job) {
                sleep(10);
                continue;
            }
            sleep(15);
            $this->convertService->convertUV3DToPdf($job);
        }
    }
}
