<?php

namespace App\Console\Commands;

use App\Repositories\GelatoRepository;
use Illuminate\Console\Command;

class AutoCreateLabelShippingGelato extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:shipping-create-label-gelato';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create auto shipping';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(GelatoRepository $gelatoRepository)
    {
        try {
            $gelatoRepository->createLabelGelato();
        } catch (\Throwable $error) {
            $this->error($error->getMessage());
        }
    }
}
