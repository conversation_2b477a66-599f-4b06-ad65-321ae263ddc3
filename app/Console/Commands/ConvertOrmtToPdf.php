<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Service\ConvertService;
use App\Models\PdfConverted;

class ConvertOrmtToPdf extends Command
{
    protected ConvertService $convertService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert:ormt-pdf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert Ornaments to PDF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $job = PdfConverted::findToConvert();
            if (!$job) {
                sleep(10);
                continue;
            }

            sleep(10);
            $convertService = new ConvertService();
            $convertService->convertOrmtToPdfByDompdf($job);
            $convertService = null;
            sleep(10);
        }
    }
}
