<?php

namespace App\Console\Commands;

use App\Jobs\UpdateShipmentStatusJob;
use App\Models\Shipment;
use EasyPost\Shipment as ShipmentEasyPost;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateTrackingStatusEasyPost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'easypost:update-status-tracking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $query = Shipment::select('shipment.*')
            ->from(DB::raw('shipment USE INDEX (created_at)'))
            ->with('shipmentEasypost')
            ->where('provider', 'easypost')
            ->where('tracking_status', '!=', 'delivered')
            ->where('id', '>', ********)
            ->where('shipment.created_at', '>=', '2025-04-01')
            ->where('shipment.created_at', '<=', '2025-04-20');

        $count = 1;

        $query->chunkById(500, function ($items) use ($count) {
            foreach ($items as $item)  {
                $carrierAccount = DB::table('shipping_carrier_easypost')
                    ->where('carrier_account', $item->account_shipping_easypost)
                    ->first();
              // var_dump($item->id, $item->shipmentEasypost->easypost_id, $carrierAccount->api_key_easypost);
                UpdateShipmentStatusJob::dispatch($item->id, $item->shipmentEasypost->easypost_id, $carrierAccount->api_key_easypost)
                    ->onQueue('update_shipment_status');

                $count++;
                $this->info("$count |  $item->ship_date");

            }
            sleep(1);
            //  return false;

        });
    }

    public function manual()
    {
        $query = Shipment::select('shipment.*')
            ->from(DB::raw('shipment USE INDEX (created_at)'))
            ->with('shipmentEasypost')
            ->where('provider', 'easypost')
            ->where('tracking_status', '!=', 'delivered')
            ->where('shipment.created_at', '>=', '2025-04-01');


        $query->chunkById(100, function ($items) {
            foreach ($items as $item) {
                $carrierAccount = DB::table('shipping_carrier_easypost')
                    ->where('carrier_account', $item->account_shipping_easypost)
                    ->first();

                $shipment = ShipmentEasyPost::retrieve($item->shipmentEasypost->easypost_id, $carrierAccount->api_key_easypost);
                if (!$shipment) {
                    $this->info("shipment not found");
                    continue;
                }

                $currentStatus = $item->tracking_status;

                $update = [];
                if ($shipment->status && $shipment->status != $item->tracking_status) {
                    $update['tracking_status'] = $shipment->status;
                    $item->update($update);
                }

                $this->info("$item->id | $item->tracking_number | $item->created_at | $currentStatus -> $shipment->status");


            }

        });
    }
}
