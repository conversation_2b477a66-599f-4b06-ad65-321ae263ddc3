<?php

namespace App\Console\Commands;

use App\Http\Service\AlertService;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderBox;
use App\Models\PurchaseOrderBoxItem;
use App\Models\Setting;
use App\Repositories\ProductQuantityRepository;
use App\Repositories\ProductRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SsactivewearUpdateOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ssactivewear:update-po-new-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'update purchase order sync from ssactivewear has: order status is new_order';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $this->updatePo();
            sleep(60 * 60);
        }
    }

    public function updatePo()
    {
        $data = PurchaseOrder::where('vendor_id', 3)->where('order_status', PurchaseOrder::NEW_ORDER_STATUS)->get();
        if (empty($data)) {
            $this->info('data is empty');
            exit();
        }
        $dataSettingAccount = Setting::where('label', 'ssactivewear_account')->first();
        if (empty($dataSettingAccount)) {
            $this->error('Account connect to api.ssactivewear.com NOT FOUND');
            $alertService = new AlertService();

            return $alertService->alertPullPurchaseOrder('Account connect to api.ssactivewear.com NOT FOUND');
        }
        $settingAccount = explode('_USER_PASS_', $dataSettingAccount->value);
        $user = $settingAccount[0];
        $password = $settingAccount[1];
        foreach ($data as $item) {
            if (empty($item->invoice_number)) {
                $this->info('Invoice number is empty, continue');

                continue;
            }
            $response = Http::withBasicAuth($user, $password)
                ->get("https://api.ssactivewear.com/v2/orders/$item->invoice_number?lines=true&Boxes=true&mediatype=json");
            if ($response->getStatusCode() != 200) {
                $alertService = new AlertService();
                $alertService->alertPullPurchaseOrder("ssactivewear: update new order ERROR , status code = {$response->getStatusCode()}");
                $this->error('client error');

                continue;
            }
            $ssacOrder = json_decode($response->getBody()->getContents());
            if (empty($ssacOrder)) {
                $this->info("$item->invoice_number empty data");

                continue;
            }
            $ssacOrder = $ssacOrder[0];
            $this->info("Invoice : $item->invoice_number");
            if (strtolower($ssacOrder->orderStatus) == strtolower(PurchaseOrder::CANCELLED_STATUS)) {
                //order cancel thi update lai quantity, incoming
                DB::transaction(function () use ($item) {
                    $item->order_status = PurchaseOrder::CANCELLED_STATUS;
                    $item->save();
                    ProductQuantityRepository::updateIncomingByPurchaseOrderInWarehouse($item->id, $item->warehouse_id);
                });
                $this->info("Update invoice number Cancel: $item->invoice_number");

                continue;
            }
            if (strtolower($ssacOrder->orderStatus) == strtolower(PurchaseOrder::SHIPPED_STATUS)) {
                DB::transaction(function () use ($ssacOrder, $item) {
                    $this->info("Invoice : $item->invoice_number");
                    //order shipped thi update lai tracking number cho PO va Box
                    $item->order_status = PurchaseOrder::SHIPPED_STATUS;
                    $item->tracking_number = $ssacOrder->trackingNumber;
                    $item->delivery_date = !empty($ssacOrder->shipDate) ? Carbon::parse($ssacOrder->shipDate)->format('Y-m-d H:i:s') : null;
                    $item->save();
                    PurchaseOrderBox::where('po_id', $item->id)->delete();
                    PurchaseOrderBoxItem::where('po_id', $item->id)->delete();
                    $trackingNumber = !empty($ssacOrder->trackingNumber) ? $ssacOrder->trackingNumber : null;
                    $arrGtin = array_unique(array_column($ssacOrder->lines, 'gtin'));
                    $productsActive = ProductRepository::fetchProductsGroupByKey($arrGtin, 'gtin');
                    foreach ($ssacOrder->boxes as $box) {
                        $trackingBoxNumber = !empty($box->trackingNumber) ? $box->trackingNumber : null;
                        $trackingBoxNumber = count($ssacOrder->boxes) == 1 && empty($trackingBoxNumber) ? $trackingNumber : $trackingBoxNumber;
                        $orderBoxId = DB::table('purchase_order_box')->insertGetId([
                            'po_id' => $item->id,
                            'box_number' => $box->boxNumber,
                            'invoice_number' => $ssacOrder->invoiceNumber,
                            'tracking_number' => $trackingBoxNumber,
                            'created_at' => date('Y-m-d H:i:s'),
                        ]);
                        foreach ($box->lines as $itemLine) {
                            if (!isset($productsActive[$itemLine->gtin])) {
                                continue;
                            }
                            $quantity = $itemLine->qtyOrdered > 0 ? $itemLine->qtyOrdered : 0;
                            $product = $productsActive[$itemLine->gtin];
                            DB::table('purchase_order_box_item')->insert([
                                'po_id' => $item->id,
                                'po_box_id' => $orderBoxId,
                                'sku' => !empty($product->sku) ? $product->sku : null,
                                'product_id' => !empty($product->id) ? $product->id : null,
                                'external_sku' => $itemLine->sku,
                                'gtin' => $itemLine->gtin,
                                'quantity' => $quantity,
                                'quantity_api' => $itemLine->qtyOrdered,
                                'created_at' => date('Y-m-d H:i:s'),
                            ]);
                        }
                    }
                    $this->info("Update invoice number Shipped: $item->invoice_number");
                });
            }
            sleep(2);
        }
        $this->info('---------------run end-----------');
    }
}
