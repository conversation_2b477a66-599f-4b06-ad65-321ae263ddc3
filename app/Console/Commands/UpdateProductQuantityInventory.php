<?php

namespace App\Console\Commands;

use App\Models\OutOfStockOrderLog;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\Setting;
use App\Models\Warehouse;
use App\Repositories\InventoryRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateProductQuantityInventory extends Command
{
    protected $signature = 'inventory:sync-quantity-and-alert-oos';

    protected $description = 'Sync product quantities from inventory and alert if out of stock';

    protected $inventoryRepo;

    public function __construct(InventoryRepository $inventory)
    {
        $this->inventoryRepo = $inventory;
        parent::__construct();
    }

    public function handle()
    {
        while (true) {
            $this->info('[StockMonitor] Start syncing & alerting stock...');
            // 1. Đồng bộ tồn kho
            // $this->inventoryRepo->syncProductQuantitiesFromInventory();

            $this->info('✅ Product quantities synced successfully.');
            // 2. Reset lại alert nếu quantity đã dương trở lại
            ProductQuantity::where('quantity', '>=', 0)
                ->whereNotNull('alerted_at')
                ->chunkById(100, function ($productQuantities) {
                    foreach ($productQuantities as $productQty) {
                        OutOfStockOrderLog::where('product_id', $productQty->product_id)
                            ->where('warehouse_id', $productQty->warehouse_id)
                            ->delete();

                        $productQty->update([
                            'alerted_at' => null,
                        ]);
                    }
                });
            // 3. Gửi alert nếu quantity < 0 và chưa alert
            $warehouses = Warehouse::all();
            ProductQuantity::where('quantity', '<', 0)
                ->whereNull('alerted_at')
                ->chunkById(100, function ($productQuantities) use ($warehouses) {
                    foreach ($productQuantities as $productQty) {
                        $product = Product::find($productQty->product_id);
                        $warehouse = $warehouses->where('id', $productQty->warehouse_id)->first();
                        if ($product && $warehouse) {
                            $awaitingProductionQty = $this->getOpenOrdersCount($product->id, $productQty->warehouse_id);
                            if ($awaitingProductionQty == 0) {
                                OutOfStockOrderLog::firstOrCreate([
                                    'product_id' => $product->id,
                                    'warehouse_id' => $productQty->warehouse_id,
                                    'created_at' => now(),
                                ]);
                            }
                            $message = "*SKU:* {$product->sku}, *Stock:*  " . number_format($productQty->quantity) . ', *Open Orders (items):* ' . number_format($awaitingProductionQty) . "\n";
                            $this->info($message);
                            $productQty->alerted_at = now();
                            $productQty->save();
                            try {
                                $urlGoogleSpace = Setting::where('name', "alert_out_of_stock_{$warehouse->code}")->value('value');
                                if ($urlGoogleSpace) {
                                    sendGoogleChat($message, $urlGoogleSpace);
                                }
                            } catch (\Throwable $e) {
                                $this->error("❌ Failed to send Google Chat alert for SKU {$product->sku}: " . $e->getMessage());
                            }

                            sleep(1);
                        }
                    }
                });

            $this->checkAndSendSecondaryOutOfStockAlert();
            $this->info('✅ Alert check completed.');

            sleep(1800);
        }
    }

    /**
     * Check and send secondary out of stock alerts for products
     * This function processes products that were previously marked as out of stock
     * and sends additional alerts if they still have open orders
     */
    public function checkAndSendSecondaryOutOfStockAlert()
    {
        OutOfStockOrderLog::chunkById(100, function ($logs) {
            foreach ($logs as $log) {
                $product = Product::find($log->product_id);
                $warehouse = Warehouse::find($log->warehouse_id);

                if (!$product || !$warehouse) continue;

                $productQty = ProductQuantity::where('product_id', $product->id)
                    ->where('warehouse_id', $log->warehouse_id)
                    ->first();

                if (!$productQty || $productQty->quantity >= 0) {
                    $log->delete();
                    continue;
                }

                $awaitingProductionQty = $this->getOpenOrdersCount($product->id, $log->warehouse_id);

                if ($awaitingProductionQty > 0) {
                    $message = "*SKU:* {$product->sku}, *Stock:*  " . number_format($productQty->quantity) . ', *Open Orders (items):* ' . number_format($awaitingProductionQty) . "\n";
                    $this->info($message);

                    try {
                        $urlGoogleSpace = Setting::where('name', "alert_out_of_stock_{$warehouse->code}")->value('value');
                        if ($urlGoogleSpace) {
                            sendGoogleChat($message, $urlGoogleSpace);
                        }
                    } catch (\Throwable $e) {
                        $this->error("❌ Failed to send 2nd alert for SKU {$product->sku}: " . $e->getMessage());
                    }
                    $log->delete();
                    sleep(1);
                }
            }
        });
    }

    public function getOpenOrdersCount($productId, $warehouseId)
    {
        return SaleOrderItemBarcode::where('warehouse_id', $warehouseId)
            ->whereHas('orderItem', function ($query) use ($productId) {
                $query->where('product_id', $productId);
            })
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->count();
    }

    private function syncInventoryStockIncrementally()
    {
        ///funtion de dong bo quantity tu inventory sang product_quantity
        $now = now();
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        // Lấy trạng thái hiện tại
        $syncState = DB::table('sync_inventory_state')->first();

        $lastMaxId = $syncState->last_max_id ?? 0;

        /**
         * BƯỚC 1: TÍNH SNAPSHOT TRƯỚC 30 NGÀY, chỉ chạy 1 lần duy nhất
         */
        if ($lastMaxId === 0) {
            $snapshotData = DB::table('inventory')
                ->select(
                    'product_id',
                    'warehouse_id',
                    DB::raw('SUM(CASE WHEN direction = 0 THEN quantity WHEN direction = 1 THEN -quantity ELSE 0 END) AS current_stock'),
                    DB::raw('MAX(id) as max_id'),
                )
                ->where('is_deleted', 0)
                ->whereNull('replicated_from_id') // Không lấy dòng revert
                ->where('created_at', '<=', $thirtyDaysAgo)
                ->groupBy('product_id', 'warehouse_id')
                ->get();

            foreach ($snapshotData as $row) {
                DB::table('temp_inventory_stock')->updateOrInsert(
                    ['product_id' => $row->product_id, 'warehouse_id' => $row->warehouse_id],
                    ['current_stock' => $row->current_stock, 'updated_at' => $now],
                );
            }

            $lastMaxId = $snapshotData->max('max_id') ?? 0;
            DB::table('sync_inventory_state')->insert([
                'last_max_id' => $lastMaxId,
                'sync_key' => 'inventory_sync',
            ]);
        }

        /**
         * BƯỚC 2: TÍNH data (CÁC DÒNG MỚI)
         */
        $deltaData = DB::table('inventory')
            ->select(
                'product_id',
                'warehouse_id',
                DB::raw('SUM(CASE WHEN direction = 0 THEN quantity WHEN direction = 1 THEN -quantity ELSE 0 END) AS change_stock'),
                DB::raw('MAX(id) as max_id'),
            )
            ->where('id', '>', $lastMaxId)
            ->whereNull('replicated_from_id') // Loại dòng revert, xử lý riêng bước 3
            ->groupBy('product_id', 'warehouse_id')
            ->get();

        foreach ($deltaData as $row) {
            DB::table('temp_inventory_stock')->updateOrInsert(
                ['product_id' => $row->product_id, 'warehouse_id' => $row->warehouse_id],
                [
                    'current_stock' => DB::raw("current_stock + ({$row->change_stock})"),
                    'updated_at' => $now
                ],
            );
        }

        /**
         * BƯỚC 3: XỬ LÝ DÒNG REVERT
         */
        $revertData = DB::table('inventory')
            ->where('replicated_from_id', '<=', $lastMaxId)
            ->where('id', '>', $lastMaxId)
            ->get();

        foreach ($revertData as $revertRow) {
            $origin = DB::table('inventory')->where('id', $revertRow->replicated_from_id)->first();
            if (!$origin) {
                continue;
            }

            // Đảo ngược lại hiệu ứng của dòng gốc
            $change = ($origin->direction === 0 ? $origin->quantity : -$origin->quantity);

            DB::table('temp_inventory_stock')->updateOrInsert(
                ['product_id' => $origin->product_id, 'warehouse_id' => $origin->warehouse_id],
                [
                    'current_stock' => DB::raw("current_stock + ({$change})"),
                    'updated_at' => $now
                ],
            );
        }

        /**
         * CẬP NHẬT delta_max_id
         */
        $maxDeltaId = max(
            $deltaData->max('max_id') ?? 0,
            $revertData->max('id') ?? 0,
        );

        if ($maxDeltaId > $lastMaxId) {
            DB::table('sync_inventory_state')
                ->where('sync_key', 'inventory_sync')
                ->update([
                    'last_max_id' => $maxDeltaId,
                    'updated_at' => $now,
                ]);
        }
    }

    public function syncProductQuantitiesFromInventory(): void
    {
        DB::statement('
            UPDATE product_quantity pq
            JOIN temp_inventory_stock tis
              ON pq.product_id = tis.product_id AND pq.warehouse_id = tis.warehouse_id
            SET pq.quantity = tis.current_stock
        ');
    }
}
