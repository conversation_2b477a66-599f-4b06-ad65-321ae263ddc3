<?php

namespace App\Console\Commands;

use App\Models\Shipment;
use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrier;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use EasyPost\Batch;
use Illuminate\Console\Command;

class MakeManifestInDay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:make-manifest-in-a-day';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            setTimezone('America/Los_Angeles');
            $skipShipmentId = Shipment::GetIdShipmentBeforeDays(2);
            $wareHouses = Warehouse::all();
            $carrier_code_DHL = ShippingCarrier::DHL_ECOMMERCE_CODE;
            $shipments = Shipment::with('shippingCarrierService', 'shipmentEasypost', 'shippingIntegrationAccount.shippingIntegration', 'store:id,name')
                ->where('id', '>=', $skipShipmentId)
                ->whereNull('refund_status')
                ->where('carrier_code', $carrier_code_DHL)
                ->where('provider', Shipment::PROVIDER_EASYPOST)
                ->whereNotNull('account_shipping_easypost')
                ->whereDoesntHave('shipmentManifestTracking', function ($q) {
                    $q->where('created_at', '>=', now()->subDays(30)->startOfDay()->toDateTimeString());
                })
                ->whereBetween('created_at', [
                    now()::yesterday()->setHour(13)->setMinute(0),
                    now()->setHour(13)->setMinute(0)
                ])
                ->get();
            $dataTracking = [];
            foreach ($wareHouses as $wareHouse) {
                $dataTracking[$wareHouse->id] = [];
            }
            $manifestId = [];
            $apikeyEasypost = [];
            setTimezoneDefault();
            foreach ($shipments as $key => $shipment) {
                if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD_STORE || $shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_STORE) {
                    if (!isset($manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost])) {
                        $dataShipmentManifest = [
                            'warehouse_id' => $shipment->warehouse_id,
                            'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                            'employee_create_id' => User::SYSTEM,
                            'barcode_box' => str_replace(' ', '', str_replace('API', '', strtoupper($shipment->store?->name))) . '-DHL' . Carbon::now()->format('mdY')
                        ];
                        $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                        $manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost] = $shipmentManifest;
                        $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                    }
                    $dataTracking[$shipment->warehouse_id][$shipment->account_shipping_easypost][] = [
                        'tracking_number' => $shipment->tracking_number,
                        'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                        'service_code_id' => $shipment->shippingCarrierService?->id,
                        'manifest_id' => $manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost],
                        'account' => $shipment->shipment_account,
                        'warehouse_id' => $shipment->warehouse_id,
                    ];
                }
                if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD) {
                    if (!isset($manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost])) {
                        $dataShipmentManifest = [
                            'warehouse_id' => $shipment->warehouse_id,
                            'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                            'employee_create_id' => User::SYSTEM,
                            'barcode_box' => strtoupper(Shipment::SHIPMENT_ACCOUNT_SWIFTPOD) . Carbon::now()->format('mdY'),
                        ];
                        $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                        $manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost] = $shipmentManifest;
                        $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                    }
                    $dataTracking[$shipment->warehouse_id][$shipment->account_shipping_easypost][] = [
                        'tracking_number' => $shipment->tracking_number,
                        'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                        'service_code_id' => $shipment->shippingCarrierService?->id,
                        'manifest_id' => $manifestId[$shipment->warehouse_id][$shipment->account_shipping_easypost],
                        'account' => $shipment->shipment_account,
                        'warehouse_id' => $shipment->warehouse_id,
                    ];
                }
            }

            foreach ($dataTracking as $key => $tracking) {
                foreach ($tracking as $keyAccount => $trackingNumber) {
                    $dataShippingEasypost = [];
                    $dataShipmentManifestTracking = [];
                    $apikey = $apikeyEasypost[$keyAccount];
                    foreach ($trackingNumber as $item) {
                        $dataShippingEasypost[] = [
                            'id' => $item['ship_id_partner']
                        ];
                        $dataShipmentManifestTracking[] = [
                            'service_code_id' => $item['service_code_id'],
                            'employee_scan_id' => User::SYSTEM,
                            'tracking_number' => $item['tracking_number'],
                            'ship_id_partner' => $item['ship_id_partner'],
                            'manifest_id' => $item['manifest_id'],
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    $dataBatch = $this->makeScanForm($dataShippingEasypost, $apikey);
                    $dataUpdateManifest = [
                        'batch_id_easypost' => $dataBatch->id,
                        'status' => ShipmentManifest::STATUS_GENERATING,
                        'state_batch' => $dataBatch->state,
                    ];
                    ShipmentManifest::where('id', $dataShipmentManifestTracking[0]['manifest_id'])
                        ->update($dataUpdateManifest);
                    ShipmentManifestTracking::insert($dataShipmentManifestTracking);
                }
            }
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
        }
    }

    protected function makeScanForm($dataShipmentTracking, $apiKey)
    {
        try {
            $batch = Batch::create(['shipments' => $dataShipmentTracking], $apiKey);

            return $batch;
        } catch (\Throwable $exception) {
            return $exception;
        }
    }
}
