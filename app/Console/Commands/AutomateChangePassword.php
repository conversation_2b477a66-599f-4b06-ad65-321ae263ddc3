<?php

namespace App\Console\Commands;

use App\Mail\SendMailNotifyChangePassword;
use App\Models\MagicLoginToken;
use App\Models\Setting;
use App\Models\User;
use App\Models\Warehouse;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Throwable;
use Tymon\JWTAuth\Facades\JWTAuth;

class AutomateChangePassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:automate-change-password {status}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify and automate app password change based on given frequency (weekly or monthly)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[START] automate change password');
        $status = $this->argument('status');

        if (!in_array($status, ['monthly', 'weekly'])) {
            $this->error("Invalid status. Use 'monthly' or 'weekly'.");

            return;
        }

        $emailsString = Setting::where('label', 'change_password_email_blocklist')->value('value');
        $blockedEmails = explode(',', $emailsString);

        $users = User::where(function ($query) use ($status) {
            $query->where(function ($q) use ($status) {
                $q->where('change_app_password_status', $status)
                    ->whereNotNull('app_send_to');
            })
                ->orWhere(function ($q) use ($status) {
                    $q->where('change_seller_password_status', $status)
                        ->whereNotNull('seller_send_to');
                });
        })->get();

        if ($users->isEmpty()) {
            $this->info("No users found for status: $status");

            return;
        }

        foreach ($users as $user) {
            try {
                $hasChanges = false;

                $appEmails = [];
                $sellerEmails = [];
                $newAppPassword = null;
                $newSellerPassword = null;

                if ($user->change_app_password_status === $status && $user->app_send_to) {
                    $newAppPassword = Str::random(9);
                    $user->password = Hash::make($newAppPassword);
                    $hasChanges = true;

                    $appEmails = $this->filterEmails($user->app_send_to, $blockedEmails);
                }

                if ($user->change_seller_password_status === $status && $user->seller_send_to) {
                    $newSellerPassword = Str::random(9);
                    $user->seller_password = Hash::make($newSellerPassword);
                    $hasChanges = true;

                    $sellerEmails = $this->filterEmails($user->seller_send_to, $blockedEmails);
                }

                $emails = [...$appEmails, ...$sellerEmails];

                if ($hasChanges) {
                    $data = [
                        'to' => $emails,
                        'password' => $newAppPassword,
                        'seller_password' => $newSellerPassword,
                        'username' => $user->username,
                        'type' => $status,
                        'include_app_qr_code' => $user->include_app_qr_code,
                        'pdfs' => []
                    ];

                    $user->save();
                    if ($user->include_app_qr_code && !is_null($newAppPassword)) {
                        $data['pdfs'] = $this->generateQRCodes($user, $data);
                    }

                    $this->sendEmail($data);
                    Storage::disk('public')->deleteDirectory((string)$user->id);
                    $this->info("Passwords updated for user ID {$user->id}");
                }
            } catch (\Throwable $e) {
                $this->error('Failed when update password ' . $e->getMessage());
            }
        }
    }

    private function filterEmails($emailList, $blockedEmails)
    {
        return array_filter(
            array_map('trim', explode(',', $emailList)),
            fn($email) => !in_array($email, $blockedEmails),
        );
    }

    private function sendEmail($datas)
    {
        if (empty($datas['to'])) {
            return;
        }

        Mail::mailer(Setting::ADMIN_SUPPORT_EMAIL)->send(new SendMailNotifyChangePassword($datas));
    }

    private function generateQRCodes($user, $data): array
    {
        $warehouses = $user->warehouses;
        if ($user->is_admin) {
            $warehouses = Warehouse::all();
        }

        // Invalidate all existing tokens before generating new QR 
        DB::beginTransaction();
        try {
            $user->expired_at = Carbon::now();
            $user->save();

            $paths = [];
            foreach ($warehouses as $warehouse) {
                if (!empty($data['password'])) {
                    $paths[] = $this->generateAppQRCode($warehouse, $user);
                }
            }
            DB::commit();
            return $paths;
        } catch (Throwable $e) {
            $this->error('Failed to update token expiration: ' . $e->getMessage());
            DB::rollBack();
            return [];
        }
    }

    private function generateAppQRCode($warehouse, $user): string|null
    {
        if (!$user->include_app_qr_code || empty($user->change_app_password_status)) {
            Log::debug('Continue');
            return null;
        }

        try {
            $filename = $this->getQRCodeFilename($warehouse->name, $user->change_app_password_status);

            $customClaims = [
                'warehouse' => [
                    'id' => $warehouse->id
                ],
                'iat' => Carbon::now()->timestamp
            ];

            $customToken = JWTAuth::claims($customClaims)->fromUser($user);

            return $this->generateWarehouseQRCodePDF($warehouse->name, $customToken, $filename, $user->id);
        } catch (\Exception $e) {
            Log::error('Failed to generate QR code: ' . $e->getMessage());
            return null;
        }
    }

    private function getQRCodeFilename(string $warehouseName, string $frequency): string
    {
        if ($frequency === 'weekly') {
            $fromDate = now()->format('Ymd');
            $toDate = now()->addWeek()->format('Ymd');
            return "{$warehouseName}_Warehouse_QR_{$fromDate}_to_{$toDate}.pdf";
        }

        $month = now()->format('F');
        $year = now()->format('Y');
        return "{$warehouseName}_Warehouse_QR_{$month}{$year}.pdf";
    }

    /**
     * Generate PDF with warehouse QR code
     */
    private function generateWarehouseQRCodePDF(string $warehouseName, string $qrContent, string $filename, int $userId): string
    {
        $path = $userId . DIRECTORY_SEPARATOR . $filename;
        // Generate QR Code image as base64
        $qrSvg = QrCode::size(500)->generate($qrContent);

        // Render HTML view for PDF
        $html = view('pdf.qr_code', [
            'warehouseName' => $warehouseName,
            'qrBase64' => base64_encode($qrSvg)
        ])->render();

        // Generate PDF
        $pdf = Pdf::loadHTML($html);
        // Review PDF output quality and settings
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => false,
            'isRemoteEnabled' => false,
            'defaultFont' => 'sans-serif'
        ]);

        Storage::disk('public')->put($path, $pdf->output());

        return $path;
    }
}
