<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use Illuminate\Console\Command;

class ManualHook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:manual-hook {type?} {id?} {start_date?} {end_date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $id = empty($this->argument('id')) ? 0 : $this->argument('id');
        $type = empty($this->argument('id')) ? 0 : $this->argument('type');
        switch ($type) {
            case 'order':
                handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $id);
                $this->info('hook order status: ' . $id);
                break;
            case 'shipment':
                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $id);
                $this->info('hook tracking : ' . $id);
                break;
            case 'store':
                $start_date = $this->argument('start_date');
                $end_date =  $this->argument('end_date');
                $this->retryStore($id, $start_date, $end_date);
                break;
            default:
                $this->warn('Please input type: order or shipment');
                break;
        }

    }

    public function retryStore($id, $start_date, $end_date)
    {
        $order = SaleOrder::where('store_id', $id)->where('order_date', '>=', $start_date)->where('order_date', '<=', $end_date)->get();
        foreach ($order as $item) {
            $this->info('hook order status: ' . $item->id, ' .external_number: ' . $item->external_number);
            handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $item->id);
            handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $item->id);
        }
    }
}
