<?php

namespace App\Console\Commands;

use App\Models\PricingHistory;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\Store;
use App\Models\StorePricingHistory;
use App\Repositories\SaleOrderPricingRepository;
use App\Repositories\StoreProductRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

class ImportStoreProductPricing extends Command
{
    protected StoreProductRepository $storeProductPricingRepo;

    protected SaleOrderPricingRepository $saleOrderPricingRepo;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:store-product-pricing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'import store product pricing';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(StoreProductRepository $storeProductPricingRepo, SaleOrderPricingRepository $saleOrderPricingRepo)
    {
        parent::__construct();

        $this->storeProductPricingRepo = $storeProductPricingRepo;

        $this->saleOrderPricingRepo = $saleOrderPricingRepo;
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        while (true) {
            setTimezone();
            $yesterday = Carbon::yesterday('America/Los_Angeles');
            $nowPST = Carbon::now('America/Los_Angeles')->toDateTimeString();
            $startYesterday = $yesterday->copy()->startOfDay()->toDateTimeString();
            $endYesterday = $yesterday->copy()->endOfDay()->toDateTimeString();
            $this->processClonePricingPending($nowPST, $startYesterday, $endYesterday);
            $this->processUploadPricingPending($nowPST, $startYesterday, $endYesterday);
            $this->info('Waiting...');
            sleep(600);
        }
    }

    public function processClonePricingPending($nowPST, $startYesterday, $endYesterday): bool
    {
        $clonePricingPending = StorePricingHistory::whereNotNull('effective_from')
            ->where('status', StorePricingHistory::PENDING_STATUS)
            ->where('effective_from', '<=', $nowPST)
            ->get();

        if ($clonePricingPending->isEmpty()) {
            return false;
        }

        foreach ($clonePricingPending as $pending) {
            echo "Clone pricing store {$pending->store_id}" . PHP_EOL;
            $store = Store::where('id', $pending->store_id)->first();
            if ($store->is_calculate_price && $store->payment_terms != Store::STORE_PREPAID) {
                if (!$this->ensureSnapshotGenerated($pending->store_id, $startYesterday, $endYesterday)) {
                    $this->warn("Store {$pending->store_id} vẫn còn đơn chưa có pricingSnapshot, skip...");

                    continue;
                }
            }
            $result = $this->storeProductPricingRepo->clonePricingPending($pending);
            $pending->status = $result ? StorePricingHistory::APPLIED_STATUS : StorePricingHistory::ERROR_STATUS;
            $pending->save();
        }

        return true;
    }

    public function processUploadPricingPending($nowPST, $startYesterday, $endYesterday): bool
    {
        $uploadPricingPending = PricingHistory::whereNotNull('effective_from')
            ->where('status', PricingHistory::PENDING_STATUS)
            ->where('effective_from', '<=', $nowPST)
            ->get();

        if ($uploadPricingPending->isEmpty()) {
            return false;
        }
        foreach ($uploadPricingPending as $csvPending) {
            echo "Import pricing store {$csvPending->store_id}" . PHP_EOL;

            try {
                $store = Store::where('id', $csvPending->store_id)->first();
                if ($store->is_calculate_price && $store->payment_terms != Store::STORE_PREPAID) {
                    if (!$this->ensureSnapshotGenerated($csvPending->store_id, $startYesterday, $endYesterday)) {
                        $this->warn("Store {$csvPending->store_id} vẫn còn đơn chưa có pricingSnapshot, skip...");

                        continue;
                    }
                }

                $this->importCsvFile($csvPending);
                $csvPending->status = PricingHistory::APPLIED_STATUS;
                $csvPending->save();
                echo 'Success' . PHP_EOL;
            } catch (\Throwable $exception) {
                echo 'Failed' . PHP_EOL;
                $csvPending->status = PricingHistory::ERROR_STATUS;
                $csvPending->save();

                Log::error('Error in processUploadPricingPending: ' . $exception->getMessage(), [
                    'csvPending_id' => $csvPending->id ?? null,
                    'exception' => $exception,
                ]);
            }
        }

        return true;
    }

    private function importCsvFile($csvPending)
    {
        try {
            $fileContents = file_get_contents($csvPending->url);
            if ($fileContents === false) {
                throw new \Exception("Failed to get file contents from URL: {$csvPending->url}");
            }
            $tempFilePath = $this->getTempFilePath($csvPending->id);
            file_put_contents($tempFilePath, $fileContents);
            $uploadedFile = new \Illuminate\Http\UploadedFile(
                $tempFilePath,
                'import_file.xlsx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                null,
                true,
            );

            $sheets = (new FastExcel)->withSheetsNames()->importSheets($uploadedFile);
            $arraySheet = $sheets->toArray();
            $store = Store::find($csvPending->store_id);
            if (!$store) {
                throw new \Exception("Store not found for store_id: {$csvPending->store_id}");
            }
            $result = $this->storeProductPricingRepo->importStorePricing(
                $store,
                $arraySheet,
                $uploadedFile,
                true,
                null,
                false,
            );
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            if (!$result) {
                throw new \Exception("Failed to import store pricing for store_id: {$csvPending->store_id}");
            }

            return true;
        } catch (\Throwable $exception) {
            Log::error('Error in importCsvFile: ' . $exception->getMessage(), [
                'csvPending_id' => $csvPending->id ?? null,
                'exception' => $exception
            ]);
            throw $exception;
        }
    }

    private function getTempFilePath($id)
    {
        $tempFolderPath = storage_path('app/public/pricing/');
        if (!is_dir($tempFolderPath)) {
            mkdir($tempFolderPath, 0777, true);
        }

        return $tempFolderPath . 'temp_import_file_' . $id . '.xlsx';
    }

    public function ensureSnapshotGenerated($storeId, $start, $end): bool
    {
        $query = SaleOrder::query()
            ->from(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->whereBetween('created_at', [$start, $end])
            ->where('store_id', $storeId)
            ->whereNull('calculated_at')
            ->whereNotIn('order_status', [SaleOrder::REJECTED, SaleOrder::DRAFT])
            ->whereDoesntHave('pricingSnapshot');

        if ($query->exists()) {
            $this->saveSnapshotPricingForOrdersCreatedYesterdayWithoutSnapshot($storeId, $start, $end);
        }

        return !$query->exists();
    }

    //Đảm bảo trước khi import giá mới thì tất cả các order tạo ngày hôm qua phải có snapshort Pricing
    public function saveSnapshotPricingForOrdersCreatedYesterdayWithoutSnapshot($storeId, $start, $end)
    {
        $i = 1;
        SaleOrder::query()
            ->from(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->with([
                'items',
                'items.getTypeProduct',
                'addressSaleOrder' => function ($queryAddress) {
                    $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
                }
            ])
            ->whereNotIn('order_status', [SaleOrder::REJECTED, SaleOrder::DRAFT])
            ->whereDoesntHave('pricingSnapshot')
            ->whereNull('calculated_at')
            ->where('store_id', $storeId)
            ->whereBetween('created_at', [$start, $end])
            ->chunkById(500, function ($saleOrders) use (&$i) {
                info("Chunk $i has {$saleOrders->count()} sale orders start...");
                foreach ($saleOrders as $saleOrder) {
                    $this->saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrder);
                }
                $i++;
            });
    }
}
