<?php

namespace App\Console\Commands;

use App\Repositories\SaleOrderRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class CoverDataSaleOrderPrintMethodReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cover:sale-order-print-method-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cover data to sale_order_print_method_reports table.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(SaleOrderRepository $saleOrderRepository): void
    {
        $this->info('============ Start ============');
        $startDate = Carbon::createFromFormat('Y-m-d', '2023-01-01');
        $endDate = Carbon::now('America/Los_Angeles')->format('Y-m-d');

        setTimezone();
        while ($startDate < $endDate) {
            $date = $startDate->format('Y-m-d');
            $saleOrderRepository->coverDataItemPrintMethodByDate($date);
            $this->info("============ $date ============");
            $startDate->addDay();
        }

        $this->info('============ End ============');
    }
}
