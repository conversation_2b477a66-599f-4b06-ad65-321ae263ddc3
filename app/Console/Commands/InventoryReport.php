<?php

namespace App\Console\Commands;

use App\Jobs\CalculateInventoryFifoReversedJob;
use App\Repositories\InventoryRepository;
use App\Repositories\PurchaseOrderRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InventoryReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:report {endDate?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'calculate inventory report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(InventoryRepository $inventoryRepository, PurchaseOrderRepository $purchaseOrderRepository)
    {
        try {
            setTimezone();
            $endDate = $this->argument('endDate');
            $endDate = empty($endDate) ? Carbon::now()->subDays(1)->toDateString() : $endDate;
            $nextDayEndDate = empty($endDate) ? Carbon::now()->toDateString() : Carbon::parse($endDate)->addDay()->toDateString();
            $startDate = Carbon::parse($endDate)->startOfMonth()->toDateString();
            $warehouses = DB::table('warehouse')
                ->whereNotNull('code')
                ->select('id', 'name', 'code')
                ->get();

            foreach ($warehouses as $warehouse) {
                $this->info("-------Calculate Inventory Report of warehouse $warehouse->name : $endDate-------");
                $inventoryData = $inventoryRepository->fetchInventoryEndUnit($startDate, $nextDayEndDate, $warehouse->id);

                if (empty($inventoryData)) {
                    continue;
                }

                foreach ($inventoryData as $inventory) {
                    CalculateInventoryFifoReversedJob::dispatch($startDate, $endDate, $inventory, $warehouse->id)
                        ->onQueue(CalculateInventoryFifoReversedJob::QUEUE_NAME);
                    $this->info("-------END-product_id : $inventory->product_id");
                }

                $this->info("-------DONE Inventory Report $warehouse->name");
            }

            $this->info('-------DONE Inventory Report-------');
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->info('-------End-------');
    }
}
