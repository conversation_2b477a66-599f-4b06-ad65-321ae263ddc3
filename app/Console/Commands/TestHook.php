<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class TestHook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:test-hook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

     // dispath job productuon status
        $sql = "SELECT id FROM sale_order WHERE production_status LIKE '{%'";

        $orders = DB::select($sql);
        foreach ($orders as $order) {
            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $order->id);

        }

        // show total order
        $this->info('Total orders with production status: ' . count($orders));


        echo now()::subDays(2)->startOfDay()->toDateTimeString();
        exit;

        $response = Http::timeout(30)->get('https://vnexpress.net');
        if ($response->getStatusCode() == 200) {
            dd(1);
        }
    }
}
