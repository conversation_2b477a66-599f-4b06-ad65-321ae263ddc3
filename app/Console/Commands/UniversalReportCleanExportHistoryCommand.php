<?php

namespace App\Console\Commands;

use App\Models\UniversalReportExportHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UniversalReportCleanExportHistoryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'universal-report:clean-export-history';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up export history for universal reports';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $exportHistories = UniversalReportExportHistory::query()
            ->withoutGlobalScopes(['isOwner'])
            ->where('status', UniversalReportExportHistory::STATUS_COMPLETED)
            ->whereNotNull('file_path')
            ->where('created_at', '<', now()->subDays(90))
            ->get();

        Log::channel('universal_report')
            ->info('Cleaning up export history', [
                'count' => $exportHistories->count(),
                'timestamp' => now()->toDateTimeString(),
            ]);

        foreach ($exportHistories as $export) {
            if (Storage::disk('s3')->exists($export->file_path)) {
                Storage::disk('s3')->delete($export->file_path);
            }

            $export->update([
                'status' => UniversalReportExportHistory::STATUS_EXPIRED,
            ]);
        }
    }
}
