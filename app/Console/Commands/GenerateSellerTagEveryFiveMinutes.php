<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use App\Models\SaleOrderTag;
use App\Models\Tag;
use DB;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Log;

class GenerateSellerTagEveryFiveMinutes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:generate-seller-tags {--start=} {--end=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate seller tags for each sale order item based on style and print method';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start = $this->option('start');
        $end = $this->option('end');

        $from = $start
            ? Carbon::parse($start)->startOfDay()
            : ($end
                ? Carbon::parse($end)->startOfDay()
                : now()->subMinutes(30));

        $to = $end
            ? Carbon::parse($end)->endOfDay()
            : ($start
                ? now()
                : now());

        $from = $from->toDateTimeString();
        $to = $to->toDateTimeString();

        $this->info('Generating seller tags for orders between ' . $from . ' and ' . $to);
        try {
            //xử lý 500 order theo từng batch
            $startId = SaleOrder::where('created_at', '>=', $from)
                ->orderBy('id')
                ->limit(1)
                ->value('id');

            $endId = SaleOrder::where('created_at', '<=', $to)
                ->orderByDesc('id')
                ->limit(1)
                ->value('id');

            if (!$startId || !$endId || $startId > $endId) {
                echo "No sale orders.\n";

                return 0;
            }

            if ($startId && $endId) {
                $this->info('Start ID: ' . $startId);
                $this->info('End ID: ' . $endId);
            }

            $sellerTags = Tag::where('source', 'seller')->pluck('id', 'name')->toArray();
            SaleOrder::whereBetween('id', [$startId, $endId])->orderBy('id')->chunkById(500, function ($saleOrders) use ($sellerTags) {
                $startTime = microtime(true);
                $orderIds = $saleOrders->pluck('id')->toArray();
                $items = DB::table('sale_order_item')
                    ->leftJoin('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item.id')
                    ->leftJoin('product_print_side', 'product_print_side.code', '=', 'sale_order_item_image.print_side')
                    ->leftJoin('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
                    ->whereIn('sale_order_item.order_id', $orderIds)
                    ->leftJoin('product_print_area', function ($join) {
                        $join->on('product_print_area.name', '=', 'product_print_side.name')
                            ->on('product_print_area.product_style_id', '=', 'product_style.id');
                    })
                    ->select([
                        'sale_order_item.id as item_id',
                        'sale_order_item.order_id',
                        'product_print_area.print_method',
                        'product_style.type',
                        'sale_order_item_image.id as image_id',
                        'sale_order_item_image.print_side'
                    ])
                    ->groupBy(
                        'sale_order_item.id',
                        'product_print_area.print_method',
                        'product_style.type',
                        'sale_order_item_image.id',
                    )->orderBy('sale_order_item.order_id', 'ASC')
                    ->get();

                $finalItems = [];

                foreach ($items as $item) {
                    $tagIds = $this->getSellerTag($item->type, strtoupper($item->print_method ?? ''), $sellerTags);

                    foreach ($tagIds as $tagId) {
                        $key = $item->order_id . '-' . $tagId;
                        $finalItems[$key] = [
                            'order_id' => $item->order_id,
                            'tag_id' => $tagId,
                            'updated_at' => now(),
                            'created_at' => now(),
                        ];
                    }
                }

                SaleOrderTag::upsert(
                    $finalItems,
                    ['order_id', 'tag_id'],
                    ['tag_id', 'updated_at'],
                );

                $duration = round(microtime(true) - $startTime, 2);
                echo count($finalItems) . ' seller tags generated in ' . $duration . ' seconds' . "\n";
            });
        } catch (\Throwable $th) {
            Log::error('Error generating seller tags: ' . $th->getMessage());
            throw $th;
        }

        return 0;
    }

    private function getSellerTag($type, $printMethod, $tags)
    {
        $styleTypeTag = Tag::getProductStyleTypeTag();
        $result = [];

        if ($printMethod === Tag::EMBROIDERY_TAG && isset($tags['Embroidery'])) {
            $result[] = $tags['Embroidery'];
        } elseif ($printMethod === Tag::BLANK_TAG && isset($tags['Blank'])) {
            $result[] = $tags['Blank'];
        }

        foreach ($styleTypeTag as $tagName => $types) {
            if (in_array($type, $types) && isset($tags[$tagName])) {
                $result[] = $tags[$tagName];
                break;
            }
        }

        return $result;
    }
}
