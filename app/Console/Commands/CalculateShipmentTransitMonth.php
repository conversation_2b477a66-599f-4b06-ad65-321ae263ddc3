<?php

namespace App\Console\Commands;

use App\Models\ShipmentTransitReport;
use App\Models\ShipmentTransitReportByMonth;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateShipmentTransitMonth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:shipment-transit-month';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate shipment transit month for shipment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->calculate();
        DB::disconnect();
        sleep(60);
    }

    public function calculate()
    {
        $timeZone = 'America/Los_Angeles';
        $time = now();
        $i = 1;
        $this->info('Start calculate shipment transit month...');

        // check time excution


        $startId = ShipmentTransitReport::whereNull('calculated_at')
            ->where('created_at', '>=', now()->subDays(30))
            ->orderBy('created_at')
            ->orderBy('id')
            ->value('id');

        if (!$startId) {
            $this->info('No shipment reports found to calculate.');
            return;
        }

        ShipmentTransitReport::whereNull('calculated_at')
            ->where('id', '>', $startId)
            ->chunkById(500, function ($shipmentReports) use ($timeZone, $time, &$i) {
                $this->info("Chunk $i has " . $shipmentReports->count() . ' shipment fulfillment month start...') . PHP_EOL;

                foreach ($shipmentReports as $shipmentReport) {
                    try {
                        $startTime = Carbon::parse($shipmentReport->start_time)->tz($timeZone);
                        $reportMonth = ShipmentTransitReportByMonth::where('warehouse_id', $shipmentReport->warehouse_id)
                            ->where('destination_id', $shipmentReport->destination_id)
                            ->where('month_year', $startTime->format('m-Y'))
                            ->where('shipment_service', $shipmentReport->shipment_service)
                            ->where('shipment_carrier', $shipmentReport->shipment_carrier)
                            ->first();

                        if (!$reportMonth) {
                            ShipmentTransitReportByMonth::create([
                                'warehouse_id' => $shipmentReport->warehouse_id,
                                'month_year' => $startTime->format('m-Y'),
                                'destination_id' => $shipmentReport->destination_id,
                                'shipment_service' => $shipmentReport->shipment_service,
                                'shipment_carrier' => $shipmentReport->shipment_carrier,
                                'total_transit_day' => $shipmentReport->transit_day,
                                'total_fulfillment_day' => $shipmentReport->fulfillment_day,
                                'total_shipments' => 1,
                            ]);
                        } else {
                            $reportMonth->total_shipments += 1;
                            $reportMonth->total_transit_day += $shipmentReport->transit_day;
                            $reportMonth->total_fulfillment_day += $shipmentReport->fulfillment_day;
                            $reportMonth->save();
                        }
                        $shipmentReport->calculated_at = now();
                        $shipmentReport->save();
                    } catch (\Throwable $th) {
                        $this->error("Calculate shipment report {$shipmentReport->id} error: " . $th->getMessage());
                    }
                }
                if ($time->diffInMinutes(now()) >= 30) {
                    $this->info('Calculation time exceeds 30 minutes');

                    return false;
                }
                $i++;
            });
        $this->info('End');
    }
}
