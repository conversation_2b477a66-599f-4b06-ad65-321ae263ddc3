<?php

namespace App\Console\Commands\EmployeeWorkLogs;

use App\Models\AdjustPullingShelves;
use App\Models\EmployeeWorkLog;
use App\Models\InternalRequest;
use App\Models\InventoryDeduction;
use App\Models\RbtPerformanceReport;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateEmployeePullingWorkLog extends Command
{
    use CalculateEmployeeWorkLogTrait;

    // Thêm tham số {warehouse_id?} với giá trị mặc định là 1
    protected $signature = 'worklog:calculate-pulling {start_date?} {end_date?}';

    protected $description = 'Calculate pulling work logs for employees within a given date range and warehouse';

    public function handle()
    {
        // Set time zone to PST (if applicable, otherwise use Laravel's default timezone settings)
        setTimezone();

        // Lấy department_id từ bảng department có tên 'Pulling'
        $departmentId = DB::table('department')->where('name', 'Pulling')->value('id');

        // Kiểm tra và lấy giá trị start_date, end_date
        $startDate = $this->argument('start_date') ? Carbon::parse($this->argument('start_date'))->startOfDay()->toDateTimeString() : Carbon::now()->subDays(7)->startOfDay()->toDateTimeString();
        $endDate = $this->argument('end_date') ? Carbon::parse($this->argument('end_date'))->endOfDay()->toDateTimeString() : Carbon::now()->subDay()->endOfDay()->toDateTimeString();
        $warehouses = Warehouse::all();
        $formattedStartDate = Carbon::parse($startDate)->format('Y-m-d');
        $formattedEndDate = Carbon::parse($endDate)->format('Y-m-d');

        foreach ($warehouses as $warehouse) {
            $warehouseId = $warehouse->id ?? 1;
            $this->info("Calculating pulling work logs from $startDate to $endDate for department ID: $departmentId and warehouse ID: $warehouseId...");
            $this->pullingApp($startDate, $endDate, $warehouseId, $departmentId);
            $this->confirmInternalRequestApp($startDate, $endDate, $warehouseId, $departmentId);
            $this->confirmRBTInternalRequest($startDate, $endDate, $warehouseId, $departmentId);
            $this->adjustPullingShelves($startDate, $endDate, $warehouseId, $departmentId);
            $this->rbtPulling($formattedStartDate, $formattedEndDate, $warehouseId, $departmentId);
        }

        $this->updateWorkHours('pulling', 'pulling', $startDate, $endDate);
        $this->updateWorkHours('adjust_pullling_shelves', 'adjust_pulling_shelves', $startDate, $endDate);
        $this->updateWorkHours(TimeTracking::JOB_TYPE_RBT_PULLING, TimeTracking::JOB_TYPE_RBT_PULLING, $startDate, $endDate);

        // Reset time zone
        setTimezoneDefault();
    }

    /**
     * Execute the Pulling performance calculation.
     */
    public function pullingApp($startDate, $endDate, $warehouseId, $departmentId)
    {
        // Insert the pulling work logs based on the data from inventory_deduction
        $deduction = InventoryDeduction::join('employee', 'employee.id', '=', 'inventory_deduction.employee_id')
            ->where('inventory_deduction.warehouse_id', $warehouseId)
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', 'pulling')
            ->where('inventory_deduction.is_deleted', 0)
            ->where('inventory_deduction.created_at', '>=', $startDate)
            ->where('inventory_deduction.created_at', '<=', $endDate)
            ->selectRaw('inventory_deduction.employee_id, inventory_deduction.warehouse_id, DATE(inventory_deduction.created_at) AS work_date, SUM(inventory_deduction.quantity) AS total_tasks')
            ->groupBy('work_date', 'employee_id')
            ->get();

        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', 'pulling')
            ->delete();
        $data = [];
        foreach ($deduction as $item) {
            $data[] = [
                'employee_id' => $item->employee_id,
                'warehouse_id' => $warehouseId,
                'work_date' => $item->work_date,
                'department_id' => $departmentId,
                'task_type' => 'pulling',
                'total_tasks' => $item->total_tasks,
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('Pulling work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }

    public function confirmInternalRequestApp($startDate, $endDate, $warehouseId, $departmentId)
    {
        $internalRequests = InternalRequest::join('employee', 'employee.id', '=', 'internal_request.employee_confirm_id')
            ->where('internal_request.warehouse_id', $warehouseId)
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', 'pulling')
            ->where('internal_request.is_deleted', 0)
            ->where('internal_request.confirmed_at', '>=', $startDate)
            ->where('internal_request.confirmed_at', '<=', $endDate)
            ->where('internal_request.is_rbt', false)
            ->selectRaw('internal_request.employee_confirm_id, DATE(internal_request.confirmed_at) AS work_date, COUNT(internal_request.id) AS total_tasks')
            ->groupBy('work_date', 'employee_confirm_id')
            ->get();

        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', 'confirm_internal_request')
            ->delete();
        $data = [];
        foreach ($internalRequests as $item) {
            $data[] = [
                'employee_id' => $item->employee_confirm_id,
                'warehouse_id' => $warehouseId,
                'work_date' => $item->work_date,
                'department_id' => $departmentId,
                'task_type' => 'confirm_internal_request',
                'total_tasks' => $item->total_tasks,
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('Confirm internal request work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }

    public function confirmRBTInternalRequest($startDate, $endDate, $warehouseId, $departmentId)
    {
        $internalRequests = InternalRequest::join('employee', 'employee.id', '=', 'internal_request.employee_confirm_id')
            ->where('internal_request.warehouse_id', $warehouseId)
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', 'pulling')
            ->where('internal_request.is_deleted', 0)
            ->where('internal_request.confirmed_at', '>=', $startDate)
            ->where('internal_request.confirmed_at', '<=', $endDate)
            ->where('internal_request.is_rbt', true)
            ->selectRaw('internal_request.employee_confirm_id, DATE(internal_request.confirmed_at) AS work_date, COUNT(internal_request.id) AS total_tasks')
            ->groupBy('work_date', 'employee_confirm_id')
            ->get();

        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', 'rbt_confirm_internal_request')
            ->delete();
        $data = [];
        foreach ($internalRequests as $item) {
            $data[] = [
                'employee_id' => $item->employee_confirm_id,
                'warehouse_id' => $warehouseId,
                'work_date' => $item->work_date,
                'department_id' => $departmentId,
                'task_type' => 'rbt_confirm_internal_request',
                'total_tasks' => $item->total_tasks,
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('Confirm RBT internal request work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }

    public function adjustPullingShelves($startDate, $endDate, $warehouseId, $departmentId)
    {
        // Insert the pulling work logs based on the data from inventory_deduction
        $adjustPullingShelves = AdjustPullingShelves::join('employee', 'employee.code', '=', 'adjust_pulling_shelves.employee_id')
            ->where('adjust_pulling_shelves.warehouse_id', $warehouseId)
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', 'pulling')
            ->where('adjust_pulling_shelves.created_at', '>=', $startDate)
            ->where('adjust_pulling_shelves.created_at', '<=', $endDate)
            ->selectRaw('employee.id as employee_id, DATE(adjust_pulling_shelves.created_at) AS work_date, sum(adjust_pulling_shelves.product_on_hand) AS total_tasks')
            ->groupBy('work_date', 'employee_id')
            ->get();

        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', 'adjust_pulling_shelves')
            ->delete();
        $data = [];
        foreach ($adjustPullingShelves as $item) {
            $data[] = [
                'employee_id' => $item->employee_id,
                'warehouse_id' => $warehouseId,
                'work_date' => $item->work_date,
                'department_id' => $departmentId,
                'task_type' => 'adjust_pulling_shelves',
                'total_tasks' => $item->total_tasks,
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('adjust pulling shelves work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }

    public function rbtPulling($startDate, $endDate, $warehouseId, $departmentId)
    {
        // Insert the pulling work logs based on the data from rbt_performance_report
        $adjustPullingShelves = RbtPerformanceReport::join('employee', 'employee.id', '=', 'rbt_performance_report.employee_id')
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', 'pulling')
            ->where('rbt_performance_report.date', '>=', $startDate)
            ->where('rbt_performance_report.date', '<=', $endDate)
            ->selectRaw('employee.id as employee_id, rbt_performance_report.date AS work_date, sum(rbt_performance_report.quantity) AS total_tasks')
            ->groupBy('work_date', 'employee_id')
            ->get();

        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', TimeTracking::JOB_TYPE_RBT_PULLING)
            ->delete();
        $data = [];
        foreach ($adjustPullingShelves as $item) {
            $data[] = [
                'employee_id' => $item->employee_id,
                'warehouse_id' => $warehouseId,
                'work_date' => $item->work_date,
                'department_id' => $departmentId,
                'task_type' => TimeTracking::JOB_TYPE_RBT_PULLING,
                'total_tasks' => $item->total_tasks,
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('rbt pulling work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }
}
