<?php

namespace App\Console\Commands\EmployeeWorkLogs;

use App\Models\EmployeeWorkLog;
use App\Models\SaleOrderItemQualityControl;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateEmployeeQcWorkLog extends Command
{
    use CalculateEmployeeWorkLogTrait;

    // Thêm tham số {warehouse_id?} với giá trị mặc định là 1
    protected $signature = 'worklog:calculate-qc {start_date?} {end_date?}';

    protected $description = 'Calculate QC work logs for employees within a given date range and warehouse';

    protected string $departmentName = 'QA';

    protected string $jobType = 'qc';

    public function handle()
    {
        setTimezone();

        // Lấy department_id từ bảng department có tên 'QC'
        $departmentId = DB::table('department')->where('name', $this->departmentName)->value('id');

        // Kiểm tra và lấy giá trị start_date, end_date, warehouse_id
        $startDate = $this->argument('start_date') ? Carbon::parse($this->argument('start_date'))->startOfDay()->toDateTimeString() : Carbon::now()->subDay()->startOfDay()->toDateTimeString();
        $endDate = $this->argument('end_date') ? Carbon::parse($this->argument('end_date'))->endOfDay()->toDateTimeString() : Carbon::now()->subDay()->endOfDay()->toDateTimeString();
        $warehouses = Warehouse::all();
        $this->info("Calculating QC work logs from $startDate to $endDate for department ID: $departmentId...");
        foreach ($warehouses as $warehouse) {
            $warehouseId = $warehouse->id ?? 1;
            $this->info("Calculating QC work logs from $startDate to $endDate for department ID: $departmentId and warehouse ID: $warehouseId...");
            $this->qcApp($startDate, $endDate, $warehouseId, $departmentId);
        }

        // call the function to calculate the QC work logs time
        $this->updateWorkHours('quality_control', $this->jobType, $startDate, $endDate);
        setTimezoneDefault();
    }

    /**
     * Execute the QC performance calculation.
     */
    public function qcApp($startDate, $endDate, $warehouseId, $departmentId)
    {
        $this->info("Start calculating QC work logs from $startDate to $endDate for department ID: $departmentId and warehouse ID: $warehouseId...");

        // Insert the QC work logs based on the data from sale_order_item_quality_control
        $qualityControl = SaleOrderItemQualityControl::join('employee', 'employee.id', '=', 'sale_order_item_quality_control.employee_id')
            ->where('employee.warehouse_id', $warehouseId)
            ->where('employee.department', $this->departmentName)
            ->where('sale_order_item_quality_control.created_at', '>=', $startDate)
            ->where('sale_order_item_quality_control.created_at', '<=', $endDate)
            ->selectRaw('employee.id AS employee_id, COUNT(DISTINCT label_id) AS total_qc, DATE(sale_order_item_quality_control.created_at) AS work_date')
            ->groupBy('work_date', 'employee.id')
            ->get();
        EmployeeWorkLog::where('work_date', '>=', $startDate)
            ->where('work_date', '<=', $endDate)
            ->where('warehouse_id', $warehouseId)
            ->where('department_id', $departmentId)
            ->where('task_type', $this->jobType)
            ->delete();
        $data = [];
        foreach ($qualityControl as $item) {
            $data[] = [
                'employee_id' => $item->employee_id,
                'warehouse_id' => $warehouseId,
                'department_id' => $departmentId,
                'work_date' => $item->work_date,
                'task_type' => $this->jobType,
                'total_tasks' => $item->total_qc
            ];
        }
        if (!empty($data)) {
            EmployeeWorkLog::insert($data);
        }
        // Output thông báo hoàn thành
        $this->info('QC work log calculation completed for warehouse ID: ' . $warehouseId . '!');

        return 0;
    }
}
