<?php

namespace App\Console\Commands;

use App\Repositories\InvoiceRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CreateInvoice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:invoice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create automatic invoice';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
        $this->info('-------Automatic create invoice-------');
        while (true) {
            $currentHourPST = Carbon::now('America/Los_Angeles')->hour;
            if ($currentHourPST < 2) {
                $this->info('Current time is before 4 AM PST, sleeping...');
                sleep(600); // Chờ 10 phút

                continue; // Tiếp tục vòng lặp
            }
            $this->createInvoice($invoiceRepository);
            sleep(600);
        }
    }

    public function createInvoice(InvoiceRepository $invoiceRepository)
    {
        try {
            $invoiceRepository->autoCreateInvoice(fn ($message) => $this->info($message));
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
    }
}
