<?php

namespace App\Console\Commands;

use App\Models\PrintMethod;
use App\Models\ReportOffenderProcessingTime;
use App\Models\SaleOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CalculateReportOffenderProcessingTime extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate-report-offender-processing-time';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'calculate-report-offender-processing-time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startTime = microtime(true);
        $uuid = Str::uuid();
        Log::info('CalculateReportOffenderProcessingTime.handle start: ' . $uuid);
        $subQuery = DB::table('sale_order')
            ->selectRaw('
                  sale_order_item_barcode.id as item_barcode_id,
                  sale_order_item_barcode.store_id,
                  sale_order_item_barcode.warehouse_id,
                  sale_order.order_type,
                  sale_order.created_at as order_date,
                  sale_order_item_barcode.print_method  ,
                  sale_order_item_barcode.print_barcode_at,
                  sale_order_item_barcode.pulled_at,
                  sale_order_item_barcode.printed_at,
                  sale_order_item_barcode.qc_at,
                  max(shipment_label_printed.printed_date) as printed_date,
                  sale_order_item_barcode.folded_at
              ')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->leftJoin('shipment_label_printed', 'shipment_label_printed.shipment_id', '=', 'sale_order.shipment_id')
            ->leftJoin('shipment_transit', 'shipment_transit.shipment_id', '=', 'sale_order.shipment_id')
            ->whereNull('shipment_transit.in_transit_at')
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.print_method', PrintMethod::DTG)
            ->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_NORMAL, SaleOrder::ORDER_TYPE_LABEL_ORDER, SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LICENSE_ORDER])
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED])
            ->where('sale_order.is_test', 0)
            ->whereRaw('sale_order.created_at > NOW() - INTERVAL 15 DAY')
            ->groupBy(['sale_order.id', 'sale_order_item_barcode.id']);
        DB::connection()->enableQueryLog();
        DB::table(DB::raw("({$subQuery->toSql()}) as tmp"))
            ->mergeBindings($subQuery)
            ->selectRaw('
            tmp.item_barcode_id,
            tmp.store_id,
            tmp.warehouse_id,
            tmp.order_type,
            tmp.order_date,
            tmp.print_method,
            calc_business_hours(order_date, print_barcode_at) AS print_wip_time,
            calc_business_hours(print_barcode_at, pulled_at) AS pulling_time,
            calc_business_hours(pulled_at, printed_at) AS printing_time,
            calc_business_hours(printed_at, qc_at) AS qc_time,
            calc_business_hours(qc_at, printed_date) AS labeling_time,
            calc_business_hours(printed_date, folded_at) AS folding_time
            ')
            ->chunkById(10000, function ($items) {
                $data = array_map(function ($item) {
                    return collect($item)->toArray();
                }, $items->toArray());
                collect($data)->chunk(500)->each(function ($chunk) {
                    $this->info('Chunk size: ' . count($chunk));
                    ReportOffenderProcessingTime::upsert($chunk->toArray(), ['item_barcode_id'], [
                        'store_id',
                        'warehouse_id',
                        'order_type',
                        'order_date',
                        'print_method',
                        'print_wip_time',
                        'pulling_time',
                        'printing_time',
                        'qc_time',
                        'labeling_time',
                        'folding_time',
                    ]);
                });

            }, 'item_barcode_id');
        $queries = DB::getQueryLog();
        Log::info('CalculateReportOffenderProcessingTime.handle end: ' . $uuid, [
            'time' => microtime(true) - $startTime,
            'queries' => $queries,
        ]);
        $this->info('CalculateReportOffenderProcessingTime Success, Time exe: ' . (microtime(true) - $startTime));

        return true;
    }
}
