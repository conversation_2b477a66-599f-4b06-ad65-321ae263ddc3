<?php

namespace App\Console\Commands;

use App\Models\PeakShippingFee;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\Promotion;
use App\Models\PromotionType;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderCalculateFailed;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemCalculateFailed;
use App\Models\SaleOrderPromotion;
use App\Models\SaleOrderSurchargeFee;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use App\Models\Tag;
use App\Repositories\SaleOrderRepository;
use App\Repositories\SurchargeFeeRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculatePriceForSeller extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:price';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate price for seller';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $this->calculate();
            $this->info('Waiting...');
            sleep(120);
        }
    }

    public function calculate()
    {
        $this->calculatePriceByOrderStatus();
        $this->calculatePriceByOrderStatus(SaleOrder::STATUS_LATE_CANCELLED);
    }

    private function calculatePriceByOrderStatus($orderStatus = null)
    {
        $this->info('---Start calculate sale order price for seller v240902---');
        $storeIds = Store::where('is_calculate_price', true)->get('id')->pluck('id');
        $productPrintSides = ProductPrintSide::all();
        $peakShippingFee = PeakShippingFee::first();
        $tagAdditionalService = Tag::select('id', 'name')->where('is_additional_service', true)->get();
        $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
        $i = 1;
        $time = now();
        $date = now()->subDays(60)->format('Y-m-d');
        // get id order 60 days ago
        $startId = SaleOrder::where('created_at', '>=', $date)->min('id');
        SaleOrder::with([
            'items.embroideryTask',
            'shipmentDefault',
            'addressSaleOrder' => function ($queryAddress) {
                $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
            }
        ])
            ->whereNull('calculated_at')
            ->whereIn('store_id', $storeIds)
            ->whereHas('pricingSnapshot')
            ->where(function ($query) use ($orderStatus) {
                if ($orderStatus == SaleOrder::STATUS_LATE_CANCELLED) {
                    $query->where('order_status', SaleOrder::STATUS_LATE_CANCELLED);
                } else {
                    $query->whereHas('shipmentDefault')->where('order_status', '!=', SaleOrder::STATUS_LATE_CANCELLED);
                }
            })
            ->whereNotIn('id', SaleOrderCalculateFailed::select('order_id'))
            ->where('id', '>=', $startId)
            ->whereDoesntHave('items', function ($query) {
                $query->where('ink_color_status', '!=', SaleOrderItem::DETECT_INK_COLOR_DONE);
            })
            ->chunkById(100, function ($saleOrders) use (&$i, $productPrintSides, $time, $peakShippingFee, $tagAdditionalService, $tagLabel) {
                $this->info("Chunk $i has " . $saleOrders->count() . ' sale orders start...');
                foreach ($saleOrders as $saleOrder) {
                    try {
                        $this->handleCalculatePriceOrderViaPricingSnapshot($saleOrder, $productPrintSides, $tagAdditionalService, $peakShippingFee, $tagLabel);
                    } catch (\Throwable $th) {
                        $this->error("Error: $saleOrder->id failed. Reason: " . $th->getMessage());
                        $this->error($th->getLine());
                        SaleOrderCalculateFailed::updateOrCreate([
                            'order_id' => $saleOrder->id,
                            'store_id' => $saleOrder->store_id,
                        ], [
                            'failed_at' => now(),
                        ]);
                    }
                }
                $this->info("Chunk $i ended.");
                if ($time->diffInMinutes(now()) >= 5) {
                    $this->info('Calculation time exceeds 5 minutes');

                    return false;
                }

                $i++;
            });

        $this->info('---End calculate sale order price for seller---');
    }

    public function handleCalculatePriceOrder($saleOrder, $productPrintSides, $tagAdditionalService, $peakShippingFee, $tagLabel)
    {
        $tag = explode(',', $saleOrder->tag);
        $this->info("Update sale order id: $saleOrder->id start...");
        $this->info('Sale order has: ' . $saleOrder->items->count() . ' items');
        $totalItemPrice = 0;
        $promotions = [];
        $store = Store::where('id', $saleOrder->store_id)->first();
        $orderPromotions = Promotion::where('store_id', $saleOrder->store_id)
            ->where('start_time', '<=', $saleOrder->created_at)
            ->where('is_public', Promotion::ACTIVE)
            ->where(function ($q) use ($saleOrder) {
                $q->whereNull('end_time')
                    ->orWhere('end_time', '>=', $saleOrder->created_at);
            })->get();

        $addressShip = $saleOrder->addressSaleOrder[0];
        // get ship price by product type
        $destination = in_array(strtoupper($addressShip->country), StoreShipment::DOMESTIC_SHIPPING) ? StoreShipment::DOMESTIC : StoreShipment::INTERNATIONAL;
        $j = 0;
        $shipPriceMax = ['price' => 0, 'addition_price' => 0];
        $totalShipPrice = 0;

        //check select store promotion
        $saleOrderItemErrors = [];
        $saleOrderRepository = app()->make(SaleOrderRepository::class);
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $isShipBySwiftpod = $saleOrder->shipmentDefault && (
            $saleOrder->shipmentDefault->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD
            || is_null($saleOrder->shipmentDefault->shipment_account)
        ) && $saleOrder->order_type !== SaleOrder::ORDER_TYPE_LABEL_ORDER && (($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && !in_array($tagLabel->id, $tag)) || $saleOrder->order_type !== SaleOrder::ORDER_TYPE_NORMAL);

        $tagMugPackaging = $tagAdditionalService->firstWhere('name', Tag::TAG_MUG_PACKAGING);
        $tagHologramSticker = $tagAdditionalService->firstWhere('name', Tag::TAG_HOLOGRAM_STICKER);
        $tagStickerAndBag = $tagAdditionalService->firstWhere('name', Tag::TAG_STICKER_AND_PLASTIC_BAG);
        $tagPlasticBag = $tagAdditionalService->firstWhere('name', Tag::TAG_PLASTIC_BAG);

        $storeSurcharges = SurchargeFee::query()
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->select('surcharge_service.*', 'store_surcharge.value', 'store_surcharge.id as store_surcharge_id')
            ->where('store_id', $saleOrder->store_id)
            ->get();
        foreach ($saleOrder->items as $saleOrderItem) {
            $isErrorOrderItem = false;
            if (!$saleOrderItem->product_id) {
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Product is empty';

                continue;
            }

            //get price and handling fee product
            $baseStoreProduct = StoreProduct::where('store_id', $store->id)
                ->where('product_id', $saleOrderItem->product_id)
                ->where('product_print_area_id', 0)
                ->first();
            if (!$baseStoreProduct) {
                $this->error("Update sale order item id: $saleOrderItem->id failed. Base price not exists.");
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Base price not exists';
                $isErrorOrderItem = true;
            }
            if (is_null($saleOrderItem->print_sides) && $saleOrder->order_type != SaleOrder::ORDER_TYPE_BLANK) {
                $this->error("Update sale order item id: $saleOrderItem->id failed. Print area not exists.");
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Print area not exists';
                $isErrorOrderItem = true;
            }
            if ($isErrorOrderItem) {
                continue;
            }
            $blankPrice = $baseStoreProduct->price;

            if ($saleOrder->store_id == Store::STORE_REDBUBBLE) {
                ///doi voi store RED BUBBLE Shrinkage cost chinh la handling free trong bang store_product_price
                $handlingFeePrice = $baseStoreProduct->handling_fee ?? 0;
            } else {
                $handlingFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_HANDLING;
                });
                $handlingFeePrice = $handlingFee->value ?? 0;
            }
            $baseUnitPrice = $blankPrice + $handlingFeePrice;
            $printSurcharge = 0;

            if (!$isShipBySwiftpod) {
                $printSurcharge = $baseStoreProduct->print_surcharge ?? 0;
            }
            $unitPrice = $baseUnitPrice + $printSurcharge;
            if (!empty($saleOrderItem->print_sides)) {
                //get product print area and calculate price
                $printSides = array_unique(str_split($saleOrderItem->print_sides));
                $productPrintSideName = $productPrintSides
                    ->whereIn('code_wip', $printSides)
                    ->pluck('name');

                $productStyle = ProductStyle::where('sku', $saleOrderItem->product_style_sku)->first();
                foreach ($orderPromotions as $promotion) {
                    if ($promotion->promotion_type_id == PromotionType::ADDITIONAL_PRINT_AREA_ID) {
                        $saleOrderPromotion = $this->handleAdditionalPrintAreaPromotion($saleOrderItem, $promotion, $productStyle, $productPrintSideName);
                        if (!empty($saleOrderPromotion)) {
                            $promotions = array_merge($promotions, $saleOrderPromotion);
                        }
                    }
                }
                $mapProductPrintAreasIds = ProductPrintArea::where('product_style_id', $productStyle->id)
                    ->whereIn('name', $productPrintSideName)
                    ->pluck('id');
                $sumPrintPrice = StoreProduct::where('store_id', $store->id)
                        ->where('product_id', $saleOrderItem->product_id)
                        ->whereIn('product_print_area_id', $mapProductPrintAreasIds)
                        ->sum('print_price') ?? 0;
                $unitPrice += $sumPrintPrice;
            }

            ////Temp
            $orderCostTmp = DB::table('tmp_invoice_order_cost')
                ->select('blank_cost', 'print_cost')
                ->where('external_number', $saleOrder->external_number)
                ->where('external_id', $saleOrderItem->external_id)
                ->first();

            if ($orderCostTmp && $orderCostTmp->blank_cost !== null) {
                $blankPrice = $orderCostTmp->blank_cost;
                $unitPrice = $blankPrice + $orderCostTmp->print_cost;
            }

            $saleOrderItem->unit_price = $unitPrice;
            $saleOrderItem->blank_price = $blankPrice;
            $saleOrderItem->handling_fee = $handlingFeePrice;
            $saleOrderItem->amount_paid = $saleOrderItem->quantity * $unitPrice;
            $totalItemPrice += $saleOrderItem->amount_paid;

            //get ship price by product type and quantity
            if (
                $store->is_calculate_shipping
                && $saleOrder->order_status !== SaleOrder::STATUS_LATE_CANCELLED
                && $isShipBySwiftpod
            ) {
                if (!$saleOrderItem->getTypeProduct) {
                    $this->error("Update sale order item id: $saleOrderItem->id failed. Product style not exist.");
                    $saleOrderItemErrors[$saleOrderItem->id][] = 'Product style not exist';

                    continue;
                }
                $shippingPrice = StoreShipment::where('store_id', $saleOrder->store_id)
                    ->where('status', StoreShipment::STATUS_ACTIVE)
                    ->where('service_type', $saleOrder->shipping_method)
                    ->where('destination', $destination)
                    ->get();

                $storeShipping = $shippingPrice
                    ->sortByDesc(function ($item) use ($saleOrderItem) {
                        return strtolower($item->size) == strtolower($saleOrderItem->product_size_sku) ? 1 : 0;
                    })
                    ->first(function ($item) use ($saleOrderItem) {
                        $check = strtolower($item->product_type) === strtolower($saleOrderItem->getTypeProduct->type);
                        if ($item->size) {
                            $check = $check && strtolower($item->size) === strtolower($saleOrderItem->product_size_sku);
                        }

                        return $check;
                    });
                if (!$storeShipping) {
                    $this->error('Shipping price has not been set yet.');
                    $saleOrderItemErrors[$saleOrderItem->id][] = 'Shipping price has not been set yet.';

                    continue;
                }

                // get first item ship max
                if ($shipPriceMax['price'] < $storeShipping->price) {
                    $shipPriceMax['price'] = $storeShipping->price;
                    $shipPriceMax['addition_price'] = $storeShipping->addition_price;
                }
                $totalShipPrice += $saleOrderItem->quantity * $storeShipping->addition_price;
            }
            //Tinh tiktok fee cho order la label va tiktok
            if ($saleOrder->order_type == SaleOrder::ORDER_TYPE_TIKTOK_ORDER
                || $saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER
                || ($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag))) {
                $tiktokFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_TIKTOK_ORDER_SERVICE;
                });
                if ($tiktokFee) {
                    $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $tiktokFee);
                }
            }
            //Tinh mug packaging fee theo item
            if ($tagMugPackaging && in_array($tagMugPackaging->id, $tag)) {
                $surchargeFeeRepository->handleSurchargeFeePerProductType($storeSurcharges, $saleOrderItem, SurchargeService::TYPE_MUG_PACKAGING);
            }
            if ($tagHologramSticker && in_array($tagHologramSticker->id, $tag)) {
                $surchargeFeeRepository->handleSurchargeFeePerProductType($storeSurcharges, $saleOrderItem, SurchargeService::TYPE_HOLOGRAM_STICKER);
            }
            //Tinh plastic bag theo item
            if (($tagPlasticBag && in_array($tagPlasticBag->id, $tag)) || $saleOrder->plastic_bag) {
                $plasticBagFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_PLASTIC_BAG;
                });
                if ($plasticBagFee) {
                    $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $plasticBagFee);
                }
            }

            $embStitchCountService = null;
            ///Tinh gia stitch count cho don embroidery
            $stitchCountRanges = [
                [10001, 15000, SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES],
                [15001, 20000, SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES],
                [20001, 25000, SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES],
                [25001, 30000, SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES],
            ];
            $surchargeEmb = [];
            foreach ($saleOrderItem->embroideryTask as $embTask) {
                foreach ($stitchCountRanges as [$min, $max, $apiValue]) {
                    if ($embTask->stitch_count >= $min && $embTask->stitch_count <= $max) {
                        $embStitchCountService = $apiValue;
                        break;
                    }
                }
                if ($embStitchCountService) {
                    $surchargeEmb[] = $embStitchCountService;
                }
            }
            if (!empty($surchargeEmb)) {
                $countedValuesEmb = array_count_values($surchargeEmb);
                foreach ($countedValuesEmb as $embSurcharge => $count) {
                    $stickCountSurcharge = $storeSurcharges->first(function ($itemSurcharge) use ($embSurcharge) {
                        return $itemSurcharge->per == SurchargeService::PER_PRINT_AREA
                            && $itemSurcharge->api_value == $embSurcharge;
                    });
                    if ($stickCountSurcharge) {
                        $stickCountSurcharge->value *= $count;
                        $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $stickCountSurcharge);
                    }
                }
            }

            $saleOrderItem->save();
            $j++;
        }

        if (!empty($saleOrderItemErrors)) {
            $this->error("Update sale order id: $saleOrder->id failed. Sale order item failed.");

            SaleOrderCalculateFailed::updateOrCreate([
                'order_id' => $saleOrder->id,
                'store_id' => $saleOrder->store_id,
            ], [
                'failed_at' => now(),
            ]);
            SaleOrderItemCalculateFailed::where('order_id', $saleOrder->id)->delete();
            foreach ($saleOrderItemErrors as $key => $arrErrorOrderItem) {
                foreach ($arrErrorOrderItem as $itemError) {
                    SaleOrderItemCalculateFailed::create([
                        'order_id' => $saleOrder->id,
                        'order_item_id' => $key,
                        'reason' => $itemError,
                        'created_at' => now(),
                    ]);
                }
            }

            return false;
        }

        // calculate ship price
        $totalShipPrice = $totalShipPrice > 0 ? $totalShipPrice - $shipPriceMax['addition_price'] + $shipPriceMax['price'] : 0;
        $saleOrder->amount_paid = $totalItemPrice;
        $saleOrder->shipping_calculate = $totalShipPrice;
        $saleOrder->order_total = $totalItemPrice + $totalShipPrice;
        $saleOrder->calculated_at = now();
        // calculate peak shipping fee
        if ($saleOrder->order_status !== SaleOrder::STATUS_LATE_CANCELLED
            && $peakShippingFee
            && $isShipBySwiftpod
            && $saleOrder->shipmentDefault->created_at->between($peakShippingFee->start_date, $peakShippingFee->end_date)
            && in_array($saleOrder->shipmentDefault->carrier_code, [ShippingCarrier::DHL_ECOMMERCE_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])
            && $destination == StoreShipment::DOMESTIC
        ) {
            $saleOrderRepository->updateOrderSurchargeFee($saleOrder, $peakShippingFee, PeakShippingFee::PEAK_SHIPPING_FEE);
        }
        $saleOrder->save();
        SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->delete();
        SaleOrderItemCalculateFailed::where('order_id', $saleOrder->id)->delete();

        //Tinh sticker and bag theo order
        if ($tagStickerAndBag && in_array($tagStickerAndBag->id, $tag)) {
            $surchargeFeeRepository->handleSurchargeFeePerOrder($storeSurcharges, $saleOrder, SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG);
        }
        if ($saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER
            || ($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag))) {
            $surchargeFeeRepository->handleLabelSurchargeFeePerOrder($storeSurcharges, $saleOrder);
        }
        ///Tinh shipping label fee cho store co setting
        if ($saleOrder->order_status != SaleOrder::STATUS_LATE_CANCELLED) {
            $surchargeFeeRepository->handleSurchargeFeePerOrder($storeSurcharges, $saleOrder, SurchargeService::TYPE_SHIPPING_LABEL_FEE);
        }
        //Neu don la in_production_cancel da duoc tinh Label printing fee,Peak shipping surcharge
        // trong lan calculate price truoc (thuong la don Label) thi se bi remove nhung fee nay di khi calculate price lan sau
        if ($saleOrder->order_status == SaleOrder::STATUS_LATE_CANCELLED) {
            SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
                ->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)
                ->delete();
            $isLabelOrder = $saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER;
            $isNormalOrderWithTag = $saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag);

            if ($isLabelOrder || $isNormalOrderWithTag) {
                $surcharge = $storeSurcharges->first(fn ($item) => $item->per == SurchargeService::PER_ORDER
                    && $item->name == SurchargeService::TYPE_LABEL_PRINTING_FEE,
                );
                if ($surcharge) {
                    SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
                        ->where('surcharge_id', $surcharge->store_surcharge_id)
                        ->delete();
                }
            }

            $shippingLabelSurcharge = $storeSurcharges->first(fn ($item) => $item->per == SurchargeService::PER_ORDER
                && $item->name == SurchargeService::TYPE_SHIPPING_LABEL_FEE,
            );
            if ($shippingLabelSurcharge) {
                SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
                    ->where('surcharge_id', $shippingLabelSurcharge->store_surcharge_id)
                    ->delete();
            }
        }
        SaleOrderPromotion::where('order_id', $saleOrder->id)->delete();
        if (!empty($promotions)) {
            SaleOrderPromotion::insert($promotions);
        }
        $this->info("Update sale order id: $saleOrder->id done.");

        return true;
    }

    public function handleAdditionalPrintAreaPromotion($saleOrderItem, $promotion, $productStyle, $productPrintSideName)
    {
        $areas = empty($promotion->detail?->print_areas) ? $productPrintSideName->toArray() : array_intersect($promotion->detail->print_areas ?? [], $productPrintSideName->toArray());
        $data = [];
        if (!empty($promotion->detail) && $productPrintSideName->count() > 1 && (empty($promotion->detail->product_types) || in_array($productStyle->type, $promotion->detail->product_types)) && count($areas) > 0
        ) {
            foreach ($areas as $area) {
                $data[] = [
                    'promotion_id' => $promotion->id,
                    'order_id' => $saleOrderItem->order_id,
                    'order_item_id' => $saleOrderItem->id,
                    'print_area' => $area,
                    'amount' => $saleOrderItem->quantity * $promotion->amount
                ];
            }
            if (count($productPrintSideName) == count($areas) || empty($promotion->detail->print_areas)) {
                return array_splice($data, 1);
            }
        }

        return $data;
    }

    public function handleCalculatePriceOrderViaPricingSnapshot($saleOrder, $productPrintSides, $tagAdditionalService, $peakShippingFee, $tagLabel, $isReCalculatePrice = false)
    {
        $tag = explode(',', $saleOrder->tag);

        $this->info("Update sale order id: $saleOrder->id start...");
        $this->info('Sale order has: ' . $saleOrder->items->count() . ' items');
        $totalItemPrice = 0;
        $promotions = [];
        $store = Store::where('id', $saleOrder->store_id)->first();
        $orderPromotions = Promotion::where('store_id', $saleOrder->store_id)
            ->where('start_time', '<=', $saleOrder->created_at)
            ->where('is_public', Promotion::ACTIVE)
            ->where(function ($q) use ($saleOrder) {
                $q->whereNull('end_time')
                    ->orWhere('end_time', '>=', $saleOrder->created_at);
            })->get();

        // get ship price by product type
        $addressShip = $saleOrder->addressSaleOrder[0] ?? null;
        $destination = null;
        if ($addressShip) {
            $country = strtoupper($addressShip->country);
            $destination = in_array($country, StoreShipment::DOMESTIC_SHIPPING)
                ? StoreShipment::DOMESTIC
                : StoreShipment::INTERNATIONAL;
        }

        $j = 0;
        $shipPriceMax = ['price' => 0, 'addition_price' => 0];
        $totalShipPrice = 0;

        //check select store promotion
        $saleOrderItemErrors = [];
        $saleOrderRepository = app()->make(SaleOrderRepository::class);
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $isShipBySwiftpod = $saleOrder->shipmentDefault && (
            $saleOrder->shipmentDefault->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD
            || is_null($saleOrder->shipmentDefault->shipment_account)
        ) && $saleOrder->order_type !== SaleOrder::ORDER_TYPE_LABEL_ORDER && (($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && !in_array($tagLabel->id, $tag)) || $saleOrder->order_type !== SaleOrder::ORDER_TYPE_NORMAL);

        $tagMugPackaging = $tagAdditionalService->firstWhere('name', Tag::TAG_MUG_PACKAGING);
        $tagHologramSticker = $tagAdditionalService->firstWhere('name', Tag::TAG_HOLOGRAM_STICKER);
        $tagStickerAndBag = $tagAdditionalService->firstWhere('name', Tag::TAG_STICKER_AND_PLASTIC_BAG);
        $tagPlasticBag = $tagAdditionalService->firstWhere('name', Tag::TAG_PLASTIC_BAG);

        $pricingSnapshot = $saleOrder->pricingSnapshot->snapshot;
        $storeSurcharges = collect($pricingSnapshot['surcharge'] ?? [])
            ->map(fn ($item) => (object) [
                'id' => $item['id'],
                'name' => $item['name'],
                'api_value' => $item['api_value'],
                'per' => $item['per'],
                'value' => $item['value'],
                'product_type' => $item['product_type'],
                'store_surcharge_id' => $item['store_surcharge_id'],
            ]);

        $printPricing = collect($pricingSnapshot['print'] ?? [])
            ->map(fn ($item) => (object) [
                'product_print_area_id' => $item['product_print_area_id'],
                'product_id' => $item['product_id'],
                'price' => $item['price'],
                'print_price' => $item['print_price'],
                'handling_fee' => $item['handling_fee'],
                'print_surcharge' => $item['print_surcharge'],
            ]);

        $shippingPricing = collect($pricingSnapshot['shipping'] ?? [])
            ->map(fn ($item) => (object) [
                'product_type' => $item['product_type'],
                'destination' => $item['destination'],
                'service_type' => $item['service_type'],
                'product_style' => $item['product_style'],
                'price' => $item['price'],
                'addition_price' => $item['addition_price'],
                'size' => $item['size'],
                'status' => $item['status'],
            ]);

        foreach ($saleOrder->items as $saleOrderItem) {
            $isErrorOrderItem = false;
            if (!$saleOrderItem->product_id) {
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Product is empty';

                continue;
            }

            //get price and handling fee product
            $baseStoreProduct = $printPricing?->first(fn ($item) => $item->product_id == $saleOrderItem->product_id
                && $item->product_print_area_id === 0,
            );

            if (!$baseStoreProduct) {
                $this->error("Update sale order item id: $saleOrderItem->id failed. Base price not exists.");
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Base price not exists';
                $isErrorOrderItem = true;
            }
            if (is_null($saleOrderItem->print_sides) && $saleOrder->order_type != SaleOrder::ORDER_TYPE_BLANK) {
                $this->error("Update sale order item id: $saleOrderItem->id failed. Print area not exists.");
                $saleOrderItemErrors[$saleOrderItem->id][] = 'Print area not exists';
                $isErrorOrderItem = true;
            }
            if ($isErrorOrderItem) {
                continue;
            }
            $blankPrice = $baseStoreProduct->price;

            if ($saleOrder->store_id == Store::STORE_REDBUBBLE) {
                ///doi voi store RED BUBBLE Shrinkage cost chinh la handling free trong bang store_product_price
                $handlingFeePrice = $baseStoreProduct->handling_fee ?? 0;
            } else {
                $handlingFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_HANDLING;
                });
                $handlingFeePrice = $handlingFee->value ?? 0;
            }
            $baseUnitPrice = $blankPrice + $handlingFeePrice;
            $printSurcharge = 0;

            if (!$isShipBySwiftpod) {
                $printSurcharge = $baseStoreProduct->print_surcharge ?? 0;
            }
            $unitPrice = $baseUnitPrice + $printSurcharge;
            if (!empty($saleOrderItem->print_sides)) {
                //get product print area and calculate price
                $printSides = array_unique(str_split($saleOrderItem->print_sides));
                $productPrintSideName = $productPrintSides
                    ->whereIn('code_wip', $printSides)
                    ->pluck('name');

                $productStyle = ProductStyle::where('sku', $saleOrderItem->product_style_sku)->first();
                foreach ($orderPromotions as $promotion) {
                    if ($promotion->promotion_type_id == PromotionType::ADDITIONAL_PRINT_AREA_ID) {
                        $saleOrderPromotion = $this->handleAdditionalPrintAreaPromotion($saleOrderItem, $promotion, $productStyle, $productPrintSideName);
                        if (!empty($saleOrderPromotion)) {
                            $promotions = array_merge($promotions, $saleOrderPromotion);
                        }
                    }
                }
                $mapProductPrintAreasIds = ProductPrintArea::where('product_style_id', $productStyle->id)
                    ->whereIn('name', $productPrintSideName)
                    ->pluck('id')
                    ->toArray();

                $sumPrintPrice = $printPricing?->filter(fn ($item) => $item->product_id === $saleOrderItem->product_id
                    && in_array($item->product_print_area_id, $mapProductPrintAreasIds),
                )->unique(fn ($item) => $item->product_id . '-' . $item->product_print_area_id)
                    ->sum('print_price') ?? 0;
                $unitPrice += $sumPrintPrice;
            }

            ////Temp
            $orderCostTmp = DB::table('tmp_invoice_order_cost')
                ->select('blank_cost', 'print_cost')
                ->where('external_number', $saleOrder->external_number)
                ->where('external_id', $saleOrderItem->external_id)
                ->first();

            if ($orderCostTmp && $orderCostTmp->blank_cost !== null) {
                $blankPrice = $orderCostTmp->blank_cost;
                $unitPrice = $blankPrice + $orderCostTmp->print_cost;
            }

            $saleOrderItem->unit_price = $unitPrice;
            $saleOrderItem->blank_price = $blankPrice;
            $saleOrderItem->handling_fee = $handlingFeePrice;
            $saleOrderItem->amount_paid = $saleOrderItem->quantity * $unitPrice;
            $totalItemPrice += $saleOrderItem->amount_paid;

            //get ship price by product type and quantity
            if (
                $store->is_calculate_shipping
                && $saleOrder->order_status !== SaleOrder::STATUS_LATE_CANCELLED
                && $isShipBySwiftpod
            ) {
                if (!$saleOrderItem->getTypeProduct) {
                    $this->error("Update sale order item id: $saleOrderItem->id failed. Product style not exist.");
                    $saleOrderItemErrors[$saleOrderItem->id][] = 'Product style not exist';

                    continue;
                }
                $shippingPrice = $shippingPricing?->filter(
                    fn ($item) => $item->service_type == $saleOrder->shipping_method
                    && $item->destination == $destination
                    && $item->status == StoreShipment::STATUS_ACTIVE
                    && strtolower($item->product_type) === strtolower($saleOrderItem->getTypeProduct->type),
                ) ?? collect();
                $storeShipping = $shippingPrice
                    ->sortByDesc(function ($item) use ($saleOrderItem) {
                        $styleMatch = strtolower($item->product_style) === strtolower($saleOrderItem->product_style_sku);
                        $sizeMatch = strtolower($item->size) === strtolower($saleOrderItem->product_size_sku);
                        if ($styleMatch && $sizeMatch) {
                            return 3;
                        }
                        if ($styleMatch) {
                            return 2;
                        }
                        if ($sizeMatch) {
                            return 1;
                        }

                        return 0;
                    })
                    ->first();

                if ($isReCalculatePrice && !$storeShipping) {
                    $shippingPriceFromDB = StoreShipment::where('store_id', $saleOrder->store_id)
                        ->where('status', StoreShipment::STATUS_ACTIVE)
                        ->where('service_type', $saleOrder->shipping_method)
                        ->where('product_type', $saleOrderItem->getTypeProduct->type)
                        ->where('destination', $destination)
                        ->get();
                    $storeShipping = $shippingPriceFromDB
                        ->sortByDesc(function ($item) use ($saleOrderItem) {
                            $styleMatch = strtolower($item->product_style) === strtolower($saleOrderItem->product_style_sku);
                            $sizeMatch = strtolower($item->size) === strtolower($saleOrderItem->product_size_sku);
                            if ($styleMatch && $sizeMatch) {
                                return 3;
                            }
                            if ($styleMatch) {
                                return 2;
                            }
                            if ($sizeMatch) {
                                return 1;
                            }

                            return 0;
                        })
                        ->first();
                }
                if (!$storeShipping) {
                    $this->error('Shipping price has not been set yet.');
                    $saleOrderItemErrors[$saleOrderItem->id][] = 'Shipping price has not been set yet.';

                    continue;
                }

                // get first item ship max
                if ($shipPriceMax['price'] < $storeShipping->price) {
                    $shipPriceMax['price'] = $storeShipping->price;
                    $shipPriceMax['addition_price'] = $storeShipping->addition_price;
                }
                $totalShipPrice += $saleOrderItem->quantity * $storeShipping->addition_price;
            }
            //Tinh tiktok fee cho order la label va tiktok
            if ($saleOrder->order_type == SaleOrder::ORDER_TYPE_TIKTOK_ORDER
                || $saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER
                || ($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag))) {
                $tiktokFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_TIKTOK_ORDER_SERVICE;
                });
                if ($tiktokFee) {
                    $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $tiktokFee);
                }
            }
            //Tinh mug packaging fee theo item
            if ($tagMugPackaging && in_array($tagMugPackaging->id, $tag)) {
                $surchargeFeeRepository->handleSurchargeFeePerProductType($storeSurcharges, $saleOrderItem, SurchargeService::TYPE_MUG_PACKAGING);
            }
            if ($tagHologramSticker && in_array($tagHologramSticker->id, $tag)) {
                $surchargeFeeRepository->handleSurchargeFeePerProductType($storeSurcharges, $saleOrderItem, SurchargeService::TYPE_HOLOGRAM_STICKER);
            }
            //Tinh plastic bag theo item
            if (($tagPlasticBag && in_array($tagPlasticBag->id, $tag)) || $saleOrder->plastic_bag) {
                $plasticBagFee = $storeSurcharges->first(function ($itemSurcharge) {
                    return $itemSurcharge->per == SurchargeService::PER_ITEM
                        && $itemSurcharge->name == SurchargeService::TYPE_PLASTIC_BAG;
                });
                if ($plasticBagFee) {
                    $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $plasticBagFee);
                }
            }

            $embStitchCountService = null;
            ///Tinh gia stitch count cho don embroidery
            $stitchCountRanges = [
                [10001, 15000, SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES],
                [15001, 20000, SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES],
                [20001, 25000, SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES],
                [25001, 30000, SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES],
            ];
            $surchargeEmb = [];
            foreach ($saleOrderItem->embroideryTask as $embTask) {
                foreach ($stitchCountRanges as [$min, $max, $apiValue]) {
                    if ($embTask->stitch_count >= $min && $embTask->stitch_count <= $max) {
                        $embStitchCountService = $apiValue;
                        break;
                    }
                }
                if ($embStitchCountService) {
                    $surchargeEmb[] = $embStitchCountService;
                }
            }
            if (!empty($surchargeEmb)) {
                $countedValuesEmb = array_count_values($surchargeEmb);
                foreach ($countedValuesEmb as $embSurcharge => $count) {
                    $stickCountSurcharge = $storeSurcharges->first(function ($itemSurcharge) use ($embSurcharge) {
                        return $itemSurcharge->per == SurchargeService::PER_PRINT_AREA
                            && $itemSurcharge->api_value == $embSurcharge;
                    });
                    if ($stickCountSurcharge) {
                        $stickCountSurcharge->value *= $count;
                        $surchargeFeeRepository->handleSurchargeFeePerItem($saleOrderItem, $stickCountSurcharge);
                    }
                }
            }

            $saleOrderItem->save();
            $j++;
        }

        if (!empty($saleOrderItemErrors)) {
            $this->error("Update sale order id: $saleOrder->id failed. Sale order item failed.");

            SaleOrderCalculateFailed::updateOrCreate([
                'order_id' => $saleOrder->id,
                'store_id' => $saleOrder->store_id,
            ], [
                'failed_at' => now(),
            ]);
            SaleOrderItemCalculateFailed::where('order_id', $saleOrder->id)->delete();
            foreach ($saleOrderItemErrors as $key => $arrErrorOrderItem) {
                foreach ($arrErrorOrderItem as $itemError) {
                    SaleOrderItemCalculateFailed::create([
                        'order_id' => $saleOrder->id,
                        'order_item_id' => $key,
                        'reason' => $itemError,
                        'created_at' => now(),
                    ]);
                }
            }

            return false;
        }

        // calculate ship price
        $totalShipPrice = $totalShipPrice > 0 ? $totalShipPrice - $shipPriceMax['addition_price'] + $shipPriceMax['price'] : 0;
        $saleOrder->amount_paid = $totalItemPrice;
        $saleOrder->shipping_calculate = $totalShipPrice;
        $saleOrder->order_total = $totalItemPrice + $totalShipPrice;
        $saleOrder->calculated_at = now();
        // calculate peak shipping fee
        if ($saleOrder->order_status !== SaleOrder::STATUS_LATE_CANCELLED
            && $peakShippingFee
            && $isShipBySwiftpod
            && $saleOrder->shipmentDefault->created_at->between($peakShippingFee->start_date, $peakShippingFee->end_date)
            && in_array($saleOrder->shipmentDefault->carrier_code, [ShippingCarrier::DHL_ECOMMERCE_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])
            && $destination == StoreShipment::DOMESTIC
        ) {
            $saleOrderRepository->updateOrderSurchargeFee($saleOrder, $peakShippingFee, PeakShippingFee::PEAK_SHIPPING_FEE);
        }
        $saleOrder->save();
        SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->delete();
        SaleOrderItemCalculateFailed::where('order_id', $saleOrder->id)->delete();

        //Tinh sticker and bag theo order
        if ($tagStickerAndBag && in_array($tagStickerAndBag->id, $tag)) {
            $surchargeFeeRepository->handleSurchargeFeePerOrder($storeSurcharges, $saleOrder, SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG);
        }
        if ($saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER
            || ($saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag))) {
            $surchargeFeeRepository->handleLabelSurchargeFeePerOrder($storeSurcharges, $saleOrder);
        }
        //Neu don la in_production_cancel da duoc tinh Label printing fee,Peak shipping surcharge
        // trong lan calculate price truoc (thuong la don Label) thi se bi remove nhung fee nay di khi calculate price lan sau
        if ($saleOrder->order_status == SaleOrder::STATUS_LATE_CANCELLED) {
            SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
                ->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)
                ->delete();
            $isLabelOrder = $saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER;
            $isNormalOrderWithTag = $saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array($tagLabel->id, $tag);

            if ($isLabelOrder || $isNormalOrderWithTag) {
                $surcharge = $storeSurcharges->first(fn ($item) => $item->per == SurchargeService::PER_ORDER
                    && $item->name == SurchargeService::TYPE_LABEL_PRINTING_FEE,
                );
                if ($surcharge) {
                    SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
                        ->where('surcharge_id', $surcharge->store_surcharge_id)
                        ->delete();
                }
            }
        }
        SaleOrderPromotion::where('order_id', $saleOrder->id)->delete();
        if (!empty($promotions)) {
            SaleOrderPromotion::insert($promotions);
        }
        $this->info("Update sale order id: $saleOrder->id done.");

        return true;
    }
}
