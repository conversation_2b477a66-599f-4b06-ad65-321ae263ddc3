<?php

namespace App\Console\Commands;

use App\Exports\ExportAdjustPullingShelves;
use App\Exports\ExportTestCount;
use App\Exports\InventoryCOGSExport;
use App\Exports\InventoryMovementExport;
use App\Models\ExportHistory;
use App\Models\Inventory;
use App\Models\Product;
use App\Repositories\ExportHistoryRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ProcessExportData extends Command
{
    protected $signature = 'process:export-data';

    protected $description = 'Process Export Data';

    protected ExportHistoryRepository $downloadHistoryRepository;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(ExportHistoryRepository $downloadHistoryRepository)
    {
        $this->downloadHistoryRepository = $downloadHistoryRepository;
        $sleep = 10;

        while (true) {
            $this->info('Begin ...');
            $this->export();
            $this->downloadHistoryRepository->cleanOldHistory();
            $this->info('Process done => sleep to: ' . now()->addSeconds($sleep)->format('Y-m-d H:i:s'));
            sleep($sleep);
        }
    }

    public function export()
    {
        setTimezone();
        $data = $this->downloadHistoryRepository->getLatest([
            'status' => ExportHistory::STATUS_PENDING,
        ]);

        foreach ($data as $item) {
            try {
                $this->info('ID: ' . $item->id . ', Module: ' . $item->module);

                if ($item->module == ExportHistory::MODULE_TEST_COUNT) {
                    Excel::store(new ExportTestCount($item->toArray()), $item->file_path, 's3');
                } elseif ($item->module == ExportHistory::MODULE_ADJUST_PULLING_SHELVES) {
                    Excel::store(new ExportAdjustPullingShelves($item->toArray()), $item->file_path, 's3');
                } elseif ($item->module == ExportHistory::MODULE_COGS_REPORT) {
                    Excel::store(new InventoryCOGSExport($item->toArray()), $item->file_path, 's3');
                } elseif (str_starts_with($item->module, ExportHistory::MODULE_INVENTORY_MOVEMENT)) {
                    $otherConditions = json_decode($item->other_conditions, true);
                    $endTime = Carbon::parse(trim($item->end_date))->endOfDay()->format('Y-m-d H:i:s');

                    if (empty($otherConditions['sku'])) {
                        throw new \Exception('Sku invalid');
                    }

                    $product = Product::query()
                        ->where('sku', $otherConditions['sku'])
                        ->first();

                    if (empty($product)) {
                        throw new \Exception('Product not found. sku: ' . $otherConditions['sku']);
                    }

                    $fifoNotCalculateYet = Inventory::query()
                        ->where('product_id', $product->id)
                        ->where('warehouse_id', $item->warehouse_id)
                        ->where('created_at', '<=', $endTime)
                        ->where('fifo_calculated_at', Inventory::FIFO_NOT_CALCULATED)
                        ->first();

                    if (!empty($fifoNotCalculateYet)) {
                        continue;
                    }

                    Excel::store(new InventoryMovementExport($product->id, $item->toArray()), $item->file_path, 's3');
                } else {
                    throw new \Exception('Not found module: ' . $item->module);
                }

                if (!Storage::disk('s3')->exists($item->file_path)) {
                    $this->warn('Not found path generated.');

                    continue;
                }

                $fileUrl = Storage::disk('s3')->url($item->file_path);
                $this->info($fileUrl);
                $this->downloadHistoryRepository->update($item->id, [
                    'status' => ExportHistory::STATUS_COMPLETED
                ]);
            } catch (\Throwable $th) {
                $uuid = Str::uuid();
                $this->error($th->getMessage());
                $exception = [
                    'uuid' => $uuid,
                    'message' => $th->getMessage(),
                    'file' => $th->getFile(),
                    'line' => $th->getLine(),
                ];

                Log::error('ProcessExportData.export', [
                    'uuid' => $uuid,
                    'data' => $item,
                    'exception' => $th,
                ]);

                $this->downloadHistoryRepository->update($item->id, [
                    'status' => ExportHistory::STATUS_FAILED,
                    'exception' => json_encode($exception),
                ]);
            }
        }
    }
}
