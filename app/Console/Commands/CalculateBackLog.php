<?php

namespace App\Console\Commands;

use App\Models\PrintMethod;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CalculateBackLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:back-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate back log for all warehouses.';

    public function handle()
    {
        $warehouses = Warehouse::all();
        $limitId = getBarcodeIdLimit();
        $shipmentId = DB::table('shipment')->where('created_at', '>=', Carbon::now()->subDays(30)->startOfDay()->toDateTimeString())->min('id');
        $shipmentItemId = DB::table('shipment_label_printed')->where('created_at', '>=', Carbon::now()->subDays(3)->startOfDay()->toDateTimeString())->min('id');
        $dataCache = [];
        foreach ($warehouses as $warehouse) {
            $input = [
                'warehouse_id' => $warehouse->id,
                'limit_barcode_id' => $limitId['order_item_barcode_id'],
                'limit_order_id' => $limitId['order_id'],
                'limit_shipment_id' => $shipmentId,
                'limit_shipment_item_id' => $shipmentItemId,
            ];
            $dataCache[$warehouse->id]['printing'] = $this->getBackLogForPrinting($input);
            $dataCache[$warehouse->id]['quality_control'] = $this->getBackLogForQC($input);
            $dataCache[$warehouse->id]['press'] = $this->getBackLogForPressing($input);
            $dataCache[$warehouse->id]['create_shipment_label'] = $this->getBackLogForShipping($input);
            $dataCache[$warehouse->id]['folding'] = $this->getBackLogForFolding($input);
        }
        Cache::store(config('cache.redis_store'))->put('CACHE_BACKLOG', $dataCache, 3600);
    }

    public function getBackLogForPrinting($input)
    {
        return SaleOrderItemBarcode::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED])
            ->where('sale_order.is_test', 0)
            ->whereNotNull('sale_order_item_barcode.pulled_at')
            ->whereNull('sale_order_item_barcode.printed_at')
            ->whereNull('sale_order_item_barcode.qc_at')
            ->whereNull('sale_order_item_barcode.pressed_at')
            ->whereNull('sale_order_item_barcode.shipped_at')
            ->whereNull('sale_order_item_barcode.folded_at')
            ->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id'])
            ->where('sale_order_item_barcode.id', '>=', $input['limit_barcode_id'])
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.print_method', PrintMethod::DTG)
            ->count();
    }

    public function getBackLogForQC($input)
    {
        return SaleOrderItemBarcode::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED])
            ->where('sale_order.is_test', 0)
            ->whereNotNull('sale_order_item_barcode.printed_at')
            ->whereNull('sale_order_item_barcode.qc_at')
            ->whereNull('sale_order_item_barcode.pressed_at')
            ->whereNull('sale_order_item_barcode.shipped_at')
            ->whereNull('sale_order_item_barcode.folded_at')
            ->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id'])
            ->where('sale_order_item_barcode.id', '>=', $input['limit_barcode_id'])
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->count();
    }

    public function getBackLogForPressing($input)
    {
        return SaleOrderItemBarcode::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED])
            ->where('sale_order.is_test', 0)
            ->whereNotNull('sale_order_item_barcode.qc_at')
            ->whereNull('sale_order_item_barcode.pressed_at')
            ->whereNull('sale_order_item_barcode.folded_at')
            ->whereNull('sale_order_item_barcode.shipped_at')
            ->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id'])
            ->where('sale_order_item_barcode.id', '>=', $input['limit_barcode_id'])
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.print_method', PrintMethod::DTG)
            ->count();
    }

    public function getBackLogForShipping($input)
    {
        return SaleOrderItemBarcode::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED])
            ->where('sale_order.is_test', 0)
            ->whereNotNull('sale_order_item_barcode.qc_at')
            ->whereNull('sale_order_item_barcode.shipped_at')
            ->whereNull('sale_order_item_barcode.folded_at')
            ->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id'])
            ->where('sale_order_item_barcode.id', '>=', $input['limit_barcode_id'])
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->count();
    }

    public function getBackLogForFolding($input)
    {
        return SaleOrder::join('shipment', 'sale_order.id', '=', 'shipment.order_id')
            ->join('shipment_label_printed', 'shipment.id', '=', 'shipment_label_printed.shipment_id')
            ->join('employee', 'employee.id', '=', 'shipment.employee_printed_id')
            ->select('sale_order.id')
            ->where('sale_order.order_folding_status', 0)
            ->where('sale_order.warehouse_id', $input['warehouse_id'])
            ->where('sale_order.id', '>=', $input['limit_order_id'])
            ->where('shipment_label_printed.id', '>=', $input['limit_shipment_item_id'])
            ->where('shipment.id', '>=', $input['limit_shipment_id'])
            ->where('employee.department', '!=', 'Folding')
            ->groupBy('sale_order.id')
            ->get()
            ->count();
    }
}
