<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use App\Models\Shipment;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class GenerateSalesReport extends Command
{
    protected $signature = 'sales:report {--start=} {--end=}';

    protected $description = 'Generate daily sales report grouped by warehouse and store';

    public function handle()
    {
        setTimezone();
        $startInput = $this->option('start');
        $endInput = $this->option('end');

        $start = $startInput ? Carbon::parse($startInput)->startOfDay() : now()->subDays(15)->startOfDay();
        $end = $endInput ? Carbon::parse($endInput)->startOfDay() : now()->subDay()->startOfDay();

        $this->info("Generating report from {$start->toDateString()} to {$end->toDateString()}...");

        for ($date = $start->copy(); $date <= $end; $date->addDay()) {
            $this->line("→ Processing date: {$date->toDateString()}");

            // Main sale_order data by created_at
            $orders = DB::table('sale_order')
                ->selectRaw('
                    warehouse_id,
                    store_id,
                    DATE(created_at) as date,
                    COUNT(*) as total_orders,
                    SUM(order_quantity) as total_items
                ')
                ->whereBetween('created_at', [$date->copy()->startOfDay(), $date->copy()->endOfDay()])
                ->where('is_test', 0)
                ->groupBy('warehouse_id', 'store_id', DB::raw('DATE(created_at)'))
                ->get()
                ->keyBy(fn ($item) => "{$item->warehouse_id}_{$item->store_id}");

            // Shipment-based data by shipment.created_at
            $shipped = DB::table('sale_order')
                ->join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
                ->selectRaw('
                    sale_order.warehouse_id,
                    sale_order.store_id,
                    DATE(shipment.created_at) as date,
                    COUNT(
                     CASE
                        WHEN shipment.provider <> ? THEN 1
                        WHEN shipment.provider = ? AND sale_order.order_status = ? THEN 1
                        ELSE NULL
                     END) as total_shipped_orders,
                    SUM(
                        CASE
                            WHEN shipment.provider <> ? THEN sale_order.order_quantity
                            WHEN shipment.provider = ? AND sale_order.order_status = ? THEN sale_order.order_quantity
                            ELSE 0
                        END
                    ) as total_shipped_items,
                    SUM(CASE WHEN sale_order.calculated_at IS NOT NULL THEN sale_order.order_total ELSE 0 END) as total_shipped_paid,
                    SUM(CASE WHEN sale_order.calculated_at IS NOT NULL THEN sale_order.amount_paid ELSE 0 END) as total_shipped_items_paid
                ', [Shipment::PROVIDER_MARKETPLACE, Shipment::PROVIDER_MARKETPLACE, SaleOrder::SHIPPED, Shipment::PROVIDER_MARKETPLACE, Shipment::PROVIDER_MARKETPLACE, SaleOrder::SHIPPED])
                ->where('sale_order.is_test', 0)
                ->whereBetween('shipment.created_at', [$date->copy()->startOfDay(), $date->copy()->endOfDay()])
                ->groupBy('warehouse_id', 'store_id', DB::raw('DATE(shipment.created_at)'))
                ->get()
                ->keyBy(fn ($item) => "{$item->warehouse_id}_{$item->store_id}");

            // Cancelled data by cancelled_at
            $cancelled = DB::table('sale_order')
                ->selectRaw('
                    warehouse_id,
                    store_id,
                    DATE(cancelled_at) as date,
                    SUM(order_total) as total_cancelled_paid,
                    SUM(amount_paid) as total_cancelled_items_paid
                ')
                ->whereBetween('cancelled_at', [$date->copy()->startOfDay(), $date->copy()->endOfDay()])
                ->where('order_status', SaleOrder::STATUS_LATE_CANCELLED)
                ->where('is_test', 0)
                ->groupBy('warehouse_id', 'store_id', DB::raw('DATE(cancelled_at)'))
                ->get()
                ->keyBy(fn ($item) => "{$item->warehouse_id}_{$item->store_id}");

            // Merge and update
            $keys = collect($orders->keys())
                ->merge($shipped->keys())
                ->merge($cancelled->keys())
                ->unique();

            foreach ($keys as $key) {
                [$warehouseId, $storeId] = explode('_', $key);
                $orderRow = $orders[$key] ?? null;
                $shippedRow = $shipped[$key] ?? null;
                $cancelledRow = $cancelled[$key] ?? null;

                $totalOrderSales = ($shippedRow->total_shipped_paid ?? 0) + ($cancelledRow->total_cancelled_paid ?? 0);
                $totalOrderItemsSales = ($shippedRow->total_shipped_items_paid ?? 0) + ($cancelledRow->total_cancelled_items_paid ?? 0);

                DB::table('sales_report_totals')->updateOrInsert(
                    [
                        'warehouse_id' => $warehouseId,
                        'store_id' => $storeId,
                        'date' => $date->toDateString(),
                    ],
                    [
                        'total_orders' => $orderRow->total_orders ?? 0,
                        'total_items' => $orderRow->total_items ?? 0,
                        'total_shipped_orders' => $shippedRow->total_shipped_orders ?? 0,
                        'total_shipped_items' => $shippedRow->total_shipped_items ?? 0,
                        'total_order_sales_amount' => $totalOrderSales,
                        'total_order_items_sales_amount' => $totalOrderItemsSales,
                    ],
                );
            }

            $this->line("✅ Updated data for date: {$date->toDateString()}");
        }

        $this->info('✅ Report generation completed.');
    }
}
