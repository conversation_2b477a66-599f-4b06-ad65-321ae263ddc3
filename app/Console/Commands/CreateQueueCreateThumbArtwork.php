<?php

namespace App\Console\Commands;

use App\Models\QueueJob;
use App\Services\QueueImageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateQueueCreateThumbArtwork extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:create-queue-create-thumb-artwork';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create queue create thumb artwork';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        while (true) {
            try {
                $count = QueueImageService::addQueueCreateThumbArtwork();
                $this->info(date('Y-m-d H:i:s') . ' | ' . QueueJob::QUEUE_CREATE_THUMB_ARTWORK . ': ' . $count);
            } catch (\Exception $exception) {
                sleep(10);
                Log::channel('queue_create_thumb')->error($exception->getMessage());
                $this->error($exception->getMessage());
            }
           // DB::disconnect();
            sleep(20);
        }

    }


}
