<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearFailedJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:failed-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear old failed jobs from the failed_jobs table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[START] Delete old failed jobs');

        DB::table('failed_jobs')->where('failed_at', '<', Carbon::now()->subMonth())->delete();

        $this->info('[END] Old failed jobs cleared successfully');
    }
}
