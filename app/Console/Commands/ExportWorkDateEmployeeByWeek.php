<?php

namespace App\Console\Commands;

use App\Exports\WorkDateEmployeeManySheetsExport;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportWorkDateEmployeeByWeek extends Command
{
    /**
     * The name and signature of the console command.
     * Add date to the signature
     *
     * @var string
     */
    protected $signature = 'export:work-log-weekly {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export work date of employee by week, then upload file to Google Driver';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = $this->argument('date');
        echo "Exporting work date of employee by week : $date\n";
        if ($date) {
            $now = Carbon::createFromFormat('Y-m-d', $date)->setTimezone('America/Los_Angeles');
        } else {
            $now = Carbon::now()->setTimezone('America/Los_Angeles');
        }
        $startDate = $now->copy()->subWeek()->startOfWeek(Carbon::MONDAY);
        $endDate = $now->copy()->subWeek()->endOfWeek(Carbon::SUNDAY);
        $startDateFormatted = $startDate->format('mdY');
        $endDateFormatted = $endDate->format('mdY');
        echo "Exporting work date of employee from {$startDate->toDateString()} to {$endDate->toDateString()}...\n";
        $fileName = "work-date/{$startDateFormatted}-{$endDateFormatted}.xlsx";
        Excel::store(new WorkDateEmployeeManySheetsExport($startDate->toDateString(), $endDate->toDateString()), $fileName, 's3');
        $url = Storage::disk('s3')->url($fileName);
        echo $url;

        return $url;
    }
}
