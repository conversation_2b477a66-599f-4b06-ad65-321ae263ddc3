<?php

namespace App\Providers;

use App\Models\Store;
use App\Repositories\AccountRepository;
use App\Repositories\AdjustPullingShelvesRepository;
use App\Repositories\BoxMovingRepository;
use App\Repositories\BoxRepository;
use App\Repositories\BrandRepository;
use App\Repositories\ClaimRepository;
use App\Repositories\Contracts\AccountRepositoryInterface;
use App\Repositories\Contracts\AdjustPullingShelvesRepositoryInterface;
use App\Repositories\Contracts\BoxMovingRepositoryInterface;
use App\Repositories\Contracts\BoxRepositoryInterface;
use App\Repositories\Contracts\BrandRepositoryInterface;
use App\Repositories\Contracts\ClaimRepositoryInterface;
use App\Repositories\Contracts\CountryRepositoryInterface;
use App\Repositories\Contracts\DocCategoryRepositoryInterface;
use App\Repositories\Contracts\DocRepositoryInterface;
use App\Repositories\Contracts\EmployeeRepositoryInterface;
use App\Repositories\Contracts\FillingShelvesRepositoryInterface;
use App\Repositories\Contracts\InventoryAdditionRepositoryInterface;
use App\Repositories\Contracts\LanguageRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Repositories\Contracts\MockupRepositoryInterface;
use App\Repositories\Contracts\PrintingPresetRepositoryInterface;
use App\Repositories\Contracts\PrintingPresetSkuRepositoryInterface;
use App\Repositories\Contracts\ProductColorRepositoryInterface;
use App\Repositories\Contracts\ProductRepositoryInterface;
use App\Repositories\Contracts\ProductSizeRepositoryInterface;
use App\Repositories\Contracts\ProductTypeWeightRepositoryInterface;
use App\Repositories\Contracts\PurchaseOrderRepositoryInterface;
use App\Repositories\Contracts\SaleOrderItemBarcodeRepositoryInterface;
use App\Repositories\Contracts\SaleOrderItemRepositoryInterface;
use App\Repositories\Contracts\SaleOrderRepositoryInterface;
use App\Repositories\Contracts\SellbriteSyncRepositoryInterface;
use App\Repositories\Contracts\SellbriteWarehouseRepositoryInterface;
use App\Repositories\Contracts\SettingRepositoryInterface;
use App\Repositories\Contracts\ShippingCarrierRepositoryInterface;
use App\Repositories\Contracts\StoreRepositoryInterface;
use App\Repositories\Contracts\SupplyAdditionRepositoryInterface;
use App\Repositories\Contracts\SupplyCategoryRepositoryInterface;
use App\Repositories\Contracts\SupplyDeductionRepositoryInterface;
use App\Repositories\Contracts\SupplyInventoryRepositoryInterface;
use App\Repositories\Contracts\SupplyPurchaseOrderRepositoryInterface;
use App\Repositories\Contracts\SupplyUnitRepositoryInterface;
use App\Repositories\Contracts\TagRepositoryInterface;
use App\Repositories\Contracts\TestCountAdjustmentRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Repositories\Contracts\VendorRepositoryInterface;
use App\Repositories\Contracts\WarehouseRepositoryInterface;
use App\Repositories\CountryRepository;
use App\Repositories\DocCategoryRepository;
use App\Repositories\DocRepository;
use App\Repositories\EmployeeRepository;
use App\Repositories\FillingShelvesRepository;
use App\Repositories\InventoryAdditionRepository;
use App\Repositories\LanguageRepository;
use App\Repositories\LocationRepository;
use App\Repositories\MockupRepository;
use App\Repositories\PrintingPresetRepository;
use App\Repositories\PrintingPresetSkuRepository;
use App\Repositories\ProductColorRepository;
use App\Repositories\ProductRepository;
use App\Repositories\ProductSizeRepository;
use App\Repositories\ProductTypeWeightRepository;
use App\Repositories\PurchaseOrderRepository;
use App\Repositories\SaleOrderItemBarcodeRepository;
use App\Repositories\SaleOrderItemRepository;
use App\Repositories\SaleOrderRepository;
use App\Repositories\SellbriteSyncRepository;
use App\Repositories\SellbriteWarehouseRepository;
use App\Repositories\SettingRepository;
use App\Repositories\ShippingCarrierRepository;
use App\Repositories\StoreRepository;
use App\Repositories\SupplyAdditionRepository;
use App\Repositories\SupplyCategoryRepository;
use App\Repositories\SupplyDeductionRepository;
use App\Repositories\SupplyInventoryRepository;
use App\Repositories\SupplyPurchaseOrderRepository;
use App\Repositories\SupplyUnitRepository;
use App\Repositories\TagRepository;
use App\Repositories\TestCountAdjustmentRepository;
use App\Repositories\UserRepository;
use App\Repositories\VendorRepository;
use App\Repositories\WarehouseRepository;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Laravel\Cashier\Cashier;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(BrandRepositoryInterface::class, BrandRepository::class);
        $this->app->bind(ProductRepositoryInterface::class, ProductRepository::class);
        $this->app->bind(WarehouseRepositoryInterface::class, WarehouseRepository::class);
        $this->app->bind(CountryRepositoryInterface::class, CountryRepository::class);
        $this->app->bind(VendorRepositoryInterface::class, VendorRepository::class);
        $this->app->bind(BoxRepositoryInterface::class, BoxRepository::class);
        $this->app->bind(LocationRepositoryInterface::class, LocationRepository::class);
        $this->app->bind(ShippingCarrierRepositoryInterface::class, ShippingCarrierRepository::class);
        $this->app->bind(PurchaseOrderRepositoryInterface::class, PurchaseOrderRepository::class);
        $this->app->bind(InventoryAdditionRepositoryInterface::class, InventoryAdditionRepository::class);
        $this->app->bind(EmployeeRepositoryInterface::class, EmployeeRepository::class);
        $this->app->bind(BoxMovingRepositoryInterface::class, BoxMovingRepository::class);
        $this->app->bind(UserRepositoryInterface::class, UserRepository::class);
        $this->app->bind(TestCountAdjustmentRepositoryInterface::class, TestCountAdjustmentRepository::class);
        $this->app->bind(AdjustPullingShelvesRepositoryInterface::class, AdjustPullingShelvesRepository::class);
        $this->app->bind(PrintingPresetRepositoryInterface::class, PrintingPresetRepository::class);
        $this->app->bind(LanguageRepositoryInterface::class, LanguageRepository::class);
        $this->app->bind(PrintingPresetSkuRepositoryInterface::class, PrintingPresetSkuRepository::class);
        $this->app->bind(SettingRepositoryInterface::class, SettingRepository::class);
        $this->app->bind(ProductColorRepositoryInterface::class, ProductColorRepository::class);
        $this->app->bind(ProductSizeRepositoryInterface::class, ProductSizeRepository::class);
        $this->app->bind(ProductTypeWeightRepositoryInterface::class, ProductTypeWeightRepository::class);
        $this->app->bind(SaleOrderRepositoryInterface::class, SaleOrderRepository::class);
        $this->app->bind(SaleOrderItemRepositoryInterface::class, SaleOrderItemRepository::class);
        $this->app->bind(SaleOrderItemBarcodeRepositoryInterface::class, SaleOrderItemBarcodeRepository::class);
        $this->app->bind(TagRepositoryInterface::class, TagRepository::class);
        $this->app->bind(AccountRepositoryInterface::class, AccountRepository::class);
        $this->app->bind(StoreRepositoryInterface::class, StoreRepository::class);
        $this->app->bind(ClaimRepositoryInterface::class, ClaimRepository::class);
        $this->app->bind(FillingShelvesRepositoryInterface::class, FillingShelvesRepository::class);
        $this->app->bind(SellbriteSyncRepositoryInterface::class, SellbriteSyncRepository::class);
        $this->app->bind(SellbriteWarehouseRepositoryInterface::class, SellbriteWarehouseRepository::class);
        $this->app->bind(MockupRepositoryInterface::class, MockupRepository::class);
        $this->app->bind(DocCategoryRepositoryInterface::class, DocCategoryRepository::class);
        $this->app->bind(DocRepositoryInterface::class, DocRepository::class);
        $this->app->bind(SupplyUnitRepositoryInterface::class, SupplyUnitRepository::class);
        $this->app->bind(SupplyCategoryRepositoryInterface::class, SupplyCategoryRepository::class);
        $this->app->bind(SupplyPurchaseOrderRepositoryInterface::class, SupplyPurchaseOrderRepository::class);
        $this->app->bind(SupplyAdditionRepositoryInterface::class, SupplyAdditionRepository::class);
        $this->app->bind(SupplyDeductionRepositoryInterface::class, SupplyDeductionRepository::class);
        $this->app->bind(SupplyInventoryRepositoryInterface::class, SupplyInventoryRepository::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Paginator::useBootstrap();
        Cashier::useCustomerModel(Store::class);

        if (config('app.env') !== 'local') {
            URL::forceScheme('https');
        }

        Builder::macro('optimizePaginate', function ($perPage = 15, $columns = ['*'], $pageName = 'page', $page = null, $total = null) {
            $page = $page ?: Paginator::resolveCurrentPage($pageName);

            $total = $total ?? $this->getCountForPagination();

            $results = $total ? $this->forPage($page, $perPage)->get($columns) : collect();

            return $this->paginator($results, $total, $perPage, $page, [
                'path' => Paginator::resolveCurrentPath(),
                'pageName' => $pageName,
            ]);
        });
    }
}
