<?php

namespace App\Services;

use App\Jobs\CreateShippingLabelJob;
use App\Models\ProductSize;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentEasypost as ShipmentEasypostModel;
use App\Models\ShipmentFee;
use App\Models\ShipmentPackage;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierPackage;
use App\Models\ShippingCarrierService;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StoreProductWeight;
use App\Models\StoreShipment;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WeightCubic;
use App\Repositories\LabelRepository;
use EasyPost\EasyPost;
use EasyPost\Error as EasyPostError;
use EasyPost\Shipment as ShipmentEasyPost;
use EasyPost\Util;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateShippingLabelService
{
    protected $saleOrderId;

    public function __construct($saleOrderId)
    {
        $this->saleOrderId = $saleOrderId;
    }

    public function handle()
    {
        try {
            $labelRepository = new LabelRepository();
            $saleOrder = SaleOrder::select('sale_order.id', 'sale_order.store_id', 'sale_order.warehouse_id',
                'sale_order.merchant_name', 'sale_order.order_number', 'sale_order.shipping_method',
                'sale_order.shipment_id', 'sale_order.order_status', 'sale_order.created_at', 'sale_order.order_quantity',
                'sale_order.ioss_number', 'sale_order.tax_id_type', 'sale_order.order_type')
                ->with([
                    'store:id,name,easypost_api_key,auto_shipping_pending_at', 'warehouse', 'addressSaleOrder',
                    'items' => function ($q) {
                        $q->select(
                            'sale_order_item.order_id', 'sale_order_item.product_id', 'sale_order_item.quantity',
                            'sale_order_item.sku', 'sale_order_item.product_sku', 'sale_order_item.product_style_sku',
                            'sale_order_item.product_size_sku', 'sale_order_item.is_deleted', 'sale_order_item.id',
                            'sale_order_item.quantity', 'sale_order_item.account_id', 'sale_order_item.warehouse_id',
                            'sale_order_item.store_id',
                        )
                            ->with('product:id,weight_single,weight_multiple,sku,style', 'productStyle:type,sku', 'productStyle.productType:name,is_hard_goods', 'productSize')
                            ->with(['barcodes' => function ($q) {
                                $q->select('sale_order_item_barcode.id', 'sale_order_item_barcode.order_id', 'sale_order_item_barcode.order_item_id',
                                    'sale_order_item_barcode.label_id', 'sale_order_item_barcode.part_number_id', 'sale_order_item_barcode.kitted_at', 'sale_order_item_barcode.employee_kitted_id')
                                    ->where('is_deleted', '<>', SaleOrderItemBarcode::DELETED);
                            }]);
                    }, 'storeAddress' => function ($q) {
                        $q->where('type_address', 'return_address');
                    }, 'barcodeItems' => function ($q) {
                        $q->select('sale_order_item_barcode.id', 'sale_order_item_barcode.order_id', 'sale_order_item_barcode.label_id', 'sale_order_item_barcode.part_number_id')
                            ->with('partNumber:id,product_id,part_number')
                            ->where('is_deleted', '<>', SaleOrderItemBarcode::DELETED);
                    }
                ])
                ->where('id', $this->saleOrderId)
                ->where('order_status', SaleOrder::STATUS_IN_PRODUCTION)
                ->whereNull('shipment_id')
                ->first();

            if (!$saleOrder) {
                echo "Error: Can't find sale order! \n";

                return;
            }

            echo "Start create label order: $saleOrder->id / $saleOrder->order_number \n";

            $dataRbPackage = $labelRepository->getConditonPacking();
            $allSetting = Setting::all();
            $urlHook = $allSetting->where('name', Setting::ALERT_SLACK_EASYPOST)->first();
            // loai tru nhung san pham khong tao shipping tu dong
            $productSkipNotAuto = $allSetting->where('name', Setting::PRODUCT_SKIP_NOT_AUTO_CREATE_SHIPPING)->first();
            $arrValueConditionProductType = $productSkipNotAuto && $productSkipNotAuto->value != '' ? array_map('strtoupper', array_map('trim', explode(',', $productSkipNotAuto->value))) : [];

            if (!$urlHook) {
                echo "Error: Can't find url hook slack push notification! \n";

                return;
            }

            $urlGoogleChat = $allSetting->where('name', Setting::ALERT_GOOGLE_CHAT_EASYPOST)->first();
            if (!$urlGoogleChat) {
                echo "Error: Can't find url hook google push notification! \n";

                return;
            }

            $storeHardGoodsShipUsps = $allSetting->where('name', Setting::STORE_HARD_GOODS_SHIP_USPS)->first();
            $arrStoreHardGoodsShipUsps = $storeHardGoodsShipUsps && $storeHardGoodsShipUsps->value != '' ? array_map('strtoupper', array_map('trim', explode(',', $storeHardGoodsShipUsps->value))) : [];

            $conditionGetAddressTo = $allSetting->where('name', Setting::CONDITION_GET_ADDRESS_TO_FOR_CREATE_LABEL)->first();
            $arrValueConditionGetAddressTo = $conditionGetAddressTo && $conditionGetAddressTo->value != '' ? explode(',', $conditionGetAddressTo->value) : [1];

            $storeUsesAccountSwiftpod = $allSetting->where('name', Setting::STORE_USE_ACCOUNT_SHIP_SWIFTPOD)->first();
            $arrStoreUsesAccountSwiftpod = $storeUsesAccountSwiftpod && $storeUsesAccountSwiftpod->value != '' ? array_map('trim', explode(',', $storeUsesAccountSwiftpod->value)) : [];
            array_push($arrStoreUsesAccountSwiftpod, 1);

            $shippingCarriers = $labelRepository->getShippingCarriers();
            if ($shippingCarriers->count() == 0) {
                echo "Can't find carrier! \n";
                $labelRepository->messageToSlack("Can't find Shipping carrier!", $urlHook->value ?? '', 'Shipping carrier', 'Not found');
                $labelRepository->sendGoogleChat("Can't find Shipping carrier!", $urlGoogleChat->value);

                return;
            }

            $carrierAccounts = $labelRepository->getShippingCarrierEasypost();
            if ($carrierAccounts->count() == 0) {
                echo "Can't find carrier USPS account! \n";
                $labelRepository->messageToSlack("Can't find carrier account!", $urlHook->value ?? '', 'Carrier account', 'Not found carrier account');
                $labelRepository->sendGoogleChat("Can't find carrier account!", $urlGoogleChat->value);

                return;
            }

            $dataShippingMethods = $labelRepository->getShippingMethodEasypost();

            if ($dataShippingMethods->isEmpty()) {
                echo "Can't find data shipping method! \n";
                $labelRepository->messageToSlack("Can't find data shipping method", $urlHook->value ?? '', 'Data shipping method', 'Not found shipping method');
                $labelRepository->sendGoogleChat("Can't find data shipping method", $urlGoogleChat->value);

                return;
            }

            $dataCubic = WeightCubic::get();
            if ($dataCubic->isEmpty()) {
                echo "Can't find data cubic! \n";
                $labelRepository->messageToSlack("Can't find data cubic!", $urlHook->value ?? '', 'Data cubic', 'Not found data cubic');
                $labelRepository->sendGoogleChat("Can't find data cubic!", $urlGoogleChat->value);

                return;
            }

            $easypostApiKeyDefault = $labelRepository->getApiKeyDefaultEasypost();
            if (!$easypostApiKeyDefault) {
                echo "Can't find easypost apikey default! \n";
                $labelRepository->messageToSlack("Can't find easypost apikey default!", $urlHook->value ?? '', 'Easypost aip key', 'Not found Easypost aip key');
                $labelRepository->sendGoogleChat("Can't find easypost apikey default!", $urlGoogleChat->value);

                return;
            }

            $getNameShippingMethodSwiftpod = $labelRepository->getNameShippingMethodSwiftpod()->toArray();
            if (empty($getNameShippingMethodSwiftpod)) {
                echo "Can't find name shipping method swiftpod! \n";
                $labelRepository->messageToSlack("Can't find name shipping method swiftpod!", $urlHook->value ?? '', 'Name shipping method swiftpod', 'Not found name shipping method swiftpod');
                $labelRepository->sendGoogleChat("Can't find name shipping method swiftpod!", $urlGoogleChat->value);

                return;
            }

            if (!$saleOrder->store) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with store.", $urlHook, $urlGoogleChat);

                return;
            }

            if (isset($storeInvalid[$saleOrder->store_id])) {
                echo "order has in store invalid \n";

                return;
            }
            if (!$saleOrder->warehouse) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with warehouse.", $urlHook, $urlGoogleChat);

                return;
            }
            if ($saleOrder->barcodeItems->count() === 0) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not found sale order item barcode.", $urlHook, $urlGoogleChat);

                return;
            }

            // check product type not create auto shipping
            $checkProductTypeNotAuto = $labelRepository->checkProductTypeAutoShipping($saleOrder->items, $arrValueConditionProductType);
            if ($checkProductTypeNotAuto) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: has product not create label auto.", $urlHook, $urlGoogleChat);

                return;
            }

            if (empty($saleOrder->shipping_method)) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not found shipping method.", $urlHook, $urlGoogleChat);

                return;
            }

            // check product of order is hard goods
            // true: has product hard goods
            // false: not product hard goods
            $isProductHardGood = $saleOrder->items->where('productStyle.productType.is_hard_goods', true)->isNotEmpty();
            $isStoreHardGoodUseUsps = $isProductHardGood && in_array($saleOrder->store_id, $arrStoreHardGoodsShipUsps) ? true : false;

            $dataAddressTo = $labelRepository->dataAddressTo($saleOrder->addressSaleOrder, true, $saleOrder, $arrValueConditionGetAddressTo);
            if (!$dataAddressTo) {
                echo "Sale order: $saleOrder->order_number has not address to. \n";
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, 'Not found address to.');
                $labelRepository->messageToSlack("Sale order: $saleOrder->order_number has not address to", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                return;
            }
            if (empty($dataAddressTo['country'])) {
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, 'Not found address to.');
                $labelRepository->messageToSlack("Sale order: $saleOrder->order_number has not country of address to", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                return;
            }
            $isDataAddressUS = strtoupper($dataAddressTo['country']) == 'US';
            $storeProductWeight = [];
            if ($saleOrder->store_id == Store::PRINTIFY_API_ID) {
                $listSku = $saleOrder->items->pluck('product_sku')->unique()->toArray();
                $storeProductWeight = StoreProductWeight::where('store_id', $saleOrder->store_id)
                    ->whereIn('product_sku', $listSku)
                    ->get()->pluck('weight', 'product_sku');
            }
            $customsInfo = $labelRepository->getDataCustomsInfo($saleOrder, $isDataAddressUS, $dataAddressTo, $storeProductWeight);
            $dataAddressReturn = $labelRepository->dataAddressReturn($saleOrder->storeAddress, $saleOrder->merchant_name, $saleOrder->addressSaleOrder);
            if (empty($dataAddressReturn)) {
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, "Sale order $saleOrder->order_number: has not return address.");
                $labelRepository->messageToSlack("Sale order $saleOrder->order_number: has not return address.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                return;
            }

            $weightOrder = calculatorWeightForSaleOrder($saleOrder, $storeProductWeight);
            if ($weightOrder == 0) {
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, 'sale order has weight = 0oz.');
                $labelRepository->messageToSlack("Sale order $saleOrder->order_number: has weight = 0oz.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                return;
            }

            if ($weightOrder > 60) {
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, 'sale order has weight > 60oz.');
                $labelRepository->messageToSlack("Sale order $saleOrder->order_number: has weight > 60oz.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                return;
            }

            $dataCubicSaleOrder = $labelRepository->getDataCubicSaleOrder($weightOrder, $dataCubic);
            if (count($dataCubicSaleOrder) != 3 && count($dataCubicSaleOrder) != 0) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order: $saleOrder->order_number has data cubic invalid.", $urlHook, $urlGoogleChat);

                return;
            }

            // check api key easypost
            // true: has api key
            // false: not api key
            $isStoreHasApiKey = !empty($saleOrder->store->easypost_api_key);
            $isDomestic = in_array(strtoupper($dataAddressTo['country']), StoreShipment::DOMESTIC_SHIPPING);
            $carrierId = $labelRepository->getCarrierForAutoCreateShippingLabel($isDomestic, $saleOrder->shipping_method, $shippingCarriers, $dataShippingMethods, $saleOrder->store->id, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps, $weightOrder);
            if (!$carrierId) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping carrier.", $urlHook, $urlGoogleChat);

                return;
            }

            $carrierCode = $shippingCarriers->where('id', $carrierId)->first()->code ?? '';
            if (empty($carrierCode)) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping carrier.", $urlHook, $urlGoogleChat);

                return;
            }
            // check xem co phai la mug 11O hoặc 150 khong
            $isMug11O = false;
            $isMug15O = false;
            foreach ($saleOrder->items as $item) {
                if ($item->product_size_sku === ProductSize::SIZE_11OZ_SKU) {
                    $isMug11O = true;
                }
                if ($item->product_size_sku === ProductSize::SIZE_15OZ_SKU) {
                    $isMug15O = true;
                }
            }

            $dataAddressFrom = $labelRepository->dataAddressFrom($saleOrder->warehouse, $saleOrder->store?->name, $carrierCode);
            if ($saleOrder->store_id == Store::PRINTIFY_API_ID && $carrierCode !== ShippingCarrier::ONTRACV3_CODE) {
                $dataParcel = [
                    'length' => '',
                    'width' => '',
                    'height' => '',
                ];
            } else {
                $dataParcel = [
                    'length' => ($dataCubicSaleOrder[0] == 0 && ($isMug11O || $isMug15O)) ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[0] : ProductSize::DIMENSION_MUG_15O[0])
                        : ($dataCubicSaleOrder[0] == 0 ? (($carrierCode == LabelRepository::DEFAULT_CARRIER_DOMESTIC || $carrierCode == ShippingCarrier::ONTRACV3_CODE || $carrierCode == ShippingCarrier::PASSPORT_CODE) ? 9 : '') : $dataCubicSaleOrder[0]),
                    'width' => ($dataCubicSaleOrder[1] == 0 && ($isMug11O || $isMug15O)) ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[1] : ProductSize::DIMENSION_MUG_15O[1])
                        : ($dataCubicSaleOrder[1] == 0 ? (($carrierCode == LabelRepository::DEFAULT_CARRIER_DOMESTIC || $carrierCode == ShippingCarrier::ONTRACV3_CODE || $carrierCode == ShippingCarrier::PASSPORT_CODE) ? 6 : '') : $dataCubicSaleOrder[1]),
                    'height' => ($dataCubicSaleOrder[2] == 0 && ($isMug11O || $isMug15O)) ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[2] : ProductSize::DIMENSION_MUG_15O[2])
                        : ($dataCubicSaleOrder[2] == 0 ? (($carrierCode == LabelRepository::DEFAULT_CARRIER_DOMESTIC || $carrierCode == ShippingCarrier::ONTRACV3_CODE || $carrierCode == ShippingCarrier::PASSPORT_CODE) ? 2 : '') : $dataCubicSaleOrder[2]),
                ];
            }

            // noi dia thi moi dung con quoc te thi sẽ van dùng asendiaUsa
            if ($carrierCode == ShippingCarrier::USPS_ECOMMERCE_CODE && $saleOrder->shipping_method == ShippingMethod::SHIPPING_METHOD_FIRST_CLASS && $isDomestic) {
                $dataParcel['predefined_package'] = ShippingCarrierPackage::PACKAGE_LETTER_USPS;
            }

            // neu kl >= 16 thi USPS can gui sang voi KL tu 304 oz tro len
            if ($saleOrder->store_id == Store::PRINTIFY_API_ID) {
                $dataParcel['weight'] = $weightOrder;
            } else {
                $dataParcel['weight'] = $weightOrder >= WeightCubic::WEIGHT_START_HAS_CUBIC && $isDomestic && in_array($carrierCode, ShippingCarrier::CARRIER_NEED_SIZE_CUBIC) ? $labelRepository->weightSendToEasypost($weightOrder) : $weightOrder;
            }

            $dataGetShippingMethod = $labelRepository->getDataShippingMethodByStoreAndCarrier($dataShippingMethods, $saleOrder->shipping_method, $saleOrder->store->id, $carrierId);

            if (!$dataGetShippingMethod) {
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping method.", $urlHook, $urlGoogleChat);

                return;
            }
            $dataShippingCarrierEasypost = $carrierAccounts->where('carrier_id', $dataGetShippingMethod->carrier_id)
                ->where('store_id', $dataGetShippingMethod->store_id)
                ->where('name', $dataGetShippingMethod->name)
                ->where('warehouse_id', $saleOrder->warehouse_id)
                ->where('status', true)
                ->first();

            if (!$dataShippingCarrierEasypost || (empty($dataShippingCarrierEasypost->carrier_account) || empty($dataShippingCarrierEasypost->api_key_easypost))) {
                $saleOrder->store->auto_shipping_pending_at = now();
                $saleOrder->store->save();
                $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order: $saleOrder->order_number Not found carrier account or easypost apikey.", $urlHook, $urlGoogleChat);

                return;
            }
            $text = '';
            if (in_array($saleOrder->warehouse_id, Warehouse::WAREHOUSE_MEXICO)) {
                $text = $labelRepository->getTextForLabelOfMexico($saleOrder->barcodeItems);
            }
            try {
                EasyPost::setApiKey($dataShippingCarrierEasypost->api_key_easypost);
                $data = [
                    'carrier_accounts' => $dataShippingCarrierEasypost->carrier_account,
                    'from_address' => $dataAddressFrom,
                    'to_address' => $dataAddressTo,
                    'parcel' => $dataParcel,
                    'return_address' => $dataAddressReturn,
                    'reference' => $saleOrder->order_number,
                    'customs_info' => $customsInfo
                ];
                $options = [];

                // add part number for create label
                if (in_array($saleOrder->warehouse_id, Warehouse::WAREHOUSE_MEXICO)) {
                    $options['print_custom_1'] = $text;
                }
                if ($saleOrder->store_id == Store::STORE_REDBUBBLE && in_array($carrierCode, [ShippingCarrier::UPS_MI_CODE, ShippingCarrier::FEDEX_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])) {
                    $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
                } elseif (in_array($carrierCode, [ShippingCarrier::DHL_ECOMMERCE_CODE])) {
                    $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
                }

                // set time for not create manifest in easypost system
                if ($carrierCode == ShippingCarrier::USPS_ECOMMERCE_CODE) {
                    $options['date_advance'] = LabelRepository::DATE_ADVANCE_MANIFEST_EASYPOST;
                }

                if (strtoupper($dataAddressTo['country']) !== 'US' && !empty($saleOrder->ioss_number)) {
                    $options['print_custom_2'] = ($saleOrder->tax_id_type ?? SaleOrder::TAX_ID_TYPE_IOSS) . ': #' . $saleOrder->ioss_number;
                }

                if (in_array($saleOrder->store_id, [Store::STORE_PMALL, Store::STORE_SAMCLUB]) && $carrierCode == ShippingCarrier::FEDEX_CODE) {
                    $options['payment'] = Shipment::PAYMENT_SHIPMENT[$saleOrder->store_id];
                }

                if (!empty($options)) {
                    $data['options'] = $options;
                }

                if (!empty($saleOrder->ioss_number)) {
                    $data['tax_identifiers'] = [
                        [
                            'entity' => 'SENDER',
                            'tax_id' => $saleOrder->ioss_number,
                            'tax_id_type' => $saleOrder->tax_id_type,
                            'issuing_country' => $dataAddressTo['country'],
                        ]
                    ];
                }
                //check sale order da co shipment
                $isCheckOrderHasShipment = $labelRepository->isCheckOrderHasShipment($saleOrder->id);
                if ($isCheckOrderHasShipment) {
                    echo "Order $saleOrder->id has shipment \n";

                    return;
                }

                if (in_array($saleOrder->shipping_method, [ShippingMethod::SHIPPING_METHOD_REDBUBLE_DDP_PASSPORT])) {
                    $shippingCarrierService = ShippingCarrierService::where('id', $dataGetShippingMethod->shipping_carrier_service_id)->first();
                    if (!$shippingCarrierService || empty($shippingCarrierService->name)) {
                        $labelRepository->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping carrier service.", $urlHook, $urlGoogleChat);

                        return;
                    }
                    $data['service'] = $shippingCarrierService->name;
                }

                $shipment = ShipmentEasyPost::create($data);
                // check create shipment error
                if (!empty($shipment->messages)) {
                    foreach ($shipment->messages as $error) {
                        // TH printify và ship bằng ontrac mà có loi thì se update logic shipping method va
                        if ($saleOrder->store_id == Store::PRINTIFY_API_ID && $carrierCode == ShippingCarrier::ONTRACV3_CODE
                            && ($error->message == 'Carrier API returned error: OutOfServiceArea' || $error->message == 'PO Box destination addresses are not supported')) {
                            // log history
                            saleOrderHistory(
                                User::SYSTEM,
                                null,
                                $saleOrder->id,
                                SaleOrderHistory::UPDATE_SHIPPING_METHOD_TYPE,
                                "Shipping method changed from $saleOrder->shipping_method to " . ShippingMethod::SHIPPING_METHOD_PRINTIFY_USPS_GROUND . ' by create label.');
                            // update shipping method
                            $saleOrder->shipping_method = ShippingMethod::SHIPPING_METHOD_PRINTIFY_USPS_GROUND;
                            $saleOrder->save();
                            // dispatch job create label
                            dispatch(new CreateShippingLabelJob((int) $saleOrder->id))->onQueue(QueueJob::QUEUE_CREATE_SHIPPING_LABEL);
                        }

                        $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, "Sale order: $saleOrder->order_number " . $error->type . ' ' . $error->message);
                        $labelRepository->messageToSlack("Sale order: $saleOrder->order_number " . $error->type . ' ' . $error->message, $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);
                    }

                    return;
                }

                $checkOrderHasShipment = SaleOrder::where('id', $saleOrder->id)->whereNotNull('shipment_id')->first();
                if ($checkOrderHasShipment) {
                    echo "Order $saleOrder->id has shipment \n";

                    return;
                }

                // mua label thang
                if (in_array($saleOrder->shipping_method, [ShippingMethod::SHIPPING_METHOD_REDBUBLE_DDP_PASSPORT])) {
                    $label = $shipment;
                } else {
                    // TH lay rate va mau shipping label
                    if (in_array($saleOrder->shipping_method, LabelRepository::SHIPPING_METHOD_DEFAULT_AUTO)) {
                        $rate = $isDomestic
                            ? $labelRepository->getRateDomestic($saleOrder, $shipment, $weightOrder, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps)
                            : $labelRepository->getRateInternational($shipment, $dataAddressTo['country'], $saleOrder);
                    } elseif ($saleOrder->shipping_method == ShippingMethod::ECONOMY && $isDomestic) {
                        $rate = $labelRepository->getRateDomestic($saleOrder, $shipment, $weightOrder, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps);
                    } else {
                        $rate = $labelRepository->getRateWithShippingMethod($dataGetShippingMethod->shippingCarrierService->name ?? null, $shipment);
                    }
                    if (empty($rate)) {
                        $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, "Sale order $saleOrder->order_number: not map rate with Easypost.");
                        $labelRepository->messageToSlack("Sale order $saleOrder->order_number: not map rate with Easypost.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                        return;
                    }
                    if ($carrierCode == ShippingCarrier::ONTRACV3_CODE) {
                        $label = $shipment
                            ->buy([
                                'rate' => $rate,
                                'insurance' => 0
                            ]);
                    } else {
                        $label = $shipment
                            ->buy([
                                'rate' => $rate,
                                'insurance' => 0
                            ])
                            ->label(['file_format' => 'ZPL']);
                    }
                }
            } catch (EasyPostError $e) {
                if ($e->ecode === 'PAYMENT_REQUIRED') {
                    $labelRepository->messageToSlack('PAYMENT_REQUIRED - ' . $e->getMessage(), $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,order number: ' . $saleOrder->order_number);
                    $labelRepository->sendGoogleChat('`Store: ' . $saleOrder->store->name . '`' . 'PAYMENT_REQUIRED - ' . $e->getMessage(), $urlGoogleChat->value);
                    echo "Create label error for PAYMENT_REQUIRED \n";
                    // log cho store đó time de khong phai query vào nữa
                    $saleOrder->store->auto_shipping_pending_at = now();
                    $saleOrder->store->save();

                    return;
                }
                $labelRepository->messageToSlack($e->getHttpBody(), $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);
                echo "Create label error $saleOrder->id \n";
                $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, $e->getHttpBody());

                return;
            }
            DB::beginTransaction();
            $shippingAccount = $easypostApiKeyDefault->value === $dataShippingCarrierEasypost->api_key_easypost
                ? ((in_array($dataShippingCarrierEasypost->store_id, $arrStoreUsesAccountSwiftpod) || in_array($dataShippingCarrierEasypost->name, $getNameShippingMethodSwiftpod)) ? 'swiftpod' : 'swiftpod_store')
                : 'store';
            $shipmentId = $labelRepository->storeShipment($label, $saleOrder, null, $shippingAccount, LabelRepository::IS_AUTO, $dataShippingCarrierEasypost->carrier_account);
            echo "shipment_id $shipmentId \n";
            $shipmentStatus = $label->status ?? null;
            $shipmentEasypost = Util::convertEasyPostObjectToArray($label);
            $dataLog = [
                'order_id' => $saleOrder->id,
                'shipment_id' => $shipmentId,
                'easypost_id' => isset($shipmentEasypost['id']) ? $shipmentEasypost['id'] : null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'track_status' => $shipmentStatus
            ];
            ShipmentEasypostModel::insert($dataLog);
            $saleOrder->shipment_id = $shipmentId;
            $saleOrder->order_status = SaleOrder::SHIPPED;
            $saleOrder->save();

            // gan shipment voi package với store của RB
            if ($saleOrder->store_id == Store::STORE_REDBUBBLE) {
                $isShipmentPackageByOrderId = ShipmentPackage::query()->where('order_id', $saleOrder->id)->first();
                $dataShipmentPackage = [];
                if (!$isShipmentPackageByOrderId) {
                    $dataShipmentPackage[] = [
                        'shipment_id' => $shipmentId,
                        'quantity' => 1,
                        'order_id' => $saleOrder->id,
                        'sku' => $labelRepository->addShipmentWithPackage($saleOrder->order_quantity, $dataRbPackage)
                    ];
                    $dataInsertSku = $labelRepository->getInsertForRedbubble()?->rb_sku;
                    if (!empty($dataInsertSku)) {
                        $dataShipmentPackage[] = [
                            'shipment_id' => $shipmentId,
                            'quantity' => 1,
                            'order_id' => $saleOrder->id,
                            'sku' => $dataInsertSku
                        ];
                    }
                    ShipmentPackage::insert($dataShipmentPackage);
                }
            }

            //verify label id tu dong gan voi shipment da chon
            $labelRepository->verifyLabelIdWithShipmentAuto($saleOrder->items, $shipmentId);

            //luu shipment fee của easypost tra ve luc mua label
            $dataShipmentFee = [];
            foreach ($label->fees as $itemShipmentFee) {
                $dataShipmentFee[] = [
                    'shipment_id' => $shipmentId,
                    'type' => $itemShipmentFee->type ?? null,
                    'amount' => $itemShipmentFee->amount ?? null
                ];
            }

            if (!empty($dataShipmentFee)) {
                ShipmentFee::insert($dataShipmentFee);
            }

            saleOrderHistory(User::SYSTEM, null, $saleOrder->id, SaleOrderHistory::CREATE_LABEL, "Label $label?->tracking_code successfully created by Auto Shipping.");
            DB::commit();

            if (in_array($shippingAccount, ['swiftpod', 'swiftpod_store'])) {
                // dispatch queue recycled tracking number
                $dataDispatchTracking = [
                    'tracking_code' => $label->tracking_code,
                    'order_id' => $saleOrder->id,
                    'shipment_id' => $shipmentId,
                    'order_number' => $saleOrder->order_number,
                    'order_type' => $saleOrder->order_type
                ];
                handleJob(QueueJob::QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER, $dataDispatchTracking);
            }

            echo "Create label shipping for sale order $saleOrder->id success \n";
        } catch (\Exception $exception) {
            DB::rollBack();
            echo "Create label shipping fail for sale order $saleOrder?->id \n";
            $labelRepository->logConvertErrorCreateShippingLabel($saleOrder, $exception->getMessage());
            $labelRepository->messageToSlack("Sale order $saleOrder?->order_number error: " . $exception->getMessage(), $urlHook?->value ?? '', $saleOrder?->store?->name, $saleOrder?->store?->id . ' ,Order number: ' . $saleOrder?->order_number);
            Log::channel('queue_create_shipping_label')->error($exception->getMessage());
            throw new \Exception($exception->getMessage());
        }
    }
}
