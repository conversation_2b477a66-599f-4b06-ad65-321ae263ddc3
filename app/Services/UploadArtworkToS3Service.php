<?php

namespace App\Services;

use App\Models\ImageHash;
use App\Models\PrintMethod;
use App\Models\ProductColor;
use App\Models\ProductPrintArea;
use App\Models\ProductStyle;
use App\Models\ProductStyleIccProfile;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\ProductPrintAreaRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UploadArtworkToS3Service
{
    public $pathS3 = '/artwork/';

    protected $pathS3IccConvert = '/artwork/icc/';

    protected $attempts;

    protected $maxRetries;

    public function __construct($imageId, $attempts, $maxRetries)
    {
        $this->imageId = $imageId;
        $this->attempts = $attempts;
        $this->maxRetries = $maxRetries;
    }

    public function handle()
    {
        try {
            $item = SaleOrderItemImage::with('product.productStyle', 'printSide:code,code_name,name')->find($this->imageId);

            if (!$item) {
                $item = SaleOrderItemImage::find($this->imageId);
                Log::channel('image')->warning("Upload to S3: $this->imageId not found");
                throw new \Exception("Upload to S3: $this->imageId not found");
            }

            $hash = ImageHash::find($item->image_hash_id);

            if (!$hash) {
                $item = SaleOrderItemImage::find($this->imageId);
                Log::channel('image')->warning("Upload to S3: hash id $item->image_hash_id not found");
                throw new \Exception("Upload to S3: hash id $item->image_hash_id not found");
            }

            // @todo print side & check extension file
            $side = $item->print_side;
            $path = $item->order_date . '/' . $item->sku . '-' . $side . '.png';

            if (!empty($item->product?->productStyle?->type) && $item->product?->productStyle?->type == ProductStyle::TYPE_INSERT && $item->image_ext == 'pdf') {
                $path = $item->order_date . '/' . $item->sku . '-' . $side . '.' . $item->image_ext;
            }

            $pathFileS3 = $this->pathS3 . $path;
            $download = $this->addHttp($item->image_url);
            $download = file_get_contents($download);
            $hashDownload = md5($download);

            if ($hashDownload != $hash->hash_md5) {
                Log::channel('image')->error('Upload to S3:: hash failed id: ' . $item->id);

                if ($this->attempts == $this->maxRetries && !empty($item)) {
                    OrderIssueService::logIssue([
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'type' => SaleOrderHistory::UPLOAD_S3_ERROR,
                        'side' => $item?->printSide?->code_name ?? '',
                        'side_name' => $item?->printSide?->name ?? '',
                    ]);
                }
                $this->setError($item);

                return;
            }

            // check path is png
            if ($item->image_ext != 'pdf') {
                // neu dung luong file > 50MB thi optimize lai
                if (strlen($download) > 20 * 1024 * 1024) {
                    $imagick = new \Imagick();
                    $imagick->readImageBlob($download);
                    $imagick->stripImage(); // Loại bỏ tất cả thông tin màu sắc hoặc profile ICC từ hình ảnh
                    jobEcho('optimize image');
                    $download = $imagick->getImageBlob();
                    $imagick->clear();
                    $imagick->destroy();
                }
            }

            $checkRotate = ProductPrintArea::where('name', $item->printSide?->name)->where('product_style_id', $item->product?->productStyle?->id)->first();

            if ($checkRotate && $checkRotate->rotate) {
                $imagick = new \Imagick();
                $imagick->readImageBlob($download);
                // xoay anh 180 độ
                $imagick->rotateImage(new \ImagickPixel(), 180);
                $download = $imagick->getImageBlob();
                $imagick->clear();
                $imagick->destroy();
            }

            if ($item->store_id != Store::STORE_REDBUBBLE || ($item->store_id == Store::STORE_REDBUBBLE && strpos($item->image_url, 'trimmed') === false)) {
                $productWhiteColorSKU = '1W';
                $isProductWhiteColor = $item->orderItem?->product_color_sku == $productWhiteColorSKU;
                $isEnableConvertICC = $item->orderItem?->ProductStyle?->icc_white_convert_status == 1;
                $isPrintMethodDTG = app(ProductPrintAreaRepository::class)->isPrintMethod(PrintMethod::DTG, $item->orderItem?->ProductStyle?->id, $side);
                $isPrintMethodNeck = app(ProductPrintAreaRepository::class)->isPrintMethod(PrintMethod::NECK, $item->orderItem?->ProductStyle?->id, $side);

                if ($isPrintMethodDTG || $isPrintMethodNeck) {
                    $downloadConvertICC = $download;
                    $product = $item->product;
                    $productColor = ProductColor::where('sku', $item->orderItem?->product_color_sku)->first();
                    $iccColor = match (strtolower($productColor?->icc_color)) {
                        'white' => 'white',
                        'black' => 'black',
                        default => 'colored',
                    };
                    $outputICCProfile = ProductStyleIccProfile::where([
                        'product_style' => $product->style
                    ])->first();
                    $inputProfile = storage_path('app/icc_profile/Color Space Profile.icm');

                    if ($product && $productColor && $outputICCProfile && !empty($outputICCProfile->getAttribute("{$iccColor}_url"))) {
                        $outputProfilePath = match (strtolower($productColor->icc_color)) {
                            'white' => $outputICCProfile->getAttribute('white_url'),
                            'black' => $outputICCProfile->getAttribute('black_url'),
                            default => $outputICCProfile->getAttribute('colored_url'),
                        };
                        echo 'product color ' . $product->color . 'has ICC color ' . $productColor->icc_color . ' and output profile path ' . $outputProfilePath . PHP_EOL;
                        $newIccProfile = colorProfileConverter($downloadConvertICC, $inputProfile, $outputProfilePath);
                        Storage::disk('s3')->put($this->pathS3IccConvert . $path, $newIccProfile);
                        $item->icc_converted_at = now();
                        $item->save();
                    }

                    if ($isProductWhiteColor && $isEnableConvertICC) {
                        $download = colorProfileConverter($download);
                    }
                }

                $s3 = Storage::disk('s3')->put($pathFileS3, $download);

                if (!$s3) {
                    Log::channel('image')->error('Upload to S3 failed id: ' . $item->id);

                    if ($this->attempts == $this->maxRetries && !empty($item)) {
                        OrderIssueService::logIssue([
                            'order_id' => $item->order_id,
                            'order_item_id' => $item->order_item_id,
                            'sku' => $item->sku,
                            'type' => SaleOrderHistory::UPLOAD_S3_ERROR,
                            'side' => $item?->printSide?->code_name ?? '',
                            'side_name' => $item?->printSide?->name ?? '',
                        ]);
                    }
                    $this->setError($item);

                    return;
                }
            }

            Log::channel('image')->info("Upload to S3 $item->id: $pathFileS3 successfully");
            $item->thumb_750 = 1;
            $item->upload_s3_status = SaleOrderItemImage::SYNC_S3_SUCCESS;
            $item->save();
            $data = [
                'order_id' => $item->order_id,
                'order_item_id' => $item->order_item_id,
                'sku' => $item->sku,
                'type' => SaleOrderHistory::UPLOAD_S3_ERROR,
                'side' => $item->printSide->code_name ?? '',
                'side_name' => $item->printSide->name ?? '',
            ];
            OrderIssueService::resolveIssue($data);
        } catch (\Throwable $th) {
            jobEcho($th->getMessage());
            jobEcho("attempts: {$this->attempts}");
            jobEcho("maxtries: {$this->maxRetries}");

            if ($this->attempts == $this->maxRetries && !empty($item)) {
                OrderIssueService::logIssue([
                    'order_id' => $item->order_id,
                    'order_item_id' => $item->order_item_id,
                    'sku' => $item->sku,
                    'type' => SaleOrderHistory::UPLOAD_S3_ERROR,
                    'side' => $item->printSide->code_name ?? '',
                    'side_name' => $item->printSide->name ?? '',
                ]);
                $this->setError($item);
            }

            Log::channel('image')->error("Upload to S3: $this->imageId " . $th->getMessage());
            throw $th;
        }
    }

    protected function setError($image)
    {
        $image->thumb_750 = 2;
        $image->thumb_250 = 2;
        $image->save();
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }
}
