<?php

namespace App\Services;

use App\Http\Service\AlertService;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderDetail;
use App\Models\SaleOrderHistory;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentLabelIssues;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use App\Repositories\SettingRepository;
use App\Repositories\ShipmentLabelIssuesRepository;
use Carbon\Carbon;
use EasyPost\Tracker;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ReadLabelOrderService
{
    protected string $geminiApiUrl;

    protected string $geminiApiKey;

    protected array $responseSchemaProperties;

    protected string $contentsText;

    protected ShipmentLabelIssuesRepository $shipmentLabelIssuesRepository;

    protected SettingRepository $settingRepository;

    public function __construct(ShipmentLabelIssuesRepository $shipmentLabelIssuesRepository, SettingRepository $settingRepository)
    {
        $this->shipmentLabelIssuesRepository = $shipmentLabelIssuesRepository;
        $this->settingRepository = $settingRepository;
        $this->initSetting();
    }

    protected function initSetting(): void
    {
        $jsonSetting = $this->settingRepository->getSettingByName(Setting::READ_LABEL_SHIPMENT_JSON);

        if (!empty($jsonSetting)) {
            $setting = json_decode($jsonSetting->value, true);
            $this->geminiApiKey = $setting['gemini']['key'] ?? '';
            $this->geminiApiUrl = $setting['gemini']['url'] ?? '';
            $this->contentsText = $setting['gemini']['contents_text'] ?? 'Extract the address and tracking number';

            if (isset($setting['gemini']['response_schema_properties']) && is_array($setting['gemini']['response_schema_properties'])) {
                $this->responseSchemaProperties = $setting['gemini']['response_schema_properties'];
            }
        }

        if (empty($this->responseSchemaProperties)) {
            $this->responseSchemaProperties = [
                'sender' => [
                    'type' => 'object',
                    'properties' => [
                        'name' => ['type' => 'string'],
                        'street1' => ['type' => 'string'],
                        'street2' => ['type' => 'string'],
                        'city' => ['type' => 'string'],
                        'state' => ['type' => 'string'],
                        'zipcode' => ['type' => 'string'],
                        'country' => [
                            'type' => 'string',
                            'description' => '2-letter country code (ISO 3166-1 alpha-2, e.g., GB, US, FR)',
                        ],
                    ],
                    'required' => ['name', 'street1', 'street2', 'city', 'state', 'zipcode', 'country'],
                ],
                'recipient' => [
                    'type' => 'object',
                    'properties' => [
                        'name' => ['type' => 'string'],
                        'street1' => ['type' => 'string'],
                        'street2' => ['type' => 'string'],
                        'city' => ['type' => 'string'],
                        'state' => ['type' => 'string'],
                        'zipcode' => ['type' => 'string'],
                        'country' => [
                            'type' => 'string',
                            'description' => '2-letter country code (ISO 3166-1 alpha-2, e.g., GB, US, FR)',
                        ],
                    ],
                    'required' => ['name', 'street1', 'street2', 'city', 'state', 'zipcode', 'country'],
                ],
                'tracking_number' => ['type' => 'string'],
                'carrier_code' => [
                    'type' => 'string',
                    'description' => 'if "Asendia" then return the text "AsendiaUsa"',
                ],
                'service_code' => ['type' => 'string'],
                'service_name' => ['type' => 'string'],
            ];
        }
    }

    public function handleReadLabel($orderId, $shipmentId, $labelUrl = null): bool
    {
        try {
            if (empty($this->geminiApiKey) || empty($this->geminiApiUrl)) {
                throw new \Exception('Setting Gemini URL or KEY invalid');
            }

            if (empty($labelUrl) && !Storage::disk('s3')->exists("label/{$shipmentId}.pdf")) {
                throw new \Exception('Label url invalid');
            }

            $labelUrl = $labelUrl ?? Storage::disk('s3')->url("label/{$shipmentId}.pdf");
            $data = $this->formatWithGemini($labelUrl);
            $data = $this->reformatData(!empty($data) ? $data : []);
            $addressFrom = $data['sender'] ?? [];
            $addressTo = $data['recipient'] ?? [];
            $trackingNumber = str_replace(' ', '', trim($data['tracking_number'] ?? ''));
            $carrierCode = str_replace(' ', '', trim($data['carrier_code'] ?? ''));
            $serviceCode = str_replace(' ', '', trim($data['service_code'] ?? ''));
            $serviceName = str_replace(' ', '', trim($data['service_name'] ?? ''));

            if (empty($trackingNumber)) {
                throw new \Exception('No find tracking number.');
            }

            $countryFrom = $addressFrom['country'] ?? '';
            $countryTo = $addressTo['country'] ?? '';

            if (empty($countryFrom)) {
                throw new \Exception('Invalid address format (from country address not found).');
            }

            if (empty($countryTo)) {
                throw new \Exception('Invalid address format (to country address not found).');
            }

            $addressFromData = [
                [
                    'type_address' => 'return_address',
                    'order_id' => $orderId,
                    'name' => $addressFrom['name'] ?? '',
                    'street1' => $addressFrom['street1'] ?? '',
                    'street2' => $addressFrom['street2'] ?? '',
                    'city' => $addressFrom['city'] ?? '',
                    'state' => $addressFrom['state'] ?? '',
                    'zip' => $addressFrom['zipcode'] ?? '',
                    'country' => $countryFrom,
                ]
            ];

            $addressData = [
                [
                    'type_address' => 'to_address',
                    'order_id' => $orderId,
                    'name' => $addressTo['name'] ?? '',
                    'street1' => $addressTo['street1'] ?? '',
                    'street2' => $addressTo['street2'] ?? '',
                    'city' => $addressTo['city'] ?? '',
                    'state' => $addressTo['state'] ?? '',
                    'zip' => $addressTo['zipcode'] ?? '',
                    'country' => $countryTo,
                ]
            ];

            $addressFromData = $this->reformatData($addressFromData);
            $addressData = $this->reformatData($addressData);
            $currentSaleOrderAddress = SaleOrderAddress::where('order_id', $orderId)
                ->where('type_address', 'to_address')
                ->first();
            $_oldAddress = $this->concatAddress($currentSaleOrderAddress ? $currentSaleOrderAddress->toArray() : []);
            $_newAddress = $this->concatAddress($addressData[0]);
            $messages = [];
            $messages[] = "Tracking Update Timeline for $trackingNumber.";
            $messages[] = "- Shipping address update:\n&nbsp;&nbsp;&nbsp;From: \"{$_oldAddress}\"\n&nbsp;&nbsp;&nbsp;To: \"$_newAddress\"";

            $currentSaleOrderReturnAddress = SaleOrderAddress::where('order_id', $orderId)
                ->where('type_address', 'return_address')
                ->first();
            $_oldReturnAddress = $this->concatAddress($currentSaleOrderReturnAddress ? $currentSaleOrderReturnAddress->toArray() : []);
            $_newReturnAddress = $this->concatAddress($addressFromData[0]);
            $messages[] = "- Return address update:\n&nbsp;&nbsp;&nbsp;From: \"{$_oldReturnAddress}\"\n&nbsp;&nbsp;&nbsp;To: \"$_newReturnAddress\"";

            SaleOrderAddress::upsert(array_merge($addressData, $addressFromData), ['type_address', 'order_id']);
            SaleOrderDetail::query()->upsert([
                'order_id' => $orderId,
                'label_status' => SaleOrderDetail::READ_LABEL_SUCCESS,
            ], ['order_id']);
            $shipment = Shipment::where('id', $shipmentId)->first();
            $oldCarrierCode = $shipment->carrier_code ?? '';
            $oldServiceCode = $shipment->service_code ?? '';
            Log::info('ReadLabelOrderService.handleReadLabel: Read label order success', [
                'upsert SaleOrderAddress From' => $addressFromData,
                'upsert SaleOrderAddress To' => $addressData,
                'update Shipment' => ['tracking_number' => $trackingNumber]
            ]);
            $trackerException = null;

            $shipment->tracking_number = $trackingNumber;
            $carrierData = $this->getCarrierAndService($carrierCode, $serviceCode, $serviceName);
            $carrierCode = $carrierData['carrier'] ?? null;
            $serviceCode = $carrierData['service'] ?? null;

            $shipment->carrier_code = $carrierCode;
            $messages[] = "- Carrier change: \"$oldCarrierCode\" => \"$carrierCode\"";

            $shipment->service_code = $serviceCode;
            $messages[] = "- Service code update: \"$oldServiceCode\" => \"$serviceCode\"";

            $shipment->save();
            try {
                // tao checker goi sang easypost de sau con nhan duoc status cua tracking number
                // dang chi check cho USPS nen truyen luon carrier la USPS
                $easypostApiKeyDefault = Setting::where('name', Setting::EASYPOST_API_KEY)->first()?->value ?? '';

                if (!empty($easypostApiKeyDefault)) {
                    $dataTracker = [
                        'tracking_code' => $trackingNumber
                    ];
                    if (!empty($carrierCode) && $carrierCode == ShippingCarrier::USPS_ECOMMERCE_CODE) {
                        $dataTracker['carrier'] = $carrierCode;
                    }
                    Tracker::create($dataTracker, $easypostApiKeyDefault);
                }
            } catch (\Exception $e) {
                $trackerException = $e;
            }
            $this->createSaleOrderHistory($orderId, implode("\n", $messages));

            if (!empty($trackerException)) {
                throw $trackerException;
            }

            return true;
        } catch (\Exception $e) {
            $order = SaleOrder::where('id', $orderId)->first();
            SaleOrderDetail::query()->upsert([
                'order_id' => $orderId,
                'label_status' => SaleOrderDetail::READ_LABEL_FAILED,
            ], ['order_id']);
            $shipment = Shipment::where('id', $shipmentId)->first();

            if (empty($trackerException)) {
                $this->createSaleOrderHistory($orderId, 'Failed to extract address from label.');
            }

            $dataLog = [
                'shipment_id' => $shipmentId,
                'issue_type' => ShipmentLabelIssues::ISSUE_TYPE_READ,
                'issues' => [
                    'exception' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ],
            ];
            Log::error('ReadLabelOrderService.handleReadLabel: Read label order error', $dataLog);
            $this->shipmentLabelIssuesRepository->log($dataLog);
            $alertService = new AlertService();
            $orderNumber = $order->order_number ?? 'unknown';
            $externalNumber = $order->external_number ?? 'unknown';
            $labelUrl = $shipment->label_url ?? 'unknown';
            $exceptionLabel = !empty($trackerException) ? 'Tracker Exception' : 'Exception';
            $alertService->alertShipmentLabelIssue("Read label order error.\nOrder number: {$orderNumber}\nOrder Ref Number: {$externalNumber}\nLabel url: {$labelUrl}\n$exceptionLabel");

            return false;
        }
    }

    public function getCarrierAndService(string $carrierCode, string $serviceCode, string $serviceName): array
    {
        if (empty($carrierCode)) {
            return [];
        }

        $carrier = ShippingCarrier::query()
            ->whereRaw("REPLACE(REPLACE(code, '_', ''), ' ', '') = ?", [str_replace(['_', ' '], '', $carrierCode)])
            ->first();

        if (empty($carrier) || empty($carrier->id)) {
            return [];
        }

        $servicePriority = ShippingCarrierService::query()
            ->where('carrier_id', $carrier->id)
            ->whereRaw("REPLACE(REPLACE(name, '_', ''), ' ', '') = ?", [str_replace(['_', ' '], '', $serviceCode)])
            ->first();

        if (!empty($servicePriority)) {
            return [
                'carrier' => $carrier->code ?? null,
                'service' => $servicePriority->name ?? null,
            ];
        }

        $service = ShippingCarrierService::query()
            ->where('carrier_id', $carrier->id)
            ->whereRaw("REPLACE(REPLACE(name, '_', ''), ' ', '') = ?", [str_replace([$carrierCode, '_', ' '], '', $serviceName)])
            ->first();

        return [
            'carrier' => $carrier->code ?? null,
            'service' => $service->name ?? null,
        ];
    }

    public function reformatData(array $data)
    {
        return array_map(function ($item) {
            if (is_array($item)) {
                foreach ($item as &$value) {
                    $value = trim($value);

                    if ($value === 'null') {
                        $value = '';
                    }
                }
            }

            if ($item === 'null') {
                $item = '';
            }

            return $item;
        }, $data);
    }

    public function createSaleOrderHistory($orderId, $message)
    {
        SaleOrderHistory::create([
            'user_id' => null,
            'employee_id' => null,
            'order_id' => $orderId,
            'type' => SaleOrderHistory::READ_LABEL_ORDER,
            'message' => $message,
            'created_at' => Carbon::now()->toDateTimeString()
        ]);
    }

    public function concatAddress($address)
    {
        $addressArray = [];
        !empty($address['name']) && $addressArray[] = $address['name'];
        !empty($address['street1']) && $addressArray[] = $address['street1'];
        !empty($address['street2']) && $addressArray[] = $address['street2'];
        !empty($address['city']) && $addressArray[] = $address['city'];
        !empty($address['state']) && $addressArray[] = $address['state'];
        !empty($address['zip']) && $addressArray[] = $address['zip'];
        !empty($address['country']) && $addressArray[] = $address['country'];

        return implode(', ', $addressArray);
    }

    /**
     * @throws \Exception
     */
    public function formatWithGemini(string $url)
    {
        $base64Pdf = base64_encode(file_get_contents($url));

        $payload = [
            'generation_config' => [
                'temperature' => 1,
                'top_p' => 0.95,
                'top_k' => 40,
                'max_output_tokens' => 8192,
                'response_schema' => [
                    'type' => 'object',
                    'properties' => $this->responseSchemaProperties,
                ],
                'response_mime_type' => 'application/json'
            ],
            'contents' => [
                [
                    'parts' => [
                        [
                            'inlineData' => [
                                'mimeType' => 'application/pdf',
                                'data' => $base64Pdf,
                            ]
                        ],
                        [
                            'text' => $this->contentsText,
                        ]
                    ]
                ]
            ]
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post($this->geminiApiUrl . '?key=' . $this->geminiApiKey, $payload);

        if ($response->failed()) {
            throw new \Exception('Error calling Gemini API: ' . $response->body());
        }

        $result = $response->json();

        if (!isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response from Gemini API');
        }

        $rawResponse = $result['candidates'][0]['content']['parts'][0]['text'];

        return json_decode($rawResponse, true);
    }
}
