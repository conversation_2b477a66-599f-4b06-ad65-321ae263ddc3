<?php

namespace App\Services\integrations;

use Exception;
use Illuminate\Support\Facades\Http;

class GeminiClient
{
    private $geminiApiKey;
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->geminiApiKey = config('gemini.api_key');
        $this->baseUrl = config('gemini.base_url');
        $this->client = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->timeout(30);
    }

    public function analyzeImage($base64Image)
    {
        $requestBody = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'inlineData' => [
                                'mimeType' => 'image/png',
                                'data' => $base64Image
                            ]
                        ],
                        [
                            'text' => 'Analyze this image and identify all recognizable entities that may be associated with trademarks, intellectual property, or well-known content. These include, but are not limited to: logos, brands (well-known or niche), singers, celebrities (e.g. actors, athletes, influencer), cartoon characters, fictional characters from movies, shows, comics, or games.
                            Return the result as a JSON array. Each item must have:

                            - name: the name of the entity or character
                            - type: one of ["logo", "brand", "singer", "celebrity", "character"]

                            Rules:
                            1. If you detect a singer, return:
                            - { "name": "<Singer\'s Real Name>", "type": "singer" }
                            2. If you detect an actor, athlete, influencer, or other celebrity (non-singer), return:
                            - { "name": "<Celebrity\'s Real Name>", "type": "celebrity" }
                            3. If the singer/celebrity is portraying a fictional character in a movie/show, return two items:
                            - { "name": "<Character Name>", "type": "character" }
                            - and the real person item as per rule 1 or 2 above.
                            4. If only a fictional character is detected and the performer cannot be identified, return just the character item.
                            5. Include logos and brands even if partially visible or stylized.
                            6. Include cartoon or stylized characters even without text labels.
                            7. Be conservative: if an entity might be related to a trademark or IP, include it.
                            8. Avoid duplicate entries.

                            Return only the JSON array. If no recognizable entity is found, return an empty array [].'
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 1,
                'topP' => 0.95,
                'topK' => 40,
                'maxOutputTokens' => 8192,
                'responseMimeType' => 'application/json',
                'responseSchema' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'name' => ['type' => 'string'],
                            'type' => ['type' => 'string']
                        ],
                        'required' => ['name', 'type']
                    ]
                ]
            ]
        ];
        try {

            $response = $this->client->post(trim($this->baseUrl . "?key={$this->geminiApiKey}"), $requestBody);

            $result = $response->json();

            $jsonResult = $result['candidates'][0]['content']['parts'][0]['text'] ?? '';
            if (empty($jsonResult)) {
                $jsonResult = json_encode($result);
            }
            json_decode($jsonResult, true);
            return [
                'success' => true,
                'detections' => json_decode($jsonResult, true),
                'promptTokens' => $result['usageMetadata']['promptTokenCount'] ?? 0,
                'totalTokens' => $result['usageMetadata']['totalTokenCount'] ?? 0
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
