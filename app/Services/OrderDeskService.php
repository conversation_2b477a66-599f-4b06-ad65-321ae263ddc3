<?php

namespace App\Services;

use App\Models\Country;
use App\Models\FolderOrderDesk;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductType;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\Shipment;
use App\Models\State;
use App\Models\Store;
use App\Models\SurchargeService;
use App\Services\integrations\OrderDeskApiClient;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OrderDeskService
{
    public OrderDeskApiClient $service;

    public $printAreas = [
        'front' => 'front',
        'back' => 'back',
        'innernecklabel' => 'inner_neck_label',
        'outernecklabel' => 'outer_neck_label',
        'pocket' => 'pocket',
        'abovepocket' => 'above_pocket',
        'leftsleeve' => 'left_sleeve',
        'rightsleeve' => 'right_sleeve',
        '3df' => '3df',
        'embfrontsmall' => 'emb_front_small',
        'embleftchest' => 'emb_left_chest',
        'bleedmug' => 'bleed_mug',
        'dtff' => 'dtf_f',
        'dtfb' => 'dtf_b',
        'dtfpocket' => 'dtf_pocket',
        'dtfabovepocket' => 'dtf_above_pocket',
        'film' => 'film',
        'blank' => 'blank',
        'poster' => 'poster',
        'maxprint' => 'maxprint',
        'diecut' => 'die_cut',
    ];

    public function __construct(public SaleOrderAccount $saleOrderAccount)
    {
        $this->service = new OrderDeskApiClient($saleOrderAccount);
    }

    public function syncFolder(): void
    {
        $response = $this->service->get('store');
        if ($response->status() === 200) {
            $data = $response->json();
            $folders = $data['store']['folders'] ?? [];
            if ($folders) {
                foreach ($folders as $folderId => $folder) {
                    FolderOrderDesk::create([
                        'folder_id' => $folderId,
                        'name' => $folder,
                        'sale_order_account_id' => $this->saleOrderAccount->id,
                    ]);
                }
            }
        } else {
            throw new \Exception('Not Authorized');
        }
    }

    public function fetchOrder()
    {
        $folderOrderDesk = FolderOrderDesk::where('name', FolderOrderDesk::FOLDER_NEW)
            ->where('sale_order_account_id', $this->saleOrderAccount->id)
            ->first();

        if (!$folderOrderDesk) {
            throw new \Exception('Folder Prepared not found');
        }

        $offset = 0;
        $limit = 50;

        do {
            $params = [
                'folder_id' => $folderOrderDesk->folder_id,
                'limit' => $limit,
                'offset' => $offset,
            ];

            $res = $this->service->get('orders', $params);

            if ($res->status() === 200) {
                $data = $res->json();
                $orders = $data['orders'] ?? [];
                $recordsReturned = $data['records_returned'] ?? 0;

                if ($orders) {
                    foreach ($orders as $order) {
                        handleJob(QueueJob::QUEUE_SYNC_ORDER_DESK_ORDER, [
                            'account_id' => $this->saleOrderAccount->id,
                            'external_key' => $order['id'],
                        ]);
                    }
                }

                $offset += $limit;
            } else {
                throw new \Exception('Not Authorized');
            }
        } while ($recordsReturned > 0);
    }

    public function getOrder($externalId)
    {
        return $this->service->get('orders/' . $externalId);
    }

    public function getTokenOfStoreSpa($dataOrderMetadata, SaleOrderAccount $saleOrderAccount)
    {
        if (empty($dataOrderMetadata['StoreID'])) {
            $store = $saleOrderAccount->store;
        } else {
            $store = Store::where('id', $dataOrderMetadata['StoreID'])->first();
        }
        if (!$store) {
            return null;
        }

        return $store;
    }

    public function formatOrderDeskToCreateOrderApi($dataOrderDesk, Store|null $store)
    {
        if (!$store) {
            return null;
        }
        $orderType = $dataOrderDesk['order_metadata']['OrderType'] ?? null;
        $dataSpa = [
            'order_id' => $dataOrderDesk['source_id'],
            'external_key' => $dataOrderDesk['id'],
            'shipping_method' => $dataOrderDesk['shipping_method'] == 'Priority Mail (PM)' ? SaleOrder::SHIPPING_METHOD_PRIORITY : SaleOrder::SHIPPING_METHOD_STANDARD,
            'line_items' => $this->formatOrderDeskLineItems($dataOrderDesk['order_items'], $orderType),
            'address' => $this->formatOrderDeskShippingAddress($dataOrderDesk['shipping']),
            'order_source' => SaleOrder::ORDER_SOURCE_ORDERDESK,
        ];
        if ($returnAddress = $this->formatOrderDeskReturnAddress($dataOrderDesk['return_address'])) {
            $dataSpa['return_address'] = $returnAddress;
        }
        $giftMessage = $dataOrderDesk['checkout_data']['GiftMessage'] ?? null;
        if ($giftMessage) {
            $dataSpa['insert'][] = [
                'name' => 'gift_message',
                'url' => $giftMessage,
                'size' => '8.5x11',
            ];
        }
        $packingSlip = $dataOrderDesk['checkout_data']['Packing Slip'] ?? null;
        if ($packingSlip) {
            $dataSpa['insert'][] = [
                'name' => 'packing_slip',
                'url' => $packingSlip,
                'size' => '8.5x11'
            ];
        }

        if ($orderType) {
            $dataSpa['order_type'] = $orderType;
        }
        $shippingMethod = $dataOrderDesk['checkout_data']['ShippingAccount'] ?? null;
        if (!empty($shippingMethod)) {
            $dataSpa['shipping_method'] = $shippingMethod;
        }
        $labelUrl = $dataOrderDesk['order_metadata']['LabelUrl'] ?? null;
        if ($labelUrl) {
            $dataSpa['label_url'] = parseEncodeUrl($labelUrl);
        }
        if (!empty($dataOrderDesk['order_metadata']['Facility'])) {
            $dataSpa['facility_code'] = $dataOrderDesk['order_metadata']['Facility'];
        }
        $this->handleAdditionalService($store, $dataOrderDesk, $dataSpa);

        return $dataSpa;
    }

    private function formatOrderDeskLineItems($order_items, $orderType)
    {
        return array_map(function ($orderItem) use ($orderType) {
            return $this->formatOrderDeskLineItem($orderItem, $orderType);
        }, $order_items);
    }

    private function formatOrderDeskLineItem($orderItem, $orderType)
    {
        $result =  [
            'sku' => substr($orderItem['code'], -9),
            'order_item_id' => $orderItem['id'] . '-' . Str::random(8),
            'quantity' => $orderItem['quantity'],
            'name' => $orderItem['name'],
        ];
        if ($orderType == SaleOrder::ORDER_TYPE_LICENSE_ORDER) {
            $result['licensed_design_id'] = array_values($orderItem['variation_list'])[0];
        } else {
            $result['print_files'] = $this->formatOrderDeskPrintFiles($orderItem['variation_list']);
            $result['preview_files'] = $this->formatOrderDeskPreviewFiles($orderItem['variation_list']);
        }

        return $result;
    }

    private function formatOrderDeskPrintFiles(mixed $variationList)
    {
        $printAreas = [];
        foreach ($variationList as $printArea => $url) {
            if (Str::of($printArea)->before('.') != 'PrintFiles') {
                continue;
            }
            $printAreas[] = [
                'key' => $this->formatPrintAreaName($printArea),
                'url' => parseEncodeUrl($url),
            ];
        }

        return $printAreas;
    }

    public function formatOrderDeskShippingAddress(mixed $shipping)
    {
        $country = $this->formatCountry($shipping);

        return [
            'name' => $shipping['first_name'] . ' ' . $shipping['last_name'],
            'company' => $shipping['company'],
            'street1' => $shipping['address1'],
            'street2' => $shipping['address2'],
            'city' => $shipping['city'],
            'state' => $this->formatState($shipping['state'], $country),
            'country' => $country,
            'zip' => $shipping['postal_code'],
            'phone' => $shipping['phone'],
        ];
    }

    public function createOrder(array $dataCreateOrder, mixed $token)
    {
        return Http::withToken($token)->post(config('spa.base_url') . 'orders', $dataCreateOrder);
    }

    public function moveFolder($folderId, $orderId)
    {
        $data = [
            'destination_folder_id' => $folderId,
            'order_id_list' => [$orderId],
        ];
        Log::channel('order_desk')->info(sprintf('payload moveOrder: %s', json_encode($data)));

        return $this->service->post('move-orders', $data);
    }

    public function addNote($externalId, $orderExternal, $message)
    {
        $orderExternal['order_notes'][] = [
            'username' => config('orderdesk.username'),
            'date_added' => Carbon::now()->toDateTimeString(),
            'content' => $message,
        ];

        return $this->service->put('orders/' . $externalId, $orderExternal);
    }

    public function addShipment($externalId, Shipment $shipment)
    {
        return $this->service->post(sprintf('orders/%s/shipments', $externalId), [
            'tracking_number' => $shipment->tracking_number,
            'carrier_code' => $shipment->shippingCarrier?->name,
            //            'shipment_method' => $shipment->service_code,
            'tracking_url' => str_replace('{tracking_code}', $shipment->tracking_number, $shipment->shippingCarrier->tracking_url)
        ]);
    }

    private function formatOrderDeskPreviewFiles(mixed $variation_list)
    {
        $previewFiles = [];
        foreach ($variation_list as $printArea => $url) {
            if (Str::of($printArea)->before('.') != 'PreviewFiles') {
                continue;
            }
            $previewFiles[] = [
                'key' => $this->formatPrintAreaName($printArea),
                'url' => parseEncodeUrl($url),
            ];
        }

        return $previewFiles;
    }

    private function formatOrderDeskReturnAddress($return_address)
    {
        if (!empty($return_address['country']) && $return_address['country'] == 'US' && empty($return_address['state'])) {
            return null;
        }
        if (
            empty($return_address['name'])
            || empty($return_address['address1'])
            || empty($return_address['city'])
            || empty($return_address['postal_code'])
            || empty($return_address['country'])
        ) {
            return null;
        }
        $country = $this->formatCountry($return_address);

        return [
            'name' => $return_address['name'],
            'company' => $return_address['company'],
            'street1' => $return_address['address1'],
            'street2' => $return_address['address2'],
            'city' => $return_address['city'],
            'state' => $this->formatState($return_address['state'], $country),
            'zip' => $return_address['postal_code'],
            'country' => $country,
            'phone' => $return_address['phone'],
        ];
    }

    public function formatCountry($address)
    {
        if (empty($countryName = $address['country'])) {
            return null;
        }
        $country = Country::where('native', $countryName)
            ->orWhere('name', $countryName)
            ->orWhere('iso2', $countryName)
            ->orWhere('iso3', $countryName)
            ->first();

        return $country ? $country->iso2 : $address['country'];
    }

    public function formatState($state, $country)
    {
        if (strtoupper($country) != 'US' || empty($state)) {
            return $state;
        }
        $state = State::where('iso2', $state)
            ->orWhere('name', $state)
            ->first();

        return $state ? $state->iso2 : $state;
    }

    public function formatPrintAreaName($printArea)
    {
        $value = Str::after($printArea, '.');
        $productPrintSide = ProductPrintSide::where('name', $value)
            ->orWhere('code_name', $value)
            ->first();
        if ($productPrintSide) {
            return $productPrintSide->code_name;
        }
        $pattern = '/[a-zA-Z0-9]+/';
        preg_match_all($pattern, $value, $matches);
        $val = strtolower(implode('', $matches[0]));

        return $this->printAreas[$val] ?? '';
    }

    private function handleAdditionalService(Store $store, $dataOrderDesk, &$dataSpa)
    {
        if ($store->id == Store::STORE_GOOD_MVMT) {
            $dataSpa['additional_services'][] = SurchargeService::API_VALUE_STICKER_AND_BAG;

            return;
        }
        if ($store->id == Store::STORE_DLS) {
            $skus = collect($dataOrderDesk['order_items'])->pluck('code')->map(function ($code) {
                return substr($code, -9);
            });
            $products = Product::with('style')->whereIn('sku', $skus->toArray())->get();

            $isProductStyleMug = $products->contains(function ($product) {
                if (!$product->style()->first()) {
                    return false;
                }

                return $product->style()->first()->type == ProductType::MUGS;
            });

            if ($isProductStyleMug) {
                $dataSpa['additional_services'][] = SurchargeService::API_VALUE_MUG_PACKAGING;
            }
        }
    }
}
