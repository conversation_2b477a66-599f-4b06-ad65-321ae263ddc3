<?php

namespace App\Services;

use App\Models\Oauth2Token;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Oauth2Service
{
    protected $platform;

    public function __construct($platform)
    {
        $this->platform = $platform;
    }

    /**
     * Lấy access token mới (client_credentials hoặc authorization_code)
     */
    public function getAccessToken(array $params = [])
    {
        $config = config("services.{$this->platform}");
        try {
            $data = [
                'grant_type' => $config['grant_type'] ?? 'client_credentials',
                'client_id' => $config['client_id'],
                'client_secret' => $config['client_secret'],
            ];

            if ($config['grant_type'] === 'authorization_code') {
                $data['redirect_uri'] = $config['redirect_uri'] ?? null;
                $data['code'] = $params['code'] ?? null;
            }

            $response = Http::asForm()->post($config['token_url'], $data);

            if ($response->successful()) {
                $tokenInfo = $response->json();
                $this->saveToken($tokenInfo);

                return $tokenInfo;
            } else {
                Log::error('OAuth2 getAccessToken failed', [
                    'platform' => $this->platform,
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'params' => $params,
                ]);

                return [
                    'error' => 'Token request failed',
                    'status' => $response->status(),
                    'body' => $response->body()
                ];
            }
        } catch (Exception $e) {
            Log::error('OAuth2 getAccessToken Exception', [
                'platform' => $this->platform,
                'message' => $e->getMessage(),
                'params' => $params,
            ]);

            return [
                'error' => 'Exception occurred',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Làm mới access token bằng refresh token
     */
    public function refreshAccessToken()
    {
        $config = config("services.{$this->platform}");

        try {
            $token = Oauth2Token::where('platform', $this->platform)->latest()->first();
            if (!$token || empty($token->refresh_token)) {
                return [
                    'error' => 'No refresh token available for platform: ' . $this->platform
                ];
            }

            $data = [
                'grant_type' => 'refresh_token',
                'client_id' => $config['client_id'],
                'client_secret' => $config['client_secret'],
                'refresh_token' => $token->refresh_token,
            ];

            $response = Http::asForm()->post($config['token_url'], $data);
            if ($response->successful()) {
                $tokenInfo = $response->json();
                $this->saveToken($tokenInfo);

                return $tokenInfo;
            } else {
                Log::error('OAuth2 refreshAccessToken failed', [
                    'platform' => $this->platform,
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'refresh_token' => $token->refresh_token,
                ]);

                return [
                    'error' => 'Refresh token request failed',
                    'status' => $response->status(),
                    'body' => $response->body()
                ];
            }
        } catch (Exception $e) {
            Log::error('OAuth2 refreshAccessToken Exception', [
                'platform' => $this->platform,
                'message' => $e->getMessage(),
                'refresh_token' => $token->refresh_token,
            ]);

            return [
                'error' => 'Exception occurred',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Kiểm tra token có hết hạn không (dựa vào DB)
     */
    public function isTokenExpired()
    {
        $token = Oauth2Token::where('platform', $this->platform)->first();
        if (!$token || !$token->access_token || !$token->expires_in || !$token->issued_at) {
            return true;
        }

        // Thêm buffer 60 giây
        $expiredAt = $token->issued_at->copy()->addSeconds($token->expires_in - 60);

        return now()->greaterThan($expiredAt);
    }

    /**
     * Lưu token vào database
     */
    public function saveToken($tokenInfo)
    {
        $old = Oauth2Token::where('platform', $this->platform)->latest()->first();
        $refreshToken = $tokenInfo['refresh_token'] ?? ($old ? $old->refresh_token : null);

        // Convert issued_at từ milliseconds → Carbon datetime
        $issuedAt = null;
        if (!empty($tokenInfo['issued_at'])) {
            // issued_at có thể là milliseconds
            $timestamp = $tokenInfo['issued_at'];
            $issuedAt = strlen($timestamp) > 10
                ? \Carbon\Carbon::createFromTimestampMs($timestamp)
                : \Carbon\Carbon::createFromTimestamp($timestamp);
        } else {
            $issuedAt = now();
        }

        Oauth2Token::updateOrCreate(
            ['platform' => $this->platform],
            [
                'access_token' => $tokenInfo['access_token'] ?? null,
                'token_type' => $tokenInfo['token_type'] ?? null,
                'refresh_token' => $refreshToken,
                'expires_in' => $tokenInfo['expires_in'] ?? (8 * 3600),
                'issued_at' => $issuedAt,
            ],
        );
    }

    /**
     * Lấy token đã lưu nếu còn hạn
     */
    public function getSavedToken()
    {
        $token = Oauth2Token::where('platform', $this->platform)->latest()->first();
        if ($token && $token->access_token && !$this->isTokenExpired()) {
            return $token->access_token;
        }

        return null;
    }
}
