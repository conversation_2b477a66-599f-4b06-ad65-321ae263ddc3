<?php

namespace App\Services;

use App\Helpers\DetectColorHelper;
use App\Helpers\SaleOrderHelper;
use App\Jobs\TrimTransparentArtwork;
use App\Jobs\UpdateOrderItemColorStatus;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\StoreProductStyleResize;
use Illuminate\Support\Facades\Log;

class DetectColorArtworkService
{
    protected $img;

    protected $imageId;

    protected $detectColorHelper;

    protected $attempts;

    protected $maxRetries;

    public function __construct($imageId, $attempts, $maxRetries)
    {
        $this->imageId = $imageId;
        $this->attempts = $attempts;
        $this->maxRetries = $maxRetries;
    }

    public function handle()
    {
        try {
            jobEcho("image id: {$this->imageId}");
            $this->detectColorHelper = new DetectColorHelper();
            set_error_handler(
                function ($severity, $message, $file, $line) {
                    throw new \ErrorException($message, $severity, $severity, $file, $line);
                },
            );
            $item = SaleOrderItemImage::with('product.productStyle', 'printSide:code,code_name,name', 'printingPresetSku')->find($this->imageId);

            if (!$item) {
                jobEcho("Detect color: $this->imageId not found");
                Log::channel('image')->error("Detect color: $this->imageId not found");
                throw new \Exception("{$this->imageId} not found");
            }

            $side = $item->print_side;
            $dir_s3 = $item->order_date . '/' . $item->sku . '-' . $side . '.png';
            $file = env('AWS_S3_URL') . '/thumb/750/' . $dir_s3;
            Log::channel('image')->info("$this->imageId $file");
            $image = file_get_contents($file);

            try {
                // fix error imagecreatefromstring(): gd-png: fatal libpng error: zTXt: chunk data is too large
                imagecreatefromstring($image);
            } catch (\Exception $e) {
                jobEcho('imagecreatefromstring error: ' . $e->getMessage());
                $imagick = new \Imagick();
                $imagick->readImageBlob($image);
                $imagick->stripImage();
                $image = $imagick->getImageBlob();
            }

            jobEcho('start detect color');
            $color = $this->detect($image, 1);
            jobEcho("design color is: {$color}");
            $this->detectColorHelper->countImageColors($image);

            // detect purple ink
            jobEcho('start detect purple ink');
            $purple = $this->detectColorHelper->isPurpleColor();
            jobEcho("purple = $purple");
            $item->is_purple = $purple;

            // clear memory
            $this->detectColorHelper->clearMemory();

            $item->save();

            // add tag purple ink
            // id tag purple ink = 165
            if ($purple == 1) {
                jobEcho("$this->imageId has purple color");
                Log::channel('image')->info("$this->imageId has purple color");
                $idTagPurple = env('ID_PURPLE_TAG', 165);
                $saleOrder = SaleOrder::find($item->order_id);
                $newTag = SaleOrderHelper::handleTag($saleOrder->tag, $idTagPurple);
                $saleOrder->tag = $newTag;
                $saleOrder->save();
            }

            jobEcho('start calculate pretreat');
            $pretreatInfo = $this->calculatedPretreat($image, 1);
            jobEcho("pretreat info: {$pretreatInfo}");

            $item->color_new = $color;
            $item->pretreat_info = $pretreatInfo;
            $item->custom_platen = $this->detectCustomPlaten($item, $pretreatInfo);
            $item->save();

            TrimTransparentArtwork::dispatch($this->imageId)->onQueue(QueueJob::QUEUE_TRIM_TRANSPARENT_ARTWORK);
            dispatch(new UpdateOrderItemColorStatus($item->order_item_id))->onQueue(QueueJob::QUEUE_UPDATE_ORDER_ITEM_COLOR_STATUS);

            saleOrderHistory(
                null,
                null,
                $item->order_id,
                SaleOrderHistory::UPDATE_ORDER_DETECT_COLOR_TYPE,
                'Detect ink_color for SKU ' . $item->sku . ' successfully and ready for production.',
            );
        } catch (\Exception $e) {
            jobEcho("$this->imageId detect color: " . $e->getMessage());
            jobEcho("attempts: {$this->attempts}");
            jobEcho("maxtries: {$this->maxRetries}");

            if ($this->attempts == $this->maxRetries && !empty($item)) {
                OrderIssueService::logIssue([
                    'order_id' => $item->order_id,
                    'order_item_id' => $item->order_item_id,
                    'sku' => $item->sku,
                    'type' => SaleOrderHistory::DETECT_COLOR_ERROR,
                    'side' => $item->printSide->code_name ?? '',
                    'side_name' => $item->printSide->name ?? '',
                ]);
                $this->setError($item);
            }

            Log::channel('image')->error("$this->imageId detect color: " . $e->getMessage());
            throw $e;
        }

        restore_error_handler();
    }

    protected function setError($item)
    {
        $item->color_new = SaleOrderItemImage::COLOR_ERROR;
        $item->save();
    }

    public function calculatedPretreat($image, $level = 1)
    {
        $size = getimagesizefromstring($image);

        if (!$size) {
            return false;
        }

        $img = imagecreatefromstring($image);

        if (!$img) {
            return false;
        }

        $this->img = $img;

        $width = $size[0];
        $height = $size[1];

        $pretreatInfo = [
            'top' => $height - 1,
            'left' => $width - 1,
            'right' => 0,
            'bottom' => 0
        ];

        for ($i = 0; $i < $width; $i += $level) {
            for ($j = 0; $j < $height; $j += $level) {
                $rgb = $this->getColorRBGA($i, $j);

                if ($rgb['alpha'] == 127) {
                    continue;
                } else {
                    $pretreatInfo['top'] = $pretreatInfo['top'] > $j ? $j : $pretreatInfo['top'];
                    $pretreatInfo['left'] = $pretreatInfo['left'] > $i ? $i : $pretreatInfo['left'];
                    $pretreatInfo['right'] = $pretreatInfo['right'] < $i ? $i : $pretreatInfo['right'];
                    $pretreatInfo['bottom'] = $pretreatInfo['bottom'] < $j ? $j : $pretreatInfo['bottom'];
                }
            }
        }

        if ($pretreatInfo['top'] >= $pretreatInfo['bottom'] || $pretreatInfo['left'] >= $pretreatInfo['right']) {
            $pretreatInfo['top'] = 0;
            $pretreatInfo['left'] = 0;
            $pretreatInfo['bottom'] = $height - 1;
            $pretreatInfo['right'] = $width - 1;
        }

        $result['top'] = $pretreatInfo['top'] / $height;
        $result['left'] = $pretreatInfo['left'] / $width;
        $result['width'] = ($pretreatInfo['right'] - $pretreatInfo['left'] + 1) / $width;
        $result['height'] = ($pretreatInfo['bottom'] - $pretreatInfo['top'] + 1) / $height;

        return json_encode($result);
    }

    public function detect($image, $level = 1)
    {
        $size = getimagesizefromstring($image);

        if (!$size) {
            return false;
        }

        $img = imagecreatefromstring($image);

        if (!$img) {
            return false;
        }

        imagecopymerge($img, $img, 0, 0, 0, 0, imagesx($img), imagesy($img), 100);

        $this->img = $img;
        $colorAlpha = [];
        $totalPixel = 0;
        $width = $size[0];
        $height = $size[1];
        $is_have_color = 0;

        for ($i = 0; $i < $width; $i += $level) {
            for ($j = 0; $j < $height; $j += $level) {
                $rgb = $this->getColorRBGA($i, $j);

                if ($rgb['alpha'] == 127) {
                    continue;
                }

                if (($this->isBlack($rgb) == false && $this->isWhite($rgb) == false) && $rgb['alpha'] < 15) {
                    $is_have_color++;
                }
            }
        }

        for ($i = 0; $i < $width; $i += $level) {
            for ($j = 0; $j < $height; $j += $level) {
                $rgb = $this->getColorRBGA($i, $j);

                if ($rgb['alpha'] == 127) {
                    continue;
                }

                // co qua nhieu diem trong suot giua cac diem co mau
                $check_block = 0;

                if ($is_have_color > 0) {
                    for ($z = 0; $z <= 3; $z++) {
                        for ($x = 0; $x <= 3; $x++) {
                            if ($i + $x < $width && $j + $z < $height) {
                                $ix = $i + $x;
                                $jz = $j + $z;
                                $block_rgb = $this->getColorRBGA($ix, $jz);

                                if ((!$this->isBlack($block_rgb) && !$this->isWhite($block_rgb)) && $block_rgb['alpha'] < 15) {
                                    $check_block++;
                                }
                            }
                        }
                    }
                }

                if ($check_block >= 9) {
                    return 2;
                }

                if (($i - 1) > 0) {
                    $tran_color_rgb = $this->getColorRBGA($i - 1, $j);

                    if ($tran_color_rgb['alpha'] > 100) {
                        continue;
                    }
                }

                if (($i + 1) < $width) {
                    $tran_color_rgb = $this->getColorRBGA($i + 1, $j);

                    if ($tran_color_rgb['alpha'] > 100) {
                        continue;
                    }
                }

                if (($j - 1) > 0) {
                    $tran_color_rgb = $this->getColorRBGA($i, $j - 1);

                    if ($tran_color_rgb['alpha'] > 100) {
                        continue;
                    }
                }

                if (($j + 1) < $height) {
                    $tran_color_rgb = $this->getColorRBGA($i, $j + 1);

                    if ($tran_color_rgb['alpha'] > 100) {
                        continue;
                    }
                }

                $color = sprintf('#%02x%02x%02x', $rgb['red'], $rgb['green'], $rgb['blue']);
                $key = $color . '_' . $rgb['alpha'];

                if (!isset($colorAlpha[$key])) {
                    $colorAlpha[$key] = ['rbg' => $rgb, 'count' => 1];
                } else {
                    $tmp = $colorAlpha[$key];
                    $tmp['count']++;
                    $colorAlpha[$key] = $tmp;
                }

                $totalPixel++;
            }
        }

        $black = $white = 0;
        $percent_alpha = 0;

        foreach ($colorAlpha as $item) {
            $count = $item['count'];
            $rgb = $item['rbg'];
            $present = ($count / $totalPixel) * 100;

            if ($this->isWhite($rgb)) {
                if ($rgb['alpha'] > 30) {
                    $percent_alpha = $percent_alpha + $present;
                } else {
                    if ($rgb['alpha'] <= 5) {
                        $white = $white + $present;
                    }
                }
            } elseif ($this->isBlack($rgb)) {
                if ($rgb['alpha'] > 30) {
                    $percent_alpha = $percent_alpha + $present;
                } else {
                    if ($rgb['alpha'] == 0) {
                        $black = $black + $present;
                    }
                }
            }
        }

        $white = round($white, 2);
        $black = round($black, 2);
        $percent_alpha = round($percent_alpha, 2);

        if ($white == 0 & $black > 0 && $percent_alpha >= 10) {
            return 2;
        }

        if ($white > 0 & $black == 0 && $percent_alpha >= 10) {
            return 2;
        }

        // check is mix black color
        if ($black > 0 && $white == 0) {
            if ($percent_alpha > 4) {
                return 2;
            }
        }

        if ($black == 0 && $white > 0) {
            return 0;
        }

        if ($white == 0 && $black > 0) {
            return 1;
        }

        return 2;
    }

    protected function isWhite($rgb)
    {
        if ($rgb['red'] >= 235 && $rgb['green'] >= 235 && $rgb['blue'] >= 235) {
            $diff = $rgb['red'] - $rgb['green'];
            $diff = $diff < 0 ? $diff * -1 : $diff;

            if ($diff > 8) {
                return false;
            }

            $diff = $rgb['red'] - $rgb['blue'];
            $diff = $diff < 0 ? $diff * -1 : $diff;

            if ($diff > 8) {
                return false;
            }

            $diff = $rgb['green'] - $rgb['blue'];
            $diff = $diff < 0 ? $diff * -1 : $diff;

            if ($diff > 8) {
                return false;
            }

            return true;
        }

        return false;
    }

    protected function isBlack($rgb)
    {
        if ($rgb['red'] <= 25 && $rgb['green'] <= 25 && $rgb['blue'] <= 25) {
            return true;
        }

        return false;
    }

    protected function getColorRBGA($i, $j)
    {
        $thisColor = imagecolorat($this->img, $i, $j);

        return imagecolorsforindex($this->img, $thisColor);
    }

    public function detectCustomPlaten($item, $pretreatInfo)
    {
        $customPlaten = null;
        $platenSize = '';

        if (!empty($item->printSide) && !empty($item->printingPresetSku)) {
            $platenName = 'platen_' . $item->printSide->code_name . '_size';
            jobEcho("print side code: {$item->printSide->code_name}");
            $platenSize = $item->printingPresetSku->{$platenName};
            jobEcho("platen size: {$platenSize}");
        } else {
            jobEcho("don't have printSide or printingPresetSku");
        }

        $store = Store::find($item->store_id);
        $orderItem = SaleOrderItem::find($item->order_item_id);
        $isResize = false;

        if (!empty($store->id) && !empty($item->product->productStyle->sku)) {
            $objTurnOff = StoreProductStyleResize::where('store_id', $store->id)
                ->where('product_style_sku', $item->product->productStyle->sku)
                ->where('is_active', StoreProductStyleResize::ACTIVED)
                ->first();

            if ($store->is_resize && empty($objTurnOff)) {
                $isResize = true;
            }
        }

        jobEcho('is resize: ' . ($isResize ? 'true' : 'false'));

        $pretreat = json_decode($pretreatInfo);
        $minLeftRight = min($pretreat->left, 1 - $pretreat->left - $pretreat->width);
        $width = round((1 - 2 * $minLeftRight) * $item->image_width);
        $height = round(($pretreat->height + $pretreat->top) * $item->image_height);

        if (!$isResize && $store->id != Store::PRINTIFY_API_ID) {
            if ($platenSize == '14x16' && ($width > 4200 || $height > 4800)) {
                jobEcho('set custom platen 16x21');
                $customPlaten = '16x21';
            }
        }

        if (
            $store->id == Store::PRINTIFY_API_ID
            && in_array($item->print_side, [0, 1])
            && $platenSize == '14x16'
            && ($width > 4200 || $height > 4800)
        ) {
            jobEcho('set custom platen 16x18');
            $customPlaten = '16x18';
        }

        if (
            $store->id !== Store::PRINTIFY_API_ID && $isResize && $orderItem?->production_config == 'large'
            && in_array($item->print_side, [0, 1])
            && $platenSize == '14x16'
            && ($width > 4200 || $height > 4800)
        ) {
            jobEcho('set custom platen 16x18');
            $customPlaten = '16x18';
        }

        return $customPlaten;
    }
}
