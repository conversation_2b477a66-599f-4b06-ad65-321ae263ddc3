<?php

use App\Jobs\AutoRefillJob;
use App\Jobs\CancelEmbroideryTaskJob;
use App\Jobs\CheckTrackingIsDeliveredJob;
use App\Jobs\CheckWipPendingScanBatchJob;
use App\Jobs\ConvertBoxIdToPdf;
use App\Jobs\ConvertCountStickerToPdf;
use App\Jobs\ConvertImageDTFJob;
use App\Jobs\ConvertImageLatexJob;
use App\Jobs\CreateAccessApiLog;
use App\Jobs\CreateEmbroideryTaskJob;
use App\Jobs\DeductionAutoJob;
use App\Jobs\DeductionJob;
use App\Jobs\DetectDesignByVisuaJob;
use App\Jobs\DetectDesignGeminiJob;
use App\Jobs\DetectDesignWebhookJob;
use App\Jobs\DetectRbtOrder;
use App\Jobs\DetectShippingMethodRedBubbleJob;
use App\Jobs\EmployeeSubscribeTopicFirebaseJob;
use App\Jobs\ExportSaleOrderJob;
use App\Jobs\FacilityNotifyJob;
use App\Jobs\MultipleStatusOrderJob;
use App\Jobs\PostBackGelatoJob;
use App\Jobs\Printify\NotifyTrackingJob;
use App\Jobs\RbtDeductionAutoJob;
use App\Jobs\RefundOrderJob;
use App\Jobs\SendItemErrorToRBJob;
use App\Jobs\SendItemPrintedToRedbubbleJob;
use App\Jobs\SendMailQCAlertLateQc;
use App\Jobs\SendMailQCAlertOosJob;
use App\Jobs\SendMailSellerSupportJob;
use App\Jobs\SendMailTicketJob;
use App\Jobs\SendMailUploadProductImageJob;
use App\Jobs\SendMailWholesaleJob;
use App\Jobs\SendNotificationJob;
use App\Jobs\SendNotifyToGoogleChatJob;
use App\Jobs\StatusOrderJob;
use App\Jobs\StockNotifyJob;
use App\Jobs\SyncOrderDeskNewOrderJob;
use App\Jobs\SyncOrderDeskRejectJob;
use App\Jobs\SyncOrderDeskShippedJob;
use App\Jobs\TopupMailJob;
use App\Jobs\TrackingShipmentJob;
use App\Jobs\UpdateProductIncomingQuantityJob;
use App\Jobs\UpdateProductQuantityJob;
use App\Jobs\UpdateQuantitySupplyJob;
use App\Jobs\UpdateSaleOrderProductionStatus;
use App\Jobs\UpdateSaleOrderSlaStatus;
use App\Jobs\UploadShipmentUrlJob;
use App\Models\AppIps;
use App\Models\BarcodePrinted;
use App\Models\Box;
use App\Models\Department;
use App\Models\Employee;
use App\Models\IntegrateCallbackLog;
use App\Models\InternalRequest;
use App\Models\InternalTicket;
use App\Models\Invoice;
use App\Models\PaymentAccount;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\ProductStyleSizeWeight;
use App\Models\QueueJob;
use App\Models\RbtCountSticker;
use App\Models\RbtProduct;
use App\Models\ReportHistory;
use App\Models\SaleOrder;
use App\Models\SaleOrderClaimSupport;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemProductImage;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\StoreIntegrate;
use App\Models\UniversalReportTag;
use App\Models\SupplyQuantity;
use App\Models\VisuaDetectImage;
use App\Models\Wallet;
use App\Models\WalletTopup;
use App\Repositories\LabelRepository;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\HttpException;

if (!function_exists('handleJob')) {
    function handleJob($name, $value)
    {
        switch ($name) {
            case SaleOrder::QUEUE_PRINTIFY_SEND_NOTIFY_TRACKING:
                NotifyTrackingJob::dispatch($value)->onQueue(SaleOrder::QUEUE_PRINTIFY_SEND_NOTIFY_TRACKING)
                    ->delay(5);
                break;
            case SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER:
                MultipleStatusOrderJob::dispatch($value)->onQueue(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER);
                break;
            case ReportHistory::EXPORT_SALE_REPORT_JOB:
                ExportSaleOrderJob::dispatch($value)->onQueue(ReportHistory::EXPORT_SALE_REPORT_JOB);
                break;
            case SaleOrder::JOB_NOTIFY_STATUS_ORDER:
                StatusOrderJob::dispatch($value)->onQueue(SaleOrder::JOB_NOTIFY_STATUS_ORDER)->delay(Carbon::now()->addMinutes(2));
                break;
            case SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT:
                TrackingShipmentJob::dispatch($value)->onQueue(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT)->delay(Carbon::now()->addMinutes(2));
                break;
            case Product::JOB_MAIL_WHOLESALE:
                SendMailWholesaleJob::dispatch($value)->onQueue(Product::JOB_MAIL_WHOLESALE);
                break;
            case InternalTicket::JOB_MAIL_TICKET:
                SendMailTicketJob::dispatch($value)->onQueue(InternalTicket::JOB_MAIL_TICKET);
                break;
            case InternalTicket::JOB_MAIL_ASSIGN_TICKET:
                \App\Jobs\SendMailAssignTicketJob::dispatch($value)->onQueue(InternalTicket::JOB_MAIL_ASSIGN_TICKET);
                break;
            case SaleOrder::JOB_ERROR_EXCEPTION_API_SEND_MESS_SLACK:
                \App\Jobs\ExceptionApiJob::dispatch($value)->onQueue(SaleOrder::JOB_ERROR_EXCEPTION_API_SEND_MESS_SLACK);
                break;
            case Product::JOB_SEND_STOCK_NOTIFY:
                StockNotifyJob::dispatch($value)->onQueue(Product::JOB_SEND_STOCK_NOTIFY);
                break;
            case Invoice::QUEUE_ALERT_DOWNLOAD_INVOICE:
                \App\Jobs\AlertDownloadInvoiceToGoogleChatJob::dispatch($value)->onQueue(Invoice::QUEUE_ALERT_DOWNLOAD_INVOICE);
                break;
            case BarcodePrinted::JOB_SEND_NOTIFICATION:
                SendNotificationJob::dispatch($value)->onQueue(BarcodePrinted::JOB_SEND_NOTIFICATION);
                break;
            case BarcodePrinted::JOB_ALERT_WIP_PENDING_SCAN_BATCH:
                CheckWipPendingScanBatchJob::dispatch($value)->onQueue(BarcodePrinted::JOB_ALERT_WIP_PENDING_SCAN_BATCH)->delay(Carbon::now()->addMinutes(30));
                break;
            case Employee::JOB_SUBSCRIBE_FIREBASE:
                EmployeeSubscribeTopicFirebaseJob::dispatch($value)->onQueue(Employee::JOB_SUBSCRIBE_FIREBASE);
                break;
            case BarcodePrinted::JOB_DEDUCTION_INVENTORY:
                DeductionJob::dispatch($value)->onQueue(BarcodePrinted::JOB_DEDUCTION_INVENTORY);
                break;
            case SaleOrder::JOB_UPDATE_PRODUCTION_STATUS:
                UpdateSaleOrderProductionStatus::dispatch($value)->onQueue(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS);
                break;
            case SaleOrderClaimSupport::JOB_SEND_MAIL_SELLER_SUPPORT:
                SendMailSellerSupportJob::dispatch($value)->onQueue(SaleOrderClaimSupport::JOB_SEND_MAIL_SELLER_SUPPORT);
                break;
            case SaleOrderItemProductImage::SEND_MAIL_CUSTOMER:
                SendMailUploadProductImageJob::dispatch($value)->onQueue(SaleOrderItemProductImage::SEND_MAIL_CUSTOMER);
                break;
            case QueueJob::CREATE_EMBROIDERY_TASK:
                CreateEmbroideryTaskJob::dispatch($value)->onQueue(QueueJob::CREATE_EMBROIDERY_TASK);
                break;
            case QueueJob::CANCEL_EMBROIDERY_TASK:
                CancelEmbroideryTaskJob::dispatch($value)->onQueue(QueueJob::CANCEL_EMBROIDERY_TASK);
                break;
            case Box::JOB_CONVERT_BOX_ID_TO_PDF:
                ConvertBoxIdToPdf::dispatch($value)->onQueue(Box::JOB_CONVERT_BOX_ID_TO_PDF);
                break;
            case AppIps::JOB_INSERT_LOG_TO_DATABASE:
                CreateAccessApiLog::dispatch($value)->onQueue(AppIps::JOB_INSERT_LOG_TO_DATABASE);
                break;
            case SaleOrder::JOB_SEND_ORDER_ITEM_PRINTED_TO_REDBUBBLE:
                SendItemPrintedToRedbubbleJob::dispatch($value)->onQueue(SaleOrder::JOB_SEND_ORDER_ITEM_PRINTED_TO_REDBUBBLE);
                break;
            case VisuaDetectImage::DETECT_DESIGN_JOB:
                DetectDesignByVisuaJob::dispatch($value)->onQueue(VisuaDetectImage::DETECT_DESIGN_JOB);
                break;
            case VisuaDetectImage::DETECT_IMAGE_WEBHOOK_JOB:
                DetectDesignWebhookJob::dispatch($value)->onQueue(VisuaDetectImage::DETECT_IMAGE_WEBHOOK_JOB);
                break;
            case QueueJob::SEND_NOTIFY_REJECT_IMAGE:
                SendNotifyToGoogleChatJob::dispatch($value)->onQueue(QueueJob::SEND_NOTIFY_REJECT_IMAGE);
                break;
            case SaleOrder::JOB_DETECT_SHIPPING_METHOD_REDBUBBLE:
                DetectShippingMethodRedBubbleJob::dispatch($value)->onQueue(SaleOrder::JOB_DETECT_SHIPPING_METHOD_REDBUBBLE);
                break;
            case ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY:
                UpdateProductQuantityJob::dispatch($value)->onQueue(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY);
                break;
            case ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING:
                UpdateProductIncomingQuantityJob::dispatch($value)->onQueue(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING)->delay(now()->addSeconds(5));
                break;
            case Product::JOB_SEND_FACILITY_NOTIFY:
                FacilityNotifyJob::dispatch($value)->onQueue(Product::JOB_SEND_FACILITY_NOTIFY);
                break;
            case QueueJob::CONVERT_IMAGE_DTF:
                ConvertImageDTFJob::dispatch($value)->onQueue(QueueJob::CONVERT_IMAGE_DTF);
                break;
            case QueueJob::CONVERT_IMAGE_LATEX:
                ConvertImageLatexJob::dispatch($value)->onQueue(QueueJob::CONVERT_IMAGE_LATEX);
                break;
            case Shipment::JOB_UPLOAD_S3_LABEL_SHIPPING:
                UploadShipmentUrlJob::dispatch($value)->onQueue(Shipment::JOB_UPLOAD_S3_LABEL_SHIPPING);
                break;
            case BarcodePrinted::JOB_AUTO_DEDUCTION:
                DeductionAutoJob::dispatch($value)->onQueue(BarcodePrinted::JOB_AUTO_DEDUCTION);
                break;
            case BarcodePrinted::JOB_RBT_AUTO_DEDUCTION:
                RbtDeductionAutoJob::dispatch($value)->onQueue(BarcodePrinted::JOB_RBT_AUTO_DEDUCTION);
                break;

            case IntegrateCallbackLog::CALL_BACK_LOG_FAIL_TO_REDBUBBLE:
                SendItemErrorToRBJob::dispatch($value)->onQueue(IntegrateCallbackLog::CALL_BACK_LOG_FAIL_TO_REDBUBBLE);
                break;
            case SaleOrder::JOB_SEND_MAIL_ALERT_OOS:
                SendMailQCAlertOosJob::dispatch($value)->onQueue(SaleOrder::JOB_SEND_MAIL_ALERT_OOS);
                break;
            case SaleOrder::JOB_SEND_MAIL_ALERT_FAILED_TIMES:
                SendMailQCAlertLateQc::dispatch($value)->onQueue(SaleOrder::JOB_SEND_MAIL_ALERT_FAILED_TIMES);
                break;
            case QueueJob::QUEUE_SYNC_ORDER_DESK_ORDER:
                SyncOrderDeskNewOrderJob::dispatch($value)->onQueue(QueueJob::QUEUE_SYNC_ORDER_DESK);
                break;
            case QueueJob::QUEUE_SYNC_ORDER_DESK_TRACKING:
                SyncOrderDeskShippedJob::dispatch($value)->onQueue(QueueJob::QUEUE_SYNC_ORDER_DESK);
                break;
            case QueueJob::QUEUE_SYNC_ORDER_DESK_REJECT:
                SyncOrderDeskRejectJob::dispatch($value)->onQueue(QueueJob::QUEUE_SYNC_ORDER_DESK);
                break;
            case StoreIntegrate::JOB_POST_BACK_GELATO:
                PostBackGelatoJob::dispatch($value)->onQueue(StoreIntegrate::JOB_POST_BACK_GELATO);
                break;
            case RbtProduct::RBT_DETECT_ORDER:
                DetectRbtOrder::dispatch($value)->onQueue(RbtProduct::RBT_DETECT_ORDER);
                break;
            case SaleOrder::JOB_REFUND_ORDER:
                RefundOrderJob::dispatch($value)->onQueue(SaleOrder::JOB_REFUND_ORDER);
                break;
            case WalletTopup::SEND_EMAIL_TOPUP:
                TopupMailJob::dispatch($value)->onQueue(WalletTopup::SEND_EMAIL_TOPUP);
                break;
            case Wallet::AUTO_REFILL_JOB:
                AutoRefillJob::dispatch($value)->onQueue(Wallet::AUTO_REFILL_JOB);
                break;
            case SaleOrder::QUEUE_UPDATE_SALE_ORDER_SLA_STATUS:
                UpdateSaleOrderSlaStatus::dispatch($value)->onQueue(SaleOrder::QUEUE_UPDATE_SALE_ORDER_SLA_STATUS);
                break;
            case QueueJob::QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER:
                CheckTrackingIsDeliveredJob::dispatch($value)->onQueue(QueueJob::QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER);
                break;
            case QueueJob::QUEUE_GEMINI_DETECT_DESIGN:
                DetectDesignGeminiJob::dispatch($value)
                    ->onQueue(QueueJob::QUEUE_GEMINI_DETECT_DESIGN);
                break;
            case RbtCountSticker::CONVERT_PDF_JOB:
                ConvertCountStickerToPdf::dispatch($value)->onQueue(RbtCountSticker::CONVERT_PDF_JOB);
                break;
            case SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB:
                UpdateQuantitySupplyJob::dispatch($value)->onQueue(SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB)->delay(Carbon::now()->addSeconds(5));
                break;
            default:
                break;
        }
    }
}

if (!function_exists('internalRequestEmit')) {
    function internalRequestEmit($name, $warehouse_id)
    {
        if (!in_array($name, InternalRequest::EVENTS)) {
            return false;
        }
        broadcast(new \App\Events\InternalRequestNotification([
            'warehouse_id' => $warehouse_id,
            'event_name' => $name
        ]));

        return true;
    }
}

if (!function_exists('updateStatusMovementEmit')) {
    function updateStatusMovementEmit($name, $warehouse_id)
    {
        if ($name != \App\Models\RbtSkuMovementDetail::SKU_MOVEMENT) {
            return false;
        }
        broadcast(new \App\Events\RbtMovementNotification([
            'warehouse_id' => $warehouse_id,
            'event_name' => $name,
        ]));

        return true;
    }
}

if (!function_exists('rejectQualityControlEmit')) {
    function rejectQualityControlEmit($employee_id)
    {
        broadcast(new \App\Events\QualityControlNotification([
            'employee_id' => $employee_id,
        ]));

        return true;
    }
}

if (!function_exists('encryptSalt')) {
    function encryptSalt($string)
    {
        $privateKey = env('APP_KEY');
        $secretKey = env('APP_SALT_KEY', 'swiftpod');
        $encryptMethod = 'AES-256-CBC';

        $key = hash('sha256', $privateKey);
        $ivalue = substr(hash('sha256', $secretKey), 0, 16);
        $result = openssl_encrypt($string, $encryptMethod, $key, 0, $ivalue);

        return base64_encode($result);
    }
}

if (!function_exists('decryptSalt')) {
    function decryptSalt($string)
    {
        $privateKey = env('APP_KEY');
        $secretKey = env('APP_SALT_KEY', 'swiftpod');
        $encryptMethod = 'AES-256-CBC';

        $key = hash('sha256', $privateKey);
        $ivalue = substr(hash('sha256', $secretKey), 0, 16);

        return openssl_decrypt(base64_decode($string), $encryptMethod, $key, 0, $ivalue);
    }
}
if (!function_exists('escapeLike')) {
    function escapeLike(string $value, string $char = '\\')
    {
        return str_replace(
            [$char, '%', '_'],
            [$char . $char, $char . '%', $char . '_'],
            $value,
        );
    }
}

if (!function_exists('isNullOrEmptyString')) {
    function isNullOrEmptyString($str)
    {
        return $str === null || trim($str) === '';
    }
}

if (!function_exists('setTimezone')) {
    function setTimezone()
    {
        $timezone = config('constants.timezone_default', 'UTC');
        DB::statement("SET time_zone ='{$timezone}'");
    }
}
if (!function_exists('setTimezoneDefault')) {
    function setTimezoneDefault($timezone = 'UTC')
    {
        DB::statement("SET time_zone ='{$timezone}'");
    }
}
if (!function_exists('getTimezone')) {
    function getTimezone()
    {
        return config('constants.timezone_default', 'UTC');
    }
}

if (!function_exists('shiftTimezoneToUTC')) {
    function shiftTimezoneToUTC($time)
    {
        return Carbon::parse($time)->shiftTimezone(getTimezone())->setTimezone('UTC');
    }
}
if (!function_exists('shiftTimezoneToPST')) {
    function shiftTimezoneToPST($time)
    {
        return Carbon::parse($time)->shiftTimezone(getTimezone())->setTimezone('America/Los_Angeles');
    }
}

if (!function_exists('convertTimeUTCToPST')) {
    function convertTimeUTCToPST($time)
    {
        return Carbon::parse($time, 'UTC')->setTimezone('America/Los_Angeles');
    }
}

if (!function_exists('convertTimePSTToUTC')) {
    function convertTimePSTToUTC($time)
    {
        return Carbon::parse($time, 'America/Los_Angeles')->setTimezone('UTC');
    }
}

if (!function_exists('jobEcho')) {
    function jobEcho($value)
    {
        if (App::environment() != 'local-testing') {
            echo $value . PHP_EOL;
        }
    }
}
if (!function_exists('numberFormat')) {
    function numberFormat($value)
    {
        return number_format($value, 0, '.', ',');
    }
}

if (!function_exists('sendGoogleChat')) {
    function sendGoogleChat($message, $url)
    {
        // check url
        if (empty($url)) {
            return;
        }
        $params = '{"text": "' . $message . '"}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, ($params));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_exec($ch);
        curl_close($ch);
    }
}

if (!function_exists('buildRedbubbleAttribute')) {
    function buildRedbubbleAttribute($style, $color, $size)
    {
        //style=aa_mens_crew_tshirt_2001&color=black&size=a_l
        return "style={$style}&color={$color}&size={$size}";
    }
}

if (!function_exists('dynamicComparison')) {
    function dynamicComparison($value, $condition)
    {
        // Tách các phần của điều kiện
        $parts = preg_split('/\s+/', $condition);

        if (count($parts) % 2 === 0) {
            // Kiểm tra xem có số lượng phần là số chẵn
            for ($i = 0; $i < count($parts); $i += 2) {
                // Lấy giá trị điều kiện và toán tử so sánh
                $operator = $parts[$i];
                $conditionValue = floatval($parts[$i + 1]);

                // Kiểm tra xem giá trị thỏa mãn điều kiện
                switch ($operator) {
                    case '<':
                        if (!($value < $conditionValue)) {
                            return false;
                        }
                        break;
                    case '>':
                        if (!($value > $conditionValue)) {
                            return false;
                        }
                        break;
                    case '<=':
                        if (!($value <= $conditionValue)) {
                            return false;
                        }
                        break;
                    case '>=':
                        if (!($value >= $conditionValue)) {
                            return false;
                        }
                        break;
                    case '=':
                        if (!($value == $conditionValue)) {
                            return false;
                        }
                        break;
                    default:
                        return false;
                }
            }

            return true;
        }

        return false;
    }
}

if (!function_exists('interpolateQuery')) {
    function interpolateQuery($query)
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        foreach ($bindings as $binding) {
            $value = is_numeric($binding) ? $binding : "'" . addslashes($binding) . "'";
            $sql = Str::replaceFirst('?', $value, $sql);
        }

        return $sql;
    }
}

if (!function_exists('convertWeightToOZ')) {
    function convertWeightToOZ($weightValue)
    {
        // 1 lb = 15.99 oz
        return floor($weightValue * ProductStyleSizeWeight::LB_OZ * 100) / 100;
    }
}

if (!function_exists('convertWeightToLB')) {
    function convertWeightToLB($weightValue)
    {
        return floor($weightValue / ProductStyleSizeWeight::LB_OZ * 100) / 100;
    }
}

if (!function_exists('saleOrderHistory')) {
    function saleOrderHistory($user, $employeeId, $orderId, $type, $message)
    {
        SaleOrderHistory::create(
            [
                'user_id' => $user,
                'employee_id' => $employeeId,
                'order_id' => $orderId,
                'type' => $type,
                'message' => $message,
                'created_at' => Carbon::now()->toDateTimeString()
            ],
        );
    }
}

if (!function_exists('calculatorWeightForSaleOrder')) {
    function calculatorWeightForSaleOrder($saleOrder, $storeProductWeight = [])
    {
        $labelRepository = new LabelRepository();
        $sumWeightSaleOrder = 0;
        foreach ($saleOrder->items as $saleOrderItem) {
            $weightProduct = $labelRepository->getWeightProduct($saleOrderItem, $saleOrder->order_quantity, $storeProductWeight);
            $sumWeightSaleOrder += (float) $weightProduct * $saleOrderItem->quantity;
        }

        return $sumWeightSaleOrder;
    }
}

if (!function_exists('getAge')) {
    function getAge($givenTime)
    {
        $givenTimePST = Carbon::parse($givenTime, 'America/Los_Angeles');

        $now = Carbon::now('America/Los_Angeles');
        $totalHours = $now->diffInHours($givenTimePST);
        $days = floor($totalHours / 24);
        $hours = $totalHours % 24;

        $result = '';

        if ($days == 1) {
            $result .= "{$days} day";
        } elseif ($days > 1) {
            $result .= "{$days} days";
        }

        if ($hours == 1) {
            $result .= " {$hours} hour";
        } elseif ($hours > 1) {
            $result .= " {$hours} hours";
        }

        return $result;
    }
}

if (!function_exists('colorProfileConverter')) {
    function colorProfileConverter($imageBlob, $profileInput = null, $profileOutput = null)
    {
        $uuid = Str::uuid()->toString();
        $dir = 'colorProfileConverter/' . $uuid;
        $path = null;

        try {
            if ($profileOutput) {
                $tempDir = storage_path('app/icc_profile/temp/');

                if (!file_exists($tempDir)) {
                    mkdir($tempDir, 0777, true);
                }

                $baseName = basename(parse_url($profileOutput, PHP_URL_PATH));
                $path = $tempDir . DIRECTORY_SEPARATOR . $baseName;

                if (!file_exists($path)) {
                    $content = file_get_contents($profileOutput);

                    if ($content === false) {
                        Log::error("Failed to get contents from: $profileOutput");

                        return $imageBlob;
                    }

                    file_put_contents($path, $content);
                }

                $profileOutput = $path;
            }

            Storage::put($dir . '/input.png', $imageBlob);
            $pathImageInput = Storage::path($dir . '/input.png');
            $pathProfilePathInput = $profileInput ?? storage_path('app/icc_profile/sRGB2014.icc');
            $pathProfilePathOutput = $profileOutput ?? storage_path('app/icc_profile/GTX_ColorOnly_HP_A_2023.icm');
            $pathImageOutPut = Storage::path($dir . '/output.png');

            $cmdGetInfoImage = "identify -format \"%x %y %U\" \"$pathImageInput\"";
            exec($cmdGetInfoImage, $outputInfo, $returnVal);
            $cmd = "convert \"$pathImageInput\" -strip -profile \"$pathProfilePathInput\" -intent Relative -black-point-compensation -profile \"$pathProfilePathOutput\" \"$pathImageOutPut\"";

            if (!empty($outputInfo[0])) {
                $info = explode(' ', $outputInfo[0]);
                $imageDpi = '';
                $unit = '';

                if (!empty($info[2]) && $info[2] != 'Undefined') {
                    $unit = "-units {$info[2]}";
                }

                if (!empty($info[0]) && !empty($info[1])) {
                    $imageDpi = "{$info[0]}x{$info[1]}";
                }

                if (!empty($unit) && !empty($imageDpi)) {
                    $cmd .= " && convert \"$pathImageOutPut\" -density $imageDpi $unit  \"$pathImageOutPut\"";
                }
            }

            Log::info("Helper.colorProfileConverter CMD builder: $cmd");
            exec($cmd, $output, $returnVar);

            if ($returnVar !== 0) {
                Log::info("[$uuid] Helper.colorProfileConverter Error run cmd", [
                    '$cmd' => $cmd,
                    '$output' => $output,
                    '$returnVar' => $returnVar,
                ]);

                throw new Exception('Error run cmd');
            }

            $imageBlobOutput = Storage::get($dir . '/output.png');
            Storage::deleteDirectory($dir);

            return $imageBlobOutput;
        } catch (Throwable $throwable) {
            Log::error("[$uuid] Helper.colorProfileConverter catch error", [$throwable]);

            if (Storage::exists($dir)) {
                Storage::deleteDirectory($dir);
            }

            throw $throwable;
        }
    }
}

if (!function_exists('parseEncodeUrl')) {
    function parseEncodeUrl($url)
    {
        $parsedUrl = parse_url($url);

        if ($parsedUrl === false) {
            // Nếu không thể parse, trả về URL như cũ
            return $url;
        }

        // Mã hóa các thành phần URL
        $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] . '://' : '';
        $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
        $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';
        $user = isset($parsedUrl['user']) ? $parsedUrl['user'] : '';
        $pass = isset($parsedUrl['pass']) ? ':' . $parsedUrl['pass'] : '';
        $pass = ($user || $pass) ? "$pass@" : '';

        $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';

        // Kiểm tra và chỉ mã hóa `path` nếu chứa ký tự `…`
        if (strpos($path, '…') !== false) {
            $pathParts = explode('/', $path);
            $pathParts = array_map('urlencode', $pathParts);
            $path = implode('/', $pathParts);
        }

        $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
        $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

        // Tạo lại URL hợp lệ
        $fixedUrl = "$scheme$user$pass$host$port$path$query$fragment";

        return $fixedUrl;
    }
}

if (!function_exists('getBarcodeIdLimit')) {
    function getBarcodeIdLimit(): array
    {
        if (!Cache::store(config('cache.redis_store'))->has('CACHE_KEY_BARCODE_ID_LIMIT')) {
            $dataCache = putCache();
            Log::info('Helper.getBarcodeIdLimit Not exists cache, create new cache from DB', $dataCache);

            return $dataCache;
        }

        return Cache::store(config('cache.redis_store'))->get('CACHE_KEY_BARCODE_ID_LIMIT');
    }
}

if (!function_exists('putCache')) {
    function putCache(): array
    {
        $saleOrder = DB::table('sale_order')
            ->selectRaw('min(id) as min_id')
            ->whereRaw('created_at >= (NOW()-INTERVAL 60 DAY)')
            ->first();
        $saleOrderItem = DB::table('sale_order_item')
            ->selectRaw('min(id) as min_id')
            ->whereRaw('created_at >= (NOW()-INTERVAL 60 DAY)')
            ->first();
        $saleOrderItemBarcode = DB::table('sale_order_item_barcode')
            ->selectRaw('min(id) as min_id')
            ->whereRaw('created_at >= (NOW()-INTERVAL 60 DAY)')
            ->first();
        $saleOrderItemImage = DB::table('sale_order_item_image')
            ->selectRaw('min(id) as min_id')
            ->whereRaw('created_at >= (NOW()-INTERVAL 10 DAY)')
            ->first();
        $dataCache = [
            'order_id' => $saleOrder->min_id ?? 1,
            'order_item_id' => $saleOrderItem->min_id ?? 1,
            'order_item_barcode_id' => $saleOrderItemBarcode->min_id ?? 1,
            'order_item_image_id' => $saleOrderItemImage->min_id ?? 1,
        ];
        Cache::store(config('cache.redis_store'))->put('CACHE_KEY_BARCODE_ID_LIMIT', $dataCache, 907200); // 1.5 week = 907200 seconds

        return $dataCache;
    }
}

if (!function_exists('checkPermissionAccounting')) {
    function checkPermissionAccounting($user)
    {
        $allowDepartments = Department::whereIn('name', [Department::DEPARTMENT_ADMIN, Department::DEPARTMENT_ACCOUNTING, Department::DEPARTMENT_TECH, Department::DEPARTMENT_INVOCING])->pluck('id')->toArray();
        $isAdmin = $user && $user->is_admin;

        if (!$isAdmin && !in_array($user->department_id, $allowDepartments)) {
            throw new HttpException(Response::HTTP_UNAUTHORIZED, 'Unauthorized');
        }
    }
}

if (!function_exists('getSequenceNumber')) {
    function getSequenceNumber($type, $storeId)
    {
        $sequenceNumber = DB::select('CALL generate_sequence_number(?, ?)', [$type, $storeId]);
        $typeNumber = strtoupper($type) . '-' . $storeId . '-' . $sequenceNumber[0]->generated_sequence_number;

        return $typeNumber;
    }
}

if (!function_exists('setProviderConfig')) {
    function setProviderConfig($provider)
    {
        $providerConfig = PaymentAccount::where(['provider' => $provider, 'is_active' => 1, 'is_test' => PaymentAccount::LIVE_MODE])->orderByDesc('id')->first();
        Config::set('cashier.key', $providerConfig?->api_key ?? env('STRIPE_KEY'));
        Config::set('cashier.secret', $providerConfig?->api_secret ?? env('STRIPE_SECRET'));
        Config::set('cashier.webhook.secret', $providerConfig?->webhook_secret ?? env('STRIPE_WEBHOOK_SECRET'));
        Config::set('cashier.payment_account_id', $providerConfig->id ?? null);
    }
}

if (!function_exists('convertCentToDollar')) {
    function convertCentToDollar($amount)
    {
        return round($amount / WalletTopup::CENT_CONVERT_RATIO, 2);
    }
}

if (!function_exists('getClientIp')) {
    // ip4 -> ip6
    function getClientIp($request): string
    {
        $cfIp = $request->header('CF-Connecting-IP');
        if ($cfIp && filter_var($cfIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $cfIp;
        }
        $xForwardedFor = $request->header('X-Forwarded-For');
        if ($xForwardedFor) {
            $ips = array_map('trim', explode(',', $xForwardedFor));
            foreach ($ips as $ip) {
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }
}

if (!function_exists('isValidPdf')) {
    function isValidPdf($fileContent)
    {
        // Check the MIME type
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);
        if ($mimeType !== 'application/pdf') {
            return false;
        }

        // Check the file header for PDF signature
        $pdfSignature = substr($fileContent, 0, 4);
        if ($pdfSignature !== '%PDF') {
            return false;
        }

        return true;
    }
}

if (!function_exists('isRbtCreationDisabledForStore')) {
    function isEnableRBTOrderForStore($storeId): bool
    {
        $setting = Setting::where('label', Setting::RBT_EXCLUDED_STORES)->first();

        if (empty($setting)) {
            return true;
        }

        $stores = explode(',', str_replace(' ', '', $setting->value));

        if (empty($stores) || !in_array($storeId, $stores)) {
            return true;
        }

        return false;
    }
}

if (!function_exists('isStoreAllowConvertICC')) {
    function isStoreAllowConvertICC($storeId): bool
    {
        $allowConvertICC = Setting::where('label', Setting::STORE_ALLOW_CONVERT_ICC)->first();

        if (!empty($allowConvertICC)) {
            if ($allowConvertICC->value == 'all') {
                return true;
            }

            $storeAllowConvertICC = explode(',', $allowConvertICC->value ?? '');

            return in_array($storeId, $storeAllowConvertICC);
        }

        return false;
    }
}

if (!function_exists('transformDateRange')) {
    function transformDateRange(array $range)
    {
        $startDate = null;
        $endDate = null;

        switch ($range[0]) {
            case UniversalReportTag::RANGE_TODAY:
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                break;
            case UniversalReportTag::RANGE_YESTERDAY:
                $startDate = now()->subDay()->startOfDay();
                $endDate = now()->subDay()->endOfDay();
                break;
            case UniversalReportTag::RANGE_THIS_WEEK:
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case UniversalReportTag::RANGE_LAST_WEEK:
                $startDate = now()->subWeek()->startOfWeek();
                $endDate = now()->subWeek()->endOfWeek();
                break;
            case UniversalReportTag::RANGE_LAST_2_WEEKS:
                $startDate = now()->subWeeks(2)->startOfWeek();
                $endDate = now()->subWeeks(2)->endOfWeek();
                break;
            case UniversalReportTag::RANGE_THIS_MONTH:
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case UniversalReportTag::RANGE_LAST_MONTH:
                $startDate = now()->subMonth()->startOfMonth();
                $endDate = now()->subMonth()->endOfMonth();
                break;
            default:
                $startDate = Carbon::parse($range[0])->startOfDay();
                $endDate = Carbon::parse($range[1])->endOfDay();
                break;
        }

        return [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')];
    }

    if (!function_exists('str_replace_first')) {
        function str_replace_first($search, $replace, $subject)
        {
            $search = '/'.preg_quote($search, '/').'/';
            return preg_replace($search, $replace, $subject, 1);
        }
    }
}
