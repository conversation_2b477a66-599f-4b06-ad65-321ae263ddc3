<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Repositories\InvoiceRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MakeProductionInvoiceTemp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $store;
    protected $startDate;
    protected $endDate;
    protected $printSide;
    protected $downloadFulill;

    public $timeout = 3600; // Set the timeout to 3600 seconds (60 minutes)

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($store, $startDate, $endDate, $printSide, $downloadFulfill = null)
    {
        $this->store = $store;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->printSide = $printSide;
        $this->downloadFulill = $downloadFulfill;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
        $invoiceRepository->generateProductionInvoiceTemp($this->store, $this->startDate, $this->endDate, $this->printSide, $this->downloadFulill);
    }
}
