<?php

namespace App\Jobs;

use App\Mail\TopupMail;
use App\Models\WalletTopup;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TopupMailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $topup = WalletTopup::with(['store', 'transaction'])->find($this->id);

            if (!empty($topup) && !empty($topup->store) && !empty($topup->store->billing_email)) {
                $mailArray = explode(',', $topup->store->billing_email);
                $billingEmail = $mailArray[0] ?? '';

                if (!empty($billingEmail)) {
                    Mail::mailer('postmark')->to($billingEmail)->send(new TopupMail($topup));
                }
            }
        } catch (\Throwable $th) {
            Log::error('TopupMailJob.handle: ', [$th]);
        }
    }
}
