<?php

namespace App\Jobs;

use App\Models\CallbackLog;
use App\Models\IntegrateCallbackLog;
use App\Models\IntegrateLog;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\StoreCallbackUrl;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SendItemPrintedToRedbubbleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 30;

    public $saleOrderItemId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($saleOrderItemId)
    {
        $this->saleOrderItemId = $saleOrderItemId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $saleOrderItem = SaleOrderItem::select('id', 'quantity', 'external_id', 'order_id', 'store_id', 'warehouse_id')->withCount(['barcodes as barcode_count' => function ($query) {
            $query->whereNotNull('employee_print_id')->where('is_deleted', false);
        }])->with('order:id,external_number,external_key')->find($this->saleOrderItemId);
        $isCallbackLog = IntegrateCallbackLog::query()
            ->where('order_id', $saleOrderItem->order_id)
            ->where('event', IntegrateCallbackLog::PRINTED_NOTIFY)
            ->where('external_number', $saleOrderItem?->order->external_number)
            ->where('value', $saleOrderItem->id)
            ->where('store_id', $saleOrderItem->store_id)
            ->where('status', IntegrateCallbackLog::STATUS_SUCCESS)
            ->first();

        if ($saleOrderItem->barcode_count === $saleOrderItem->quantity && !$isCallbackLog) {
            // gui thong tin sang ben redbubble
            $log = IntegrateCallbackLog::create([
                'order_id' => $saleOrderItem->order_id ?? '',
                'event' => IntegrateCallbackLog::PRINTED_NOTIFY,
                'external_number' => $saleOrderItem?->order->external_number,
                'value' => $saleOrderItem->id,
                'store_id' => $saleOrderItem->store_id,
                'status' => IntegrateCallbackLog::STATUS_FAIL,
            ]);
            $this->sendOrderStatusToRedbubble($saleOrderItem, $log);
        }
    }

    protected function sendOrderStatusToRedbubble($saleOrderItem, $log)
    {
        $dataUrlCallback = IntegrateLog::where('order_id', $saleOrderItem->order_id)->where('store_id', $saleOrderItem->store_id)->first();
        $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->first();
        if (!$dataUrlCallback) {
            $message = "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id not found data intergate log" . StoreCallbackUrl::PRINTED_NOTIFY;
            $log->message = $message;
            $log->save();
            sendGoogleChat($message, $urlGoogleSpace?->value ?? '');

            return;
        }
        $dataRebubble = json_decode($dataUrlCallback?->json);
        $urlCallback = $dataRebubble->callback_uri;
        if (empty($urlCallback)) {
            $message = "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id not found url callback " . StoreCallbackUrl::PRINTED_NOTIFY;
            $log->message = "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id not found url callback " . StoreCallbackUrl::PRINTED_NOTIFY;
            $log->save();
            sendGoogleChat($message, $urlGoogleSpace?->value ?? '');

            return;
        }
        $warehouse = Warehouse::where('id', $saleOrderItem->warehouse_id)->first();
        $location = $warehouse->city . '-' . $warehouse->state . '-' . $warehouse->country;
        $dataSentRedbubble = [
            'order_id' => $saleOrderItem->order->external_key,
            'status' => 'printed',
            'items' => [
                ['item_id' => $saleOrderItem->external_id]
            ],
            'location' => $location,
            'timestamp' => Carbon::now()->toIso8601String(),
        ];

        try {
            $response = $this->sendRequestRedbubble($urlCallback, $dataSentRedbubble);
        } catch (\Exception $e) {
            sendGoogleChat("order item $saleOrderItem->external_id send printed to store failed.", $urlGoogleSpace?->value ?? '');
        }
        if ($response->status() >= 200 && $response->status() < 300) {
            $log->status = CallbackLog::STATUS_SUCCESS;
            $log->message = 'Response status: ' . $response->status() . ' Body: ' . $response->body();
            $log->data = json_encode($dataSentRedbubble);
            $log->save();
        } else {
            $message = "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id url: " . $urlCallback . ' response status:' . $response->status() . ' Body:' . $response->body();
            $log->message = $message;
            $log->save();
            sendGoogleChat("order item $saleOrderItem->external_id send printed to store failed.", $urlGoogleSpace?->value ?? '');
        }
    }

    protected function sendRequestRedbubble($url, $data)
    {
        $response = Http::timeout(30)->withHeaders([
            'Content-Type' => 'application/json',
            'User-Agent' => 'Swiftpod WebHook Agent 1.0',
        ])->post(trim($url), $data);

        return $response;
    }
}
