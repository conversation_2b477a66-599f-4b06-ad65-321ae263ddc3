<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Repositories\InvoiceRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MakeGeneralInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;

    public $timeout = 900; // Set the timeout to 900 seconds (15 minutes)

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
        try {
            setTimezone();
            $invoice = Invoice::find($this->id);
            if ($invoice) {
                if (is_null($invoice->processing_general_at)) {
                    echo "Processing general invoice $this->id\n";
                    $invoice->processing_general_at = now();
                    $invoice->save();

                    $invoiceRepository->generateGeneralInvoice($invoice);
                } else {
                    echo "Invoice $this->id is already being processed for general";
                }
            } else {
                echo "Wrong invoiceId: $this->id when making general invoice";
            }
        } catch (\Throwable $th) {
            if (isset($invoice)) {
                Invoice::where('id', $invoice->id)->update(['processing_general_at' => null]);
            }
            echo 'Error when making general invoice: ' . $th->getMessage();
            throw $th;
        }
    }
}
