<?php

namespace App\Jobs;

use App\Models\BarcodePrinted;
use App\Models\PrintMethod;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\RbtProduct;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Services\OrderIssueService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DetectPrintMethod implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $barcodeId;

    /**
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($barcodeId)
    {
        $this->barcodeId = $barcodeId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->actionDetect();
        } catch (\Exception $exception) {
            Log::channel('job_detect_print_method')->error($exception->getMessage());

            throw new \Exception($exception->getMessage());
        }
    }

    public function actionDetect()
    {
        $barcode = SaleOrderItemBarcode::with('orderItem')->where('id', $this->barcodeId)->first();

        try {
            if (!$barcode) {
                $barcode = SaleOrderItemBarcode::find($this->barcodeId);
                Log::channel('job_detect_print_method')->error("Detect print_method: $this->barcodeId not found");

                throw new \Exception("Detect print_method: $this->barcodeId not found");
            }

            if (!empty($barcode->orderItem->print_sides) && $barcode->orderItem->print_sides == ProductPrintSide::CODE_WIP_BLANK) {
                $barcode->print_method = BarcodePrinted::METHOD_BLANK;
                $barcode->save();

                return;
            }

            $resultPrintMethod = '';
            $printSides = SaleOrderItemImage::where('order_item_id', $barcode->order_item_id)->pluck('print_side');
            $printMethods = ProductPrintArea::whereHas('productPrintSide', function ($productPrintSide) use ($printSides) {
                $productPrintSide->whereIn('code', $printSides);
            })->whereHas('productStyle', function ($productStyle) use ($barcode) {
                $productStyle->where('sku', $barcode->orderItem->product_style_sku);
            })->pluck('print_method')->toArray();
            $priorityPrintMethods = PrintMethod::orderBy('priority', 'DESC')->pluck('name');

            foreach ($priorityPrintMethods as $item) {
                if (in_array($item, $printMethods)) {
                    $resultPrintMethod = $item;

                    break;
                }
            }

            $barcode->print_method = $resultPrintMethod;
            $barcode->save();
            OrderIssueService::resolveIssue([
                'order_id' => $barcode->order_id,
                'order_item_id' => $barcode->order_item_id,
                'sku' => $barcode->sku,
                'type' => SaleOrderHistory::MISSING_PRINT_METHOD,
            ]);
            $checkOrderDetectPrintMethodFinished = SaleOrderItemBarcode::query()
                ->where('order_id', $barcode->order_id)
                ->whereNull('print_method')
                ->first();

            if (empty($checkOrderDetectPrintMethodFinished)) {
                handleJob(RbtProduct::RBT_DETECT_ORDER, $barcode->order_id);
            }
        } catch (\Throwable $th) {
            if ($this->attempts() == $this->job->maxTries() && !empty($barcode)) {
                OrderIssueService::logIssue([
                    'order_id' => $barcode->order_id,
                    'order_item_id' => $barcode->order_item_id,
                    'sku' => $barcode->sku,
                    'type' => SaleOrderHistory::MISSING_PRINT_METHOD,
                ]);

                $barcode->print_method = 'ERROR';
                $barcode->save();
            }
            Log::channel('job_detect_print_method')->error("$this->barcodeId detect print method: " . $th->getMessage());

            throw $th;
        }
    }
}
