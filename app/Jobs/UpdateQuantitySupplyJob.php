<?php

namespace App\Jobs;

use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyQuantity;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateQuantitySupplyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $warehouseId = $this->data['warehouse_id'] ?? null;
        $supplyId = $this->data['supply_id'] ?? null;

        if (!$warehouseId || !$supplyId) {
            echo 'no data';

            return;
        }

        // update quantity and incoming_stock
        $result = SupplyPurchaseOrderItem::query()
            ->join('supply_purchase_order', 'supply_purchase_order_item.po_id', '=', 'supply_purchase_order.id')
            ->where('supply_purchase_order_item.supply_id', $supplyId)
            ->where('supply_purchase_order.warehouse_id', $warehouseId)
            ->whereNotIn('supply_purchase_order.order_status', [SupplyPurchaseOrder::COMPLETED_STATUS, SupplyPurchaseOrder::CANCELLED_STATUS])
            ->selectRaw('GREATEST(SUM(supply_purchase_order_item.quantity) - IFNULL(SUM(supply_purchase_order_item.quantity_onhand), 0), 0) as incoming_stock')
            ->first();
        echo 'Run update quantity supply job for supply_id: ' . $supplyId . ' and warehouse_id: ' . $warehouseId . PHP_EOL;

        SupplyQuantity::updateOrCreate(
            [
                'supply_id' => $supplyId,
                'warehouse_id' => $warehouseId
            ],
            [
                'incoming_stock' => $result ? (int) $result->incoming_stock : 0
            ],
        );
    }
}
