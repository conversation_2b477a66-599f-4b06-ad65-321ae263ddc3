<?php

namespace App\Jobs;

use App\Models\PrintMethod;
use App\Models\RbtDailyLimit;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DetectRbtOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $orderId;

    /**
     * @var int
     */

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->actionDetect();
        } catch (\Exception $exception) {
            Log::error('DetectRbtOrder.handle', [$exception]);

            throw new \Exception($exception->getMessage());
        }
    }

    public function actionDetect()
    {
        jobEcho("===========Start detect sale order rbt: $this->orderId===========");
        $saleOrder = SaleOrder::query()
            ->where('id', $this->orderId)
            ->firstOrFail();

        if (empty($saleOrder)) {
            jobEcho('Sale order not found!');

            return false;
        }

        if (!isEnableRBTOrderForStore($saleOrder->store_id)) {
            jobEcho('RBT excluded store');

            return false;
        }

        try {
            // Define conditions for RBT eligibility
            $isEligibleForRbt = $saleOrder->warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID
                && !$saleOrder->is_test
                && ($saleOrder->order_status == SaleOrder::NEW_ORDER || $saleOrder->order_status == SaleOrder::ON_HOLD)
                && !$saleOrder->is_rbt;

            if (!$isEligibleForRbt) {
                jobEcho('flag $isEligibleForRbt not pass!');

                return false;
            }

            $limitRbt = Setting::where('label', 'rbt_daily_received')->value('value');
            $dateReceived = Carbon::now('UTC')->setTimezone('America/Los_Angeles')->format('Y-m-d');
            $currentDailyRbt = RbtDailyLimit::where('date', $dateReceived)->value('total') ?? 0;

            if ($currentDailyRbt >= $limitRbt) {
                jobEcho("Over limit RBT. Limit setting: $limitRbt. Current: $currentDailyRbt");

                return false;
            }

            $listItems = SaleOrderItem::query()
                ->select(['sale_order_item.id', 'sale_order_item.quantity'])
                ->with('barcodes')
                ->leftJoin('rbt_product', 'rbt_product.product_id', '=', 'sale_order_item.product_id')
                ->where('sale_order_item.order_id', $this->orderId)
                ->where('rbt_product.is_active', true)
                ->get();

            if (count($listItems) <= 0) {
                jobEcho('line items not found! ');

                return false;
            }

            $lineItemRbt = [];
            $totalQtyRBT = 0;

            foreach ($listItems as $item) {
                $printMethods = $item->barcodes->pluck('print_method')->unique();
                $printMethodsValid = $printMethods->every(function ($method) {
                    return in_array($method, [PrintMethod::DTG, PrintMethod::NECK]);
                });

                if ($printMethodsValid) {
                    $lineItemRbt[] = $item->id;
                    $totalQtyRBT += $item->quantity;
                }
            }

            if (empty($lineItemRbt)) {
                jobEcho('Nothing item is RBT');

                return false;
            }

            jobEcho('detect is rbt:');
            $existingTags = explode(',', $saleOrder->tag ?? '');

            if (in_array(Tag::TAG_RBT_ID, $existingTags)) {
                jobEcho('RBT tag already exists, no update performed.');

                return false;
            }

            $saleOrder->update([
                'is_rbt' => SaleOrderItem::IS_RBT,
                'tag' => !empty($saleOrder->tag) ? $saleOrder->tag . ',' . Tag::TAG_RBT_ID : Tag::TAG_RBT_ID,
            ]);

            SaleOrderItem::query()
                ->whereIn('id', $lineItemRbt)
                ->update([
                    'is_rbt' => SaleOrderItem::IS_RBT,
                ]);

            // Update or create RbtDailyLimit
            RbtDailyLimit::updateOrCreate(
                [
                    'date' => $dateReceived,
                    'warehouse_id' => 1,
                ],
                [
                    'total' => DB::raw('COALESCE(total, 0) + ' . $totalQtyRBT),
                ],
            );
        } catch (\Throwable $th) {
            Log::error('DetectRbtOrder.actionDetect', [$th]);

            throw $th;
        }

        return false;
    }
}
