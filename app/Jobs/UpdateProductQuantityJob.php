<?php

namespace App\Jobs;

use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\StoreCallbackUrl;
use App\Models\StoreProduct;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UpdateProductQuantityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;

    public function __construct($params)
    {
        $this->params = $params;
    }

    public function handle()
    {
        if (!isset($this->params['warehouse_id'])) {
            $listWarehouse = ProductQuantity::select('product_quantity.warehouse_id', 'product_quantity.product_id')
                ->where('product_quantity.product_id', $this->params['product_id'])
                ->groupBy('product_quantity.warehouse_id', 'product_quantity.product_id')
                ->get()
                ->toArray();

            foreach ($listWarehouse as $item) {
                $this->updateAndNotifyFacilityStockStatus($item['product_id'], $item['warehouse_id']);
            }
        } else {
            $this->updateAndNotifyFacilityStockStatus($this->params['product_id'], $this->params['warehouse_id']);
        }
        $this->updateAndNotifyStockStatus($this->params['product_id']);

        if (isset($this->params['is_discontinued'])) {
            $product = Product::find($this->params['product_id']);
            $product->stock_status = Product::STATUS_DISCONTINUED;
            $product->discontinued_at = Carbon::now();
            $product->save();
        }
    }

    public function updateAndNotifyStockStatus($productId)
    {
        $storeIds = StoreProduct::where('product_id', $productId)
            ->distinct('store_id')
            ->pluck('store_id')
            ->toArray();

        $storeCallBacks = StoreCallbackUrl::where('event', Product::STOCK_NOTIFY)
            ->whereIn('store_id', $storeIds)
            ->pluck('store_id')
            ->toArray();

        if (empty($storeCallBacks) || empty($storeIds)) {
            echo 'Stores not found for product: ' . $productId . "\n";

            return false;
        }

        echo 'Begin update product stock status by total product: ' . $productId . "\n";
        $product = Product::find($productId);
        $productQuantity = ProductQuantity::select(DB::raw('SUM(quantity + incoming_stock) as stock'))
            ->where('product_id', $productId)
            ->first();

        if (!$product && !$productQuantity) {
            echo "No product quantity found for the specified product and warehouse.\n";

            return false;
        }

        $event = Product::STATUS_DISCONTINUED;

        if ($product->is_discontinued == true) {
            if ($product->stock_status != Product::STATUS_DISCONTINUED) {
                $this->params['is_discontinued'] = true;
                echo "Product have been discontinued.\n";
                foreach ($storeCallBacks as $storeId) {
                    echo 'Push queue send stock notify with product_id: ' . $productId . ' for store_id: ' . $storeId;

                    if (in_array($storeId, $storeIds)) {
                        $currentTime = Carbon::now();
                        handleJob(Product::JOB_SEND_STOCK_NOTIFY, [
                            'store_id' => $storeId,
                            'product_id' => $productId,
                            'event' => Product::STATUS_DISCONTINUED,
                            'current_time' => $currentTime
                        ]);
                    }
                }
            }

            return;
        }

        $quantity = isset($productQuantity) ? $productQuantity->stock : 0;

        if ($quantity > 0 && $product->stock_status == Product::STATUS_OUT_OF_STOCK) {
            $product->stock_status = Product::STATUS_IN_STOCK;
            $product->save();
            echo 'New product stock status is: ' . Product::STATUS_IN_STOCK . "\n";
            $event = Product::STATUS_IN_STOCK;
        } elseif ($quantity <= 0 && $product->stock_status == Product::STATUS_IN_STOCK) {
            $product->stock_status = Product::STATUS_OUT_OF_STOCK;
            $product->save();
            echo 'New product stock status is: ' . Product::STATUS_OUT_OF_STOCK . "\n";
            $event = Product::STATUS_OUT_OF_STOCK;
        } else {
            echo 'Product stock status remain unchanged: ' . $product->stock_status . "\n";

            return true;
        }

        foreach ($storeCallBacks as $storeId) {
            echo 'Push queue send stock notify with product_id: ' . $productId . ' for store_id: ' . $storeId;
            $currentTime = Carbon::now();
            handleJob(Product::JOB_SEND_STOCK_NOTIFY, [
                'store_id' => $storeId,
                'product_id' => $productId,
                'event' => $event,
                'current_time' => $currentTime
            ]);
        }
    }

    public function updateAndNotifyFacilityStockStatus($productId, $warehouseId)
    {
        $storeIds = StoreProduct::where('product_id', $productId)
            ->distinct('store_id')
            ->pluck('store_id')
            ->toArray();

        $storeCallBacks = StoreCallbackUrl::where('event', Product::FACILITY_NOTIFY)
            ->whereIn('store_id', $storeIds)
            ->pluck('store_id')
            ->toArray();

        if (empty($storeCallBacks) || empty($storeIds)) {
            echo 'Stores not found for product: ' . $productId . "\n";

            return false;
        }

        echo 'Begin update product stock status by Warehouse: ' . $productId . "\n";
        $product = Product::find($productId);
        $event = Product::STATUS_DISCONTINUED;

        $productQuantity = ProductQuantity::select('id', 'product_id', 'stock_status', DB::raw('SUM(quantity + incoming_stock) as stock'))
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->first();

        if ($product->is_discontinued == true) {
            if ($product->stock_status != Product::STATUS_DISCONTINUED) {
                $this->params['is_discontinued'] = true;
                echo "Product have been discontinued.\n";
                foreach ($storeCallBacks as $storeId) {
                    echo 'Push queue send stock notify with product_id: ' . $productId . ' for store_id: ' . $storeId;
                    $currentTime = Carbon::now();
                    handleJob(Product::JOB_SEND_FACILITY_NOTIFY, [
                        'store_id' => $storeId,
                        'product_id' => $productId,
                        'event' => $event,
                        'current_time' => $currentTime,
                        'warehouse_id' => $warehouseId
                    ]);
                }
            }

            return;
        }

        if (!$product && !$productQuantity) {
            echo "No product quantity found for the specified product and warehouse.\n";

            return false;
        }

        $quantity = $productQuantity->stock;

        if ($quantity > 0 && $productQuantity->stock_status == Product::STATUS_OUT_OF_STOCK) {
            $productQuantity->stock_status = Product::STATUS_IN_STOCK;
            $productQuantity->save();
            echo 'New product stock status is: ' . Product::STATUS_IN_STOCK . "\n";
            $event = Product::STATUS_IN_STOCK;
        } elseif ($quantity <= 0 && $productQuantity->stock_status == Product::STATUS_IN_STOCK) {
            $productQuantity->stock_status = Product::STATUS_OUT_OF_STOCK;
            $productQuantity->save();
            echo 'New product stock status is: ' . Product::STATUS_OUT_OF_STOCK . "\n";
            $event = Product::STATUS_OUT_OF_STOCK;
        } else {
            echo 'Product stock status remains unchanged: ' . $productQuantity->stock_status . "\n";

            return;
        }

        foreach ($storeCallBacks as $storeId) {
            echo 'Push queue send facility notify with product_id: ' . $productId . ' for store_id: ' . $storeId;

            $currentTime = Carbon::now();
            handleJob(Product::JOB_SEND_FACILITY_NOTIFY, [
                'store_id' => $storeId,
                'product_id' => $productId,
                'event' => $event,
                'current_time' => $currentTime,
                'warehouse_id' => $warehouseId
            ]);
        }
    }
}
