<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderOnHold;
use App\Models\Tag;
use App\Models\VisuaDetectImage;
use App\Models\VisuaDetectImageItem;
use App\Repositories\SaleOrderRepository;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Response;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ReceiveResponseDetectDesignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $input;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        jobEcho('Update image id: ' . $this->input['image_id'] . ' to received response.');
        try {
            $count = VisuaDetectImage::where('image_id', $this->input['image_id'])->update(['is_received_response' => 1]);
            jobEcho('Update image id: ' . $this->input['image_id'] . ' to received response ' . $count . ' row.');
            if (empty($this->input['sessionId'])) {
                jobEcho('Session Id not found.');

                return;
            }
            $detectDesign = VisuaDetectImage::with('image.imageHash')->where('visua_session_id', $this->input['sessionId'])->first();
            if (empty($detectDesign) || $detectDesign->is_received_response) {
                jobEcho('Detect design not found or received response: ' . $this->input['sessionId']);

                return;
            }

            $headers = [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'X-DEVELOPER-KEY' => env('VISUA_API_KEY')
            ];
            $url = env('VISUA_URL') . '/detect/' . $this->input['sessionId'] . '/response';
            $client = new Client();
            $request = new \GuzzleHttp\Psr7\Request('GET', $url, $headers);
            $response = $client->send($request);
            $isViolation = false;
            if ($response->getStatusCode() !== Response::HTTP_OK) {
                jobEcho('Detect design fail: ' . $this->input['sessionId'] . ' ' . $response->getBody()->getContents());
                $data['json'] = $response->getBody()->getContents();
                $detectDesign->json = $data['json'];
            } else {
                $data['json'] = $response->getBody()->getContents();
                $detectDesign->json = $data['json'];
                $content = json_decode($data['json'], true);
                if (!empty($content['data']['detections'])) {
                    $dataItem = [];
                    foreach ($content['data']['detections'] as $item) {
                        if (!empty($item['confidence']) && floatval($item['confidence']) >= VisuaDetectImage::CONFIDENCE_ON_HOLD) {
                            $dataItem[] = [
                                'visua_id' => !empty($item['id']) ? $item['id'] : (!empty($item['visualClassId']) ? $item['visualClassId'] : 'unknown'),
                                'visua_detect_image_id' => $detectDesign->id,
                                'name' => $item['name'],
                                'url' => $item['iconUrl'],
                                'type' => $item['type'],
                                'confidence' => $item['confidence'],
                            ];
                            $isViolation = true;
                        }
                    }
                    if (!empty($dataItem)) {
                        jobEcho('Detect design success: ' . $this->input['sessionId'] . ' ' . json_encode($dataItem));
                        VisuaDetectImageItem::insert($dataItem);
                    }
                }
            }
            $detectDesign->is_received_response = 1;
            if ($isViolation) {
                $detectDesign->is_ip_violation = 1;
                if (empty($detectDesign->image->imageHash->user_check_id)) {
                    $orderService = app(SaleOrderRepository::class);
                    $orderService->holdOrderByVisua($detectDesign->order_id);
                }
            } else {
                $orderOnHold = SaleOrderOnHold::where(function ($query) {
                    $query->where('store_on_hold', true)
                        ->orWhere('manual_on_hold', true);
                })
                    ->where('visua_on_hold', true)
                    ->where('order_id', $detectDesign->order_id)->update([
                        'visua_on_hold' => false
                    ]);
                if ($orderOnHold) {
                    $order = SaleOrder::find($detectDesign->order_id);
                    if ($order && $order->tag) {
                        $tag = explode(',', $order->tag);
                        $tag = array_diff($tag, [Tag::VISUA_GEMINI_ID]);
                        $order->tag = implode(',', $tag);
                        $order->save();
                    }
                } else {
                    $saleOrder = SaleOrder::whereDoesntHave('visualDetectImages', function ($query) {
                        $query->where('is_ip_violation', VisuaDetectImage::IS_TRUE);
                    })
                        ->whereHas('orderOnHold', function ($query) {
                            $query->where('store_on_hold', VisuaDetectImage::IS_FALSE)
                                ->where('manual_on_hold', VisuaDetectImage::IS_FALSE)
                                ->where('visua_on_hold', VisuaDetectImage::IS_TRUE);
                        })
                        ->where('id', $detectDesign->order_id)
                        ->where('order_status', SaleOrder::ON_HOLD)->first();
                    if ($saleOrder) {
                        $saleOrder->order_status = SaleOrder::NEW_ORDER;
                        if ($saleOrder->tag) {
                            $tag = explode(',', $saleOrder->tag);
                            $tag = array_diff($tag, [Tag::VISUA_GEMINI_ID]);
                            $saleOrder->tag = implode(',', $tag);
                        }
                        $saleOrder->save();
                        SaleOrderOnHold::where('order_id', $detectDesign->order_id)->delete();
                        SaleOrderHistory::create([
                            'order_id' => $detectDesign->order_id,
                            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                            'message' => 'Order status changed from "on hold" to "new order" by Visua',
                        ]);
                    }
                }
            }
            $detectDesign->save();
        } catch (\Exception $exception) {
            if (!empty($detectDesign)) {
                $detectDesign->is_received_response = 1;
                $detectDesign->save();
            }
            jobEcho('Exception: ' . $exception->getMessage());
            Log::channel('visua_receive_response_error')->error($exception->getMessage());
        }
    }
}
