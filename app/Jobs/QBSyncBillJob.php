<?php

namespace App\Jobs;

use App\Http\Service\AlertService;
use App\Models\PurchaseOrder;
use App\Services\QBBillService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class QBSyncBillJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $poId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($poId)
    {
        $this->queue = 'queue-sync-quick-book';
        $this->poId = $poId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QBBillService $QBBillService, AlertService $alertService)
    {
        try {
            $po = PurchaseOrder::where('id', $this->poId)
                ->with(['items:po_id,quantity,price,total'])
                ->with(['items.product:id,style,color,size,qb_ref'])
                ->with(['vendor:id,qb_ref'])
                ->with(['paymentTerm:id,qb_ref'])
                ->first();

            if (empty($po)) {
                Log::info('QBSyncBillJob.handle not found PO ID: ' . $this->poId);

                return;
            }

            if ($po->qb_ref <= 0) {
                $r = $QBBillService->create($po);
            } else {
                $r = $QBBillService->update($po);
            }

            if (empty($r)) {
                throw new \Exception("Sync Bill not success, PO number: ' . $po->po_number");
            }

            Log::info('QBSyncBillJob.handle sync Bill success, PO number: ' . $po->po_number);
        } catch (\Exception $e) {
            Log::error('QBSyncBillJob.handle', [$e]);
            $alertService->alertQuickBookIssue([
                'Sync Bill Failed.',
                'Timespan: ' . now()->toDateTimeString(),
                'PO ID: ' . ($po->id ?? 'unknown'),
                'PO number: ' . ($po->po_number ?? 'unknown'),
                'File: ' . $e->getFile(),
                'Line: ' . $e->getLine(),
                'Exception: ' . htmlentities($e->getMessage()),
            ]);
        }
    }
}
