<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentManifest;
use App\Models\ShipmentTransit;
use App\Models\ShippingIntegrationAccount;
use Carbon\Carbon;
use EasyPost\Batch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class WebhookEasyPost implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    public $timeout = 30;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    private function getTimeByStatus($status, $input)
    {
        foreach ($input as $item) {
            if ($item['status'] == $status) {
                return $item['datetime'];
            }
        }

        return null;
    }

    public function handle()
    {
        $request = collect($this->data);

        if ($request && $request->has('result') && array_key_exists('tracking_code', $request['result'])) {
            try {
                $shipment = Shipment::where('tracking_number', $request['result']['tracking_code'])->latest('id')->first();

                echo 'tracking: ' . $request['result']['tracking_code'] . "\n";

                if (!$shipment) {
                    echo 'tracking not found: ' . $request['result']['tracking_code'] . "\n";

                    return true;
                }

                $shipmentTransit = ShipmentTransit::firstOrCreate(['shipment_id' => $shipment->id], [
                    'unknown_at' => $shipment->created_at,
                    'order_id' => $shipment->order_id,
                    'warehouse_id' => $shipment->warehouse_id,
                ]);

                switch ($request['result']['object']) {
                    case 'Tracker':
                        $shipment->tracking_status = $request['result']['status'];
                        $shipment->est_delivery_at = !empty($request['result']['est_delivery_date']) ? Carbon::parse($request['result']['est_delivery_date'])->toDateTimeString() : null;
                        $shipment->save();
                        $time = $this->getTimeByStatus($request['result']['status'], $request['result']['tracking_details'] ?? []);

                        $time = Carbon::parse($time)->toDateTimeString();

                        switch ($request['result']['status']) {
                            case 'delivered':
                                $saleOrder = SaleOrder::where('id', $shipment->order_id)->first();
                                $saleOrder->shipping_amount = $saleOrder->shipping_amount + $shipment->shipment_cost;
                                $saleOrder->save();
                                if (empty($shipmentTransit->delivered_at)) {
                                    $shipmentTransit->delivered_at = $time;
                                    handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $shipment->order_id);
                                }
                                break;
                            case 'in_transit':
                                if (empty($shipmentTransit->in_transit_at)) {
                                    $shipmentTransit->in_transit_at = $time;
                                    handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $shipment->order_id);
                                }
                                break;
                            case 'return_to_sender':
                                if (empty($shipmentTransit->returned_at)) {
                                    $shipmentTransit->returned_at = $time;
                                }
                                break;
                        }
                        break;
                    case 'Refund':
                        $shipment->refund_status = $request['result']['status'];
                        $shipment->save();
                        if ($request['result']['status'] == 'refunded') {
                            $time = $this->getTimeByStatus($request['result']['status'], $request['result']['tracking_details'] ?? []);
                            if (empty($shipmentTransit->refunded_at)) {
                                $shipmentTransit->refunded_at = $time;
                            }
                        }
                        break;
                }
                $shipmentTransit->save();
            } catch (\Throwable $error) {
                \Log::error($error->getMessage());
            }
        }

        //check scan form
        if ($request && $request->has('result') && $request['result']['object'] == 'ScanForm') {
            try {
                echo 'scan form: ' . $request['result']['id'] . "\n";
                DB::beginTransaction();
                $manifest = ShipmentManifest::where('batch_id_easypost', $request['result']['batch_id'])->first();
                if ($manifest && !empty($request['result']['form_url']) && $request['result']['object'] == 'ScanForm') {
                    $manifest->url = $request['result']['form_url'];
                    $manifest->status = ShipmentManifest::STATUS_PRINT_WAITING;
                    $manifest->message = null;
                    $manifest->save();
                    // k can log lai khi luu xong trang thai
                }

                if ($manifest && empty($request['result']['form_url']) && $request['result']['object'] == 'ScanForm' && !empty($request['result']['message'])) {
                    $manifest->message = $request['result']['message'];
                    $manifest->save();
                    // k can log lai khi luu xong message
                }
                DB::commit();
            } catch (\Throwable $error) {
                DB::rollBack();
                \Log::error($error->getMessage());
            }
        }

        // check batch and create scan form
        if ($request && $request->has('result') && $request['result']['object'] == 'Batch') {
            try {
                echo 'batch: ' . $request['result']['id'] . "\n";
                DB::beginTransaction();
                $manifest = ShipmentManifest::where('batch_id_easypost', $request['result']['id'])->first();
                if ($manifest && empty($request['result']['scan_form']) && $request['result']['object'] == 'Batch') {
                    if ($request['result']['state'] == 'purchased') {
                        $apiKeyEasypost = ShippingIntegrationAccount::with('shippingIntegration')->where('id', $manifest->integration_account_id)->first();
                        $batch = Batch::retrieve($request['result']['id'], $apiKeyEasypost->shippingIntegration->api_key);
                        $batch->create_scan_form();
                    }
                    $manifest->state_batch = $request['result']['state'];
                    $manifest->save();
                }
                DB::commit();
            } catch (\Throwable $error) {
                DB::rollBack();
                \Log::error($error->getMessage());
            }
        }
    }
}
