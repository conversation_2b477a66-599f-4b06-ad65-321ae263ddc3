<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\Shipment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UpdateSaleOrderProductionStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $order_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order_id)
    {
        $this->order_id = $order_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     *
     * @throws \Exception
     */
    public function handle()
    {
        $uuid = Str::uuid();
        // sleep optimize update db
        sleep(0.1);

        try {


            $saleOrder = SaleOrder::query()
                ->with([
                    'items' => function ($query) {
                        $query->with([
                            'barcodes' => function ($q) {
                                //  lay ban ghi chua bi xoa
                                $q->where('is_deleted', '=', 0);
                            }])
                            ->orderBy('id');
                    }])
                ->with(['store', 'account'])
                ->where('id', $this->order_id)
                ->first();
            $shipment = Shipment::where('order_id', $this->order_id)
                ->with('shipmentLabelPrinted')
                ->whereHas('shipmentLabelPrinted')
                ->first();
            $productionStatus = [
                'Folded' => [],
                'Labeled' => [],
                'QC’ed' => [],
                'Pressed' => [],
                'Printed' => [],
                'Kitted' => [],
                'Pretreated' => [],
                'Pulled' => [],
                'Not Started' => [],
            ];

            if (!empty($saleOrder->items)) {
                foreach ($saleOrder->items as $item) {
                    if (!empty($item->barcodes)) {
                        foreach ($item->barcodes as $saleOrderItemBarcode) {
                            $productionStatus = $this->getProductionStatus($saleOrderItemBarcode, $productionStatus);
                        }
                    }
                }
            }

            if ($shipment) {
                $productionStatus['Labeled'][] = 1;
            } else {
                $productionStatus['Labeled'][] = 0;
            }

            $status = $this->filterProductionStatus($productionStatus);

            if ($saleOrder->production_status === $status) {

                return;
            }
            DB::table('sale_order')
                ->where('id', $this->order_id)
                ->update([
                    'production_status' => $status,
                    'updated_at' => now(),
                ]);

        } catch (\Exception $exception) {
            Log::error('UpdateSaleOrderProductionStatus.handle', [
                'uuid' => $uuid,
                'exception' => $exception
            ]);
            throw new \Exception($exception->getMessage());
        }
    }

    public function getProductionStatus($label, $data)
    {
        if (is_null($label['pulled_at']) || is_null($label['employee_pull_id'])) {
            $data['Not Started'][] = 1;
        }

        $data['Pulled'][] = !is_null($label['pulled_at']) ? 1 : 0;
        $data['Pretreated'][] = !is_null($label['pretreated_at']) ? 1 : 0;
        $data['Printed'][] = !is_null($label['printed_at']) || !is_null($label['employee_print_id']) ? 1 : 0;
        $data['Kitted'][] = !empty($label['employee_kitted_id']) || !empty($label['kitted_at']) ? 1 : 0;
        $data['QC’ed'][] = !is_null($label['qc_at']) ? 1 : 0;
        $data['Pressed'][] = !is_null($label['pressed_at']) ? 1 : 0;
        $data['Folded'][] = !is_null($label['folded_at']) ? 1 : 0;

        return $data;
    }

    public function filterProductionStatus(array $array): string
    {
        if (in_array(1, $array['Folded']) && !in_array(0, $array['Folded'])) {
            return 'folded';
        }

        if (in_array(1, $array['Labeled']) && !in_array(0, $array['Labeled'])) {
            return 'labeled';
        }

        if (in_array(1, $array['Kitted']) && !in_array(0, $array['Kitted'])) {
            return 'kitted';
        }

        if (in_array(1, $array['QC’ed']) && !in_array(0, $array['QC’ed'])) {
            return 'qc';
        }

        if (in_array(1, $array['Pressed']) && !in_array(0, $array['Pressed'])) {
            return 'pressed';
        }

        if (in_array(1, $array['Printed']) && !in_array(0, $array['Printed'])) {
            return 'printed';
        }

        if (in_array(1, $array['Pretreated']) && !in_array(0, $array['Pretreated'])) {
            return 'pretreated';
        }

        if (in_array(1, $array['Pulled']) && !in_array(0, $array['Pulled'])) {
            return 'pulled';
        }

        return 'not_started';
    }
}
