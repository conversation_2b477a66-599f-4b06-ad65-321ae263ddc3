<?php

namespace App\Jobs;

use App\Models\BarcodePrinted;
use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\User;
use App\Repositories\LocationProductRepository;
use App\Repositories\ProductQuantityRepository;
use App\Repositories\TimeCheckingRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeductionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $batchId;

    public $timeout = 3600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($batchId)
    {
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            echo "Start deduction job \n";
            $barcodePrinted = BarcodePrinted::with('employeeConvert')
                ->where('id', $this->batchId)
                ->where('print_status', BarcodePrinted::ACTIVE)
                ->whereNotNull('color_sku')
                ->firstOrFail();
            echo 'batch ID: ' . $barcodePrinted?->id . "\n";
            $saleOrderItemBarcodes = SaleOrderItemBarcode::with('saleOrder')->where('barcode_printed_id', $this->batchId)->get();
            $locationPullingShelve = Location::where('type', Location::PULLING_SHELVES)
                ->where('warehouse_id', $barcodePrinted->warehouse_id)
                ->where('is_deleted', false)
                ->firstOrFail();

            foreach ($saleOrderItemBarcodes as $key => $saleOrderItemBarcode) {
                DB::beginTransaction();
                echo 'Sale order item barcode: ' . $saleOrderItemBarcode->id . "\n";
                echo 'Sale order: ' . $saleOrderItemBarcode->order_id . "\n";

                if (!empty($saleOrderItemBarcode->saleOrder?->order_status) && in_array($saleOrderItemBarcode->saleOrder?->order_status, [SaleOrder::DRAFT, SaleOrder::ON_HOLD, SaleOrder::CANCELLED, SaleOrder::REJECTED, SaleOrder::STATUS_LATE_CANCELLED])) {
                    continue;
                }

                // Lấy thông tin về sản phẩm và vị trí trong kho
                $sku = substr($saleOrderItemBarcode->sku, -9);
                $product = Product::where('sku', $sku)->first();

                if (!$product) {
                    echo 'Product sku not found: ' . $sku . "\n";

                    continue;
                }

                // Tạo đối tượng InventoryDeduction và lưu vào cơ sở dữ liệu
                $input = [
                    'sale_order_sku' => $saleOrderItemBarcode->sku,
                    'label_id' => $saleOrderItemBarcode->label_id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'location_id' => $locationPullingShelve?->id,
                    'user_id' => User::SYSTEM,
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'employee_id' => $barcodePrinted->employee_id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'sale_order_id' => $saleOrderItemBarcode->order_id,
                ];
                $inventoryDeduction = InventoryDeduction::create($input);

                // Cập nhật trạng thái pulled_at và employee pull id  của SaleOrderItemBarcode
                $saleOrderItemBarcode->update([
                    'employee_pull_id' => $barcodePrinted->employee_id,
                    'pulled_at' => date('Y-m-d H:i:s')
                ]);

                // Tạo đối tượng Inventory và lưu vào cơ sở dữ liệu
                $inputInventory = [
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_OUTPUT,
                    'product_id' => $input['product_id'],
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'location_id' => $input['location_id'],
                    'user_id' => $input['user_id'],
                    'object_id' => $inventoryDeduction->id,
                    'object_name' => Inventory::OBJECT_DEDUCTION,
                    'quantity' => $input['quantity'],
                ];
                Inventory::create($inputInventory);

                // Cập nhật số lượng sản phẩm trong kho và vị trí
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -$input['quantity']);
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -$input['quantity']);

                // Kiểm tra và cập nhật pulled_at của đơn hàng (nếu có)
                if (!empty($saleOrderItemBarcode->saleOrder)) {
                    echo "sale order update pull. $saleOrderItemBarcode->order_id \n";
                    $this->updatePulledAt($saleOrderItemBarcode->saleOrder);
                }

                ///Todo : log time tracking for Employee
                if (!empty($inventoryDeduction->employee_id)) {
                    echo "log time tracking for employee. \n";
                    TimeCheckingRepository::createTimeTrackingWhenDeduction($inventoryDeduction->employee_id, $inventoryDeduction->created_at);
                }

                // log time line cho sale order
                saleOrderHistory(
                    null,
                    $barcodePrinted->employee_id,
                    $saleOrderItemBarcode->order_id,
                    SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE,
                    'Deduction label ID ' . $saleOrderItemBarcode?->label_id . 'created successfully by ' . $barcodePrinted?->employeeConvert?->name,
                );

                DB::commit();

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->order_id);

                echo $key . "\n";

                if ($key % 200 == 0 && $key != 0) {
                    echo "sleep 2s \n";
                    sleep(2);
                }

                echo "----------------- \n";
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            echo 'Error: ' . $th->getMessage() . "\n";
            Log::error('DeductionJob.handle', [
                'batchId' => $this->batchId,
                'exception' => $th,
            ]);
        }
    }

    public function updatePulledAt($order)
    {
        // neu order ko phai single thi bo qua
        if ($order->order_quantity > 1) {
            return false;
        }

        $totalBarcodeOfSaleOrderNotPulled = SaleOrderItemBarcode::query()
            ->where('order_id', $order->id)
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->whereNull('employee_pull_id')
            ->count();

        //Todo : kiểm tra tất cả item của sale order đã dc Pull hay chưa ?
        if (!$totalBarcodeOfSaleOrderNotPulled) {
            echo 'update pulled at for order: ' . $order->id . "\n";
            //Todo : Nếu tất cả đã dc pull hết rồi thì set order_pulled_status = 1 cho sale_order đấy
            // chỉ update cho order có quantity = 1
            return $order->update([
                'order_pulled_status' => 1,
                'order_pulled_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}
