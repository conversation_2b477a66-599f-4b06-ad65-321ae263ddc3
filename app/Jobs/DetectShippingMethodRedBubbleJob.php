<?php

namespace App\Jobs;

use App\Models\CallbackLog;
use App\Models\ProductTypeWeight;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\Setting;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StoreCallbackUrl;
use App\Models\User;
use Carbon\Carbon;
use EasyPost\Address;
use EasyPost\Util;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class DetectShippingMethodRedBubbleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $saleOrderId;

    /**
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($saleOrderId)
    {
        $this->saleOrderId = $saleOrderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $dataWeight = ProductTypeWeight::get()->toArray();
            $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->first();
            $saleOrder = SaleOrder::with(['integrateLog', 'items', 'items.productStyle', 'items.productSize', 'items.product'])
                ->with(['address' => function ($query) {
                    $query->where('type_address', 'to_address');
                }])
                ->where('store_id', Store::STORE_REDBUBBLE)
                ->where('id', $this->saleOrderId)
                ->first();
            if (empty($saleOrder->integrateLog)) {
                $message = "external number: $saleOrder->external_number - not found data intergrate log.";
                $this->sendGoogleChat($message, $urlGoogleSpace?->value ?? '');

                return;
            }

            $shippingMethodOld = $saleOrder->shipping_method;
            $orderStatusOld = $saleOrder->order_status;
            $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();
            $addressParam = [
                'verify' => ['delivery'],
                'street1' => $saleOrder->address?->street1,
                'street2' => $saleOrder->address?->street2,
                'city' => $saleOrder->address?->city,
                'state' => $saleOrder->address?->state,
                'zip' => $saleOrder->address?->zip,
                'country' => $saleOrder->address?->country,
                'company' => $saleOrder->address?->company,
                'phone' => $saleOrder->address?->phone
            ];
            $residential = $this->checkResidential($addressParam, $easypostApiKeyDefault->value);
            $weightOrder = calculatorWeightForSaleOrder($saleOrder);
            $weightOrderLbs = $this->convertOZToWeight('oz', $weightOrder);
            $dataRebubble = json_decode($saleOrder->integrateLog?->json);
            if (count($dataRebubble->shipments) > 1) {
                foreach ($dataRebubble->shipments as $shipmentItem) {
                    if ($shipmentItem->shipment_id == $saleOrder->external_number) {
                        $shippingMethodRb = $shipmentItem->shipping_info->method;
                        break;
                    }
                }
            } else {
                $shippingMethodRb = $dataRebubble->shipments[0]->shipping_info->method;
            }
            $shippingMethod = $this->getShippingMethod($saleOrder, $shippingMethodRb,
                $weightOrderLbs, $addressParam,
                $residential, $urlGoogleSpace?->value ?? '');
            DB::beginTransaction();
            if (!$shippingMethod['status']) {
                if (in_array($saleOrder->order_status, [SaleOrder::STATUS_NEW_ORDER, SaleOrder::STATUS_ON_HOLD, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_IN_PRODUCTION_CANCELLED])) {
                    $this->sendGoogleChat("Detect shipping method for external number: $saleOrder->external_number fail", $urlGoogleSpace?->value ?? '');
                    SaleOrderHistory::create([
                        'order_id' => $saleOrder->id,
                        'user_id' => User::SYSTEM,
                        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
                        'message' => "Detect shipping method for external number: $saleOrder->external_number fail.",
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);

                    return;
                }

                $urlCallback = $dataRebubble->callback_uri;
                $saleOrderItemRb = [];
                foreach ($saleOrder->items as $saleOrderItem) {
                    $saleOrderItemRb[] = [
                        'item_id' => $saleOrderItem->external_id
                    ];
                }
                $dataSentRb = [
                    'order_id' => $saleOrder->external_key === null ? $saleOrder->external_number : $saleOrder->external_key,
                    'status' => 'error',
                    'items' => $saleOrderItemRb,
                    'type' => 'shipping',
                    'message' => 'The desired carrier/service is not available on this package.',
                    'code' => 'incompatible_shipping_method',
                    'timestamp' => Carbon::now()->toIso8601String(),
                ];

                $log = CallbackLog::create([
                    'order_id' => $saleOrder->id ?? '',
                    'event' => StoreCallbackUrl::SHIPMENT_NOTIFY,
                    'value' => $saleOrder->id,
                    'store_id' => $saleOrder->store_id,
                    'status' => CallbackLog::STATUS_FAIL,
                ]);
                $response = $this->sendRequestRedbubble($urlCallback, $dataSentRb);
                if ($response->status() >= 200 && $response->status() < 300) {
                    $log->status = CallbackLog::STATUS_SUCCESS;
                    $log->message = 'Response status: ' . $response->status() . ' Body: ' . $response->body();
                    $log->save();
                } else {
                    $log->message = "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id url: " . $urlCallback . ' response status:' . $response->status() . ' Body:' . $response->body();
                    $log->save();
                }
                $saleOrder->order_status = SaleOrder::STATUS_ON_HOLD;
                $saleOrder->save();
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                    'message' => "Order status changed from $orderStatusOld to " . SaleOrder::STATUS_ON_HOLD . ' by detect shipping method.',
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                $message = "external number: $saleOrder->external_number - detect shipping method fail.";
                $this->sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
            } else {
                if ($saleOrder->order_status == SaleOrder::DRAFT) {
                    $saleOrder->order_status = SaleOrder::STATUS_NEW_ORDER;
                    SaleOrderHistory::create([
                        'order_id' => $saleOrder->id,
                        'user_id' => User::SYSTEM,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => "Order status changed from $orderStatusOld to " . SaleOrder::STATUS_NEW_ORDER . ' by detect shipping method.',
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                    StoreSaleOrderPricingSnapshotJob::dispatch($saleOrder->id)
                        ->onQueue(SaleOrder::JOB_STORE_PRICING_SNAPSHOT);
                }
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_SUCCESS,
                    'message' => "Shipping method changed from $shippingMethodOld to " . $shippingMethod['shipping_method'] . ' by detect shipping method.',
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                $saleOrder->is_eps = $shippingMethodRb == 'express' ? 1 : 0;
                $saleOrder->shipping_method = $shippingMethod['shipping_method'];
                $saleOrder->save();
            }
            $saleOrder->address->residential = $residential;
            $saleOrder->address->save();
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            $message = "external number: $saleOrder->external_number - " . $exception->getMessage();
            $this->sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
            throw new \Exception($exception->getMessage());
        }
    }

    public function checkResidential($addressParam, $easypostApiKey)
    {
        $address = Address::create($addressParam, $easypostApiKey);
        $address = Util::convertEasyPostObjectToArray($address);

        return $address['residential'] ? 1 : 0;
    }

    public function dataRuleGetServiceForRB($warehouseId)
    {
        $ruleSanJose = [
            [
                'method' => 'standard',
                'weight' => '<= 5',
                'is_domestic' => true,
                'state' => 'AL,AK,AZ,AR,CA,CO,CT,DE,DC,FL,GA,HI,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '<= 5',
                'is_domestic' => true,
                'state' => 'AL,AK,AZ,AR,CA,CO,CT,DE,DC,FL,GA,HI,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 <= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 <= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 6',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DE,DC,FL,GA,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,NE,NH,NJ,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,VT,VA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 6',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DE,DC,FL,GA,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,NE,NH,NJ,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,VT,VA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 6',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DE,DC,FL,GA,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,NE,NH,NJ,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,VT,VA,WV,WI,WY',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 6',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DE,DC,FL,GA,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,NE,NH,NJ,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,VT,VA,WV,WI,WY',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 8',
                'is_domestic' => true,
                'state' => 'CO,ID,MT,NM,UT,WA',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 8',
                'is_domestic' => true,
                'state' => 'CO,ID,MT,NM,UT,WA',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 8',
                'is_domestic' => true,
                'state' => 'CO,ID,MT,NM,UT,WA',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 8',
                'is_domestic' => true,
                'state' => 'CO,ID,MT,NM,UT,WA',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 11',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 11',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 11',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 11',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'is_domestic' => true,
                'state' => 'AL,AR,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NH,NJ,NM,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'is_domestic' => true,
                'state' => 'AL,AR,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NH,NJ,NM,NY,NC,ND,OH,OK,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'is_domestic' => true,
                'state' => 'AZ,CA,NV,OR',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 7',
                'is_domestic' => true,
                'state' => 'AL,AZ,AR,CA,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 7',
                'is_domestic' => true,
                'state' => 'AL,AZ,AR,CA,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
        ];
        $ruleVirginia = [
            [
                'method' => 'standard',
                'weight' => '<= 5',
                'is_domestic' => true,
                'state' => 'AL,AK,AR,AZ,CA,CO,CT,DE,DC,FL,GA,HI,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '<= 5',
                'is_domestic' => true,
                'state' => 'AL,AK,AR,AZ,CA,CO,CT,DE,DC,FL,GA,HI,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 <= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 <= 20',
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 8',
                'is_domestic' => true,
                'state' => 'CT,DC,DE,GA,IN,KY,MA,MD,MI,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WV',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 8',
                'is_domestic' => true,
                'state' => 'CT,DC,DE,GA,IN,KY,MA,MD,MI,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WV',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 6',
                'is_domestic' => true,
                'state' => 'AZ,CA,CO,ID,IA,KS,LA,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 6',
                'is_domestic' => true,
                'state' => 'AZ,CA,CO,ID,IA,KS,LA,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 6',
                'is_domestic' => true,
                'state' => 'AL,AZ,AR,CA,CO,FL,ID,IL,IA,KS,LA,ME,MN,MS,MO,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WI,WY',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 6',
                'is_domestic' => true,
                'state' => 'AL,AZ,AR,CA,CO,FL,ID,IL,IA,KS,LA,ME,MN,MS,MO,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WI,WY',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 11',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 11',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 8',
                'is_domestic' => true,
                'state' => 'CT,DC,DE,GA,IN,KY,MA,MD,MI,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 8',
                'is_domestic' => true,
                'state' => 'CT,DC,DE,GA,IN,KY,MA,MD,MI,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 11',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 5 < 11',
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_GroundAdvantage',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'start' => 0,
                'end' => 7.1,
                'is_domestic' => true,
                'state' => 'AZ,CA,CO,ID,KS,LA,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'start' => 0,
                'end' => 7.1,
                'is_domestic' => true,
                'state' => 'AZ,CA,CO,ID,KS,LA,MT,NE,NV,NM,ND,OK,OR,SD,TX,UT,WA,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'start' => 0,
                'end' => 7.1,
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '<= 7',
                'start' => 0,
                'end' => 7.1,
                'is_domestic' => true,
                'state' => 'AL,AR,CT,DC,DE,FL,GA,IA,IL,IN,KY,MA,MD,ME,MI,MN,MO,MS,NC,NH,NJ,NY,OH,PA,RI,SC,TN,VA,VT,WI,WV',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 7',
                'start' => 7,
                'end' => null,
                'is_domestic' => true,
                'state' => 'AL,AR,AZ,CA,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 7',
                'start' => 7,
                'end' => null,
                'is_domestic' => true,
                'state' => 'AL,AR,AZ,CA,CO,CT,DE,DC,FL,GA,ID,IL,IN,IA,KS,KY,LA,ME,MD,MA,MI,MN,MS,MO,MT,NE,NV,NH,NJ,NM,NY,NC,ND,OH,OK,OR,PA,RI,SC,SD,TN,TX,UT,VT,VA,WA,WV,WI,WY',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'start' => null,
                'end' => null,
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'start' => null,
                'end' => null,
                'is_domestic' => true,
                'state' => 'AK,HI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_FEDEX_2_DAY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '< 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_epacketDdp',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '>= 4.4',
                'is_domestic' => false,
                'country' => 'CA',
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => false,
                'state' => null,
                'carrier_code' => 'passport',
                'service_code' => 'RB_passport01_PriorityDduDelcon',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '< 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'UPSMailInnovations',
                'service_code' => 'RB_UPSM01_ExpeditedMailInnovations',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '>= 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '>= 10',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 1
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AS,GU,MP,PR,VI',
                'carrier_code' => 'FedEx',
                'service_code' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'express',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 0
            ],
            [
                'method' => 'standard',
                'weight' => '> 0 < 999999',
                'is_domestic' => true,
                'state' => 'AA,AE,AP',
                'carrier_code' => 'USPS',
                'service_code' => 'RB_USPS01_Priority',
                'residential' => 1
            ],
        ];
        $dataRules = [
            '1' => $ruleSanJose,
            '19' => $ruleVirginia
        ];

        return $dataRules[$warehouseId] ?? [];
    }

    public function sendGoogleChat($message, $url)
    {
        $params = '{"text": "' . $message . '"}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // Tắt việc trả về giá trị
        curl_exec($ch);
        curl_close($ch);
    }

    public function getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace)
    {
        try {
            $isDomestic = strtoupper($address['country']) === 'US' ? true : false;
            $state = strtoupper($address['state']);
            $addressCountry = strtoupper($address['country']);
            $address = $address['street1'] . $address['street2'];
            $shippingMethodRebubble = '';
            $isPoBoxInAddress = $this->containsPoBox($address);
            if ($isPoBoxInAddress && $isDomestic) {
                $shippingMethodPoBox = $shippingMethodRb == 'standard' ? ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD : ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS;
                $shippingMethodSwiftpod = ShippingMethod::where('store_id', Store::STORE_REDBUBBLE)->where('api_shipping_method', $shippingMethodPoBox)->first();
                if (!$shippingMethodSwiftpod) {
                    $message = 'The desired carrier/service is not available on this package';
                    $this->sendGoogleChat("external number: $saleOrder->external_number - $message", $urlGoogleSpace);
                    // bao lỗi hay làm gì đó
                    return [
                        'shipping_method' => $shippingMethodRebubble,
                        'status' => false,
                        'message' => $message
                    ];
                }

                return [
                    'shipping_method' => $shippingMethodSwiftpod->api_shipping_method,
                    'status' => true,
                    'message' => 'Success'
                ];
            }
            $collection = new Collection($this->dataRuleGetServiceForRB($saleOrder->warehouse_id));
            // Lọc các bản ghi thỏa mãn điều kiện
            $result = $collection->filter(function ($rule) use ($shippingMethodRb, $isDomestic, $state, $residential, $addressCountry) {
                if ($shippingMethodRb == 'standard') {
                    return $rule['method'] == $shippingMethodRb
                        && $rule['is_domestic'] == $isDomestic
                        && (empty($rule['state']) || in_array($state, explode(',', $rule['state'])))
                        && ($rule['residential'] == $residential || $rule['residential'] == 2)
                        && (!isset($rule['country']) || strtoupper($rule['country']) == $addressCountry);
                } elseif ($shippingMethodRb == 'express') {
                    return $rule['method'] == $shippingMethodRb
                        && $rule['is_domestic'] == $isDomestic
                        && (empty($rule['state']) || in_array($state, explode(',', $rule['state'])))
                        && (!isset($rule['country']) || strtoupper($rule['country']) == $addressCountry);
                }
            });
            if ($result->isEmpty()) {
                $message = 'The desired carrier/service is not available on this package';
                $this->sendGoogleChat("external number: $saleOrder->external_number - $message", $urlGoogleSpace);
                // bao lỗi hay làm gì đó
                return [
                    'shipping_method' => $shippingMethodRebubble,
                    'status' => false,
                    'message' => $message
                ];
            }
            $shippingMethodFind = [];

            foreach ($result as $condition) {
                if (dynamicComparison($dataWeightOrder, $condition['weight'])) {
                    $shippingMethodFind = $condition;
                    break;
                }
            }

            if (empty($shippingMethodFind)) {
                $message = 'The desired carrier/service is not available on this package';
                $this->sendGoogleChat("external number: $saleOrder->external_number - $message", $urlGoogleSpace);
                // bao lỗi hay làm gì đó
                return [
                    'shipping_method' => $shippingMethodRebubble,
                    'status' => false,
                    'message' => $message
                ];
            }
            $shippingMethodSwiftpod = ShippingMethod::where('store_id', Store::STORE_REDBUBBLE)->where('api_shipping_method', $shippingMethodFind['service_code'] ?? '')
                ->first();
            if (!$shippingMethodSwiftpod) {
                $message = 'The desired carrier/service is not available on this package';
                $this->sendGoogleChat("external number: $saleOrder->external_number - $message", $urlGoogleSpace);
                // bao lỗi hay làm gì đó
                return [
                    'shipping_method' => $shippingMethodRebubble,
                    'status' => false,
                    'message' => $message
                ];
            }

            return [
                'shipping_method' => $shippingMethodSwiftpod->api_shipping_method,
                'status' => true,
                'message' => 'Success'
            ];
        } catch (\Exception $e) {
            $this->sendGoogleChat("external number: $saleOrder->external_number - " . $e->getMessage(), $urlGoogleSpace);

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    protected function sendRequestRedbubble($url, $data)
    {
        $response = Http::timeout(30)->withHeaders([
            'Content-Type' => 'application/json',
            'User-Agent' => 'Swiftpod WebHook Agent 1.0',
        ])->post(trim($url), $data);

        return $response;
    }

    public function sendRequestSaleOrderAPI($url, $token, $data = [], $method = 'POST')
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json',
                'Accept: application/json'
            ],
        ]);

        $response = curl_exec($curl);

        curl_close($curl);

        return [
            'response' => json_decode($response),
            'code' => curl_getinfo($curl)['http_code']
        ];
    }

    public function convertOZToWeight($weightUnit, $weightValue)
    {
        if ($weightUnit == 'oz') {
            return floor($weightValue / ProductTypeWeight::LB_OZ * 100) / 100;
        }

        return $weightValue;
    }

    public function dynamicComparison($value, $condition)
    {
        // Tách ra các phần của điều kiện
        $parts = explode(' ', $condition);
        // Chuyển đổi phần số sang kiểu số
        $conditionValue = floatval($parts[1]);
        // Kiểm tra và xử lý từng phần
        switch ($parts[0]) {
            case '<':
                if ($value < $conditionValue) {
                    return true;
                }
                break;
            case '>':
                if ($value > $conditionValue) {
                    return true;
                }
                break;
            case '<=':
                if ($value <= $conditionValue) {
                    return true;
                }
                break;
            case '>=':
                if ($value >= $conditionValue) {
                    return true;
                }
                break;
            case '=':
            case '==':
                if ($value == $conditionValue) {
                    return true;
                }
                break;
            default:
                return false;
        }

        return false;
    }

    public function containsPoBox($string)
    {
        $pattern = '/ *(?:p\.?\s*o\.?|(post|postal)\s+(office|))(\s+)?(?:box|[0-9]*)? *(number|num|no)? *\d+/i';

        return preg_match($pattern, $string);
    }
}
