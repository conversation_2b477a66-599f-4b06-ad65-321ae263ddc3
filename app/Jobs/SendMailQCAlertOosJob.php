<?php

namespace App\Jobs;

use App\Mail\SendMailQCAlertOos;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\Setting;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendMailQCAlertOosJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $barcode = SaleOrderItemBarcode::findOrFail($this->data['id'] ?? 0);
            $order = SaleOrder::find($barcode->order_id);
            $product = SaleOrderItem::where('sku', $barcode->sku)->first();
            Log::info('Start sending email for label ' . $barcode->label_id . ' with order ' . $order['number']);

            $toSetting = Setting::where('label', 'qc_email_notification_to')->where('warehouse_id', $this->data['warehouse_id'])->first();
            $ccSetting = Setting::where('label', 'qc_email_notification_cc')->where('warehouse_id', Setting::GLOBAL)->first();
            $idMail = Setting::where('label', 'qc_fail_oos_id')->where('warehouse_id', Setting::GLOBAL)->first();
            // Handle potential null values
            $arrayTo = $toSetting ? array_map('trim', explode(',', $toSetting['value'])) : [];
            $arrayCc = $ccSetting ? array_map('trim', explode(',', $ccSetting['value'])) : [];

            $data = [
                'to' => $arrayTo,
                'cc_to' => $arrayCc,
                'order_status' => $this->convertStatus($order['order_status']),
                'label_id' => $barcode->label_id,
                'sku' => $product['sku'],
                'product_sku' => $product['product_sku'],
                'id' => $idMail->value + 1,
            ];
            if ($idMail) {
                $idMail->update(['value' => $idMail->value + 1]);
            }

            // Pass the data to the mailable
            Mail::to($data['to'])->cc($data['cc_to'])->send(new SendMailQCAlertOos($data));

            // Update barcode status email
            DB::table('sale_order_item_barcode_status')
            ->where('label_id', $barcode->label_id)
            ->update(['sent_oos_email' => true]);

            Log::info('Sending email completed for label ' . $barcode->label_id . ' with order ' . $order['number']);
        } catch (Exception $e) {
            Log::error('Failed when sending email for label ' . $barcode->label_id . ' with order ' . $order['number'] . ': ' . $e->getMessage());
        }
    }

    private function convertStatus($status)
    {
        switch ($status) {
            case SaleOrder::STATUS_CANCELLED:
                return 'Cancelled';

            case SaleOrder::STATUS_IN_PRODUCTION:
                return 'In Production';

            case SaleOrder::STATUS_IN_PRODUCTION_CANCELLED:
                return 'In Production Cancelled';

            case SaleOrder::STATUS_NEW_ORDER:
                return 'New Order';

            case SaleOrder::STATUS_REJECT:
                return 'Rejected';

            case SaleOrder::STATUS_ON_HOLD:
                return 'On Hold';

            default:
                return 'New Order';
        }
    }
}
