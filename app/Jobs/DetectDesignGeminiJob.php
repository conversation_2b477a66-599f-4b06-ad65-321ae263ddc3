<?php

namespace App\Jobs;

use App\Models\QueueJob;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\VisuaDetectImage;
use App\Services\integrations\GeminiClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class DetectDesignGeminiJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $errorKey = "gemini_error_count";
        $errorCount = Cache::store('redis')->get($errorKey) ?? 0;
        $setting = Setting::where('name', Setting::GOOGLE_SPACE_GEMINI_DETECT_FAIL)->first();

        if ($errorCount >= config('gemini.max_attempt_error')) {
            jobEcho('Too many errors');

            sendGoogleChat('visual_id skip: ' . ($this->data['visua_detect_image_id'] ?? '') . ' Gemini: Too many errors', $setting?->value ?? '');
            Cache::store('redis')->forget($errorKey);
            return;
        }
        if (empty($this->data['visua_detect_image_id'])) {
            return;
        }
        $visuaDetectImage = VisuaDetectImage::with([
            'image.imageHash',
        ])->find($this->data['visua_detect_image_id']);
        $image = $visuaDetectImage->image;

        if (empty($image)) {
            jobEcho(($this->visuaDetectImageId ?? '') . ': not found image');
            return true;
        }
        if (!empty($image->imageHash->user_check_id)) {
            jobEcho(($this->visuaDetectImageId ?? '') . ': image has been checked');

            return;
        }
        if ($image?->imageHash?->is_ip_violation) {
            jobEcho(($this->visuaDetectImageId ?? '') . ': due to IP Violation');

            return;
        }

        $data = [
            'image_id' => $image->id,
            'order_id' => $image->order_id,
        ];

        try {
            $gemini = new GeminiClient();
            $imageBase64 = base64_encode(file_get_contents($this->data['url']));
            $res = $gemini->analyzeImage($imageBase64);
            if (!empty($res['detections']['code'])) {
                if ($res['detections']['code'] != 503) {
                    $setting = Setting::where('name', Setting::GOOGLE_SPACE_GEMINI_DETECT_FAIL)->first();
                    sendGoogleChat(sprintf(
                        'visual_id retry: %s, Gemini status: %s',
                        $this->data['visua_detect_image_id'],
                        $res['detections']['code'],
                    ), $setting?->value ?? '');
                }
                DetectDesignGeminiJob::dispatch($this->data)
                    ->delay(now()->addMinute());
                return;
            }
            $visuaDetectImage->update([
                'gemini_json' => $res,
            ]);

            if ($res['success']) {
                $tags = [];
                foreach ($res['detections'] as $item) {
                    if (!empty($item['name'])) {
                        $tags[] = [
                            'value' => $item['name'],
                            'type' => $item['type'],
                        ];
                    }
                }
                $saleOrderItemImage = SaleOrderItemImage::find($image->id);
                $saleOrderItemImage->tags()->createMany($tags);
            } else {
                $redisKey = "gemini_retry:{$visuaDetectImage->id}";
                $retryCount = Redis::get($redisKey) ?? 0;


                // Skip if already retried 3 times
                if ($retryCount >= 3) {
                    sendGoogleChat(
                        sprintf('Retry max 3 times error: %s', $visuaDetectImage->id),
                        $setting?->value
                    );

                    return;
                }

                // Increment retry count and set expiration
                Redis::incr($redisKey);
                Redis::expire($redisKey, 3600); // Expire after 1 hour

                self::dispatch($data)->onQueue(QueueJob::QUEUE_GEMINI_DETECT_DESIGN)
                    ->delay(now()->addMinute());
                return;

                // todo: remove after
                if ($errorCount == 0) {
                    Cache::store('redis')->put($errorKey, 1, config('gemini.time_limit_error')); // 600 seconds = 10 minutes
                } else {
                    Cache::store('redis')->increment($errorKey);
                }
            }
            // Clear redis key if process completed successfully
            Redis::del($redisKey);
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $data['gemini_json'] = ['error' => $errorMessage];

            VisuaDetectImage::where('image_id', $data['image_id'])->update([
                'gemini_json' => $data['gemini_json'],
            ]);


            jobEcho($exception->getMessage());
            Log::channel('visua_detect_image_error')->error(($image->id ?? '') . ': ' . $exception->getMessage());
        }
    }
}
