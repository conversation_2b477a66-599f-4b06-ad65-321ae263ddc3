<?php

namespace App\Jobs;

use App\Models\FolderOrderDesk;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Services\OrderDeskService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncOrderDeskRejectJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public $saleOrderId)
    {
    }

    public function handle(): void
    {
        Log::channel('order_desk')->info(sprintf('Start sync order desk rejected from order desk api with saleOrderId: %s', $this->saleOrderId));
        $saleOrder = SaleOrder::with(['account'])->where('id', $this->saleOrderId)
            ->firstOrFail();
        $saleOrderAccount = $saleOrder->account;
        if ($saleOrderAccount->source != SaleOrderAccount::SOURCE_ORDER_DESK
            || $saleOrderAccount->sync_orderdesk != SaleOrderAccount::ACTIVE || empty($saleOrder->external_key)) {
            return;
        }
        $folders = FolderOrderDesk::where('sale_order_account_id', $saleOrderAccount->id)
            ->pluck('folder_id', 'name')
            ->toArray();
        $orderDeskService = new OrderDeskService($saleOrderAccount);
        $response = $orderDeskService->getOrder($saleOrder->external_key);
        $orderOrderDesk = $response->json()['order'] ?? [];

        if (!$response->successful()) {
            throw new \Exception('OrderDesk API error: ' . $response->json()['message']);
        }

        $orderDeskService->addNote(
            $saleOrder->external_key,
            $orderOrderDesk,
            sprintf(config('orderdesk.note.order_reject'), $this->saleOrderId, $saleOrder->order_status, $saleOrder->rejected_reason),
        );
        $orderDeskService->moveFolder($folders[FolderOrderDesk::FOLDER_CANCELED], $saleOrder->external_key);
        Log::channel('order_desk')->info(sprintf('Synced Order #%s status Rejected ', $saleOrder->id));
    }
}
