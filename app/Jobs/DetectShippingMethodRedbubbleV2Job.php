<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\Setting;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DetectShippingMethodRedbubbleV2Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $saleOrderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($saleOrderId)
    {
        $this->saleOrderId = $saleOrderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->first();
            $saleOrder = SaleOrder::with(['integrateLog'])
                ->where('store_id', Store::STORE_REDBUBBLE)
                ->where('id', $this->saleOrderId)
                ->first();

            if (empty($saleOrder->integrateLog)) {
                $message = "external number: $saleOrder->external_number - not found data intergrate log.";
                sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
                    'message' => "Detect shipping method for external number: $saleOrder->external_number missing integrateLog.",
                    'created_at' => now(),
                ]);

                return;
            }

            $dataRedbubble = json_decode($saleOrder->integrateLog?->json);
            $shippingMethodOld = $saleOrder->shipping_method;
            $orderStatusOld = $saleOrder->order_status;

            $carrierCode = '';
            $serviceCode = '';
            foreach ($dataRedbubble->shipments as $shipmentItem) {
                if ($shipmentItem->shipment_id == $saleOrder->external_number) {
                    $carrierCode = $shipmentItem->shipping_info->carrier;
                    $serviceCode = $shipmentItem->shipping_info->service;
                    break;
                }
            }

            if (empty($carrierCode) || empty($serviceCode)) {
                $message = "external number: $saleOrder->external_number - not found carrier or service code.";
                sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
                    'message' => $message,
                    'created_at' => now(),
                ]);

                return;
            }

            $integrateShippingMethod = ShippingMethod::where('store_id', Store::STORE_REDBUBBLE)
                ->where('integrate_carrier_code', $carrierCode)
                ->where('integrate_service_code', $serviceCode)
                ->first();

            if (empty($integrateShippingMethod)) {
                $message = "external number: $saleOrder->external_number - not found integrate shipping.";
                sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
                $saleOrder->order_status = SaleOrder::STATUS_ON_HOLD;
                $saleOrder->save();
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
                    'message' => $message,
                    'created_at' => now(),
                ]);

                return;
            }

            if ($saleOrder->order_status == SaleOrder::DRAFT) {
                $saleOrder->order_status = SaleOrder::STATUS_NEW_ORDER;
                SaleOrderHistory::create([
                    'order_id' => $saleOrder->id,
                    'user_id' => User::SYSTEM,
                    'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                    'message' => "Order status changed from $orderStatusOld to " . SaleOrder::STATUS_NEW_ORDER . ' by detect shipping method.',
                    'created_at' => now(),
                ]);
                StoreSaleOrderPricingSnapshotJob::dispatch($saleOrder->id)
                    ->onQueue(SaleOrder::JOB_STORE_PRICING_SNAPSHOT);
            }

            SaleOrderHistory::create([
                'order_id' => $saleOrder->id,
                'user_id' => User::SYSTEM,
                'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_SUCCESS,
                'message' => "Shipping method changed from $shippingMethodOld to " . $integrateShippingMethod->api_shipping_method . ' by detect shipping method.',
                'created_at' => now(),
            ]);

            $saleOrder->shipping_method = $integrateShippingMethod->api_shipping_method;
            $saleOrder->save();
        } catch (\Exception $e) {
            $message = "external number: $saleOrder->external_number - " . $e->getMessage();
            sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
            throw new \Exception($e->getMessage());
        }
    }
}
