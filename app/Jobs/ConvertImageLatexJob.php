<?php

namespace App\Jobs;

use App\Repositories\LatexPrintingRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ConvertImageLatexJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $params;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $description = 'Job Image Canvas';

    /**
     * Execute the job.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Execute the console command.
     */
    public function handle(LatexPrintingRepository $latexPrintingRepository): int
    {
        try {
            $result = $latexPrintingRepository->generatePrintFile($this->params['label_id']);
            Log::info('ConvertImageLatexJob.handle label_id: ' . $this->params['label_id'], [
                'params' => $this->params,
                'result' => $result,
            ]);

            return 1;
        } catch (Exception $e) {
            Log::error('ConvertImageLatexJob.handle', [$e]);

            return 0;
        }
    }
}
