<?php

namespace App\Jobs;

use App\Http\Service\ShipstationService;
use App\Models\LogShipStationOrder;
use App\Models\SaleOrderAccount;
use App\Models\Shipment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncTrackingToShipStation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shipmentId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $result = $this->pushTracking();
        echo $result['message'] . "\n";
        if (!$result['status']) {
            throw new \Exception("Fail sync tracking to shipstation: {$this->shipmentId} => {$result['message']}");
        }
    }

    public function pushTracking()
    {
        $shipment = Shipment::with(['saleOrder:id,account_id,external_id,external_number', 'shippingCarrier'])->find($this->shipmentId);
        if (empty($shipment->saleOrder->external_number) || empty($shipment->saleOrder->account_id)) {
            return [
                'message' => 'Shipment not found',
                'status' => false
            ];
        }

        $account = SaleOrderAccount::find($shipment->saleOrder->account_id);
        if (!$account) {
            return [
                'message' => 'Account not found',
                'status' => false
            ];
        }
        $shipstationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);

        $logOrder = $shipstationService->getLogOrder($shipment->saleOrder->external_number);
        if (empty($logOrder)) {
            $order = $this->getOrderFromShipstation($shipstationService, $shipment->saleOrder);
            if (empty($order)) {
                return [
                    'message' => 'Order not exist on shipstation',
                    'status' => false
                ];
            } else {
                $logOrder = $order;
            }
        }

        echo 'external_number: ' . $shipment->saleOrder->external_number . "\n";

        $orders = $shipstationService->getFulfillments([
            'orderNumber' => $shipment->saleOrder->external_number,
            'trackingNumber' => $shipment->tracking_number
        ]);

        if ($orders && $orders->total != 0) {
            return [
                'message' => 'Tracking is synced',
                'status' => true
            ];
        }

        $ss = $shipstationService->markShipped($logOrder->external_id, $shipment->shippingCarrier?->name, $shipment->tracking_number);

        if ($ss) {
            return [
                'message' => 'Mark as shipped ' . $shipment->saleOrder->external_id,
                'status' => true
            ];
        } else {
            return [
                'message' => 'Not mark as shipped ' . $shipment->saleOrder->external_id,
                'status' => false
            ];
        }
    }

    private function getOrderFromShipstation($shipstationService, $saleOrder)
    {
        $result = $shipstationService->getDataByOrderNumber(1, $saleOrder->external_number);
        if (!empty($result->orders[0])) {
            LogShipStationOrder::create([
                'external_id' => $result->orders[0]->orderId,
                'account_id' => $saleOrder->account_id,
                'external_number' => $result->orders[0]->orderNumber,
                'order_date' => $result->orders[0]->orderDate
            ]);

            return (object) [
                'external_id' => $result->orders[0]->orderId
            ];
        } else {
            return null;
        }
    }
}
