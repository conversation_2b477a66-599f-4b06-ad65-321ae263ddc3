<?php

namespace App\Jobs;

use App\Exports\UniversalReportExport;
use App\Models\UniversalReportExportHistory;
use App\Models\UniversalReportTemplate;
use App\Repositories\UniversalReportRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;
;

class ExportReportByRawSqlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $failOnTimeout = true;

    public $timeout = 300;

    public $tries = 1;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected int $exportHistoryId
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(UniversalReportRepository $universalReportRepository)
    {
        try {
            $export = UniversalReportExportHistory::with(['template'])
                ->find($this->exportHistoryId);

            if (isset($export->template->settingCustom)) {
                $settingExports = $export->template->settingCustom->settingExports ?? [];
            } else if (isset($export->template->settingDefault)) {
                $settingExports = $export->template->settingDefault->settingExports ?? [];
            } else {
                $settingExports = [];
            }

            $paramExport = $universalReportRepository->transformParamExport($settingExports);
            $export->update([
                'report_setting_data' => $paramExport,
            ]);

            $data = $universalReportRepository->previewTemplate($export->template, [
                ...$paramExport,
                'show_raw_sql' => true,
            ]);

            $filename = now()->format('Y-m-d-H-i-s') . '.csv';
            $filePath = UniversalReportTemplate::STORAGE_FOLDER . '/' . $export->id . '/' . $filename;
            Excel::store(new UniversalReportExport($data['sql']), $filePath, 's3');
            
            $export->update([
                'file_path' => $filePath,
                'end_time' => now(),
                'status' => UniversalReportExportHistory::STATUS_COMPLETED,
            ]);
        } catch (\Throwable $th) {
            $message = $th instanceof QueryException
                ? $th->getMessage()
                : 'Something went wrong';

            UniversalReportExportHistory::find($this->exportHistoryId)
                ->update([
                    'end_time' => now(),
                    'status' => UniversalReportExportHistory::STATUS_FAILED,
                    'message' => $message,
                ]);

            $this->fail($th);
        }
    }
}
