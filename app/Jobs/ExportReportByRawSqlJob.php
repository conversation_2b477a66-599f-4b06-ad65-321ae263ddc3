<?php

namespace App\Jobs;

use App\Exports\UniversalReportExport;
use App\Models\UniversalReportColumn;
use App\Models\UniversalReportExportHistory;
use App\Models\UniversalReportSetting;
use App\Models\UniversalReportTemplate;
use App\Repositories\UniversalReportRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportReportByRawSqlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $failOnTimeout = true;

    public $timeout = 300;

    public $tries = 1;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected int $exportHistoryId
    ) {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(UniversalReportRepository $universalReportRepository)
    {
        try {
            $export = UniversalReportExportHistory::with(['template'])
                ->withoutGlobalScopes(['isOwner'])
                ->find($this->exportHistoryId);

            $settingExport = UniversalReportSetting::with(['settingExports'])
                ->withoutGlobalScopes(['ownerReportSetting'])
                ->where('report_template_id', $export->report_template_id)
                ->where(function ($sub) use ($export) {
                    $sub->where('user_id', $export->user_id)
                        ->orWhere('is_default', true);
                })
                ->orderBy('is_default')
                ->first();

            $settingExportData = $settingExport?->settingExports ?? [];
            $paramExport = $universalReportRepository->transformParamExport($export->template, $settingExportData);

            $export->update([
                'report_setting_data' => $paramExport,
            ]);

            $data = $universalReportRepository->previewTemplate($export->template, [
                ...$paramExport,
                'show_raw_sql' => true,
            ]);

            $settingExportColumns = $settingExportData->where('model_type', UniversalReportColumn::class)
                ->map(function ($item) {
                    return [
                        'name' => $item->model_key,
                        'alias' => $item->alias,
                        'custom_alias' => $item->value,
                    ];
                })->toArray();
            $filePath = UniversalReportTemplate::STORAGE_FOLDER . '/' . $export->id . '/' . $export->name;
            Excel::store(
                new UniversalReportExport($export->report_template_id, $settingExportColumns, $data['sql']),
                $filePath,
                's3',
            );

            $export->update([
                'file_path' => $filePath,
                'file_size' => Storage::disk('s3')->size($filePath),
                'show_in_popup' => true,
                'end_time' => now(),
                'status' => UniversalReportExportHistory::STATUS_COMPLETED,
            ]);
        } catch (\Throwable $th) {
            $message = $th instanceof QueryException
                ? $th->getMessage()
                : 'Something went wrong';

            UniversalReportExportHistory::withoutGlobalScopes(['isOwner'])
                ->find($this->exportHistoryId)
                ->update([
                    'end_time' => now(),
                    'status' => UniversalReportExportHistory::STATUS_FAILED,
                    'message' => $message,
                ]);

            $this->fail($th);
        }
    }
}
