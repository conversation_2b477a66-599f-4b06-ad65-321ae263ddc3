<?php

namespace App\Jobs;

use App\Models\Store;
use App\Models\WalletTopup;
use App\Repositories\TopupRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Log;

class AutoRefillJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $storeId;

    protected $paymentService;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($storeId)
    {
        $this->storeId = $storeId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(TopupRepository $topupRepository)
    {
        $store = Store::with(['wallet', 'autoRefill'])->findOrFail($this->storeId);
        $this->paymentService = $topupRepository->getPaymentGateway($store->autoRefill->payment_gateway);

        $topupPayload = $topupRepository->makeDataTopupRefill($store, $store->autoRefill->payment_gateway);
        //check if there is a pending refill topup with the same payment method
        $pendingRefillTopup = WalletTopup::where([
            'store_id' => $store->id,
            'payment_method_id' => $topupPayload['payment_method_id'] ?? null,
            'status' => WalletTopup::STATUS_PENDING,
            'is_refill' => true,
        ])->first();

        if ($pendingRefillTopup) {
            echo 'Refill store: ' . $store->id . ' is already in progress' . PHP_EOL;

            return;
        }

        echo 'Refilling store: ' . $store->id . PHP_EOL;
        $topup = $topupRepository->createTopup($store, $topupPayload);
        DB::beginTransaction();

        try {
            $this->paymentService->charge($topup);
            $store->update(['is_refilling' => false]);

            DB::commit();
            echo 'Refill store: ' . $store->id . ' success' . PHP_EOL;
        } catch (\Throwable $th) {
            DB::rollBack();

            $topup->update([
                'status' => WalletTopup::STATUS_FAILED,
            ]);

            $store->autoRefill->update([
                'status' => false,
                'reason' => [
                    'topup' => [
                        'signature' => $topup->signature,
                        'amount_requested' => $topup->amount_requested,
                    ],
                    'payment_method_data' => $topup->payment_method_data,
                ]
            ]);

            $store->update(['is_refilling' => false]);

            Log::channel('stripe')->error('AutoRefillJob failed', [
                'store_id' => $store->id,
                'error' => $th->getMessage(),
            ]);

            $this->fail($th);
            echo 'Refill store: ' . $store->id . ' failed' . PHP_EOL;
        }
    }
}
