<?php

namespace App\Jobs;

use App\Models\RbtCountSticker;
use App\Models\RbtCountStickerPrinted;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Koerel\PdfUnite\PdfUnite;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class ConvertCountStickerToPdf implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $batch_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($batch_id)
    {
        $this->batch_id = $batch_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        echo "Start convert batch $this->batch_id. \n";

        $view = 'rbt_count_sticker';
        set_time_limit(0);
        ini_set('memory_limit', '10G');
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);

        $log_error = storage_path('logs/pdf.txt');
        try {
            $barcodePrinted = RbtCountStickerPrinted::find($this->batch_id);
            $items = RbtCountSticker::where('rbt_count_sticker_printed_id', $this->batch_id)
                ->get();
            echo 'start';
            $total_item = count($items);
            echo "count: $total_item\n";
            $render = [];
            $barcode_ids = [];
            $file_number = 1;
            $path = storage_path('app/public/' . RbtCountSticker::STICKER_PATH);
            $page = ceil($total_item / 200);
            // Create the directory if it doesn't exist
            if (!File::exists($path)) {
                File::makeDirectory($path, 07777, true);
            }
            foreach ($items as $key => &$item) {
                $qr = $this->generateQr($item->barcode);
                $item->barcode_qr = $qr;
                $arrayLabel = explode('-', $item->label);
                $item->date = $arrayLabel[0];
                $item->counter = $arrayLabel[1];
                $render[] = $item;
                if (count($render) == 200 || $key + 1 == $total_item) {
                    app()->make('view.engine.resolver')->register('blade', function () {
                        return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
                    });

                    $pdf = PDF::loadView($view, ['items' => $render])
                        ->setOptions(['dpi' => 203, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => storage_path('logs/')])
                        ->setPaper([0, 0, 2.25 * 72, 1.25 * 72]);

                    if ($page == 1) {
                        $save_file = "$path/{$this->batch_id}.pdf";
                    } else {
                        $save_file = "$path/{$this->batch_id}-{$file_number}.pdf";
                    }
                    echo "page: $file_number/$page\n";
                    echo "page: $file_number/$page, save to $save_file\n";
                    $pdf->setWarnings(true)->save($save_file);
                    echo "save to $save_file\n";
                    $pdf = null;
                    $merge_file[] = $save_file;
                    $render = [];
                    $file_number++;
                }
            }
            if (count($merge_file) > 1) {
                echo "Start sending batch. \n";
                $unite = new PdfUnite();
                $merge_file[] = "$path/$barcodePrinted->id.pdf"; // out put
                $unite->join(...$merge_file);
                foreach ($merge_file as $key => $item) {
                    if ($key + 1 == count($merge_file)) {
                        break;
                    }
                    echo $item . "\n";
                    unlink($item);
                }
            }
            $pathPdf = "$path/$barcodePrinted->id.pdf";

            if (file_exists("$path/$barcodePrinted->id.pdf")) {
                $s3 = Storage::disk('s3')->put(RbtCountSticker::STICKER_PATH . "/$barcodePrinted->id.pdf", file_get_contents($pathPdf));
                if ($s3) {
                    echo "upload to s3 success\n";
                    $barcodePrinted->url = RbtCountSticker::STICKER_PATH . "/$barcodePrinted->id.pdf";
                    $barcodePrinted->convert_status = 1;
                    $barcodePrinted->converted_at = date('Y-m-d H:i:s');
                    $barcodePrinted->save();

                    RbtCountSticker::where('rbt_count_sticker_printed_id', $this->batch_id)
                        ->update([
                            'convert_pdf_status' => 1,
                        ]);
                    unset($s3);
                    File::delete($pathPdf);
                } else {
                    echo "upload to s3 fail $barcodePrinted->id\n";
                }
            } else {
                echo "error convert $barcodePrinted->id\n";
            }
            echo "done batch $barcodePrinted->id \n";
        } catch (\Exception $e) {
            $barcodePrinted->convert_status = 2;
            $barcodePrinted->converted_at = date('Y-m-d H:i:s');
            $barcodePrinted->save();
            // Handle exceptions
            echo 'Exception: ' . $e->getMessage() . "\n";
            file_put_contents($log_error, date('Y-m-d H:i:s'), FILE_APPEND);
            file_put_contents($log_error, $e->getMessage(), FILE_APPEND);
            file_put_contents($log_error, $e->getTraceAsString(), FILE_APPEND);
        }
    }

    public function generateQr($text)
    {
        $qr = QrCode::size(150)->generate($text);

        return '<img src="data:image/svg+xml;base64,' . base64_encode($qr) . '"  width="120" height="120" />';
    }
}
