<?php

namespace App\Jobs;

use App\Exports\SaleOrderExport;
use App\Exports\SaleOrderExportTest;
use App\Exports\SaleOrderExportTestWithFromQuery;
use App\Models\ReportHistory;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportSaleOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $input;

    public $timeout = 3600;

    protected $reportHistory;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $time = Carbon::now('America/Los_Angeles')->format('Ymd_His');
        $fileName = "{$this->input['name']}.xlsx";
        setTimezone();
        $this->reportHistory = ReportHistory::find($this->input['report_history_id']);
        try {
            echo 'Starting report ID: ' . ($this->input['report_history_id'] ?? null) . "\n";
            if (!empty($this->input['test'])) {
                if ($this->input['test'] == 1) {
                    $fileName = "{$this->input['name']}.csv";
                    Excel::store(new SaleOrderExportTest($this->input), ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName", 's3', \Maatwebsite\Excel\Excel::CSV);
                } elseif ($this->input['test'] = 2) {
                    Excel::store(new SaleOrderExportTest($this->input), ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName", 's3');
                } else {
                    Excel::store(new SaleOrderExportTestWithFromQuery($this->input), ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName", 's3');
                }
            } else {
                Excel::store(new SaleOrderExport($this->input), ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName", 's3');
            }
            echo 'Done' . "\n";
        } catch (\Exception $exception) {
            echo 'Export fail. Report ID: ' . ($this->input['report_history_id'] ?? null) . ".{$exception->getMessage()}" . "\n";
            Log::error('Export fail. Report ID: ' . ($this->input['report_history_id'] ?? null) . ".{$exception->getMessage()}");
        }
        if (Storage::disk('s3')->exists(ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName")) {
            $this->reportHistory->status = ReportHistory::COMPLETE_STATUS;
            $this->reportHistory->url = ReportHistory::SALE_REPORT_FOLDER . "/$time/$fileName";
        } else {
//            $this->reportHistory->status = ReportHistory::ERROR_STATUS;
        }
        $this->reportHistory->save();
    }

    public function failed(\Exception $exception)
    {
        echo 'Export fail. Report ID: ' . ($this->input['report_history_id'] ?? null) . ".{$exception->getMessage()}" . "\n";
        $reportHistory = ReportHistory::find($this->input['report_history_id']);

        if (!empty($reportHistory)) {
            $reportHistory->status = ReportHistory::ERROR_STATUS;
            $reportHistory->save();
        }
    }
}
