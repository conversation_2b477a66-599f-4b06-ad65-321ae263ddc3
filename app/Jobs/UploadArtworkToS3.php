<?php

namespace App\Jobs;

use App\Services\UploadArtworkToS3Service;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UploadArtworkToS3 implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var int
     */
    public $imageId;

    /**
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($imageId)
    {
        $this->imageId = $imageId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $service = new UploadArtworkToS3Service($this->imageId, $this->attempts(), 3);
            $service->handle();
        } catch (\Exception $exception) {
            Log::channel('job_upload_s3')->error($exception->getMessage());
            throw new \Exception($exception->getMessage());
        }
        DB::disconnect();
    }
}
