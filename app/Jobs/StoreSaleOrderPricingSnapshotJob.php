<?php

namespace App\Jobs;

use App\Http\Service\GetOrderService;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Repositories\SaleOrderPricingRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StoreSaleOrderPricingSnapshotJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $saleOrderId;

    protected $getOrderService;

    public $timeout = 600; // Set the timeout to 600 seconds (10 minutes)

    public function __construct($saleOrderId)
    {
        $this->saleOrderId = $saleOrderId;
    }

    public function handle(): void
    {
        echo("id : {$this->saleOrderId}") . PHP_EOL;
        $this->getOrderService = app()->make(GetOrderService::class);
        $saleOrder = SaleOrder::with([
            'items',
            'items.getTypeProduct',
            'addressSaleOrder' => fn ($q) => $q->where('type_address', SaleOrderAddress::TO_ADDRESS)
        ])->findOrFail($this->saleOrderId);
        $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
        $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrder);
    }
}
