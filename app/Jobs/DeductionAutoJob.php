<?php

namespace App\Jobs;

use App\Models\BarcodePrinted;
use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\LocationProductRepository;
use App\Repositories\ProductQuantityRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeductionAutoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $barcodePrintedId;

    public $timeout = 3600;

    const PRODUCT_STYLE_NO_DEDUCTION_OPTIONS = ['BS1000'];

    const HS1000_SKU = 'HSWT';

    const G18000_SKU = 'UNGS';

    const SP30_SKU = 'SPST3';

    const SKU_3930R = 'CSST';

    const AAJT_SKU = 'AAJT';

    const UCVC_SKU = 'UCVC';

    const BS1000_SKU = 'BSTE';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($barcodePrintedId)
    {
        $this->barcodePrintedId = $barcodePrintedId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            echo "Start deduction auto job \n";
            $barcodePrinted = BarcodePrinted::where('id', $this->barcodePrintedId)
                ->where('print_status', BarcodePrinted::ACTIVE)
                ->whereNull('pulled_at')
                ->firstOrFail();
            echo 'barcode ID: ' . $this->barcodePrintedId . "\n";

            if ($barcodePrinted->warehouse_id == Warehouse::WAREHOUSE_MEXICO_ID) {
                echo "Warehouse ID is Mexico. continue...\n";

                return;
            }

            $locationPullingShelve = Location::where('type', Location::PULLING_SHELVES)
                ->where('warehouse_id', $barcodePrinted->warehouse_id)
                ->where('is_deleted', false)
                ->firstOrFail();
            $saleOrderItemBarcodes = SaleOrderItemBarcode::with('saleOrder')
                ->where('is_deleted', 0)
                ->where('barcode_printed_id', $this->barcodePrintedId)
                ->get();

            DB::beginTransaction();

            $dataOrderHistory = [];

            foreach ($saleOrderItemBarcodes as $saleOrderItemBarcode) {
                if (in_array($saleOrderItemBarcode->saleOrder->order_status, [
                    SaleOrder::DRAFT,
                    SaleOrder::ON_HOLD,
                    SaleOrder::CANCELLED,
                    SaleOrder::REJECTED,
                    SaleOrder::STATUS_LATE_CANCELLED
                ])) {
                    echo "order#{$saleOrderItemBarcode->saleOrder->order_number} status is {$saleOrderItemBarcode->saleOrder->order_status}\n";

                    continue;
                }

                if ($saleOrderItemBarcode->pulled_at) {
                    echo "barcode {$saleOrderItemBarcode->label_id} is pulled }\n";

                    continue;
                }

                echo 'Sale order item barcode: ' . $saleOrderItemBarcode->id . "\n";
                echo 'Sale order: ' . $saleOrderItemBarcode->order_id . "\n";
                $sku = substr($saleOrderItemBarcode->sku, -9);
                $product = Product::where('sku', $sku)->first();

                if (!$product) {
                    echo 'Product sku not found: ' . $sku . "\n";

                    continue;
                }

                $skuSuffix = substr($product->sku, 4);

                // Check for Printify store and HS1000 SKU
                if ($saleOrderItemBarcode->store_id == Store::PRINTIFY_API_ID && str_contains($product->sku, self::HS1000_SKU)) {
                    $skuG18000 = self::G18000_SKU . $skuSuffix;
                    $productG18000 = Product::where('sku', $skuG18000)->first();

                    if (!$productG18000) {
                        echo 'Product for G18000 sku not found: ' . $skuG18000 . "\n";

                        continue;
                    }

                    $product->id = $productG18000->id;
                } // Deduct 3930R instead of SP30 or SPST3
                elseif (str_contains($product->sku, self::SP30_SKU)) {
                    $sku3930R = self::SKU_3930R . $skuSuffix;
                    $product3930R = Product::where('sku', $sku3930R)->first();

                    if (!$product3930R) {
                        echo 'Product for 3930R sku not found: ' . $sku3930R . "\n";

                        continue;
                    }

                    $product->id = $product3930R->id;
                } // Deduct AAJT or UCVC instead of BS1000
                elseif (str_contains($product->sku, self::BS1000_SKU)) {
                    $sizeMapping = $this->sizeMapping();

                    if (isset($sizeMapping[$product->sku])) {
                        $mappingSku = $sizeMapping[$product->sku];
                        $sizeMappingProduct = Product::where('sku', $mappingSku)->first();

                        if (!$sizeMappingProduct) {
                            echo 'Product for BS1000 sku not found: ' . $mappingSku . "\n";

                            continue;
                        }

                        $product->id = $sizeMappingProduct->id;
                    } else {
                        echo 'Product for BS1000 sku not found: ' . $product->sku . "\n";

                        continue;
                    }
                }

                $input = [
                    'sale_order_sku' => $saleOrderItemBarcode->sku,
                    'label_id' => $saleOrderItemBarcode->label_id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'location_id' => $locationPullingShelve->id,
                    'user_id' => $barcodePrinted->user_id,
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'employee_id' => $barcodePrinted->employee_id,
                    'created_at' => now(),
                    'sale_order_id' => $saleOrderItemBarcode->order_id,
                ];
                $inventoryDeduction = InventoryDeduction::create($input);
                $saleOrderItemBarcode->employee_pull_id = $barcodePrinted->employee_id;
                $saleOrderItemBarcode->pulled_at = now();
                $saleOrderItemBarcode->save();
                $inputInventory = [
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_OUTPUT,
                    'product_id' => $input['product_id'],
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'location_id' => $input['location_id'],
                    'user_id' => $input['user_id'],
                    'object_id' => $inventoryDeduction->id,
                    'object_name' => Inventory::OBJECT_DEDUCTION,
                    'quantity' => $input['quantity'],
                ];
                Inventory::create($inputInventory);
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -$input['quantity']);
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -$input['quantity']);
                $this->updatePulledAtStatus($saleOrderItemBarcode->saleOrder);

                // log time line cho sale order
                if (isset($dataOrderHistory[$saleOrderItemBarcode->order_id])) {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $dataOrderHistory[$saleOrderItemBarcode->order_id] . ', ' . $saleOrderItemBarcode->label_id;
                } else {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $saleOrderItemBarcode->label_id;
                }

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->order_id);
            }
            $dataImportHistory = [];

            foreach ($dataOrderHistory as $key => $value) {
                $dataImportHistory[] = [
                    'user_id' => null,
                    'employee_id' => null,
                    'order_id' => $key,
                    'type' => SaleOrderHistory::UPDATE_ORDER_WIP_TYPE,
                    'message' => "Deduction label ID $value in batch ID $this->barcodePrintedId",
                    'created_at' => Carbon::now()->toDateTimeString()
                ];
            }

            if (!empty($dataImportHistory)) {
                SaleOrderHistory::insert($dataImportHistory);
            }

            $barcodePrinted->pulled_at = now();
            $barcodePrinted->save();

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('DeductionAutoJob.handle', [
                'barcodePrintedId' => $this->barcodePrintedId,
                'exception' => $th,
            ]);

            throw $th;
        }
    }

    private function updatePulledAtStatus($order)
    {
        if ($order->order_quantity > 1) {
            return false;
        }

        $remainingBarcodes = SaleOrderItemBarcode::query()
            ->where('order_id', $order->id)
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->whereNull('employee_pull_id')
            ->count();

        if (!$remainingBarcodes) {
            return $order->update([
                'order_pulled_status' => 1,
                'order_pulled_at' => now()
            ]);
        }

        return false;
    }

    private function isDeductionApplicable($style)
    {
        return !in_array($style, self::PRODUCT_STYLE_NO_DEDUCTION_OPTIONS);
    }

    private function sizeMapping()
    {
        return [
            'BSTE1W00S' => 'AAJT1W00S',
            'BSTE1W00M' => 'AAJT1W00M',
            'BSTE1W00L' => 'AAJT1W00L',
            'BSTE1W0XL' => 'AAJT1W0XL',
            'BSTE1W2XL' => 'AAJT1W2XL',
            'BSTE1W3XL' => 'AAJT1W3XL',
            'BSTE1B00S' => 'AAJT1B00S',
            'BSTE1B00M' => 'AAJT1B00M',
            'BSTE1B00L' => 'AAJT1B00L',
            'BSTE1B0XL' => 'AAJT1B0XL',
            'BSTE1B2XL' => 'AAJT1B2XL',
            'BSTE1B3XL' => 'AAJT1B3XL',
            'BSTE3W00S' => 'AAJT5H00S',
            'BSTE3W00M' => 'AAJT5H00M',
            'BSTE3W00L' => 'AAJT5H00L',
            'BSTE3W0XL' => 'AAJT5H0XL',
            'BSTE3W2XL' => 'AAJT5H2XL',
            'BSTE3W3XL' => 'AAJT5H3XL',
            'BSTE4M00S' => 'AAJT4M00S',
            'BSTE4M00M' => 'AAJT4M00M',
            'BSTE4M00L' => 'AAJT4M00L',
            'BSTE4M0XL' => 'AAJT4M0XL',
            'BSTE4M2XL' => 'AAJT4M2XL',
            'BSTE4M3XL' => 'AAJT4M3XL',
            'BSTEV700S' => 'AAJTV700S',
            'BSTEV700M' => 'AAJTV700M',
            'BSTEV700L' => 'AAJTV700L',
            'BSTEV70XL' => 'AAJTV70XL',
            'BSTEV72XL' => 'AAJTV72XL',
            'BSTEV73XL' => 'AAJTV73XL',
            'BSTE8X00S' => 'AAJT8X00S',
            'BSTE8X00M' => 'AAJT8X00M',
            'BSTE8X00L' => 'AAJT8X00L',
            'BSTE8X0XL' => 'AAJT8X0XL',
            'BSTE8X2XL' => 'AAJT8X2XL',
            'BSTE8X3XL' => 'AAJT8X3XL',
            'BSTE0F00S' => 'AAJT0F00S',
            'BSTE0F00M' => 'AAJT0F00M',
            'BSTE0F00L' => 'AAJT0F00L',
            'BSTE0F0XL' => 'AAJT0F0XL',
            'BSTE0F2XL' => 'AAJT0F2XL',
            'BSTE0F3XL' => 'AAJT0F3XL',
            'BSTE3H00S' => 'AAJT3H00S',
            'BSTE3H00M' => 'AAJT3H00M',
            'BSTE3H00L' => 'AAJT3H00L',
            'BSTE3H0XL' => 'AAJT3H0XL',
            'BSTE3H2XL' => 'AAJT3H2XL',
            'BSTE3H3XL' => 'AAJT3H3XL',
            'BSTE1N00S' => 'AAJT1N00S',
            'BSTE1N00M' => 'AAJT1N00M',
            'BSTE1N00L' => 'AAJT1N00L',
            'BSTE1N0XL' => 'AAJT1N0XL',
            'BSTE1N2XL' => 'AAJT1N2XL',
            'BSTE1N3XL' => 'AAJT1N3XL',
            'BSTE9F00S' => 'AAJT9F00S',
            'BSTE9F00M' => 'AAJT9F00M',
            'BSTE9F00L' => 'AAJT9F00L',
            'BSTE9F0XL' => 'AAJT9F0XL',
            'BSTE9F2XL' => 'AAJT9F2XL',
            'BSTE9F3XL' => 'AAJT9F3XL',
            'BSTE4N00S' => 'AAJTHV00S',
            'BSTE4N00M' => 'AAJTHV00M',
            'BSTE4N00L' => 'AAJTHV00L',
            'BSTE4N0XL' => 'AAJTHV0XL',
            'BSTE4N2XL' => 'AAJTHV2XL',
            'BSTE4N3XL' => 'AAJTHV3XL',
            'BSTE1R00S' => 'AAJT1R00S',
            'BSTE1R00M' => 'AAJT1R00M',
            'BSTE1R00L' => 'AAJT1R00L',
            'BSTE1R0XL' => 'AAJT1R00S',
            'BSTE1R2XL' => 'AAJT1R00S',
            'BSTE1R3XL' => 'AAJT1R00S',
            'BSTE2C00S' => 'AAJT2C00S',
            'BSTE2C00M' => 'AAJT2C00M',
            'BSTE2C00L' => 'AAJT2C00L',
            'BSTE2C0XL' => 'AAJT2C0XL',
            'BSTE2C2XL' => 'AAJT2C2XL',
            'BSTE2C3XL' => 'AAJT2C3XL',
            'BSTE8D00S' => 'AAJT8D00S',
            'BSTE8D00M' => 'AAJT8D00M',
            'BSTE8D00L' => 'AAJT8D00L',
            'BSTE8D0XL' => 'AAJT8D0XL',
            'BSTE8D2XL' => 'AAJT8D2XL',
            'BSTE8D3XL' => 'AAJT8D3XL',
            'BSTEJG00S' => 'UCVCEE00S',
            'BSTEJG00M' => 'UCVCEE00M',
            'BSTEJG00L' => 'UCVCEE00L',
            'BSTEJG0XL' => 'UCVCEE0XL',
            'BSTEJG2XL' => 'UCVCEE2XL',
            'BSTEJG3XL' => 'UCVCEE3XL',
            'BSTERY00S' => 'UCVCQ200S',
            'BSTERY00M' => 'UCVCQ200M',
            'BSTERY00L' => 'UCVCQ200L',
            'BSTERY0XL' => 'UCVCQ20XL',
            'BSTERY2XL' => 'UCVCQ22XL',
            'BSTERY3XL' => 'UCVCQ23XL',
            'BSTEME00S' => 'UCVCLL00S',
            'BSTEME00M' => 'UCVCLL00M',
            'BSTEME00L' => 'UCVCLL00L',
            'BSTEME0XL' => 'UCVCLL0XL',
            'BSTEME2XL' => 'UCVCLL2XL',
            'BSTEME3XL' => 'UCVCLL3XL',
            'BSTEON00S' => 'UCVCLM00S',
            'BSTEON00M' => 'UCVCLM00M',
            'BSTEON00L' => 'UCVCLM00L',
            'BSTEON0XL' => 'UCVCLM0XL',
            'BSTEON2XL' => 'UCVCLM2XL',
            'BSTEON3XL' => 'UCVCLM3XL',
            'BSTER700S' => 'UCVCR700S',
            'BSTER700M' => 'UCVCR700M',
            'BSTER700L' => 'UCVCR700L',
            'BSTER70XL' => 'UCVCR70XL',
            'BSTER72XL' => 'UCVCR72XL',
            'BSTER73XL' => 'UCVCR73XL',
            'BSTEOO00S' => 'UCVCOO00S',
            'BSTEOO00M' => 'UCVCOO00M',
            'BSTEOO00L' => 'UCVCOO00L',
            'BSTEOO0XL' => 'UCVCOO0XL',
            'BSTEOO2XL' => 'UCVCOO2XL',
            'BSTEOO3XL' => 'UCVCOO3XL',
            'BSTEJU00S' => 'UCVCHI00S',
            'BSTEJU00M' => 'UCVCHI00M',
            'BSTEJU00L' => 'UCVCHI00L',
            'BSTEJU0XL' => 'UCVCHI0XL',
            'BSTEJU2XL' => 'UCVCHI2XL',
            'BSTEJU3XL' => 'UCVCHI3XL',
            'BSTEQP00S' => 'UCVCOP00S',
            'BSTEQP00M' => 'UCVCOP00M',
            'BSTEQP00L' => 'UCVCOP00L',
            'BSTEQP0XL' => 'UCVCOP0XL',
            'BSTEQP2XL' => 'UCVCOP2XL',
            'BSTEQP3XL' => 'UCVCOP3XL',
            'BSTELD00S' => 'UCVC6Z00S',
            'BSTELD00M' => 'UCVC6Z00M',
            'BSTELD00L' => 'UCVC6Z00L',
            'BSTELD0XL' => 'UCVC6Z0XL',
            'BSTELD2XL' => 'UCVC6Z2XL',
            'BSTELD3XL' => 'UCVC6Z3XL',
            'BSTE2R00S' => 'UCVC2R00S',
            'BSTE2R00M' => 'UCVC2R00M',
            'BSTE2R00L' => 'UCVC2R00L',
            'BSTE2R0XL' => 'UCVC2R0XL',
            'BSTE2R2XL' => 'UCVC2R2XL',
            'BSTE2R3XL' => 'UCVC2R3XL',
        ];
    }
}
