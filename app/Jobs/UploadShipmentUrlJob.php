<?php

namespace App\Jobs;

use App\Http\Service\AlertService;
use App\Models\SaleOrder;
use App\Models\SaleOrderDetail;
use App\Models\SaleOrderHistory;
use App\Models\Shipment;
use App\Models\ShipmentLabelIssues;
use App\Repositories\ShipmentLabelIssuesRepository;
use App\Services\ReadLabelOrderService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UploadShipmentUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $shipmentId;

    const PDF_FILE = 'pdf';

    const PNG_FILE = 'png';

    const JPG_FILE = 'jpg';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId)
    {
        $this->shipmentId = $shipmentId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('UploadShipmentUrlJob.handle shipment ID: ' . $this->shipmentId, [
            'attempt' => $this->attempts()
        ]);

        try {
            $shipment = Shipment::find($this->shipmentId);

            if (empty($shipment)) {
                throw new \Exception('Shipment not found, shipment ID: ' . $this->shipmentId);
            }

            if (empty($shipment->label_url)) {
                throw new \Exception('Missing shipment label URL for shipment ID: ' . $this->shipmentId);
            }

            $file = file_get_contents($shipment->label_url);

            if (!$file) {
                throw new \Exception('Unable to download label file.');
            }

            $fileType = $this->getFileType($file);

            if (!in_array($fileType, [self::PDF_FILE, self::JPG_FILE, self::PNG_FILE])) {
                $this->logTimeOrder($shipment->order_id, 'Label is not a valid format.');
                SaleOrderDetail::query()->upsert([
                    'order_id' => $shipment->order_id,
                    'label_status' => SaleOrderDetail::DOWNLOAD_LABEL_FAILED,
                ], ['order_id']);

                return;
            }

            if (in_array($fileType, [self::JPG_FILE, self::PNG_FILE])) {
                $file = $this->convertImageToPDF($file, $this->shipmentId);
            }

            Storage::disk('s3')->put("/label/{$this->shipmentId}.pdf", $file);

            if (!Storage::disk('s3')->exists("/label/{$this->shipmentId}.pdf")) {
                $this->logTimeOrder($shipment->order_id, 'Unable to upload label PDF file to S3.');
                SaleOrderDetail::query()->upsert([
                    'order_id' => $shipment->order_id,
                    'label_status' => SaleOrderDetail::DOWNLOAD_LABEL_FAILED,
                ], ['order_id']);

                return;
            }

            $order = SaleOrder::find($shipment->order_id);

            if ($order && $order->order_status == SaleOrder::STATUS_REJECT && $order->rejected_reason == SaleOrder::REJECT_INVALID_DOWNLOAD_URL) {
                $order->update([
                    'rejected_reason' => null,
                    'order_status' => SaleOrder::STATUS_NEW_ORDER,
                    'rejected_at' => null,
                ]);
            }

            resolve(ReadLabelOrderService::class)->handleReadLabel($shipment->order_id, $shipment->id);
        } catch (\Exception $e) {
            if ($this->attempts() == $this->job->maxTries()) {
                if (!empty($shipment->order_id)) {
                    SaleOrderDetail::query()->upsert([
                        'order_id' => $shipment->order_id,
                        'label_status' => SaleOrderDetail::DOWNLOAD_LABEL_FAILED,
                    ], ['order_id']);
                }

                $this->logTimeOrder($shipment->order_id, 'Unable to download label file.');
                $dataLog = [
                    'shipment_id' => $shipment->id ?? -1,
                    'issue_type' => ShipmentLabelIssues::ISSUE_TYPE_DOWNLOAD,
                    'issues' => [
                        'exception' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ],
                ];

                $shipmentLabelIssuesRepository = new ShipmentLabelIssuesRepository();
                $shipmentLabelIssuesRepository->log($dataLog);
                $alertService = new AlertService();
                $orderNumber = $shipment->saleOrder->order_number ?? 'unknown';
                $externalNumber = $shipment->saleOrder->external_number ?? 'unknown';
                $labelUrl = $shipment->label_url ?? 'unknown';
                $alertService->alertShipmentLabelIssue("Download label order error\nOrder number: {$orderNumber}\nOrder Ref Number: {$externalNumber}\nLabel url: {$labelUrl}");
            }

            Log::error('UploadShipmentUrlJob.handle shipment ID: ' . $this->shipmentId, [$e]);

            throw $e;
        }
    }

    private function getFileType($fileContent)
    {
        // Check the MIME type and file header for PDF signature
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);
        $fileSignature = substr($fileContent, 0, 8);

        if ($mimeType === 'application/pdf' && str_starts_with($fileSignature, '%PDF')) {
            return 'pdf';
        }

        if ($mimeType === 'image/png' && str_starts_with($fileSignature, "\x89PNG")) {
            return 'png';
        }

        if ($mimeType === 'image/jpeg' && str_starts_with($fileSignature, "\xFF\xD8\xFF")) {
            return 'jpg';
        }

        return null;
    }

    public function logTimeOrder($orderId, $message)
    {
        $order = SaleOrder::find($orderId);

        if ($order && $order->order_status != SaleOrder::STATUS_REJECT) {
            $order->update([
                'rejected_reason' => SaleOrder::REJECT_INVALID_DOWNLOAD_URL,
                'order_status' => SaleOrder::STATUS_REJECT,
                'rejected_at' => Carbon::now(),
            ]);
        }

        SaleOrderHistory::create([
            'user_id' => null,
            'employee_id' => null,
            'order_id' => $orderId,
            'type' => 'Error shipment',
            'message' => $message,
            'created_at' => Carbon::now()->toDateTimeString()
        ]);
    }

    public function convertImageToPDF($fileContent, $shipmentId)
    {
        $dpi = 203;
        $pageWidth = 4 * $dpi;
        $pageHeight = 6 * $dpi;
        $shipment = Shipment::query()->with(['store', 'saleOrder:id,order_type'])->where('id', $shipmentId)->first();
        $shipment->image = '<img width="' . $pageWidth . '" height="' . $pageHeight . '" src="data:image/png;base64, ' . base64_encode($fileContent) . '" />';
        $view = view('label', ['item' => $shipment, 'width' => $pageWidth, 'height' => $pageHeight, 'notes' => null])->render();
        $pdf = PDF::loadHTML($view)->setOptions([
            'dpi' => $dpi,
            'logOutputFile' => storage_path('logs/pdf.log'),
            'tempDir' => storage_path('logs/')
        ]);
        $pdf->setWarnings(true);

        return $pdf->output();
    }
}
