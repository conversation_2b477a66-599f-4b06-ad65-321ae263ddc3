<?php

namespace App\Jobs;

use App\Models\PrintMethod;
use App\Models\ProductColor;
use App\Models\ProductStyleIccProfile;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\ProductPrintAreaRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TrimTransparentArtwork implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var int
     */
    public $imageId;

    const MAX_FILE_SIZE = 20;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($imageId)
    {
        $this->imageId = $imageId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        jobEcho("image id: $this->imageId");
        try {
            $item = SaleOrderItemImage::find($this->imageId);

            if (!empty($item) && !empty($item->pretreat_info)) {
                $imageUrl = $item->image_url;
                // check store is redbubble then create trim from s3
                $storeRedBubbleID = Store::STORE_REDBUBBLE;

                if ($item->store_id == $storeRedBubbleID) {
                    $side = $item->print_side;
                    $fileName = '/artwork/' . $item->order_date . '/' . $item->sku . '-' . $side . '.png';
                    $imageUrl = env('AWS_S3_URL') . $fileName;
                }

                $pretreatInfo = json_decode($item->pretreat_info);

                if (!preg_match('~^(?:f|ht)tps?://~i', $imageUrl)) {
                    // If not exist then add http
                    $imageUrl = 'https://' . $imageUrl;
                }

                $fileSize = $item->image_size / 1024 / 1024;
                jobEcho('file size: ' . $fileSize);

                if ($fileSize > self::MAX_FILE_SIZE) {
                    $imagick = new \Imagick($imageUrl);
                    $imagick->setImageFormat('png');
                    $imagick->stripImage(); // Loại bỏ tất cả thông tin màu sắc hoặc profile ICC từ hình ảnh
                    $imageDepth = $imagick->getImageDepth();

                    if ($imageDepth > 8) {
                        // Chuyển đổi độ sâu bit của mỗi kênh thành 8-bit
                        $imagick->setImageDepth(8);
                        // Đặt không gian màu và loại bỏ các thông tin không cần thiết
                        $imagick->setImageColorspace(\Imagick::COLORSPACE_SRGB);
                    }

                    $image = $imagick->getImagesBlob();
                    $newSize = strlen($image) / 1024 / 1024;
                    jobEcho('new file size: ' . $newSize);
                } else {
                    $image = file_get_contents($imageUrl);
                }

                try {
                    $img = imagecreatefromstring($image);

                    if ($img === false) {
                        throw new \Exception('Cannot create image from string');
                    }

                    $resolution = imageresolution($img);
                    $item->image_dpi = $resolution[0] . 'x' . $resolution[1];
                    jobEcho('image dpi: ' . $item->image_dpi);
                    $item->save();
                } catch (\Exception $exception) {
                    jobEcho($exception->getMessage());
                    $imagick = new \Imagick();
                    $imagick->readImageBlob($image);
                    jobEcho('Get dpi from image');
                    $resolution = $imagick->getImageResolution();
                    $dpiX = round($resolution['x'] * 2.54, 0);
                    $dpiY = round($resolution['y'] * 2.54, 0);

                    if ($resolution['x'] == 0) {
                        $dpiX = $dpiY = 96;
                        jobEcho('DPI X is 0, set to 96');
                    }

                    $item->image_dpi = $dpiX . 'x' . $dpiY;
                    jobEcho('Update dpi ' . $item->image_dpi . ' for image');
                    $item->save();
                }

                $side = $item->print_side;
                $fileName = $item->order_date . '/trim/' . $item->sku . '-' . $side . '.png';

                $imagick = new \Imagick();
                $imagick->readImageBlob($image);
                $img = clone $imagick;

                $width = $img->getImageWidth();
                $height = $img->getImageHeight();

                $wCrop = $pretreatInfo->width * $width;
                $hCrop = $pretreatInfo->height * $height;
                $xCrop = $pretreatInfo->left * $width;
                $yCrop = $pretreatInfo->top * $height;

                $img->setImagePage(0, 0, 0, 0);
                $img->cropImage($wCrop, $hCrop, $xCrop, $yCrop);
                $img->setImageFormat('png');
                $imageBlob = $img->getImageBlob();
                $img->clear();
                $img->destroy();
                $img = null;

                $productWhiteColorSKU = '1W';
                $isProductWhiteColor = $item->orderItem?->product_color_sku == $productWhiteColorSKU;
                $isEnableConvertICC = $item->orderItem?->ProductStyle?->icc_white_convert_status == 1;
                $isPrintMethodDTG = app(ProductPrintAreaRepository::class)->isPrintMethod(PrintMethod::DTG, $item->orderItem?->ProductStyle?->id, $side);
                $isPrintMethodNeck = app(ProductPrintAreaRepository::class)->isPrintMethod(PrintMethod::NECK, $item->orderItem?->ProductStyle?->id, $side);

                if ($isPrintMethodDTG || $isPrintMethodNeck) {
                    $product = $item->product;
                    $imageBlobConvertICC = $imageBlob;
                    $productColor = ProductColor::where('name', $product->color)->first();
                    $outputICCProfile = ProductStyleIccProfile::where([
                        'product_style' => $product->style
                    ])->first();
                    $inputProfile = storage_path('app/icc_profile/Color Space Profile.icm');

                    if ($product && $outputICCProfile) {
                        $outputProfilePath = match (strtolower($productColor->icc_color)) {
                            'white' => $outputICCProfile->getAttribute('white_url'),
                            'black' => $outputICCProfile->getAttribute('black_url'),
                            default => $outputICCProfile->getAttribute('colored_url'),
                        };
                        echo 'product color ' . $product->color . 'has ICC color ' . $productColor->icc_color . ' and output profile path ' . $outputProfilePath . PHP_EOL;
                        $newIccProfile = colorProfileConverter($imageBlobConvertICC, $inputProfile, $outputProfilePath);
                        Storage::disk('s3')->put("/artwork/icc/$fileName", $newIccProfile);
                        $item->icc_converted_at = now();
                        $item->save();
                    }

                    if ($isProductWhiteColor && $isEnableConvertICC) {
                        $imageBlob = colorProfileConverter($imageBlob);
                    }
                }

                $status = Storage::disk('s3')->put("/artwork/$fileName", $imageBlob);
                Log::channel('image')->info("Upload trim artwork to S3 $item->id: /artwork/$fileName successfully");
                jobEcho("Upload trim artwork to S3 $item->id: /artwork/$fileName successfully");

                return $status;
            }
        } catch (\Throwable $th) {
            Log::channel('image')->error($th->getMessage());
            jobEcho($th->getMessage());

            throw new \Exception($th->getMessage());
        }
    }
}
