<?php

namespace App\Jobs;

use App\Models\SaleOrderItemImage;
use App\Models\VisuaDetectImage;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Response;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DetectDesignByVisuaJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $input;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            sleep(0.2);
            jobEcho('Detect image V1.1: ' . $this->input['image_id'] . ': ' . $this->input['url']);
            if (!empty($this->input['image_id'])) {
                $image = SaleOrderItemImage::with(['imageHash', 'visuaDetect'])->where('id', $this->input['image_id'])->first();
            } else {
                return;
            }
            if (empty($image)) {
                throw new \Exception('Image not found: ' . $this->input['image_id']);
            }
            if (!empty($image->imageHash->user_check_id)) {
                jobEcho(($this->input['image_id'] ?? '') . ': image has been checked');

                return;
            }
            if ($image?->imageHash?->is_ip_violation) {
                jobEcho(($this->input['image_id'] ?? '') . ': due to IP Violation');

                return;
            }
            $data = [
                'image_id' => $image->id,
                'order_id' => $image->order_id,
            ];
            // check exist visua detect image use lock for update


            // create detect design
            // VisuaDetectImage::create($data);


            $headers = [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'X-DEVELOPER-KEY' => env('VISUA_API_KEY')
            ];
            $url = env('VISUA_URL') . '/detect';
            $client = new Client();
            $request = new \GuzzleHttp\Psr7\Request('POST', $url, $headers);
            $options = [
                'form_params' => [
                    'mediaUrl' => $this->input['url'],
                    'callbackUrl' => env('API_URL') . 'webhook/detect'
                ]
            ];
            $response = $client->send($request, $options);

            if ($response->getStatusCode() == Response::HTTP_ACCEPTED) {
                jobEcho('Success');
                $data['json'] = $response->getBody()->getContents();
                $content = json_decode($data['json'], true);
                if (!empty($content['data']['sessionId'])) {
                    $data['visua_session_id'] = $content['data']['sessionId'];
                    ReceiveResponseDetectDesignJob::dispatch([
                        'sessionId' => $data['visua_session_id'],
                        'image_id' => $data['image_id'],
                    ])->onQueue(VisuaDetectImage::RECEIVE_RESPONSE_DETECT_IMAGE_JOB)->delay(300);
                } else {
                    $data['is_received_response'] = 1;
                }
                VisuaDetectImage::where('image_id', $data['image_id'])->update([
                    'visua_session_id' => $data['visua_session_id'] ?? null,
                    'json' => $data['json'] ?? null,
                    'is_received_response' => $data['is_received_response'] ?? 0,
                ]);
            } else {
                jobEcho('Fail');
                $data['json'] = $response->getBody()->getContents();
                $data['is_received_response'] = 1;

                //  sua thanh update
                VisuaDetectImage::where('image_id', $data['image_id'])->update([
                    'visua_session_id' => null,
                    'json' => $data['json'] ?? null,
                    'is_received_response' => $data['is_received_response'],
                ]);
            }
        } catch (\Exception $exception) {
            $data['json'] = $exception->getMessage();
            $data['is_received_response'] = 1;

            // sua thanh update
            VisuaDetectImage::where('image_id', $data['image_id'])->update([
                'visua_session_id' => null,
                'json' => $data['json'] ?? null,
                'is_received_response' => $data['is_received_response'],
            ]);


            jobEcho($exception->getMessage());
            Log::channel('visua_detect_image_error')->error(($image->id ?? '') . ': ' . $exception->getMessage());
        }
    }
}
