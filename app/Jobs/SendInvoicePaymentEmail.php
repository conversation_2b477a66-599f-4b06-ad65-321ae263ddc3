<?php

namespace App\Jobs;

use App\Mail\InvoiceTransactionMail;
use App\Models\InvoiceTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendInvoicePaymentEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transactionId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($transactionId)
    {
        $this->transactionId = $transactionId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $transaction = InvoiceTransaction::with(['invoice.store'])->findOrFail($this->transactionId);
            if (!empty($transaction->invoice->store->billing_email)) {
                $mailArray = explode(',', $transaction->invoice->store->billing_email);
                $billingEmail = $mailArray[0] ?? '';

                if (!empty($billingEmail) && $transaction->payment_method !== InvoiceTransaction::CARD_METHOD) {
                    Mail::mailer('postmark')->to($billingEmail)->send(new InvoiceTransactionMail($transaction));
                }
            }
        } catch (\Throwable $th) {
            Log::error('SendInvoiceEmail.handle: ', [$th]);
            throw $th;
        }
    }
}
