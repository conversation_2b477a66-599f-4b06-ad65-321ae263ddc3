<?php

namespace App\Jobs;

use App\Models\BarcodePrinted;
use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\Warehouse;
use App\Repositories\LocationProductRepository;
use App\Repositories\ProductQuantityRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RbtDeductionAutoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;

    public $timeout = 3600;

    const PRODUCT_STYLE_NO_DEDUCTION_OPTIONS = ['BS1000'];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        DB::beginTransaction();

        try {
            echo "Start deduction auto job \n";
            $barcodePrinted = BarcodePrinted::where('id', $this->params['barcode_id'])
                ->where('print_status', BarcodePrinted::ACTIVE)
                ->whereNull('pulled_at')
                ->firstOrFail();
            echo 'barcode ID: ' . $this->params['barcode_id'] . "\n";

            if ($barcodePrinted->warehouse_id == Warehouse::WAREHOUSE_MEXICO_ID) {
                echo "Warehouse ID is Mexico. continue...\n";
                DB::rollBack();

                return;
            }

            $locationPullingShelve = Location::find($this->params['location']);
            $saleOrderItemBarcodes = SaleOrderItemBarcode::with('saleOrder')
                ->where('is_deleted', 0)
                ->where('barcode_printed_id', $this->params['barcode_id'])
                ->get();
            $dataOrderHistory = [];

            foreach ($saleOrderItemBarcodes as $saleOrderItemBarcode) {
                if (in_array($saleOrderItemBarcode->saleOrder->order_status, [
                    SaleOrder::DRAFT,
                    SaleOrder::ON_HOLD,
                    SaleOrder::CANCELLED,
                    SaleOrder::REJECTED,
                    SaleOrder::STATUS_LATE_CANCELLED
                ])) {
                    echo "order#{$saleOrderItemBarcode->saleOrder->order_number} status is {$saleOrderItemBarcode->saleOrder->order_status}\n";

                    continue;
                }

                if ($saleOrderItemBarcode->pulled_at) {
                    echo "barcode {$saleOrderItemBarcode->label_id} is pulled }\n";

                    continue;
                }

                echo 'Sale order item barcode: ' . $saleOrderItemBarcode->id . "\n";
                echo 'Sale order: ' . $saleOrderItemBarcode->order_id . "\n";
                $sku = substr($saleOrderItemBarcode->sku, -9);
                $product = Product::where('sku', $sku)->first();

                if (!$product) {
                    echo 'Product sku not found: ' . $sku . "\n";

                    continue;
                }

                if (!$this->isDeductionApplicable($product->style)) {
                    echo 'product style not deduction. continue...' . "\n";

                    continue;
                }

                $input = [
                    'sale_order_sku' => $saleOrderItemBarcode->sku,
                    'label_id' => $saleOrderItemBarcode->label_id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'location_id' => $locationPullingShelve->id,
                    'user_id' => $barcodePrinted->user_id,
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'employee_id' => $barcodePrinted->employee_id,
                    'created_at' => now(),
                    'sale_order_id' => $saleOrderItemBarcode->order_id,
                ];
                $inventoryDeduction = InventoryDeduction::create($input);
                $saleOrderItemBarcode->employee_pull_id = $barcodePrinted->employee_id;
                $saleOrderItemBarcode->pulled_at = now();
                $saleOrderItemBarcode->save();
                $inputInventory = [
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_OUTPUT,
                    'product_id' => $input['product_id'],
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'location_id' => $input['location_id'],
                    'user_id' => $input['user_id'],
                    'object_id' => $inventoryDeduction->id,
                    'object_name' => Inventory::OBJECT_DEDUCTION,
                    'quantity' => $input['quantity'],
                ];
                Inventory::create($inputInventory);
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -$input['quantity']);
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -$input['quantity']);
                $this->updatePulledAtStatus($saleOrderItemBarcode->saleOrder);

                // log time line cho sale order
                if (isset($dataOrderHistory[$saleOrderItemBarcode->order_id])) {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $dataOrderHistory[$saleOrderItemBarcode->order_id] . ', ' . $saleOrderItemBarcode->label_id;
                } else {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $saleOrderItemBarcode->label_id;
                }

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->order_id);
            }

            $dataImportHistory = [];
            $barcodeId = $this->params['barcode_id'];

            foreach ($dataOrderHistory as $key => $value) {
                $dataImportHistory[] = [
                    'user_id' => null,
                    'employee_id' => null,
                    'order_id' => $key,
                    'type' => SaleOrderHistory::UPDATE_ORDER_WIP_TYPE,
                    'message' => "Deduction label ID $value in batch ID $barcodeId",
                    'created_at' => Carbon::now()->toDateTimeString()
                ];
            }

            if (!empty($dataImportHistory)) {
                SaleOrderHistory::insert($dataImportHistory);
            }

            $barcodePrinted->pulled_at = now();
            $barcodePrinted->save();

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('RbtDeductionAutoJob.handle', [
                'params' => $this->params,
                'exception' => $th,
            ]);

            throw $th;
        }
    }

    private function updatePulledAtStatus($order)
    {
        if ($order->order_quantity > 1) {
            return false;
        }

        $remainingBarcodes = SaleOrderItemBarcode::query()
            ->where('order_id', $order->id)
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->whereNull('employee_pull_id')
            ->count();

        if (!$remainingBarcodes) {
            return $order->update([
                'order_pulled_status' => 1,
                'order_pulled_at' => now()
            ]);
        }

        return false;
    }

    private function isDeductionApplicable($style)
    {
        return !in_array($style, self::PRODUCT_STYLE_NO_DEDUCTION_OPTIONS);
    }
}
