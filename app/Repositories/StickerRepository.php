<?php

namespace App\Repositories;

use App\Events\ConvertToAiFileNotification;
use App\Models\BarcodePrinted;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\PrintingPresetSku;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\UvPresetSku;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StickerRepository extends CommonRepository
{
    public function count($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $queryFetchBarcodePending = $this->buildQueryBarcodePending($warehouse_id, $input);
        $barcodePending = $queryFetchBarcodePending->select(
            DB::raw('COUNT(*) as count'),
            'sale_order_item.product_id',
            'product.style',
            'product.size',
            'product.color',
            'product.sku',
        )
            ->groupBy('sale_order_item.product_id')
            ->get()
            ->keyBy('product_id');

        $productIds = $barcodePending->keys()->all();
        $lastCreatedDates = BarcodePrinted::whereIn('product_id', $productIds)
            ->where('warehouse_id', $warehouse_id)
            ->select('product_id', DB::raw('MAX(created_at) as last_created_at'))
            ->groupBy('product_id')
            ->pluck('last_created_at', 'product_id');

        $total = $barcodePending->sum('count');

        $result = $barcodePending->map(function ($item) use ($lastCreatedDates) {
            return [
                'product_id' => $item->product_id,
                'count' => $item->count,
                'color' => $item->color,
                'size' => $item->size,
                'sku' => $item->sku,
                'last_created_at' => isset($lastCreatedDates[$item->product_id])
                    ? Carbon::parse($lastCreatedDates[$item->product_id])->format('m/d/Y H:i')
                    : null,
                'style_name' => $item->style
            ];
        })->values()->toArray();

        return [
            'total' => $total,
            'data' => $result
        ];
    }

    private function buildQueryBarcodePending($warehouse_id, $input)
    {
        return SaleOrderItemBarcode::where('sale_order_item_barcode.print_method', ProductStyle::METHOD_UV)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('product_style.type', ProductType::STICKER)
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->when(!empty($input['priorityStores']), function ($query) use ($input) {
                if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                    $query->where('sale_order_item_barcode.store_id', $input['store_id']);
                } else {
                    $query->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
                }
            })
            ->when(!empty($input['product_id']), function ($query) use ($input) {
                $query->where('sale_order_item.product_id', $input['product_id']);
            })
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->join('product_style', 'product_style.name', '=', 'product.style');
    }

    public function list($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return PdfConverted::listSticker($warehouse_id, $limit);
    }

    public function history($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return PdfConverted::listHistorySticker($warehouse_id, $limit, $label_id);
    }

    public function preset($input)
    {
        $product_id = $input['product_id'];
        $product = Product::findByIdAndPresetSkuUv($product_id);

        if (!$product) {
            throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
        }

        if (is_null($product->printingPresetSku->platen_front_size)) {
            throw new Exception('Product not setting platen size yet!', Response::HTTP_BAD_REQUEST);
        }

        $widthFrame = $product->uvPresetSku->page_width * 15;
        $maxItemOnRow = $product->uvPresetSku->max_item_on_row;
        $width = $widthFrame / $maxItemOnRow;

        $platenSize = explode('x', $product->printingPresetSku->platen_front_size);
        $widthItem = (float) $platenSize[0];
        $heightItem = (float) $platenSize[1];

        $height = $heightItem / $widthItem * $width;

        // size preview front end
        return [
            'width_item' => $width,
            'height_item' => $height,
            'width' => $widthFrame,
            'max_item_on_row' => $maxItemOnRow,
        ];
    }

    public function convertToAi($input)
    {
        $pdfConvertedId = $input['pdf_converted_id'];
        $converted = PdfConverted::findToConvertAi($pdfConvertedId);
        if (!$converted) {
            throw new Exception('Must download the file before converting!', Response::HTTP_BAD_REQUEST);
        }
        broadcast(new ConvertToAiFileNotification((array) json_decode($converted->position_illustrator)));

        return [
            'data' => $converted->position_illustrator
        ];
    }

    public function generatePdf($input)
    {
        try {
            DB::beginTransaction();
            $employee_id = $input['employee_id'];
            $warehouse_id = config('jwt.warehouse_id');
            $product_id = $input['product_id'];
            $limit = $input['limit'];
            $options = [];
            $queryFetchBarcodePending = $this->buildQueryBarcodePending($warehouse_id, $input);
            $barcodePending = $queryFetchBarcodePending->select(
                'sale_order_item_barcode.label_id',
                'sale_order_item_barcode.order_item_id',
                'sale_order_item_barcode.id as barcode_id',
                'sale_order_item.print_sides',
            )
                ->limit($limit)->get();

            if ($barcodePending->isEmpty()) {
                throw new Exception('barcode pending is empty, please convert again!', Response::HTTP_NOT_FOUND);
            }
            $product = Product::find($product_id);
            if (!$product) {
                throw new Exception('Product not found', Response::HTTP_NOT_FOUND);
            }
            $presetSticker = PrintingPresetSku::where('sku', $product->sku)->first();
            if (!$presetSticker) {
                throw new Exception('Preset not found', Response::HTTP_NOT_FOUND);
            }
            $uvPreset = UvPresetSku::where('product_sku', $product->sku)->first();
            if (!$uvPreset) {
                throw new Exception('Not define uv preset yet', Response::HTTP_NOT_FOUND);
            }
            $printSideSticker = ProductPrintSide::whereIn('code_wip', [ProductPrintSide::CODE_WIP_FRONT, ProductPrintSide::CODE_WIP_DIE_CUT, ProductPrintSide::CODE_WIP_SQUARE])
                ->select('code_wip', 'code', 'id')
                ->get()
                ->keyBy('code_wip'); //code_wip là print_sides trong sale_order_item

            foreach ($barcodePending as $itemBarcode) {
                if (strlen($itemBarcode->print_sides) > 1) {
                    throw new Exception("The 'print_sides' value must not exceed 1 character.", Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if (!in_array($itemBarcode->print_sides, [ProductPrintSide::CODE_WIP_FRONT, ProductPrintSide::CODE_WIP_DIE_CUT, ProductPrintSide::CODE_WIP_SQUARE])) {
                    throw new Exception('The print area not a sticker', Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                if ($itemBarcode->print_sides == ProductPrintSide::CODE_WIP_FRONT && !$presetSticker->front_size) {
                    throw new Exception('Preset Front not found', Response::HTTP_NOT_FOUND);
                }
                if ($itemBarcode->print_sides == ProductPrintSide::CODE_WIP_DIE_CUT && !$presetSticker->die_cut_size) {
                    throw new Exception('Preset Die cut not found', Response::HTTP_NOT_FOUND);
                }
                $labelPrintSide = $printSideSticker[$itemBarcode->print_sides]->code;
                $image = SaleOrderItemImage::findByOrderItemAndSide($itemBarcode->order_item_id, $labelPrintSide);
                if (!$image) {
                    throw new Exception('Image not found!', Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                $printSide = $itemBarcode->print_sides;
                $options[] = [
                    'label_id' => $itemBarcode->label_id,
                    'image_id' => $image->id,
                    'side' => $labelPrintSide,
                    'print_area_id' => $printSideSticker[$printSide]->id,
                    'barcode_id' => $itemBarcode->barcode_id,
                ];
            }
            $groupedOptions = collect($options)->groupBy('side');

            foreach ($groupedOptions as $keySide => $optionsBySide) {
                $printArea = $printSideSticker->firstWhere('code', $keySide);
                if (!$printArea) {
                    throw new Exception('Print area not found', Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                $dataBarcodePrinted = [
                    'quantity_input' => $optionsBySide->count(),
                    'quantity' => $optionsBySide->count(),
                    'product_id' => $product_id,
                    'employee_id' => $employee_id,
                    'warehouse_id' => $warehouse_id,
                    'user_id' => Auth::id(),
                    'convert_percent' => $optionsBySide->count(),
                    'print_method' => BarcodePrinted::METHOD_UV,
                    'created_at' => Carbon::now(),
                    'print_status' => BarcodePrinted::ACTIVE,
                    'convert_status' => BarcodePrinted::INACTIVE
                ];
                $barcodePrinted = BarcodePrinted::create($dataBarcodePrinted);
                $pdfConverted = PdfConverted::create([
                    'quantity_input' => $optionsBySide->count(),
                    'quantity' => $optionsBySide->count(),
                    'product_id' => $product_id,
                    'employee_convert_id' => $employee_id,
                    'warehouse_id' => $warehouse_id,
                    'user_id' => Auth::id(),
                    'convert_percent' => $optionsBySide->count(),
                    'print_method' => ProductStyle::METHOD_UV,
                    'created_at' => now(),
                    'download_status' => PdfConverted::INACTIVE,
                    'options' => $optionsBySide->toArray(),
                    'convert_status' => PdfConverted::INACTIVE,
                    'type' => ProductType::STICKER,
                    'code_wip' => $printArea->code_wip,
                ]);
                foreach ($optionsBySide as $option) {
                    SaleOrderItemBarcode::where('label_id', $option['label_id'])->update([
                        'employee_print_id' => $employee_id,
                        'barcode_printed_id' => $barcodePrinted->id,
                        'printed_at' => now(),
                    ]);
                    PdfConvertedItem::create([
                        'label_id' => $option['label_id'],
                        'pdf_converted_id' => $pdfConverted->id,
                        'barcode_id' => $option['barcode_id'],
                        'print_side' => $option['side'],
                        'print_method' => ProductStyle::METHOD_UV,
                    ]);
                }
                $order_ids = array_unique(SaleOrderItemBarcode::getArrayOrderIds($barcodePrinted->id));
                if (!empty($order_ids)) {
                    SaleOrder::changeStatusToInProduction($order_ids);
                    $printingRepository = new PrintingRepository();
                    foreach ($order_ids as $id) {
                        $printingRepository->updateOrderPrinted($id);
                    }
                }
            }
            DB::commit();

            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
