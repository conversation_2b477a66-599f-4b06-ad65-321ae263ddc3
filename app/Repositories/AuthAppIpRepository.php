<?php

namespace App\Repositories;

use App\Models\AppIps;
use App\Models\AppUsers;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Auth;

class AuthAppIpRepository
{
    public function authenticate($request)
    {
        $credentials = ['username' => $request['username'], 'password' => $request['password']];

        if (Auth::guard('employee')->attempt($credentials)) {
            $user = Auth::guard('employee')->user();
            if (!$user->is_active) {
                return response()->json(['error' => 'Your account is inactive. Please contact support.'], 401);
            }
        } else {
            return response()->json(['error' => 'Incorrect username or password. Please try again.'], 401);
        }

        $ipAddress = $request->header('CF-Connecting-IP') ?: $request->header('X-Forwarded-For') ?? '127.0.0.1';
        $payload = [
            'iss' => 'SwiftPod Authentication Service',
            'sub' => $user->id,
            'iat' => time(),
            'exp' => time() + 60 * 60 * 24 * 2,
        ];

        $token = JWT::encode($payload, env('JWT_SECRET'), 'HS256');

        handleJob(AppIps::JOB_INSERT_LOG_TO_DATABASE, [
            'ip_address' => $ipAddress,
            'country' => $request->header('CF-IPCountry') ?? null,
            'user' => $user->id
        ]);

        return response()->json([
            'access_token' => $token,
            'user' => ['user' => $user->username, 'ip' => $ipAddress],
        ]);
    }

    public function update($request)
    {
        try {
            if (!($token = $request->bearerToken())) {
                throw new Exception('Unauthorized');
            }

            $decoded = JWT::decode($token, new Key(env('JWT_SECRET'), 'HS256'));
            $user = AppUsers::find($decoded->sub);

            if (!$user) {
                throw new Exception('Unauthorized');
            }

            if ($user->password_changed_at && $decoded->iat < strtotime($user->password_changed_at)) {
                return response()->json(['message' => 'Password changed, please log in again'], 401);
            }

            $ipAddress = $request->header('CF-Connecting-IP') ?: $request->header('X-Forwarded-For') ?? '127.0.0.1';
            $country = $request->header('CF-IPCountry') ?? null;

            AppIps::logAccess($ipAddress, $country, $user->id);

            return response()->json(['ip_address' => $ipAddress, 'country' => $country, 'user' => $user->id]);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getMessage()], 401);
        }
    }

    public function list($data)
    {
        return AppIps::getList($data);
    }

    public function countIp()
    {
        return [
            'country' => AppIps::select('country')->whereNotNull('ip_address')->groupBy('country')->get(),
            'user' => AppIps::select('app_users.username', 'app_users.id')
                ->join('app_users', 'app_users.id', '=', 'app_ips.user_id')
                ->whereNotNull('app_ips.user_id')
                ->groupBy('app_users.id', 'app_users.username')
                ->get(),
        ];
    }

    public function updateIp($data)
    {
        $ips = AppIps::find($data['id']);
        if (!$ips) {
            return response()->json('Ip Not Found!', 422);
        }

        $ips->update(['is_allowed' => $data['is_allowed'], 'is_reviewed' => true]);

        return response()->json('Ip has been active');
    }

    public function reviewIp($data)
    {
        $ips = AppIps::find($data['id']);
        if (!$ips) {
            return response()->json('Ip Not Found!', 422);
        }

        $ips->update(['is_reviewed' => true]);

        return response()->json('Ip has been reviewed');
    }

    public function createUser($data)
    {
        $data['password'] = bcrypt($data['password']);

        return AppUsers::create($data);
    }

    public function updateUser($data)
    {
        $user = AppUsers::find($data['id']);
        if (!$user) {
            return response()->json('User Not Found!', 422);
        }

        if (!empty($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        } else {
            unset($data['password']);
        }

        $user->update($data);

        return response()->json('User updated successfully');
    }

    public function createIp($data)
    {
        $data['expired_at'] = isset($data['expired_at']) && !empty($data['expired_at'])
        ? Carbon::parse($data['expired_at'], 'America/Los_Angeles')->setTimezone('UTC')->format('Y-m-d H:i:s')
        : null;

        return AppIps::create($data);
    }

    public function manualUpdateIp($id, $data)
    {
        $ip = AppIps::find($id);
        if (!$ip) {
            return response()->json('IP Not Found!', 422);
        }

        $data['expired_at'] = isset($data['expired_at']) && !empty($data['expired_at'])
        ? Carbon::parse($data['expired_at'], 'America/Los_Angeles')->setTimezone('UTC')->format('Y-m-d H:i:s')
        : null;

        $ip->update($data);

        return response()->json('IP updated successfully');
    }

    public function deleteIp($id)
    {
        $ip = AppIps::find($id);
        if (!$ip) {
            return response()->json('IP Not Found!', 422);
        }

        $ip->delete();

        return response()->json('IP deleted successfully');
    }

    public function deleteUser($id)
    {
        $user = AppUsers::find($id);
        if (!$user) {
            return response()->json('User Not Found!', 422);
        }

        $user->delete();

        return response()->json('User deleted successfully');
    }

    public function listBypass($input = [])
    {
        $limit = !empty($input['limit']) ? $input['limit'] : 25;
        $query = User::with('warehouses', 'roles', 'department')
            ->where('user.is_bypass_restriction', true);
        if (!empty($input['keyword'])) {
            $query->where(function ($q) use ($input) {
                $q->where('username', 'like', '%' . $input['keyword'] . '%')
                    ->orWhere('email', 'like', '%' . $input['keyword'] . '%');
            });
        }

        return $query->paginate($limit);
    }

    public function addByPassUser($data)
    {
        return User::whereIn('id', $data['id'])->update(['is_bypass_restriction' => true]);
    }

public function disableBypass($id)
{
    $user = User::find($id);

    if (!$user) {
        return response()->json('User Not Found!', 422);
    }

    $user->is_bypass_restriction = false;
    $user->save();

    return response()->json('User restriction bypass has been disabled successfully');
}

public static function manualUpdate($data)
{
    $ip = AppIps::find($data['id']);

    if (!$ip) {
        return response()->json('Ip Not Found!', 422);
    }
    $data['expired_at'] = isset($data['expired_at']) && !empty($data['expired_at'])
    ? Carbon::parse($data['expired_at'], 'America/Los_Angeles')->setTimezone('UTC')->format('Y-m-d H:i:s')
    : null;

    $ip->update([
        'ip_address' => $data['ip_address'],
        'expired_at' => $data['expired_at'],
        'is_allowed' => $data['is_active'],
        'note' => $data['note'],
    ]);

    return response()->json('Ip has been updated successfully');
}

public function updateIpRestriction($data)
{
    $setting = Setting::where('label', Setting::IP_RESTRICTION)->first();

    if (!$setting) {
        return false;
    }

    $setting->value = $data['status'];
    $setting->save();

    return true;
}
}
