<?php

namespace App\Repositories;

use App\Models\ProductionReport;
use App\Models\ProductStyle;
use Carbon\Carbon;

class ProductionReportRepository extends CommonRepository
{
    const TEE = 'Tee';

    const FLEECE = 'Fleece';

    const MUGS = 'mugs';

    const DOG_LEASH = 'Dog leash';

    const ORNAMENT = 'Ornament';

    const PRODUCT_TYPE_REPORT = [self::TEE, self::FLEECE, self::MUGS, self::DOG_LEASH, self::ORNAMENT];

    public function __construct()
    {
        parent::__construct();
    }

    public function getReport($request)
    {
        $result = [
            [
                'product_type' => 'accessories',
                'quantity_pulled' => 0,
                'quantity_printed' => 0,
                'quantity_qc' => 0,
                'quantity_shipped' => 0,
                'quantity_folded' => 0,
            ],
            [
                'product_type' => 'apparel',
                'quantity_pulled' => 0,
                'quantity_printed' => 0,
                'quantity_qc' => 0,
                'quantity_shipped' => 0,
                'quantity_folded' => 0,
            ],
            [
                'product_type' => 'mug',
                'quantity_pulled' => 0,
                'quantity_printed' => 0,
                'quantity_qc' => 0,
                'quantity_shipped' => 0,
                'quantity_folded' => 0,
            ],
        ];

        $productionReport = ProductionReport::selectRaw("
                CASE
                    WHEN `type`='" . self::DOG_LEASH . "' OR `type`='" . self::ORNAMENT . "'
                        THEN 'accessories'
                    WHEN `type`='" . self::MUGS . "'
                        THEN 'mug'
                    WHEN `type`='" . self::TEE . "' OR `type`='" . self::FLEECE . "'
                        THEN 'apparel'
                END AS product_type,
                SUM(qty_pulled) AS quantity_pulled,
                SUM(qty_printed) AS quantity_printed,
                SUM(qty_qc) AS quantity_qc,
                SUM(qty_shipped) AS quantity_shipped,
                SUM(qty_folded) AS quantity_folded
                ")
            ->where('report_date', $request->date)
            ->where('warehouse_id', $request->warehouse)
            ->whereIn('type', self::PRODUCT_TYPE_REPORT)
            ->groupBy('product_type')
            ->get();

        foreach ($result as $key => $item) {
            $itemProductionReport = $productionReport->where('product_type', strtolower($item['product_type']))->first();
            if (!empty($itemProductionReport)) {
                $result[$key] = $itemProductionReport;
            }
        }

        return $result;
    }

    public function syncQantityData($startDate, $endDate)
    {
        $startDate = Carbon::parse($startDate)->startOfDay()->format('Y-m-d H:i:s');
        $endDate = Carbon::parse($endDate)->endOfDay()->format('Y-m-d H:i:s');
        $fieldUpdates = [
            'pulled_at' => 'qty_pulled',
            'printed_at' => 'qty_printed',
            'qc_at' => 'qty_qc',
            'shipped_at' => 'qty_shipped',
            'folded_at' => 'qty_folded',
        ];

        foreach ($fieldUpdates as $field => $fieldSynced) {
            $this->syncQantityDataByField($field, $fieldSynced, $startDate, $endDate);
        }
    }

    public function syncQantityDataByField($field, $fieldSynced, $startDate, $endDate)
    {
        setTimezone();
        $data = ProductStyle::selectRaw("
                date(sale_order_item_barcode.$field) as report_date,
                sale_order_item_barcode.warehouse_id,
                product_style.type,
                count(sale_order_item_barcode.id) as $fieldSynced
            ")
            ->join('sale_order_item', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->where('sale_order_item_barcode.is_deleted', false)
            ->whereIn('product_style.type', self::PRODUCT_TYPE_REPORT)
            ->where("sale_order_item_barcode.$field", '>=', $startDate)
            ->where("sale_order_item_barcode.$field", '<=', $endDate)
            ->groupBy('report_date')
            ->groupBy('warehouse_id')
            ->groupBy('type')
            ->get()
            ->toArray();

        ProductionReport::upsert($data, ['report_date', 'warehouse_id', 'type']);
    }
}
