<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderDuplicate;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderReprint;
use App\Models\Store;
use App\Models\Tag;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SaleOrderApiRepository extends CommonRepository
{
    public function sendRequest($url, $token, $data, $method = 'POST')
    {
        $curl = curl_init();
        $url = env('SALE_ORDER_API_URL') . $url;
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json',
                'Accept: application/json',
            ],
        ]);
        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function createManualOrder($input)
    {
        try {
            $store = Store::find($input['store_id']);
            $data = $input->all();

            if (!$store->token) {
                throw new Exception('Missing store API key.');
            }

            if ($store->isStoreSample()) {
                $orderItemExternalIds = $this->preprocessRequestSampleOrder($data);
            }
            $data['is_create_manual'] = 1;

            if (!empty(($input['line_items']))) {
                foreach ($input['line_items'] as $key => $item) {
                    if (!empty($item['print_files'])) {
                        foreach ($item['print_files'] as $index => $image) {
                            if (!empty($image['file'])) {
                                $filename = $item['order_item_id'] . '-' . 'print_files' . '-' . $image['key'] . '-' . rand() . ".{$image['file']->guessExtension()}";
                                $folderPath = SaleOrder::MANUAL_ORDER_FOLDER;
                                $path = Storage::disk('s3')->putFileAs($folderPath, $image['file'], $filename);
                                $data['line_items'][$key]['print_files'][$index]['url'] = env('AWS_S3_URL', '') . "/$path";
                                unset($data['line_items'][$key]['print_files'][$index]['file']);
                            }
                        }
                    }
                    if (!empty($item['preview_files'])) {
                        foreach ($item['preview_files'] as $index => $image) {
                            if (!empty($image['file'])) {
                                $filename = 'preview_files' . '-' . $image['key'] . '-' . rand() . ".{$image['file']->guessExtension()}";
                                $folderPath = SaleOrder::MANUAL_ORDER_FOLDER;
                                $path = Storage::disk('s3')->putFileAs($folderPath, $image['file'], $filename);
                                $data['line_items'][$key]['preview_files'][$index]['url'] = env('AWS_S3_URL', '') . "/$path";
                                unset($data['line_items'][$key]['preview_files'][$index]['file']);
                            }
                        }
                    }
                }
            }
            $response = $this->sendRequest('orders', $store->token, $data);

            if ($response->status && $store->isStoreSample()) {
                !empty($orderItemExternalIds) && $this->updateSampleOrderItem($store->id, $orderItemExternalIds);
                $this->attachTagSampleOrder($store->id, $response->data->order_id);
                $saleOrder = SaleOrder::where('encode_id', $response?->data?->id)->where('store_id', $store->id)->first();
                saleOrderHistory(auth()->user()['id'] ?? '',
                    $input['employee_id'] ?? '',
                    $saleOrder?->id,
                    SaleOrderHistory::MANUAL_CREATE_ORDER_TYPE,
                    'Manual order successfully created',
                );
            }
            //order duplicate
            if ($response->status && !empty($input['is_duplicate'])) {
                $orderParent = SaleOrder::find($input['parent_id']);
                $orderDuplicate = SaleOrder::where('encode_id', $response->data->id)
                    ->where('store_id', $input['store_id'])->first();
                SaleOrderDuplicate::create([
                    'order_id' => $orderDuplicate->id,
                    'parent_id' => $orderParent->id
                ]);
            }

            if ($response->status && !empty($input['is_reprint'])) {
                $orderParent = SaleOrder::find($input['parent_id']);
                DB::beginTransaction();
                //Todo : fix tam where theo store_id, con phai formatcode
                $orderReprint = SaleOrder::where('external_number', $response->data->order_id)
                    ->where('store_id', $input['store_id'])->first();

                if (empty($input['is_calculate_invoice'])) {
                    $orderReprint->calculated_at = now();
                    $orderReprint->order_total = 0;
                    $orderReprint->amount_paid = 0;
                    $orderReprint->shipping_calculate = 0;
                    $orderReprint->save();
                    SaleOrderItem::where('order_id', $orderReprint->id)->update([
                        'unit_price' => 0,
                        'blank_price' => 0,
                        'handling_fee' => 0,
                        'amount_paid' => 0,
                    ]);
                }

                SaleOrderReprint::create(
                    [
                        'order_id' => $orderReprint->id,
                        'parent_id' => $input['parent_id']
                    ],
                );
                SaleOrderHistory::create(
                    [
                        'user_id' => auth()->user()['id'] ?? '',
                        'employee_id' => $input['employee_id'] ?? '',
                        'order_id' => $orderReprint->id,
                        'type' => SaleOrder::REPRINT,
                        'message' => "Reprinted from order: $orderParent->external_number. Reason: " . $input['reason'] . (empty($input['is_calculate_invoice']) ? '' : '. Charge invoice for this order'),
                        'created_at' => Carbon::now()->toDateTimeString()
                    ],
                );
                SaleOrderHistory::create(
                    [
                        'user_id' => auth()->user()['id'] ?? '',
                        'employee_id' => $input['employee_id'] ?? '',
                        'order_id' => $input['parent_id'],
                        'type' => SaleOrder::REPRINT,
                        'message' => "Reprint Order: {$orderParent->external_number}. Reason: " . $input['reason'],
                        'created_at' => Carbon::now()->toDateTimeString()
                    ],
                );
                DB::commit();
            }

            if ($response->status && empty($input['is_reprint']) && !$store->isStoreSample()) {
                $saleOrder = SaleOrder::where('encode_id', $response?->data?->id)->where('store_id', $store->id)->first();
                saleOrderHistory(auth()->user()['id'] ?? '',
                    $input['employee_id'] ?? '',
                    $saleOrder?->id,
                    SaleOrderHistory::MANUAL_CREATE_ORDER_TYPE,
                    'Manual order successfully created');
            }

            return $response;
        } catch (Exception $exception) {
            DB::rollBack();

            return [
                'status' => false,
                'message' => $exception->getMessage()
            ];
        }
    }

    public function preprocessRequestSampleOrder(&$input): array
    {
        $orderItemIds = [];

        if (!empty($input['line_items'])) {
            foreach ($input['line_items'] as &$item) {
                if (empty($item['print_files'])) {
                    $item['print_files'] = [
                        [
                            'key' => 'front',
                            'url' => 'https://pod.com',
                        ]
                    ];
                    $orderItemIds[] = $item['order_item_id'];
                }
            }
        }

        return $orderItemIds;
    }

    public function updateSampleOrderItem($storeId, $orderItemExternalIds)
    {
        $orderItemIds = SaleOrderItem::whereIn('external_id', $orderItemExternalIds)
            ->where('store_id', $storeId)
            ->pluck('id');

        SaleOrderItem::whereIn('id', $orderItemIds)
            ->update([
                'options' => [],
                'print_side' => null,
                'print_sides' => null,
                'ink_color_status' => SaleOrderItem::DETECT_INK_COLOR_DONE,
                'ink_color_detected_at' => Carbon::now(),
            ]);
    }

    public function attachTagSampleOrder($storeId, $orderExternalNumber)
    {
        $order = SaleOrder::where('external_number', $orderExternalNumber)
            ->where('store_id', $storeId)
            ->first();
        $csoProductSkus = explode(',', env('SAMPLE_ORDER_CSO_PRODUCT_SKUS', 'CUSTNLXXX'));
        $sobProductSkus = explode(',', env('SAMPLE_ORDER_SOB_PRODUCT_SKUS', 'SSOB11BOX,SSNL11BOX'));
        $tags = $order->tag ? explode(',', $order->tag) : [];

        foreach ($order->items as $orderItem) {
            if (in_array($orderItem->product_sku, $csoProductSkus)) {
                $tags[] = Tag::TAG_SAMPLE_CUSTOM_ID;
            } elseif (in_array($orderItem->product_sku, $sobProductSkus)) {
                $tags[] = Tag::TAG_SAMPLE_BOX_ID;
            } else {
                $tags[] = Tag::TAG_SAMPLE_ID;
            }
        }

        $order->tag = implode(',', array_unique($tags));
        $order->save();
    }
}
