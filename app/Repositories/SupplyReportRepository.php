<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderItemImage;
use App\Models\SupplyInkReport;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupplyReportRepository extends CommonRepository
{
    const RATE_CONVERSION = 1000; // 1l = 1000cc

    public function getInkConsumption($year)
    {
        $colors = env('WAREHOUSE_COLOR_INK_CONSUMPTION', '#6092C0,#54B399,#D36086,#9170B8,#D4A274');
        $arrColors = explode(',', $colors);
        $dataReport = [];
        $isYearCurrent = $year == now()->format('Y');
        $warehouses = Warehouse::pluck('name', 'id')->toArray();

        for ($i = 1; $i <= 12; $i++) {
            if ($isYearCurrent) {
                $key = $i == 12 ? now()->format('Ym') : now()->startOfMonth()->subMonths(12 - $i)->format('Ym');
            } else {
                $key = $year . str_pad($i, 2, '0', STR_PAD_LEFT);
            }

            $items = [];
            $colorIndex = 0;

            foreach ($warehouses as $id => $name) {
                $items[$id] = [
                    'color' => !empty($arrColors[$colorIndex]) ? $arrColors[$colorIndex] : '#8C92AC',
                    'name' => $name,
                    'qty_ink_color' => 0,
                    'qty_ink_white' => 0,
                ];
                $colorIndex++;
            }

            $dataReport[$key] = [
                'month' => Carbon::createFromFormat('Ymd', "{$key}01")->format('Y-m'),
                'warehouses' => $items
            ];
        }

        $data = SupplyInkReport::query();

        if ($isYearCurrent) {
            $dateCondition = now()->startOfMonth()->subMonths(12)->format('Ym');
            $data->where('month_report', '>=', (int) $dateCondition);
        } else {
            $start = Carbon::createFromFormat('Y', $year)->firstOfYear()->format('Ym');
            $end = Carbon::createFromFormat('Y', $year)->lastOfYear()->format('Ym');
            $data->where('month_report', '>=', (int) $start);
            $data->where('month_report', '<=', (int) $end);
        }

        $data = $data->get();

        foreach ($data as $item) {
            if (array_key_exists($item->month_report, $dataReport)) {
                $moth = &$dataReport[$item->month_report];
                $warehouse = &$moth['warehouses'][$item->warehouse_id];
                $warehouse['qty_ink_color'] += round($item->ink_color_cc / self::RATE_CONVERSION, 3);
                $warehouse['qty_ink_white'] += round($item->ink_white_cc / self::RATE_CONVERSION, 3);
            }
        }

        $dataReport = array_map(function ($item) {
            $item['warehouses'] = array_values($item['warehouses']);

            return $item;
        }, $dataReport);

        return [
            'unit' => 'Liters',
            'data_report' => array_values($dataReport),
        ];
    }

    public function getInkConsumptionForecast($params)
    {
        $unitConversion = 18; //1 tank = 18 litters
        $unitType = $params['unit_type'] ?? '';
        $now = now();
        $dataReport = [];
        $buildMonthReport = [];
        $dateStart = empty($params['year']) ? now()->startOfMonth()->subMonths(5) : Carbon::createFromFormat('Y', $params['year'])->firstOfYear();
        $dateEnd = $dateStart->clone()->addMonths(12);
        $isInkColor = empty($params['color_type']) || $params['color_type'] == SupplyInkReport::INK_COLOR;
        $isInkWhite = empty($params['color_type']) || $params['color_type'] == SupplyInkReport::INK_WHITE;
        $data = Warehouse::with(['supplyInkReports' => function ($query) use ($dateStart, $dateEnd) {
            if ($dateStart >= now() && $dateStart <= now()->startOfMonth()->addMonths(6)) {
                $query->where(function ($q) use ($dateStart) {
                    $q->where('month_report', now()->clone()->startOfMonth()->subMonths(13)->format('Ym'))
                        ->orWhere('month_report', '>=', (int) $dateStart->clone()->subMonths(13)->format('Ym'));
                });
            } else {
                $query->where('month_report', '>=', (int) $dateStart->clone()->subMonths(13)->format('Ym'));
            }

            $query->where('month_report', '<=', (int) $dateEnd->format('Ym'));
        }]);

        if (!empty($params['warehouse_id'])) {
            $data->where('id', $params['warehouse_id']);
        }

        for ($i = 0; $i < 12; $i++) {
            $month = ($i == 0) ? $dateStart : $dateStart->clone()->addMonths($i);
            $buildMonthReport[$month->format('Ym')] = $month->format('M Y');
        }

        foreach ($data->get() as $warehouse) {
            $_warehouse = [
                'warehouse_id' => $warehouse->id,
                'warehouse' => $warehouse->name,
            ];

            $isInkColor && $inkColorRow = $_warehouse + [
                'ink' => SupplyInkReport::INK_COLOR,
                'ink_label' => SupplyInkReport::INK_COLOR_TYPES[SupplyInkReport::INK_COLOR],
                'months' => [],
            ];

            $isInkWhite && $inkWhiteRow = $_warehouse + [
                'ink' => SupplyInkReport::INK_WHITE,
                'ink_label' => SupplyInkReport::INK_COLOR_TYPES[SupplyInkReport::INK_WHITE],
                'months' => [],
            ];

            foreach ($buildMonthReport as $key => $value) {
                $month = Carbon::createFromFormat('Ymd', "{$key}01")->startOfMonth();
                $reportMonthCurrent = $warehouse->supplyInkReports
                    ->where('month_report', $key)
                    ->first();
                $lastMonth = ($month >= now() && $month <= now()->startOfMonth()->addMonths(6)) ? $now->clone()->startOfMonth()->subMonth() : $month->clone()->subMonth();
                $reportLastMonth = $warehouse->supplyInkReports
                    ->where('month_report', $lastMonth->format('Ym'))
                    ->first();
                $reportLastYear = $warehouse->supplyInkReports
                    ->where('month_report', $month->clone()->subMonths(12)->format('Ym'))
                    ->first();
                $lastMonthOfLastYear = ($month >= now() && $month <= now()->startOfMonth()->addMonths(6)) ? $now->clone()->startOfMonth()->subMonths(13) : $month->clone()->subMonths(13);
                $reportLastMonthOfLastYear = $warehouse->supplyInkReports
                    ->where('month_report', $lastMonthOfLastYear->format('Ym'))
                    ->first();
                $reportMonthCurrent = $reportMonthCurrent ? $reportMonthCurrent->toArray() : [];
                $reportLastMonth = $reportLastMonth ? $reportLastMonth->toArray() : [];
                $reportLastYear = $reportLastYear ? $reportLastYear->toArray() : [];
                $reportLastMonthOfLastYear = $reportLastMonthOfLastYear ? $reportLastMonthOfLastYear->toArray() : [];

                if ($isInkColor) {
                    $colorRows = $this->getForecastAccuracy($reportMonthCurrent, $reportLastMonth, $reportLastYear, $reportLastMonthOfLastYear, SupplyInkReport::INK_COLOR);
                    $colorRows = array_map(function ($item) use ($unitType, $unitConversion) {
                        if (is_numeric($item) && $unitType == 'tank') {
                            $item = ceil($item / $unitConversion);
                        }

                        is_numeric($item) && $item = round($item);

                        if ($item == -0) {
                            return 0;
                        }

                        return $item;
                    }, $colorRows);

                    $inkColorRow['months'][$key] = $colorRows;
                }

                if ($isInkWhite) {
                    $colorRows = $this->getForecastAccuracy($reportMonthCurrent, $reportLastMonth, $reportLastYear, $reportLastMonthOfLastYear, SupplyInkReport::INK_WHITE);
                    $colorRows = array_map(function ($item) use ($unitType, $unitConversion) {
                        if (is_numeric($item) && $unitType == 'tank') {
                            $item = ceil($item / $unitConversion);
                        }

                        is_numeric($item) && $item = round($item);

                        if ($item == -0) {
                            return 0;
                        }

                        return $item;
                    }, $colorRows);

                    $inkWhiteRow['months'][$key] = $colorRows;
                }
            }

            $isInkColor && $dataReport[] = $inkColorRow;
            $isInkWhite && $dataReport[] = $inkWhiteRow;
        }

        return [
            'months' => $buildMonthReport,
            'dataReport' => $dataReport
        ];
    }

    public function getForecastAccuracy($reportMonthCurrent, $reportLastMonth, $reportLastYear, $reportLastMonthOfLastYear, $type)
    {
        $growthRate = null;

        if (isset($reportLastMonth['order_quantity']) && !empty($reportLastMonthOfLastYear['order_quantity'])) {
            $growthRate = $reportLastMonth['order_quantity'] / $reportLastMonthOfLastYear['order_quantity'];
        }

        $foreCast = isset($reportLastYear[$type]) && $growthRate !== null ? $reportLastYear[$type] / self::RATE_CONVERSION * $growthRate : null;
        $actual = isset($reportMonthCurrent[$type]) ? $reportMonthCurrent[$type] / self::RATE_CONVERSION : null;

        return [
            'forecast' => $foreCast !== null ? round($foreCast, 3) : '-',
            'actual' => $actual !== null ? round($reportMonthCurrent[$type] / self::RATE_CONVERSION, 3) : '-',
            'variance' => $foreCast !== null && $actual !== null ? round($foreCast - ($actual ?? 0), 3) : '-',
        ];
    }

    public function calculateSupplyInkByOrderDate($start = null, $end = null)
    {
        if ($start) {
            $start = Carbon::createFromFormat('Y-m-d', $start)->format('Y-m-d');
        } else {
            $start = now()->startOfMonth()->subMonth()->format('Y-m-d');
        }

        if ($end) {
            $end = Carbon::createFromFormat('Y-m-d', $start)->format('Y-m-d');
        } else {
            $end = now()->endOfMonth()->format('Y-m-d');
        }

        $minIdSaleOrderItemImage = SaleOrderItemImage::selectRaw('min(id) as `id`')
            ->where('created_at', '>=', $start)
            ->first();
        $maxIdSaleOrderItemImage = SaleOrderItemImage::selectRaw('max(id) as `id`')
            ->where('created_at', '<=', $end)
            ->first();
        $data = SaleOrderItemImage::selectRaw(
            'DATE_FORMAT(created_at, "%Y%m") as `month_report`,
            warehouse_id,
            SUM(ink_color_cc) as `ink_color_cc`,
            SUM(ink_white_cc) as `ink_white_cc`',
        );

        if (!empty($minIdSaleOrderItemImage->id)) {
            $data->where('id', '>=', $minIdSaleOrderItemImage->id);
        }

        if (!empty($maxIdSaleOrderItemImage->id)) {
            $data->where('id', '<=', $maxIdSaleOrderItemImage->id);
        }

        $data = $data->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->groupBy('month_report')
            ->groupBy('warehouse_id')
            ->get()
            ->toArray();

        DB::connection()->enableQueryLog();
        SupplyInkReport::upsert($data, ['month_report', 'warehouse_id'], ['ink_color_cc', 'ink_white_cc']);
        $queries = DB::getQueryLog();
        Log::info('SupplyReportRepository.calculateSupplyInkByOrderDate', ['queries' => $queries]);
        $this->calculateSupplyInkTotalSale($start, $end);
    }

    public function calculateSupplyInkTotalSale($start, $end)
    {
        $minIdSaleOrder = SaleOrder::selectRaw('min(id) as `id`')
            ->where('created_at', '>=', $start)
            ->first();
        $maxIdSaleOrder = SaleOrder::selectRaw('max(id) as `id`')
            ->where('created_at', '<=', $end)
            ->first();
        $data = SaleOrder::query()
            ->selectRaw(
                'DATE_FORMAT(sale_order.created_at, "%Y%m") as `month_report`,
            sale_order.warehouse_id,
            COALESCE(SUM(sale_order_item.quantity), 0) as `order_quantity`',
            )
            ->leftJoin('sale_order_item', 'sale_order.id', '=', 'sale_order_item.order_id');

        if (!empty($minIdSaleOrder->id)) {
            $data->where('sale_order.id', '>=', $minIdSaleOrder->id);
        }

        if (!empty($maxIdSaleOrder->id)) {
            $data->where('sale_order.id', '<=', $maxIdSaleOrder->id);
        }

        $data = $data->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order.created_at', '>=', $start)
            ->where('sale_order.created_at', '<=', $end)
            ->groupBy('month_report')
            ->groupBy('sale_order.warehouse_id')
            ->get()
            ->whereNotNull('warehouse_id')
            ->toArray();

        if (!empty($data)) {
            DB::connection()->enableQueryLog();
            SupplyInkReport::upsert($data, ['month_report', 'warehouse_id'], ['order_quantity']);
            $queries = DB::getQueryLog();
            Log::info('SupplyReportRepository.calculateSupplyInkTotalSale', ['queries' => $queries]);
        }
    }
}
