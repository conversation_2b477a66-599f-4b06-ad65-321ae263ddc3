<?php

namespace App\Repositories;

use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyInventory;
use App\Models\SupplyInventoryAddition;
use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyQuantity;
use App\Models\TimeTracking;
use App\Repositories\Contracts\SupplyAdditionRepositoryInterface;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SupplyAdditionRepository extends CommonRepository implements SupplyAdditionRepositoryInterface
{
    public function __construct(protected SupplyLocationRepository $supplyLocationRepository)
    {
        parent::__construct();
    }

    public function fetchAll($request)
    {
        $query = SupplyInventoryAddition::with(
            [
                'supplyPurchaseOrder:id,po_number,invoice_number,order_number',
                'user:id,username',
                'supply:id,name,sku',
                'employee:id,name,code',
                'location:id,barcode',
                'box:id,barcode'
            ])
            ->search($request);
        if ($request->has('export')) {
            if (!empty($request['start_date']) && !empty($request['end_date'])) {
                $from = Carbon::parse($request['start_date'])->startOfDay();
                $to = Carbon::parse($request['end_date'])->endOfDay();
                $query->whereBetween('created_at', [$from, $to]);
            }

            return $query->get();
        }

        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function create($request)
    {
        try {
            if (!empty($request->box_id) && $this->isExistSupplyBox($request['box_id'])) {
                return response()->json([
                    'box_id' => ['The box ID has already been taken.']
                ], 422);
            }

            if (!empty($request->box_id) && $this->isSupplyBoxDeleted($request['box_id'])) {
                return response()->json([
                    'box_id' => ['The box ID is deleted. Please create a new one.']
                ], 422);
            }

            $poItem = SupplyPurchaseOrderItem::with(['po', 'supply'])
                ->whereHas('po', function ($query) use ($request) {
                    $query->where('id', $request->po_id);
                })
                ->where('supply_id', $request->supply_id)
                ->first();
            if (!$poItem) {
                return response()->json([
                    'supply_id' => ['supply is not belong PO']
                ], 422);
            }
            if (in_array($poItem->po->order_status, [SupplyPurchaseOrder::COMPLETED_STATUS, SupplyPurchaseOrder::CANCELLED_STATUS])) {
                return response()->json([
                    'purchase_order' => ['The purchase order has been completed or canceled and cannot be updated!']
                ], 422);
            }

            $quantityMax = $poItem->quantity - $poItem->quantity_onhand;
            if ($request->quantity > $quantityMax) {
                return response()->json([
                    'quantity' => ['The quantity may not be greater than ' . $quantityMax . '.']
                ], 422);
            }
            DB::beginTransaction();
            $poItem->quantity_onhand += $request->quantity;
            $poItem->received_status = $poItem->quantity_onhand !== $poItem->quantity
                ? SupplyPurchaseOrderItem::PARTIAL_RECEIVED_STATUS
                : SupplyPurchaseOrderItem::RECEIVED_STATUS;
            $poItem->save();
            $po = $poItem->po;
            $po->order_status = SupplyPurchaseOrderItem::where('po_id', $po->id)
                ->where(function ($query) {
                    $query->where('received_status', '<>', SupplyPurchaseOrderItem::RECEIVED_STATUS)
                        ->orWhereNull('received_status');
                })
                ->exists()
                ? SupplyPurchaseOrder::PARTIAL_RECEIVED_STATUS
                : SupplyPurchaseOrder::COMPLETED_STATUS;
            $po->save();
            // Create box
            $supplyBox = collect();
            if (!empty($request['box_id'])) {
                $inputBox['barcode'] = $request->box_id;
                $inputBox['warehouse_id'] = config('jwt.warehouse_id');
                $inputBox['supply_id'] = $request->supply_id;
                $inputBox['quantity'] = $request->quantity;
                $inputBox['location_id'] = $request->location_id;
                $supplyBox = SupplyBox::create($inputBox);
            }
            $supplyInventoryAddition = SupplyInventoryAddition::create([
                'supply_id' => $request->supply_id,
                'sku' => $request->sku,
                'po_id' => $request->po_id,
                'location_id' => $request->location_id,
                'box_id' => $supplyBox?->id ?? null,
                'quantity' => $request->quantity,
                'employee_id' => $request->employee_id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'user_id' => auth()->user()->id,
                'is_deleted' => SupplyInventoryAddition::IS_NOT_DELETED,
            ]);
            $supplyInventory = new SupplyInventory([
                'type' => SupplyInventory::TYPE_INPUT,
                'supply_id' => $request->supply_id,
                'object_id' => $supplyInventoryAddition->id,
                'object_name' => SupplyInventory::OBJECT_ADDITION,
                'quantity' => $request->quantity,
                'warehouse_id' => config('jwt.warehouse_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'user_id' => auth()->user()->id,
                'is_deleted' => SupplyInventory::IS_NOT_DELETED,
            ]);
            $supplyInventory->save();

            $this->supplyLocationRepository->updateQuantity($request->location_id, $request->supply_id, $request->quantity);

            $supplyQuantity = SupplyQuantity::query()
                ->where('supply_id', $request->supply_id)
                ->where('warehouse_id', config('jwt.warehouse_id'))
                ->first();
            if ($supplyQuantity) {
                $supplyQuantity->incoming_stock -= $request->quantity;
                $supplyQuantity->quantity += $request->quantity;
                $supplyQuantity->save();
            }
            $timeTracking = TimeTracking::find($request->id_time_checking);
            $timeTracking->quantity += $request->quantity;
            $timeTracking->end_time =  date('Y-m-d H:i:s');
            $timeTracking->save();
            DB::commit();

            return response()->json($supplyInventoryAddition);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function isExistSupplyBox($boxId)
    {
        return SupplyBox::where('barcode', $boxId)
            ->whereNotNull('location_id')
            ->exists();
    }

    public function isSupplyBoxDeleted($boxId)
    {
        return SupplyBox::where('barcode', $boxId)
            ->whereNotNull('location_id')
            ->onlyTrashed()
            ->exists();
    }

    public function revert($id)
    {
        try {
            $supplyInventoryAddition = SupplyInventoryAddition::with('supplyPurchaseOrder')
                ->where('is_deleted', SupplyInventoryAddition::IS_NOT_DELETED)->find($id);

            if (!$supplyInventoryAddition || !$supplyInventoryAddition->supplyPurchaseOrder) {
                return response()->json([
                    'quantity' => ['Supply addition invalid']
                ], 422);
            }
            $supplyPurchaseOrderItem = $supplyInventoryAddition->supplyPurchaseOrderItem($supplyInventoryAddition->supply_id)->first();
            if (!$supplyPurchaseOrderItem) {
                return response()->json([
                    'quantity' => ['Supply purchase order item invalid']
                ], 422);
            }
            DB::beginTransaction();
            $supplyBox = SupplyBox::find($supplyInventoryAddition->box_id);
            if ($supplyBox) {
                $supplyBox->delete();
            }
            $supplyPurchaseOrderItem->quantity_onhand -= $supplyInventoryAddition->quantity;
            $supplyPurchaseOrderItem->received_status = $supplyPurchaseOrderItem->quantity_onhand !== $supplyPurchaseOrderItem->quantity
                ? ($supplyPurchaseOrderItem->quantity_onhand === 0 ? null : SupplyPurchaseOrderItem::PARTIAL_RECEIVED_STATUS)
                : SupplyPurchaseOrderItem::RECEIVED_STATUS;

            $this->supplyLocationRepository->updateQuantity($supplyInventoryAddition->location_id, $supplyInventoryAddition->supply_id, $supplyInventoryAddition->quantity * -1);

            $supplyPurchaseOrderItem->save();
            $supplyPurchaseOrder = $supplyInventoryAddition->supplyPurchaseOrder;
            $supplyPurchaseOrder->order_status = SupplyPurchaseOrderItem::where('po_id', $supplyPurchaseOrder->id)
                ->whereNotNull('received_status')
                ->exists()
                ? SupplyPurchaseOrder::PARTIAL_RECEIVED_STATUS
                : SupplyPurchaseOrder::SHIPPED_STATUS;
            $supplyPurchaseOrder->save();
            $supplyInventoryAddition->is_deleted = 1;
            $supplyInventoryAddition->save();
            $supplyInventory = SupplyInventory::where('object_name', SupplyInventory::OBJECT_ADDITION)
                ->where('object_id', $supplyInventoryAddition->id)
                ->first();
            $supplyInventory->is_deleted = 1;
            $supplyInventory->save();

            $supplyQuantity = SupplyQuantity::query()
                ->where('supply_id', $supplyInventoryAddition->supply_id)
                ->where('warehouse_id', config('jwt.warehouse_id'))
                ->first();
            if ($supplyQuantity) {
                $supplyQuantity->incoming_stock += $supplyInventoryAddition->quantity;
                $supplyQuantity->quantity -= $supplyInventoryAddition->quantity;
                $supplyQuantity->save();
            }
            DB::commit();

            return response()->json($supplyInventoryAddition);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function fetch($id)
    {
        return Supply::with(['category:id,name', 'unit:id,name', 'brand:id,name'])->find($id);
    }

    public function update($id, $dataUpdate)
    {
        $supplyUnit = Supply::find($id);
        if (!$supplyUnit) {
            return null;
        }
        $supplyUnit->name = $dataUpdate['name'];
        $supplyUnit->sku = $dataUpdate['sku'];
        $supplyUnit->category_id = $dataUpdate['category_id'];
        $supplyUnit->brand_id = $dataUpdate['brand_id'];
        $supplyUnit->unit_id = $dataUpdate['unit_id'];
        $supplyUnit->save();

        return $supplyUnit;
    }

    public function delete($id)
    {
        return Supply::where('id', $id)->delete();
    }
}
