<?php

namespace App\Repositories;

use App\Models\UniversalReportCategory;
use App\Models\UniversalReportColumn;
use App\Models\UniversalReportSetting;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class UniversalReportRepository extends CommonRepository
{
    public function getList($request)
    {
        $query = UniversalReportTemplate::with(['tags', 'columns']);

        if (!empty($request->name)) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if (!empty($request->category_ids)) {
            $query->whereIn('report_category_id', explode(',', $request->category_ids));
        }

        return $query->paginate($request->limit ?? 25);
    }

    public function show(UniversalReportTemplate $template, $request)
    {
        $template->load([
            'tags',
            'columns',
            'settingDefault.settingExports',
            'settingCustom.settingExports',
        ]);

        if (!empty($request->with_all_columns)) {
            $template->setRelation('column_configuration', $template->columnConfiguration());
        }

        return $template;
    }

    public function create($params)
    {
        try {
            DB::beginTransaction();

            $template = UniversalReportTemplate::create($params['template']);

            /** Attach Tags */
            $requestTags = collect($params['tags'] ?? [])->map(function ($tag) use ($template) {
                $tag['code'] = trim($tag['code']);
                $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '\s*\]\]/', $template->query, $matches);

                return $tag;
            });
            $template->tags()->createMany($requestTags);

            /** Insert Columns */
            $columnSelected = $this->getColumnFromQuery($template->query);
            $template->columns()->createMany($columnSelected);

            DB::commit();

            return $template->load([
                'tags',
                'columns',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function update(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $template->update($params['template']);

            /** Attach Tags */
            $requestTags = collect($params['tags'] ?? [])->mapToGroups(function ($tag) use ($template) {
                $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '\s*\]\]/', $template->query, $matches);

                return [isset($tag['id']) ? 'update' : 'insert' => $tag];
            });

            $tagNeedUpdate = $requestTags->get('update', collect());
            $template->tags()->whereNotIn('id', $tagNeedUpdate->pluck('id'))->delete();
            $template->tags()->createMany($requestTags->get('insert', collect())->toArray());
            foreach ($tagNeedUpdate as $itemUpdate) {
                UniversalReportTag::where('id', $itemUpdate['id'])->update([
                    'is_used' => $itemUpdate['is_used'],
                    'is_required' => $itemUpdate['is_required'] ?? false,
                    'code' => $itemUpdate['code'],
                    'label' => $itemUpdate['label'] ?? null,
                    'value' => $itemUpdate['value'] ?? null,
                    'value_type' => $itemUpdate['value_type'],
                    'placeholder_1' => $itemUpdate['placeholder_1'] ?? null,
                    'placeholder_2' => $itemUpdate['placeholder_2'] ?? null,
                ]);
            }

            /** Insert Columns */
            $columnSelected = $this->getColumnFromQuery($template->query);
            $template->columns()->delete();
            $template->columns()->createMany($columnSelected);

            DB::commit();

            return $template->load([
                'tags',
                'columns',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    private function getColumnFromQuery(string $query): array
    {
        if (!preg_match_all('/(?i)\{\{\s*([^\[\]]+?)(?:\s+as\s+(.*?))?\s*\}\}/', $query, $matches)) {
            return [];
        }

        $columnSelected = [];
        [$original, $columns, $alias] = $matches;

        foreach ($columns as $index => $selectItem) {
            if (preg_match('/[`]?([\w]+)[`]?\s*\.\s*\*/', $selectItem, $columnMatch)) {
                $tableAlias = $columnMatch[1];

                if (Schema::hasTable($tableAlias)) {
                    $columnSelected[] = [
                        'original' => $original[$index],
                        'table' => $tableAlias,
                        'alias' => $alias[$index] ?? null
                    ];

                    continue;
                }

                $regexSelectAll = '/(?i)(?:from|join)\s+[\r\n\s]*[`"]?([\w.]+)[`"]?\s+(?:as\s+)?[`"]?' . $tableAlias . '[`"]?/';
                if (!Schema::hasTable($tableAlias) && preg_match($regexSelectAll, $query, $tableMatch)) {
                    if (!Schema::hasTable($tableMatch[1])) {
                        continue;
                    }

                    $columnSelected[] = [
                        'original' => $original[$index],
                        'table' => $tableMatch[1],
                        'alias' => $tableAlias
                    ];

                    continue;
                }
            }

            $columnSelected[] = [
                'original' => $original[$index],
                'column' => $selectItem,
                'alias' => $alias[$index] ?? null
            ];
        }

        return $columnSelected;
    }

    public function delete(UniversalReportTemplate $template)
    {
        try {
            DB::beginTransaction();

            $template->tags()->delete();
            $template->columns()->delete();
            UniversalReportSetting::where('report_template_id', $template->id)->delete();

            $template->delete();

            DB::commit();

            return $this->successResponse(
                'Template deleted successfully.',
            );
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function getCategories($request)
    {
        $query = UniversalReportCategory::query();

        if (!empty($request->name)) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        return $query->get();
    }

    public function duplicate(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $newTemplate = $template->replicate();
            $newTemplate->name = $newTemplate->name . ' (Copy)';

            if (isset($params['name'])) {
                $newTemplate->name = $params['name'];
            }

            if (isset($params['description'])) {
                $newTemplate->description = $params['description'];
            }

            $newTemplate->save();

            $tags = $template->tags->map(function ($tag) {
                return $tag->replicate();
            });
            $newTemplate->tags()->saveMany($tags);

            $columns = $template->columns->map(function ($column) {
                return $column->replicate();
            });
            $newTemplate->columns()->saveMany($columns);

            DB::commit();

            return $newTemplate->load(['tags', 'columns']);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function previewTemplate(UniversalReportTemplate $template, array $params)
    {
        try {
            $sql = $template->query;
            $template->load([
                'tags' => fn ($query) => $query->where('is_used', true),
                'columns',
                'settingDefault.settingExports',
                'settingCustom.settingExports',
            ]);

            try {
                /** Append column selected */
                $sql = $this->handleAppendTemplateColumn($template->columns, $sql, $params);

                /** Append tags */
                $sql = $this->handleAppendTemplateTag($template->tags, $sql, $params);

                Log::channel('universal_report')->info('Executing SQL query', [
                    'sql' => $sql,
                    'template_id' => $template->id,
                    'payload' => $params,
                ]);

                $data = DB::connection('mysql_report')->select($sql);

                return $data;
            } catch (QueryException $queryException) {
                Log::channel('universal_report')->error('Error executing SQL query', [
                    'sql' => $sql,
                    'template_id' => $template->id,
                    'error' => $queryException->getMessage(),
                    'payload' => $params,
                ]);

                return [];
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function revertTemplate(UniversalReportTemplate $template)
    {
        if (empty($template->settingCustom)) {
            return [];
        }

        try {
            DB::beginTransaction();
            $template->settingCustom->settingExports()->delete();
            $template->settingCustom->delete();
            DB::commit();

            $template->load([
                'tags',
                'columns',
                'settingDefault.settingExports',
                'settingCustom.settingExports',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function getTags(UniversalReportTemplate $template, UniversalReportTag $tag, Request $request)
    {
        try {
            $limit = min($request->limit ?? 25, 100);
            $offset = $request->offset ?? 0;

            switch ($tag->type) {
                case UniversalReportTag::TYPE_SINGLE_SELECT:
                case UniversalReportTag::TYPE_MULTI_SELECT:
                    if ($tag->value_type === UniversalReportTag::VALUE_TYPE_SQL) {
                        $sql = $this->appendOuterLimitOffset($tag->value, $limit, $offset);
                        $sql = $this->appendFilterByValue($sql, $request->keyword ?? null);

                        try {
                            $options = DB::connection('mysql_report')->select($sql);
                            $options = collect($options)->map(function ($option) {
                                return array_filter([
                                    'value' => $option->value ?? null,
                                    'text' => $option->key ?? $option->text ?? null,
                                ]);
                            })->filter();
                        } catch (QueryException $queryException) {
                            $options = [];
                        }
                    }
                    break;

                default:
                    break;
            }

            $tag->setRelation('options', collect($options ?? []));

            return $tag;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function updateOrCreateSettingExport(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $isDefault = isset($params['is_default']) && $params['is_default'];

            if ($isDefault) {
                $settingDefault = UniversalReportSetting::where('report_template_id', $template->id)
                    ->whereNull('user_id')
                    ->first();

                if (isset($settingDefault)) {
                    $settingDefault->settingExports()->delete();
                    $settingDefault->delete();
                }
            }

            $setting = UniversalReportSetting::updateOrCreate(
                [
                    'report_template_id' => $template->id,
                    'user_id' => !$isDefault ? auth()->id() : null,
                ],
                [
                    'is_default' => $params['is_default'] ?? false,
                    'export_status' => UniversalReportSetting::EXPORT_STATUS_NONE,
                    'export_status' => UniversalReportSetting::EXPORT_STATUS_NONE,
                    'file_path' => null,
                    'error_message' => null,
                    'download_at' => null,
                ],
            );

            if (!empty($params['columns'])) {
                $columns = collect($params['columns'])->map(function ($column) {
                    return [
                        'model_type' => UniversalReportColumn::class,
                        'model_key' => $column['name'],
                        'value' => $column['alias']
                    ];
                })->toArray();
            }

            if (!empty($params['tags'])) {
                $exportItems = [];
                $existTags = UniversalReportTag::whereIn('id', array_column($params['tags'], 'id'))->get();

                foreach ($params['tags'] as $tag) {
                    $totalValue = count($tag['value'] ?? []);
                    $currentTag = $existTags->firstWhere('id', $tag['id']);

                    foreach ($tag['value'] as $index => $tagValue) {
                        $exportItem = [
                            'model_id' => $tag['id'],
                            'model_type' => UniversalReportTag::class,
                            'value' => $tagValue,
                        ];

                        if ($currentTag->type === UniversalReportTag::TYPE_RANGE_DATE && $totalValue === 2) {
                            $exportItem['model_key'] = $index === 0 ? 'start_date' : 'end_date';
                        }

                        $exportItems[] = $exportItem;
                    }
                }
            }

            $setting->settingExports()->delete();
            $setting->settingExports()->createMany([
                ...$columns ?? [],
                ...$exportItems ?? [],
            ]);

            DB::commit();

            return $setting->load([
                'settingColumns',
                'settingTags',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    private function appendOuterLimitOffset(string $sql, int $limit = 100, int $offset = 0): string
    {
        $sqlTrimmed = trim($sql);
        $endsWithSemicolon = str_ends_with($sqlTrimmed, ';');

        if ($endsWithSemicolon) {
            $sqlTrimmed = rtrim($sqlTrimmed, ';');
        }

        $normalizedSql = preg_replace('/\s+/', ' ', $sqlTrimmed);

        $tokens = preg_split('/\s+/', $normalizedSql);
        $level = 0;
        $hasOuterLimitOrOffset = false;

        foreach ($tokens as $token) {
            $tokenLower = strtolower($token);
            $level += substr_count($token, '(') - substr_count($token, ')');

            if ($level === 0 && ($tokenLower === 'limit' || $tokenLower === 'offset')) {
                $hasOuterLimitOrOffset = true;
                break;
            }
        }

        if ($hasOuterLimitOrOffset) {
            return $sql;
        }

        $sqlTrimmed .= " LIMIT $limit OFFSET $offset";

        return $sqlTrimmed . ($endsWithSemicolon ? ';' : '');
    }

    private function appendFilterByValue(string $sql, ?string $value, ?string $alias = null): string
    {
        if (!preg_match('/\{\{(.*?)\}\}/', $sql, $filterMatch)) {
            return $sql;
        }

        [$placeholderExpr, $filterTemplate] = $filterMatch;

        $filterClause = null;
        if (!empty($value) && preg_match('/\[\[.*?\]\]/', $filterTemplate, $placeholderMatch)) {
            if (empty($alias) || (isset($alias) && $alias === $placeholderMatch[0])) {
                $filterClause = str_replace($placeholderMatch[0], $value, $filterTemplate);
            }
        }

        return str_replace($placeholderExpr, $filterClause, $sql);
    }

    private function handleAppendTemplateColumn($columns, string &$sql, array $params)
    {
        $columnSelected = collect($params['columns'])
            ->map(function ($item) {
                $alias = isset($item['alias']) ? " AS `{$item['alias']}`" : '';

                return "`{$item['name']}`{$alias}";
            })
            ->join(',' . PHP_EOL);

        foreach ($columns as $index => $column) {
            if ($index === 0) {
                $sql = str_replace($column['original'], $columnSelected, $sql);

                continue;
            }
            $sql = str_replace($column['original'], '', $sql);
        }

        return $sql;
    }

    private function handleAppendTemplateTag($tags, string &$sql, array $params)
    {
        $tagSelected = [];
        foreach ($tags as $tag) {
            $requestTag = array_filter($params['tags'], fn ($item) => $item['id'] === $tag->id);
            $requestTagValue = array_map('trim', $requestTag ? $requestTag[0]['value'] : []);

            $valueSelected = [];
            foreach ($requestTagValue as $item) {
                if (preg_match('/\[(.*?)\]/s', $item, $matches)) {
                    $valueSelected[] = trim($matches[1]);

                    continue;
                }

                $valueSelected[] = trim($item);
            }

            $tagSelected = $tag->transformTagValues($tagSelected, $valueSelected);
        }

        if (preg_match_all('/\{\{(.*?)\}\}/s', $sql, $matchAllConditions, PREG_SET_ORDER)) {
            foreach ($matchAllConditions as $matchCondition) {
                $conditionHasTag = false;
                [$originalCondition, $condition] = $matchCondition;

                foreach ($tagSelected as $tag) {
                    if ($conditionHasTag = preg_match('/\[\[\s*' . $tag['code'] . '\s*\]\]/', $condition, $tagMatches)) {
                        $condition = str_replace($tagMatches[0], $tag['value_selected'], $condition);
                        break;
                    }
                }

                if (!$conditionHasTag) {
                    $sql = str_replace($originalCondition, '', $sql);

                    continue;
                }

                $sql = str_replace($originalCondition, $condition, $sql);
            }
        }

        if (preg_match_all('/\[\[(.*?)\]\]/s', $sql, $matchAllTag, PREG_SET_ORDER)) {
            foreach ($matchAllTag as $matchTag) {
                [$originalTag, $codeTag] = $matchTag;

                foreach ($tagSelected as $tag) {
                    if (preg_match('/\[\[\s*' . $codeTag . '\s*\]\]/', $sql, $tagMatches)) {
                        $sql = str_replace($originalTag, $tag['value_selected'], $sql);
                        break;
                    }
                }
            }
        }

        return $sql;
    }
}
