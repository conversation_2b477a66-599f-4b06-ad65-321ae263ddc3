<?php

namespace App\Repositories;

use App\Jobs\ExportReportByRawSqlJob;
use App\Models\UniversalReportCategory;
use App\Models\UniversalReportColumn;
use App\Models\UniversalReportExportHistory;
use App\Models\UniversalReportSetting;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use App\Models\User;
use Google\Service\Datastream\Validation;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\ValidationException;

class UniversalReportRepository extends CommonRepository
{
    public function getList($request)
    {
        $query = UniversalReportTemplate::with(['tags', 'columns']);

        if (!empty($request->name)) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if (!empty($request->category_ids)) {
            $query->whereIn('report_category_id', explode(',', $request->category_ids));
        }

        $query->orderByDesc('id');

        if (empty($request->limit)) {
            return $query->select([
                'id',
                'name'
            ])->get();
        }
            
        return $query->paginate(min($request->limit, 100));
    }

    public function show(UniversalReportTemplate $template, $request)
    {
        $template->load([
            'columns',
            'tags',
            'settingDefault.settingExports',
            'settingCustom.settingExports',
        ]);

        if (!empty($request->with_all_columns)) {
            $template->load([
                'tags' => fn ($query) => $query->where('is_used', true),
            ]);
            $template->setRelation('column_configuration', $template->columnConfiguration());
        }

        return $template;
    }

    public function create($params)
    {
        try {
            DB::beginTransaction();

            $timezonePattern = '/\bSET\s+?time_zone\s+?=\s+?.*?;/i';
            if (preg_match($timezonePattern, $params['template']['query'], $matches)) {
                $sql_set_timezone = $matches[0];
                $params['template']['query'] = preg_replace($timezonePattern, "", $params['template']['query']);
                $params['template']['statement'] = $sql_set_timezone;
            }

            $template = UniversalReportTemplate::create($params['template']);

            /** Attach Tags */
            $requestTags = collect($params['tags'] ?? [])->map(function ($tag) use ($template) {
                if ($tag['type'] == UniversalReportTag::TYPE_RANGE_DATE) {
                    $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '(?:\.\d+)?\s*\]\]/', $template->query, $matches);
                } else {
                    $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '\s*\]\]/', $template->query, $matches);
                }

                return $tag;
            });
            $template->tags()->createMany($requestTags);

            /** Insert Columns */
            $columnSelected = $this->getColumnFromQuery($template->query);
            $template->columns()->createMany($columnSelected['dynamic_columns']);

            DB::commit();

            return $template->load([
                'tags',
                'columns',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function update(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $timezonePattern = '/SET\s+?time_zone\s+?=\s+?.*?;/i';
            if (preg_match($timezonePattern, $params['template']['query'], $matches)) {
                $sql_set_timezone = $matches[0];
                $params['template']['query'] = preg_replace($timezonePattern, "", $params['template']['query']);
                $params['template']['statement'] = $sql_set_timezone;
            } else {
                $params['template']['statement'] = null;
            }

            $template->update($params['template']);

            /** Attach Tags */
            $requestTags = collect($params['tags'] ?? [])->mapToGroups(function ($tag) use ($template) {
                if ($tag['type'] == UniversalReportTag::TYPE_RANGE_DATE) {
                    $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '(?:\.\d+)?\s*\]\]/', $template->query, $matches);
                } else {
                    $tag['is_used'] = preg_match('/\[\[\s*' . $tag['code'] . '\s*\]\]/', $template->query, $matches);
                }

                return [isset($tag['id']) ? 'update' : 'insert' => $tag];
            });

            $tagNeedUpdate = $requestTags->get('update', collect());
            $template->tags()->whereNotIn('id', $tagNeedUpdate->pluck('id'))->delete();
            $template->tags()->createMany($requestTags->get('insert', collect())->toArray());
            foreach ($tagNeedUpdate as $itemUpdate) {
                UniversalReportTag::where('id', $itemUpdate['id'])->update([
                    'is_used' => $itemUpdate['is_used'],
                    'is_required' => $itemUpdate['is_required'] ?? false,
                    'code' => $itemUpdate['code'],
                    'position' => $itemUpdate['position'],
                    'label' => $itemUpdate['label'] ?? null,
                    'value' => $itemUpdate['value'] ?? null,
                    'value_type' => $itemUpdate['value_type'],
                    'placeholder_1' => $itemUpdate['placeholder_1'] ?? null,
                    'placeholder_2' => $itemUpdate['placeholder_2'] ?? null,
                ]);
            }

            /** Insert Columns */
            $columnSelected = $this->getColumnFromQuery($template->query)['dynamic_columns'];

            $template->columns()->delete();
            $template->columns()->createMany($columnSelected);

            DB::commit();

            return $template->load([
                'tags',
                'columns',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function delete(UniversalReportTemplate $template)
    {
        try {
            DB::beginTransaction();

            $template->tags()->delete();
            $template->columns()->delete();
            UniversalReportSetting::where('report_template_id', $template->id)->delete();

            $template->delete();

            DB::commit();

            return $this->successResponse(
                'Template deleted successfully.',
            );
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function getCategories($request)
    {
        $query = UniversalReportCategory::query();;

        if (!empty($request->name)) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        return $query->get();
    }

    public function duplicate(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $newTemplate = $template->replicate();
            $newTemplate->name = $newTemplate->name . ' (Copy)';

            if (isset($params['name'])) {
                $newTemplate->name = $params['name'];
            }

            if (isset($params['description'])) {
                $newTemplate->description = $params['description'];
            }

            $newTemplate->save();

            $tags = $template->tags->map(function ($tag) {
                return $tag->replicate();
            });
            $newTemplate->tags()->saveMany($tags);

            $columns = $template->columns->map(function ($column) {
                return $column->replicate();
            });
            $newTemplate->columns()->saveMany($columns);

            DB::commit();

            return $newTemplate->load(['tags', 'columns']);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function previewTemplate(UniversalReportTemplate $template, array $params)
    {
        try {
            $sql = $template->query;
            $template->load([
                'tags' => fn ($query) => $query->where('is_used', true),
                'columns',
                'settingDefault.settingExports',
                'settingCustom.settingExports',
            ]);

            try {
                /** Append column selected */
                $virtualColumns = $template->virtualColumnMap();

                $columnSelected = $this->getColumnSelected($virtualColumns, collect($params['columns'] ?? []));
                $sql = $this->getColumnFromQuery($sql, $columnSelected->toArray())['query'];

                /** Append tags */
                $sql = $this->handleAppendTemplateTag(
                    $sql,
                    $params['tags'] ?? [],
                    $template->tags,
                    boolval($params['is_preview'] ?? false),
                    boolval($params['show_raw_sql'] ?? false)
                );

                Log::channel('universal_report')->info('Executing SQL query', [
                    'sql' => $sql,
                    'template_id' => $template->id,
                    'payload' => $params,
                ]);

                if (boolval($params['show_raw_sql'] ?? false)) {
                    /** Handle SQL statement */
                    if (boolval($params['is_preview'] ?? false) && !empty($template->statement)) {
                        $sql = $template->statement . "\n" . $sql;
                    }

                    return [
                        'sql' => $sql
                    ];
                }

                $data = $this->executeQueryWithConnectionReport($sql, $template->statement ?? null);

                return $data;

            } catch (QueryException $queryException) {
                Log::channel('universal_report')->error('Error executing SQL query previewTemplate', [
                    'sql' => $sql,
                    'template_id' => $template->id,
                    'error' => $queryException->getMessage(),
                    'payload' => $params,
                ]);

                throw ValidationException::withMessages([
                    'sql' => 'Error executing SQL query: ' . $queryException->getMessage(),
                ]);
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function countTemplate(UniversalReportTemplate $template, array $params)
    {
        try {
            $sql = $template->query;
            $template->load([
                'tags' => fn ($query) => $query->where('is_used', true),
                'columns',
                'settingDefault.settingExports',
                'settingCustom.settingExports',
            ]);

            try {
                /** Append column selected */
                $virtualColumns = $template->virtualColumnMap();

                $columnSelected = $this->getColumnSelected($virtualColumns, collect($params['columns'] ?? []));
                $sql = $this->getColumnFromQuery($sql, $columnSelected->toArray())['query'];

                /** Append tags */
                $sql = $this->handleAppendTemplateTag($sql, $params['tags'] ?? [], $template->tags, boolval($params['is_preview'] ?? false));

                /** Handle SQL COUNT total */
                $sql = $this->handleSqlCountTotal($sql);

                $data = $this->executeQueryWithConnectionReport($sql, $template->statement ?? null);

                return $data;

            } catch (QueryException $queryException) {
                Log::channel('universal_report')->error('Error executing SQL query countTemplate', [
                    'sql' => $sql,
                    'template_id' => $template->id,
                    'error' => $queryException->getMessage(),
                    'payload' => $params,
                ]);

                if (boolval($params['show_raw_sql'] ?? false)) {
                    throw $queryException;
                } else {
                    return [];
                }
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function exportTemplate(UniversalReportTemplate $template)
    {
        try {
            $export = UniversalReportExportHistory::create([
                'name' => $template->name . ' - ' . now()->format('Y-m-d H-i-s') . '.csv',
                'user_id' => auth()->id(),
                'report_template_id' => $template->id,
                'start_time' => now(),
                'show_in_popup' => true,
                'status' => UniversalReportExportHistory::STATUS_PENDING
            ]);
            
            ExportReportByRawSqlJob::dispatch($export->id)
                ->onQueue('universal-report-export');

            return $export;
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function deleteExportHistory(UniversalReportExportHistory $history)
    {
        try {
            $history->delete();

            return $this->successResponse(
                'Export history deleted successfully.',
            );
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function getExportHistories(Request $request)
    {
        $query = UniversalReportExportHistory::query()
            ->with([
                'user',
                'template',
            ])
            ->when(
                auth()->check() && auth()->user()->is_admin == User::ADMIN,
                fn ($query) => $query->withoutGlobalScopes(['isOwner']),
            );

        if (!empty($request->status)) {
            $query->where('status', $request->status);
        }

        if (!empty($request->report_template_ids)) {
            $query->whereIn('report_template_id', explode(',', $request->report_template_ids));
        }

        if (!empty($request->category_ids)) {
            $query->whereHas('template', function ($q) use ($request) {
                $q->whereIn('report_category_id', explode(',', $request->category_ids));
            });
        }

        if (!empty($request->show_in_popup)) {
            $query->where('show_in_popup', boolval($request->show_in_popup));
        }

        if (!empty($request->user_ids)) {
            $query->whereIn('user_id', explode(',', $request->user_ids));
        }

        if (!empty($request->keyword)) {
            $query->where(function ($subQuery) use ($request) {
                $subQuery->where('name', 'like', '%' . $request->keyword . '%')
                    ->orWhereHas('template', function ($q) use ($request) {
                        $q->where('name', 'like', '%' . $request->keyword . '%');
                    });
            });
        }
        if (!empty($request->user_ids)) {
            $query->whereIn('user_id', explode(',', $request->user_ids));
        }

        return $query->orderByDesc('id')
            ->paginate($request->limit ?? 25);
    }

    public function actionExportHistory($historyId, $action)
    {
        switch ($action) {
            case 'download':
                $history = UniversalReportExportHistory::query()
                    ->where('status', UniversalReportExportHistory::STATUS_COMPLETED)
                    ->whereNotNull('file_path')
                    ->findOrFail($historyId);

                $history->update(['last_download_at' => now()]);

                return [
                    'file_url' => $history->getFileUrlAttribute(),
                    'file_size' => $history->file_size,
                ];
            
            case 'close':
                return UniversalReportExportHistory::findOrFail($historyId)
                    ->update(['show_in_popup' => false]);
            
            default:
                throw ValidationException::withMessages([
                    'action' => 'Action not supported!',
                ]);
        }
    }

    public function revertTemplate(UniversalReportTemplate $template)
    {
        if (empty($template->settingCustom)) {
            return [];
        }

        try {
            DB::beginTransaction();
            $template->settingCustom->settingExports()->delete();
            $template->settingCustom->delete();
            DB::commit();

            $template->load([
                'tags',
                'columns',
                'settingDefault.settingExports',
                'settingCustom.settingExports',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function getTags(UniversalReportTemplate $template, UniversalReportTag $tag, Request $request)
    {
        try {
            $sql = $tag->value;
            $limit = $request->limit ?? 0;
            $offset = $request->offset ?? 0;

            switch ($tag->type) {
                case UniversalReportTag::TYPE_SINGLE_SELECT:
                case UniversalReportTag::TYPE_MULTI_SELECT:
                    if ($tag->value_type === UniversalReportTag::VALUE_TYPE_SQL) {
                        if ($limit > 0 || $offset > 0) {
                            $sql = $this->appendOuterLimitOffset($sql, $limit, $offset);
                        }
                        $sql = $this->appendFilterByValue($sql, $request->keyword ?? null);

                        try {
                            $options = $this->executeQueryWithConnectionReport($sql);
                            $options = collect($options)->map(function ($option) {
                                return collect((array) $option)
                                    ->mapWithKeys(fn($v, $k) => [strtolower($k) => $v])
                                    ->only(['value', 'key', 'text'])
                                    ->pipe(function ($arr) {
                                        return [
                                            'value' => $arr['value'] ?? null,
                                            'text'  => $arr['key'] ?? $arr['text'] ?? null,
                                        ];
                                    });
                            })->filter();
                        } catch (QueryException $queryException) {
                            $options = [];
                        }
                    }
                    break;

                default:
                    break;
            }

            $tag->setRelation('options', collect($options ?? []));

            return $tag;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function updateOrCreateSettingExport(UniversalReportTemplate $template, array $params)
    {
        try {
            DB::beginTransaction();

            $isDefault = isset($params['is_default']) && $params['is_default'];

            if ($isDefault) {
                $settingDefault = UniversalReportSetting::where('report_template_id', $template->id)
                    ->whereNull('user_id')
                    ->first();

                if (isset($settingDefault)) {
                    $settingDefault->settingExports()->delete();
                    $settingDefault->delete();
                }
            }

            $setting = UniversalReportSetting::updateOrCreate(
                [
                    'report_template_id' => $template->id,
                    'user_id' => !$isDefault ? auth()->id() : null,
                ],
                [
                    'is_default' => $params['is_default'] ?? false,
                    'export_status' => UniversalReportSetting::EXPORT_STATUS_NONE,
                    'export_status' => UniversalReportSetting::EXPORT_STATUS_NONE,
                    'file_path' => null,
                    'error_message' => null,
                    'download_at' => null,
                ],
            );

            if (!empty($params['columns'])) {
                $columns = collect($params['columns'])->map(function ($column) {
                    return [
                        'model_type' => UniversalReportColumn::class,
                        'model_key' => $column['name'],
                        'alias' => $column['alias'],
                        'value' => $column['custom_alias']
                    ];
                })->toArray();
            }

            if (!empty($params['tags'])) {
                $exportItems = [];
                $existTags = UniversalReportTag::whereIn('id', array_column($params['tags'], 'id'))->get();

                foreach ($params['tags'] as $tag) {
                    $totalValue = count($tag['value'] ?? []);
                    $currentTag = $existTags->firstWhere('id', $tag['id']);

                    foreach ($tag['value'] as $index => $tagValue) {
                        $exportItem = [
                            'model_id' => $tag['id'],
                            'model_type' => UniversalReportTag::class,
                            'value' => $tagValue,
                        ];

                        if ($currentTag->type === UniversalReportTag::TYPE_RANGE_DATE && $totalValue === 2) {
                            $exportItem['model_key'] = $index === 0 ? 'start_date' : 'end_date';
                        }

                        $exportItems[] = $exportItem;
                    }
                }
            }

            $setting->settingExports()->delete();
            $setting->settingExports()->createMany([
                ...$columns ?? [],
                ...$exportItems ?? [],
            ]);

            DB::commit();

            return $setting->load([
                'settingColumns',
                'settingTags',
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function transformParamExport(UniversalReportTemplate $template, $settingExports)
    {
        $params = [];
        $virtualColumns = $template->virtualColumnMap();

        if (empty($settingExports)) {
            if ($virtualColumns->isNotEmpty()) {
                $params['columns'] = $virtualColumns->toArray();
            }

            return $params;
        }

        $settingExports = $settingExports->groupBy('model_type');
        foreach ($settingExports as $settingType => $settings) {
            if ($settingType === UniversalReportColumn::class) {
                $requestColumns = $settings->map(function ($item) {
                    return [
                        'name' => $item->model_key,
                        'alias' => $item->alias,
                        'custom_alias' => $item->value,
                    ];
                });

                $columnSelected = $this->getColumnSelected($virtualColumns, $requestColumns);
                
                if ($columnSelected->isEmpty()) {
                    continue;
                }

                $params['columns'] = $columnSelected->toArray();
            }

            if ($settingType === UniversalReportTag::class) {
                $params['tags'] = $settings->groupBy('model_id')->map(function ($items, $tagId) {
                    return [
                        'id' => $tagId,
                        'value' => $items->pluck('value')->toArray(),
                    ];
                })->values()->toArray();

                continue;
            }
        }

        return $params;
    }

    
    public function appendOuterLimitOffset(string $sql, int $limit = 100, int $offset = 0): string
    {
        $sqlTrimmed = trim($sql);
        $endsWithSemicolon = str_ends_with($sqlTrimmed, ';');

        if ($endsWithSemicolon) {
            $sqlTrimmed = rtrim($sqlTrimmed, ';');
        }

        $normalizedSql = preg_replace('/\s+/', ' ', $sqlTrimmed);

        $tokens = preg_split('/\s+/', $normalizedSql);
        $level = 0;
        $hasOuterLimitOrOffset = false;

        foreach ($tokens as $token) {
            $tokenLower = strtolower($token);
            $level += substr_count($token, '(') - substr_count($token, ')');

            if ($level === 0 && ($tokenLower === 'limit' || $tokenLower === 'offset')) {
                $hasOuterLimitOrOffset = true;
                break;
            }
        }

        if ($hasOuterLimitOrOffset) {
            return $sql;
        }

        $sqlTrimmed .= " LIMIT $limit OFFSET $offset";

        return $sqlTrimmed . ($endsWithSemicolon ? ';' : '');
    }

    public function executeQueryWithConnectionReport($query, $statement = null)
    {
        $pattern = '/\b(CREATE\s+?DATABASE|DROP\s+?DATABASE|ALTER\s+?DATABASE|ALTER\s+?SCHEMA|CREATE\s+?TABLE|DROP\s+?TABLE|ALTER\s+?TABLE|TRUNCATE\s+?TABLE|RENAME\s+?TABLE|DROP\s+?INDEX|UPDATE\s+[0-9A-Z\$_\\\`\'\"]+\s+SET|INSERT\s+?INTO|DELETE\s+?FROM)\b/i';

        if (isset($statement)) {
            if (preg_match($pattern, $statement)) {
                throw ValidationException::withMessages([
                    'sql' => 'Invalid SQL query',
                ]);
            }

            DB::connection('mysql_report')->statement($statement);
        }

        if (preg_match($pattern, $query)) {
            throw ValidationException::withMessages([
                'sql' => 'Invalid SQL query',
            ]);
        }

        return DB::connection('mysql_report')->select($query);
    }

    private function getColumnSelected(Collection $virtualColumns, Collection $requestColumns): Collection
    {
        $paramColumns = $requestColumns->map(function ($itemColumn) use ($virtualColumns) {
            $replacement = $virtualColumns->firstWhere('name', $itemColumn['name']);

            return $replacement ?? $itemColumn;
        });

        $missingFromParams = $virtualColumns->reject(
            fn($missing) => $paramColumns->contains('name', $missing['name'])
        );

        $columnSelected = $paramColumns->when(
            $paramColumns->isNotEmpty(),
            fn ($collection) => $collection->concat($missingFromParams)->values(),
            fn () => $paramColumns
        );

        return $columnSelected;
    }

    private function getColumnFromQuery(string $query, array $columnSelected = []) : array
    {
        if (preg_match('/\b(CREATE\s+?DATABASE|DROP\s+?DATABASE|ALTER\s+?DATABASE|ALTER\s+?SCHEMA|CREATE\s+?TABLE|DROP\s+?TABLE|ALTER\s+?TABLE|TRUNCATE\s+?TABLE|RENAME\s+?TABLE|DROP\s+?INDEX|UPDATE\s+[0-9A-Z\$_\\\`\'\"]+\s+SET|INSERT\s+?INTO|DELETE\s+?FROM)\b/i', $query)) {
            throw ValidationException::withMessages([
                'sql' => 'Invalid SQL Query',
            ]);
        }

        $pattern = "/(?!\bSELECT\b[^{}]*?\bFROM\b)\bSELECT\b(.*?)\bFROM\b/is";
        if (!preg_match($pattern, $query, $select_statement, PREG_UNMATCHED_AS_NULL)) {
            return [
                'dynamic_columns' => [],
                'query' => $query,
            ];
        }

        $ori_select_content = $select_statement ? $select_statement[1] : "";
        $select_static_columns = $ori_select_content;

        if (empty($ori_select_content)) {
            return [];
        }

        $ori_select_content_for_placeholder_detect = $ori_select_content;

        /** 1st for select column without alias */
        $pattern = "/{{\s*?([\w\$`]+?\s*?\.\s*?[\w\$`\*]+?|[\w\$`]+?)\s*?}}\s*,?/i";
        preg_match_all($pattern, $ori_select_content_for_placeholder_detect, $placeholder_cols_wo_alias, PREG_SET_ORDER);

        /** Remove matches by 1st regex to make sure it will not be detected again in 2nd regex */
        $ori_select_content_for_placeholder_detect = preg_replace($pattern, "", $select_static_columns);
        
        /** 2nd for select column with alias */
        $pattern = "/{{\s*?([^}]*?)(?:\s+AS\s+|\s+)(\w+?|`[^\`]+?`|\'[^\']+?\'|\"[^\"]+?\")\s*?}}\s*,?/i";
        preg_match_all($pattern, $ori_select_content_for_placeholder_detect, $placeholder_cols_w_alias, PREG_SET_ORDER);

        $placeholder_cols = array_merge($placeholder_cols_wo_alias, $placeholder_cols_w_alias);

        $dynamicColumns = [];
        foreach ($placeholder_cols as $patternColumn) {
            // Clean up result array: 0 => {{ original_col_name AS `alias` }} , 1 => original_col_name, 2 => alias
            $col_replacement_arr = array_values(array_filter($patternColumn));
            // eg: {{ original_col_name AS `alias` }}
            $column_placeholder = $col_replacement_arr[0];
            // eg: original_col_name
            $original_col_name = trim($col_replacement_arr[1]);
            $original_col_alias = $col_replacement_arr[2] ?? null;

            $pattern = "/`?([^{}\[\]\?`]+)`?\s*?\.\s*?\*/i";
            if (preg_match($pattern, $original_col_name, $select_all_table, PREG_UNMATCHED_AS_NULL)) {
                // Must trim to get correct $table_original_name in any case. eg: wh  . *
                $select_all_table_alias = trim($select_all_table[1]);//eg: return 'wh' from 'wh .*' or '`wh` . *'
                // MySQL accept $ in table name, so we escape it: https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
                $select_all_table_alias = str_replace("$", "\\$", $select_all_table_alias);
                $onlyTable = str_replace("`", "", $select_all_table_alias);

                if (Schema::hasTable($onlyTable)) {
                    $dynamicColumns[] = [
                        'virtual_column' => false,
                        'original' => $column_placeholder,
                        'table' => $onlyTable,
                        'column' => null,
                        'alias' => $original_col_alias,
                    ];
                    $select_static_columns = str_replace_first($column_placeholder, "", $select_static_columns);

                    continue;
                }

                $pattern = "/\b(?:FROM|JOIN)\s+(`?[0-9A-Z\$_]+`?)(?:\s+AS\s+|\s+)(?:`" . $select_all_table_alias . "`|\b" . $select_all_table_alias . "\b)/i";
                if (!Schema::hasTable($select_all_table[1]) && preg_match($pattern, $query, $table_original_name, PREG_UNMATCHED_AS_NULL)) {
                    $select_all_table_original_name = $table_original_name ? trim(str_replace("`", "", $table_original_name[1])) : "";

                    if (!Schema::hasTable($select_all_table_original_name)) {
                        $select_static_columns = str_replace_first($column_placeholder, $original_col_name . ",", $select_static_columns);

                        continue;
                    }

                    $dynamicColumns[] = [
                        'virtual_column' => false,
                        'original' => $column_placeholder,
                        'table' => $select_all_table_original_name,
                        'column' => null,
                        'alias' => $select_all_table_alias
                    ];
                    $select_static_columns = str_replace_first($column_placeholder, "", $select_static_columns);

                    continue;
                } else {
                    $select_static_columns = str_replace_first($column_placeholder, $original_col_name . ",", $select_static_columns);

                    continue;
                }
            }

            $replacedColumn = preg_replace("/^\s*[\'\"]|[\'\"]\s*$/i", '', $original_col_name);
            $replacedColumn = str_replace('`', '', $replacedColumn);

            $replacedAlias = preg_replace("/^\s*[\'\"]|[\'\"]\s*$/i", '', $original_col_alias);
            $replacedAlias = str_replace('`', '', $replacedAlias);

            $dynamicColumns[] = [
                'virtual_column' => preg_match('/[^0-9a-z_\$\.`\s]+/', $original_col_name, $mat),
                'original' => $column_placeholder,
                'table' => null,
                'column' => $original_col_name,
                'alias' => !empty($replacedAlias) ? $replacedAlias : $replacedColumn,
            ];

            $select_static_columns = str_replace_first($column_placeholder, "", $select_static_columns);
        }

        $select_static_columns = preg_replace("/\,\s*$/i", "", $select_static_columns);
        $user_selected_columns = [];

        foreach($columnSelected as $itemColumn) {
            $new_col_alias = isset($itemColumn['alias']) ? $itemColumn['alias'] : $itemColumn['name'];
            $new_col_alias = preg_replace("/^\s*[\'\"]|[\'\"]\s*$/i", "", $new_col_alias);
            $new_col_alias = trim(str_replace("`", "", $new_col_alias));

            $user_selected_columns[] = $itemColumn['name'] . " AS `" . $new_col_alias . "`";
        }
        // $user_selected_columns on the top of SELECT statement
        $new_select_content = "";
        if($user_selected_columns) {//updated
            $new_select_content .= "\n\t" . implode(",\n\t", $user_selected_columns);
        }
        
        // Clean up the rest of SELECT static columns
        $select_static_columns = trim($select_static_columns);
        $select_static_columns_arr = array_filter(array_map('trim', explode("\n", $select_static_columns)));//updated, use \n instead of ,
        if($select_static_columns_arr) {
            $select_static_columns = "\n\t" . implode("\n\t", $select_static_columns_arr);
        }
        
        // $select_static_columns at the end of SELECT statement
        if($select_static_columns) {
            $new_select_content .= (trim($new_select_content) ? "," : "") . $select_static_columns;//updated
        }

        $new_select_content .= "\n";
        $newQuery = str_replace_first($ori_select_content, $new_select_content, $query);

        return [
            'dynamic_columns' => $dynamicColumns,
            'query' => $newQuery ?? $query,
        ];
    }

    private function appendFilterByValue(string $sql, ?string $value, ?string $alias = null): string
    {
        if (!preg_match('/\{\{(.*?)\}\}/', $sql, $filterMatch)) {
            return $sql;
        }

        [$placeholderExpr, $filterTemplate] = $filterMatch;

        $filterClause = null;
        if (!empty($value) && preg_match('/\[\[.*?\]\]/', $filterTemplate, $placeholderMatch)) {
            if (empty($alias) || (isset($alias) && $alias === $placeholderMatch[0])) {
                $filterClause = str_replace($placeholderMatch[0], $value, $filterTemplate);
            }
        }

        return str_replace($placeholderExpr, $filterClause, $sql);
    }

    private function handleAppendTemplateTag(string &$sql, array $requestTags = [], Collection $templateTags, bool $isPreview = false, bool $isShowSqlRaw = false)
    {
        $payloadSelected = [];
        foreach ($templateTags as $tag) {
            $requestTag = collect($requestTags ?? [])->firstWhere('id', $tag->id);

            if (empty($requestTag)) {
                continue;
            }

            $requestTagValue = array_map('trim', $requestTag ? $requestTag['value'] : []);

            $valueSelected = [];
            foreach ($requestTagValue as $item) {
                if (preg_match('/\[(.*?)\]/s', $item, $matches)) {
                    $valueSelected[] = trim($matches[1]);
                    continue;
                };

                $valueSelected[] = trim($item);
            }

            $payloadSelected = $tag->transformTagValues($payloadSelected, $valueSelected);
        }

        $condition_payload = collect($payloadSelected)->mapWithKeys(function ($value) {
            return [$value['code'] => $value['value_selected']];
        })->toArray();

        $pattern_parse_conditions = "/{{([^\{\}]*?\[\[([^\[\]]*?)\]\][^\{\}]*?)}}/i";
        preg_match_all($pattern_parse_conditions, $sql, $placeholder_conditions, PREG_SET_ORDER);

        $condition_tag_arr = array_keys($condition_payload);
        $condition_tag_arr = array_map(fn($value): string => "/\[\[\x20*" . $value . "\x20*\]\]/i", $condition_tag_arr);
        $condition_value_arr = array_values($condition_payload);

        // We support recursive placeholder condition for more flexible of writing SQL Query
        while(!empty($placeholder_conditions)) {
        // Parse each {{ ...[[...]]... }} condition
            foreach ($placeholder_conditions as $condition_replacement_arr) {
                //eg: {{ po.order_date < [[input_date]] }}
                $condition_placeholder = $condition_replacement_arr[0];
                //eg: po.order_date < [[input_date]] 
                $condition_placeholder_content = $condition_replacement_arr[1];
                
                // We will remove condition placeholder if there is ANY empty value tag inside it
                $has_empty_tag = false;
                
                $pattern = "/\[\[\x20*(.*?)\x20*\]\]/i";
                preg_match_all($pattern, $condition_placeholder, $placeholder_tags, PREG_SET_ORDER);
                
                // Parse each [[...]] tag inside placeholder {{ ...[[...]]... }}
                foreach ($placeholder_tags as $placeholder_tag_arr) {
                    //eg: [[input_date]]
                    $placeholder_tag_replacement = $placeholder_tag_arr[0];
                    //eg: input_date
                    $placeholder_tag_name = $placeholder_tag_arr[1];
                    
                    if(!isset($condition_payload[$placeholder_tag_name]) || $condition_payload[$placeholder_tag_name] === "") {
                        $has_empty_tag = true;
                        break;
                    }
                }
                
                // Remove placeholder if there is ANY empty value tag inside it
                if($has_empty_tag) {
                    $sql = str_replace_first($condition_placeholder, "", $sql);
                } else {
                    $condition_placeholder_replacement = preg_replace($condition_tag_arr, $condition_value_arr, $condition_placeholder_content);
                    $sql = str_replace_first($condition_placeholder, $condition_placeholder_replacement, $sql);
                }
            }
            
            // Match again to update $placeholder_conditions
            preg_match_all($pattern_parse_conditions, $sql, $placeholder_conditions, PREG_SET_ORDER);
        }   

        // We also support to replace tag [[...]] which is not inside placeholder {{ .. }}
        $sql = preg_replace($condition_tag_arr, $condition_value_arr, $sql);

        // Check LIMIT to add LIMIT ... if need
        // Optional step, eg: for data previewing
        if ($isPreview && !$isShowSqlRaw) {
            $pattern = "/\b(LIMIT\s+?(\d+)(?:\s*?,\s*?(\d+))?)\s*;?\s*$/i";
            $sql = preg_replace($pattern, "", $sql);
            if(empty($limit)) {
                $sql = preg_replace("/\;\s*$/i", "", $sql);
                $sql .= "\nLIMIT 0, 10";
            }
        }

        return $sql;
    }

    private function handleSqlCountTotal(string $sql) : string
    {
        $sql = preg_replace("/\;\s*$/i", "", $sql);
        $sql = "SELECT COUNT(*) AS total_count FROM (\n" . $sql . "\n) as total_count_query";

        return $sql;
    }
}
