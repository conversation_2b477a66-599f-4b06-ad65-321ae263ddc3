<?php

namespace App\Repositories;

use App\Models\UserAccessLog;

class LavenderAuthRepository
{
    protected $warehouseRepository;

    public function __construct(WarehouseRepository $warehouseRepository)
    {
        $this->warehouseRepository = $warehouseRepository;
    }

    public function doLogin($inputs, $request)
    {
        if ((!$token = auth()->attempt($inputs)) || empty(auth()->user()->is_lavender_editor)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        UserAccessLog::logUserAccess(
            getClientIp($request),
            $request->header('CF-IPCountry') ?? null,
            auth()->user()?->id ?? null,
            'lavender',
        );

        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 120,
            'user' => auth()->user(),
        ]);
    }

    public function doLogout()
    {
        return auth()->logout();
    }

    public function createNewToken($token, $isRefresh = true)
    {
        $response = [
            'access_token' => $token,
            'token_type' => 'bearer',
            'user' => auth()->user(),
        ];

        if (!$isRefresh) {
            $response['expires_in'] = auth()->factory()->getTTL() * 120;
        }

        return $response;
    }
}
