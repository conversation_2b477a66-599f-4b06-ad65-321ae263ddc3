<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderSurchargeFee;
use App\Models\SurchargeFee;
use App\Models\SurchargeFeeHistory;
use App\Models\SurchargeService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SurchargeFeeRepository extends CommonRepository
{
    public function createOrUpdate($input)
    {
        DB::beginTransaction();
        try {
            $query = SurchargeService::where('name', $input['type']);
            if (!isset($input['product_type'])) {
                $query->whereNull('product_type');
            } else {
                $query->where('product_type', $input['product_type']);
            }
            $surchargeService = $query->first();
            if (!$surchargeService) {
                throw new \Exception('Surcharge Service not found.');
            }
            $surchargeFee = SurchargeFee::where('service_id', $surchargeService->id)->where('store_id', $input['store_id'])->first();
            if ($surchargeFee) {
                $message = "Fee updated from $surchargeFee->value to {$input['value']}";
                $surchargeFee->update([
                    'value' => $input['value'],
                ]);
            } else {
                $surchargeFee = SurchargeFee::create([
                    'service_id' => $surchargeService->id,
                    'value' => $input['value'],
                    'store_id' => $input['store_id'],
                    'type' => $surchargeService->api_value,
                ]);
                $message = "Fee updated from 0 to {$input['value']}";
            }
            SurchargeFeeHistory::create([
                'message' => $message,
                'user_id' => auth()->user()->id,
                'store_surcharge_id' => $surchargeFee->id,
            ]);
            DB::commit();

            return $surchargeFee;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getList($storeId)
    {
        return SurchargeService::select('surcharge_service.*', 'store_surcharge.value')
            ->leftJoin('store_surcharge', function ($join) use ($storeId) {
                $join->on('surcharge_service.id', '=', 'store_surcharge.service_id')
                    ->where('store_surcharge.store_id', '=', $storeId);
            })
            ->groupBy(DB::raw('CASE WHEN surcharge_service.api_value IS NOT NULL THEN surcharge_service.api_value ELSE surcharge_service.id END'))
            ->orderBy('surcharge_service.id', 'ASC')
            ->get();
    }

    public function handleSurchargeFeePerOrder(Collection $storeSurcharges, SaleOrder $saleOrder, string $surchargeType)
    {
        $surcharge = $storeSurcharges->first(function ($itemSurcharge) use ($surchargeType) {
            return $itemSurcharge->per == SurchargeService::PER_ORDER && $itemSurcharge->name == $surchargeType;
        });
        if ($surcharge) {
            $this->updateSurchargeFeePerOrder($saleOrder, $surcharge);
        }
    }

    public function handleLabelSurchargeFeePerOrder(Collection $storeSurcharges, SaleOrder $saleOrder): void
    {
        $surcharge = $storeSurcharges->first(function ($itemSurcharge) {
            return $itemSurcharge->per == SurchargeService::PER_ORDER
                && $itemSurcharge->name == SurchargeService::TYPE_LABEL_PRINTING_FEE;
        });
        if ($surcharge) {
            $this->updateSurchargeFeePerOrder($saleOrder, $surcharge);
        }
    }

    public function updateSurchargeFeePerOrder(SaleOrder $saleOrder, object $surcharge)
    {
        return SaleOrderSurchargeFee::updateOrCreate([
            'order_id' => $saleOrder->id,
            'surcharge_id' => $surcharge->store_surcharge_id,
        ], [
            'value' => $surcharge->value,
            'created_at' => now()
        ]);
    }

    public function handleSurchargeFeePerItem(SaleOrderItem $saleOrderItem, object $surchargeFee)
    {
        return SaleOrderItemSurchargeFee::updateOrCreate([
            'order_id' => $saleOrderItem->order_id,
            'order_item_id' => $saleOrderItem->id,
            'surcharge_id' => $surchargeFee->store_surcharge_id,
        ], [
            'value' => $surchargeFee->value * $saleOrderItem->quantity,
            'created_at' => now()
        ]);
    }

    public function handleSurchargeFeePerProductType(Collection $storeSurcharges, SaleOrderItem $saleOrderItem, $type)
    {
        $surchargeFee = $storeSurcharges->first(function ($itemSurcharge) use ($type) {
            return $itemSurcharge->per == SurchargeService::PER_PRODUCT_TYPE && $itemSurcharge->name == $type;
        });
        if ($surchargeFee) {
            $productTypeSettingSurcharge = explode(',', $surchargeFee->product_type);
            $productTypeSettingSurcharge = array_map('strtolower', $productTypeSettingSurcharge);
            $saleOrderItemType = strtolower($saleOrderItem->getTypeProduct->type);
            if (in_array($saleOrderItemType, $productTypeSettingSurcharge)) {
                $this->handleSurchargeFeePerItem($saleOrderItem, $surchargeFee);
            }
        }
    }

    public function fetchSaleOrdersSurcharge($saleOrderIds)
    {
        $saleOrderIds = is_array($saleOrderIds) ? $saleOrderIds : [$saleOrderIds];

        return SaleOrder::query()
            ->select(
                'sale_order.id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order_surcharge.value as total',
                'surcharge_service.name',
                'surcharge_service.per',
                'surcharge_service.api_value',
                'sale_order_surcharge.id as order_surcharge_id',
                'store_surcharge.value as fee',
                DB::raw('1 as quantity'),
            )
            ->join('sale_order_surcharge', 'sale_order_surcharge.order_id', '=', 'sale_order.id')
            ->join('store_surcharge', 'store_surcharge.id', '=', 'sale_order_surcharge.surcharge_id')
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->whereIn('sale_order.id', $saleOrderIds)
            ->get();
    }

    public function fetchSaleOrdersItemsSurcharge($saleOrderIds, $withoutEmbSurcharge = true)
    {
        $saleOrderIds = is_array($saleOrderIds) ? $saleOrderIds : [$saleOrderIds];

        return SaleOrder::query()
            ->select(
                'sale_order.id',
                'sale_order.order_number',
                'sale_order.external_number',
                DB::raw('SUM(sale_order_item_surcharge.value) AS total'),
                DB::raw('SUM(sale_order_item.quantity) AS quantity'),
                'surcharge_service.name',
                'surcharge_service.per',
                'store_surcharge.value as fee',
                'surcharge_service.api_value',
                'sale_order_item_surcharge.id as item_surcharge_id',
            )
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_surcharge', 'sale_order_item_surcharge.order_item_id', '=', 'sale_order_item.id')
            ->join('store_surcharge', 'store_surcharge.id', '=', 'sale_order_item_surcharge.surcharge_id')
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->whereIn('sale_order.id', $saleOrderIds)
            ->where(function ($query) use ($withoutEmbSurcharge) {
                if ($withoutEmbSurcharge) {
                    $query->whereNotIn('surcharge_service.api_value',
                        [
                            SurchargeService::API_VALUE_HANDLING,
                            SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES,
                            SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES,
                            SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES,
                            SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES,
                        ]);
                }
            })
            ->groupBy('sale_order.id', 'surcharge_service.id')
            ->get();
    }

    public function fetchSaleOrderInsertFeeByInvoice($invoiceId, $saleOrderIds)
    {
        return SaleOrder::query()
            ->select(
                'sale_order.id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order_insert_calculate_price.amount_paid AS total',
                'sale_order_insert_calculate_price.qty as quantity',
                'sale_order_insert_sku.type as name',
                DB::raw('"order" as per'),
                'sale_order_insert_calculate_price.unit_price as fee',
            )
            ->join('invoice_sale_order_insert', 'invoice_sale_order_insert.sale_order_id', '=', 'sale_order.id')
            ->join('sale_order_insert_calculate_price', 'sale_order_insert_calculate_price.order_insert_id', '=', 'invoice_sale_order_insert.order_insert_id')
            ->join('sale_order_insert_sku', 'sale_order_insert_sku.sku', '=', 'sale_order_insert_calculate_price.product_sku')
            ->whereNotNull('sale_order_insert_calculate_price.calculated_at')
            ->where('invoice_sale_order_insert.invoice_id', $invoiceId)
            ->whereIn('sale_order.id', $saleOrderIds)
            ->get();
    }

    public function fetchPeakSurChargeByOrder($orderId)
    {
        return SaleOrderSurchargeFee::where('type', SaleOrderSurchargeFee::PEAK_SHIPPING_FEE)
            ->where('order_id', $orderId)
            ->first();
    }

    public function fetchSaleOrdersItemsEmbSurcharge($saleOrderIds)
    {
        return SaleOrder::query()
            ->select(
                'sale_order.id',
                'sale_order.order_number',
                'sale_order.external_number',
                DB::raw('SUM(sale_order_item_surcharge.value) AS total'),
                DB::raw('SUM(sale_order_item.quantity) AS quantity'),
                'surcharge_service.name',
                'surcharge_service.per',
                'store_surcharge.value as fee',
                'sale_order_item.external_id',
            )
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_surcharge', 'sale_order_item_surcharge.order_item_id', '=', 'sale_order_item.id')
            ->join('store_surcharge', 'store_surcharge.id', '=', 'sale_order_item_surcharge.surcharge_id')
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->whereIn('sale_order.id', $saleOrderIds)
            ->whereIn('surcharge_service.api_value', [
                SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES,
                SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES,
                SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES,
                SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES,
            ])
            ->groupBy('sale_order.id', 'order_item_id', 'surcharge_service.id')
            ->get();
    }
}
