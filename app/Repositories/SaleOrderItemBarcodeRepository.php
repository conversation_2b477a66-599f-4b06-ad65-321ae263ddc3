<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\Store;
use App\Models\Warehouse;
use Illuminate\Http\Request;

class SaleOrderItemBarcodeRepository extends CommonRepository
{
    public function getList(Request $request, $getTotal = false)
    {
        $query = SaleOrderItemBarcode::with([
            'saleOrder',
            'saleOrder.shipmentDefault:id,order_id,tracking_number,tracking_status,provider'
        ]);

        if (!empty($request['shipment_status'])) {
            $query->whereHas('saleOrder.shipmentDefault', function ($q) use ($request) {
                $q->where('tracking_status', $request['shipment_status']);
            });
        }

        $query = $query->search($request);

        if ($getTotal) {
            return ['total' => $query->count()];
        }

        $saleOrderItemBarCodes = $query->orderBy($this->sortColumn, $this->sortBy)
            ->simplePaginate($this->limit);
        $employees = Employee::all();
        $stores = Store::all();
        $warehouses = Warehouse::all();
        $convertEmployees = [];

        foreach ($employees as $employee) {
            $convertEmployees[$employee->id] = $employee->name;
        }

        $convertStores = [];
        $codeStore = [];

        foreach ($stores as $store) {
            $convertStores[$store->id] = $store->name;
            $codeStore[$store->id] = $store->code;
        }

        $convertWarehouses = [];

        foreach ($warehouses as $warehouse) {
            $convertWarehouses[$warehouse->id] = $warehouse->name;
        }

        foreach ($saleOrderItemBarCodes as $saleOrderItemBarcode) {
            $saleOrderItemBarcode->pulled_by = !empty($saleOrderItemBarcode->employee_pull_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_pull_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_pull_id] : '';
            $saleOrderItemBarcode->pretreated_by = !empty($saleOrderItemBarcode->employee_pretreat_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_pretreat_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_pretreat_id] : '';
            $saleOrderItemBarcode->printed_by = !empty($saleOrderItemBarcode->employee_print_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_print_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_print_id] : '';
            $saleOrderItemBarcode->qc_by = !empty($saleOrderItemBarcode->employee_qc_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_qc_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_qc_id] : '';
            $saleOrderItemBarcode->staged_by = !empty($saleOrderItemBarcode->employee_staging_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_staging_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_staging_id] : '';
            $saleOrderItemBarcode->kitted_by = !empty($saleOrderItemBarcode->employee_kitted_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_kitted_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_kitted_id] : '';
            $saleOrderItemBarcode->folded_by = !empty($saleOrderItemBarcode->employee_folding_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_folding_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_folding_id] : '';
            $saleOrderItemBarcode->shipped_by = !empty($saleOrderItemBarcode->employee_ship_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_ship_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_ship_id] : '';
            $saleOrderItemBarcode->reprinted_by = !empty($saleOrderItemBarcode->employee_reprint_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_reprint_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_reprint_id] : '';
            $saleOrderItemBarcode->pressed_by = !empty($saleOrderItemBarcode->employee_press_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_press_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_press_id] : '';
            $saleOrderItemBarcode->store = !empty($saleOrderItemBarcode->store_id) && isset($convertStores[$saleOrderItemBarcode->store_id]) ? $convertStores[$saleOrderItemBarcode->store_id] : '';
            $saleOrderItemBarcode->store_code = !empty($saleOrderItemBarcode->store_id) && isset($codeStore[$saleOrderItemBarcode->store_id]) ? $codeStore[$saleOrderItemBarcode->store_id] : '';
            $saleOrderItemBarcode->warehouse = !empty($saleOrderItemBarcode->warehouse_id) && isset($convertWarehouses[$saleOrderItemBarcode->warehouse_id]) ? $convertWarehouses[$saleOrderItemBarcode->warehouse_id] : '';
            $saleOrderItemBarcode->print_label = !empty($saleOrderItemBarcode->barcode_printed_employee_id) && isset($convertEmployees[$saleOrderItemBarcode->barcode_printed_employee_id]) ? $convertEmployees[$saleOrderItemBarcode->barcode_printed_employee_id] : '';
        }

        return $saleOrderItemBarCodes;
    }

    public function getBarcodeForLineProductionByOrderIds($orderIds): array
    {
        return SaleOrderItemBarcode::query()->whereIn('order_id', $orderIds)
            ->where('is_deleted', 0)
            ->select('order_id',
                'print_barcode_at',
                'pulled_at',
                'pretreated_at',
                'scanned_at',
                'printed_at',
                'qc_at',
                'staged_at',
                'folded_at',
                'shipped_at')
            ->get()
            ->toArray();
    }

    public function updateBarcodeEmployeeFoldingBySaleOrderId($employeeId, $saleOrderId)
    {
        $dataUpdate = [
            'employee_folding_id' => $employeeId,
            'folded_at' => date('Y-m-d H:i:s')
        ];

        return SaleOrderItemBarcode::where('order_id', $saleOrderId)
            ->where('is_deleted', '=', 0)
            ->update($dataUpdate);
    }

    public static function pendingDetectPrintMethod($limit = 10)
    {
        $saleOrderItemBarcodeModel = new SaleOrderItemBarcode();
        $q = $saleOrderItemBarcodeModel->select('id')
            ->where('id', '>=', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)) // 2023-08-01
            ->whereNull('print_method');

        return $q->orderBy('id', 'ASC')
            ->take($limit)
            ->pluck('id');
    }

    public function getByLabelId($labelId)
    {
        return SaleOrderItemBarcode::where('label_id', $labelId)->first();
    }

    public function getSaleOrderItemBarcodeByBarcodePrintedId($barcodePrintedId)
    {
        return SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrintedId)
            ->where('is_deleted', 0)
            ->get();
    }
}
