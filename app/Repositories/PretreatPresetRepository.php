<?php


namespace App\Repositories;

use App\Imports\PretreatPresetImport;
use App\Models\PretreatPreset;
use App\Models\ProductColor;
use App\Models\ProductStyle;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Response;

class PretreatPresetRepository extends CommonRepository
{
    public function fetch($params)
    {
        $query = PretreatPreset::search($params);
//            ->with(['productStyle:id,sku,name', 'productColor:id,sku,name']);

        return $query->orderBy('created_at', 'DESC')->paginate($params['limit'] ?? self::LIMIT);
    }

    public function fetchAll()
    {
        return PretreatPreset::all();
    }

    public function create($params)
    {
        $pretreatPreset = PretreatPreset::create($params);

        return $this->successResponse('Create pretreat preset successfully!', $pretreatPreset);
    }

    public function update($id, $dataUpdate)
    {
        $pretreatPreset = PretreatPreset::where('id', $id)->update($dataUpdate);

        return $this->successResponse('Update pretreat preset successfully!', $pretreatPreset);
    }

    public function verifyCsvFile($input)
    {
        $data = Excel::toArray(new PretreatPresetImport, $input->file)[0];
        $invalidData = [];
        $validData = [];

        $dataString = json_encode($data);
        foreach ($data as $key => $value) {
            $value = array_combine(['preset_name', 'density', 'cure_time', 'cure_temperature', 'press_time', 'press_temperature', 'pressure', 'print_cure_time', 'print_cure_temperature'], $value);
            //check duplicate

            // check value empty
            if (in_array(null, $value, true)) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Invalid_data',
                ];
                continue;
            }

            //check number value
            $item = $value;
            if (count(array_filter(array_slice($item, 1), 'is_numeric')) < 8) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Data type mismatch',
                ];
                continue;
            }

            if ($value['density'] < 0.01) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Density must be greater than or equal to 0.01.',
                ];
                continue;
            }

            if ($value['cure_temperature'] < 32) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Cure temperature must be greater than or equal to 32.',
                ];
                continue;
            }
            if ($value['print_cure_temperature'] < 32) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Print cure temperature must be greater than or equal to 32.',
                ];
                continue;
            }
            if ($value['press_temperature'] < 32) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Press temperature must be greater than or equal to 32.',
                ];
                continue;
            }
            if ($value['print_cure_time'] < 1) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Print cure time must be greater than or equal to 1.',
                ];
                continue;
            }
            // if ($value['print_time'] < 1) {
            //     $invalidData[] = [
            //         'row' => $value,
            //         'reason' => 'Print time must be greater than or equal to 1.',
            //     ];
            //     continue;
            // }
            if ($value['cure_time'] < 1) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Cure time must be greater than or equal to 1.',
                ];
                continue;
            }
            if ($value['press_time'] < 1) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Press time must be greater than or equal to 1.',
                ];
                continue;
            }
            if ($value['pressure'] < 1) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Pressure must be greater than or equal to 1.',
                ];
                continue;
            }
            $validData[] = $value;
        }

        return [
            'valid' => $validData,
            'invalid' =>  $invalidData,
            'count_valid' => count($validData),
            'count_invalid' => count($invalidData),
            'total' => count($validData) + count($invalidData),
        ];
    }

    public function importCsv($input)
    {
        $res = $this->verifyCsvFile($input);

        $data = $res['valid'] ?? [];
        if (!$data) return $res;

        try {
            DB::beginTransaction();
            PretreatPreset::upsert(
                $data,
                ['preset_name'],
                ['density', 'cure_time', 'cure_temperature', 'press_time', 'press_temperature', 'pressure', 'print_cure_time', 'print_cure_temperature'],
            );
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return response(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response(['message' => 'Import pretreat preset successfully!']);
    }

}
