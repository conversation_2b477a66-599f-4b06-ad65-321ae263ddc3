<?php

namespace App\Repositories;

use App\Console\Commands\ImportStoreProductPricing;
use App\Models\PricingHistory;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductPromotion;
use App\Models\ProductQuantity;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\Promotion;
use App\Models\SaleOrder;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StorePricingHistory;
use App\Models\StoreProduct;
use App\Models\StorePromotion;
use App\Models\StoreShipment;
use App\Models\StoreStyle;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use Carbon\Carbon;
use function GuzzleHttp\Promise\all;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class StoreProductRepository extends CommonRepository
{
    private string $typeErrorFile = 'excel';

    private string $typeErrorData = 'product';

    private string $prefix = 'pricing/history';

    const IMPORT_TYPE = 'import-pricing';

    const UPLOAD_TYPE = 'upload-pricing';

    const STORE_USE_SHRINKAGE_COST = [Store::STORE_REDBUBBLE, Store::STORE_DASHERY, Store::STORE_TEE_PUBLIC];

    public function fetch($input)
    {
        $ppSubquery = DB::table(DB::raw('store_product_price USE INDEX (idx_store_product_area)'))
        ->select(
            'product_id',
            DB::raw('MIN(print_price) as print_price'),
        )
        ->where('product_print_area_id', '!=', 0)
        ->where('status', 1)
        ->where('store_id', Auth::id())
        ->groupBy('product_id');

        $bpSubquery = DB::table(DB::raw('store_product_price USE INDEX (idx_store_product_area)'))
            ->select(
                'product_id',
                'price as blank_price',
                'handling_fee',
            )
            ->where('product_print_area_id', 0)
            ->where('status', 1)
            ->where('store_id', Auth::id())
            ->groupBy('product_id');

        $dataPrice = DB::table('product as p')
            ->leftJoinSub($ppSubquery, 'pp', function ($join) {
                $join->on('p.id', '=', 'pp.product_id');
            })
            ->leftJoinSub($bpSubquery, 'bp', function ($join) {
                $join->on('p.id', '=', 'bp.product_id');
            })
            ->select(
                'p.parent_id',
                DB::raw('COALESCE(pp.print_price, 0) + COALESCE(bp.blank_price, 0) + COALESCE(bp.handling_fee, 0) as min_price'),
            )
            ->groupBy('p.parent_id')
            ->get();

        if (count($dataPrice) == 0) {
            return [];
        }

        $dataPriceMin = [];
        foreach ($dataPrice as $key => $item) {
            if (isset($dataPriceMin[$item->parent_id])) {
                $dataPriceMin[$item->parent_id] = $dataPriceMin[$item->parent_id] < $item->min_price ? $dataPriceMin[$item->parent_id] : $item->min_price;
            } else {
                $dataPriceMin[$item->parent_id] = $item->min_price;
            }
        }
        $query = Product::select(['product.id', 'product.parent_id', 'brand.name as brand_name', 'product_style.description as product_style_description'])
            ->leftJoin('brand', 'product.brand_id', '=', 'brand.id')
            ->leftJoin(
                DB::raw('(select product_id, sum(quantity + incoming_stock) as stock from product_quantity group by product_id) as B'),
                function ($join) {
                    $join->on('product.id', '=', 'B.product_id');
                },
            )
            ->with([
                'productParent.brand:id,name',
                'productParent:id,name,style,size,color,image,brand_id'
            ])
            ->join('store_style', 'product.style', '=', 'store_style.style')
            ->join('product_style', 'product.style', '=', 'product_style.name')
            ->join('store_product_price', 'product.id', '=', 'store_product_price.product_id')
            ->where('store_style.status', StoreStyle::STATUS_ACTIVE)
            ->where('store_product_price.status', StoreStyle::STATUS_ACTIVE)
            ->where('store_style.store_id', Auth::id())
            ->where('store_product_price.store_id', Auth::id())
            ->whereIn('product.parent_id', array_keys($dataPriceMin))
            ->groupBy('product.parent_id');

        if (!empty($input['brand'])) {
            $query = $query->leftJoin('product as parent', 'product.parent_id', '=', 'parent.id')
                ->leftJoin('brand as brand_parent', 'parent.brand_id', '=', 'brand_parent.id')
                ->where('brand_parent.name', 'LIKE', '%' . $input['brand'] . '%');
        }

        if (!empty($input['style'])) {
            $query = $query->where('product.style', 'LIKE', '%' . $input['style'] . '%');
        }

        if (!empty($input['popular']) && ($input['popular'] === 'DESC' || $input['popular'] === 'ASC')) {
            $query = $query->orderBy('product.is_popular', $input['popular']);
        }

        if (!empty($input['new']) && ($input['new'] === 'DESC' || $input['new'] === 'ASC')) {
            $query = $query->orderBy('product.parent_id', $input['new']);
        }

        if (!empty($input['sku'])) {
            $query = $query->where('product.sku', 'LIKE', '%' . $input['sku'] . '%');
        }

        if (!empty($input['type'])) {
            $query->where('product_style.type', $input['type']);
        }

        $productColors = ProductColor::all();

        return $query->get()->map(function ($product) use ($dataPriceMin, $productColors) {
            if (!isset($dataPriceMin[$product->parent_id])) {
                return false;
            }
            $product->blank = $dataPriceMin[$product->parent_id] ? $dataPriceMin[$product->parent_id] : 0;
            if (isset($product->productParent)) {
                $colorParent = explode(',', strtoupper($product->productParent->color));
                $product->productParent->color_code = $productColors->filter(function ($color) use ($colorParent) {
                    return $color->color_code && in_array(strtoupper($color->name), $colorParent);
                })->pluck('color_code', 'name');
                $product->productParent->image = env('AWS_S3_URL', '') . '/' . $product->productParent->image;
            }
            $product->promotion = null;

            return $product;
        });
    }

    public function getList($request)
    {
        $storeId = $request['store_id'];
        $styles = StoreProduct::select('product_id')->with('product')->where('store_id', $storeId)
            ->get()->pluck('product.style')->unique();
        $products = Product::select('id', 'style', 'name', 'size', 'brand_id', 'sku')->whereIn('style', $styles)
            ->with(['storeStyle' => function ($q) use ($storeId) {
                $q->where('store_id', $storeId)->where('type', StoreStyle::TYPE_PRODUCT);
            }, 'brand'])
            ->parent()
            ->get();

        foreach ($products as $product) {
            $product->status = $product->storeStyle ? $product->storeStyle->status : 0;
        }

        return $products;
    }

    public function getData($request)
    {
        $result = [];
        $storeId = $request['store_id'];
        $style = ProductStyle::where('name', $request['style'])->first();
        $data = StoreProduct::select('product_id', 'price', 'print_price', 'handling_fee', 'status', 'product_print_area_id', 'print_surcharge')->where('store_id', $storeId)
            ->with(['productPrintArea' => function ($query) use ($style) {
                $query->whereHas('productStyle', function ($query) use ($style) {
                    $query->where('id', $style->id);
                })->select('id', 'name');
            }, 'product' => function ($query) use ($request) {
                $query->select('color', 'size', 'id', 'sku', 'style')->where('style', $request['style']);
            }])->get();

        $prints = ProductPrintSide::pluck('name')->unique();

        $data->each(function ($item) use (&$result, $prints, $storeId) {
            if ($item->product?->sku) {
                if ($item->product_print_area_id === 0) {
                    $result[$item->product->sku]['sku'] = $item->product->sku;
                    $result[$item->product->sku]['price'] = $item->price;
                    in_array($storeId, self::STORE_USE_SHRINKAGE_COST)
                    ? $result[$item->product->sku]['shrinkage_cost'] = $item->handling_fee
                    : $result[$item->product->sku]['handling_fee'] = $item->handling_fee;
                    $result[$item->product->sku]['print_surcharge'] = $item->print_surcharge;
                    $result[$item->product->sku]['color'] = $item->product->color;
                    $result[$item->product->sku]['size'] = $item->product->size;
                    $result[$item->product->sku]['status'] = $item->status;
                } else {
                    foreach ($prints as $print) {
                        if ($print == $item->productPrintArea?->name) {
                            $result[$item->product->sku][$print] = $item->print_price;
                            break;
                        }
                    }
                }
            }
        });
        foreach ($result as $key => $row) {
            foreach ($prints as $print) {
                if (!isset($row[$print])) {
                    $result[$key][$print] = '';
                }
            }
        }

        return array_values($result);
    }

    public function setStatus($sku, $status, $storeId)
    {
        try {
            $product = Product::select('id', 'sku')->where('sku', $sku)->firstOrFail();
            StoreProduct::where('product_id', $product->id)->where('store_id', $storeId)->update(['status' => $status]);

            return [
                'success' => true,
                'message' => 'Update status of store product successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getStore()
    {
        return Store::has('storeProducts')->get();
    }

    public function createOrUpdate($id, $input)
    {
        $store = Store::find($id);

        $data = $this->transformData($input);

        return StoreProduct::insert($data);
    }

    public function deleteFromStoreAndStyle($store, $style)
    {
        return StoreProduct::where('store_id', $store)
            ->whereHas('product', function ($query) use ($style) {
                $query->where('style', $style);
            })->delete();
    }

    public function transformData($data)
    {
        $result = [];
        foreach ($data['product'] as $item) {
            $element['product_id'] = $item['product_id'];
            $element['print_price'] = 0;
            $element['product_print_area_id'] = null;
            $element['store_id'] = $data['store_id'];
            $element['status'] = $data['status'];
            $element['price'] = $item['price'];
            $element['handling_fee'] = $item['handling_fee'];
            array_push($result, $element);
            foreach ($item['print_areas'] as $area) {
                $elementWithArea = $element;
                $elementWithArea['price'] = 0;
                $elementWithArea['handling_fee'] = 0;
                $elementWithArea['product_print_area_id'] = $area['id'];
                $elementWithArea['print_price'] = $area['print_price'];
                array_push($result, $elementWithArea);
            }
        }

        return $result ?? [];
    }

    public function getPricingDetail($id)
    {
        $storeId = Auth::id();
        $pricingDetail = Product::select(['product.id', 'product.style', 'product.sku', 'product.size', 'product.color', 'product.brand_id', 'product.image'])
            ->with([
                'brand:id,name',
                'product_variants' => function ($query) use ($storeId) {
                    $query->select(['id', 'parent_id', 'sku', 'is_discontinued'])
                        ->with([
                            'productQuantities' => function ($q) {
                                $q->select('id', 'product_id', DB::raw('SUM(incoming_stock) as incoming_stock'), DB::raw('SUM(quantity) as quantity'))
                                    ->groupBy('product_id');
                            },
                            'storeProducts' => function ($storeQuery) use ($storeId) {
                                $storeQuery->select(['id', 'product_id', 'price', 'handling_fee'])
                                    ->where('store_id', $storeId)
                                    ->where('product_print_area_id', 0)
                                    ->where('status', StoreProduct::STATUS_ACTIVE);
                            }
                        ])
                        ->withCount([
                            'storeProducts' => function ($storeCountQuery) use ($storeId) {
                                $storeCountQuery->where('store_id', $storeId)
                                    ->where('status', StoreProduct::STATUS_ACTIVE);
                            }
                        ]);
                },
                'productStyle' => function ($queryProductStyle) use ($storeId) {
                    $queryProductStyle->select(['id', 'name', 'type', 'description'])
                        ->with([
                            'storeShippingPrices' => function ($queryProductType) use ($storeId) {
                                $queryProductType->select(['price', 'addition_price', 'size', 'destination', 'product_type', 'service_type'])
                                    ->where('store_id', $storeId)
                                    ->where('status', true);
                            }
                        ]);
                }
            ])
            ->where('parent_id', 0)
            ->findOrFail($id);

        $productVariants = $pricingDetail->product_variants->filter(fn ($item) => $item->store_products_count > 0)->values();

        if (count($productVariants) == 0) {
            return response()->json([
                'status' => false,
                'message' => 'Pricing Not Found',
            ], Response::HTTP_NOT_FOUND);
        }

        unset($pricingDetail->product_variants);
        $pricingDetail->product_variants = $productVariants;

        $pricingDetail->color_info = ProductColor::whereIn('name', explode(',', $pricingDetail->color))
            ->select('color_code', 'sku', 'name')
            ->get();

        $pricingDetail->size_info = ProductSize::whereIn('name', explode(',', $pricingDetail->size))
            ->select(['name', 'sku'])
            ->get();

        $pricingDetail->min_value = StoreProduct::select([
            'product_print_area.name',
            DB::raw('MIN(store_product_price.print_price) as min_value'),
            DB::raw('COUNT(DISTINCT store_product_price.print_price) as price_count'),
        ])
        ->leftJoin('product_print_area', 'store_product_price.product_print_area_id', '=', 'product_print_area.id')
        ->leftJoin('product', 'store_product_price.product_id', '=', 'product.id')
        ->whereNotNull('store_product_price.product_print_area_id')
        ->where('product.parent_id', $id)
        ->where('store_product_price.status', StoreProduct::STATUS_ACTIVE)
        ->where('store_product_price.store_id', $storeId)
        ->groupBy('product_print_area.name')
        ->get();

        $pricingDetail->image = env('AWS_S3_URL', '') . '/' . $pricingDetail->image;

        if (isset($pricingDetail->productStyle) && isset($pricingDetail->productStyle->storeShippingPrices)) {
            $shippingMethod = ShippingMethod::where('store_id', $storeId)
                ->where('api_shipping_method', '!=', [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_STANDARD])
                ->with([
                    'shippingCarrierService:id,display_name',
                    'shippingCarrier:id,name'
                ])
                ->get();

            foreach ($pricingDetail->productStyle->storeShippingPrices as $key => $value) {
                $method = $shippingMethod->first(function ($item) use ($value) {
                    return strtolower($item->api_shipping_method) === strtolower($value->service_type);
                });
                if (!empty($method) && (isset($method->shippingCarrier) || isset($method->shippingCarrierService))) {
                    $value->service_type = $method->shippingCarrier?->name . ' ' . $method->shippingCarrierService?->display_name;
                }
            }
        }

        return $pricingDetail;
    }

    public function getDataPromotion($parentProductStore)
    {
        $dataSku = Product::whereIn('parent_id', $parentProductStore)
            ->get()
            ->groupBy(function ($item) {
                return $item->parent_id;
            })->map(function ($group) {
                return $group->map(function ($item) {
                    return $item->sku;
                });
            });

        $storePromotions = StorePromotion::select()
            ->with([
                'promotion.productPromotions' => function ($q) {
                    $q->where('product_promotion.status', ProductPromotion::STATUS_ACTIVE);
                },
                'promotion.productPromotions.product:id,parent_id,sku'
            ])
            ->join('promotion', 'promotion.id', '=', 'store_promotion.promotion_id')
            ->where('store_promotion.store_id', Auth::id())
            ->where('store_promotion.status', StorePromotion::STATUS_ACTIVE)
            ->where('promotion.status', Promotion::STATUS_ACTIVE)
            ->where(function ($q) {
                $q->where('promotion.start_date', '<=', Carbon::now()->toDateString())
                    ->where('promotion.end_date', '>=', Carbon::now()->toDateString())
                    ->orWhereNull('promotion.start_date');
            })
            ->orderBy('promotion.apply_all_sku')
            ->get();
        $dataSelectStoreByParentId = $this->getDataSelectStorePromotionList($storePromotions, $parentProductStore, 'parent_id');
        $dataSelectStoreSelectSku = $dataSelectStoreByParentId['dataSelectStoreSelectSku']; // select store - select sku
        $dataSelectStoreAllSku = $dataSelectStoreByParentId['dataSelectStoreAllSku']; // select store - all sku
        $dataSkuForParentIdOfSelect = $dataSelectStoreByParentId['dataSkuForParentIdOfSelect'];

        foreach ($dataSku as $key => $value) {
            if (isset($dataSkuForParentIdOfSelect[$key]) && $value->count() == count($dataSkuForParentIdOfSelect[$key])) {
                $dataSelectStoreAllSku[$key] = $dataSelectStoreSelectSku[$key];
            }
        }

        if (!empty($dataSelectStoreAllSku)) {
            return $this->mergeDataSelectStore($dataSelectStoreSelectSku, $dataSelectStoreAllSku);
        }
        $dataSelectStore = $this->mergeDataSelectStore($dataSelectStoreSelectSku, $dataSelectStoreAllSku);

        $storeAllPromotion = Promotion::select(['id', 'discount', 'name', 'apply_all_store', 'apply_all_sku'])
            ->with([
                'productPromotions' => function ($q) {
                    $q->where('product_promotion.status', ProductPromotion::STATUS_ACTIVE);
                },
                'productPromotions.product' => function ($q) use ($parentProductStore) {
                    $q->select(['id', 'parent_id', 'sku'])
                        ->whereIn('product.parent_id', $parentProductStore);
                }
            ])
            ->where('apply_all_store', Promotion::ALL_STORE)
            ->where('status', Promotion::STATUS_ACTIVE)
            ->where(function ($q) {
                $q->where('start_date', '<=', Carbon::now()->toDateString())
                    ->where('end_date', '>=', Carbon::now()->toDateString())
                    ->orWhereNull('start_date');
            })
            ->orderBy('apply_all_sku')
            ->get();

        return $this->getDataAllStorePromotionList($storeAllPromotion, $parentProductStore, $dataSelectStore, 'parent_id');
    }

    public function getDetailProductPromotion($parentId)
    {
        $products = Product::where('parent_id', $parentId)
            ->get()
            ->pluck('sku');

        $storePromotionsActive = StorePromotion::select()
            ->with([
                'promotion.productPromotions' => function ($q) {
                    $q->where('product_promotion.status', ProductPromotion::STATUS_ACTIVE);
                },
                'promotion.productPromotions.product' => function ($q) use ($parentId) {
                    $q->select(['id', 'parent_id', 'sku'])
                        ->where('product.parent_id', $parentId);
                }
            ])
            ->join('promotion', 'promotion.id', '=', 'store_promotion.promotion_id')
            ->where('promotion.status', Promotion::STATUS_ACTIVE)
            ->where('store_promotion.store_id', Auth::id())
            ->where('store_promotion.status', true)
            ->where(function ($q) {
                $q->where('promotion.start_date', '<=', Carbon::now()->toDateString())
                    ->where('promotion.end_date', '>=', Carbon::now()->toDateString())
                    ->orWhereNull('promotion.start_date');
            })
            ->orderBy('promotion.apply_all_sku')
            ->get();

        $dataSelectStoreBySku = $this->getDataSelectStorePromotion($storePromotionsActive, $products, 'sku');
        $dataSelectStoreSelectSku = $dataSelectStoreBySku['dataSelectStoreSelectSku']; // select store - select sku
        $dataSelectStoreAllSku = $dataSelectStoreBySku['dataSelectStoreAllSku']; // select store - all sku

        $storePromotionsInactive = StorePromotion::select()
            ->with([
                'promotion.productPromotions' => function ($q) {
                    $q->where('product_promotion.status', ProductPromotion::STATUS_ACTIVE);
                },
                'promotion.productPromotions.product' => function ($q) use ($parentId) {
                    $q->select(['id', 'parent_id', 'sku'])
                        ->where('product.parent_id', $parentId);
                }
            ])
            ->join('promotion', 'promotion.id', '=', 'store_promotion.promotion_id')
            ->where('promotion.status', Promotion::STATUS_ACTIVE)
            ->where('store_promotion.store_id', Auth::id())
            ->where('store_promotion.status', false)
            ->where(function ($q) {
                $q->where('promotion.start_date', '<=', Carbon::now()->toDateString())
                    ->where('promotion.end_date', '>=', Carbon::now()->toDateString())
                    ->orWhereNull('promotion.start_date');
            })
            ->orderBy('promotion.apply_all_sku')
            ->get();
        $storePromotionsInactiveRefactor = $this->getDataStorePromotionActiveAndInactive($storePromotionsInactive, $products, $dataSelectStoreSelectSku, $dataSelectStoreAllSku, 'sku');
        $dataSelectStoreSelectSku = $storePromotionsInactiveRefactor['dataSelectStoreSelectSku']; // select store - select sku
        $dataSelectStoreAllSku = $storePromotionsInactiveRefactor['dataSelectStoreAllSku']; // select store - all sku
        $dataSelectStore = $dataSelectStoreSelectSku + $dataSelectStoreAllSku;

        if (!empty($dataSelectStoreAllSku)) {
            return $dataSelectStore;
        }

        $storeAllPromotion = Promotion::select(['id', 'discount', 'name', 'apply_all_store', 'apply_all_sku'])
            ->with([
                'productPromotions' => function ($q) {
                    $q->where('product_promotion.status', ProductPromotion::STATUS_ACTIVE);
                },
                'productPromotions.product' => function ($q) use ($parentId) {
                    $q->select(['id', 'parent_id', 'sku'])
                        ->where('product.parent_id', $parentId);
                }
            ])
            ->where('apply_all_store', Promotion::ALL_STORE)
            ->where('status', Promotion::STATUS_ACTIVE)
            ->where(function ($q) {
                $q->where('start_date', '<=', Carbon::now()->toDateString())
                    ->where('end_date', '>=', Carbon::now()->toDateString())
                    ->orWhereNull('start_date');
            })
            ->orderBy('apply_all_sku')
            ->get();

        return $this->getDataAllStorePromotion($storeAllPromotion, $products, $dataSelectStore, 'sku');
    }

    /**
     * @return array[]
     */
    private function getDataSelectStorePromotion($storePromotions, $dataProducts, $typeProduct)
    {
        $dataSelectStoreSelectSku = []; // select store - select sku
        $dataSelectStoreAllSku = []; // select store - all sku
        foreach ($storePromotions as $promotion) {
            if ($promotion->apply_all_sku === 0) {
                if ($promotion->promotion->productPromotions->count() == 0) {
                    foreach ($dataProducts as $product) {
                        if (!isset($dataSelectStoreSelectSku[$product])) {
                            $dataSelectStoreAllSku[$product] = isset($dataSelectStoreAllSku[$product]) && $promotion->discount > $dataSelectStoreAllSku[$product]
                                ? $promotion->discount : (count($dataSelectStoreAllSku) > 0 && isset($dataSelectStoreAllSku[$product]) ? $dataSelectStoreAllSku[$product] : $promotion->discount);
                        }
                    }
                }
                foreach ($promotion->promotion->productPromotions as $item) {
                    if ($item->product) {
                        $dataSelectStoreSelectSku[$item->product->{$typeProduct}] = isset($dataSelectStoreSelectSku[$item->product->{$typeProduct}]) && $promotion->promotion->discount > $dataSelectStoreSelectSku[$item->product->{$typeProduct}]
                            ? $promotion->promotion->discount : (count($dataSelectStoreSelectSku) > 0 && isset($dataSelectStoreSelectSku[$item->product->{$typeProduct}]) ? $dataSelectStoreSelectSku[$item->product->{$typeProduct}] : $promotion->promotion->discount);
                    }
                }
            } else {
                foreach ($dataProducts as $product) {
                    if (!isset($dataSelectStoreSelectSku[$product])) {
                        $dataSelectStoreAllSku[$product] = isset($dataSelectStoreAllSku[$product]) && $promotion->discount > $dataSelectStoreAllSku[$product]
                            ? $promotion->discount : (count($dataSelectStoreAllSku) > 0 && isset($dataSelectStoreAllSku[$product]) ? $dataSelectStoreAllSku[$product] : $promotion->discount);
                    }
                }
            }
        }

        return [
            'dataSelectStoreSelectSku' => $dataSelectStoreSelectSku,
            'dataSelectStoreAllSku' => $dataSelectStoreAllSku
        ];
    }

    /**
     * @return array
     */
    private function getDataAllStorePromotion($storeAllPromotion, $dataProducts, $dataSelectStore, $typeProduct)
    {
        $dataAllStoreSelectSku = []; // all store - select sku
        $dataAllStoreAllSku = []; // all store - all sku
        foreach ($storeAllPromotion as $promotion) {
            if ($promotion->apply_all_sku == 0) {
                foreach ($promotion->productPromotions as $item) {
                    if (isset($item->product) && !isset($dataSelectStore[$item->product->{$typeProduct}])) {
                        $dataAllStoreSelectSku[$item->product->{$typeProduct}] = isset($dataAllStoreSelectSku[$item->product->{$typeProduct}]) && $promotion->discount > $dataAllStoreSelectSku[$item->product->{$typeProduct}]
                            ? $promotion->discount : (count($dataAllStoreSelectSku) > 0 && isset($dataAllStoreSelectSku[$item->product->{$typeProduct}]) ? $dataAllStoreSelectSku[$item->product->{$typeProduct}] : $promotion->discount);
                    }
                }
            } else {
                foreach ($dataProducts as $product) {
                    if (!isset($dataAllStoreSelectSku[$product]) && !isset($dataSelectStore[$product])) {
                        $dataAllStoreAllSku[$product] = isset($dataAllStoreAllSku[$product]) && $promotion->discount > $dataAllStoreAllSku[$product]
                            ? $promotion->discount : (count($dataAllStoreAllSku) > 0 && isset($dataAllStoreAllSku[$product]) ? $dataAllStoreAllSku[$product] : $promotion->discount);
                    }
                }
            }
        }

        return $dataSelectStore + $dataAllStoreSelectSku + $dataAllStoreAllSku;
    }

    public function setStatusStyleProduct($store_id, $style)
    {
        $style = StoreStyle::where('store_id', $store_id)->where('style', $style)->where('type', StoreStyle::TYPE_PRODUCT)->firstOrFail();
        $style->status = !$style->status;
        $style->save();

        return true;
    }

    public function pricingHistory($request)
    {
        return PricingHistory::with(['user' => function ($query) {
            $query->select('username', 'id');
        }])->where('store_id', $request->store_id)
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($history) {
                $history->created_at_pst = Carbon::parse($history->created_at)->timezone('America/Los_Angeles')->format('Y-m-d H:i:s');
                $history->effective_from = $history->effective_from
                    ? Carbon::parse($history->effective_from)->timezone('America/Los_Angeles')->format('Y-m-d H:i:s')
                    : null;

                return $history;
            });
    }

    private function getDataSelectStorePromotionList($storePromotions, $dataProducts, $typeProduct)
    {
        $dataSelectStoreSelectSku = []; // select store - select sku
        $dataSelectStoreAllSku = []; // select store - all sku
        $dataSkuForParentIdOfSelect = [];
        foreach ($storePromotions as $promotion) {
            if ($promotion->apply_all_sku === 0) {
                foreach ($promotion->promotion->productPromotions as $item) {
                    if ($item->product) {
                        $dataSelectStoreSelectSku[$item->product->{$typeProduct}] = isset($dataSelectStoreSelectSku[$item->product->{$typeProduct}]) && $promotion->promotion->discount > $dataSelectStoreSelectSku[$item->product->{$typeProduct}]
                            ? $promotion->promotion->discount : (count($dataSelectStoreSelectSku) > 0 && isset($dataSelectStoreSelectSku[$item->product->{$typeProduct}]) ? $dataSelectStoreSelectSku[$item->product->{$typeProduct}] : $promotion->promotion->discount);
                        if (!isset($dataSkuForParentIdOfSelect[$item->product->{$typeProduct}][$item->product->sku])) {
                            $dataSkuForParentIdOfSelect[$item->product->{$typeProduct}][] = $item->product->sku;
                        }
                    }
                }
            } else {
                foreach ($dataProducts as $product) {
                    $dataSelectStoreAllSku[$product] = isset($dataSelectStoreAllSku[$product]) && $promotion->discount > $dataSelectStoreAllSku[$product]
                        ? $promotion->discount : (count($dataSelectStoreAllSku) > 0 && isset($dataSelectStoreAllSku[$product]) ? $dataSelectStoreAllSku[$product] : $promotion->discount);
                }
            }
        }

        return [
            'dataSelectStoreSelectSku' => $dataSelectStoreSelectSku,
            'dataSelectStoreAllSku' => $dataSelectStoreAllSku,
            'dataSkuForParentIdOfSelect' => $dataSkuForParentIdOfSelect
        ];
    }

    private function getDataAllStorePromotionList($storeAllPromotion, $dataProducts, $dataSelectStore, $typeProduct)
    {
        if (!empty($dataSelectStore)) {
            foreach ($storeAllPromotion as $promotion) {
                if ($promotion->apply_all_sku == 0) {
                    foreach ($promotion->productPromotions as $item) {
                        if (isset($item->product)) {
                            $dataSelectStore[$item->product->{$typeProduct}] = isset($dataSelectStore[$item->product->{$typeProduct}]) && $promotion->discount > $dataSelectStore[$item->product->{$typeProduct}]
                                ? $promotion->discount : (count($dataSelectStore) > 0 && isset($dataSelectStore[$item->product->{$typeProduct}]) ? $dataSelectStore[$item->product->{$typeProduct}] : $promotion->discount);
                        }
                    }
                } else {
                    foreach ($dataProducts as $product) {
                        $dataSelectStore[$product] = isset($dataSelectStore[$product]) && $promotion->discount > $dataSelectStore[$product]
                            ? $promotion->discount : (count($dataSelectStore) > 0 && isset($dataSelectStore[$product]) ? $dataSelectStore[$product] : $promotion->discount);
                    }
                }
            }

            return $dataSelectStore;
        }

        $dataAllStoreSelectSku = []; // all store - select sku
        $dataAllStoreAllSku = []; // all store - all sku
        foreach ($storeAllPromotion as $promotion) {
            if ($promotion->apply_all_sku == 0) {
                foreach ($promotion->productPromotions as $item) {
                    if (isset($item->product) && !isset($dataSelectStore[$item->product->{$typeProduct}])) {
                        $dataAllStoreSelectSku[$item->product->{$typeProduct}] = isset($dataAllStoreSelectSku[$item->product->{$typeProduct}]) && $promotion->discount > $dataAllStoreSelectSku[$item->product->{$typeProduct}]
                            ? $promotion->discount : (count($dataAllStoreSelectSku) > 0 && isset($dataAllStoreSelectSku[$item->product->{$typeProduct}]) ? $dataAllStoreSelectSku[$item->product->{$typeProduct}] : $promotion->discount);
                    }
                }
            } else {
                foreach ($dataProducts as $product) {
                    if (!isset($dataAllStoreSelectSku[$product]) && !isset($dataSelectStore[$product])) {
                        $dataAllStoreAllSku[$product] = isset($dataAllStoreAllSku[$product]) && $promotion->discount > $dataAllStoreAllSku[$product]
                            ? $promotion->discount : (count($dataAllStoreAllSku) > 0 && isset($dataAllStoreAllSku[$product]) ? $dataAllStoreAllSku[$product] : $promotion->discount);
                    }
                }
            }
        }

        return $dataAllStoreAllSku + $dataAllStoreSelectSku;
    }

    public function mergeDataSelectStore($dataSelectStore, $dataALlStore)
    {
        if (empty($dataALlStore)) {
            return $dataSelectStore;
        }

        foreach ($dataALlStore as $key => $value) {
            if (isset($dataSelectStore[$key]) && $dataSelectStore[$key] > $value) {
                $dataALlStore[$key] = $dataSelectStore[$key];
            }
        }

        return $dataALlStore;
    }

    private function getDataStorePromotionActiveAndInactive($storePromotionsInactive, $products, $dataSelectStoreSku, $dataSelectStoreWithAllSku, $typeProduct)
    {
        $dataSelectStoreSelectSku = $dataSelectStoreSku; // select store - select sku
        $dataSelectStoreAllSku = $dataSelectStoreWithAllSku; // select store - all sku
        foreach ($storePromotionsInactive as $promotion) {
            if ($promotion->apply_all_sku === 0) {
                if ($promotion->apply_all_sku === 0) {
                    foreach ($promotion->promotion->productPromotions as $item) {
                        if ($item->product) {
                            $dataSelectStoreSelectSku[$item->product->{$typeProduct}]
                                = isset($dataSelectStoreSelectSku[$item->product->{$typeProduct}])
                                ? $dataSelectStoreSelectSku[$item->product->{$typeProduct}]
                                : $promotion->promotion->discount;
                        }
                    }
                } else {
                    foreach ($products as $product) {
                        $dataSelectStoreAllSku[$product] = isset($dataSelectStoreAllSku[$product]) && $promotion->discount > $dataSelectStoreAllSku[$product]
                            ? $promotion->discount : (count($dataSelectStoreAllSku) > 0 && isset($dataSelectStoreAllSku[$product]) ? $dataSelectStoreAllSku[$product] : $promotion->discount);
                    }
                }
            }
        }

        return [
            'dataSelectStoreSelectSku' => $dataSelectStoreSelectSku,
            'dataSelectStoreAllSku' => $dataSelectStoreAllSku
        ];
    }

    public function importStorePricing($store, $arraySheet, $file, $isImport = false, $effectiveFrom = null, $logFile = true)
    {
        if ($this->isInvalidSheet($arraySheet, $store)) {
            return $this->responseError('Excel sheet name is not correct, please check example again');
        }
        $products = !empty($arraySheet['Apparel']) ? $this->importProduct($arraySheet['Apparel'], $store->id) : [];

        if ($this->hasErrors($products)) {
            return $products;
        }

        $shipments = !empty($arraySheet['Shipping']) ? $this->importShipping($arraySheet['Shipping'], $store) : [];

        if ($this->hasErrors($shipments)) {
            return $shipments;
        }

        if ($isImport) {
            return $this->processImport($store, $products, $shipments, $file, $effectiveFrom, $logFile);
        }

        return $this->responseSuccess(false, $products, $shipments);
    }

    private function isInvalidSheet($arraySheet, $store)
    {
        return empty($arraySheet['Apparel']) && (empty($arraySheet['Shipping']) && $store->is_calculate_shipping);
    }

    private function responseError($message)
    {
        return ['errors' => [$message], 'type' => 'excel'];
    }

    private function hasErrors($data)
    {
        return isset($data['type']) && $data['type'] === 'excel' && !empty($data['errors']);
    }

    private function processImport($store, $products, $shipments, $file, $effectiveFrom, $logFile)
    {
        try {
            DB::beginTransaction();
            if ($effectiveFrom) {
                $effectiveFrom = Carbon::parse($effectiveFrom, 'America/Los_Angeles')->setTimezone('UTC');
                $this->createPricingHistory($store->id, $file, $effectiveFrom->toDateTimeString(), PricingHistory::PENDING_STATUS);
                DB::commit();

                return $this->responseSuccess(true, $products, $shipments);
            }
            if ($store->is_calculate_price && $store->payment_terms != Store::STORE_PREPAID) {
                ///Dam bao cac order duoc tao tu ngay hom qua deu phai co snapshot het moi duoc import  gia moi
                setTimezone();
                $yesterday = Carbon::yesterday('America/Los_Angeles');
                $startYesterday = $yesterday->copy()->startOfDay()->toDateTimeString();
                $endYesterday = $yesterday->copy()->endOfDay()->toDateTimeString();
                $command = app(ImportStoreProductPricing::class);
                $command->ensureSnapshotGenerated($store->id, $startYesterday, $endYesterday);
                setTimezoneDefault();
            }
            $isUpdatePrice = $this->handleImportProducts($products, $store);
            $isUpdatePrice |= $this->handleImportShipments($shipments, $store);
            if ($isUpdatePrice && $logFile) {
                $this->createPricingHistory($store->id, $file);
            }
            DB::commit();

            return $this->responseSuccess($isUpdatePrice, $products, $shipments);
        } catch (\Throwable $exception) {
            DB::rollBack();
            Log::error('Error in processImport: ' . $exception->getMessage(), [
                'store_id' => $store->id ?? null,
                'exception' => $exception
            ]);
            throw $exception;
        }
    }

    private function responseSuccess($isUpdatePrice, $products, $shipments)
    {
        return [
            'is_update_price' => $isUpdatePrice,
            'type' => $this->typeErrorData,
            'error_product' => $products['errors'] ?? [],
            'error_shipment' => $shipments['errors'] ?? [],
            'products' => $products['insert'] ?? [],
            'shipments' => $shipments['insert'] ?? []
        ];
    }

    private function createPricingHistory($storeId, $file, $effectFrom = null, $status = null)
    {
        $pathName = Storage::disk('s3')->put($this->prefix, $file);
        $pathName = str_replace($this->prefix . '/', '', $pathName);

        return PricingHistory::create([
            'user_id' => auth()->id(),
            'name' => $pathName,
            'url' => env('AWS_S3_URL', '') . '/' . $this->prefix . '/' . $pathName,
            'store_id' => $storeId,
            'effective_from' => $effectFrom,
            'status' => $status,
        ]);
    }

    private function handleImportProducts(&$products, $store)
    {
        if (empty($products['record'])) {
            return false;
        }
        $lastIdBeforeUpsert = StoreProduct::where('store_id', $store->id)->latest('id')->first();

        StoreStyle::upsert(
            $products['style'],
            ['store_id', 'style', 'type'],
            ['store_id', 'style', 'type'],
        );
        $productNaRemove = array_filter($products['record'], function ($item) {
            return strtolower($item['print_price']) == 'n/a';
        });

        foreach ($productNaRemove as $productNa) {
            StoreProduct::where('product_id', $productNa['product_id'])
                ->where('product_print_area_id', $productNa['product_print_area_id'])
                ->where('store_id', $productNa['store_id'])
                ->delete();
        }

        $productValidInsert = array_filter($products['record'], function ($item) {
            return strtolower($item['print_price']) != 'n/a';
        });

        foreach ($productValidInsert as &$item) {
            if ($item['product_print_area_id'] === 0) {
                $fieldsToUpdate = ['price', 'handling_fee', 'print_surcharge'];
                foreach ($fieldsToUpdate as $field) {
                    if (isset($item[$field]) && strtolower($item[$field]) === 'n/a') {
                        //neu la n/a thi set = 0 , chi ap dung cho 'price', 'handling_fee', 'print_surcharge'
                        $item[$field] = 0;
                    } elseif (isset($item[$field]) && $item[$field] === '') {
                        //neu empty thi giu nguyen gia tri trong DB
                        unset($item[$field]);
                    } elseif (is_null($item[$field])) {
                        //neu null thi giu nguyen gia tri trong DB
                        unset($item[$field]);
                    }
                }
            }
        }
        $updateColumns = ['price', 'print_price', 'handling_fee', 'print_surcharge'];
        $grouped = [];
        foreach ($productValidInsert as $record) {
            $columnsInItem = array_intersect(array_keys($record), $updateColumns);
            $grouped[implode(',', $columnsInItem)][] = $record;
        }
        foreach ($grouped as $key => $group) {
            foreach (array_chunk($group, 1000) as $chunk) {
                StoreProduct::upsert(
                    $chunk,
                    ['product_print_area_id', 'product_id', 'store_id'],
                    array_keys($chunk[0]),
                );
            }
        }
        // Send notify for new SKUs
        $lastIdAfterUpsert = StoreProduct::where('store_id', $store->id)->latest('id')->first();
        if ($lastIdBeforeUpsert && $lastIdBeforeUpsert->id !== $lastIdAfterUpsert->id) {
            $ids = StoreProduct::where('id', '>', $lastIdBeforeUpsert->id)
                ->where('id', '<=', $lastIdAfterUpsert->id)
                ->where('store_id', $store->id)
                ->groupBy('product_id')
                ->pluck('product_id')
                ->toArray();

            foreach ($ids as $id) {
                handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $id, 'store_id' => $store->id]);
            }
        }

        return true;
    }

    private function handleImportShipments(&$shipments, $store)
    {
        if (empty($shipments['insert'])) {
            return false;
        }
        $shipments['insert'] = array_filter($shipments['insert'], function ($item) use ($store) {
            $price = strtolower($item['price']);
            $priceAddition = strtolower($item['addition_price']);
            if ($price === 'n/a' && $priceAddition === 'n/a') {
                StoreShipment::where('store_id', $store->id)
                    ->where('product_type', $item['product_type'])
                    ->where('destination', $item['destination'])
                    ->where('service_type', $item['service_type'])
                    ->when(isset($item['size']), function ($query) use ($item) {
                        if (!empty($item['size'])) {
                            $query->where('size', $item['size']);
                        } else {
                            $query->where('size', '');
                        }
                    })
                    ->delete();

                return false;
            }

            return true;
        });
        StoreStyle::upsert(
            $shipments['style'],
            ['store_id', 'style', 'type'],
            ['store_id', 'style', 'type'],
        );
        foreach ($shipments['insert'] as $row) {
            if (is_null($row['price'])) {
                continue;
            }
            if ($row['price'] === '' && $row['addition_price'] === '') {
                continue;
            }

            $itemStoreShipment = StoreShipment::where('store_id', $store->id)
                ->where('product_type', $row['product_type'])
                ->where('destination', $row['destination'])
                ->where('service_type', $row['service_type'])
                ->where('size', $row['size'] ?? '')
                ->first();

            if ($itemStoreShipment) {
                if ($row['price'] !== '') {
                    $itemStoreShipment->price = $row['price'];
                }
                if ($row['addition_price'] !== '') {
                    $itemStoreShipment->addition_price = $row['addition_price'];
                }
                $itemStoreShipment->save();
            } else {
                StoreShipment::create(
                    [
                        'store_id' => $store->id,
                        'product_type' => $row['product_type'],
                        'destination' => $row['destination'],
                        'service_type' => $row['service_type'],
                        'size' => $row['size'],
                        'price' => !empty($row['price']) ? $row['price'] : 0,
                        'addition_price' => !empty($row['addition_price']) ? $row['addition_price'] : 0,
                    ]);
            }
        }

        return true;
    }

    private function importProduct($excels, $storeId)
    {
        $errors = [];
        $result = [];
        $inserts = [];
        $styles = [];
        $errorsProduct = [];
        $existSkus = [];
        $types = ProductPrintSide::select('name')->get()->pluck('name')->toArray();
        $hashType = [
            'SKU',
            'Blank Price',
            in_array($storeId, self::STORE_USE_SHRINKAGE_COST) ? 'Shrinkage Cost' : 'Handling Fee',
            StoreProduct::EXCEL_PRINT_SURCHARGE,
        ];
        $headerValid = array_merge($types, $hashType);
        foreach (array_keys($excels[0]) as $title) {
            if (empty($title)) {
                continue;
            }
            if (!in_array($title, $headerValid)) {
                $errors[] = "Column $title is not correct, please check example again";
            }
        }
        if (!empty($errors)) {
            return [
                'type' => $this->typeErrorFile,
                'errors' => $errors,
            ];
        }
        $productsStyle = Product::leftJoin('product_style', 'product_style.name', 'product.style')
            ->select('product.id', 'product.style', 'product.sku', 'product_style.id as product_style_id')
            ->whereNotNull('product.sku')
            ->get()->mapWithKeys(function ($item) {
                return [
                    $item->sku => [
                        'style_id' => $item->product_style_id,
                        'id' => $item->id,
                        'style' => $item->style
                    ]];
            });
        $productPrintAreas = ProductPrintArea::select('id', 'name', 'product_style_id')->get()->toArray();
        $arrProductPrintAreaSideName = [];
        $arrProductPrintAreaId = [];
        foreach ($productPrintAreas as $productPrintArea) {
            $arrProductPrintAreaId[$productPrintArea['product_style_id']][$productPrintArea['id']] = $productPrintArea['name'];
            $arrProductPrintAreaSideName[$productPrintArea['product_style_id']][$productPrintArea['name']] = true;
        }
        $products = Product::all(['sku', 'id'])->pluck('id', 'sku');
        foreach ($excels as $rowKey => $row) {
            $sku = $row[StoreProduct::EXCEL_SKU];
            $basePrice = [];
            if (empty($sku)) {
                continue;
            }

            if (in_array($sku, $existSkus)) {
                $errors[$rowKey][$sku][] = 'Duplicate SKU';
                $row['message'] = $errors[$rowKey][$sku];
                $errorsProduct[] = $row;

                continue;
            }

            if (!isset($products[$sku])) {
                $errors[$sku][] = 'Invalid SKU';
                $row['message'] = $errors[$sku];
                $errorsProduct[] = $row;

                continue;
            }

            if (!isset($productsStyle[$sku]['style_id'])) {
                $errors[$sku][] = 'SKU has style invalid';
                $row['message'] = $errors[$sku];
                $errorsProduct[] = $row;

                continue;
            }
            //done validate sku
            $existSkus[] = $sku;
            $pricingKeys = ['Blank Price', 'Shrinkage Cost', 'Handling Fee', StoreProduct::EXCEL_PRINT_SURCHARGE];
            foreach ($pricingKeys as $key) {
                if (isset($row[$key]) && !$this->isCellValidPricing($row[$key])) {
                    $errors[$sku][] = "Invalid row, $key";
                }
            }
            if (!isset($errors[$sku]) && array_intersect_key(array_flip($pricingKeys), $row)) {
                $blankPrice = $row['Blank Price'] ?? null;
                $handlingPrice = in_array($storeId, self::STORE_USE_SHRINKAGE_COST)
                    ? ($row['Shrinkage Cost'] ?? null)
                    : ($row['Handling Fee'] ?? null);
                $printSurcharge = $row[StoreProduct::EXCEL_PRINT_SURCHARGE] ?? null;
                $basePrice = [
                    'product_print_area_id' => 0,
                    'product_id' => $productsStyle[$sku]['id'],
                    'store_id' => $storeId,
                    'price' => $this->convertToNumber($blankPrice),
                    'print_price' => 0,
                    'handling_fee' => $this->convertToNumber($handlingPrice),
                    'print_surcharge' => $this->convertToNumber($printSurcharge),
                ];
            }
            $knownKeys = array_merge($pricingKeys, [StoreProduct::EXCEL_SKU]);
            $styleId = $productsStyle[$sku]['style_id'];
            $hasError = false;
            $validPrintAreas = [];
            foreach ($row as $printAreaName => $price) {
                if (!in_array($printAreaName, $knownKeys, true)) {
                    if (is_null($price) || trim($price) == '') {
                        continue;
                    }
                    $errorsArea = [];
                    // Validate pricing
                    if (!$this->isCellValidPricing($price)) {
                        $errorsArea[$sku][] = "Invalid row, $printAreaName";
                    }
                    if ($price != '') {
                        if (empty($arrProductPrintAreaSideName[$styleId])
                            || !isset($arrProductPrintAreaSideName[$styleId][$printAreaName])) {
                            $errorsArea[$sku][] = "Invalid pricing, $printAreaName";
                        }
                    }
                    if (!empty($errorsArea[$sku])) {
                        $hasError = true;
                        if (!isset($errors[$sku])) {
                            $errors[$sku] = $errorsArea[$sku];
                        } else {
                            $errors[$sku] = array_merge($errors[$sku], $errorsArea[$sku]);
                        }
                    } else {
                        $validPrintAreas[] = [
                            'product_print_area_id' => array_search($printAreaName, $arrProductPrintAreaId[$styleId], true),
                            'product_id' => $productsStyle[$sku]['id'],
                            'store_id' => $storeId,
                            'price' => 0,
                            'print_price' => $this->convertToNumber($price),
                            'handling_fee' => 0,
                            'print_surcharge' => 0,
                        ];
                    }
                }
            }

            if (!$hasError) {
                if (!empty($basePrice)) {
                    $result[] = $basePrice;
                }
                foreach ($validPrintAreas as $printArea) {
                    $result[] = $printArea;
                }
            }

            if (!empty($errors[$sku])) {
                $row['message'] = $errors[$sku];
                $errorsProduct[] = $row;
            } else {
                $inserts[] = $row;
            }
            $styles[$styleId] = [
                'style' => $productsStyle[$sku]['style'],
                'store_id' => $storeId,
                'type' => StoreStyle::TYPE_PRODUCT
            ];
        }

        return [
            'type' => $this->typeErrorData,
            'errors' => $errorsProduct,
            'insert' => $inserts,
            'record' => $result,
            'style' => array_values($styles)
        ];
    }

    private function convertToNumber($value)
    {
        if ($value == '') {
            return $value;
        }
        if (strtolower($value) === 'n/a') {
            return $value;
        }
        if (is_numeric($value)) {
            return $value;
        }

        return (float) preg_replace('/^\$/', '', $value);
    }

    private function isCellValidPricing($cell)
    {
        if (is_null($cell)) {
            return true;
        }
        if (trim($cell) === '') {
            return true;
        }
        if (strtolower($cell) === 'n/a') {
            return true;
        }
        if (strpos($cell, '$') !== false) {
            if (!preg_match('/^\$\d+(\.\d+)?$/', $cell)) {
                return false;
            }

            return true;
        }
        if (!is_numeric($cell)) {
            return false;
        }

        return true;
    }

    private function importShipping($excel, $store)
    {
        $insert = [];
        $errors = [];
        $styles = [];
        foreach (array_keys($excel[0]) as $title) {
            if (empty($title)) {
                continue;
            }
            if (!in_array($title, StoreShipment::allColumnExcel())) {
                $errors[] = "Column $title is not correct, please check example again";
            }
        }
        if (!empty($errors)) {
            return [
                'type' => $this->typeErrorFile,
                'errors' => $errors,
                'success' => []
            ];
        }
        if (!$store->is_calculate_shipping) {
            return [
                'type' => $this->typeErrorData,
                'insert' => [],
                'errors' => [],
                'style' => []
            ];
        }

        $allProductTypes = ProductType::pluck('name')->toArray();
        $allProductSizes = ProductSize::pluck('sku')->toArray();
        $allShippingMethods = ShippingMethod::pluck('api_shipping_method')->toArray();
        foreach ($excel as $row) {
            $record = [
                'store_id' => $store->id,
                'product_type' => $row[StoreShipment::EXCEL_PRODUCT_TYPE],
                'destination' => $row[StoreShipment::EXCEL_SHIPPING_TYPE],
                'service_type' => $row[StoreShipment::EXCEL_SERVICE_TYPE],
                'size' => empty($row[StoreShipment::EXCEL_SIZE]) ? '' : $row[StoreShipment::EXCEL_SIZE],
                'price' => $row[StoreShipment::EXCEL_PRICE],
                'addition_price' => $row[StoreShipment::EXCEL_PRICE_ADDITION],
            ];
            $errorRow = [];
            $invalidPricing = !$this->isCellValidPricing($row[StoreShipment::EXCEL_PRICE]);
            $invalidPriceAddition = !$this->isCellValidPricing($row[StoreShipment::EXCEL_PRICE_ADDITION]);
            if ($invalidPricing || $invalidPriceAddition) {
                if ($invalidPricing) {
                    $errorRow[] = 'Invalid row pricing';
                }
                if ($invalidPriceAddition) {
                    $errorRow[] = 'Invalid row price addition';
                }
            }
            if (
                (strtolower($row[StoreShipment::EXCEL_PRICE]) === 'n/a' && strtolower($row[StoreShipment::EXCEL_PRICE_ADDITION]) !== 'n/a')
                || (strtolower($row[StoreShipment::EXCEL_PRICE_ADDITION]) === 'n/a' && strtolower($row[StoreShipment::EXCEL_PRICE]) !== 'n/a')
            ) {
                $errorRow[] = "Invalid row, bold price and addition price must be 'n/a'";
            }

            if (!in_array($row[StoreShipment::EXCEL_SHIPPING_TYPE], ['Domestic', 'International'])) {
                $errorRow[] = 'Invalid shipping type';
            }
            if (!in_array($row[StoreShipment::EXCEL_SERVICE_TYPE], $allShippingMethods)) {
                $errorRow[] = 'Invalid service type';
            }
            if (!in_array($row[StoreShipment::EXCEL_PRODUCT_TYPE], $allProductTypes)) {
                $errorRow[] = 'Invalid product type';
            }
            if (!empty($row[StoreShipment::EXCEL_SIZE]) && !in_array($row[StoreShipment::EXCEL_SIZE], $allProductSizes)) {
                $errorRow[] = 'Invalid product size';
            }
            if ($errorRow) {
                $record['message'] = $errorRow;
                $errors[] = $record;

                continue;
            }
            $record['price'] = $this->convertToNumber($row[StoreShipment::EXCEL_PRICE]);
            $record['addition_price'] = $this->convertToNumber($row[StoreShipment::EXCEL_PRICE_ADDITION]);

            $insert[] = $record;
            $styles[$row[StoreShipment::EXCEL_PRODUCT_TYPE]] = [
                'store_id' => $store->id,
                'style' => $row[StoreShipment::EXCEL_PRODUCT_TYPE],
                'type' => StoreStyle::TYPE_SHIPMENT
            ];
        }

        return [
            'type' => $this->typeErrorData,
            'insert' => $insert,
            'errors' => $errors,
            'style' => array_values($styles)
        ];
    }

    public function fetchClonePricingHistoryById($storeId)
    {
        return StorePricingHistory::with('sourceStore')
            ->where('store_id', $storeId)
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) {
                $item->created_at_pst = Carbon::parse($item->created_at)->tz('America/Los_Angeles')->format('Y-m-d H:i:s');
                $item->effective_from = $item->effective_from
                    ? Carbon::parse($item->effective_from)->tz('America/Los_Angeles')->format('Y-m-d H:i:s')
                    : null;

                return $item;
            });
    }

    public function cloneStorePricing($request)
    {
        $sourceStoreId = $request->source_store_id;
        $targetStoreId = $request->store_id;
        $productPricing = $request->product_pricing ?? [];
        $shippingPricing = $request->shipping_pricing ?? [];
        $surchargePricing = $request->surcharge_pricing ?? [];
        $effectiveFrom = $request->effective_from ?? null;
        $isInsertStorePricing = false;

        try {
            DB::beginTransaction();
            if ($effectiveFrom) {
                $this->handleEffectiveFrom($sourceStoreId, $targetStoreId, $productPricing, $shippingPricing, $surchargePricing, $effectiveFrom);
                DB::commit();

                return [
                    'success' => true,
                    'message' => "Pricing import was successful. Effective from {$effectiveFrom} UTC",
                ];
            }
            $store = Store::find($targetStoreId);
            if ($store->is_calculate_price && $store->payment_terms != Store::STORE_PREPAID) {
                ///Dam bao cac order duoc tao tu ngay hom qua deu phai co snapshot het moi duoc import  gia moi
                setTimezone();
                $yesterday = Carbon::yesterday('America/Los_Angeles');
                $startYesterday = $yesterday->copy()->startOfDay()->toDateTimeString();
                $endYesterday = $yesterday->copy()->endOfDay()->toDateTimeString();
                $command = app(ImportStoreProductPricing::class);
                $command->ensureSnapshotGenerated($store->id, $startYesterday, $endYesterday);
                setTimezoneDefault();
            }
            $isInsertStorePricing |= $this->cloneProductPricing($sourceStoreId, $targetStoreId, $productPricing);
            $isInsertStorePricing |= $this->cloneShippingPricing($sourceStoreId, $targetStoreId, $shippingPricing);
            $isInsertStorePricing |= $this->cloneSurchargePricing($sourceStoreId, $targetStoreId, $surchargePricing);

            if ($isInsertStorePricing) {
                $this->logStorePricingHistory($sourceStoreId, $targetStoreId, $productPricing, $shippingPricing, $surchargePricing);
            }
            DB::commit();

            return [
                'success' => true,
                'message' => 'Clone pricing completed successfully.',
            ];
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('Error in cloneStorePricing: ' . $exception->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to clone pricing. Please check the logs for more details.',
                'error' => $exception->getMessage(),
            ];
        }
    }

    private function handleEffectiveFrom($sourceStoreId, $targetStoreId, $productPricing, $shippingPricing, $surchargePricing, $effectiveFrom)
    {
        $effectiveFrom = Carbon::parse($effectiveFrom, 'America/Los_Angeles')->setTimezone('UTC');
        $this->logStorePricingHistory(
            $sourceStoreId,
            $targetStoreId,
            $productPricing,
            $shippingPricing,
            $surchargePricing,
            $effectiveFrom->toDateTimeString(),
            StorePricingHistory::PENDING_STATUS,
        );
    }

    /**
     * Clone Product Pricing
     */
    private function cloneProductPricing($sourceStoreId, $targetStoreId, $productPricing)
    {
        if (empty($productPricing)) {
            return false;
        }

        $query = StoreProduct::with('product')->where('store_id', $sourceStoreId);

        if (empty($productPricing['include_all'])) {
            $query->join('product', 'product.id', '=', 'store_product_price.product_id')
                ->join('product_style', 'product_style.name', '=', 'product.style')
                ->whereIn('product_style.name', $productPricing['product_styles'])
                ->select('store_product_price.*');
        }

        $products = $query->get();
        if ($products->isEmpty()) {
            return false;
        }

        $styles = $products->pluck('product.style')->unique();
        $storeStyles = $styles->map(fn ($style) => [
            'style' => $style,
            'store_id' => $targetStoreId,
            'type' => StoreStyle::TYPE_PRODUCT,
        ])->toArray();

        StoreStyle::upsert($storeStyles, ['store_id', 'style', 'type'], ['store_id', 'style', 'type']);

        $dataToUpsert = $products->map(fn ($item) => [
            'store_id' => $targetStoreId,
            'product_id' => $item->product_id,
            'product_print_area_id' => $item->product_print_area_id,
            'price' => $item->price,
            'print_price' => $item->print_price,
            'handling_fee' => $item->handling_fee,
            'print_surcharge' => $item->print_surcharge,
            'status' => $item->status,
            'created_at' => now(),
            'updated_at' => now(),
        ])->toArray();

        foreach (array_chunk($dataToUpsert, 500) as $chunk) {
            StoreProduct::upsert($chunk, ['product_print_area_id', 'product_id', 'store_id'], ['price', 'print_price', 'handling_fee', 'print_surcharge', 'status', 'updated_at']);
        }

        return true;
    }

    /**
     * Clone Shipping Pricing
     */
    private function cloneShippingPricing($sourceStoreId, $targetStoreId, $shippingPricing)
    {
        if (empty($shippingPricing)) {
            return false;
        }

        $query = StoreShipment::where('store_id', $sourceStoreId);

        if (empty($shippingPricing['include_all'])) {
            $query->join('product_type', 'product_type.name', '=', 'store_shipping_price.product_type')
                ->whereIn('product_type.name', $shippingPricing['product_types'])
                ->select('store_shipping_price.*');
        }

        $shipments = $query->get();
        if ($shipments->isEmpty()) {
            return false;
        }

        $productTypes = $shipments->pluck('product_type')->unique();
        $storeTypes = $productTypes->map(fn ($type) => [
            'style' => $type,
            'store_id' => $targetStoreId,
            'type' => StoreStyle::TYPE_SHIPMENT,
        ])->toArray();

        StoreStyle::upsert($storeTypes, ['store_id', 'style', 'type'], ['store_id', 'style', 'type']);

        $dataToUpsert = $shipments->map(fn ($item) => [
            'store_id' => $targetStoreId,
            'product_type' => $item->product_type,
            'destination' => $item->destination,
            'service_type' => $item->service_type,
            'price' => $item->price,
            'addition_price' => $item->addition_price,
            'size' => $item->size,
            'status' => $item->status,
            'created_at' => now(),
            'updated_at' => now(),
        ])->toArray();

        foreach (array_chunk($dataToUpsert, 500) as $chunk) {
            StoreShipment::upsert($chunk, ['store_id', 'product_type', 'destination', 'service_type', 'size'], ['price', 'addition_price', 'status', 'updated_at']);
        }

        return true;
    }

    /**
     * Clone Surcharge Pricing
     */
    private function cloneSurchargePricing($sourceStoreId, $targetStoreId, $surchargePricing)
    {
        if (empty($surchargePricing)) {
            return false;
        }
        $surchargeServices = SurchargeService::query();
        if (empty($surchargePricing['include_all'])) {
            $surchargeServices->whereIn('surcharge_service.name', $surchargePricing['surcharges']);
        }
        $surchargeServices = $surchargeServices->get();
        foreach ($surchargeServices as $service) {
            SurchargeFee::where('store_id', $targetStoreId)->where('service_id', $service->id)->delete();
            $surcharge = SurchargeFee::query()
                ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
                ->where('store_surcharge.service_id', $service->id)
                ->where('store_surcharge.store_id', $sourceStoreId)
                ->select('surcharge_service.*', 'store_surcharge.value')
                ->first();
            if ($surcharge) {
                SurchargeFee::updateOrCreate(
                    [
                        'store_id' => $targetStoreId,
                        'service_id' => $surcharge->id,
                        'type' => $surcharge->api_value,
                    ],
                    [
                        'value' => $surcharge->value,
                        'updated_at' => now(),
                    ],
                );
            }
        }

        return true;
    }

    /**
     * Log Store Pricing History
     */
    private function logStorePricingHistory($sourceStoreId, $targetStoreId, $productPricing, $shippingPricing, $surchargePricing, $effectiveFrom = null, $status = null)
    {
        StorePricingHistory::create([
            'store_id' => $targetStoreId,
            'source_store_id' => $sourceStoreId,
            'product_pricing_log' => isset($productPricing['include_all']) && $productPricing['include_all'] ? 'All' : (!empty($productPricing['product_styles']) ? implode(',', $productPricing['product_styles']) : null),
            'shipping_pricing_log' => isset($shippingPricing['include_all']) && $shippingPricing['include_all'] ? 'All' : (!empty($shippingPricing['product_types']) ? implode(',', $shippingPricing['product_types']) : null),
            'surcharge_pricing_log' => isset($surchargePricing['include_all']) && $surchargePricing['include_all'] ? 'All' : (!empty($surchargePricing['surcharges']) ? implode(',', $surchargePricing['surcharges']) : null),
            'effective_from' => $effectiveFrom,
            'status' => $status,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    public function getPrintPriceDetail($id)
    {
        $product = Product::findOrFail($id);

        $priceDetail = $this->getDataPrintPriceDetail(auth()->id(), $product->style, false);
        $result['header'] = $priceDetail[0] ?? [];
        $result['data'] = array_slice($priceDetail, 1);

        return $result;
    }

    public function getDataPrintPriceDetail($storeId, $styles, $isExport = true)
    {
        $result = [];
        $styles = is_array($styles) ? $styles : [$styles];
        $queryBase = DB::table('store_product_price')
            ->join('product', 'product.id', '=', 'store_product_price.product_id')
            ->join('product_style', 'product_style.name', '=', 'product.style')
            ->leftJoin('product_print_area', 'product_print_area.id', '=', 'store_product_price.product_print_area_id')
            ->join('store_style', function ($join) use ($storeId) {
                $join->on('product.style', '=', 'store_style.style')
                    ->where('store_style.store_id', $storeId);
            })
            ->where('store_product_price.store_id', $storeId)
            ->where('store_product_price.status', StoreProduct::STATUS_ACTIVE)
            ->where('store_style.status', StoreStyle::STATUS_ACTIVE);

        if (empty($this->requestData['all_style'] ?? null)) {
            $queryBase->whereIn('product_style.name', $styles);
        }

        $queryProductHavePrintAreaAndBlank = $queryBase->clone()->select('store_product_price.product_id')
            ->where('store_product_price.product_print_area_id', '=', 0);
        $queryBase->joinSub($queryProductHavePrintAreaAndBlank, 'tmp', function ($join) {
            $join->on('tmp.product_id', '=', 'store_product_price.product_id');
        });
        $queryGetPrintAreas = $queryBase->clone()
            ->select('product_print_area.name')
            ->whereNotNull('product_print_area.name')
            ->groupBy('product_print_area.name')
            ->orderBy('product_print_area.name');
        $printAreas = $queryGetPrintAreas->get()
            ->pluck('name')
            ->toArray();
        $queryGetProducts = $queryBase->clone()
        ->selectRaw('
                store_product_price.*,
                product_print_area.name as product_print_area_name,
                product.sku,
                product.style,
                product.color,
                product.size
            ');

        if (!$isExport) {
            $queryGetProducts->selectRaw('
                product_quantity.quantity,
                product_quantity.incoming_stock,
                product.is_discontinued
            ')->leftJoin('product_quantity', 'product_quantity.product_id', '=', 'store_product_price.product_id');
        }

        if (!empty(request()->input('debug'))) {
            Log::info('CatalogPricingExport.collection query', [
                'QueryGetPrintAreas' => interpolateQuery($queryGetPrintAreas),
                'QueryGetProducts' => interpolateQuery($queryGetProducts),
            ]);
        }

        $products = $queryGetProducts->orderBy('product_style.name')->get();
        $header = ['Style', 'Color', 'Size', 'SKU', 'Blank Price', 'Handling Fee'];

        if (!$isExport) {
            $header = ['SKU', 'Color', 'Size', 'Quantity', 'Is Discontinued'];
        }

        $header = array_merge($header, $printAreas);

        foreach ($products as $item) {
            if (!isset($result[$item->sku])) {
                $result[$item->sku] = [
                    'style' => $item->style,
                    'color' => $item->color,
                    'size' => $item->size,
                    'sku' => $item->sku,
                    'blank_price' => 'n/a',
                    'handling_fee' => 'n/a',
                ];

                if (!$isExport) {
                    $result[$item->sku] = [
                        'sku' => $item->sku,
                        'color' => $item->color,
                        'size' => $item->size,
                        'quantity' => $item->quantity,
                        'incoming_stock' => $item->incoming_stock,
                        'discontinued' => $item->is_discontinued,
                        'blank_price' => 'n/a',
                        'handling_fee' => 'n/a',
                    ];
                }

                $result[$item->sku] = array_merge($result[$item->sku], array_fill_keys($printAreas, 'n/a'));
            }

            $productItem = &$result[$item->sku];

            if ($item->product_print_area_id === 0) {
                $productItem['handling_fee'] = $item->handling_fee;
                $productItem['blank_price'] = $item->price;
            } elseif (isset($productItem[$item->product_print_area_name])) {
                $productItem[$item->product_print_area_name] = $item->print_price;
            }
        }

        $result = array_values($result);
        array_unshift($result, $header);

        return $result;
    }

    public function clonePricingPending(StorePricingHistory $pending): bool
    {
        if (empty($pending->product_pricing_log)
            && empty($pending->shipping_pricing_log)
            && empty($pending->surcharge_pricing_log)) {
            return false;
        }
        try {
            DB::beginTransaction();
            $isInsertStorePricing = false;
            $cloneActions = [
                'product' => [
                    'data' => $pending->product_pricing_log,
                    'method' => 'cloneProductPricing',
                    'key' => 'product_styles',
                ],
                'shipping' => [
                    'data' => $pending->shipping_pricing_log,
                    'method' => 'cloneShippingPricing',
                    'key' => 'product_types',
                ],
                'surcharge' => [
                    'data' => $pending->surcharge_pricing_log,
                    'method' => 'cloneSurchargePricing',
                    'key' => 'surcharges',
                ]
            ];
            foreach ($cloneActions as $type => $action) {
                if (!empty($action['data'])) {
                    $pricingData = ($action['data'] === 'All')
                        ? ['include_all' => true]
                        : [$action['key'] => explode(',', $action['data'])];
                    $isInsertStorePricing |= call_user_func([$this, $action['method']],
                        $pending->source_store_id,
                        $pending->store_id,
                        $pricingData,
                    );
                }
            }
            if ($isInsertStorePricing) {
                DB::commit();

                return true;
            }
            DB::rollBack();

            return false;
        } catch (\Throwable $exception) {
            DB::rollBack();
            Log::error('error in clonePricingPending: ' . $exception->getMessage(), [
                'pending_id' => $pending->id ?? null,
                'exception' => $exception
            ]);

            return false;
        }
    }

    public function cancelImportPricing($id, $typeCancel)
    {
        if (!in_array($typeCancel, [self::IMPORT_TYPE, self::UPLOAD_TYPE])) {
            return false;
        }
        $model = ($typeCancel === self::IMPORT_TYPE) ? StorePricingHistory::class : PricingHistory::class;
        $record = $model::where('id', $id)->first();
        if (!$record || $record->status != StorePricingHistory::PENDING_STATUS) {
            return false;
        }

        return $model::where('id', $id)
                ->update(['status' => StorePricingHistory::CANCELED_STATUS]) > 0;
    }
}
