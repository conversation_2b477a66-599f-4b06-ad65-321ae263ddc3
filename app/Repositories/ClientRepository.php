<?php

namespace App\Repositories;

use App\Models\Client;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Support\Facades\Auth;

class ClientRepository extends CommonRepository
{
    public function getAll()
    {
        return Client::all();
    }

    public function getList($params)
    {
        $query = Client::with('user')->select(['id', 'name', 'username', 'created_at', 'user_id']);
        $sortBy = isset($params['sort_by']) && $params['sort_by'] == 'descending' ? 'DESC' : 'ASC';
        $sortColumn = $params['sort_column'] ?? 'name';
        if (isset($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        return $query->orderBy($sortColumn, $sortBy)->paginate($params['limit'] ?? self::LIMIT);
    }

    public function show($id)
    {
        return Client::select(['id', 'name', 'username', 'created_at'])
            ->find($id);
    }

    public function create($data)
    {
        $data['password'] = bcrypt($data['password']);
        $data['user_id'] = Auth::user()->id;

        $client = Client::create($data);
        $roleAdmin = TeamMemberRole::create([
            'name' => 'Admin',
            'client_id' => $client->id,
        ]);

        foreach (TeamMemberRolePermission::listFuction() as $function) {
            TeamMemberRolePermission::create([
                'team_member_role_id' => $roleAdmin->id,
                'function_name' => $function,
                'permission' => TeamMemberRolePermission::ALL_PERMISSION,
            ]);
        }

        return $client;
    }

    public function edit($id, $data)
    {
        if (!empty($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        }

        return Client::where('id', $id)
            ->update($data);
    }
}
