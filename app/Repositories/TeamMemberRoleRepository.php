<?php

namespace App\Repositories;

use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;

class TeamMemberRoleRepository extends CommonRepository
{
    public function getRoles($params)
    {
        setTimezone();
        $query = TeamMemberRole::query();

        $query->where('client_id', auth()->user()->client_id ?? null);

        if (isset($params['fetch_all'])) {
            return $query->get();
        }

        return $query->paginate($params['limit'] ?? self::LIMIT);
    }

    public function showRole($id)
    {
        return TeamMemberRole::with('permissions')->where('id', $id)->first();
    }

    public function create($input)
    {
        $roleData = [
            'name' => preg_replace('/\s+/', ' ', trim($input['name'])),
            'client_id' => auth()->user()->client_id,
        ];

        $role = TeamMemberRole::create($roleData);

        foreach ($input['permissions'] as $permission) {
            $permissionData = [
                'function_name' => $permission['function_name'] ?? null,
                'permission' => $permission['permission'] ?? TeamMemberRolePermission::NO_PERMISSION,
            ];
            $role->permissions()->create($permissionData);
        }

        return $role;
    }

    public function update($id, $dataUpdate)
    {
        $roleData = [
            'name' => preg_replace('/\s+/', ' ', trim($dataUpdate['name'])),
        ];

        TeamMemberRole::where('id', $id)->update($roleData);

        foreach ($dataUpdate['permissions'] as $permission) {
            $permissionData = [
                'function_name' => $permission['function_name'],
                'permission' => $permission['permission'] ?? TeamMemberRolePermission::NO_PERMISSION,
            ];

            TeamMemberRolePermission::updateOrCreate(
                [
                    'team_member_role_id' => $id,
                    'function_name' => $permission['function_name']
                ],
                $permissionData,
            );
        }

        return true;
    }

    public function delete($id)
    {
        TeamMemberRole::where('id', $id)->delete();
        TeamMemberRolePermission::where('team_member_role_id', $id)->delete();

        return true;
    }
}
