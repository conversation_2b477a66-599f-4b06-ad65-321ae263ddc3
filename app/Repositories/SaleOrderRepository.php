<?php

namespace App\Repositories;

use App\Http\Service\OrderEditingService;
use App\Imports\RerouteImport;
use App\Jobs\ShipmentRefundJob;
use App\Models\Claim;
use App\Models\Client;
use App\Models\Employee;
use App\Models\ForecastIncomingSaleOrder;
use App\Models\IntegrateLog;
use App\Models\InvoiceSaleOrder;
use App\Models\InvoiceSaleOrderError;
use App\Models\PeakShippingFee;
use App\Models\PrintMethod;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderAutomationRule;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderChangePrintMethod;
use App\Models\SaleOrderClaimSupport;
use App\Models\SaleOrderClaimSupportFeedback;
use App\Models\SaleOrderClaimSupportFeedbackFile;
use App\Models\SaleOrderClaimSupportHistory;
use App\Models\SaleOrderClaimSupportImage;
use App\Models\SaleOrderCustomerEmail;
use App\Models\SaleOrderDuplicate;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemProductImage;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderOnHold;
use App\Models\SaleOrderPrintMethodReport;
use App\Models\SaleOrderRefund;
use App\Models\SaleOrderSlaReport;
use App\Models\SaleOrderSurchargeFee;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentItem;
use App\Models\ShipmentItemLabel;
use App\Models\ShippingCarrier;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StoreAddress;
use App\Models\StoreShipment;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use App\Models\Tag;
use App\Models\TeamMember;
use App\Models\User;
use App\Models\WalletReceipt;
use App\Models\WalletTransaction;
use App\Models\Warehouse;
use App\Repositories\Contracts\SaleOrderRepositoryInterface;
use App\Repositories\Contracts\TagRepositoryInterface;
use Carbon\Carbon;
use EasyPost\Address;
use EasyPost\EasyPost;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use stdClass;
use Throwable;

class SaleOrderRepository extends CommonRepository implements SaleOrderRepositoryInterface
{
    protected $timeCheckingRepository;

    protected $tagRepository;

    public function __construct(
        TimeCheckingRepository $timeCheckingRepository,
        TagRepositoryInterface $tagRepository
    ) {
        parent::__construct();
        $this->timeCheckingRepository = $timeCheckingRepository;
        $this->tagRepository = $tagRepository;
    }

    public function getList(Request $request, $getTotal = false)
    {
        if ($getTotal) {
            $query = SaleOrder::selectId($request);

            if (count($query->getBindings()) == 3 && !empty($request['order_date_start']) && !empty($request['order_date_end'])) {
                $query->from(DB::raw((new SaleOrder())->getTable() . ' FORCE INDEX (idx_created_at)'));
            } elseif (!empty($request['warehouse_id']) && !empty($request['order_status']) && isset($request['is_test'])) {
                $query->from(DB::raw((new SaleOrder())->getTable() . ' USE INDEX (idx_created_at, idx_warehouse_id_is_test, idx_status_test)'));
            }

            $total = DB::table(DB::raw("({$query->toSql()}) as subquery"))
                ->setBindings($query->getBindings())
                ->count();

            return [
                'total' => $total,
                'filter_name' => $request->filter_name ?? '',
            ];
        }
        $query = SaleOrder::query()
            ->with([
                'store',
                'account',
                'shipmentDefault:id,order_id,tracking_number,tracking_status,provider',
                'items.licensedDesign.holder'
                //                , 'orderItemErrors:id,order_id,message'
            ])
            ->with(['address' => function ($query) {
                $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
            }])
            ->with(['shipmentCreateErrorLogLast' => function ($query) {
                $query->first();
            }])
            ->with(['sla'])
            ->select([DB::raw('sale_order.*, sale_order.order_quantity as quantity, DATE(sale_order.created_at) as order_date')])
            ->searchV2($request);

        $sortBy = (!empty($request['sort_by'])) ? $request['sort_by'] : ((!empty($request['order_status'])) && in_array($request['order_status'], [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]) ? 'asc' : 'desc');
        $sortColumn = (!empty($request['sort_column'])) ? $request['sort_column'] : ((!empty($request['order_status'])) && in_array($request['order_status'], [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]) ? 'created_at' : 'id');
        if (empty($request['order_status']) || ($request['order_status'] == SaleOrder::SHIPPED && $sortColumn == 'id')) {
            $saleOrders = $query->get();
            $saleOrders = new Paginator($saleOrders, $this->limit, $request['page'] ?? 1);
        } else {
            $saleOrders = $query->orderBy($sortColumn, $sortBy)->simplePaginate($this->limit);
        }
        foreach ($saleOrders as $saleOrder) {
            // $saleOrder->production_status = '';

            $saleOrder->created_at_utc = shiftTimezoneToUTC($saleOrder->created_at);
            $saleOrder->expired_at_utc = $saleOrder->sla?->expired_at ? shiftTimezoneToUTC($saleOrder->sla->expired_at) : null;

            // foreach (SaleOrder::PRODUCTION_STATUS as $status => $column) {
            //     if ($saleOrder[$column] === SaleOrder::ACTIVE) {
            //         $saleOrder->production_status = $status;

            //         break;
            //     }
            // }
            if (count($saleOrder->shipmentCreateErrorLogLast) > 0) {
                if (str_contains($saleOrder->shipmentCreateErrorLogLast[0]->note, '{"error":{"code":')) {
                    $saleOrder->shipmentCreateErrorLogLast[0]->note = empty(json_decode($saleOrder->shipmentCreateErrorLogLast[0]->note)->error->errors[0]->message) ? '' : json_decode($saleOrder->shipmentCreateErrorLogLast[0]->note)->error->errors[0]->message;
                }
            }
        }

        return [
            'data' => $saleOrders,
            'filter_name' => $request->filter_name ?? '',
        ];
    }

    public function getCountInvalidAddress($request)
    {
        $warehouseId = $request['warehouse_id'];

        $query = SaleOrder::whereNotIn('sale_order.order_status', [SaleOrder::STATUS_REJECT, SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_ON_HOLD, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_LATE_CANCELLED])
            ->WhereHas('shipmentCreateErrorLog')
            ->where('sale_order.is_test', SaleOrder::NOT_TEST);

        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where('sale_order.warehouse_id', $warehouseId);
        }

        if (isset($request['store_ids']) && is_array($request['store_ids'])) {
            $query->whereIn('sale_order.store_id', $request['store_ids']);
        }

        return [
            'total' => $query->count(),
        ];
    }

    public function getCount(Request $request)
    {
        if (empty(auth()->user()->is_all_store)) {
            $storeIds = auth()->user()->store_ids;
            if (empty($storeIds)) {
                $storeIds = [];
            }
        }
        $listStatusSort = [
            SaleOrder::NEW_ORDER,
            SaleOrder::IN_PRODUCTION,
            SaleOrder::ON_HOLD,
        ];
        if (isset($storeIds) && is_array($storeIds)) {
            $stores = Store::whereIn('id', $storeIds)->orderBy('name')->get(['id', 'code']);
        } else {
            $stores = Store::orderBy('name')->get(['id', 'code']);
        }
        $query = SaleOrder::where('is_test', SaleOrder::NOT_TEST);
        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where('sale_order.warehouse_id', $request['warehouse_id']);
        }
        if (!empty($request['warehouse'])) {
            $query->where('sale_order.warehouse_id', $request['warehouse']);
        }
        // where created_at > 90 day  optimize speed
        $query->where('sale_order.created_at', '>=', Carbon::now()->subDays(90)->toDateTimeString());

        $query->whereIn('order_status', $listStatusSort)
            ->groupBy(['sale_order.order_status', 'sale_order.store_id'])
            ->select(DB::raw('sale_order.order_status, sale_order.store_id as id, count(*) as total'));
        $data = $query->get();
        $listStatus = [
            SaleOrder::NEW_ORDER,
            SaleOrder::IN_PRODUCTION,
            SaleOrder::SHIPPED,
            SaleOrder::CANCELLED,
            SaleOrder::STATUS_LATE_CANCELLED,
            SaleOrder::REJECTED,
            SaleOrder::ON_HOLD,
        ];

        $result = [];
        foreach ($listStatus as $status) {
            $result[$status] = [];
            foreach ($stores as $store) {
                $result[$status][] = [
                    'id' => $store->id,
                    'code' => $store->code,
                    'total' => 0,
                ];
            }
            if (in_array($status, $listStatusSort)) {
                foreach ($data as $item) {
                    if ($item->order_status == $status) {
                        $storeHasOrder = [];
                        $storeNotHasOrder = [];
                        foreach ($result[$status] as $index => $store) {
                            if ($item->id == $store['id']) {
                                $result[$status][$index]['total'] = $item->total;
                            }
                            if ($result[$status][$index]['total'] > 0) {
                                $storeHasOrder[] = $result[$status][$index];
                            } else {
                                $storeNotHasOrder[] = $result[$status][$index];
                            }
                        }
                        if (in_array($status, $listStatusSort)) {
                            $result[$status] = array_merge($storeHasOrder, $storeNotHasOrder);
                        }
                    }
                }
            }
        }

        return $result;
    }

    public function getBarcodeItemsNotReprint(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'orderIds' => 'required|array'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }

        return SaleOrder::query()
            ->with(['barcodeItems' => function ($q) {
                $q->where('reprint_status', 0)->where('is_deleted', SaleOrderItemBarcode::ACTIVE);
            }, 'barcodeItems.claim'])
            ->whereHas('barcodeItems', function ($q) {
                $q->whereNotNull('label_id')->where('label_id', '!=', '');
            })
            ->whereIn('id', $request['orderIds'])
            ->get();
    }

    public function getBarcodeItemsNotManual(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'orderIds' => 'required|array'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }

        return SaleOrder::query()->whereIn('id', $request['orderIds'])
            ->get();
    }

    public function getBarcodeItemsNotManualByListOrderNumbers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_numbers' => 'required|array'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }

        return SaleOrder::query()->whereIn('order_number', $request['order_numbers'])
            ->orWhereIn('external_number', $request['order_numbers'])
            ->with('store')
            ->get()->each(function ($item) use ($request) {
                if (in_array($item->order_number, $request['order_numbers'])) {
                    $item->search_by_order_number = true;
                } else {
                    $item->search_by_order_number = false;
                }
            });
    }

    public function getDetail(int $id)
    {
        $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
        $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
        $data = SaleOrder::with(['account', 'addressSaleOrder', 'wareHouse', 'storeAddress', 'orderReprints', 'customerEmail', 'orderOnHold'])
            ->with(['items.itemErrors' => function ($q) {
                $q->with('printSide:code_name,name');
            }])
            ->with(['items' => function ($q) {
                $q->with([
                    'getTypeProduct:sku,type,name',
                    'product:id,style,color,size',
                    'images.imageHash',
                    'images.printSide',
                    'images.visuaDetect',
                    'images.trademarks',
                    'productImages',
                    'licensedDesign.holder',
                ]);
            }])
            ->with(['shipment' => function ($q) {
                $q->orderByDesc('id')->with('address', 'shipmentLabelLastPrinted')->with(['items' => function ($q) {
                    $q->orderByDesc('id');
                }]);
            }])
            ->with(['timeline' => function ($q) {
                $q->orderByDesc('id')->with('user:id,username', 'employee:id,name');
            }])
            ->with(['store.shippingMethods' => function ($q) {
                $q->whereNotIn('api_shipping_method', [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_FIRST_CLASS])
                    ->with('shippingCarrier:name,id', 'shippingCarrierService:display_name,id');
            }])
            ->with(['barcodeItems' => function ($q) {
                $q->where('reprint_status', SaleOrderItemBarcode::REPRINTED)
                    ->where('is_deleted', SaleOrderItemBarcode::INACTIVE)
                    ->select('id', 'order_id', 'employee_reprint_id', 'label_id', 'reprinted_at')
                    ->orderByDesc('id')
                    ->with('employeeReprint:id,name');
            }])
            ->withSum('items as quantity', 'quantity')
            ->addSelect(DB::raw('order_date as order_date_utc, DATE(sale_order.order_time) as order_date'))
            ->find($id);

        if (!$data) {
            return;
        }
        if (in_array($data->store_id, $storeExcludeCheckIP)) {
            $data->storeExcludeCheckIP = true;
        } else {
            $data->storeExcludeCheckIP = false;
        }
        if (!empty($data->shipment)) {
            $data->shipment->map(function ($item) {
                $item->created_at_pst = shiftTimezoneToPST($item->created_at)->format('Y-m-d');

                return $item;
            });
        }
        if (!empty($data->orderOnHold)) {
            $data->release_on_hold_at = $data->orderOnHold->release_on_hold_at;
        } else {
            $data->release_on_hold_at = null;
        }
        if (count($data->addressSaleOrder) == 1) {
            $dataStoreAddress = $data->storeAddress->where('type_address', StoreAddress::TYPE_ADDRESS_RETURN)->first();
            if ($dataStoreAddress) {
                $dataStoreAddress->default = true;
                $dataStoreAddress->type_address = StoreAddress::TYPE_ADDRESS_RETURN;
                $data->addressSaleOrder->push($dataStoreAddress);
            } else {
                $data->wareHouse->default = true;
                $data->wareHouse->type_address = StoreAddress::TYPE_ADDRESS_RETURN;
                $data->addressSaleOrder->push($data->wareHouse);
            }
        }
        $printSide = ProductPrintSide::all()->toArray();
        foreach ($data->items as $orderItems) {
            $options = json_decode($orderItems->options);
            $optionConvert = [];
            $errorLineItems = [];
            foreach ($options as $option) {
                $side = array_filter($optionConvert, function ($value) use ($option) {
                    return $value->name == $option->name;
                });
                $optionSplit = explode('.', $option->name);

                if (count($optionSplit) == 2 && empty($side)) {
                    $code = null;
                    $error = new \stdClass();
                    $optionName = strtolower(trim(implode('_', preg_split('/(?=[A-Z])/', str_replace(' ', '', $optionSplit[1]))), '_'));
                    foreach ($printSide as $side) {
                        // if (str_replace(' ', '_', $side['name']) == $optionSplit[1]) {
                        if ($side['code_name'] == $optionName) {
                            foreach ($orderItems->itemErrors as $itemError) {
                                if (empty($itemError->side)) {
                                    if (!in_array($itemError->type, $errorLineItems ?? [])) {
                                        $errorLineItems[] = $itemError->type;
                                    }
                                } elseif ($itemError->side == $side['code_name']) {
                                    $error->side = $side['code_name'];
                                    if (!in_array($itemError->type, $error->message ?? [])) {
                                        $error->message[] = $itemError->type;
                                    }
                                }
                            }
                            $code = $side['code'];
                            break;
                        }
                    }
                    $imageExt = $orderItems->images->where('print_side', $code)->first()?->image_ext;
                    $option->error = $error;
                    $option->print_proof_link = "thumb/proof/{$data->order_date_utc}/{$orderItems->sku}-{$code}.png";
                    if ($orderItems->getTypeProduct?->type == ProductStyle::TYPE_INSERT) {
                        $option->artwork_link = "artwork/{$data->order_date_utc}/{$orderItems->sku}-{$code}.{$imageExt}";
                    } else {
                        $option->artwork_link = "artwork/{$data->order_date_utc}/{$orderItems->sku}-{$code}.png";
                    }
                    $option->manual_mockup_link = "mockup/{$orderItems->sku}/{$code}";
                    $optionConvert[] = $option;
                }
            }
            $orderItems->options = json_encode($optionConvert);
            $orderItems->errorLineItems = $errorLineItems;
        }

        $dataShippingMethodOfStore = $data->store->shippingMethods->map(function ($item) {
            return [
                'api_shipping_method' => $item->api_shipping_method,
                'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : '',
                'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
            ];
        });

        $dataShippingMethodDefault = collect(SaleOrder::ARRAY_SHIPPING_METHOD_DEFAULT);
        $dataShippingMethodDefault = $dataShippingMethodDefault->map(function ($item) {
            return [
                'api_shipping_method' => $item,
                'shipping_carrier' => ucfirst($item),
                'shipping_carrier_service' => '',
            ];
        });
        $data->shipping_methods_store = $dataShippingMethodDefault->merge($dataShippingMethodOfStore);
        $orderInsertRepo = new SaleOrderInsertRepository();
        $data->order_insert = $orderInsertRepo->getInsert($id);
        $data->duplicate_counts = SaleOrderDuplicate::where('parent_id', $id)->count();
        if ($data->tag) {
            $tagIds = explode(',', $data->tag);
            $additionalServices = Tag::query()
                ->join('surcharge_service', 'surcharge_service.id', '=', 'tag.surcharge_service_id')
                ->where('is_additional_service', true)
                ->whereIn('tag.id', $tagIds)
                ->pluck('surcharge_service.api_value');
            if ($additionalServices->isNotEmpty()) {
                $data->additional_services = $additionalServices->toArray();
            }
        }

        return $data;
    }

    public function getOrderItems(int $id)
    {
        $order = SaleOrder::with(['items' => function ($q) {
            $q->with('barcodes', 'barcodes.partNumber:id,part_number')->orderByDesc('id');
        }])->find($id);
        if (empty($order)) {
            return [];
        }
        if (!empty($order->items)) {
            $employees = Employee::all();
            $stores = Store::all();
            $warehouses = Warehouse::all();
            $convertEmployees = [];
            foreach ($employees as $employee) {
                $convertEmployees[$employee->id] = $employee->name;
            }
            $convertStores = [];
            foreach ($stores as $store) {
                $convertStores[$store->id] = $store->name;
            }
            $convertWarehouses = [];
            foreach ($warehouses as $warehouse) {
                $convertWarehouses[$warehouse->id] = $warehouse->name;
            }
            foreach ($order->items as $item) {
                if (!empty($item->barcodes)) {
                    foreach ($item->barcodes as $saleOrderItemBarcode) {
                        $saleOrderItemBarcode->pulled_by = !empty($saleOrderItemBarcode->employee_pull_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_pull_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_pull_id] : '';
                        $saleOrderItemBarcode->pretreated_by = !empty($saleOrderItemBarcode->employee_pretreat_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_pretreat_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_pretreat_id] : '';
                        $saleOrderItemBarcode->printed_by = !empty($saleOrderItemBarcode->employee_print_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_print_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_print_id] : '';
                        $saleOrderItemBarcode->qc_by = !empty($saleOrderItemBarcode->employee_qc_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_qc_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_qc_id] : '';
                        $saleOrderItemBarcode->pressed_by = !empty($saleOrderItemBarcode->employee_press_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_press_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_press_id] : '';
                        $saleOrderItemBarcode->staged_by = !empty($saleOrderItemBarcode->employee_staging_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_staging_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_staging_id] : '';
                        $saleOrderItemBarcode->kitted_by = !empty($saleOrderItemBarcode->employee_kitted_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_kitted_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_kitted_id] : '';
                        $saleOrderItemBarcode->folded_by = !empty($saleOrderItemBarcode->employee_folding_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_folding_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_folding_id] : '';
                        $saleOrderItemBarcode->shipped_by = !empty($saleOrderItemBarcode->employee_ship_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_ship_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_ship_id] : '';
                        $saleOrderItemBarcode->reprinted_by = !empty($saleOrderItemBarcode->employee_reprint_id) && isset($convertEmployees[$saleOrderItemBarcode->employee_reprint_id]) ? $convertEmployees[$saleOrderItemBarcode->employee_reprint_id] : '';
                        $saleOrderItemBarcode->store = !empty($saleOrderItemBarcode->store_id) && isset($convertStores[$saleOrderItemBarcode->store_id]) ? $convertStores[$saleOrderItemBarcode->store_id] : '';
                        $saleOrderItemBarcode->warehouse = !empty($saleOrderItemBarcode->warehouse_id) && isset($convertWarehouses[$saleOrderItemBarcode->warehouse_id]) ? $convertWarehouses[$saleOrderItemBarcode->warehouse_id] : '';
                        $saleOrderItemBarcode->print_label = !empty($saleOrderItemBarcode->barcode_printed_employee_id) && isset($convertEmployees[$saleOrderItemBarcode->barcode_printed_employee_id]) ? $convertEmployees[$saleOrderItemBarcode->barcode_printed_employee_id] : '';
                    }
                }
            }
        }

        return $order;
    }

    private function prepareAddressData($request): array
    {
        return [
            'order_id' => $request['id'],
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'name' => $request['name'] ?? null,
            'email' => $request['email'] ?? null,
            'company' => $request['company'] ?? null,
            'phone' => $request['phone'] ?? null,
            'street1' => $request['street1'] ?? null,
            'street2' => $request['street2'] ?? null,
            'city' => $request['city'] ?? null,
            'state' => $request['state'] ?? null,
            'zip' => $request['zip'] ?? null,
            'country' => $request['country'] ?? null,
            'residential' => $request['residential'] ?? SaleOrderAddress::IS_NOT_RESIDENTIAL
        ];
    }

    public function createComment(int $id, Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }
        $comment = $this->saveHistory($id, SaleOrderHistory::CREATE_COMMENT_TYPE, $request['message']);

        return $this->handleSuccess('Create comment successfully.', $comment);
    }

    public function updateOrderStatus(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }

        $existInInvoice = InvoiceSaleOrder::where('sale_order_id', $id)->exists();
        $existInInvoiceError = InvoiceSaleOrderError::where('sale_order_id', $id)->exists();

        if ($existInInvoice || $existInInvoiceError) {
            return $this->handleFail('This order status can’t be change since it already included in an invoice.');
        }

        if ($saleOrder->is_test == 1) {
            return $this->handleFail("Can't perform this action for order test.");
        }
        $validator = Validator::make($request->all(), [
            'order_status' => 'required|max:255',
            'is_xqc' => 'required|in:0,1',
            'is_test' => 'required|in:0,1',
            'rejected_reason' => 'required_if:order_status,' . SaleOrder::REJECTED . '|in:ip_violation,invalid_artwork,invalid_address,out_of_stock,customer_changed_mind,invalid_label_url,unable_to_be_digitized,tone_on_tone'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }
        $from = $saleOrder->order_status;
        $to = $request['order_status'];
        if (
            $from != $to
            && (
                ($from == SaleOrder::SHIPPED && $to == SaleOrder::REJECTED)
                || $to == SaleOrder::CANCELLED
                || in_array($from, [SaleOrder::CANCELLED, SaleOrder::REJECTED])
                || (in_array($from, [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED]) && in_array($to, [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED]))
                || (!in_array($from, [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_SHIPPED]) && $to === SaleOrder::STATUS_LATE_CANCELLED)
                || $from == SaleOrder::STATUS_LATE_CANCELLED
                || (!empty($saleOrder->order_production_at) && $to == SaleOrder::NEW_ORDER)
                || (!empty($saleOrder->shipment_id) && in_array($to, [SaleOrder::IN_PRODUCTION]) && !$saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER)
                || ($from == SaleOrder::SHIPPED && $to == SaleOrder::STATUS_LATE_CANCELLED)
            )
        ) {
            return $this->handleFail("Can't change orders " . ucwords(str_replace('_', ' ', $from)) . ' to ' . ucwords(str_replace('_', ' ', $to)));
        }

        try {
            DB::beginTransaction();
            if ($from != $to && $to == SaleOrder::ON_HOLD) {
                SaleOrderOnHold::updateOrCreate(
                    ['order_id' => $id],
                    [
                        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
                        'release_on_hold_at' => $request['release_on_hold_at'] ?? null,
                        'manual_on_hold_by' => \auth()->user()->id,
                    ],
                );
            } elseif ($to != SaleOrder::ON_HOLD) {
                SaleOrderOnHold::where('order_id', $id)->delete();
            } else {
                $orderOnHold = SaleOrderOnHold::where('order_id', $id)->first();
                if ($orderOnHold && isset($request['release_on_hold_at']) && $orderOnHold->release_on_hold_at != $request['release_on_hold_at']) {
                    $oldReleaseOnHoldAt = $orderOnHold->release_on_hold_at;
                    $orderOnHold->manual_on_hold = SaleOrderOnHold::IS_TRUE;
                    $orderOnHold->release_on_hold_at = !empty($request['release_on_hold_at']) ? Carbon::parse($request['release_on_hold_at'])->format('Y-m-d H:i:s') : null;
                    $orderOnHold->save();
                    $this->saveHistory($id, SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE, 'Release Date on ' . (!empty($oldReleaseOnHoldAt) ? Carbon::parse($oldReleaseOnHoldAt)->format('Y-m-d H:i:s') : '"--"') . ' changed to ' . (!empty($request['release_on_hold_at']) ? Carbon::parse($request['release_on_hold_at'])->format('Y-m-d H:i:s') : '"--"'));
                }
            }
            if ($to === SaleOrder::REJECTED) {
                SaleOrderItemImage::query()->where('order_id', $saleOrder->id)->update([
                    'delete_status' => SaleOrderItemImage::INACTIVE
                ]);
                SaleOrderItemBarcode::query()->where('order_id', $saleOrder->id)->update([
                    'is_deleted' => SaleOrderItemBarcode::INACTIVE
                ]);
            }
            if ($to === SaleOrder::CANCELLED || $to === SaleOrder::STATUS_LATE_CANCELLED) {
                $saleOrder->cancelled_at = Carbon::now()->toDateTimeString();
                $store = Store::where('id', $saleOrder->store_id)->first();

                if ($to === SaleOrder::STATUS_LATE_CANCELLED && $store?->payment_terms != Store::STORE_PREPAID) {
                    // update calculated_at for seller calculate price
                    $saleOrder->calculated_at = null;
                    SaleOrderItemBarcode::where('order_id', $id)->update(['is_deleted' => 1]);
                }
            } else {
                $saleOrder->cancelled_at = null;
            }
            if ($to === SaleOrder::REJECTED) {
                $saleOrder->rejected_at = Carbon::now()->toDateTimeString();
                $saleOrder->rejected_reason = $request['rejected_reason'];
                $saleOrder->tag = OrderEditingService::addTag($saleOrder->tag, Tag::MANUAL_REJECT_ID);
            } else {
                $saleOrder->rejected_at = null;
                $saleOrder->rejected_reason = null;
            }
            $saleOrder->order_status = $to;

            if ($saleOrder->is_test == 0 && $saleOrder->is_xqc != $request['is_xqc']) {
                $saleOrder->is_xqc = $request['is_xqc'];
                $this->saveHistory($id, SaleOrderHistory::UPDATE_ORDER_TEST_TYPE, 'The order has been marked as XQC order');
            }

            if ($saleOrder->is_test == 0 && $request['is_test'] == 1) {
                $saleOrder->is_test = $request['is_test'];
                SaleOrderItemBarcode::where('order_id', $id)->update(['is_deleted' => 1]);
                $this->saveHistory($id, SaleOrderHistory::UPDATE_ORDER_XQC_TYPE, 'Order successfully converted to test order');
                handleJob(QueueJob::CANCEL_EMBROIDERY_TASK, $saleOrder->id);
            }
            $saleOrder->save();
            if ($from != $to) {
                $message = "Order status changed from \"$from\" to \"$to\". ";
                if ($to == SaleOrder::ON_HOLD && !empty($request['release_on_hold_at'])) {
                    $message .= 'Release Date on ' . Carbon::parse($request['release_on_hold_at'])->format('Y-m-d H:i:s');
                }
                $this->saveHistory($id, SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE, $message);
            }
            DB::commit();

            return $this->handleSuccess('Update order successfully.', $saleOrder);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->handleFail($e->getMessage());
        }
    }

    public function updateShippingMethod(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::with([
            'address',
            'store.shippingMethods' => function ($q) {
                $q->select('store_id', 'api_shipping_method')
                    ->whereNotIn('api_shipping_method', [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_PRIORITY]);
            }
        ])
            ->find($id);
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }
        $dataShippingMethod = SaleOrder::ARRAY_SHIPPING_METHOD_DEFAULT;
        if ($saleOrder->store->shippingMethods->count() > 0) {
            $dataShippingMethod = array_merge($saleOrder->store->shippingMethods->pluck('api_shipping_method')->toArray(), $dataShippingMethod);
        }
        $validator = Validator::make($request->all(), [
            'shipping_method' => ['required', Rule::in($dataShippingMethod)]
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }
        $saleOrder->is_eps = $request['shipping_method'] == 'express' ? true : false;
        $methodFrom = $saleOrder->shipping_method;
        $methodTo = $request['shipping_method'];

        $tagImport = $saleOrder->tag;

        if ($methodTo == 'express') {
            $tag = Tag::where('name', Tag::UPGRADE_TO_EXPRESS_SHIPPING)->first();
            if (empty($tagImport)) {
                $tagImport = $tag->id;
            } else {
                $tagImport = $saleOrder->tag . ',' . $tag->id;
            }
        }

        if ($methodTo == 'priority') {
            $tag = Tag::where('name', Tag::UPGRADE_TO_PRIORITY_SHIPPING)->first();
            if (empty($tagImport)) {
                $tagImport = $tag->id;
            } else {
                $tagImport = $saleOrder->tag . ',' . $tag->id;
            }
        }

        if ($methodTo == 'printify_ups01_2ndDayAir') {
            $tag = Tag::where('name', Tag::UPGRADE_TO_UPS_2_DAY_AIR)->first();
            if (empty($tagImport)) {
                $tagImport = $tag->id;
            } else {
                $tagImport = $saleOrder->tag . ',' . $tag->id;
            }
        }

        $saleOrder->shipping_method = $methodTo;
        $saleOrder->tag = $tagImport;
        $saleOrder->save();
        $this->saveHistory($id, SaleOrderHistory::UPDATE_SHIPPING_METHOD_TYPE, "Shipping method changed from \"$methodFrom\" to \"$methodTo\"");
        //todo: handle charge for prepaid store

        return $this->handleSuccess('Update shipping method successfully.', $saleOrder);
    }

    public function updateTag(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }
        $validator = Validator::make($request->all(), [
            'tag' => 'array'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }
        $from = !empty($saleOrder->tag) ? explode(',', $saleOrder->tag) : [];
        $to = $request->input('tag', []);
        $currentTags = array_map('intval', $from);
        $tagRestrictRemoves = $this->tagRepository->tagRestrictRemove();
        $protectedTagIds = array_merge($tagRestrictRemoves->pluck('id')->toArray(), [Tag::LABEL_TAG_ID, Tag::TAG_RBT_ID]);

        $tagsToAdd = array_diff($to, $currentTags);
        $tagsToRemove = array_diff($currentTags, $to);

        if (array_intersect($tagsToRemove, $protectedTagIds) || array_intersect($tagsToAdd, $protectedTagIds)) {
            $protectedTagNameString = Tag::TAG_LABEL . ', ' . $tagRestrictRemoves->pluck('name')->implode(', ');
            throw new \Exception('You cannot add or remove tags: ' . $protectedTagNameString);
        }

        $message = '';
        $addedTag = [];
        $removedTag = [];
        foreach ($to as $tag) {
            if (!is_numeric($tag)) {
                continue;
            }
            if (!in_array($tag, $from)) {
                $addedTag[] = $tag;
            }
        }
        $addedTag = $addedTag ? Tag::whereIn('id', $addedTag)->get()->toArray() : [];
        if ($addedTag) {
            $message .= 'Add new tag "';
            foreach ($addedTag as $key => $tag) {
                if ($key != count($addedTag) - 1) {
                    $message .= $tag['name'] . ', ';

                    continue;
                }
                $message .= $tag['name'];
            }
            $message .= '"';
        }
        foreach ($from as $tag) {
            if (!is_numeric($tag)) {
                continue;
            }
            if (!in_array($tag, $to)) {
                $removedTag[] = $tag;
            }
        }
        $removedTag = $removedTag ? Tag::whereIn('id', $removedTag)->get()->toArray() : [];
        if ($removedTag) {
            $message .= $message ? '\nRemove tag "' : 'Remove tag "';
            foreach ($removedTag as $key => $tag) {
                if ($key != count($removedTag) - 1) {
                    $message .= $tag['name'] . ', ';

                    continue;
                }
                $message .= $tag['name'];
            }
            $message .= '"';
        }
        $tags = implode(',', $to);
        $saleOrder->tag = $tags;
        $saleOrder->save();
        if ($message) {
            $this->saveHistory($id, SaleOrderHistory::UPDATE_TAG_TYPE, $message);
        }

        return $this->handleSuccess('Update tag successfully.', $saleOrder);
    }

    public function updateNote(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        $noteFrom = !empty($saleOrder->customer_note) ? $saleOrder->customer_note : '';
        $noteTo = empty($request['note']) ? '' : $request['note'];
        $saleOrder->customer_note = $noteTo;
        $saleOrder->save();
        $this->saveHistory($id, SaleOrderHistory::UPDATE_NOTE_TYPE, "Buyer note changed from \"$noteFrom\" to \"$noteTo\"");

        return $this->handleSuccess('Update note successfully.', $saleOrder);
    }

    public function updateInternalNote(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        $validator = Validator::make($request->all(), [
            'internal_note' => 'nullable|string'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();

            return $this->handleFail('The given data was invalid.', $errors);
        }

        $noteFrom = !empty($saleOrder->internal_note) ? $saleOrder->internal_note : '';
        $noteTo = empty($request['internal_note']) ? '' : $request['internal_note'];
        $saleOrder->internal_note = $noteTo;
        $saleOrder->save();
        $this->saveHistory($id, SaleOrderHistory::UPDATE_INTERNAL_NOTE_TYPE, "Internal note changed from \"$noteFrom\" to \"$noteTo\"");

        return $this->handleSuccess('Update internal note successfully.', $saleOrder);
    }

    public function updateIossNumber(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }
        $oldIossNumber = $saleOrder->tax_id_type ? $saleOrder->tax_id_type . ': #' . $saleOrder->ioss_number : $saleOrder->ioss_number;
        $saleOrder->ioss_number = $request['ioss_number'];
        $saleOrder->tax_id_type = $request['tax_id_type'];
        $newTaxNumber = $request['tax_id_type'] . ': #' . $request['ioss_number'];
        $historyMessage = 'Add new tax indentifier:  ' . $newTaxNumber;
        if (!empty($oldIossNumber)) {
            $historyMessage = 'Tax indentifier changed from "' . $oldIossNumber . '" to "' . $newTaxNumber . '"';
        }
        $saleOrder->save();
        $this->saveHistory($id, SaleOrderHistory::UPDATE_IOSS_NUMBER_TYPE, $historyMessage);

        return $this->handleSuccess('Update tax indentifier successfully.', $saleOrder);
    }

    private function getAddressMessage($newData, $oldData): string
    {
        $message = '';
        $dataTypeAddress = explode('_', $newData['type_address']);
        $typeAddress = count($dataTypeAddress) == 2 ? ' ' . explode('_', $newData['type_address'])[0] : '';
        foreach ($newData as $key => $value) {
            $label = $key === 'type_address' ? 'Type' : ucfirst($key) . $typeAddress . ' address';
            $from = !empty($oldData[$key]) ? $oldData[$key] : 'NULL';
            $to = !empty($value) ? $value : 'NULL';
            if ($value !== $oldData[$key]) {
                if ($key == count($newData) - 1) {
                    $message .= "$label changed from \"$from\" to \"$to\"";

                    continue;
                }
                $message .= "$label changed from \"$from\" to \"$to\"\n";
            }
        }

        return $message;
    }

    public function updateAddress(int $id, Request $request): array
    {
        $saleOrder = SaleOrder::find($id);
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }
        $saleOrderAddress = SaleOrderAddress::where('order_id', $id)->where('type_address', 'to_address')->first();
        if (!$saleOrderAddress) {
            return $this->handleFail('Address to not found.');
        }
        $data = $this->prepareAddressData($request);
        if (!$saleOrderAddress) {
            $saleOrderAddress = SaleOrderAddress::create($data);
            $this->saveHistory($id, SaleOrderHistory::UPDATE_ADDRESS_TYPE, 'Already initialized address for order.');

            return $this->handleSuccess('Update address successfully.', $saleOrderAddress);
        }
        $message = $this->getAddressMessage($data, $saleOrderAddress);
        foreach ($data as $key => $value) {
            $saleOrderAddress[$key] = $value;
        }
        if (empty($message)) {
            return $this->handleFail('No change of address found');
        }
        $saleOrderAddress->save();
        $this->saveHistory($id, SaleOrderHistory::UPDATE_ADDRESS_TYPE, $message);

        return $this->handleSuccess('Update address successfully.', $saleOrderAddress);
    }

    public function verifyAddress(int $id): array
    {
        $saleOrderAddress = SaleOrderAddress::where('order_id', $id)->with('saleOrder')->first();
        if (!$saleOrderAddress) {
            return $this->handleFail('Order address not found.');
        }
        try {
            $store = Store::find($saleOrderAddress->saleOrder->store_id);
            if (!$store) {
                return $this->handleFail('Store not found.');
            }
            $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();
            $easypostApiKeyDefault = $easypostApiKeyDefault ? $easypostApiKeyDefault->value : '';
            $easypostApiKey = !empty($store->easypost_api_key) ? $store->easypost_api_key : $easypostApiKeyDefault;
            EasyPost::setApiKey($easypostApiKey);
            $addressParam = [
                'verify' => ['delivery'],
                'street1' => $saleOrderAddress->street1,
                'street2' => $saleOrderAddress->street2,
                'city' => $saleOrderAddress->city,
                'state' => $saleOrderAddress->state,
                'zip' => $saleOrderAddress->zip,
                'country' => $saleOrderAddress->country,
                'company' => $saleOrderAddress->company,
                'phone' => $saleOrderAddress->phone
            ];
            $address = Address::create($addressParam);
            $isValid = $address->verifications->delivery->success;
            $errors = $address->verifications->delivery->errors;
            $errorMessage = $this->getMessage($errors);
            if ($isValid) {
                if (empty($errors)) {
                    $saleOrderAddress->verified_status = SaleOrderAddress::SUCCESS;
                    $saleOrderAddress->verified_message = 'Address Validated';
                    $saleOrderAddress->save();
                    $this->saveHistory($id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, 'Address Validated');

                    return $this->handleSuccess('Address Validated', $saleOrderAddress);
                } else {
                    $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::HAS_PROBLEM, $errorMessage);
                    $this->saveHistory($id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, $errorMessage);

                    return $this->handleFail($errorMessage);
                }
            }
            if (!empty($errors)) {
                $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::ERROR, $errorMessage);
                $this->saveHistory($id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, $errorMessage);

                return $this->handleFail($errorMessage);
            }
        } catch (Exception $exception) {
            $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::ERROR, $exception->getMessage());
            $this->saveHistory($id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, 'Invalid address');

            return $this->handleFail($exception->getMessage());
        }
    }

    private function getMessage($errors): string
    {
        $errorMessage = '';
        if (empty($errors)) {
            return $errorMessage;
        }
        foreach ($errors as $key => $error) {
            if ($error->field === 'address' && count($errors) > 1) {
                continue;
            }
            if ($key == count($errors) - 1) {
                $errorMessage .= $error->message;

                continue;
            }
            $errorMessage .= $error->message . '\n';
        }

        return $errorMessage;
    }

    public function saveHistory($orderId, $type, $message, $employeeId = null)
    {
        $data = SaleOrderHistory::create([
            'user_id' => auth()->user()['id'],
            'employee_id' => $employeeId,
            'order_id' => $orderId,
            'type' => $type,
            'message' => $message,
            'created_at' => Carbon::now()->toDateTimeString()
        ]);
        $data['created_at'] = convertTimeUTCToPST($data['created_at'])->format('Y-m-d H:i:s');

        return $data;
    }

    private function updateVerifyAddress($id, $status, $message): void
    {
        SaleOrderAddress::where('id', $id)->update([
            'verified_status' => $status,
            'verified_message' => $message
        ]);
    }

    /**
     * @throws Exception
     */
    public function reprint($request)
    {
        DB::beginTransaction();
        try {
            $listOrderId = [];
            foreach ($request['label_ids'] as $label_id) {
                // $barcode = SaleOrderItemBarcode::whereHas(
                //     'saleOrder',
                //     function ($query) {
                //         $query->where('is_manual', SaleOrder::IS_MANUAL);
                //     }
                // )->where('label_id', $label_id)->first();

                // if (!$barcode) {
                $saleOrderItemBarcode = SaleOrderItemBarcode::query()
                    ->where('label_id', $label_id)
                    ->where('reprint_status', '<>', SaleOrderItemBarcode::REPRINTED)
                    ->active()
                    ->first();
                if (!$saleOrderItemBarcode) {
                    throw new Exception("label_id $label_id was not found or reprinted.");
                }
                $newReprintData = $saleOrderItemBarcode->toArray();
                $saleOrderItemBarcode->is_deleted = SaleOrderItemBarcode::INACTIVE;
                $saleOrderItemBarcode->reprint_status = SaleOrderItemBarcode::REPRINTED;
                $saleOrderItemBarcode->reprinted_at = Carbon::now()->toDateTimeString();
                $saleOrderItemBarcode->employee_reprint_id = $request['employee_id'];
                $saleOrderItemBarcode->save();
                unset($newReprintData['id']);
                $newBarcode = new SaleOrderItemBarcode();
                $newBarcode->order_id = $saleOrderItemBarcode->order_id;
                $newBarcode->order_item_id = $saleOrderItemBarcode->order_item_id;
                $newBarcode->sku = $saleOrderItemBarcode->sku;
                $newBarcode->barcode_number = $saleOrderItemBarcode->barcode_number;
                $newBarcode->order_quantity = $saleOrderItemBarcode->order_quantity;
                $newBarcode->warehouse_id = $saleOrderItemBarcode->warehouse_id;
                $newBarcode->store_id = $saleOrderItemBarcode->store_id;
                $newBarcode->account_id = $saleOrderItemBarcode->account_id;
                $newBarcode->created_at = Carbon::now()->toDateTimeString();
                $newBarcode->reprint_status = SaleOrderItemBarcode::NOT_REPRINTED;
                $label = $this->generateLabelId($newReprintData['label_id'], $saleOrderItemBarcode->label_root_id);
                $newBarcode->label_id = $label['label_id'];
                $newBarcode->label_root_id = $label['root_id'];

                $newBarcode->save();
                $claim = Claim::create([
                    'label_id' => $saleOrderItemBarcode->label_id,
                    'employee_id' => $request['employee_id'],
                    'type' => $request['type'],
                    'reason' => $request['reason']
                ]);

                $claim->ticket_id = $claim->type . '-' . Carbon::now()->format('mdy') . '-' . $claim->id;
                $claim->save();
                // }
                $listOrderId[] = $saleOrderItemBarcode->order_id;

                // xoa label id o bang shipment item label
                $shipmentItemLabel = ShipmentItemLabel::where('label_id', $saleOrderItemBarcode->label_id)->first();
                if ($shipmentItemLabel) {
                    $shipmentItem = ShipmentItem::where('order_id', $saleOrderItemBarcode->order_id)
                        ->where('id', $shipmentItemLabel->shipment_item_id)
                        ->first();
                    if ($shipmentItem) {
                        if ($shipmentItem->quantity > 1) {
                            $shipmentItem->quantity = $shipmentItem->quantity - 1;
                            $shipmentItem->save();
                        } else {
                            $shipmentItem->delete();
                        }
                    }
                    $shipmentItemLabel->delete();
                }
                saleOrderHistory(
                    auth()->user()?->id,
                    $request['employee_id'],
                    $saleOrderItemBarcode->order_id,
                    SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
                    'Reprinted Label ID ' . $label['label_id'],
                );
            }

            $res = $this->handleSuccess('Reprint successfully.');
            if (!$res['status']) {
                return response()->json($res['output'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            DB::commit();

            if (count($listOrderId) > 0) {
                foreach ($listOrderId as $ids) {
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
                }
            }
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Reprint successfully!');
    }

    public function manualProcess($request)
    {
        DB::beginTransaction();
        $listEnableManual = [
            SaleOrder::NEW_ORDER,
            SaleOrder::IN_PRODUCTION,
            SaleOrder::SHIPPED,
            SaleOrder::ON_HOLD,
        ];
        try {
            foreach ($request['orderIds'] as $orderId) {
                $saleOrder = SaleOrder::where('id', $orderId)
                    ->whereIn('order_status', $listEnableManual)
                    ->where('is_manual', '!=', 1)->first();
                if ($saleOrder) {
                    $saleOrder->cancelled_at = null;
                    $saleOrder->rejected_at = null;
                    $saleOrder->rejected_reason = null;
                    $saleOrderItemBarcode = SaleOrderItemBarcode::query()
                        ->where('order_id', $orderId)
                        ->active()
                        ->where('reprint_status', '<>', SaleOrderItemBarcode::REPRINTED)
                        ->get();
                    foreach ($saleOrderItemBarcode as $item) {
                        $item->update([
                            'is_deleted' => SaleOrderItemBarcode::INACTIVE
                        ]);
                        $barcode = new SaleOrderItemBarcode();
                        $barcode->order_id = $item->order_id;
                        $barcode->order_item_id = $item->order_item_id;
                        $barcode->sku = $item->sku;
                        $barcode->barcode_number = $item->barcode_number;
                        $barcode->order_quantity = $item->order_quantity;
                        $barcode->warehouse_id = $item->warehouse_id;
                        $barcode->store_id = $item->store_id;
                        $barcode->account_id = $item->account_id;
                        $barcode->created_at = Carbon::now()->toDateTimeString();
                        $label = $this->generateLabelId($item->label_id, $item->label_root_id);
                        $barcode->label_id = $label['label_id'];
                        $barcode->save();
                        $claim = Claim::create([
                            'label_id' => $item->label_id,
                            'employee_id' => $request['employee_id'],
                            'type' => Claim::MANUAL_PROCESS_TYPE,
                            'reason' => $request['reason']
                        ]);
                        $claim->ticket_id = $claim->type . '-' . Carbon::now()->format('mdy') . '-' . $claim->id;
                        $claim->save();
                    }
                    $saleOrder->employee_manual_id = $request['employee_id'];
                    $saleOrder->is_manual = SaleOrder::IS_MANUAL;
                    $saleOrder->manual_process_at = Carbon::now()->toDateTimeString();
                    $saleOrder->save();
                    $this->saveHistory($orderId, SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE, 'Order status changed Manual Process', $request['employee_id'] ?? null);
                }
            }
            DB::commit();

            return $this->successResponse('Manual Process successfully!');
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function reroute($request)
    {
        DB::beginTransaction();
        try {
            $warehouse = Warehouse::all()->pluck('name', 'id')->toArray();
            $params['orderIds'] = $request['order_ids'];
            $params['warehouse_id'] = $request['warehouse_id'];
            $data = $this->getOrderAvailableReRoute($params);

            if (count($data['orderAvailables']) > 0) {
                $dataUpdate = [
                    'orders' => $data['orderAvailables'],
                    'warehouse_id' => $request['warehouse_id'],
                    'employee_id' => $request['employee_id'],
                    'order_ids' => array_column($data['orderAvailables'], 'order_id'),
                    'all_warehouse' => $warehouse,
                ];
                $this->updateData($dataUpdate);
            }

            DB::commit();
            if (count($data['orderAvailables']) > 0) {
                $this->refundShipment($request);
            }
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Reroute successfully!');
    }

    public function updateData($dataUpdate)
    {
        $this->updateSaleOrder($dataUpdate);
        $this->updateSaleOrderItem($dataUpdate);
        $this->updateSaleOrderItemImage($dataUpdate);
        $this->updateSaleOrderItemBarcode($dataUpdate);
        $this->updateSaleOrderRbt($dataUpdate);
        $this->updateSaleOrderHistory($dataUpdate);
    }

    public function checkBeforeReroute($request)
    {
        $result = [];
        $data = $this->getOrderAvailableReRoute($request);
        foreach ($request['orderIds'] as $key => $orderId) {
            if (array_key_exists($orderId, $data['orders'])) {
                $result[] = [
                    'id' => $orderId,
                    'order_number' => $data['orders'][$orderId],
                    'available' => array_key_exists($orderId, $data['orderAvailables']) ? true : false,
                ];
            } else {
                $result[] = [
                    'id' => $orderId,
                    'order_number' => null,
                    'available' => false,
                ];
            }
        }

        return $result;
    }

    public function findById($id)
    {
        return SaleOrder::find($id);
    }

    public function findByCondition($input)
    {
        return SaleOrder::where('id', $input['order_id'])->where('warehouse_id', $input['warehouse_id'])->first();
    }

    public function getOrderAvailableReRoute($input)
    {
        $query = SaleOrder::with(['barcodeItems.receivedWip'])
            ->whereIn('sale_order.id', $input['orderIds']);

        $data['orders'] = $query->clone()->pluck('order_number', 'id')->toArray();

        $data['orderAvailables'] = $query
            ->when(true, function ($query) use ($input) {
                $query->where('warehouse_id', '!=', $input['warehouse_id']);
            })
            ->select('sale_order.order_number', 'sale_order.id', 'sale_order.warehouse_id', 'sale_order.is_rbt', 'sale_order.order_status')
            ->get()
            ->filter(function ($item) {
                if ($item->is_rbt) {
                    return $item->order_status === 'new_order'
                        && $item->barcodeItems->every(fn ($barcode) => is_null($barcode->receivedWip));
                }

                return !in_array($item->order_status, SaleOrder::ARRAY_STATUS_INACTIVE);
            })
            ->mapWithKeys(function ($item) {
                return [$item->id => [
                    'order_id' => $item->id,
                    'order_number' => $item->order_number,
                    'warehouse_id' => $item->warehouse_id,
                ]];
            })
            ->toArray();

        return $data;
    }

    public function getSaleOrderByOrderNumber($orderNumber)
    {
        return SaleOrder::where('order_number', $orderNumber)->first();
    }

    public function getReRouteRule()
    {
        return SaleOrderAutomationRule::where('is_active', 1)
            ->where('type', 1)
            ->orderBy('priority', 'DESC')
            ->get();
    }

    public function generateLabelId($oldLabelId, $rootId)
    {
        $indexLastPhrase = strrpos($oldLabelId, '(');
        if ($indexLastPhrase > 0) {
            $labelTmp = explode('(', $oldLabelId);
            $lastReprint = str_replace(')', '', $labelTmp[1]);
            $nextReprint = $lastReprint + 1;

            return [
                'label_id' => $labelTmp[0] . '(' . $nextReprint . ')',
                'root_id' => $labelTmp[0]
            ];
        } else {
            return [
                'label_id' => $oldLabelId . '(1)',
                'root_id' => $oldLabelId
            ];
        }
    }

    public function getListOrder(Request $request)
    {
        setTimezone();
        $userInfo = auth()->user();
        $data['is_login_support'] = $userInfo['is_login_support'];
        if (auth()->user() instanceof User) {
            if ($userInfo['is_admin'] == 0 && $userInfo['is_all_store'] != 1) {
                $request['store_ids'] = $userInfo->store_ids ?? [];
            }
        } else {
            $request['store_id'] = Auth::id();
        }
        $query = SaleOrder::select([
            'sale_order.id',
            'sale_order.encode_id as key',
            'sale_order.external_number',
            'sale_order.order_status',
            'sale_order.created_at',
            'sale_order.amount_paid',
            'sale_order.shipping_calculate',
            'sale_order.order_date',
            'sale_order.encode_id',
            'sale_order.is_test',
            'sale_order.shipment_id',
            'sale_order.store_id',
            'sale_order.is_fba_order',
            'sale_order.order_type',
            'sale_order.payment_status',
            'sale_order.order_quantity as quantity'
        ])
            ->with([
                'address:id,order_id,name',
                'items' => function ($q) {
                    $q->select('order_id', 'licensed_design_id', 'id');
                },
                'items.licensedDesign' => function ($q) {
                    $q->select('licensed_holder_id', 'licensed_design_id'); // hoặc 'licensed_holder_id' nếu tên column là vậy
                },
                'items.licensedDesign.holder' => function ($q) {
                    $q->select('id', 'licensed_holder', 'licensed_code');
                },
                'shipmentDefault' => function ($q) {
                    $q->select('id', 'order_id', 'tracking_number', 'carrier_code', 'est_delivery_at', 'tracking_status')
                        ->whereNull('employee_refund_id');
                },
                'store' => function ($q) {
                    $q->select('id', 'code', 'name');
                },
                'sla:order_id,expired_at',
                'tags:order_id,tag_id'
            ])
            ->search($request);
        $query->latest('id');

        if (!empty($request['action']) && $request['action'] == 'export') {
            return $query->get();
        }

        $shippingCarrier = ShippingCarrier::whereNotNull('code')->pluck('tracking_url', 'code')->toArray();

        return $query->paginate($request['limit'], ['*'], 'page', $request['page'])
            ->through(function ($item) use ($shippingCarrier) {
                foreach ($item->items as $orderItem) {
                    unset($orderItem->order_id);
                    unset($orderItem->id);
                }

                if ($item->tags) {
                    foreach ($item->tags as $tag) {
                        unset($tag->order_id);
                    }
                }

                if ($item->sla) {
                    unset($item->sla->order_id);
                }

                if ($item->address) {
                    unset($item->address->order_id);
                }

                unset($item->id);

                if ($item->shipmentDefault) {
                    unset($item->shipmentDefault->order_id);
                    if ($item->shipmentDefault->carrier_code && isset($shippingCarrier[$item->shipmentDefault->carrier_code])) {
                        $item->shipmentDefault->url = str_replace('{tracking_code}', $item->shipmentDefault->tracking_number, $shippingCarrier[$item->shipmentDefault->carrier_code]);
                    } else {
                        $item->shipmentDefault->url = '';
                    }
                }

                return $item;
            });
    }

    public function getOrderDetail($encodeId, $request)
    {
        $user = auth()->user();
        setTimezone();
        $order = SaleOrder::select([
            'encode_id as key',
            'external_number',
            'order_status',
            'order_date',
            'created_at',
            'calculated_at',
            'shipping_calculate',
            'shipping_method',
            'encode_id',
            'id',
            'is_test',
            'ioss_number',
            'tax_id_type',
            'store_id',
            'warehouse_id',
            'plastic_bag',
            'order_type',
            'order_quantity as quantity',
            'payment_status',
        ])
            ->with([
                'promotions:id,order_id,amount',
                'address:id,order_id,name,phone,street1,street2,city,state,zip,country,company,email',
                'returnAddress:id,order_id,name,phone,street1,street2,city,state,zip,country,company,email',
                'storeReturnAddress:id,store_id,name,phone,street1,street2,city,state,zip,country,company,email',
                'wareHouse:id,name,phone,street1,street2,city,state,zip,country',
                'shipment.shippingCarrier:id,code,name,tracking_url',
                'shipment.shippingCarrierService:id,name,display_name',
                'shipment:id,order_id,ship_date,tracking_number,carrier_code,service_code,refund_status,label_url,est_delivery_at,tracking_status',
                'sla',
                'tags:order_id,tag_id',
                'items' => function ($query) {
                    $query->select([
                        'id', 'name', 'order_id', 'product_sku', 'sku', 'quantity', 'product_id', 'options', 'licensed_design_id',
                        'unit_price', 'blank_price', 'handling_fee', 'product_style_sku', 'product_id', 'product_color_sku', 'product_size_sku',
                        'external_id'
                    ])
                        ->with([
                            'images' => function ($imageQuery) {
                                $imageQuery->select(['id', 'order_item_id', 'image_url', 'image_width', 'image_height', 'print_side'])
                                    ->with(['printSizeType:code,name']);
                            },
                            'getTypeProduct' => function ($styleQuery) {
                                $styleQuery->select('sku', 'name');
                            },
                            'product' => function ($productQuery) {
                                $productQuery->select(['id', 'brand_id'])
                                    ->with(['brand:id,name']);
                            },
                            'productColor',
                            'productSize',
                            'licensedDesign.holder',
                        ]);
                },
                'peakShippingFee'
            ])
            ->with(['store.shippingMethods' => function ($q) {
                $q->whereNotIn('api_shipping_method', [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_FIRST_CLASS])
                    ->with('shippingCarrier:name,id', 'shippingCarrierService:display_name,id');
            }])
            ->withSum('refunds as refund_amount', 'amount')
            ->where('encode_id', $encodeId);
        if (!isset($user['is_login_support']) && $user['is_login_support'] != 1) {
            $order = $order->where('store_id', Auth::id());
        }
        $order = $order->firstOrFail();
        $order->duplicate_counts = SaleOrderDuplicate::where('parent_id', $order->id)->count();
        $id = $order->id;
        unset($order->id);

        if ($order->tags) {
            foreach ($order->tags as $tag) {
                unset($tag->order_id);
            }
        }

        $isConvertedDtgToDtf = $order->order_quantity == 1 && resolve(SaleOrderChangePrintMethodRepository::class)->isConvertedDtgToDtf($id);

        foreach ($order->items as &$soItems) {
            unset($soItems->order_id);
            if ($isConvertedDtgToDtf) {
                foreach ($soItems->images as &$soItemImage) {
                    if ($soItemImage->print_side == 12) {
                        $soItemImage->print_side = 0;
                        $soItemImage->printSizeType->code = 0;
                        $soItemImage->printSizeType->name = 'Front';
                    } elseif ($soItemImage->print_side == 13) {
                        $soItemImage->print_side = 1;
                        $soItemImage->printSizeType->code = 1;
                        $soItemImage->printSizeType->name = 'Back';
                    }
                }
            }
        }

        if (!in_array($order->shipping_method, [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_FIRST_CLASS])) {
            $shipping = ShippingMethod::where('store_id', $order->store_id)
                ->where('api_shipping_method', $order->shipping_method)
                ->with([
                    'shippingCarrierService:id,display_name',
                    'shippingCarrier:id,name'
                ])
                ->first();

            if (isset($shipping) && (isset($shipping->shippingCarrier) || isset($shipping->shippingCarrierService))) {
                $order->shipping_method = $shipping->shippingCarrier?->name . ' ' . $shipping->shippingCarrierService?->display_name;
            }
        }
        $orderInsertRepo = new SaleOrderInsertRepository();
        $order->order_insert = $orderInsertRepo->getInsert($id);

        $order->peak_shipping_fee = $order->order_status == SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
            ? null
            : $order->peak_shipping_fee;

        if ($order->shipment->count() > 0) {
            $order->shipment->each(function (&$shipment) {
                $shipment->url = ($shipment->tracking_number && $shipment->shippingCarrier?->tracking_url) ? str_replace('{tracking_code}', $shipment->tracking_number, $shipment->shippingCarrier->tracking_url) : null;

                return $shipment;
            });
        }

        $surchargeResults = $this->fetchOrderSurcharge($order, $id);
        $order->surcharge_fee = $surchargeResults;

        return $order;
    }

    public function getSummary(Request $request)
    {
        setTimezone();

        $userInfo = auth()->user();
        if (isset($userInfo['is_login_support'])) {
            if ($userInfo['is_admin'] != 1 && $userInfo['is_all_store'] != 1) {
                $request['store_ids'] = $userInfo->store_ids ?? [];
            }
        } else {
            $request['store_ids'] = [$userInfo->id];
        }
        $query = SaleOrder::search($request);
        $cloneQuery = $query->clone()->selectRaw('is_test, order_status, sale_order.id AS sale_order_id');
        $summaryTest = $query->clone()->where('is_test', true)->count();

        $summary = SaleOrder::selectRaw('COUNT(CASE WHEN is_test = 0 THEN 1 ELSE NULL END) AS total, order_status')
            ->fromSub($cloneQuery, 'unique_orders')
            ->groupBy('order_status')
            ->pluck('total', 'order_status')
            ->toArray();

        $summaryAll = array_sum($summary) + $summaryTest;
        $summary['all'] = $summaryAll;
        $summary['test'] = $summaryTest;

        return $summary;
    }

    public function verifyAddressV1(int $id)
    {
        $saleOrderAddress = SaleOrderAddress::where('id', $id)->with('saleOrder')->first();

        if (!$saleOrderAddress) {
            return $this->handleFail('Order address not found.');
        }
        try {
            $store = Store::find($saleOrderAddress->saleOrder->store_id);
            if (!$store) {
                return $this->handleFail('Store not found.');
            }
            $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();
            $easypostApiKeyDefault = $easypostApiKeyDefault ? $easypostApiKeyDefault->value : '';
            $easypostApiKey = !empty($store->easypost_api_key) ? $store->easypost_api_key : $easypostApiKeyDefault;
            EasyPost::setApiKey($easypostApiKey);
            $addressParam = [
                'verify' => ['delivery'],
                'street1' => $saleOrderAddress->street1,
                'street2' => $saleOrderAddress->street2,
                'city' => $saleOrderAddress->city,
                'state' => $saleOrderAddress->state,
                'zip' => $saleOrderAddress->zip,
                'country' => $saleOrderAddress->country,
                'company' => $saleOrderAddress->company,
                'phone' => $saleOrderAddress->phone
            ];
            $address = Address::create($addressParam);
            $isValid = $address->verifications->delivery->success;
            $errors = $address->verifications->delivery->errors;
            $errorMessage = $this->getMessage($errors);
            if ($isValid) {
                if (empty($errors)) {
                    $saleOrderAddress->verified_status = SaleOrderAddress::SUCCESS;
                    $saleOrderAddress->verified_message = 'Address Validated';
                    $saleOrderAddress->save();
                    $this->saveHistory($saleOrderAddress->order_id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, 'Address Validated');

                    return $this->handleSuccess('Address Validated', $saleOrderAddress);
                } else {
                    $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::HAS_PROBLEM, $errorMessage);
                    $this->saveHistory($saleOrderAddress->order_id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, $errorMessage);

                    return $this->handleFail($errorMessage);
                }
            }
            if (!empty($errors)) {
                $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::ERROR, $errorMessage);
                $this->saveHistory($saleOrderAddress->order_id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, $errorMessage);

                return $this->handleFail($errorMessage);
            }
        } catch (Exception $exception) {
            $this->updateVerifyAddress($saleOrderAddress->id, SaleOrderAddress::ERROR, $exception->getMessage());
            $this->saveHistory($saleOrderAddress->order_id, SaleOrderHistory::VERIFY_ADDRESS_TYPE, 'Invalid address');

            return $this->handleFail($exception->getMessage());
        }
    }

    public function updateAddressV1(int $id, Request $request): array
    {
        $saleOrderAddress = SaleOrderAddress::where('id', $id)->first();
        if (!$saleOrderAddress) {
            throw new Exception('Address not found.', Response::HTTP_NOT_FOUND);
        }

        $saleOrder = SaleOrder::find($saleOrderAddress->order_id);
        if (!$saleOrder) {
            throw new Exception('Order not found.', Response::HTTP_NOT_FOUND);
        }

        $data = $this->prepareAddressDataV1($request, $saleOrderAddress->order_id);
        $message = $this->getAddressMessage($data, $saleOrderAddress);

        foreach ($data as $key => $value) {
            $saleOrderAddress[$key] = $value;
        }

        if (empty($message)) {
            return $this->handleFail('No change of address found');
        }
        $saleOrderAddress->save();

        if ($request['type_address'] == SaleOrderAddress::TO_ADDRESS && $saleOrder->store_id == Store::STORE_REDBUBBLE) {
            $integrateLogRedbubble = IntegrateLog::where('store_id', Store::STORE_REDBUBBLE)->where('order_id', $saleOrder->id)->first();
            $dataRebubble = json_decode($integrateLogRedbubble?->json);
            // ap dung cho version cu phai dung detect shipping method RB

            if ($dataRebubble) {
                $shouldHandleJob = $dataRebubble->version == '2023-04-11';
                if (!$shouldHandleJob) {
                    foreach ($dataRebubble->shipments as $value) {
                        if (
                            $value->shipment_id == $saleOrder->external_number
                            && ($value->shipping_info->carrier == 'unknown'
                                || $value->shipping_info->service == 'unknown')
                        ) {
                            $shouldHandleJob = true;
                            break;
                        }
                    }
                }

                if ($shouldHandleJob) {
                    handleJob(SaleOrder::JOB_DETECT_SHIPPING_METHOD_REDBUBBLE, $saleOrder->id);
                }
            }
        }

        $this->saveHistory($saleOrderAddress->order_id, SaleOrderHistory::UPDATE_ADDRESS_TYPE, $message);

        return $this->handleSuccess('Update address successfully.', $saleOrderAddress);
    }

    public function prepareAddressDataV1($request, $order_id): array
    {
        return [
            'order_id' => $order_id,
            'type_address' => $request['type_address'],
            'name' => $request['name'] ?? null,
            'email' => $request['email'] ?? null,
            'company' => $request['company'] ?? null,
            'phone' => $request['phone'] ?? null,
            'street1' => $request['street1'] ?? null,
            'street2' => $request['street2'] ?? null,
            'city' => $request['city'] ?? null,
            'state' => $request['state'] ?? null,
            'zip' => $request['zip'] ?? null,
            'country' => $request['country'] ?? null,
            'residential' => $request['residential'] ?? SaleOrderAddress::IS_NOT_RESIDENTIAL
        ];
    }

    public function sendRequestSaleOrderAPI($url, $token, $data = [], $method = 'POST')
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json',
                'Accept: application/json'
            ],
        ]);

        $response = curl_exec($curl);

        curl_close($curl);

        return [
            'response' => json_decode($response),
            'code' => curl_getinfo($curl)['http_code']
        ];
    }

    public function cancelOrder($encodeId)
    {
        $order = SaleOrder::with('store')
            ->where('encode_id', $encodeId)
            ->firstOrFail();

        $url = env('SALE_ORDER_API_URL') . 'orders/' . $encodeId . '/cancel';
        $data = [
            'source' => 'seller dashboard',
            'is_login_support' => false,
        ];
        $this->getUpdatedBy($data, 'cancelled_by');

        try {
            if (!empty($order->store->token)) {
                $result = $this->sendRequestSaleOrderAPI($url, $order->store->token, $data);
                $response = json_decode(json_encode($result['response']), true);
                if (!empty($response['status'])) {
                    return true;
                }
            }

            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    public function rerouteVerifyCsvFile($input)
    {
        $data = Excel::toArray(new RerouteImport, $input->file)[0];

        $data = array_map(function ($item) {
            return count($item) > 0 ? trim($item[0]) : '';
        }, $data);

        $data = array_filter($data);
        $data = array_unique($data);
        $data = array_values($data);

        if (!$data) {
            return [
                'total' => 0,
                'valid' => 0,
                'invalid' => 0,
            ];
        }

        $total = count($data);

        $saleOrder = [];
        SaleOrder::whereIn('order_number', $data)
            ->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->select('id as order_id', 'warehouse_id', 'order_number')
            ->chunk(500, function ($items) use (&$saleOrder) {
                $saleOrder = array_merge($saleOrder, $items->toArray());
            });

        $validOrder = [];
        $invalidOrder = [];
        $invalidOrderUserWarehouse = [];

        $orderNumbers = count($saleOrder) > 0 ? array_column($saleOrder, 'order_number') : [];
        foreach ($data as $item) {
            if (!in_array($item, $orderNumbers)) {
                $invalidOrder[] = [
                    'order_number' => $item,
                    'reason' => 'Invalid order number',
                ];
            }
        }

        if ($saleOrder) {
            foreach ($saleOrder as $saleOrderItem) {
                if ($saleOrderItem['warehouse_id'] != $input->warehouse_id) {
                    $invalidOrderUserWarehouse[] = [
                        'order_number' => $saleOrderItem['order_number'],
                        'reason' => 'The order number imported does not match the warehouse when you login',
                    ];
                } else {
                    $validOrder[] = $saleOrderItem;
                }
            }
        }

        $valid = count($validOrder);
        $invalid = $total - $valid;

        return [
            'total' => $total,
            'valid' => $valid,
            'invalid' => $invalid,
            'valid_order' => $validOrder,
            'invalid_order' => $invalidOrder,
            'invalid_order_user_warehouse' => $invalidOrderUserWarehouse,
            'order_error' => array_merge($invalidOrder, $invalidOrderUserWarehouse),
        ];
    }

    public function rerouteImportCsvFile($input)
    {
        $res = $this->rerouteVerifyCsvFile($input);

        $validOrder = $res['valid_order'] ?? [];
        if (!$validOrder) {
            return $res;
        }

        $data = [
            'orders' => $validOrder,
            'warehouse' => $input->warehouse,
            'employee_id' => $input->employee_id,
            'order_ids' => array_column($validOrder, 'order_id')
        ];

        return $this->rerouteUpdateByOrders($data);
    }

    private function rerouteUpdateByOrders($input)
    {
        DB::beginTransaction();
        try {
            $input = (array) $input;
            $orders = $input['orders'];
            $input['warehouse_id'] = $input['warehouse'];
            $items = array_chunk($orders, 500);

            $warehouse = Warehouse::all()->pluck('name', 'id')->toArray();

            foreach ($items as $item) {
                $dataItem = [
                    'orders' => $item,
                    'warehouse_id' => $input['warehouse'],
                    'employee_id' => $input['employee_id'],
                    'order_ids' => array_column($item, 'order_id'),
                    'all_warehouse' => $warehouse
                ];
                $this->updateData($dataItem);
            }

            DB::commit();
            $this->refundShipment($input);

            return $this->successResponse('Import Reroute successfully!');
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function updateSaleOrder($input)
    {
        $dataUpdate = [
            'warehouse_id' => $input['warehouse_id'],
            'barcode_printed_status' => 0,
            'barcode_printed_at' => null,
            'print_file_created_at' => null,
            'print_file_status' => 0,
            'order_pulled_status' => 0,
            'order_pulled_at' => null,
            'order_staged_status' => 0,
            'order_staged_at' => null,
            'order_printed_status' => null,
            'order_printed_at' => null,
            'order_pretreated_at' => null,
            'order_pretreated_status' => 0,
            'order_qc_status' => 0,
            'order_qc_at' => null,
            'order_folding_at' => null,
            'order_folding_status' => 0,
            'order_shipping_status' => 0,
            'order_shipped_at' => null,
            'order_production_at' => null,
            'order_status' => SaleOrder::STATUS_NEW_ORDER,
        ];

        return SaleOrder::whereIn('id', $input['order_ids'])->update($dataUpdate);
    }

    private function updateSaleOrderItem($input)
    {
        $dataUpdate = [
            'warehouse_id' => $input['warehouse_id'],
        ];

        return SaleOrderItem::whereIn('order_id', $input['order_ids'])->update($dataUpdate);
    }

    private function updateSaleOrderItemImage($input)
    {
        $dataUpdate = [
            'warehouse_id' => $input['warehouse_id'],
        ];

        return SaleOrderItemImage::whereIn('order_id', $input['order_ids'])->update($dataUpdate);
    }

    public function updateSaleOrderItemBarcode($input)
    {
        $itemBarcodes = SaleOrderItemBarcode::whereIn('order_id', $input['order_ids'])
            ->where('is_deleted', false)
            ->get()
            ->toArray();

        $dataUpdate = [
            'warehouse_id' => $input['warehouse_id'],
            'employee_reroute_id' => $input['employee_id'],
            'is_deleted' => true,
        ];
        SaleOrderItemBarcode::whereIn('order_id', $input['order_ids'])->update($dataUpdate);

        $dataInsert = [];
        $listOrderIds = [];
        $listLabels = [];
        foreach ($itemBarcodes as $key => $itemBarcode) {
            $label = $this->generateLabelId($itemBarcode['label_id'], $itemBarcode['label_root_id']);
            $dataInsert[] = [
                'order_id' => $itemBarcode['order_id'],
                'order_item_id' => $itemBarcode['order_item_id'],
                'sku' => $itemBarcode['sku'],
                'barcode_number' => $itemBarcode['barcode_number'],
                'order_quantity' => $itemBarcode['order_quantity'],
                'warehouse_id' => $input['warehouse_id'],
                'store_id' => $itemBarcode['store_id'],
                'account_id' => $itemBarcode['account_id'],
                'created_at' => Carbon::now()->toDateTimeString(),
                'label_id' => $label['label_id'],
                'label_root_id' => $label['root_id'],
                'employee_reroute_id' => $input['employee_id'],
                'print_method' => $itemBarcode['print_method'],
            ];
            $listOrderIds[] = $itemBarcode['order_id'];
            $listLabels[] = $itemBarcode['label_id'];
        }

        $saleOrderItemBarcode = new SaleOrderItemBarcode();
        $saleOrderItemBarcode->insert($dataInsert);

        ShipmentItem::whereIn('order_id', $listOrderIds)->delete();
        ShipmentItemLabel::whereIn('label_id', $listLabels)->delete();

        if (count($listOrderIds) > 0) {
            foreach ($listOrderIds as $ids) {
                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
            }
        }
    }

    private function updateSaleOrderHistory($input)
    {
        $dataInsert = array_map(function ($item) use ($input) {
            return [
                'user_id' => auth()->user()['id'],
                'employee_id' => $input['employee_id'] ?? null,
                'order_id' => $item['order_id'],
                'type' => SaleOrderHistory::IMPORT_REROUTE,
                'message' => 'Reroute from ' . $input['all_warehouse'][$item['warehouse_id']] . ' to ' . $input['all_warehouse'][$input['warehouse_id']],
                'created_at' => now()
            ];
        }, $input['orders']);

        return SaleOrderHistory::insert($dataInsert);
    }

    private function refundShipment($input)
    {
        $dataUpdate = [];
        Shipment::whereIn('order_id', $input['order_ids'])
            ->select('id', 'order_id')
            ->with('saleOrder:id,store_id,account_id')
            ->chunk(500, function ($items) use ($input, &$dataUpdate) {
                foreach ($items as $key => $item) {
                    // $data = [
                    //     'shipmentId' => $item->id,
                    //     'orderId' => $item->order_id,
                    //     'employeeId' => $input['employee_id'],
                    // ];
                    // ShipmentRefundJob::dispatch($data)->onQueue(Shipment::JOB_SHIPMMENT_REFUND);
                    $dataUpdate[] = [
                        'id' => $item->id,
                        'store_id' => $item->saleOrder->store_id ?? null,
                        'account_id' => $item->saleOrder->account_id ?? null,
                        'warehouse_id' => $input['warehouse_id'],
                    ];
                }
            });
        Shipment::upsert($dataUpdate, ['id'], ['store_id', 'account_id', 'warehouse_id']);
    }

    public function searchNewOrders($input)
    {
        $query = SaleOrder::where('is_test', 0);

        if (!empty($input['store_id'])) {
            $query->whereIn('store_id', $input['store_id']);
        }
        if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
            if (empty($input['warehouse'])) {
                $query->where('warehouse_id', config('jwt.warehouse_id'));
            } elseif ($input['warehouse'] != 'all') {
                $query->where('warehouse_id', $input['warehouse']);
            } else {
                $user = auth()->user();
                if (!$user->is_admin) {
                    $warehouseIds = $user->warehouses->pluck('id')->toArray();
                    $query->whereIn('warehouse_id', $warehouseIds);
                }
            }
        }

        return $query;
    }

    public function searchOrdersShipped($input)
    {
        $query = DB::table('shipment')
            ->whereNotIn('tracking_number', ['940010010936113003118', '940010010936113003115', '940010010936113003113']);

        if (!empty($input['store_id'])) {
            $query->whereIn('shipment.store_id', $input['store_id']);
        }
        if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
            if (empty($input['warehouse'])) {
                $query->where('shipment.warehouse_id', config('jwt.warehouse_id'));
            } elseif ($input['warehouse'] != 'all') {
                $query->where('shipment.warehouse_id', $input['warehouse']);
            } else {
                $user = auth()->user();
                if (!$user->is_admin) {
                    $warehouseIds = $user->warehouses->pluck('id')->toArray();
                    $query->whereIn('shipment.warehouse_id', $warehouseIds);
                }
            }
        }

        return $query;
    }

    public function getTotalShippedByDay($input)
    {
        $bindings = [];
        if (!empty($input['filter_type']) && $input['filter_type'] == 'seller-login' && $input['mode'] == 'order') {
            //seller login

            $query = SaleOrder::join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
                ->where('sale_order.is_test', 0)
                ->selectRaw('DATE(shipment.created_at) as ship_date,
                 COUNT(
                     CASE
                        WHEN shipment.provider <> ? THEN 1
                        WHEN shipment.provider = ? AND sale_order.order_status = ? THEN 1
                        ELSE NULL
                     END) as total', [Shipment::PROVIDER_MARKETPLACE, Shipment::PROVIDER_MARKETPLACE, SaleOrder::SHIPPED])
                ->groupBy(DB::raw('DATE(shipment.created_at)'));

            if (!empty($input['start_date'])) {
                $query->where('shipment.created_at', '>=', $input['start_date'] . ' 00:00:00');
            }
            if (!empty($input['end_date'])) {
                $query->where('shipment.created_at', '<=', $input['end_date'] . ' 23:59:59');
            }
            if (isset($input['store_id']) && is_array($input['store_id'])) {
                $query->whereIn('shipment.store_id', $input['store_id']);
            }
            if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
                if (empty($input['warehouse'])) {
                    $query->where('shipment.warehouse_id', config('jwt.warehouse_id'));
                } elseif ($input['warehouse'] != 'all') {
                    $query->where('shipment.warehouse_id', $input['warehouse']);
                } else {
                    $user = auth()->user();
                    if (!$user->is_admin) {
                        $warehouseIds = $user->warehouses->pluck('id')->toArray();
                        $query->whereIn('shipment.warehouse_id', $warehouseIds);
                    }
                }
            }

            return $query->get();
        } else {
            if (!empty($input['mode']) && $input['mode'] == 'item') {
                $query = SaleOrder::join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
                    ->where('sale_order.is_test', 0)
                    ->selectRaw('
                        DATE(shipment.created_at) as ship_date,
                        SUM(
                        CASE
                            WHEN shipment.provider <> ? THEN sale_order.order_quantity
                            WHEN shipment.provider = ? AND sale_order.order_status = ? THEN sale_order.order_quantity
                            ELSE 0
                        END
                    ) as total,
                        SUM(CASE WHEN sale_order.calculated_at IS NOT NULL THEN sale_order.amount_paid ELSE 0 END) as total_sales_unit
                    ', [Shipment::PROVIDER_MARKETPLACE, Shipment::PROVIDER_MARKETPLACE, SaleOrder::SHIPPED])
                    ->groupBy(DB::raw('DATE(shipment.created_at)'));
            } else {
                //support login
                $query = SaleOrder::join('shipment', 'shipment.id', '=', 'sale_order.shipment_id')
                    ->where('sale_order.is_test', 0)
                    ->selectRaw('
                    DATE(shipment.created_at) as ship_date,
                     COUNT(
                     CASE
                        WHEN shipment.provider <> ? THEN 1
                        WHEN shipment.provider = ? AND sale_order.order_status = ? THEN 1
                        ELSE NULL
                     END) as total,
                    SUM(CASE WHEN sale_order.calculated_at IS NOT NULL THEN sale_order.order_total ELSE 0 END) as total_sales_unit
                    ', [Shipment::PROVIDER_MARKETPLACE, Shipment::PROVIDER_MARKETPLACE, SaleOrder::SHIPPED])
                    ->groupBy(DB::raw('DATE(shipment.created_at)'));
            }
            if (!empty($input['start_date'])) {
                $query->where('shipment.created_at', '>=', $input['start_date'] . ' 00:00:00');
            }
            if (!empty($input['end_date'])) {
                $query->where('shipment.created_at', '<=', $input['end_date'] . ' 23:59:59');
            }

            if (!empty($input['store_id'])) {
                $query->whereIn('shipment.store_id', $input['store_id']);
            }
            if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
                if (empty($input['warehouse'])) {
                    $query->where('shipment.warehouse_id', config('jwt.warehouse_id'));
                } elseif ($input['warehouse'] != 'all') {
                    $query->where('shipment.warehouse_id', $input['warehouse']);
                } else {
                    $user = auth()->user();
                    if (!$user->is_admin) {
                        $warehouseIds = $user->warehouses->pluck('id')->toArray();
                        if (!empty($warehouseIds)) {
                            $query->whereIn('shipment.warehouse_id', $warehouseIds);
                        }
                    }
                }
            }

            return $query->get();
        }
    }

    public function getPastTotalByDay($input)
    {
        $query = DB::table('sales_report_totals');

        if (!empty($input['mode']) && $input['mode'] == 'item') {
            $query->selectRaw('date, SUM(total_items) as total_unit, SUM(total_shipped_items) as total_shipped_unit, SUM(total_order_items_sales_amount) as total_sales_unit');
        } else {
            $query->selectRaw('date, SUM(total_orders) as total_unit, SUM(total_shipped_orders) as total_shipped_unit, SUM(total_order_sales_amount) as total_sales_unit');
        }

        if (!empty($input['start_date'])) {
            $query->where('date', '>=', $input['start_date']);
        }
        if (!empty($input['end_date'])) {
            $query->where('date', '<=', $input['end_date']);
        }

        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        }

        if (isset($input['store_id']) && is_array($input['store_id'])) {
            if (!empty($input['store_id'])) {
                $query->whereIn('store_id', $input['store_id']);
            }
        }

        if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
            if (empty($input['warehouse'])) {
                $query->where('warehouse_id', config('jwt.warehouse_id'));
            } elseif ($input['warehouse'] != 'all') {
                $query->where('warehouse_id', $input['warehouse']);
            } else {
                $user = auth()->user();
                if (!$user->is_admin) {
                    $warehouseIds = $user->warehouses->pluck('id')->toArray();
                    $query->whereIn('warehouse_id', $warehouseIds);
                }
            }
        }

        return $query->groupBy('date')->get();
    }

    public function getTotalByDay($input)
    {
        $dateField = 'created_at';

        if (!empty($input['status']) && $input['status'] == SaleOrder::STATUS_LATE_CANCELLED) {
            $dateField = 'cancelled_at';
        }

        if (!empty($input['mode']) && $input['mode'] == 'item') {
            $selectValidData = "(SELECT DATE($dateField) as order_date, sum(order_quantity) as total, SUM(amount_paid) as total_amount FROM sale_order where ";
            $selectInvalidData = "(SELECT DATE($dateField) as order_date, sum(order_quantity) as total, SUM(amount_paid) as total_amount FROM sale_order where is_test = 1 AND ";
        } else {
            $selectValidData = "(SELECT DATE($dateField) as order_date, count(*) as total, SUM(order_total) as total_amount FROM sale_order where ";
            $selectInvalidData = "(SELECT DATE($dateField) as order_date, count(*) as total, SUM(order_total) as total_amount FROM sale_order where is_test = 1 AND ";
        }

        if (!empty($input['start_date'])) {
            $selectValidData .= $dateField . " >= '" . addslashes($input['start_date']) . " 00:00:00'" . ' AND ';
            $selectInvalidData .= $dateField . " >= '" . addslashes($input['start_date']) . " 00:00:00'" . ' AND ';
        }

        if (!empty($input['end_date'])) {
            $selectValidData .= $dateField . " <= '" . addslashes($input['end_date']) . " 23:59:59'" . ' AND ';
            $selectInvalidData .= $dateField . " <= '" . addslashes($input['end_date']) . " 23:59:59'" . ' AND ';
        }

        if (!empty($input['status'])) {
            $selectValidData .= 'order_status = "' . addslashes($input['status']) . '" AND ';
            $selectInvalidData .= 'order_status = "' . addslashes($input['status']) . '" AND ';
        }

        if (isset($input['store_id']) && is_array($input['store_id'])) {
            if (!empty($input['store_id'])) {
                $selectValidData .= 'store_id IN (' . addslashes(implode(',', $input['store_id'])) . ') AND ';
                $selectInvalidData .= 'store_id IN (' . addslashes(implode(',', $input['store_id'])) . ') AND ';
            } else {
                $selectValidData .= 'store_id IN ("' . '") AND ';
                $selectInvalidData .= 'store_id IN ("' . '") AND ';
            }
        }
        if (!empty($input['path']) && $input['path'] == SaleOrder::SWIFT_POD_PATH) {
            if (empty($input['warehouse'])) {
                $selectValidData .= 'warehouse_id = ' . addslashes(config('jwt.warehouse_id')) . ' AND ';
                $selectInvalidData .= 'warehouse_id = ' . addslashes(config('jwt.warehouse_id')) . ' AND ';
            } elseif ($input['warehouse'] != 'all') {
                $selectValidData .= 'warehouse_id = ' . addslashes($input['warehouse']) . ' AND ';
                $selectInvalidData .= 'warehouse_id = ' . addslashes($input['warehouse']) . ' AND ';
            } else {
                $user = auth()->user();
                if (!$user->is_admin) {
                    $warehouseIds = $user->warehouses->pluck('id')->toArray();
                    $selectValidData .= 'warehouse_id IN (' . addslashes(implode(',', $warehouseIds)) . ') AND ';
                    $selectInvalidData .= 'warehouse_id IN (' . addslashes(implode(',', $warehouseIds)) . ') AND ';
                }
            }
        }

        $selectValidData = substr($selectValidData, 0, -4);
        $selectInvalidData = substr($selectInvalidData, 0, -4);
        $selectValidData .= " GROUP BY DATE($dateField)) a1";
        $selectInvalidData .= " GROUP BY DATE($dateField)) a2";

        return DB::table(
            DB::raw($selectValidData),
        )
            ->leftJoin(DB::raw($selectInvalidData), 'a1.order_date', '=', 'a2.order_date')
            ->selectRaw('a1.order_date, (COALESCE(a1.total, 0) - COALESCE(a2.total, 0)) AS total, (COALESCE(a1.total_amount, 0) - COALESCE(a2.total_amount, 0)) AS total_paid')
            ->get();
    }

    public function getReportByDay($input)
    {
        $compareType = $input['compare_type'] ?? 'date';
        //get data from today
        $params = $input;
        $params['start_date'] = $params['end_date'] = Carbon::now('America/Los_Angeles')->format('Y-m-d');
        $todayOrders = $this->getTotalByDay($params);
        $todayShippedOrders = $this->getTotalShippedByDay($params);
        $params['status'] = SaleOrder::STATUS_LATE_CANCELLED;
        $todayInProductionCancelledOrders = $this->getTotalByDay($params);
        //get data from the past
        $pastOrders = $this->getPastTotalByDay($input);
        //merge past orders and today orders
        $today = new stdClass;
        $today->date = Carbon::now('America/Los_Angeles')->format('Y-m-d');
        $today->total_unit = count($todayOrders) > 0 ? $todayOrders->first()->total : 0;
        $today->total_shipped_unit = count($todayShippedOrders) > 0 ? $todayShippedOrders->first()->total : 0;
        $today->total_sales_unit = ($todayOrders->first()->total_amount ?? 0) + ($todayInProductionCancelledOrders->first()->total_amount ?? 0);
        $totalOrders = $pastOrders->put($params['start_date'], $today);
        // Store original dates
        $originalStartDate = Carbon::parse($input['start_date']);
        $originalEndDate = Carbon::parse($input['end_date']);

        // Get data from last year
        $input['start_date'] = $this->getDateFromLastYear($originalStartDate, $compareType);
        $input['end_date'] = $this->getDateFromLastYear($originalEndDate, $compareType);
        $totalOrdersLastYear = $this->getPastTotalByDay($input);

        // Get data from last month
        $input['start_date'] = $this->getDateFromLastMonth($originalStartDate, $compareType);
        $input['end_date'] = $this->getDateFromLastMonth($originalEndDate, $compareType);
        $totalOrdersLastMonth = $this->getPastTotalByDay($input);
        $convertTotalOrders = $convertTotalOrdersLastMonth = $convertTotalOrdersLastYear = [];
        foreach ($totalOrders as $orderNow) {
            $convertTotalOrders[$orderNow->date] = $orderNow;
        }

        foreach ($totalOrdersLastMonth as $orderLastMonth) {
            $convertTotalOrdersLastMonth[$orderLastMonth->date] = $orderLastMonth;
        }

        foreach ($totalOrdersLastYear as $orderLastYear) {
            $convertTotalOrdersLastYear[$orderLastYear->date] = $orderLastYear;
        }

        // Build data
        $newData = [];
        $loopDate = $originalStartDate->copy();

        for ($loopDate; $loopDate->lte($originalEndDate); $loopDate->addDay()) {
            $dateFormat = $loopDate->format('m-d');
            $currentKey = $loopDate->format('Y-m-d');
            $subMonthKey = $this->getDateFromLastMonth($loopDate, $compareType);
            $subYearKey = $this->getDateFromLastYear($loopDate, $compareType);
            $newData[] = [
                'year' => $originalStartDate->format('Y'),
                'date' => $dateFormat,
                'subMonthFormat' => $subMonthKey,
                'new_order' => isset($convertTotalOrders[$currentKey]) ? $convertTotalOrders[$currentKey]->total_unit : 0,
                'new_order_sub_month' => isset($convertTotalOrdersLastMonth[$subMonthKey]) ? $convertTotalOrdersLastMonth[$subMonthKey]->total_unit : 0,
                'new_order_sub_year' => isset($convertTotalOrdersLastYear[$subYearKey]) ? $convertTotalOrdersLastYear[$subYearKey]->total_unit : 0,
                'order_shipped' => isset($convertTotalOrders[$currentKey]) ? $convertTotalOrders[$currentKey]->total_shipped_unit : 0,
                'order_shipped_sub_month' => isset($convertTotalOrdersLastMonth[$subMonthKey]) ? $convertTotalOrdersLastMonth[$subMonthKey]->total_shipped_unit : 0,
                'order_shipped_sub_year' => isset($convertTotalOrdersLastYear[$subYearKey]) ? $convertTotalOrdersLastYear[$subYearKey]->total_shipped_unit : 0,
                //sales
                'order_items_sales' => isset($convertTotalOrders[$currentKey]) ? $convertTotalOrders[$currentKey]->total_sales_unit : 0,
                'order_items_sales_sub_month' => isset($convertTotalOrdersLastMonth[$subMonthKey]) ? $convertTotalOrdersLastMonth[$subMonthKey]->total_sales_unit : 0,
                'order_items_sales_sub_year' => isset($convertTotalOrdersLastYear[$subYearKey]) ? $convertTotalOrdersLastYear[$subYearKey]->total_sales_unit : 0
            ];
        }

        return $newData;
    }

    protected function getDateFromLastYear(Carbon $date, $type = 'date'): string
    {
        $lastYearDate = $date->copy()->subYear();

        if ($type == 'weekday' && $lastYearDate->dayOfWeek != $date->dayOfWeek) {
            //366 days
            $lastYearDate->addDay();
        }

        return $lastYearDate->toDateString();
    }

    public function getDateFromLastMonth(Carbon $date, $type = 'date')
    {
        $lastMonthDate = $date->copy()->subMonth();

        if ($type == 'weekday') {
            $lastMonthDate = $date->copy()->subDays(28);
        }

        return $lastMonthDate->toDateString();
    }

    public function getOrderAging($input)
    {
        if (!empty($input['mode']) && $input['mode'] == 'item') {
            $sql = 'SELECT
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 5 * 24 * 60 * 60, order_quantity, 0)) AS day5,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 4 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 5 * 24 * 60 * 60, order_quantity, 0)) AS day4,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 3 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 4 * 24 * 60 * 60, order_quantity, 0)) AS day3,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 2 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 3 * 24 * 60 * 60, order_quantity, 0)) AS day2,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 2 * 24 * 60 * 60, order_quantity, 0)) AS day1,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 0 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 24 * 60 * 60, order_quantity, 0)) AS day0
                    ';
        } else {
            $sql = 'SELECT
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 5 * 24 * 60 * 60, 1, 0)) AS day5,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 4 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 5 * 24 * 60 * 60, 1, 0)) AS day4,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 3 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 4 * 24 * 60 * 60, 1, 0)) AS day3,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 2 * 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 3 * 24 * 60 * 60, 1, 0)) AS day2,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 24 * 60 * 60 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 2 * 24 * 60 * 60, 1, 0)) AS day1,
                        SUM(IF (TIMESTAMPDIFF(SECOND, created_at, NOW()) >= 0 and TIMESTAMPDIFF(SECOND, created_at, NOW()) < 24 * 60 * 60, 1, 0)) AS day0
                    ';
        }
        $thirtyDaysAgo = Carbon::now(getTimezone())->subDays(30)->startOfDay()->toDateString();
        $sql .= "FROM
                    sale_order
                WHERE
                    (created_at) >= '{$thirtyDaysAgo}'
                    AND is_test = 0
                    AND order_status IN ('new_order', 'in_production')";

        if (!empty($input['type_order_tiktok'])) {
            $sql .= " AND FIND_IN_SET('" . Tag::TIKTOK_TAG_ID . "', sale_order.tag)";
        }

        if (empty($input['warehouse'])) {
            $sql .= ' AND warehouse_id = ' . config('jwt.warehouse_id');
        } elseif ($input['warehouse'] != 'all') {
            $sql .= ' AND warehouse_id = ' . $input['warehouse'];
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $warehouseIds = $user->warehouses->pluck('id')->toArray();
                $sql .= ' AND warehouse_id in (' . implode(',', $warehouseIds) . ')';
            }
        }
        if (!empty($input['store_id'])) {
            $sql .= ' AND store_id in(' . implode(',', $input['store_id']) . ')';
        }

        return DB::select($sql);
    }

    public function getAging($input)
    {
        $orderAging = (array) $this->getOrderAging($input)[0] ?? [];
        $dataAging = [];
        for ($index = 0; $index < 6; $index++) {
            $name = $index < 5 ? "$index" : '5+';
            $dataAging[] = [
                'name' => $index < 5 ? "$index" : '5+',
                'total' => !empty($orderAging["day$index"]) ? $orderAging["day$index"] : 0
            ];
        }

        return $dataAging;
    }

    public function updateFoldingForSaleOrder($saleOrderId)
    {
        $dataUpdate = [
            'order_folding_status' => SaleOrder::ALL_ITEM_BARCODE_FOLDING,
            'order_folding_at' => date('Y-m-d H:i:s')
        ];

        return SaleOrder::where('id', $saleOrderId)->update($dataUpdate);
    }

    public function manualTracking($inputs, $id)
    {
        $saleOrder = SaleOrder::findOrderMissShipped($id);
        if (!$saleOrder) {
            throw new Exception('This order does not satisfy the condition!', Response::HTTP_BAD_REQUEST);
        }

        try {
            DB::beginTransaction();
            $shipment = Shipment::create([
                'carrier_code' => $inputs['carrier_code'],
                'tracking_number' => $inputs['tracking_number'],
                'is_manual' => Shipment::IS_MANUAL_TRACKING,
                'ship_date' => $inputs['ship_date'],
                'shipment_cost' => $inputs['price'],
                'order_id' => $id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'account_id' => $saleOrder->account_id,
                'store_id' => $saleOrder->store_id
            ]);

            $saleOrder->order_status = SaleOrder::STATUS_SHIPPED;
            if (is_null($saleOrder->shipment_id)) {
                $saleOrder->shipment_id = $shipment->id;
            }
            $saleOrder->save();

            $date = Carbon::parse($shipment->ship_date)->format('Y-m-d');
            $saleOrderHistory = SaleOrderHistory::create([
                'user_id' => auth()->user()['id'],
                'order_id' => $id,
                'type' => SaleOrderHistory::MANUAL_TRACKING_TYPE,
                'message' => "Created tracking number $shipment->tracking_number of $shipment->carrier_code carrier with ship date on $date for $$shipment->shipment_cost",
                'created_at' => now()
            ]);
            DB::commit();

            return $saleOrderHistory;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function retryCreateThumbForOrder($id)
    {
        $orderId = SaleOrder::find($id);
        if (!$orderId) {
            return response()->json(['message' => 'Order not found!'], Response::HTTP_NOT_FOUND);
        }
        try {
            SaleOrderItemImage::where('order_id', $id)
                ->update([
                    'thumb_750' => 0,
                    'skip_retry' => 0,
                    'retry_count' => 0,
                    'thumb_250' => 0,
                    'color_new' => null,
                    'upload_s3_status' => 0,
                ]);

            return response()->json(['message' => 'Retry create thumb for order success'], Response::HTTP_OK);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function forecast($inputs)
    {
        setTimezone();
        $timeZonePST = 'America/Los_Angeles';
        $date = $inputs['date'];
        $warehouseId = $inputs['warehouse_id'] ?? null;
        $storeId = $inputs['store_id'] ?? null;
        $type = $inputs['type']; //0 = new_order , 1= shipped
        $currentTimePST = Carbon::now($timeZonePST);
        $currentHour = $currentTimePST->hour;
        $timeAppendText = $currentHour < 12 ? 'AM' : 'PM';
        $speedForecast = [];
        $isCurrent = Carbon::parse($date)->copy()->toDateString() == $currentTimePST->copy()->toDateString() ? 1 : 0;
        if ($type == 1) {
            ///Data cho Chart Pie (chi show trong man shipped)
            $countSpeedForecast = SaleOrder::countOrderByStatus($warehouseId, $storeId);
            $countShippedLast = Shipment::countOrderShippedByRange($currentTimePST->copy()->subDays(6)->startOfDay()->format('Y-m-d H:i:s'), $warehouseId, $storeId);
            $speedForecast['new_order'] = !$countSpeedForecast->isEmpty() && $countSpeedForecast->has('new_order') ? $countSpeedForecast['new_order'] : 0;
            $speedForecast['in_production'] = !$countSpeedForecast->isEmpty() && $countSpeedForecast->has('in_production') ? $countSpeedForecast['in_production'] : 0;
            $speedForecast['total'] = $speedForecast['new_order'] + $speedForecast['in_production'];
            $speedForecast['percent_new_order'] = $speedForecast['total'] == 0 ? 0 : ceil($speedForecast['new_order'] / $speedForecast['total'] * 100);
            $speedForecast['percent_in_production'] = $speedForecast['total'] == 0 ? 0 : (100 - $speedForecast['percent_new_order']);
            $speedForecast['total_shipped'] = $countShippedLast ? $countShippedLast->count : 0;
            $speedForecast['avg_shipped'] = ceil($speedForecast['total_shipped'] / 7);
            $speedForecast['est_shipped'] = $speedForecast['avg_shipped'] == 0 ? 0 : ceil($speedForecast['total'] / $speedForecast['avg_shipped']);
        }

        if (!$isCurrent) {
            //past
            //Total New Order
            $startTimeSummaryLastDay = Carbon::parse($date)->copy()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeSummaryLastDay = Carbon::parse($date)->copy()->endOfDay()->format('Y-m-d H:i:s');
            $totalCurrent = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeSummaryLastDay, $endTimeSummaryLastDay, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeSummaryLastDay, $endTimeSummaryLastDay, $warehouseId, $storeId);
            $totalOrder = [
                'count' => $totalCurrent,
                'percent' => 0,
            ];
            $totalCurrentAvgHour = $totalCurrent / 24;
            $totalOrderAvg = [
                'count' => ceil($totalCurrentAvgHour),
                'percent' => 0,
            ];
            $totalOrderIncoming = [
                'count' => 0,
                'percent' => 0,
            ];
            $startTime = Carbon::parse($date)->format('Y-m-d 00:00:00');
            $endTime = Carbon::parse($date)->format('Y-m-d 23:59:59');
            $saleOrders = $this->countAndMapOrders($startTime, $endTime, $warehouseId, $storeId, $type);
            $forecastPast = $this->getForecastPast($date, $warehouseId, $storeId, $type);

            return [
                'total_order' => $totalOrder,
                'total_order_hour' => $totalOrderAvg,
                'total_order_day' => $totalOrderIncoming,
                'list_order' => $saleOrders,
                'speed_forecast' => $speedForecast,
                'forecast_past' => $forecastPast,
                'time' => "$currentHour:00 $timeAppendText",
                'is_current' => $isCurrent
            ];
        } else {
            ///Current
            ///AVG new order / hour
            [$totalCurrentAvgHour, $totalCurrentAvgLastHour, $incomingForeCast, $incomingForeCastLastHour]
                = $this->calculateAvgOrderByCurrentTime($currentHour, $currentTimePST, $type, $warehouseId, $storeId);
            $totalOrderAvg = [
                'count' => ceil($totalCurrentAvgHour),
                'percent' => $totalCurrentAvgLastHour ? ceil(($totalCurrentAvgHour - $totalCurrentAvgLastHour) / $totalCurrentAvgLastHour * 100) : 0,
            ];
            $totalOrderIncoming = [
                'count' => ceil($incomingForeCast),
                'percent' => $incomingForeCastLastHour ? ceil(($incomingForeCast - $incomingForeCastLastHour) / $incomingForeCastLastHour * 100) : 0,
            ];
            //Total New Order
            $startTimeSummaryBefore1Hour = $currentTimePST->copy()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeSummaryBefore1Hour = $currentTimePST->copy()->subHour()->endOfHour()->format('Y-m-d H:i:s');
            $endTimeSummaryBefore2Hour = $currentTimePST->copy()->subHours(2)->endOfHour()->format('Y-m-d H:i:s');
            $totalCurrent = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeSummaryBefore1Hour, $endTimeSummaryBefore1Hour, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeSummaryBefore1Hour, $endTimeSummaryBefore1Hour, $warehouseId, $storeId);
            $totalCurrentLastHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeSummaryBefore1Hour, $endTimeSummaryBefore2Hour, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeSummaryBefore1Hour, $endTimeSummaryBefore2Hour, $warehouseId, $storeId);
            $totalOrder = [
                'count' => $totalCurrent,
                'percent' => $totalCurrentLastHour ? ceil(($totalCurrent - $totalCurrentLastHour) / $totalCurrentLastHour * 100) : 0,
            ];

            $startTime = Carbon::parse($date)->format('Y-m-d 00:00:00');
            $endTime = $currentTimePST->copy()->subHour()->endOfHour()->format('Y-m-d H:i:s');
            if ($startTime > $endTime) {
                //Thoi diem 0h den 1h AM
                $endTime = Carbon::parse($date)->format('Y-m-d 00:59:59');
            }
            $startTimeLastWeek = Carbon::parse($startTime)->subDays(7)->format('Y-m-d H:i:s');
            $endTimeLastWeek = Carbon::parse($startTime)->subDays(7)->endOfDay()->format('Y-m-d H:i:s');

            $saleOrders = $this->countAndMapOrders($startTime, $endTime, $warehouseId, $storeId, $type);
            $saleOrdersLastWeek = $this->countAndMapOrders($startTimeLastWeek, $endTimeLastWeek, $warehouseId, $storeId, $type);
            $totalOrdersLastWeek = array_reduce($saleOrdersLastWeek, function ($carry, $item) {
                return $carry + $item['count'];
            }, 0);

            $indexForeCast = 0;
            $e = array_column($saleOrders, 'count');
            foreach ($saleOrders as $index => &$itemOrder) {
                if (($currentHour == 0 && $index == $currentHour) || ($currentHour != 0 && $index == $currentHour - 1)) {
                    $slice = array_slice($e, 0, $index + 1);
                    $itemOrder['forecast_incoming'] = array_sum($slice) / $itemOrder['hour'] * (24 - $itemOrder['hour']);
                    $itemOrder['forecast_total'] = $itemOrder['forecast_incoming'] + array_sum($slice);
                    $indexForeCast = $index;
                }
            }

            foreach ($saleOrdersLastWeek as &$itemOrder) {
                $itemOrder['proportion'] = $totalOrdersLastWeek ? $itemOrder['count'] / $totalOrdersLastWeek : 0;
            }

            foreach ($saleOrders as $index => &$itemOrder2) {
                if ($index >= $currentHour) {
                    $itemOrder2['count'] = $saleOrders[$indexForeCast]['forecast_total'] * $saleOrdersLastWeek[$index]['proportion'];
                    $itemOrder2['is_forecast_hour'] = 1;
                }
            }
            $forecastPast = $this->getForecastPast($date, $warehouseId, $storeId, $type);
            $filteredCollection = collect($forecastPast)->filter(function ($item) use ($currentHour) {
                return $item['hour'] <= $currentHour;
            });
            $forecastPast = $filteredCollection->values()->all();

            return [
                'total_order' => $totalOrder,
                'total_order_hour' => $totalOrderAvg,
                'total_order_day' => $totalOrderIncoming,
                'list_order' => $saleOrders,
                'speed_forecast' => $speedForecast,
                'time' => "$currentHour:00 $timeAppendText",
                'is_current' => $isCurrent,
                'forecast_past' => $forecastPast,
            ];
        }
    }

    protected function getForecastPast($date, $warehouseId, $storeId, $type): array
    {
        $type = $type == 0 ? 'new_order' : 'shipped';
        $query = ForecastIncomingSaleOrder::where('date', $date)->where('type', $type)
            ->select('forecast', 'hour');
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        } else {
            $query->whereNull('warehouse_id');
        }
        if ($storeId) {
            $query->where('store_id', $storeId);
        } else {
            $query->whereNull('store_id');
        }
        $data = $query->groupBy('hour')->get();

        return $this->fillMissingHours($data->isEmpty() ? [] : $data->toArray());
    }

    private function fillMissingHours($data): array
    {
        if (empty($data)) {
            return array_map(function ($hour) {
                return ['forecast' => 0.0, 'hour' => $hour];
            }, range(1, 24));
        }
        foreach ($data as &$item) {
            $item['hour']++;
        }
        $result = [];
        for ($hour = 1; $hour <= 24; $hour++) {
            $entry = array_filter($data, function ($item) use ($hour) {
                return $item['hour'] == $hour;
            });
            if (empty($entry)) {
                $result[] = ['forecast' => 0.0, 'hour' => $hour];
            } else {
                $result[] = reset($entry);
            }
        }

        return $result;
    }

    protected function calculateAvgOrderByCurrentTime($currentHour, $currentTimePST, $type, $warehouseId, $storeId)
    {
        if ($currentHour == 0) {
            $startTimeAvg = $currentTimePST->copy()->subDay()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeAvg = $currentTimePST->copy()->subDay()->endOfDay()->format('Y-m-d H:i:s');
            $endTimeAvgBefore1Hour = $currentTimePST->copy()->subDay()->endOfDay()->subHour()->format('Y-m-d H:i:s');
            $totalCurrentAvgHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId);
            $totalCurrentAvgHour = $totalCurrentAvgHour / 24;
            $totalCurrentAvgLastHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvgBefore1Hour, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvgBefore1Hour, $warehouseId, $storeId);

            $totalCurrentAvgLastHour = $totalCurrentAvgLastHour / 23;
            $incomingForeCast = $totalCurrentAvgHour * (24 - $currentHour);
            $incomingForeCastLastHour = 0;
        } elseif ($currentHour == 1) {
            $startTimeAvg = $currentTimePST->copy()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeAvg = $currentTimePST->copy()->subHour()->endOfHour()->format('Y-m-d H:i:s');
            $startTimeAvgBefore1Hour = $currentTimePST->copy()->subDay()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeAvgBefore1Hour = $currentTimePST->copy()->subDay()->endOfDay()->format('Y-m-d H:i:s');
            $totalCurrentAvgHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId);
            $totalCurrentAvgHour = $totalCurrentAvgHour / $currentHour;
            $totalCurrentAvgLastHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvgBefore1Hour, $endTimeAvgBefore1Hour, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvgBefore1Hour, $endTimeAvgBefore1Hour, $warehouseId, $storeId);
            $totalCurrentAvgLastHour = $totalCurrentAvgLastHour / 24;

            $incomingForeCast = $totalCurrentAvgHour * (24 - $currentHour);
            $incomingForeCastLastHour = 0;
        } else {
            $startTimeAvg = $currentTimePST->copy()->startOfDay()->format('Y-m-d H:i:s');
            $endTimeAvg = $currentTimePST->copy()->subHour()->endOfHour()->format('Y-m-d H:i:s');
            $endTimeAvgBefore1Hour = $currentTimePST->copy()->subHours(2)->endOfHour()->format('Y-m-d H:i:s');

            $totalCurrentAvgHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvg, $warehouseId, $storeId);
            $totalCurrentAvgHour = $totalCurrentAvgHour / $currentHour;
            $totalCurrentAvgLastHour = $type == 0
                ? SaleOrder::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvgBefore1Hour, $warehouseId, $storeId)
                : Shipment::countTotalOrdersByRangeTime($startTimeAvg, $endTimeAvgBefore1Hour, $warehouseId, $storeId);
            $totalCurrentAvgLastHour = $totalCurrentAvgLastHour / ($currentHour - 1);

            $incomingForeCast = $totalCurrentAvgHour * (24 - $currentHour);
            $incomingForeCastLastHour = $totalCurrentAvgLastHour * (24 - $currentHour + 1);
        }

        return [$totalCurrentAvgHour, $totalCurrentAvgLastHour, $incomingForeCast, $incomingForeCastLastHour];
    }

    public function countAndMapOrders($startTime, $endTime, $warehouseId, $storeId, $type)
    {
        $saleOrders = $type == 0
            ? SaleOrder::countAllOrderByRangeTimeByQuery($startTime, $endTime, $warehouseId, $storeId)
            : Shipment::countOrderByRangeShipTimeByQuery($startTime, $endTime, $warehouseId, $storeId);
        $saleOrders = $saleOrders->isEmpty() ? [] : $saleOrders->toArray();

        return $this->mapForecastHour($saleOrders);
    }

    public function mapForecastHour($orders)
    {
        $hoursPresent = $orders ? array_column($orders, 'hour') : [];
        $allHours = range(0, 23);
        $missingHours = array_diff($allHours, $hoursPresent);
        if (!empty($missingHours)) {
            foreach ($missingHours as $item) {
                $orders[] = [
                    'hour' => $item,
                    'count' => 0,
                ];
            }
        }
        $hours = array_column($orders, 'hour');
        array_multisort($hours, $orders);
        foreach ($orders as &$item) {
            $item['hour']++;
        }

        return $orders;
    }

    public $header = [
        'Order reference ID *' => true,
        'Test order *' => true,
        'Order status *' => true,
        'Order Type' => false,
        'Label URL' => false,
        'Name *' => true,
        'Email' => false,
        'Company' => false,
        'Phone' => false,
        'Street 1' => false,
        'Street 2' => false,
        'City' => false,
        'State' => false,
        'Country' => false,
        'Zip' => false,
        'Line item ID *' => true,
        'Line item SKU *' => true,
        'Line item name *' => true,
        'Quantity *' => true,
        'Print area key 1 *' => true,
        'Artwork URL 1 *' => true,
        'Preview URL 1' => false,
        'Packing slip URL' => false,
        'Packing slip size' => false,
        'Gift message URL' => false,
        'Gift message content' => false,
        'Gift message size' => false,
        'Shipping method *' => true,
        'Tax ID' => false,
        'Tax ID type' => false
    ];

    public function transformOrder($header, $rows)
    {
        // get first row
        $row = $rows->first();

        $lineItems = $this->transformLineItems($header, $rows);
        $status = $row['Test order *'] == 'yes' ? true : (empty($row['Order status *']) ? 'no' : false);
        $orderType = '';
        if (!empty($row['Order Type'])) {
            $orderType = in_array($row['Order Type'], SaleOrder::ACCEPTED_CSV_IMPORTING_ORDER_TYPES)
                ? $row['Order Type']
                : SaleOrder::INVALID_CSV_IMPORTING_ORDER_TYPES;
        }

        // check default shipping method
        if (in_array(strtolower((string) $row['Shipping method *']), ['economy', 'standard', 'express', 'priority'])) {
            $row['Shipping method *'] = strtolower((string) $row['Shipping method *']);
        }

        // create $order same as SaleOrder
        return [
            'order_id' => (string) $row['Order reference ID *'],
            'test_order' => $status,
            'order_status' => strtolower((string) $row['Order status *']),
            'order_type' => $orderType,
            'label_url' => strval($row['Label URL']) ?? '',
            'line_items' => $lineItems,
            'address' => $this->transformAddress($row),
            'shipping_method' => (string) $row['Shipping method *'],
            'tax_id' => (string) $row['Tax ID'],
            'tax_id_type' => (string) $row['Tax ID type'],
            'insert' => $this->transformInsert($row),
            'order_source' => SaleOrder::ORDER_SOURCE_CSV
        ];
    }

    public function transformLineItems($header, $rows)
    {
        // get all print file key
        $printAreaKeys = $header->filter(function ($item) {
            return Str::contains($item, 'Print area key');
        });
        if ($printAreaKeys->isEmpty()) {
            return [];
        }

        $lineItems = [];

        foreach ($rows as $row) {
            $printFiles = [];
            $previewFiles = [];
            $indexKey = 0;
            foreach ($printAreaKeys as $printAreaKey) {
                $indexKey++;

                if (!empty($row['Artwork URL ' . $indexKey . ' *'])) {
                    $printFiles[] = [
                        'key' => strtolower($row[$printAreaKey]),
                        'url' => $row['Artwork URL ' . $indexKey . ' *'],
                    ];
                }
                // if not empty
                if (!empty($row['Preview URL ' . $indexKey])) {
                    $previewFiles[] = [
                        'key' => strtolower($row[$printAreaKey]),
                        'url' => $row['Preview URL ' . $indexKey],
                    ];
                }
            }
            // convert to SaleOrderItem
            $lineItems[] = [
                'order_item_id' => $row['Line item ID *'],
                'sku' => strtoupper(trim($row['Line item SKU *'])),
                'name' => $row['Line item name *'],
                'quantity' => (int) $row['Quantity *'],
                'print_files' => $printFiles,
                'preview_files' => $previewFiles,
            ];
        }

        return $lineItems;
    }

    public function transformAddress($row)
    {
        $address = [
            'name' => $row['Name *'],
            'email' => $row['Email'] ?? '',
            'company' => $row['Company'],
            'phone' => $row['Phone'],
            'street1' => $row['Street 1'],
            'street2' => $row['Street 2'],
            'city' => $row['City'],
            'state' => $row['State'],
            'country' => $row['Country'],
            'zip' => $row['Zip'],
        ];
        // if not empty convert to string
        foreach ($address as $key => $value) {
            if (!empty($value)) {
                $address[$key] = (string) $value;
            }
        }

        return $address;
    }

    public function transformInsert($row)
    {
        $insert = [];
        if (!empty($row['Packing slip URL'])) {
            $insert[] = [
                'name' => 'packing_slip',
                'url' => (string) $row['Packing slip URL'],
                'size' => strtolower((string) $row['Packing slip size']),
            ];
        }
        if (!empty($row['Gift message URL']) || !empty($row['Gift message content'])) {
            $insert[] = [
                'name' => 'gift_message',
                'url' => (string) $row['Gift message URL'],
                'message' => (string) $row['Gift message content'],
                'size' => strtolower((string) $row['Gift message size']),
            ];
        }

        return empty($insert) ? null : $insert;
    }

    public function insertSaleOrderClaimSupport($request)
    {
        $issue = $request->input('issue');
        $solution = $request->input('solution');
        $type = $request->input('type');
        $desc = $request->input('desc');
        $email = $request->input('email');
        $order_item_ids = $request->input('order_item_id');
        $store_id = $request->input('store_id');
        $order_id = $request->input('order_id');

        try {
            DB::beginTransaction();
            if ($type == SaleOrderClaimSupport::TYPE_ORDER_LOST_IN_TRANSIT) {
                $saleOrderClaim = SaleOrderClaimSupport::create([
                    'issue' => $issue,
                    'solution' => $solution,
                    'type' => $type,
                    'additional_details' => $desc,
                    'customer_email' => $email,
                    'store_id' => $store_id,
                    'order_id' => $order_id,
                    'created_at' => now(),
                    'updated_at' => null
                ]);
                $saleOrderClaim->ticket_number = 'SL-' . sprintf('%06s', $saleOrderClaim->id);
                $saleOrderClaim->save();
            } else {
                $saleOrderClaim = SaleOrderClaimSupport::create([
                    'issue' => $issue,
                    'solution' => $solution,
                    'type' => $type,
                    'additional_details' => $desc,
                    'customer_email' => $email,
                    'store_id' => $store_id,
                    'order_id' => $order_id,
                    'sale_order_item_id' => $order_item_ids,
                    'created_at' => now(),
                    'updated_at' => null
                ]);
                $saleOrderClaim->ticket_number = 'SL-' . sprintf('%06s', $saleOrderClaim->id);
                $saleOrderClaim->save();
                $uploadedFiles = $request->file('files');
                foreach ($uploadedFiles as $index => $file) {
                    $prefix = 'seller/proof';
                    $binaryData = $file->get();
                    $uniqueFilename = Str::uuid()->toString() . '.' . $file->getClientOriginalExtension();
                    // $pathName = Storage::disk('public')->put('/seller/proof/' . $uniqueFilename, $binaryData);
                    $pathName = Storage::disk('s3')->put('/seller/proof/' . $uniqueFilename, $binaryData);
                    // $pathName = Storage::disk('s3')->put($prefix, $filename);
                    if (substr($pathName, 0, strlen($prefix)) == $prefix) {
                        $pathName = substr($pathName, strlen($prefix) + 1);
                    }
                    SaleOrderClaimSupportImage::create([
                        'sale_order_claim_support_id' => $saleOrderClaim->id,
                        'link_url' => env('AWS_S3_URL', '') . '/' . $prefix . '/' . $uniqueFilename,
                    ]);
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return response()->json(['error' => $e->getMessage()], 500);
        }
        handleJob(SaleOrderClaimSupport::JOB_SEND_MAIL_SELLER_SUPPORT, $saleOrderClaim->id);

        return empty($saleOrderClaim) ? null : response()->json(['message' => 'Submit form successfully'], 200);
    }

    public function getClaimOrder($filter)
    {
        setTimezone();
        $tickets = SaleOrderClaimSupport::query()
            ->with(['saleOrder', 'store', 'images', 'assigne', 'resolveBy'])
            // ->when($filter['store'], function ($query) use ($filter) {
            //     return $query->where('store_id', $filter['id']);
            // })
            ->when($filter['store'], function ($query) use ($filter) {
                return $query->whereHas('store', function ($subQuery) use ($filter) {
                    $subQuery->where('name', 'like', '%' . $filter['store'] . '%');
                });
            })
            ->when($filter['order_number'], function ($query) use ($filter) {
                return $query->whereHas('saleOrder', function ($subQuery) use ($filter) {
                    $subQuery->where('order_number', 'like', '%' . $filter['order_number'] . '%');
                });
            })
            ->when($filter['ref_number'], function ($query) use ($filter) {
                return $query->whereHas('saleOrder', function ($subQuery) use ($filter) {
                    $subQuery->where('external_number', 'like', '%' . $filter['ref_number'] . '%');
                });
            })
            ->when($filter['ticket_number'], function ($query) use ($filter) {
                return $query->where('ticket_number', $filter['ticket_number']);
            })
            ->when($filter['email'], function ($query) use ($filter) {
                return $query->where('customer_email', $filter['email']);
            })
            ->when($filter['status'], function ($query) use ($filter) {
                return $query->where('status', $filter['status']);
            })
            ->when($filter['issue'], function ($query) use ($filter) {
                return $query->where('type', $filter['issue']);
            })
            ->when($filter['resolution'], function ($query) use ($filter) {
                return $query->where('solution', $filter['resolution']);
            })
            ->when($filter['start_date'], function ($query) use ($filter) {
                return $query->where('created_at', '>=', Carbon::parse($filter['start_date'])->startOfDay()->format('Y-m-d H:i:s'));
            })
            ->when($filter['end_date'], function ($query) use ($filter) {
                return $query->where('created_at', '<=', Carbon::parse($filter['end_date'])->endOfDay()->format('Y-m-d H:i:s'));
            })->orderBy($this->sortColumn ?? 'id', $this->sortBy ?? 'desc')
            ->paginate($this->limit);
        $tickets->getCollection()->transform(function ($value) {
            $value->age = $this->diffTime($value->created_at, $value->resolved_at);

            return $value;
        });

        return $tickets;
    }

    public function saveComment($request)
    {
        // Validate incoming request data
        $validatedData = $request->validate([
            'employee_id' => 'required|integer',
            'claim_order_id' => 'required|integer',
            'action' => 'required|string|max:255',
            'note' => 'required|string|max:255',
        ]);

        $comment = new SaleOrderClaimSupportHistory();
        $comment->employee_id = $validatedData['employee_id'];
        $comment->claim_order_id = $validatedData['claim_order_id'];
        $comment->action = $validatedData['action'];
        $comment->note = $validatedData['note'];
        $comment->save();

        return response()->json($comment, 201);
    }

    public function getComment($request)
    {
        setTimezone();
        $validatedData = $request->validate([
            'claim_order_id' => 'required|integer',
        ]);
        $collection = SaleOrderClaimSupportHistory::with('employee:id,name')
            ->where('claim_order_id', $validatedData['claim_order_id'])
            ->get();

        return response()->json($collection);
    }

    public function insertSaleOrderClaimSupportFeedback($request)
    {
        $inputTicket = $request->only(['sale_order_claim_support_id', 'employee_id', 'status', 'feedback']);
        DB::beginTransaction();
        $user = Employee::find($inputTicket['employee_id']);
        $claim = SaleOrderClaimSupport::find($inputTicket['sale_order_claim_support_id']);
        $claimHistory = [
            'claim_order_id' => $inputTicket['sale_order_claim_support_id'],
            'employee_id' => $inputTicket['employee_id'],
            'action' => 'update',
            'note' => 'The status was updated from ' . $this->getClassStatus($claim->status) . ' to ' . $this->getClassStatus($inputTicket['status']) . ' by ' . $user->name,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
        SaleOrderClaimSupportHistory::create($claimHistory);
        $claim->status = $inputTicket['status'];
        if ($inputTicket['status'] != 'in_review') {
            $claim->resolved_by = $inputTicket['employee_id'];
            $claim->resolved_at = Carbon::now();
        }
        $claim->save();
        if ($inputTicket['status'] != 'in_review') {
            $ticket = SaleOrderClaimSupportFeedback::create($inputTicket);
            $ticket->save();
            $this->createFileForTicket($request, $claim);
            // chỉ gửi mail thông báo vs claim tạo sau 2024-03-15
            // if ($claim->created_at->isAfter(Carbon::parse('2024-03-15'))) {
            handleJob(SaleOrderClaimSupport::JOB_SEND_MAIL_SELLER_SUPPORT, $claim->id);
            // }
        }
        DB::commit();
    }

    public function createFileForTicket($request, $ticket)
    {
        if ($request->has('file')) {
            $files = $request->file('file');
            foreach ($files as $index => $file) {
                $prefix = 'seller/feedback';
                $filename = $ticket->id . '-' . $index . '.' . $file->getClientOriginalExtension();
                $binaryData = $file->get();
                $pathName = Storage::disk('s3')->put('/seller/feedback/' . $filename, $binaryData);
                if (substr($pathName, 0, strlen($prefix)) == $prefix) {
                    $pathName = substr($pathName, strlen($prefix) + 1);
                }
                SaleOrderClaimSupportFeedbackFile::create([
                    'sale_order_claim_support_id' => $ticket->id,
                    'file' => env('AWS_S3_URL', '') . '/' . $prefix . '/' . $filename,
                ]);
            }
        }
    }

    public function getClassStatus($status)
    {
        $classStatus = '';
        switch ($status) {
            case 'new':
                $classStatus = 'New';
                break;
            case 'in_progress':
                $classStatus = 'In Progress';
                break;
            case 'in_review':
                $classStatus = 'In Review';
                break;
            case 'approve':
                $classStatus = 'Approve';
                break;
            case 'rejected':
                $classStatus = 'Rejected';
                break;
            case 'closed':
                $classStatus = 'Closed';
                break;
            default:
                $classStatus = 'New';
        }

        return $classStatus;
    }

    public function generateName()
    {
        DB::beginTransaction();
        $claims = SaleOrderClaimSupport::whereNull('ticket_number')->get();

        foreach ($claims as $item) {
            $item->ticket_number = 'SL-' . sprintf('%06s', $item->id);
            $item->save();
        }
        DB::commit();
    }

    public function assignClaim($request)
    {
        $claim = SaleOrderClaimSupport::find($request['claim_order_id']);
        $currentUser = Employee::find($claim->assign);
        $currentUser = $currentUser ? $currentUser->name : 'Unassign';
        $claim->assign = $request['employee_assign_id'];
        $claim->save();

        $user = Employee::find($request['employee_id']);
        $userAssign = Employee::find($request['employee_assign_id']);
        $claimHistory = [
            'claim_order_id' => $request['claim_order_id'],
            'employee_id' => $request['employee_id'],
            'action' => 'update',
            'note' => 'The assignee was updated from ' . $currentUser . ' to ' . $userAssign->name . ' by ' . $user->name,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
        SaleOrderClaimSupportHistory::create($claimHistory);
    }

    public function updateStatusClaimOrder($request)
    {
        $claim = SaleOrderClaimSupport::find($request['claim_order_id']);
        if ($request) {
            $currentUser = Employee::find($claim->assign);
        }
        $currentUser = $currentUser ? $currentUser->name : 'Unassign';
        $claim->assign = $request['employee_assign_id'];
        $claim->save();

        $user = Employee::find($request['employee_id']);
        $userAssign = Employee::find($request['employee_assign_id']);
        $claimHistory = [
            'claim_order_id' => $request['claim_order_id'],
            'employee_id' => $request['employee_id'],
            'action' => 'update',
            'note' => 'The status was updated from ' . $currentUser . ' to ' . $userAssign->name . ' by ' . $user->name,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
        SaleOrderClaimSupportHistory::create($claimHistory);
    }

    public function getCountClaim()
    {
        $count = SaleOrderClaimSupport::whereIn('status', ['new', 'in_review'])->count();

        return $count;
    }

    private function diffTime($startDate, $endDate): string
    {
        if (!$endDate) {
            return '';
        }
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        $age = '';
        $days = date_diff($startDate, $endDate)->days;
        if ($days > 1) {
            $age .= $days . ' days ';
        } elseif ($days == 1) {
            $age .= '1 day ';
        }
        $hour = date_diff($startDate, $endDate)->h;
        if ($hour > 1) {
            $age .= $hour . ' hours ';
        } elseif ($hour == 1) {
            $age .= '1 hour ';
        }
        $minute = date_diff($startDate, $endDate)->i;
        if ($minute > 1) {
            $age .= $minute . ' minutes ';
        } elseif ($minute == 1) {
            $age .= '1 minute ';
        }

        return $age ? $age : 'a few seconds';
    }

    public function getListClaim($filter)
    {
        setTimezone();
        $filter['store_id'] = [Auth::id()];
        $user = auth()->user();

        if ($user->is_login_support) {
            $filter['store_id'] = $user->is_admin ? [] : $user->store_ids;
        }

        $tickets = SaleOrderClaimSupport::query()
            ->with(['saleOrder', 'store:id,name'])
            ->whereHas('store', function ($query) {
                $query->where('is_active', Store::STATUS_ACTIVE);
            })
            ->when($filter['status'], function ($query) use ($filter) {
                return $query->where('status', $filter['status']);
            })
            ->when($filter['type'], function ($query) use ($filter) {
                return $query->where('type', $filter['type']);
            })
            ->when($filter['resolution'], function ($query) use ($filter) {
                return $query->where('solution', $filter['resolution']);
            })
            ->when($filter['key_search'], function ($query) use ($filter) {
                return $query->whereHas('saleOrder', function ($query) use ($filter) {
                    $query->where('encode_id', $filter['key_search'])
                        ->orWhere('external_number', $filter['key_search']);
                });
            });

        if (!empty($filter['store_id'])) {
            $tickets->whereIn('store_id', $filter['store_id']);
        }

        if (empty($filter['status'])) {
            if (empty($filter['status'])) {
                $tickets->orderByRaw("
                    CASE
                        WHEN status = 'new' THEN 0
                        WHEN status = 'in_review' THEN 1
                        ELSE 2
                    END
                ");
            }
        }

        $tickets->orderBy($this->sortColumn ?? 'id', $this->sortBy ?? 'desc');

        return $tickets->paginate($this->limit);
    }

    public function getDetailClaim($id, $request)
    {
        $user = auth()->user();
        setTimezone();
        $claim = SaleOrderClaimSupport::find($id);
        $itemIds = explode(',', $claim->sale_order_item_id);
        $order = SaleOrderClaimSupport::with([
            'images',
            'saleOrder',
            'feedback',
            'feedbackFiles',
            'saleOrder.items' => function ($query) use ($itemIds) {
                $query->whereIn('id', $itemIds)
                    ->select([
                        'id',
                        'name',
                        'order_id',
                        'product_sku',
                        'sku',
                        'quantity',
                        'product_id',
                        'options',
                        'unit_price',
                        'blank_price',
                        'handling_fee',
                        'product_style_sku',
                        'product_id',
                        'product_color_sku',
                        'product_size_sku'
                    ])
                    ->with([
                        'images' => function ($imageQuery) {
                            $imageQuery->select(['id', 'order_item_id', 'image_url', 'image_width', 'image_height', 'print_side'])
                                ->with(['printSizeType:code,name']);
                        },
                        'getTypeProduct' => function ($styleQuery) {
                            $styleQuery->select('sku', 'name');
                        },
                        'product' => function ($productQuery) {
                            $productQuery->select(['id', 'brand_id'])
                                ->with(['brand:id,name']);
                        },
                        'productColor',
                        'productSize'
                    ]);
            }
        ])
            ->where('id', $id)
            ->first();

        return $order;
    }

    public function getCountClaimStatus()
    {
        return SaleOrderClaimSupport::select(['status', DB::raw('COUNT(id) AS quantity')])->groupBy('status')->get();
    }

    public function updateSurchargeFee($saleOrder, $saleOrderItem, $surchargeName)
    {
        $surchargeFee = SurchargeFee::where('store_id', $saleOrder->store_id)
            ->where('type', $surchargeName)
            ->first();

        if ($surchargeFee) {
            SaleOrderItemSurchargeFee::updateOrCreate(
                [
                    'name' => $surchargeFee->type,
                    'order_item_id' => $saleOrderItem->id,
                    'order_id' => $saleOrder->id,
                ],
                ['value' => $saleOrderItem->quantity * $surchargeFee->value],
            );
        }

        return $surchargeFee;
    }

    public function updateOrderSurchargeFee($saleOrder, $peakShippingFee, $surchargeName)
    {
        $value = 0.00;

        switch ($saleOrder->shipping_method) {
            case SaleOrder::SHIPPING_METHOD_STANDARD:
                $value = $peakShippingFee->standard_fee;
                break;

            case SaleOrder::SHIPPING_METHOD_PRIORITY:
                $value = $peakShippingFee->priority_fee;
                break;

            case SaleOrder::SHIPPING_METHOD_EXPRESS:
                $value = $peakShippingFee->express_fee;
                break;
        }

        return SaleOrderSurchargeFee::updateOrCreate(
            [
                'type' => $surchargeName,
                'order_id' => $saleOrder->id,
            ],
            ['value' => $value],
        );
    }

    public function holdOrderByVisua($orderId)
    {
        try {
            $saleOrder = SaleOrder::find($orderId);
            $storeOnHold = Setting::where('name', Store::STORE_ON_HOLD_SETTING_TEST)->first();
            $stores = explode(',', $storeOnHold->value) ?? [];
            if (empty($saleOrder) || empty($stores) || (!in_array($saleOrder->store_id, $stores) && !in_array('all', $stores))) {
                return true;
            }
            if ($saleOrder->tag) {
                if (!in_array(Tag::VISUA_DETECTED_ID, explode(',', $saleOrder->tag))) {
                    $saleOrder->tag = $saleOrder->tag . ',' . Tag::VISUA_DETECTED_ID;
                }
            } else {
                $saleOrder->tag = Tag::VISUA_DETECTED_ID;
            }
            if ($saleOrder->order_status != SaleOrder::NEW_ORDER) {
                $saleOrder->save();
                if ($saleOrder->order_status == SaleOrder::ON_HOLD) {
                    SaleOrderOnHold::updateOrCreate([
                        'order_id' => $saleOrder->id,
                    ], [
                        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
                        'user_id' => User::SYSTEM,
                    ]);
                }

                return false;
            }
            $status = $saleOrder->order_status;
            $saleOrder->order_status = SaleOrder::ON_HOLD;
            $saleOrder->save();
            SaleOrderHistory::create([
                'order_id' => $saleOrder->id,
                'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                'message' => 'Order status changed from "' . $status . '" to "on hold" due to IP Violation by Visua',
            ]);
            SaleOrderOnHold::updateOrCreate([
                'order_id' => $saleOrder->id,
            ], [
                'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
                'user_id' => User::SYSTEM,
            ]);

            return true;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public function statisticOrderSla($request)
    {
        setTimezone();

        $userInfo = auth()->user();
        if (empty($request['is_support_login'])) {
            $storeIds = [$userInfo->id];
        } else {
            $storeIds = $request->filled('store_id') ? [$request->store_id] : ($userInfo->store_ids ?? []);
        }
        $date = Carbon::now()->format('Y-m-d');
        $yesterday = Carbon::now()->subDay()->format('Y-m-d');

        // Retrieve product types
        $productTypes = ProductType::select('name')->pluck('name');

        // Retrieve orders
        $orders = SaleOrder::select(
            'product_style.type as product_type',
            DB::raw('COUNT(sale_order_item.id) as item_count'),
        )
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('product_style', 'product_style.name', '=', 'product.style')
            ->where('sale_order.is_test', false)
            ->whereIn('sale_order.order_status', [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_NEW_ORDER])
            ->when(!empty($request['is_support_login']), function ($query) use ($request, $storeIds, $userInfo) {
                $query->when(empty($request['store_id']) && !$userInfo->is_admin, function ($query) use ($storeIds) {
                    return $query->whereIn('sale_order.store_id', $storeIds);
                })
                    ->when(!empty($request['store_id']), function ($query) use ($storeIds) {
                        return $query->whereIn('sale_order.store_id', $storeIds);
                    });
            }, function ($query) use ($storeIds) {
                return $query->whereIn('sale_order.store_id', $storeIds);
            })
            ->groupBy('product_style.type')
            ->orderByDesc('product_style.type')
            ->orderByDesc(DB::raw('COUNT(sale_order_item.id)'))
            ->get()
            ->keyBy('product_type');
        // Retrieve sum of total orders
        $sumTotalOrders = SaleOrderSlaReport::where('calculate_day', $date)
            ->groupBy('product_type')
            ->selectRaw('product_type, SUM(total_orders) as total_orders_sum')
            ->when(!$userInfo->is_admin, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->get()
            ->keyBy('product_type');
        $sumTotalOrdersYesterday = SaleOrderSlaReport::where('calculate_day', $yesterday)
            ->groupBy('product_type')
            ->selectRaw('product_type, SUM(total_orders) as total_orders_sum')
            ->when(!$userInfo->is_admin, function ($query) use ($storeIds) {
                return $query->whereIn('store_id', $storeIds);
            })
            ->get()
            ->keyBy('product_type');
        // Merge orders and sum of total orders
        $result = $productTypes->map(function ($productType) use ($orders, $sumTotalOrders, $sumTotalOrdersYesterday) {
            $itemCount = $orders[$productType]->item_count ?? 0;
            $totalOrdersSum = $sumTotalOrders[$productType]->total_orders_sum ?? 0;
            $totalOrdersSumYesterday = $sumTotalOrdersYesterday[$productType]->total_orders_sum ?? 0;
            $averageOrder = ($totalOrdersSum != 0) && ($totalOrdersSumYesterday != 0) ? (($totalOrdersSum - $totalOrdersSumYesterday) / $totalOrdersSumYesterday) * 100 : 0;

            return [
                'product_type' => $productType,
                'item_count' => $itemCount,
                'total_orders_sum' => $totalOrdersSum,
                'total_orders_sum_yesterday' => $totalOrdersSumYesterday,
                'average' => number_format($averageOrder, 2, '.', ','),
            ];
        })->filter(function ($product) {
            return $product['item_count'] != 0 && $product['total_orders_sum'] != 0;
        })->values();

        return $result;
    }

    public function getOrderSla($request)
    {
        setTimezone();
        $userInfo = auth()->user();
        if (empty($request['is_support_login'])) {
            $storeIds = [$userInfo->id];
        } else {
            $storeIds = $request->filled('store_id') ? [$request->store_id] : ($userInfo->store_ids ?? []);
        }
        $end_date = $request->filled('end_date') ? Carbon::parse($request['end_date'])->endOfDay() : Carbon::now()->endOfDay();
        $start_date = $request->filled('start_date') ? Carbon::parse($request['start_date'])->startOfDay() : null;
        $orderType = $request->filled('order_type') ? $request['order_type'] : null;
        $shippingType = $request->filled('shipping_method') ? $request['shipping_method'] : null;
        $businessDays = 0;
        while ($businessDays < 3) {
            $end_date->subDay();
            if ($end_date->isWeekday()) {
                $businessDays++;
            }
        }
        $end_date = $end_date->endOfDay();

        $orders = SaleOrder::select(
            'sale_order.encode_id',
            'sale_order.created_at',
            'sale_order.external_number  as ref_number',
            'sale_order_item.store_id',
            'sale_order.is_xqc',
            'sale_order.id',
            'sale_order.shipping_method',
            'sale_order.is_create_manual',
            'sale_order.is_manual',
            'sale_order.order_status',
            'sale_order.amount_paid',
            'sale_order.shipping_calculate',
            'sale_order.order_date',
            'sale_order.is_test',
            'sale_order.shipment_id',
            'sale_order.is_fba_order',
            'sale_order.order_type',

            DB::raw('GROUP_CONCAT(DISTINCT product_style.type) as product_type'),
            DB::raw('(TIMESTAMPDIFF(DAY, sale_order.created_at + INTERVAL 2 DAY, NOW() + INTERVAL 2 DAY) - TIMESTAMPDIFF(WEEK, sale_order.created_at + INTERVAL 2 DAY, NOW() + INTERVAL 2 DAY) * 2) AS order_age'),
        )
            ->with([
                'address:id,order_id,name'
            ])
            ->with([
                'store:id,name'
            ])
            ->with([
                'shipmentDefault:id,order_id,tracking_number,tracking_status,provider'
            ])
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('product_style', 'product_style.name', '=', 'product.style')
            ->where('sale_order.is_test', false)
            ->whereIn('sale_order.order_status', [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_NEW_ORDER])
            ->where('sale_order.created_at', '<', $end_date)
            ->when(empty($request['store_id']) && !$userInfo->is_admin, function ($query) use ($storeIds) {
                return $query->whereIn('sale_order.store_id', $storeIds);
            })
            ->when(!empty($request['store_id']), function ($query) use ($storeIds) {
                return $query->whereIn('sale_order.store_id', $storeIds);
            })
            ->when(!empty($request['start_date']), function ($query) use ($start_date) {
                return $query->where('sale_order.created_at', '>=', $start_date);
            })
            ->when(!empty($request['shipping_method']), function ($query) use ($shippingType) {
                switch ($shippingType) {
                    case 'standard':
                        $query->where('sale_order.shipping_method', 'standard');
                        break;
                    case 'express':
                        $query->where('sale_order.shipping_method', 'express');
                        break;
                    case 'priority':
                        $query->where('sale_order.shipping_method', 'priority');
                        break;
                    case 'other':
                        $query->whereNotIn('sale_order.shipping_method', ['standard', 'express', 'priority']);
                        break;
                }
            })
            ->when(!empty($request['order_type']), function ($query) use ($orderType) {
                switch ($orderType) {
                    case 'is_xqc':
                        $query->where('sale_order.is_xqc', true);
                        break;
                    case 'is_manual':
                        $query->where('sale_order.is_manual', true);
                        break;
                    case 'is_create_manual':
                        $query->where('sale_order.is_create_manual', true);
                        break;
                    case 'is_fba_order':
                        $query->where('sale_order.is_fba_order', true);
                        break;
                    default:
                        $query->where('sale_order.order_type', $orderType);
                        break;
                }
            });
        if (!empty($request['keyword'])) {
            $keyword = $request['keyword'];
            $queryOrderIds = SaleOrder::where('external_number', $keyword)
                ->orWhere('encode_id', $keyword)
                ->select('id');
            $queryShipments = Shipment::where('tracking_number', $keyword)->select('order_id as id');
            $queryOrderIds->union($queryShipments);
            $queryItems = SaleOrderItem::select('order_id as id');
            if (strlen($keyword) == 4) {
                $queryItems->where('product_style_sku', $keyword);
            } elseif (strlen($keyword) == 6) {
                $queryItems->where('product_style_sku', substr($keyword, 0, 4))
                    ->where('product_color_sku', substr($keyword, 4, 2));
            } elseif (strlen($keyword) == 9) {
                $queryItems->where('product_sku', $keyword);
            } else {
                $queryItems->where('sku', $keyword);
            }

            $queryOrderIds->union($queryItems);
            $orders->joinSub($queryOrderIds, 'order_ids', 'sale_order.id', '=', 'order_ids.id');
        }

        $orders->orderBy('sale_order.created_at', 'DESC')
            ->groupBy('sale_order.id');
        $shippingMethods = ShippingMethod::whereIn('api_shipping_method', $orders->pluck('shipping_method'))
            ->with([
                'shippingCarrierService:id,display_name',
                'shippingCarrier:id,name'
            ])
            ->get();

        if (!empty($request['action']) && $request['action'] == 'export') {
            return $orders->get();  // Use getCollection() to retrieve the modified collection
        }

        $orders = $orders->paginate($request['limit'] ?? $this->limit);

        $orders->getCollection()->each(function ($order) use ($shippingMethods) {
            $order->store_name = $order->store->name;
            // Skip if the shipping method is one of the defined constants
            if (!in_array($order->shipping_method, [SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_STANDARD], true)) {
                // Find the matching shipping method in the shipping methods collection
                $shipping = $shippingMethods->first(function ($method) use ($order) {
                    return $method->api_shipping_method === $order->shipping_method && $method->store_id == $order->store_id;
                });

                // If a valid shipping method is found, update the shipping method details
                if ($shipping && ($shipping->shippingCarrier || $shipping->shippingCarrierService)) {
                    $carrierName = $shipping->shippingCarrier?->name ?? '';
                    $serviceName = $shipping->shippingCarrierService?->display_name ?? '';
                    $order->shipping_method = trim("{$carrierName} {$serviceName}");
                }
            }
        });

        return $orders;
    }

    public function uploadSampleImage($input)
    {
        $order = SaleOrder::with('customerEmail')->where('id', $input['order_id'])->first();

        if (empty(count($order->customerEmail))) {
            return $this->errorResponse('Missing Email', 404);
        }
        try {
            DB::beginTransaction();
            if (!empty($input['image_ids'])) {
                // remove file s3 when delete image
                $images = SaleOrderItemProductImage::where('order_id', $input['order_id'])->whereNotIn('id', $input['image_ids'])->get();

                SaleOrderItemProductImage::where('order_id', $input['order_id'])->whereNotIn('id', $input['image_ids'])->delete();
            } else {
                $images = SaleOrderItemProductImage::where('order_id', $input['order_id'])->get();
                SaleOrderItemProductImage::where('order_id', $input['order_id'])->delete();
            }
            foreach ($images as $image) {
                Storage::disk('s3')->delete($image->image_url);
            }
            $data = [];

            foreach ($input['items'] as $key => $item) {
                $orderItem = SaleOrderItem::with('emailCustomers')->where('id', $item['order_item_id'])->first();
                if (!empty($item['images']) && $input->has("items.{$key}.images")) {
                    $files = $input->file("items.{$key}.images");
                    foreach ($files as $index => $file) {
                        $filename = $orderItem->sku . '-' . rand() . ".{$file->guessExtension()}";
                        $folderPath = SaleOrderItemProductImage::FILE_PATH;
                        Storage::disk('s3')->putFileAs($folderPath, $file, $filename);
                        $filePath = $folderPath . '/' . $filename;

                        $data[] = [
                            'order_id' => $input['order_id'],
                            'order_item_id' => $item['order_item_id'],
                            'image_url' => $filePath,
                            'user_id' => auth()->user()->id,
                        ];
                    }
                }
            }

            if (!empty($data)) {
                SaleOrderItemProductImage::insert($data);
            }
            $emails = $order->customerEmail->pluck('email')->toArray();
            $dataHistory = [
                'order_id' => $input['order_id'],
                'type' => 'update',
                'user_id' => \auth()->user()->id,
                'message' => 'Product photo sent to ' . implode(', ', $emails)
            ];
            SaleOrderHistory::create($dataHistory);
            $param = [
                'order_id' => $input['order_id']
            ];
            DB::commit();
            handleJob(SaleOrderItemProductImage::SEND_MAIL_CUSTOMER, $param);

            return $this->successResponse('Upload product image successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage());
        }
    }

    public function submitEmail($input)
    {
        try {
            DB::beginTransaction();
            $emails = SaleOrderCustomerEmail::where('order_id', $input['order_id'])->selectRaw('group_concat(email) as email')->first();
            SaleOrderCustomerEmail::where('order_id', $input['order_id'])->delete();
            $data = [];
            foreach ($input['emails'] as $email) {
                $data[] = [
                    'order_id' => $input['order_id'],
                    'email' => $email,
                    'created_by' => \auth()->user()->id,
                ];
            }
            SaleOrderCustomerEmail::insert($data);
            $dataHistory = [
                'order_id' => $input['order_id'],
                'type' => 'update',
                'user_id' => \auth()->user()->id,
                'message' => 'Product Photo Recipient was changed from ' . $emails->email . ' to ' . implode(', ', $input['emails'])
            ];
            SaleOrderHistory::create($dataHistory);
            DB::commit();

            return $this->successResponse('Email for the Product Photo Recipient have been update successfully.');
        } catch (Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage());
        }
    }

    public function sendEmailSampleProduct($input)
    {
    }

    public function updateShippingMethodForSeller($request): array
    {
        $saleOrder = SaleOrder::where('encode_id', $request->order_id)->first();
        if (!$saleOrder) {
            return $this->handleFail('Order not found.');
        }
        $saleOrder->is_eps = $request['shipping_method'] == 'express' ? true : false;
        $methodFrom = $saleOrder->shipping_method;
        $methodTo = $request['shipping_method'];
        $tagImport = $saleOrder->tag;
        if ($methodTo == 'express') {
            $tagImport = $this->addTag($saleOrder, Tag::UPGRADE_TO_EXPRESS_SHIPPING, $tagImport);
        }

        if ($methodTo == 'priority') {
            $tagImport = $this->addTag($saleOrder, Tag::UPGRADE_TO_PRIORITY_SHIPPING, $tagImport);
        }
        $saleOrder->shipping_method = $methodTo;
        $saleOrder->tag = $tagImport;

        $user = auth()->user();
        $userName = $user->user_login_id
            ? User::where('id', $user->user_login_id)->value('username')
            : $user->code;

        if ($user->member_id) {
            $member = TeamMember::find($user->member_id);
            $userName = "member $member->name";
        }

        SaleOrderHistory::create([
            'user_id' => null,
            'employee_id' => null,
            'order_id' => $saleOrder->id,
            'type' => SaleOrderHistory::UPDATE_SHIPPING_METHOD_TYPE,
            'message' => "The shipping method was changed from \"$methodFrom\" to \"$methodTo\" via SL by $userName.",
            'created_at' => Carbon::now()->toDateTimeString()
        ]);

        $store = auth()->user();

        if ($store->payment_terms == Store::STORE_PREPAID) {
            $this->chargeUpgradeShippingMethod($saleOrder, $store, $methodFrom, $methodTo);
        }

        $saleOrder->save();

        return $this->handleSuccess('Update shipping method successfully.', $saleOrder);
    }

    public function addTag($saleOrder, $tagName, $tagImport)
    {
        $tag = Tag::where('name', $tagName)->first();

        if ($tag) {
            $currentTags = $saleOrder->tag ? explode(',', $saleOrder->tag) : [];

            if (!in_array($tag->id, $currentTags)) {
                $currentTags[] = $tag->id;
            }

            $tagImport = implode(',', $currentTags);
        }

        return $tagImport;
    }

    public function getHardGoodAndShirt($orderId): array
    {
        return DB::table('sale_order')
            ->selectRaw('product_type.is_hard_goods, sum(sale_order_item.quantity) as quantity')
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->join('product_type', 'product_type.name', '=', 'product_style.type')
            ->where('sale_order.id', $orderId)
            ->groupBy('product_type.is_hard_goods')
            ->pluck('quantity', 'is_hard_goods')
            ->toArray();
    }

    public function getListDtgToDtf($params)
    {
        $limit = $params['limit'] ?? 30;
        $stores = Store::all()->pluck('name', 'id')->toArray();
        $saleOrderValid = SaleOrder::where('order_status', SaleOrder::NEW_ORDER)
            ->whereRaw('sale_order.created_at >= (NOW()-INTERVAL 10 DAY)')
            ->first();
        $query = SaleOrder::select([
            'sale_order.id',
            'sale_order.order_number',
            'sale_order.order_status',
            'sale_order.order_date',
            'sale_order.created_at',
            'sale_order.store_id',
            'sale_order_item_barcode.sku as barcode_sku',
        ])
            ->with([
                'imageItems' => function ($q) {
                    $q->selectRaw('order_id, id, image_url, print_side')
                        ->whereIn('print_side', [0, 1]);
                },
                'items:order_id,sku,product_sku,product_style_sku,product_color_sku,product_size_sku',
            ])
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->leftJoin('sale_order_change_print_methods', 'sale_order_change_print_methods.order_id', '=', 'sale_order.id')
            ->whereExists(function ($query) {
                $query->select('product_print_area.id')
                    ->from('product_print_area')
                    ->whereRaw('product_print_area.product_style_id = product_style.id')
                    ->whereNotNull('product_print_area.name')
                    ->whereRaw("
                                product_print_area.name = (
                                    CASE
                                        WHEN sale_order_item_image.print_side = 0 THEN 'DTF F'
                                        WHEN sale_order_item_image.print_side = 1 THEN 'DTF B'
                                        ELSE NULL
                                    END
                                )
                            ");
            })
            ->where('sale_order.order_status', SaleOrder::NEW_ORDER)
            ->whereIn('sale_order.warehouse_id', [
                Warehouse::WAREHOUSE_SANJOSE_ID,
                Warehouse::WAREHOUSE_TEXAS_ID,
                Warehouse::WAREHOUSE_VIRGINIA_ID,
            ])
            ->where('sale_order.is_xqc', '<>', true)
            ->where('sale_order.order_quantity', 1)
            ->where('sale_order.id', '>=', $saleOrderValid->id ?? 1)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereNull('sale_order_item_image.custom_platen')
            ->where('sale_order_item_image.thumb_750', 1)
            ->whereNull('sale_order_change_print_methods.id')
            ->whereIn('sale_order_item.print_sides', ['F', 'B'])
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);

        if (!empty($params['warehouse'])) {
            $query->where('sale_order.warehouse_id', $params['warehouse']);
        }

        if (!empty($params['store_id'])) {
            $query->where('sale_order.store_id', $params['store_id']);
        }

        if (!empty($params['style'])) {
            $query->where('product.style', $params['style']);
        }

        if (!empty($params['color'])) {
            $query->where('product.color', $params['color']);
        }

        if (!empty($params['size'])) {
            $query->where('product.size', $params['size']);
        }

        $data = $query->limit($limit)->get();

        foreach ($data as &$item) {
            $side = $item->imageItems[0]->print_side;
            $image_name = "{$item->barcode_sku}-$side.png";
            $thumb = env('AWS_S3_URL', '') . '/thumb/250/' . $item->order_date . '/' . $image_name;
            $item->thumb = $thumb;
            $item->store_name = $stores[$item->store_id] ?? null;
        }

        return $data;
    }

    public function convertDtgToDtf($data)
    {
        $printSideMapping = [
            0 => 12,
            1 => 13,
        ];

        foreach ($data as &$item) {
            if (empty($item['convert'])) {
                $item['status'] = true;

                continue;
            }

            $item['status'] = false;
            DB::beginTransaction();

            try {
                $barcode = SaleOrderItemBarcode::where('order_id', $item['id'])
                    ->where('is_deleted', 0)
                    ->where('barcode_printed_id', 0)
                    ->whereIn('print_method', [PrintMethod::DTG, PrintMethod::NECK])
                    ->lockForUpdate()
                    ->first();

                $images = SaleOrderItemImage::where('order_id', $item['id'])
                    ->whereIn('print_side', [0, 1])
                    ->whereNull('custom_platen')
                    ->lockForUpdate()
                    ->get();

                if (!empty($barcode) && !empty($images)) {
                    if ($barcode->print_method == PrintMethod::DTG) {
                        DB::table('sale_order_item_barcode')
                            ->where('id', $barcode->id)
                            ->update(['print_method' => PrintMethod::DTF]);
                    }

                    foreach ($images as $imageItem) {
                        DB::table('sale_order_item_image')
                            ->where('id', $imageItem->id)
                            ->update([
                                'print_side' => $printSideMapping[$imageItem->print_side],
                                'color_new' => null,
                                'thumb_750' => 0,
                                'upload_s3_status' => 0,
                                'thumb_250' => 0,
                                'skip_retry' => 0,
                                'retry_download_manual_count' => 0,
                                'retry_detect_color_count' => 0,
                                'last_retry_detect_color' => null,
                                'retry_count' => 0
                            ]);
                    }

                    $tags = $barcode->saleOrder->tag ? explode(',', $barcode->saleOrder->tag) : [];
                    $tags[] = Tag::TAG_DTG_DTF_ID;
                    DB::table('sale_order')->where('id', $barcode->order_id)
                        ->update(['tag' => implode(',', array_unique($tags))]);

                    $item['status'] = true;
                }
                DB::commit();
            } catch (Exception $exception) {
                Log::error('SaleOrderRepository.convertDtgToDtf', [$exception]);
                DB::rollBack();
            }
        }

        foreach ($data as $result) {
            if ($result['status']) {
                SaleOrderChangePrintMethod::updateOrCreate(
                    [
                        'order_id' => $result['id'],
                        'converted' => !empty($result['convert']) ? 'DTG-DTF' : 'DTG-DTG'
                    ],
                    ['order_id' => $result['id']],
                );
            }

            if (!empty($result['convert']) && $result['status']) {
                SaleOrderHistory::create([
                    'user_id' => auth()->user()->id ?? null,
                    'employee_id' => null,
                    'order_id' => $result['id'],
                    'type' => SaleOrderHistory::DTG_TO_DTF,
                    'message' => 'Order switched from DTG to DTF',
                    'created_at' => now(),
                ]);
            }
        }

        return $data;
    }

    public function getDataItemPrintMethodByDate($date)
    {
        $orders = DB::table(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_barcode', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product_print_side', 'product_print_side.code', '=', 'sale_order_item_image.print_side')
            ->join('product_print_area', 'product_print_area.name', '=', 'product_print_side.name')
            ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->whereColumn('product_style.id', 'product_print_area.product_style_id')
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->whereIn('sale_order.order_status', [
                SaleOrder::STATUS_NEW_ORDER,
                SaleOrder::STATUS_SHIPPED,
                SaleOrder::STATUS_IN_PRODUCTION,
                SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
            ])
            ->where('sale_order.created_at', '>=', Carbon::parse($date)->startOfDay()->format('Y-m-d H:i:s'))
            ->where('sale_order.created_at', '<=', Carbon::parse($date)->endOfDay()->format('Y-m-d H:i:s'))
            ->select(
                'product_print_area.print_method',
                'sale_order.store_id',
                'sale_order.warehouse_id',
                DB::raw('COUNT(*) AS total'),
                DB::raw('DATE(sale_order.created_at) AS order_date'),
            )
            ->groupBy([
                DB::raw('DATE(sale_order.created_at)'),
                'product_print_area.print_method',
                'sale_order.store_id',
                'sale_order.warehouse_id'
            ]);
        $sleeveOrders = DB::table(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_barcode', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product_print_side', 'product_print_side.code', '=', 'sale_order_item_image.print_side')
            ->join('product_print_area', 'product_print_area.name', '=', 'product_print_side.name')
            ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku')
            ->whereColumn('product_style.id', 'product_print_area.product_style_id')
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->whereIn('sale_order.order_status', [
                SaleOrder::STATUS_NEW_ORDER,
                SaleOrder::STATUS_SHIPPED,
                SaleOrder::STATUS_IN_PRODUCTION,
                SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
            ])
            ->where('sale_order.created_at', '>=', Carbon::parse($date)->startOfDay()->format('Y-m-d H:i:s'))
            ->where('sale_order.created_at', '<=', Carbon::parse($date)->endOfDay()->format('Y-m-d H:i:s'))
            ->whereIn('product_print_side.name', ProductPrintSide::sleevePrintSideName()) //get all print side with name contains sleeve
            ->select(
                DB::raw("'SLEEVE' AS print_method"),
                'sale_order.store_id',
                'sale_order.warehouse_id',
                DB::raw('COUNT(*) AS total'),
                DB::raw('DATE(sale_order.created_at) AS order_date'),
            )
            ->groupBy([
                DB::raw('DATE(sale_order.created_at)'),
                'sale_order.store_id',
                'sale_order.warehouse_id'
            ]);

        return $orders->union($sleeveOrders)->get();
    }

    public function coverDataItemPrintMethodByDate($date)
    {
        $data = $this->getDataItemPrintMethodByDate($date);
        SaleOrderPrintMethodReport::where('order_date', Carbon::parse($date)->format('Y-m-d'))->delete();
        if ($data->isNotEmpty()) {
            $data->chunk(100)
                ->each(function ($chunk) {
                    $dataInsert = $chunk->map(function ($item) {
                        return [
                            'print_method' => $item->print_method,
                            'store_id' => $item->store_id,
                            'warehouse_id' => $item->warehouse_id,
                            'total' => $item->total,
                            'order_date' => $item->order_date,
                        ];
                    });

                    SaleOrderPrintMethodReport::insert($dataInsert->toArray());
                });
        }

        return true;
    }

    public function printTypeStatistic($params)
    {
        $query = SaleOrderPrintMethodReport::select([
            'print_method',
            DB::raw('SUM(total) as total_order_item')
        ]);

        if (isset($params['store_id']) && !empty($params['store_id'])) {
            Log::debug('store_id', [$params['store_id']]);
            $query->whereIn('store_id', $params['store_id']);
        }

        if (empty($params['warehouse'])) {
            $query->where('warehouse_id', config('jwt.warehouse_id'));
        } elseif ($params['warehouse'] != 'all') {
            $query->where('warehouse_id', $params['warehouse']);
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $warehouseIds = $user->warehouses->pluck('id')->toArray();
                $query->whereIn('warehouse_id', $warehouseIds);
            }
        }
        if (isset($params['start_date'])) {
            $query->where('order_date', '>=', Carbon::parse($params['start_date'])->format('Y-m-d'));
        }
        if (isset($params['end_date'])) {
            $query->where('order_date', '<=', Carbon::parse($params['end_date'])->format('Y-m-d'));
        }

        $data = $query->groupBy('print_method')->get();

        $startDate = Carbon::parse($params['start_date'])->setTimezone('America/Los_Angeles');
        $endDate = Carbon::parse($params['end_date'])->setTimezone('America/Los_Angeles');
        $dayNow = Carbon::now('America/Los_Angeles')->format('Y-m-d');
        if (Carbon::parse($dayNow)->between($startDate, $endDate)) {
            setTimezone();
            $dataDayNow = $this->getDataItemPrintMethodByDate($dayNow);

            if (isset($params['store_id']) && !empty($params['store_id'])) {
                $dataDayNow = $dataDayNow->whereIn('store_id', $params['store_id']);
            }

            if (empty($params['warehouse'])) {
                $dataDayNow = $dataDayNow->where('warehouse_id', config('jwt.warehouse_id'));
            } elseif ($params['warehouse'] != 'all') {
                $dataDayNow = $dataDayNow->where('warehouse_id', $params['warehouse']);
            } else {
                $user = auth()->user();
                if (!$user->is_admin) {
                    $warehouseIds = $user->warehouses->pluck('id')->toArray();
                    $dataDayNow = $dataDayNow->whereIn('warehouse_id', $warehouseIds);
                }
            }

            if ($dataDayNow->isNotEmpty()) {
                $dataDateNowConvert = $dataDayNow->map(function ($item) {
                    return [
                        'print_method' => $item->print_method,
                        'total_order_item' => $item->total,
                    ];
                });

                $data = $data->isNotEmpty() ? $data : collect([]);
                $data = $data->map(function ($item) {
                    return [
                        'print_method' => $item->print_method,
                        'total_order_item' => $item->total_order_item,
                    ];
                })
                    ->merge($dataDateNowConvert)
                    ->groupBy('print_method')
                    ->map(function ($items, $print_method) {
                        return [
                            'print_method' => $print_method,
                            'total_order_item' => $items->sum('total_order_item'),
                        ];
                    })
                    ->values();
            }
        }

        return $data;
    }

    private function updateSaleOrderRbt($input)
    {
        $rbtOrders = SaleOrder::whereIn('id', $input['order_ids'])->get();
        if ($rbtOrders->isEmpty()) {
            return;
        }
        foreach ($rbtOrders as $order) {
            $tags = array_filter(
                array_map('trim', explode(',', $order->tag)), // Trim each tag
                function ($tag) {
                    return $tag != Tag::TAG_RBT_ID;
                },
            );

            $order->tag = implode(',', $tags);
            $order->is_rbt = SaleOrderItem::IS_NOT_RBT;
            $order->save();
            SaleOrderItem::query()
                ->where('order_id', $order->id)
                ->update(['is_rbt' => SaleOrderItem::IS_NOT_RBT]);
        }
    }

    public function getListOrderStorePrepaid($params)
    {
        setTimezone();
        $storeIds = Store::where('payment_terms', Store::STORE_PREPAID)->pluck('id')->toArray();

        $query = SaleOrder::with('refunds.user')
            ->withSum('saleOrderInsertCalculatePrice as total_insert_price', 'amount_paid')
            ->withSum('surchargeFees as total_order_surcharge_fee', 'value')
            ->withSum('itemSurchargeFees as total_item_surcharge_fee', 'value')
            ->withSum('refunds as refund_amount', 'amount')
            ->whereIn('store_id', $storeIds)
            ->where('order_status', '<>', SaleOrder::DRAFT)
            ->where('is_test', SaleOrder::NOT_TEST)
            ->search($params);

        return $query->orderBy('id', 'desc')->paginate($this->limit);
    }

    public function getPrePaidOrderDetail($id)
    {
        setTimezone();
        $order = SaleOrder::with('refunds.user')
            ->withSum('saleOrderInsertCalculatePrice as total_insert_price', 'amount_paid')
            ->withSum('surchargeFees as total_order_surcharge_fee', 'value')
            ->withSum('itemSurchargeFees as total_item_surcharge_fee', 'value')
            ->withSum('refunds as refund_amount', 'amount')
            ->findOrFail($id);

        return $order;
    }

    public function refundPrePaidOrder($id, $params)
    {
        $order = SaleOrder::where('id', $id)->lockForUpdate()->first();
        $this->makeRefund($order, $params['amount'], $params);
        $order->payment_status = $params['partial_refund'] ? SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED : SaleOrder::PAYMENT_STATUS_REFUNDED;
        $order->save();

        return [
            'success' => true,
            'message' => 'Refund successfully.',
        ];
    }

    public function makeRefund(SaleOrder $order, $totalAmount, $params = [])
    {
        $walletRepository = new \App\Repositories\WalletRepository();
        $orderItems = [];
        $blankPrice = 0;
        $printPrice = 0;
        $totalOrder = $order->order_total;
        $newBalance = $walletRepository->update(WalletTransaction::TRANSACTION_TYPE_REFUND, $totalAmount, $order->store_id);
        $order->load(['items', 'items.product.brand']);

        foreach ($order->items as $item) {
            $printSidesCode = str_split($item->print_sides);
            $printSides = ProductPrintSide::whereIn('code_wip', $printSidesCode)->get()->pluck('name')->toArray();

            $orderItems[] = [
                'name' => $item->product && $item->product->brand ? "{$item->product->brand->name} / {$item->product->name}" : $item->product->name,
                'sku' => $item->product_sku,
                'quantity' => $item->quantity,
                'print_areas' => implode(', ', $printSides),
                'item_price' => number_format(($item->unit_price - $item->handling_fee) * $item->quantity, 2),
            ];
            $blankPrice += $item->blank_price * $item->quantity;
            $printPrice += ($item->unit_price - $item->blank_price - $item->handling_fee) * $item->quantity;
        }

        $surchargeResult = $this->fetchOrderSurcharge($order, $order->id);
        $surcharge = [];

        foreach ($surchargeResult as $result) {
            $surcharge[$result['name']] = $result['total'];

            if ($result['name'] !== 'Handling') {
                $totalOrder += $result['total'];
            }
        }

        $receiptData = [
            'items' => $orderItems,
            'blank_price' => number_format($blankPrice, 2),
            'print_price' => number_format($printPrice, 2),
            'surcharge' => $surcharge,
            'shipping_method' => $order->shipping_method,
            'shipping' => $order->shipping_calculate,
            'refund' => $totalAmount,
            'total' => number_format($totalOrder, 2),
        ];

        $orderRefund = SaleOrderRefund::create([
            'order_id' => $order->id,
            'reason' => $params['reason'] ?? null,
            'amount' => $params['amount'] ?? $totalAmount,
            'total_amount' => $params['total_refund'] ?? $totalAmount,
            'refund_by' => auth()->user()?->id,
            'type' => auth()->user() ? SaleOrderRefund::MANUAL_REFUND : SaleOrderRefund::AUTO_REFUND,
        ]);
        $transaction = WalletTransaction::create([
            'store_id' => $order->store_id,
            'type' => WalletTransaction::TRANSACTION_TYPE_REFUND,
            'amount' => $totalAmount,
            'new_balance' => $newBalance,
            'direction' => WalletTransaction::DIRECTION_IN,
            'object_id' => $orderRefund->id,
            'object_type' => WalletTransaction::TRANSACTION_TYPE_REFUND,
            'object_number' => $order->encode_id,
            'description' => 'Order #' . $order->encode_id,
        ]);
        WalletReceipt::create([
            'store_id' => $order->store_id,
            'order_id' => $order->id,
            'transaction_id' => $transaction->id,
            'amount' => $totalAmount,
            'billing_address_id' => $order->store->billing_address_id,
            'receipt_number' => getSequenceNumber('receipt', $order->store_id),
            'data' => json_encode($receiptData),
        ]);
    }

    public function chargeUpgradeShippingMethod(SaleOrder $saleOrder, $store, $from, $to)
    {
        $oldShippingPrice = $saleOrder->shipping_calculate;
        $newShippingPrice = 0;
        $shipPriceMax = ['price' => 0, 'addition_price' => 0];
        $newPeakShippingFeeValue = 0;
        $oldPeakShippingFee = app(SurchargeFeeRepository::class)->fetchPeakSurChargeByOrder($saleOrder->id);
        $oldPeakShippingFeeValue = $oldPeakShippingFee->value ?? 0;
        $totalInsertPrice = SaleOrderInsertCalculatePrice::where('order_id', $saleOrder->id)->sum('amount_paid');
        $totalOrderSurchageFee = SaleOrderSurchargeFee::where('order_id', $saleOrder->id)->sum('value');
        $totalItemSurchageFee = SaleOrderItemSurchargeFee::where('order_id', $saleOrder->id)->sum('value');
        $oldAmount = $totalInsertPrice + $totalOrderSurchageFee + $totalItemSurchageFee + $saleOrder->order_total;

        if ($oldPeakShippingFee) {
            $peakShippingFee = PeakShippingFee::first();
            $newPeakShippingFee = $this->updateOrderSurchargeFee($saleOrder, $peakShippingFee, PeakShippingFee::PEAK_SHIPPING_FEE);
            $newPeakShippingFeeValue = $newPeakShippingFee->value ?? 0;
        }

        $saleOrder->load([
            'items',
            'addressSaleOrder' => function ($queryAddress) {
                $queryAddress->where('type_address', SaleOrderAddress::TO_ADDRESS);
            },
        ]);
        $address = $saleOrder->addressSaleOrder()->first();
        $destination = in_array(strtoupper($address->country), StoreShipment::DOMESTIC_SHIPPING) ? StoreShipment::DOMESTIC : StoreShipment::INTERNATIONAL;
        $shippingPrices = StoreShipment::where('store_id', $saleOrder->store_id)
            ->where('status', StoreShipment::STATUS_ACTIVE)
            ->where('service_type', $saleOrder->shipping_method)
            ->where('destination', $destination)
            ->get();

        foreach ($saleOrder->items as $saleOrderItem) {
            $storeShipping = $shippingPrices
                ->sortByDesc(function ($item) use ($saleOrderItem) {
                    return strtolower($item->size) == strtolower($saleOrderItem->product_size_sku) ? 1 : 0;
                })
                ->first(function ($item) use ($saleOrderItem) {
                    $check = strtolower($item->product_type) === strtolower($saleOrderItem->getTypeProduct->type);
                    if ($item->size) {
                        $check = $check && strtolower($item->size) === strtolower($saleOrderItem->product_size_sku);
                    }

                    return $check;
                });

            if (!$storeShipping) {
                throw new Exception('Shipping price has not been set yet.');
            }

            if ($shipPriceMax['price'] < $storeShipping->price) {
                $shipPriceMax['price'] = $storeShipping->price;
                $shipPriceMax['addition_price'] = $storeShipping->addition_price;
            }
            $newShippingPrice += $saleOrderItem->quantity * $storeShipping->addition_price;
        }

        $newShippingPrice = $newShippingPrice > 0 ? $newShippingPrice - $shipPriceMax['addition_price'] + $shipPriceMax['price'] : 0;
        $totalAmount = ($newShippingPrice + $newPeakShippingFeeValue) - ($oldShippingPrice + $oldPeakShippingFeeValue);
        if ($totalAmount < 0) {
            throw new Exception('The shipping price cannot be lower than the original shipping price.');
        }

        $saleOrder->shipping_calculate = $newShippingPrice;
        $saleOrder->order_total = $saleOrder->order_total + ($newShippingPrice - $oldShippingPrice);
        $saleOrder->calculated_at = now();

        if ($saleOrder->payment_status === SaleOrder::PAYMENT_STATUS_PAID && $totalAmount > 0) {
            $receiptData = [
                'old_amount' => $oldAmount,
                'new_amount' => $oldAmount + $totalAmount,
            ];
            $walletRepository = resolve(WalletRepository::class);
            $newBalance = $walletRepository->update(WalletTransaction::TRANSACTION_TYPE_PAYMENT, $totalAmount, $saleOrder->store_id);
            $transaction = WalletTransaction::create([
                'store_id' => $saleOrder->store_id,
                'type' => WalletTransaction::TRANSACTION_TYPE_ADJUSTMENT,
                'object_id' => $saleOrder->id,
                'object_type' => WalletTransaction::TRANSACTION_TYPE_ADJUSTMENT,
                'object_number' => $saleOrder->encode_id,
                'amount' => $totalAmount,
                'new_balance' => $newBalance,
                'direction' => WalletTransaction::DIRECTION_OUT,
                'description' => "Order #$saleOrder->encode_id",
                'note' => "Upgrade shipping method from $from to $to",
            ]);
            WalletReceipt::create([
                'store_id' => $saleOrder->store_id,
                'transaction_id' => $transaction->id,
                'order_id' => $saleOrder->id,
                'billing_address_id' => $store->billing_address_id,
                'receipt_number' => getSequenceNumber('receipt', $saleOrder->store_id),
                'amount' => $totalAmount,
                'data' => json_encode($receiptData),
            ]);
        }
    }

    public function fetchOrderSurcharge($order, $id)
    {
        $surchargeResults = [];
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $totalHandlingFee = $order->items->sum(function ($item) {
            return $item->handling_fee * $item->quantity;
        });
        if ($totalHandlingFee) {
            $surchargeResults[] = ['name' => SurchargeService::TYPE_HANDLING, 'total' => round($totalHandlingFee, 2)];
        }
        $saleOrdersSurcharge = $surchargeFeeRepository->fetchSaleOrdersSurcharge($id);
        foreach ($saleOrdersSurcharge as $surcharge) {
            $surchargeResults[] = ['name' => $surcharge->name, 'total' => $surcharge->total];
        }
        $saleOrderItemsSurcharge = $surchargeFeeRepository->fetchSaleOrdersItemsSurcharge($id, false);
        foreach ($saleOrderItemsSurcharge as $surcharge) {
            $surchargeResults[] = ['name' => $surcharge->name, 'total' => $surcharge->total];
        }
        $insertValue = SaleOrderInsertCalculatePrice::where('order_id', $id)
            ->whereNotNull('calculated_at')
            ->sum('amount_paid');
        if ($insertValue) {
            $surchargeResults[] = ['name' => 'Insert', 'total' => $insertValue];
        }
        $peakSurcharge = $surchargeFeeRepository->fetchPeakSurChargeByOrder($id);
        if ($peakSurcharge) {
            $surchargeResults[] = ['name' => 'Peak Shipping', 'total' => $peakSurcharge->value];
        }

        return $surchargeResults;
    }

    public function sellerSubmitOrder($encodeId)
    {
        try {
            $saleOrder = SaleOrder::with('store')->where('encode_id', $encodeId)->firstOrFail();
            $url = env('SALE_ORDER_API_URL') . 'orders/status/' . $encodeId;
            $data = [
                'order_status' => SaleOrder::NEW_ORDER,
                'source' => 'Seller dashboard',
                'is_login_support' => false,
            ];

            $this->getUpdatedBy($data);

            $result = $this->sendRequestSaleOrderAPI($url, $saleOrder->store->token, $data, 'PUT');
            $response = json_decode(json_encode($result['response']), true);

            if (!empty($response['status'])) {
                return [
                    'message' => $response['message'] ?? 'Update order successfully',
                    'code' => Response::HTTP_OK,
                ];
            }

            return [
                'message' => $response['message'] ?? 'Update order failed',
                'code' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];
        } catch (Throwable $th) {
            Log::error('SaleOrderRepository sellerSubmitOrder: ' . $th->getMessage());

            return [
                'message' => 'Update order failed',
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
            ];
        }
    }

    private function getUpdatedBy(&$data, $key = 'updated_by')
    {
        if (empty(auth()->user()->user_login_id) && !empty(auth()->user()->client_id)) {
            if (auth()->user()->member_id) {
                $member = TeamMember::find(auth()->user()->member_id);
                $data[$key] = "member $member->name";
            } else {
                $client = Client::find(auth()->user()->client_id);
                $data[$key] = $client->name;
            }
        } else {
            $user = User::find(auth()->user()->user_login_id);
            $data[$key] = $user?->username;
            $data['is_login_support'] = true;
        }
    }

    public function getSlaReport(array $input)
    {
        $orderTypeMapping = [
            SaleOrder::ORDER_TYPE_NORMAL => 'Standard',
            SaleOrder::ORDER_TYPE_TIKTOK_ORDER => 'Tiktok',
            SaleOrder::ORDER_TYPE_LABEL_ORDER => 'Label',
        ];
        // Base Query
        $data = DB::table('sale_order_sla')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_sla.order_id')
            ->select(
                'sale_order.order_type',
                DB::raw("COUNT(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) BETWEEN -1440 AND 0
                        THEN 1 END) AS on_1_day"),
                DB::raw("COUNT(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) BETWEEN -4320 AND -1440
                        THEN 1 END) AS on_1_3_days"),
                DB::raw("COUNT(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) < -4320
                        THEN 1 END) AS on_3_days"),
                DB::raw("SUM(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) BETWEEN -1440 AND 0
                        THEN sale_order.order_quantity ELSE 0 END) AS quantity_1_day"),
                DB::raw("SUM(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) BETWEEN -4320 AND -1440
                        THEN sale_order.order_quantity ELSE 0 END) AS quantity_1_3_days"),
                DB::raw("SUM(CASE
                        WHEN TIMESTAMPDIFF(MINUTE, CONVERT_TZ(NOW(), 'UTC', 'America/Los_Angeles'), CONVERT_TZ(expired_at, 'UTC', 'America/Los_Angeles')) < -4320
                        THEN sale_order.order_quantity ELSE 0 END) AS quantity_3_days"),
            )
            ->whereNull('sale_order_sla.completed_at')
            ->where('sale_order.is_test', 0)
            ->whereIn('sale_order.order_status', ['new_order', 'in_production']);

        // Warehouse Filtering
        if (!empty($input['warehouse']) && $input['warehouse'] !== 'all') {
            $data->where('sale_order.warehouse_id', $input['warehouse']);
        } elseif (!isset($input['warehouse']) || empty($input['warehouse'])) {
            $data->where('sale_order.warehouse_id', config('jwt.warehouse_id'));
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $data->whereIn('sale_order.warehouse_id', $user->warehouses->pluck('id')->toArray());
            }
        }

        // Store ID Filtering
        if (isset($input['store_id'])) {
            $data->whereIn('sale_order.store_id', (array) $input['store_id']);
        }

        // Date Filtering
        if (!empty($input['start_date'])) {
            $data->where('sale_order.created_at', '>=', Carbon::parse($input['start_date'])->startOfDay());
        }
        if (!empty($input['end_date'])) {
            $data->where('sale_order.created_at', '<=', Carbon::parse($input['end_date'])->endOfDay());
        }

        // Fetch Data
        $data = $data->groupBy('sale_order.order_type')->get();
        // Process & Merge Order Types
        $groupedOrders = $data->map(function ($item) use ($orderTypeMapping, $input) {
            return [
                'order_type' => $orderTypeMapping[$item->order_type] ?? 'Standard',
                'total' => (!empty($input['mode']) && $input['mode'] === 'item')
                    ? $item->quantity_1_day + $item->quantity_1_3_days + $item->quantity_3_days
                    : ($item->on_1_day + $item->on_1_3_days + $item->on_3_days),
            ];
        })
            ->groupBy('order_type')
            ->map(function ($items) {
                return [
                    'order_type' => $items->first()['order_type'],
                    'total' => $items->sum('total'),
                ];
            })
            ->values();
        // Summary Calculation
        $fields = (!empty($input['mode']) && $input['mode'] === 'item')
            ? ['quantity_1_day', 'quantity_1_3_days', 'quantity_3_days']
            : ['on_1_day', 'on_1_3_days', 'on_3_days'];

        $summary = [
            'on_1_day' => $data->sum($fields[0]),
            'on_1_3_days' => $data->sum($fields[1]),
            'on_3_days' => $data->sum($fields[2]),
            'total' => $groupedOrders->sum('total'),
        ];

        return [
            'order_type' => $groupedOrders,
            'summary' => $summary
        ];
    }

    public function getSkuReport($input)
    {
        $data = SaleOrder::query();
        $input['mode'] = 'item';
        if (!empty($input['mode']) && $input['mode'] === 'item') {
            $data->selectRaw('
                sale_order_item.product_id,
                product.sku,
                product.name,
                SUM(sale_order_item.quantity) as total
        ');
        } else {
            $data->selectRaw('
                sale_order_item.product_id,
                product.sku,
                product.name,
                COUNT(sale_order.id) as total
            ');
        }
        $data->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->whereNotIn('sale_order.order_status', [
                SaleOrder::ON_HOLD,
                SaleOrder::CANCELLED,
                SaleOrder::REJECTED,
                SaleOrder::DRAFT,
                SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
            ])
            ->where('sale_order.is_test', 0)
            ->whereNotNull('sale_order_item.product_id');

        // Warehouse Filtering
        if ($input['warehouse'] !== 'all') {
            $data->where('sale_order.warehouse_id', $input['warehouse']);
        } elseif (empty($input['warehouse'])) {
            $data->where('sale_order.warehouse_id', config('jwt.warehouse_id'));
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $data->whereIn('sale_order.warehouse_id', $user->warehouses->pluck('id')->toArray());
            }
        }

        // Store ID Filtering
        if (!empty($input['store_id'])) {
            $data->whereIn('sale_order.store_id', (array) $input['store_id']);
        }

        // Date Filtering
        if (!empty($input['start_date'])) {
            $data->where('sale_order.created_at', '>=', Carbon::parse($input['start_date'])->startOfDay());
        }
        if (!empty($input['end_date'])) {
            $data->where('sale_order.created_at', '<=', Carbon::parse($input['end_date'])->endOfDay());
        }

        $data = $data
            ->groupBy('sale_order_item.product_id')
            ->orderByDesc('total')
            ->limit(10)
            ->get();

        return $data;
    }

    public function getClientReport($input)
    {
        $data = SaleOrder::query()
            ->selectRaw('clients.name, SUM(sale_order.order_quantity) as total_quantity, COUNT(sale_order.id) as total_orders')
            ->from(DB::raw('sale_order STRAIGHT_JOIN store ON store.id = sale_order.store_id'))
            ->join(DB::raw('clients'), 'clients.id', '=', 'store.client_id')
            ->whereNotIn('sale_order.order_status', [
                SaleOrder::ON_HOLD,
                SaleOrder::CANCELLED,
                SaleOrder::REJECTED,
                SaleOrder::DRAFT,
                SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
            ])
            ->where('sale_order.is_test', 0);

        // Warehouse Filtering
        if (!empty($input['warehouse']) && $input['warehouse'] !== 'all') {
            $data->where('sale_order.warehouse_id', $input['warehouse']);
        } elseif (empty($input['warehouse'])) {
            $data->where('sale_order.warehouse_id', config('jwt.warehouse_id'));
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $data->whereIn('sale_order.warehouse_id', $user->warehouses->pluck('id')->toArray());
            }
        }

        // Store ID Filtering
        if (!empty($input['store_id'])) {
            $data->whereIn('sale_order.store_id', (array) $input['store_id']);
        }

        // Date Filtering
        if (!empty($input['start_date'])) {
            $data->where('sale_order.created_at', '>=', Carbon::parse($input['start_date'])->startOfDay());
        }
        if (!empty($input['end_date'])) {
            $data->where('sale_order.created_at', '<=', Carbon::parse($input['end_date'])->endOfDay());
        }

        $data = $data
            ->groupBy('clients.name')
            ->orderByDesc('total_orders')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get();

        return $data;
    }

    public function safeSubMonthFormat(Carbon $date): ?string
    {
        $subMonth = $date->copy()->subMonth();
        $subMonthNoOverflow = $date->copy()->subMonthNoOverflow();

        if ($subMonth->format('m-d') !== $subMonthNoOverflow->format('m-d')) {
            // Nếu overflow xảy ra → không dùng (trả về null)
            return null;
        }

        return $subMonthNoOverflow->format('m-d');
    }

    public function duplicateOrder($params)
    {
        $data = $params['data'] ?? [];
        $order = SaleOrder::with('store')->where('encode_id', $params['key'])->first();

        if (empty($order)) {
            throw new Exception('Order not found.');
        }

        $saleOrdersSurcharge = app(SurchargeFeeRepository::class)->fetchSaleOrdersSurcharge([$order->id]);
        $additional_services = [];

        foreach ($saleOrdersSurcharge as $surcharge) {
            if (in_array($surcharge->api_value, [SurchargeService::API_VALUE_HOLOGRAM_STICKER, SurchargeService::API_VALUE_MUG_PACKAGING, SurchargeService::API_VALUE_HOLOGRAM_STICKER, SurchargeService::API_VALUE_PLASTIC_BAG])) {
                $additional_services[] = $surcharge->api_value;
            }
        }

        $saleOrderItemsSurcharge = app(SurchargeFeeRepository::class)->fetchSaleOrdersItemsSurcharge([$order->id], false);

        foreach ($saleOrderItemsSurcharge as $surcharge) {
            if (in_array($surcharge->api_value, [SurchargeService::API_VALUE_HOLOGRAM_STICKER, SurchargeService::API_VALUE_MUG_PACKAGING, SurchargeService::API_VALUE_HOLOGRAM_STICKER, SurchargeService::API_VALUE_PLASTIC_BAG])) {
                $additional_services[] = $surcharge->api_value;
            }
        }

        if (!empty($additional_services)) {
            $data['additional_services'] = array_unique($additional_services);
        }

        $token = $order->store->token ?? null;

        if (empty($token)) {
            throw new Exception('Store Api key not found.');
        }

        $result = $this->sendRequestSaleOrderAPI(env('SALE_ORDER_API_URL') . 'orders', $token, $data);
        $response = json_decode(json_encode($result['response']), true);

        if ($result['code'] === Response::HTTP_CREATED && !empty($response['status']) && isset($response['data']['id'])) {
            $id = $response['data']['id'];
            $newOrder = SaleOrder::where('encode_id', $id)->first();
            SaleOrderDuplicate::create([
                'parent_id' => $order->id,
                'order_id' => $newOrder->id
            ]);

            return $response;
        } else {
            return [
                'success' => false,
                'message' => $response['message'] ?? 'Network error',
                'errors' => $response['errors'] ?? null,
            ];
        }
    }

    public function getOrderQuantityReport($input)
    {
        $mode = $input['mode'] ?? 'order';
        $nowPST = Carbon::now(getTimezone());
        $thirtyDaysAgoPST = Carbon::now(getTimezone())->subDays(30)->startOfDay()->toDateTimeString();
        $days = [
            'lt1d' => ['from' => $nowPST->copy()->subDay(), 'to' => null],
            'd1' => ['from' => $nowPST->copy()->subDays(2), 'to' => $nowPST->copy()->subDay()],
            'd2' => ['from' => $nowPST->copy()->subDays(3), 'to' => $nowPST->copy()->subDays(2)],
            'd3' => ['from' => $nowPST->copy()->subDays(4), 'to' => $nowPST->copy()->subDays(3)],
            'd4' => ['from' => $nowPST->copy()->subDays(5), 'to' => $nowPST->copy()->subDays(4)],
            'd5plus' => ['from' => null, 'to' => $nowPST->copy()->subDays(5)],
        ];
        $quantities = [
            '1' => 'order_quantity = 1',
            '2' => 'order_quantity = 2',
            '3' => 'order_quantity = 3',
            '4' => 'order_quantity = 4',
            '5' => 'order_quantity = 5',
            '6_19' => 'order_quantity > 5 AND order_quantity < 20',
            '20+' => 'order_quantity > 19',
        ];
        $selects = [];
        $bindings = [];
        $storeIds = is_array($input['store_id']) ? $input['store_id'] : [];
        $warehouseIds = is_array($input['warehouse']) ? $input['warehouse'] : [];
        foreach ($days as $label => $range) {
            foreach ($quantities as $key => $condition) {
                if ($mode == 'item') {
                    if ($label === 'lt1d') {
                        $selects[] = "SUM(IF($condition AND created_at > ?, order_quantity, 0)) AS `{$label}_{$key}`";
                        $bindings[] = $range['from'];
                    } elseif ($label === 'd5plus') {
                        $selects[] = "SUM(IF($condition AND created_at <= ?, order_quantity, 0)) AS `{$label}_{$key}`";
                        $bindings[] = $range['to'];
                    } else {
                        $selects[] = "SUM(IF($condition AND created_at >= ? AND created_at < ?, order_quantity, 0)) AS `{$label}_{$key}`";
                        $bindings[] = $range['from'];
                        $bindings[] = $range['to'];
                    }
                } else {
                    if ($label === 'lt1d') {
                        $selects[] = "COUNT(IF($condition AND created_at > ?, 1, NULL)) AS `{$label}_{$key}`";
                        $bindings[] = $range['from'];
                    } elseif ($label === 'd5plus') {
                        $selects[] = "COUNT(IF($condition AND created_at <= ?, 1, NULL)) AS `{$label}_{$key}`";
                        $bindings[] = $range['to'];
                    } else {
                        $selects[] = "COUNT(IF($condition AND created_at >= ? AND created_at < ?, 1, NULL)) AS `{$label}_{$key}`";
                        $bindings[] = $range['from'];
                        $bindings[] = $range['to'];
                    }
                }
            }
        }
        $query = SaleOrder::query()->selectRaw(implode(",\n", $selects), $bindings)
            ->where('is_test', SaleOrder::NOT_TEST)
            ->where('created_at', '>=', $thirtyDaysAgoPST)
            ->whereIn('order_status', [SaleOrder::STATUS_NEW_ORDER, SaleOrder::STATUS_IN_PRODUCTION]);

        if ($input['warehouse'] !== 'all') {
            $query->whereIn('warehouse_id', $input['warehouse']);
        }

        if (!empty($input['store_id'])) {
            $query->whereIn('store_id', $storeIds);
        }

        if (!empty($input['order_type']) && $input['order_type'] == SaleOrder::ORDER_TYPE_TIKTOK_ORDER) {
            $query->whereRaw('FIND_IN_SET(?, tag)', Tag::TIKTOK_TAG_ID);
        }

        return $query->first()->toArray();
    }
}
