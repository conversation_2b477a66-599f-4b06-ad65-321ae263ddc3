<?php

namespace App\Repositories;

use App\Models\ScreenClient;
use App\Models\ScreenClientAddress;
use Illuminate\Support\Facades\DB;

class ScreenClientRepository extends CommonRepository
{
    public function getList($input)
    {
        $sortBy = !empty($input['sort_by']) ? $input['sort_by'] : 'desc';
        $sortColumn = (!empty($input['sort_column'])) ? $input['sort_column'] : 'created_at';

        $limit = !empty($input['limit']) ? $input['limit'] : $this->limit;
        $query = ScreenClient::with(['createdBy', 'address']);
        if (!empty($input['name'])) {
            $query->where(function ($q) use ($input) {
                $q->where('name', 'like', '%' . $input['name'] . '%')
                    ->orWhere('company', 'like', '%' . $input['name'] . '%');
            });
        }

        return $query->orderBy($sortColumn, $sortBy)->paginate($limit);
    }

    public function getALl()
    {
        return ScreenClient::all();
    }

    public function getDetail($id)
    {
        return ScreenClient::with(['address'])->find($id);
    }

    public function create($input)
    {
        try {
            DB::beginTransaction();
            $data = [
                'name' => $input['name'] ?? null,
                'email' => $input['email'] ?? null,
                'company' => $input['company'] ?? null,
                'phone' => $input['phone'] ?? null,
                'contact_name' => $input['contact_name'] ?? null,
                'contact_email' => $input['contact_email'] ?? null,
                'contact_phone' => $input['contact_phone'] ?? null,
                'user_id' => auth()->user()->id
            ];
            $client = ScreenClient::create($data);
            ScreenClientAddress::create([
                'client_id' => $client->id,
                'name' => $input['name'] ?? null,
                'email' => $input['email'] ?? null,
                'phone' => $input['phone'] ?? null,
                'type' => ScreenClientAddress::TYPE_CLIENT,
                'street1' => $input['street1'] ?? null,
                'street2' => $input['street2'] ?? null,
                'city' => $input['city'] ?? null,
                'state' => $input['state'] ?? null,
                'zipcode' => $input['zipcode'] ?? null,
                'country' => $input['country'] ?? null,
            ]);
            if (!empty($input['is_has_billing_address'])) {
                ScreenClientAddress::create([
                    'client_id' => $client->id,
                    'name' => $input['billing_name'] ?? null,
                    'email' => $input['billing_email'] ?? null,
                    'phone' => $input['billing_phone'] ?? null,
                    'type' => ScreenClientAddress::TYPE_BILLING,
                    'street1' => $input['billing_street1'] ?? null,
                    'street2' => $input['billing_street2'] ?? null,
                    'city' => $input['billing_city'] ?? null,
                    'state' => $input['billing_state'] ?? null,
                    'zipcode' => $input['billing_zipcode'] ?? null,
                    'country' => $input['billing_country'] ?? null,
                ]);
            }
            DB::commit();

            return $client;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function update($input, $id)
    {
        try {
            $client = ScreenClient::find($id);
            if (empty($client)) {
                throw new \Exception('Client not found');
            }
            DB::beginTransaction();
            $data = [
                'name' => $input['name'] ?? null,
                'email' => $input['email'] ?? null,
                'company' => $input['company'] ?? null,
                'phone' => $input['phone'] ?? null,
                'contact_name' => $input['contact_name'] ?? null,
                'contact_email' => $input['contact_email'] ?? null,
                'contact_phone' => $input['contact_phone'] ?? null,
            ];
            $client->update($data);
            ScreenClientAddress::where('client_id', $client->id)->delete();
            ScreenClientAddress::create([
                'client_id' => $client->id,
                'name' => $input['name'] ?? null,
                'email' => $input['email'] ?? null,
                'phone' => $input['phone'] ?? null,
                'type' => ScreenClientAddress::TYPE_CLIENT,
                'street1' => $input['street1'] ?? null,
                'street2' => $input['street2'] ?? null,
                'city' => $input['city'] ?? null,
                'state' => $input['state'] ?? null,
                'zipcode' => $input['zipcode'] ?? null,
                'country' => $input['country'] ?? null,
            ]);
            if (!empty($input['is_has_billing_address'])) {
                ScreenClientAddress::create([
                    'client_id' => $client->id,
                    'name' => $input['billing_name'] ?? null,
                    'email' => $input['billing_email'] ?? null,
                    'phone' => $input['billing_phone'] ?? null,
                    'type' => ScreenClientAddress::TYPE_BILLING,
                    'street1' => $input['billing_street1'] ?? null,
                    'street2' => $input['billing_street2'] ?? null,
                    'city' => $input['billing_city'] ?? null,
                    'state' => $input['billing_state'] ?? null,
                    'zipcode' => $input['billing_zipcode'] ?? null,
                    'country' => $input['billing_country'] ?? null,
                ]);
            }
            DB::commit();

            return $client;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
