<?php

namespace App\Repositories;

use App\Models\SupplyAdjustInventory;
use App\Models\SupplyInventory;
use App\Models\SupplyQuantity;
use App\Models\TimeTracking;
use Illuminate\Support\Facades\DB;

class SupplyAdjustInventoryRepository extends CommonRepository
{
    public function fetchAll($request)
    {
        $query = SupplyAdjustInventory::with('supply:id,name,sku', 'user:id,username', 'employee:id,name')
            ->search($request);

        if ($request->has('export')) {
            return $query->get();
        }

        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function create($request)
    {
        try {
            DB::beginTransaction();
            $supplyQuantity = SupplyQuantity::where('supply_id', $request->supply_id)
                ->where('warehouse_id', config('jwt.warehouse_id'))->first();
            if (!$supplyQuantity) {
                return response()->json([
                    'supply_id' => ['The SKU cannot be adjusted because it has not been added to the inventory yet.']
                ], 422);
            }
            $availableQuantity = $supplyQuantity->quantity;
            $actualQuantity = $request->quantity;
            $adjustQuantity = $actualQuantity - $availableQuantity;
            if ($adjustQuantity == 0) {
                return response()->json([
                    'quantity' => ['The quantity is matched, so no need to adjust!']
                ], 422);
            }
            $adjustInventory = SupplyAdjustInventory::create([
                'user_id' => auth()->user()->id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'supply_id' => $request->supply_id,
                'quantity_available' => $availableQuantity,
                'quantity_on_hand' => $actualQuantity,
                'quantity_adjust' => $adjustQuantity,
                'employee_id' => $request->employee_id,
            ]);
            $direction = $adjustQuantity > 0 ? SupplyInventory::DIRECTION_INPUT : SupplyInventory::DIRECTION_OUTPUT;
            $supplyInventory = new SupplyInventory([
                'direction' => $direction,
                'type' => SupplyInventory::TYPE_ADJUST,
                'supply_id' => $request->supply_id,
                'object_id' => $adjustInventory->id,
                'object_name' => 'adjust',
                'quantity' => abs($adjustQuantity),
                'warehouse_id' => config('jwt.warehouse_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'user_id' => auth()->user()->id,
                'is_deleted' => SupplyInventory::IS_NOT_DELETED,
            ]);
            $supplyInventory->save();
            $supplyQuantity->quantity += $adjustQuantity;
            $supplyQuantity->save();

            $timeTracking = TimeTracking::find($request->id_time_checking);
            $timeTracking->quantity += 1;
            $timeTracking->end_time = date('Y-m-d H:i:s');
            $timeTracking->save();
            DB::commit();

            return response()->json([
                'message' => 'Supply inventory has been adjusted successfully!'
            ], 200);
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'error' => $exception->getMessage()
            ], 500);
        }
    }
}
