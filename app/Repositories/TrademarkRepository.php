<?php

namespace App\Repositories;

use App\Models\Trademark;
use App\Models\TrademarkHistory;
use Illuminate\Support\Facades\Log;

class TrademarkRepository extends CommonRepository
{
    public function getList($request)
    {
        $limit = $request->limit ?? 25;

        $query = Trademark::with(['ownership', 'histories', 'user']);
        if (!empty($request->trademark)) {
            $query->where('name', 'LIKE', "%{$request->trademark}%");
        }
        if (!empty($request->type)) {
            $query->where('type', $request->type);
        }
        if (!empty($request->ownership_id)) {
            $query->whereHas('ownership', function ($q) use ($request) {
                $q->where('id', $request->ownership_id);
            });
        }
        if ((string) $request?->is_active === (string) Trademark::INACTIVE) {
            $query->where('is_active', Trademark::INACTIVE);
        } elseif ((string) $request?->is_active === (string) Trademark::IS_ACTIVE) {
            $query->where('is_active', Trademark::IS_ACTIVE);
        }

        return $query->paginate($limit);
    }

    public function getAll()
    {
        return Trademark::all();
    }

    public function create($request)
    {
        try {
            $trademark = Trademark::create([
                'name' => $request['name'],
                'type' => $request['type'],
                'is_active' => $request['is_active'] ?? Trademark::IS_ACTIVE,
                'ownership_id' => $request['ownership_id'],
                'created_by' => auth()->user()->id
            ]);

            return $this->successResponse('Create trademark successfully', $trademark);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    public function update($id, $request)
    {
        try {
            $trademark = Trademark::find($id);
            TrademarkHistory::create([
                'trademark_id' => $trademark->id,
                'name' => $trademark->name,
                'type' => $trademark->type,
                'ownership_id' => $trademark->ownership_id ?? null,
                'is_active' => $trademark->is_active,
                'updated_by' => auth()->user()->id,
            ]);
            $trademark->name = $request['name'];
            $trademark->type = $request['type'];
            $trademark->ownership_id = $request['ownership_id'] ?? null;
            $trademark->is_active = $request['is_active'];
            $trademark->save();

            return $this->successResponse('Update trademark successfully', $trademark);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    public function delete($id)
    {
        try {
            $trademark = Trademark::find($id);
            if (empty($trademark)) {
                return $this->errorResponse('Trademark not found', 404);
            }
            $trademark->is_deleted = Trademark::IS_ACTIVE;
            $trademark->save();

            return $this->successResponse('Remove trademark successfully', $trademark);
        } catch (\Exception $exception) {
            Log::debug($exception->getMessage());

            return $this->errorResponse($exception->getMessage(), $exception->getCode());
        }
    }
}
