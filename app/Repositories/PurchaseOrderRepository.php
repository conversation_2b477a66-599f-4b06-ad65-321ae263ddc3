<?php

namespace App\Repositories;

use App\Models\InventoryAddition;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderBox;
use App\Models\PurchaseOrderBoxItem;
use App\Models\PurchaseOrderHistory;
use App\Models\PurchaseOrderItem;
use App\Models\Warehouse;
use App\Repositories\Contracts\PurchaseOrderRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class PurchaseOrderRepository implements PurchaseOrderRepositoryInterface
{
    const LIMIT = 10;

    public function fetchAll($input = null)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = PurchaseOrder::with(['vendor', 'user'])
            ->withCount([
                'items as total_price' => function ($query) {
                    $query->select(DB::raw('SUM(total)'));
                },
                'items as total_quantity' => function ($query) {
                    $query->select(DB::raw('SUM(quantity)'));
                },
                'items as total_quantity_onhand' => function ($query) {
                    $query->select(DB::raw('IFNULL(SUM(quantity_onhand), 0)'));
                },
                'box as total_box' => function ($query) {
                    $query->select(DB::raw('COUNT(*)'));
                },
                'box as total_received_box' => function ($query) {
                    $query->select(DB::raw('COUNT(*)'))->whereNotNull('received_at');
                }
            ])
            ->where('warehouse_id', $input['warehouse_id']);

        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query->whereHas('items', function ($q) use ($sku) {
                $q->whereHas('product', function ($query) use ($sku) {
                    $query->where('sku', 'LIKE', '%' . $sku . '%');
                });
            });
        }

        if (!empty($input['vendor_id'])) {
            $query->where('vendor_id', $input['vendor_id']);
        }

        if (!empty($input['order_number'])) {
            $orderNumber = $input['order_number'];
            $query->where('order_number', 'LIKE', '%' . $orderNumber . '%');
        }

        if (!empty($input['po_number'])) {
            $poNumber = $input['po_number'];
            $query->where('po_number', 'LIKE', '%' . $poNumber . '%');
        }

        if (!empty($input['invoice_number'])) {
            $invoiceNumber = $input['invoice_number'];
            $query->where('purchase_order.invoice_number', 'LIKE', '%' . $invoiceNumber . '%');
        }

        if (!empty($input['tracking_number'])) {
            $trackingNumber = $input['tracking_number'];
            $query->leftJoin('purchase_order_box', function ($join) {
                $join->on('purchase_order_box.po_id', '=', 'purchase_order.id');
            })->where(function ($q) use ($trackingNumber) {
                $q->where('purchase_order.tracking_number', 'LIKE', '%' . $trackingNumber . '%')
                    ->orWhere('purchase_order_box.tracking_number', 'LIKE', '%' . $trackingNumber . '%');
            });
        }

        if (!empty($input['status'])) {
            $status = $input['status'];
            $query->where('order_status', $status);
        }

        if (!empty($input['order_date'])) {
            $data = $input['order_date'];

            if (!empty($data[0])) {
                $query->where('order_date', '>=', $data[0]);
            }

            if (!empty($data[1])) {
                $query->where('order_date', '<=', $data[1]);
            }
        }

        $tags = !empty($input['tag']) ? $input['tag'] : '';

        if (!empty($tags) && is_array($tags) && count($tags) > 0) {
            foreach ($tags as $key => $tag) {
                if ($key === 0) {
                    $query->whereRaw('FIND_IN_SET(?,tag)', $tag);
                } else {
                    $query->orWhereRaw('FIND_IN_SET(?,tag)', $tag);
                }
            }
        }

        if (isset($input['incoming']) && $input['incoming'] === 'true') {
            $query->whereNotIn('order_status', [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::CLOSED_STATUS]);
        }

        if (!empty($input['get_total'])) {
            return ['total' => $query->count()];
        }

        $sortColumn = !empty($input['sort_column']) ? $input['sort_column'] : 'order_date';
        $sortBy = !empty($input['sort_by']) ? $input['sort_by'] : 'desc';

        if (isset($input['incoming']) && $input['incoming'] === 'true') {
            $sortColumn = 'created_at';
        }

        if ($sortColumn == 'created_at') {
            $sortBy = strtolower($sortBy) == 'desc' ? 'asc' : 'desc';
        }

        $purchaseOrders = $query->orderBy($sortColumn, $sortBy)->paginate($limit);
        $purchaseOrders->each(function ($po) {
            if (in_array($po->order_status, [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::CLOSED_STATUS])
                && $po->total_quantity != $po->total_quantity_onhand) {
                $po->total_incoming = 0;
            } else {
                $po->total_incoming = $po->total_quantity - $po->total_quantity_onhand;
                $po->total_incoming = $po->total_incoming > 0 ? $po->total_incoming : 0;
            }
            $po->created_at_utc = shiftTimezoneToUTC($po->created_at);
        });

        return $purchaseOrders;
    }

    public function getPurchaseOrderSummary($input = [])
    {
        $query = DB::table('purchase_order as po')
            ->join('purchase_order_item as i', 'i.po_id', '=', 'po.id')
            ->selectRaw('
                SUM(i.quantity) as total_unit,
                SUM(IFNULL(i.quantity_onhand, 0)) as addition_unit,
                SUM(
                    CASE
                        WHEN po.order_status NOT IN (?, ?, ?) OR i.quantity = i.quantity_onhand
                        THEN GREATEST(i.quantity - IFNULL(i.quantity_onhand, 0), 0)
                        ELSE 0
                    END
                ) as incoming_unit,
                SUM(i.total) as total_purchase_amount
            ')
            ->addBinding([
                PurchaseOrder::CANCELLED_STATUS,
                PurchaseOrder::COMPLETED_STATUS,
                PurchaseOrder::CLOSED_STATUS,
            ]);

        // warehouse
        if (!empty($input['warehouse_id'])) {
            $query->where('po.warehouse_id', $input['warehouse_id']);
        }

        // vendor
        if (!empty($input['vendor_id'])) {
            $query->where('po.vendor_id', $input['vendor_id']);
        }

        // status
        if (!empty($input['status'])) {
            $query->where('po.order_status', $input['status']);
        }

        // order_date
        if (!empty($input['order_date'])) {
            if (!empty($input['order_date'][0])) {
                $query->where('po.order_date', '>=', $input['order_date'][0]);
            }

            if (!empty($input['order_date'][1])) {
                $query->where('po.order_date', '<=', $input['order_date'][1]);
            }
        }

        // sku
        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query->whereExists(function ($sub) use ($sku) {
                $sub->select(DB::raw(1))
                    ->from('purchase_order_item as poi')
                    ->join('product as p', 'p.id', '=', 'poi.product_id')
                    ->whereRaw('poi.po_id = po.id')
                    ->where('p.sku', 'LIKE', "%{$sku}%");
            });
        }

        // order_number
        if (!empty($input['order_number'])) {
            $query->where('po.order_number', 'LIKE', '%' . $input['order_number'] . '%');
        }

        // po_number
        if (!empty($input['po_number'])) {
            $query->where('po.po_number', 'LIKE', '%' . $input['po_number'] . '%');
        }

        // invoice_number
        if (!empty($input['invoice_number'])) {
            $query->where('po.invoice_number', 'LIKE', '%' . $input['invoice_number'] . '%');
        }

        // tracking_number (từ po hoặc box)
        if (!empty($input['tracking_number'])) {
            $tracking = $input['tracking_number'];
            $query->where(function ($q) use ($tracking) {
                $q->where('po.tracking_number', 'LIKE', "%$tracking%")
                    ->orWhereExists(function ($sub) use ($tracking) {
                        $sub->select(DB::raw(1))
                            ->from('purchase_order_box as b')
                            ->whereRaw('b.po_id = po.id')
                            ->where('b.tracking_number', 'LIKE', "%$tracking%");
                    });
            });
        }

        // tags
        if (!empty($input['tag']) && is_array($input['tag'])) {
            $query->where(function ($q) use ($input) {
                foreach ($input['tag'] as $index => $tag) {
                    if ($index === 0) {
                        $q->whereRaw('FIND_IN_SET(?, po.tag)', [$tag]);
                    } else {
                        $q->orWhereRaw('FIND_IN_SET(?, po.tag)', [$tag]);
                    }
                }
            });
        }

        // incoming = true
        if (!empty($input['incoming']) && $input['incoming'] === 'true') {
            $query->whereNotIn('po.order_status', [
                PurchaseOrder::CANCELLED_STATUS,
                PurchaseOrder::COMPLETED_STATUS,
                PurchaseOrder::CLOSED_STATUS,
            ]);
        }

        $result = $query->first();

        return [
            'total_unit' => (int) ($result->total_unit ?? 0),
            'addition_unit' => (int) ($result->addition_unit ?? 0),
            'incoming_unit' => (int) ($result->incoming_unit ?? 0),
            'total_purchase_amount' => number_format((float) ($result->total_purchase_amount ?? 0), 2),
        ];
    }

    public function create($input)
    {
        $orderParentInput = $this->buildPurchaseOrderInput($input);

        try {
            DB::transaction(function () use ($orderParentInput, $input) {
                $order = PurchaseOrder::create($orderParentInput);

                if (!empty($input['items'])) {
                    foreach ($input['items'] as $item) {
                        $OrderItem = $this->buildPurchaseOrderItem($item, $order->id);
                        PurchaseOrderItem::create($OrderItem);
                        if (!empty($item['product_id']) && !empty($item['quantity'])) {
                            ProductQuantityRepository::updateInComeQuantity($input['warehouse_id'], $item['product_id'], $item['quantity']);
                        }
                    }
                }

                $log = $this->buildPurchaseOrderLog($order->id, $input['user_id'], PurchaseOrderHistory::TYPE_CREATE);
                PurchaseOrderHistory::create($log);
            });
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 201);
    }

    private function buildPurchaseOrderLog($order_id, $user_id, $type, $message = null)
    {
        return [
            'po_id' => $order_id,
            'user_id' => $user_id,
            'message' => $message,
            'type' => $type,
        ];
    }

    private function buildPurchaseOrderInput(&$input)
    {
        if (empty($input['po_number'])) {
            $input['po_number'] = $this->generateOrderNumber();
        }

        if (!empty($input['order_date'])) {
            $input['order_date'] = Carbon::parse($input['order_date'])->format('Y-m-d H:m:s');
        }

        if (!empty($input['delivery_date'])) {
            $input['delivery_date'] = Carbon::parse($input['delivery_date'])->format('Y-m-d H:m:s');
        }

        if (!empty($input['tracking_number']) && !empty($input['order_status']) && in_array(
            $input['order_status'],
            [
                PurchaseOrder::NEW_ORDER_STATUS,
                PurchaseOrder::NOT_SHIPPED_STATUS
            ],
        )) {
            $input['order_status'] = PurchaseOrder::SHIPPED_STATUS;
        }

        return $input;
    }

    private function buildPurchaseOrderItem($item, $parent_order_id)
    {
        $data['product_id'] = !empty($item['product_id']) ? $item['product_id'] : null;
        $data['quantity'] = !empty($item['quantity']) ? $item['quantity'] : null;
        $data['price'] = !empty($item['price']) ? $item['price'] : null;
        $data['total'] = !empty($item['total']) ? $item['total'] : null;
        $data['po_id'] = $parent_order_id;

        return $data;
    }

    private function generateOrderNumber()
    {
        $data = DB::select('CALL create_serial("PO")');

        return $data[0]->number;
    }

    public function fetch($id)
    {
        $order = PurchaseOrder::with(['items'])->find($id);

        return $this->buildDataOrder($order);
    }

    public function fetchProductFromOrder($poId)
    {
        $order = PurchaseOrder::with(['items'])->find($poId);
        $data = $this->buildDataOrder($order);

        if (!empty($data['items'])) {
            return array_column($data['items'], 'product');
        }

        return [];
    }

    private function buildDataOrder($order)
    {
        if (!empty($order)) {
            $productArr = [];
            $order = $order->toArray();

            $itemAddition = InventoryAddition::query()
                ->where('po_id', $order['id'])
                ->where('is_deleted', 0)
                ->pluck('product_id')
                ->unique()
                ->toArray();

            if (!empty($order['items'])) {
                $productIds = array_column($order['items'], 'product_id');
                //$productData = Product::whereIn("id", $productIds)->get()->toArray();
                $productData = DB::table('product')
                    ->leftJoin('product_quantity', 'product.id', '=', 'product_quantity.product_id')
                    ->select('product.*', 'product_quantity.quantity as quantity')
                    ->whereIn('product.id', $productIds)
                    ->get()
                    ->toArray();

                foreach ($productData as $product) {
                    $productArr[$product->id] = $product;
                }

                foreach ($order['items'] as &$item) {
                    $item['has_addition'] = in_array($item['product_id'], $itemAddition);
                    $product = $productArr[$item['product_id']] ?? null;
                    $item['product'] = $product;
                    $item['is_quantity_editable'] = $item['quantity'] == $item['quantity_onhand'];
                }
            }

            return $order;
        }

        return [];
    }

    private function buildDataManyOrder($orders)
    {
        if (!empty($orders)) {
            $orders = $orders->toArray();
            $productArr = $productIdsArr = [];

            foreach ($orders as $order) {
                if (!empty($order['items'])) {
                    foreach ($order['items'] as $item) {
                        if (!empty($item['product_id'])) {
                            $productIdsArr[] = $item['product_id'];
                        }
                    }
                }
            }

            // $productData = Product::whereIn("id", array_unique($productIdsArr))->get()->toArray();
            $productData = DB::table('product')
                ->leftJoin('product_quantity', 'product.id', '=', 'product_quantity.product_id')
                ->select('product.*', 'product_quantity.quantity as quantity')
                ->whereIn('product.id', array_unique($productIdsArr))
                ->get()
                ->toArray();

            foreach ($productData as $product) {
                $productArr[$product->id] = $product;
            }

            foreach ($orders as &$order) {
                if (!empty($order['items'])) {
                    foreach ($order['items'] as &$item) {
                        $product = isset($productArr[$item['product_id']]) ? $productArr[$item['product_id']] : null;
                        $item['product'] = $product;
                    }
                }
            }

            return $orders;
        }

        return [];
    }

    public function update($id, $dataUpdate)
    {
        $warehouseId = $dataUpdate['warehouse_id'];
        $dataOrder = $this->buildPurchaseOrderInput($dataUpdate);
        [$dataOrder, $dataOrderItems] = $this->convertDataOrderUpdate($dataOrder);
        $productIdsNeedUpdateIncoming = collect();
        $logQuantityChange = collect();
        $logMoneyChange = collect();
        try {
            DB::transaction(function () use ($id, $dataOrder, $dataOrderItems, $productIdsNeedUpdateIncoming, $logQuantityChange, $logMoneyChange) {
                $existingPo = PurchaseOrder::where('id', $id)->first();
                $existingItems = PurchaseOrderItem::where('po_id', $id)->get()->keyBy('product_id');
                $po = PurchaseOrder::findOrFail($id);
                $po->update($dataOrder);
                $this->updateInventoryAddition($po);
                $inputItems = collect($dataOrderItems)->keyBy('product_id');
                foreach ($inputItems as $productId => $item) {
                    $orderItem = $this->buildPurchaseOrderItem($item, $po->id);
                    $product = Product::where('id', $productId)->select('sku', 'name')->first();
                    if (!$existingItems->has($productId)) {
                        // Thêm mới
                        PurchaseOrderItem::create($orderItem);
                        $logQuantityChange->push("Added {$item['quantity']} " . ($item['quantity'] == 1 ? 'item' : 'items') . " of {$product->sku}");
                        $productIdsNeedUpdateIncoming->push($productId);
                    } else {
                        // Cập nhật
                        $existing = $existingItems[$productId];
                        $diffQty = $item['quantity'] - $existing->quantity;
                        if ($diffQty != 0) {
                            $logQuantityChange->push("Updated quantity of {$product->sku} from {$existing->quantity} to {$item['quantity']}");
                            $productIdsNeedUpdateIncoming->push($productId);
                        }
                        $existing->update($orderItem);
                    }
                }
                // Xoá sản phẩm không còn
                $deletedProductIds = $existingItems->keys()->diff($inputItems->keys())->values()->all();
                foreach ($deletedProductIds as $deletedProductId) {
                    $deletedItem = $existingItems[$deletedProductId];
                    PurchaseOrderItem::where('po_id', $po->id)
                        ->where('product_id', $deletedProductId)
                        ->delete();
                    $product = Product::where('id', $deletedProductId)->select('sku', 'name')->first();
                    $logQuantityChange->push("Removed {$deletedItem->quantity} " . ($deletedItem->quantity == 1 ? 'item' : 'items') . " of {$product->sku}");
                    $productIdsNeedUpdateIncoming->push($deletedProductId);
                }
                if ($existingPo->sub_total != $dataOrder['sub_total']) {
                    $logMoneyChange->push('Subtotal changed from $' . numberFormat($existingPo->sub_total) . ' to $' . numberFormat($dataOrder['sub_total']));
                }
                if ($existingPo->total != $dataOrder['total']) {
                    $logMoneyChange->push('Total changed from $' . numberFormat($existingPo->total) . ' to $' . numberFormat($dataOrder['total']));
                }

                $items = PurchaseOrderItem::where('po_id', $po->id)->get();
                if ($items->every(fn ($item) => $item->quantity == $item->quantity_onhand)) {
                    $po->order_status = PurchaseOrder::COMPLETED_STATUS;
                    $po->save();
                } elseif ($items->contains(fn ($item) => $item->quantity_onhand > 0 && $item->quantity_onhand < $item->quantity)) {
                    $po->order_status = PurchaseOrder::PARTIAL_RECEIVED_STATUS;
                    $po->save();
                }
                if ($logMoneyChange->isNotEmpty()) {
                    $message = $logMoneyChange->implode(', ');
                    $log = $this->buildPurchaseOrderLog($id, $dataOrder['user_id'], PurchaseOrderHistory::TYPE_UPDATE, $message);
                    PurchaseOrderHistory::create($log);
                }
                if ($logQuantityChange->isNotEmpty()) {
                    $message = $logQuantityChange->implode(', ');
                    $log = $this->buildPurchaseOrderLog($id, $dataOrder['user_id'], PurchaseOrderHistory::TYPE_UPDATE, $message);
                    PurchaseOrderHistory::create($log);
                }
            });

            foreach ($productIdsNeedUpdateIncoming->unique() as $productId) {
                ProductQuantityRepository::updateInComeQuantity($warehouseId, $productId);
            }

            return response()->json(['message' => 'success']);
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    private function updateInventoryAddition(PurchaseOrder $po): void
    {
        InventoryAddition::where('po_id', $po->id)->update([
            'po_number' => $po->po_number,
            'invoice_number' => $po->invoice_number,
            'updated_at' => now(),
        ]);
    }

    private function convertDataOrderUpdate($dataUpdate)
    {
        $dataOrderItem = [];

        if (isset($dataUpdate['items'])) {
            if (!empty($dataUpdate['items'])) {
                $dataOrderItem = $dataUpdate['items'];
            }

            $dataUpdate = Arr::except($dataUpdate, ['items']);
        }

        return [$dataUpdate, $dataOrderItem];
    }

    public function cancelOrder($id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $purchaseOrder = PurchaseOrder::where('id', $id)->first();

            if (!$purchaseOrder) {
                return response()->json([
                    'status' => false,
                    'message' => 'Purchase order not found!'
                ], Response::HTTP_NOT_FOUND);
            }

            if ($purchaseOrder->order_status === PurchaseOrder::COMPLETED_STATUS) {
                return response()->json([
                    'status' => false,
                    'message' => 'Purchase order completed so cannot cancel!'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $purchaseOrder->order_status = PurchaseOrder::CANCELLED_STATUS;
            $purchaseOrder->status_updated_at = date('Y-m-d H:i:s');
            $purchaseOrder->save();
            ProductQuantityRepository::updateIncomingByPurchaseOrder($id);
            $log = $this->buildPurchaseOrderLog($id, auth()->user()['id'], PurchaseOrderHistory::TYPE_CANCEL);
            PurchaseOrderHistory::create($log);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function comment($input)
    {
        $input['type'] = PurchaseOrderHistory::TYPE_COMMENT;

        return PurchaseOrderHistory::create($input)->fresh();
    }

    public function history($input)
    {
        return PurchaseOrderHistory::with('user')
            ->where('po_id', $input['po_id'])
            ->orderBy('id', 'desc')
            ->get();
    }

    public function fetchOrderByPoNumber($poNumber, $vendorId = null)
    {
        $query = PurchaseOrder::with('items.product')
            ->whereNotIn('order_status', [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS])
            ->where('po_number', 'LIKE', '%' . $poNumber . '%');

        if (!empty($vendorId)) {
            $query->where('vendor_id', $vendorId);
        }

        return $query->get();
    }

    public function fetchOrderByVendor($vendorId = null)
    {
        return PurchaseOrder::with(['items.product', 'box.items'])
            ->whereNotIn('order_status', [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS])
            ->where('vendor_id', $vendorId)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->get();
    }

    public function fetchOrderByInvoiceNumber($invoiceNumber)
    {
        return PurchaseOrder::with('items.product')
            ->whereNotIn('order_status', [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS])
            ->where('invoice_number', 'LIKE', '%' . $invoiceNumber . '%')
            ->get();
    }

    public function fetchPurchaseOrderBoxDetail($poId)
    {
        return PurchaseOrderBox::with('items.product:id,name')->where('po_id', $poId)->get();
    }

    public function createOrderBox($inputs)
    {
        if (empty($inputs['invoice_number'])) {
            $po = PurchaseOrder::where('id', $inputs['po_id'])->first();
            $inputs['invoice_number'] = !empty($po->invoice_number) ? $po->invoice_number : null;
        }

        try {
            DB::transaction(function () use ($inputs) {
                $inputOrderBox['po_id'] = $inputs['po_id'];
                $inputOrderBox['invoice_number'] = $inputs['invoice_number'];
                $inputOrderBox['tracking_number'] = !empty($inputs['tracking_number']) ? $inputs['tracking_number'] : null;
                $inputOrderBox['box_number'] = !empty($inputs['box_number']) ? $inputs['box_number'] : null;
                $inputOrderBox['created_at'] = date('Y-m-d H:i:s');
                $purchaseOrderBox = PurchaseOrderBox::create($inputOrderBox);

                if (!empty($inputs['items'])) {
                    foreach ($inputs['items'] as $item) {
                        $inputOrderBoxItem['po_id'] = $inputs['po_id'];
                        $inputOrderBoxItem['po_box_id'] = $purchaseOrderBox->id;
                        $inputOrderBoxItem['product_id'] = $item['product_id'];
                        $inputOrderBoxItem['gtin'] = !empty($item['gtin']) ? $item['gtin'] : null;
                        $inputOrderBoxItem['sku'] = !empty($item['sku']) ? $item['sku'] : null;
                        $inputOrderBoxItem['quantity'] = $item['quantity'];
                        $inputOrderBoxItem['created_at'] = date('Y-m-d H:i:s');
                        PurchaseOrderBoxItem::create($inputOrderBoxItem);
                    }
                }
            });
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(PurchaseOrderBox::with('items')->latest('id')->first(), 201);
    }

    public function updateLostBox($inputs)
    {
        if (!empty($inputs['set_status']) && is_null($inputs['received'])) {
            return $inputs['set_status'];
        }

        return null;
    }

    public function updateOrderBox($id, $inputs)
    {
        try {
            DB::transaction(function () use ($id, $inputs) {
                $inputOrderBox['invoice_number'] = !empty($inputs['invoice_number']) ? $inputs['invoice_number'] : null;
                $inputOrderBox['tracking_number'] = !empty($inputs['tracking_number']) ? $inputs['tracking_number'] : null;
                $inputOrderBox['box_number'] = !empty($inputs['box_number']) ? $inputs['box_number'] : null;
                $inputOrderBox['updated_at'] = date('Y-m-d H:i:s');
                $inputOrderBox['status'] = $this->updateLostBox($inputs);
                PurchaseOrderBox::where('id', $id)->update($inputOrderBox);
                $purchaseOrderBox = PurchaseOrderBox::where('id', $id)->first();
                $orderItemIds = PurchaseOrderBoxItem::where('po_box_id', $id)->pluck('id')->toArray();
                $orderUpdateId = [];

                if (!empty($inputs['items'])) {
                    foreach ($inputs['items'] as $item) {
                        if (!empty($item['id'])) {
                            $inputOrderBoxItem['product_id'] = $item['product_id'];
                            $inputOrderBoxItem['quantity'] = $item['quantity'];
                            $inputOrderBoxItem['updated_at'] = date('Y-m-d H:i:s');
                            PurchaseOrderBoxItem::where('id', $item['id'])->update($inputOrderBoxItem);
                            if ($purchaseOrderBox->status == PurchaseOrderBox::LOST_STATUS) {
                                ProductQuantityRepository::updateInComeQuantity($inputs['warehouse_id'], $item['product_id'], $item['quantity'] * -1);
                            }
                            $orderUpdateId[] = $item['id'];
                        } else {
                            $inputOrderBoxItem['product_id'] = $item['product_id'];
                            $inputOrderBoxItem['quantity'] = $item['quantity'];
                            $inputOrderBoxItem['po_id'] = $purchaseOrderBox->po_id;
                            $inputOrderBoxItem['gtin'] = !empty($item['gtin']) ? $item['gtin'] : null;
                            $inputOrderBoxItem['sku'] = !empty($item['sku']) ? $item['sku'] : null;
                            $inputOrderBoxItem['po_box_id'] = $id;
                            $inputOrderBoxItem['created_at'] = date('Y-m-d H:i:s');
                            PurchaseOrderBoxItem::create($inputOrderBoxItem);
                        }
                    }
                }
                //Todo : xu ly nhung order item bi xoa
                $idsDeleted = array_diff($orderItemIds, $orderUpdateId);
                PurchaseOrderBoxItem::whereIn('id', $idsDeleted)->delete();
                //Todo : xu ly nhung order item bi xoa
                $idsDeleted = array_diff($orderItemIds, $orderUpdateId);
                PurchaseOrderItem::whereIn('id', $idsDeleted)->delete();
            });
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function removeProductPurchaseOrderBox($boxId)
    {
        try {
            DB::transaction(function () use ($boxId) {
                PurchaseOrderBox::where('id', $boxId)->delete();
                PurchaseOrderBoxItem::where('po_box_id', $boxId)->delete();
            });
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function removeProductPurchaseOrderBoxItem($boxItemId)
    {
        try {
            PurchaseOrderBoxItem::where('id', $boxItemId)->delete();
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function fetchTrackingNumberByOrder($input)
    {
        $query = DB::table('purchase_order')
            ->select('purchase_order_box.tracking_number')
            ->join('purchase_order_box', 'purchase_order.id', '=', 'purchase_order_box.po_id')
            ->where(function ($q) {
                $q->where('purchase_order_box.status', '!=', PurchaseOrderBox::LOST_STATUS)
                    ->orWhereNull('purchase_order_box.status');
            })
            ->whereNull('purchase_order_box.received_at');

        if (!empty($input['invoice_number'])) {
            $invoice_number = $input['invoice_number'];
            $query->where('purchase_order.invoice_number', 'LIKE', '%' . $invoice_number . '%');
        }
        if (!empty($input['po_number'])) {
            $po_number = $input['po_number'];
            $query->where('purchase_order.po_number', 'LIKE', '%' . $po_number . '%');
        }

        return $query->get();
    }

    public function getPurchaseOrderByProduct($startDate, $endDate, $productId, $warehouseId)
    {
        return PurchaseOrderItem::select('product_id', 'quantity', 'price', 'po_id', DB::raw('purchase_order.order_date AS po_order_date'))
            ->whereHas('purchaseOrder', function ($query) use ($startDate, $endDate, $warehouseId) {
                $query->where('order_date', '>=', $startDate)
                    ->where('order_date', '<', $endDate)
                    ->where('order_status', '!=', PurchaseOrder::CANCELLED_STATUS)
                    ->where('warehouse_id', '=', $warehouseId);
            })
            ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
            ->where('product_id', $productId)
            ->whereNotNull('price')
            ->where('price', '!=', 0)
            ->orderBy('po_order_date', 'desc')
            ->get();
    }

    public function getNewestPoPriceByProduct($productGtin, $warehouseId)
    {
        $productId = Product::where('gtin', $productGtin)->first()?->id;
        $poItem = PurchaseOrderItem::select('purchase_order_item.id', 'product_id', 'quantity', 'price', DB::raw('purchase_order.order_date AS po_order_date'))
            ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
            ->whereNotNull('purchase_order.order_date')
            ->where('purchase_order.order_status', '!=', PurchaseOrder::CANCELLED_STATUS)
            ->where('purchase_order.warehouse_id', '=', $warehouseId)
            ->where('product_id', $productId)
            ->orderBy('po_order_date', 'desc')
            ->first();
        $dataCountry = [];

        if ($warehouseId == Warehouse::WAREHOUSE_MEXICO_ID) {
            $dataPartNumber = PartNumber::with('partNumberCountry:iso2,name,id')->where('product_id', $productId)->get();
            $dataCountry = $dataPartNumber->map(function ($item) {
                return [
                    'part_number' => $item->part_number,
                    'country' => $item->partNumberCountry->name,
                    'iso2' => $item->partNumberCountry->iso2,
                ];
            });
        }

        return [
            'status' => 200,
            'data' => [
                'product_id' => $productId,
                'newest_po_price' => !empty($poItem) ? $poItem?->price : 0,
                'data_country' => $dataCountry
            ]
        ];
    }

    public function getLostBoxPurchaseOrderByProduct($startDate, $endDate, $productId, $warehouseId)
    {
        return PurchaseOrderBoxItem::select('product_id', 'quantity', 'po_id')
            ->whereHas('purchaseOrder', function ($query) use ($startDate, $endDate, $warehouseId) {
                $query->where('order_date', '>=', $startDate)
                    ->where('order_date', '<=', $endDate)
                    ->where('order_status', PurchaseOrder::COMPLETED_STATUS)
                    ->where('warehouse_id', '=', $warehouseId)
                    ->orderBy('order_date', 'desc');
            })
            ->whereHas('purchaseOrderBox', function ($query) {
                $query->where('status', PurchaseOrderBox::LOST_STATUS);
            })
            ->where('product_id', $productId)
            ->get();
    }

    public function fetchOrdersHasProduct($request)
    {
        $limit = !empty($request->limit) ? $request->limit : 10;

        return PurchaseOrder::select('id', 'po_number', 'order_date', 'order_status')
            ->whereHas('items', function ($query) use ($request) {
                $query->where('product_id', $request->product_id);
            })
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->whereNotIn('order_status', [PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::CANCELLED_STATUS])
            ->paginate($limit);
    }

    public function getProductInPurchaseOrderByGtin($poId, $gtin)
    {
        $data = PurchaseOrder::query()
            ->select('product.*')
            ->join('purchase_order_item', 'purchase_order.id', '=', 'purchase_order_item.po_id')
            ->join('product', 'purchase_order_item.product_id', '=', 'product.id')
            ->where('purchase_order.id', $poId)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->whereNotNull('gtin')
            ->where('gtin', '<>', '')
            ->where('is_deleted', 0)
            ->whereRaw('COALESCE(INSTR(?, gtin)) > 0', [$gtin])
            ->first();

        if (empty($data)) {
            return response()->json(['gtin' => ['Product not found in this purchase order']], 404);
        }

        return $data;
    }

    public function getLatestProductPriceFromPo($productId)
    {
        $lastPoItemsHasProduct = PurchaseOrderItem::query()
            ->select('purchase_order_item.price')
            ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
            ->where('purchase_order_item.product_id', $productId)
            ->where('purchase_order.order_status', '<>', PurchaseOrder::CANCELLED_STATUS)
            ->whereNotNull('purchase_order_item.price')
            ->where('purchase_order_item.price', '>', 0)
            ->orderBy('purchase_order.order_date', 'desc')
            ->orderBy('purchase_order.id', 'desc')
            ->first();

        return $lastPoItemsHasProduct->price ?? 0;
    }

    public function calculateTotalByClosedStatus($purchaseOrder)
    {
        $shortAmount = $purchaseOrder->items
            ->sum(function ($item) {
                $quantity = $item['quantity'] ?? 0;
                $quantityOnHand = $item['quantity_onhand'] ?? 0;
                $price = $item['price'] ?? 0;

                return ($quantity - $quantityOnHand) * $price;
            });
        $subTotal = $purchaseOrder->sub_total ?? 0;
        $fee = $purchaseOrder->fee ?? 0;
        $discount = $purchaseOrder->discount ?? 0;

        return $subTotal + $fee - $shortAmount - $discount;
    }
}
