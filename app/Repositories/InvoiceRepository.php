<?php

namespace App\Repositories;

use App\Exports\FulfillmentExport;
use App\Exports\InsertInvoiceExport;
use App\Exports\PackingInvoiceRedBubbleExport;
use App\Exports\ProductInvoiceExport;
use App\Exports\ProductInvoiceRedBubbleExport;
use App\Exports\ProductInvoiceTempExport;
use App\Exports\PromotionInvoiceExport;
use App\Exports\ShippingInvoiceExport;
use App\Exports\ShippingInvoiceTempExport;
use App\Exports\SurchargeInvoiceExport;
use App\Jobs\AlertDownloadInvoiceToGoogleChatJob;
use App\Jobs\ConvertMugsToPdfJob;
use App\Jobs\MakeGeneralInvoice;
use App\Jobs\MakeInsertOrderInvoice;
use App\Jobs\MakeProductionInvoice;
use App\Jobs\MakeProductionInvoiceTemp;
use App\Jobs\MakePromotionInvoice;
use App\Jobs\MakeShippingInvoice;
use App\Jobs\MakeSurchargeInvoice;
use App\Jobs\ReGenerateInvoiceError;
use App\Jobs\StoreSaleOrderPricingSnapshotJob;
use App\Mail\SendMailProductionInvoice;
use App\Models\BarcodePrinted;
use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\InvoiceSaleOrderInsert;
use App\Models\PdfConverted;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\Store;
use App\Models\TmpInvoiceDownload;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceRepository
{
    const ITEM_PER_PAGE = 25;

    const ITEM_PER_PAGE_10 = 10;

    protected $timeCheckingRepository;

    public function __construct(TimeCheckingRepository $timeCheckingRepository)
    {
        $this->timeCheckingRepository = $timeCheckingRepository;
    }

    public function getAll($params, $hasPaging = true)
    {
        setTimezone();
        $query = Invoice::with(['store']);

        if (!isset($params['fetch_all'])) {
            $query->where('has_error', Invoice::NO_ERROR);
        }

        if (isset($params['invoice_number'])) {
            $query->where('invoice_number', 'LIKE', '%' . $params['invoice_number'] . '%');
        }

        if (isset($params['store_id'])) {
            $query->where('store_id', $params['store_id']);
        } else {
            $query->where('store_id', Auth::id());
        }

        if (isset($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }

        if (isset($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }

        if (!empty($params['type'])) {
            $query->where('type', $params['type']);
        }

        $query->latest();

        return $hasPaging
            ? $query->paginate($params['limit'] ?? self::ITEM_PER_PAGE)
            : $query->get();
    }

    public function createInvoice($store, $params, $fnInfo = null)
    {
        $startDate = $params['startDate'];
        $endDate = $params['endDate'];
        $type = $params['type'];

        if (Invoice::where('start_at', $startDate)
            ->where('end_at', $endDate)
            ->where('store_id', $store->id)
            ->where('type', $type)->first()
        ) {
            $fnInfo && $fnInfo("The invoice from $startDate to $endDate was generated");

            return [
                'status' => false,
                'message' => "The invoice from $startDate to $endDate was generated",
            ];
        }

        // If has no sale order => doesn't make invoice
        $saleOrder = new SaleOrder();
        $queryShippedWithoutLabelOrder = $saleOrder->select('*')->where('store_id', $store->id)
            ->whereHas('shipmentDefault', function ($q) use ($startDate, $endDate) {
                $q->where('created_at', '>=', $startDate)
                  ->where('created_at', '<=', $endDate)
                  ->where('provider', '<>', Shipment::PROVIDER_MARKETPLACE);
            })
        ->where('is_test', SaleOrder::NOT_TEST)
        ->whereDoesntHave('invoices', function ($q) use ($type) {
            $q->where('type', $type);
        });

        $queryShippedLabelOrder = $saleOrder->select('*')->where('store_id', $store->id)
            ->where('order_status', SaleOrder::STATUS_SHIPPED)
            ->whereHas('shipmentDefault', function ($q) use ($startDate, $endDate) {
                $q->where('created_at', '>=', $startDate)
                    ->where('created_at', '<=', $endDate)
                    ->where('provider', Shipment::PROVIDER_MARKETPLACE);
            })
            ->where('is_test', SaleOrder::NOT_TEST)
            ->whereDoesntHave('invoices', function ($q) use ($type) {
                $q->where('type', $type);
            });

        $queryShippedWithoutLabelOrderNotNull = $queryShippedWithoutLabelOrder->clone()->whereNotNull('calculated_at');
        $queryShippedLabelOrderNotNull = $queryShippedLabelOrder->clone()->whereNotNull('calculated_at');
        $queryShippedCalculatedNotNull = $queryShippedWithoutLabelOrderNotNull->clone()->union($queryShippedLabelOrderNotNull->clone());

        $queryShippedWithoutLabelOrderNull = $queryShippedWithoutLabelOrder->clone()->whereNull('calculated_at');
        $queryShippedLabelOrderNull = $queryShippedLabelOrder->clone()->whereNull('calculated_at');
        $queryShippedCalculatedNull = $queryShippedWithoutLabelOrderNull->clone()->union($queryShippedLabelOrderNull->clone());

        $queryShippedWithoutLabelOrderHasOrderInsert = $queryShippedWithoutLabelOrder->clone()->whereHas('saleOrderInsertCalculatePrice');
        $queryShippedLabelOrderHasOrderInsert = $queryShippedLabelOrder->clone()->whereHas('saleOrderInsertCalculatePrice');
        $queryOrderInsertShippedCalculatedPrice = $queryShippedWithoutLabelOrderHasOrderInsert->clone()->union($queryShippedLabelOrderHasOrderInsert->clone());

        $queryLateCancelled = $saleOrder
        ->where('store_id', $store->id)
        ->where('order_status', SaleOrder::STATUS_LATE_CANCELLED)
        ->where('cancelled_at', '>=', $startDate)
        ->where('cancelled_at', '<=', $endDate)
        ->where('is_test', SaleOrder::NOT_TEST)
        ->whereDoesntHave('invoices', function ($q) use ($type) {
            //TODO: if this query is slow, add condition created_at > 6 months ago to narrow down the column
            $q->where('type', $type);
        });
        $queryLateCancelledCalculatedNotNull = $queryLateCancelled->clone()->whereNotNull('calculated_at');
        $queryLateCancelledCalculatedNull = $queryLateCancelled->clone()->whereNull('calculated_at');
        $queryOrderInsertLateCancelledCalculatedPrice = $queryLateCancelled->clone()->whereHas('saleOrderInsertCalculatePrice');
        $saleOrderQueryCalculatedNotNull = $queryShippedCalculatedNotNull->clone()->union($queryLateCancelledCalculatedNotNull->clone());
        $saleOrderQueryCalculatedNull = $queryShippedCalculatedNull->clone()->union($queryLateCancelledCalculatedNull->clone());
        $saleOrderQueryCalculatedPrice = $queryOrderInsertShippedCalculatedPrice->clone()->union($queryOrderInsertLateCancelledCalculatedPrice->clone());

        if ($saleOrderQueryCalculatedNotNull->count() == 0 && $saleOrderQueryCalculatedNull->count() == 0) {
            $fnInfo && $fnInfo('No sale order for generate invoice');

            return [
                'status' => false,
                'message' => 'No sale order for generate invoice',
            ];
        }
        $hasError = Invoice::NO_ERROR;
        if ($saleOrderQueryCalculatedNull->count() > 0) {
            $fnInfo && $fnInfo('Missing sale orders has not been calculated');
            $hasError = Invoice::HAS_ERROR;
        }

        $fnInfo && $fnInfo('Generate invoice from ' . $startDate . ' to ' . $endDate);
        $orderInserts = $saleOrderQueryCalculatedPrice->get();
        // Create invoice record
        $newInvoice = new Invoice();
        $newInvoice->type = $type;
        $newInvoice->has_error = $hasError;
        $newInvoice->name = 'Invoice';
        $newInvoice->store_id = $store->id;
        $newInvoice->start_at = $startDate;
        $newInvoice->end_at = $endDate;
        $newInvoice->save();

        // Sinh invoice_number sau khi đã có ID
        $storeCode = trim(str_replace([' - API', '-API'], '', $store->code));
        $newInvoice->invoice_number = $storeCode . $newInvoice->id;
        $newInvoice->save(); // Lưu lại invoice_number

        if ($store->id == 316905 && $type == 'monthly') {
            ///Test invoice monthly Printify
            $saleOrderQueryCalculatedNotNull->chunkById(400000, function ($orders) use ($newInvoice) {
                $idSuccess = $orders->pluck('id');
                $newInvoice->saleOrders()->syncWithoutDetaching($idSuccess);
            });
        } else {
            $idSuccess = $saleOrderQueryCalculatedNotNull->pluck('id');
            if ($idSuccess->isNotEmpty()) {
                $newInvoice->saleOrders()->sync($idSuccess);
            }
        }
        $idErrors = $saleOrderQueryCalculatedNull->pluck('id');
        if ($idErrors->isNotEmpty()) {
            $newInvoice->saleOrderErrors()->sync($idErrors);
        }
        if ($orderInserts->isNotEmpty()) {
            foreach ($orderInserts as $order) {
                foreach ($order->saleOrderInsertCalculatePrice as $orderInsert) {
                    InvoiceSaleOrderInsert::create([
                        'invoice_id' => $newInvoice->id,
                        'sale_order_id' => $orderInsert->order_id,
                        'order_insert_id' => $orderInsert->order_insert_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
            $newInvoice->has_insert_invoice = Invoice::HAS_INSERT_INVOICE;
            $hasError = InvoiceSaleOrderInsert::query()
                ->select('sale_order_insert_calculate_price.id')
                ->join('sale_order_insert_calculate_price', 'sale_order_insert_calculate_price.order_insert_id', '=', 'invoice_sale_order_insert.order_insert_id')
                ->where('invoice_sale_order_insert.invoice_id', $newInvoice->id)
                ->whereNull('sale_order_insert_calculate_price.calculated_at')
                ->exists();
            if ($hasError) {
                $newInvoice->has_error_insert_invoice = Invoice::HAS_ERROR;
            }
            $newInvoice->save();
        }

        return [
            'status' => true,
            'data' => $newInvoice,
        ];
    }

    public function makeJobInvoice($invoices = [], $fnInfo = null)
    {
        foreach ($invoices as $key => $invoice) {
            // Dispatch job create invoice pdf
            $fnInfo && $fnInfo('Generate general invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
            $this->dispatchJobGenerateInvoice($invoice);

            // Dispatch job create invoice production excel
            $fnInfo && $fnInfo('Generate production invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
            $this->dispatchJobGenerateProductionInvoice($invoice);

            // Dispatch job create invoice production excel
            $fnInfo && $fnInfo('Generate shipping invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
            $this->dispatchJobGenerateShippingInvoice($invoice);

            $fnInfo && $fnInfo('Generate insert invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
            $this->dispatchJobGenerateInsertInvoice($invoice);

            $fnInfo && $fnInfo('Generate surcharge invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
            $this->dispatchJobGenerateSurchargeInvoice($invoice);

            // Dispatch job create invoice promotion excel
            $fnInfo && $fnInfo('Generate promotion invoice (' . $invoice->id . ') - invoice number: ' . $invoice->invoice_number);
//            $this->dispatchJobGenerateProductionInvoice($invoice);
            $this->dispatchJobGeneratePromotionInvoice($invoice);
        }
    }

    public function dispatchJobGenerateInvoice($invoice)
    {
        if ($invoice->processing_general_at) {
            return;
        }

        dispatch(new MakeGeneralInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_GENERAL_INVOICE);
    }

    public function dispatchJobGenerateProductionInvoice($invoice)
    {
        if ($invoice->processing_production_at) {
            return;
        }

        dispatch(new MakeProductionInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_PRODUCTION_INVOICE);
    }

    public function dispatchJobGeneratePromotionInvoice($invoice)
    {
        if ($invoice->processing_promotion_at) {
            return;
        }

        dispatch(new MakePromotionInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_PROMOTION_INVOICE);
    }

    public function dispatchJobGenerateShippingInvoice($invoice)
    {
        if ($invoice->processing_shipping_at) {
            return;
        }

        dispatch(new MakeShippingInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_SHIPPING_INVOICE);
    }

    public function dispatchJobGenerateInsertInvoice($invoice)
    {
        if ($invoice->processing_insert_at) {
            return;
        }

        dispatch(new MakeInsertOrderInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_INSERT_INVOICE);
    }

    public function autoCreateInvoice($fnInfo = null)
    {
        ini_set('memory_limit', '10G');
        setTimezone();
        $today = now()->setTimezone(getTimezone());

        // Select all store to auto create invoice
        $stores = Store::where('is_calculate_price', true)->where('payment_terms', Store::STORE_POSTPAID)->get();

        foreach ($stores as $store) {
            $fnInfo && $fnInfo('Generate invoice for store (' . $store->id . ')');
            DB::beginTransaction();
            try {
                $dates = [];
                //create invoice every day
                $theDayBefore = $today->clone()->subDay();
                array_push($dates, [
                    'type' => 'daily',
                    'startDate' => $theDayBefore->clone()->startOfDay(),
                    'endDate' => $theDayBefore->clone()->endOfDay(),
                ]);
                // Create invoice every week
                if ($today->isMonday()) {
                    $beforeWeek = $today->clone()->subDay();
                    array_push($dates, [
                        'type' => 'weekly',
                        'startDate' => $beforeWeek->clone()->startOfWeek(),
                        'endDate' => $beforeWeek->clone()->endOfWeek()
                    ]);
                }
                // Delay to add price product
                if ($today->isTuesday()) {
                    $beforeWeek = $today->clone()->subDays(2);
                    array_push($dates, [
                        'type' => 'weekly',
                        'startDate' => $beforeWeek->clone()->startOfWeek(),
                        'endDate' => $beforeWeek->clone()->endOfWeek()
                    ]);
                }
                if ($today->isWednesday()) {
                    $beforeWeek = $today->clone()->subDays(3);
                    array_push($dates, [
                        'type' => 'weekly',
                        'startDate' => $beforeWeek->clone()->startOfWeek(),
                        'endDate' => $beforeWeek->clone()->endOfWeek()
                    ]);
                }
                //create invoice every 2 weeks start from 24/03/2025
                $biWeeklyStartDate = Carbon::create(2025, 3, 24);
                $daysDifference = $today->diffInDays($biWeeklyStartDate);
                //set max 3 days same as month and week
                if ($daysDifference % 14 < 4) {
                    $currentPeriodNumber = floor($daysDifference / 14);
                    $startDate = $biWeeklyStartDate->clone()->addDays(($currentPeriodNumber - 1) * 14);
                    $endDate = $startDate->clone()->addDays(13);
                    array_push($dates, [
                        'type' => 'bi-weekly',
                        'startDate' => $startDate->startOfDay(),
                        'endDate' => $endDate->endOfDay(),
                    ]);
                }

                // Create invoice every month
                if ($today->day == 1 || $today->day == 2 || $today->day == 3) {
                    $beforeMonth = $today->clone()->subDays($today->day);
                    array_push($dates, [
                        'type' => 'monthly',
                        'startDate' => $beforeMonth->clone()->startOfMonth(),
                        'endDate' => $beforeMonth->clone()->endOfMonth()
                    ]);
                }
                $invoiceGenerate = [];
                foreach ($dates as $date) {
                    $response = $this->createInvoice($store, $date, $fnInfo);
                    if (!$response['status']) {
                        continue;
                    }
                    $invoiceGenerate[] = $response['data'];
                }
                DB::commit();
                $this->makeJobInvoice($invoiceGenerate, $fnInfo);
            } catch (\Throwable $th) {
                DB::rollBack();
                $fnInfo && $fnInfo($th->getMessage());
            }

            sleep(15);
        }
    }

    public function testCreateInvoice($params)
    {
        if (empty($params['store_id']) || empty($params['start_date']) || empty($params['end_date']) || empty($params['type'])) {
            return [
                'status' => false,
                'message' => 'Wrong params.',
            ];
        }
        $store = Store::findOrFail($params['store_id']);
        $startDate = \Carbon\Carbon::createFromFormat('Y-m-d', $params['start_date'])->startOfDay();
        $endDate = \Carbon\Carbon::createFromFormat('Y-m-d', $params['end_date'])->endOfDay();

        DB::beginTransaction();
        try {
            setTimezone();
            $field['startDate'] = $startDate;
            $field['endDate'] = $endDate;
            $field['type'] = $params['type'];

            $response = $this->createInvoice($store, $field, null);
            if (!$response['status']) {
                return $response;
            }
            DB::commit();
            $this->makeJobInvoice([$response['data']]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return [
            'status' => true,
            'message' => 'Create invoice success.',
        ];
    }

    public function generateGeneralInvoice(Invoice $invoice)
    {
        switch ($invoice->store_id) {
            case Store::STORE_REDBUBBLE:
                $this->generatePDFInvoiceRedBubble($invoice);
                break;
            default:
                $this->generatePDFInvoice($invoice);
                break;
        }

        Invoice::where('id', $invoice->id)->update(['processing_general_at' => null]);
        echo "General invoice $invoice->id has been processed\n";
    }

    public function generatePDFInvoice($invoice)
    {
        if ($invoice->has_error) {
            return;
        }
        if ($invoice->has_insert_invoice && $invoice->has_error_insert_invoice) {
            return;
        }

        $invoice->load(['store', 'store.storeAddressBilling', 'store.storeProducts']);
        $totalItems = 0;
        $totalPricesWithoutHandling = 0;
        $totalPricesHandling = 0;
        $totalShippingPrice = 0;
        $totalInsertItems = 0;
        $totalPriceInserts = 0;
        $totalQuantityHandlingFee = 0;
        $totalPeakShippingFee = 0;
        $totalPeakSurchargeOrder = 0;
        $totalShippingUnit = 0;
        $totalPromotion = 0;
        $allSaleOrderIds = InvoiceSaleOrder::where('invoice_id', $invoice->id)->pluck('sale_order_id');
        echo 'Number of sale orders: ' . $allSaleOrderIds->count() . "\n";

        $chunkSaleOrderIds = $allSaleOrderIds->chunk(500);
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $promotionRepository = app()->make(PromotionRepository::class);
        $orderInsertSurchargeResults = collect();
        foreach ($chunkSaleOrderIds as $saleOrderIds) {
            $saleOrders = SaleOrder::whereIn('id', $saleOrderIds)->with(['items', 'peakShippingFee'])->get();
            foreach ($saleOrders as $saleOrder) {
                $totalPeakShippingFee += $saleOrder->order_status != SaleOrder::STATUS_LATE_CANCELLED
                ? ($saleOrder->peakShippingFee->value ?? 0)
                : 0;

                if ($saleOrder->order_status != SaleOrder::STATUS_LATE_CANCELLED
                    && !empty($saleOrder->peakShippingFee?->value)) {
                    $totalPeakSurchargeOrder += 1;
                }
                $i = 0;
                foreach ($saleOrder->items as $saleOrderItem) {
                    if (!$saleOrderItem->product_id) {
                        break;
                    }
                    $totalItems += $saleOrderItem->quantity;
                    $blankPriceAfterDiscount = $saleOrderItem->blank_price * (1 - (($saleOrderItem->promotion ? $saleOrderItem->promotion->discount : 0)) / 100);
                    $totalPricesWithoutHandling += $saleOrderItem->quantity * ($saleOrderItem->unit_price - $saleOrderItem->blank_price + $blankPriceAfterDiscount - $saleOrderItem->handling_fee);
                    if ($saleOrderItem->handling_fee > 0) {
                        $totalPricesHandling += $saleOrderItem->quantity * $saleOrderItem->handling_fee;
                        $totalQuantityHandlingFee += $saleOrderItem->quantity;
                    }
                    $i++;
                }
                $totalShippingPrice += $saleOrder->shipping_calculate;
                if ($saleOrder->shipment_id) {
                    $totalShippingUnit += 1;
                }
            }
            $saleOrdersSurcharge = $surchargeFeeRepository->fetchSaleOrdersSurcharge($saleOrderIds->toArray());
            $saleOrderItemsSurcharge = $surchargeFeeRepository->fetchSaleOrdersItemsSurcharge($saleOrderIds->toArray(), false);
            $orderInsertSurchargeResults = $orderInsertSurchargeResults->concat($saleOrdersSurcharge);
            $orderInsertSurchargeResults = $orderInsertSurchargeResults->concat($saleOrderItemsSurcharge);
            $totalPromotion += $promotionRepository->calculatePromotions($saleOrderIds->toArray());
        }

        $orderInsertSurchargeResults = $orderInsertSurchargeResults->groupBy('name')->map(function ($group) {
            return [
                'name' => $group->first()->name,
                'total' => $group->sum('total'),
                'quantity' => $group->sum('quantity'),
                'fee' => $group->sum('quantity') > 0
                    ? round($group->sum('total') / $group->sum('quantity'), 2)
                    : 0,
                'per' => $group->first()->per,
            ];
        });
        $totalSurchargeFee = $orderInsertSurchargeResults?->sum('total') ?? 0;
        $invoice->total_items = $totalItems;
        $invoice->total_prices_without_handling = $totalPricesWithoutHandling;
        $invoice->total_prices_handling = $totalPricesHandling;
        $invoice->total_shipping = $totalShippingUnit;
        $invoice->total_shipping_prices = $totalShippingPrice;
        $invoice->total_quantity_handling = $totalQuantityHandlingFee;

        if ($invoice->has_insert_invoice) {
            $allSaleOrderInsertIds = InvoiceSaleOrderInsert::where('invoice_id', $invoice->id)->pluck('order_insert_id');
            echo 'Number of sale order inserts: ' . $allSaleOrderInsertIds->count() . "\n";

            $chunkSaleOrderInsertIds = $allSaleOrderInsertIds->chunk(500);
            foreach ($chunkSaleOrderInsertIds as $saleOrderInsertIds) {
                $saleOrderInserts = SaleOrderInsertCalculatePrice::whereIn('order_insert_id', $saleOrderInsertIds)->get();
                foreach ($saleOrderInserts as $saleOrderInsert) {
                    if ($saleOrderInsert->calculated_at) {
                        $totalInsertItems += $saleOrderInsert->qty;
                        $totalPriceInserts += $saleOrderInsert->amount_paid;
                    }
                }
            }
        }
        $invoice->total_inserts = $totalInsertItems;
        $invoice->total_price_inserts = $totalPriceInserts;
        $invoice->total_surcharge_fee = $totalSurchargeFee;
        $invoice->total_peak_order = $totalPeakSurchargeOrder;
        $invoice->total_peak_shipping_fee = $totalPeakShippingFee;
        $invoice->bill_to_name = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->name : $invoice->store->name;
        $invoice->bill_to_street1 = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->street1 : $invoice->store->street1;
        $invoice->bill_to_city = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->city : $invoice->store->city;
        $invoice->bill_to_state = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->state : $invoice->store->state;
        $invoice->bill_to_zip = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->zip : $invoice->store->zip;
        $invoice->bill_to_country = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->country : $invoice->store->country;
        $invoice->bill_to_email = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->email : $invoice->store->contact_email;
        $invoice->total_promotion = $totalPromotion;

        $invoice->surcharge_fee = $orderInsertSurchargeResults;

        app()->make('view.engine.resolver')->register('blade', function () {
            return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
        });

        $pdf = Pdf::loadView('pdf.general-invoice', ['invoice' => $invoice])->setPaper('a4');
        echo "Generated PDF invoice $invoice->id\n";

        return Storage::disk('s3')->put('invoices/invoice_' . $invoice->id . '.pdf', $pdf->output());
    }

    public function generatePDFInvoiceRedBubble($invoice)
    {
        $productAmount = SaleOrderItem::query()
            ->join('invoice_sale_order', 'sale_order_item.order_id', '=', 'invoice_sale_order.sale_order_id')
            ->where('invoice_sale_order.invoice_id', $invoice->id)
            ->sum('sale_order_item.amount_paid');

        $packingInvoiceExport = new PackingInvoiceRedBubbleExport($invoice);
        $invoice->product_amount = $productAmount;
        $invoice->packing_amount = $packingInvoiceExport->collection()->sum('totalBilled');
        $invoice->load(['store.storeAddressBilling']);
        $invoice->bill_to_name = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->name : $invoice->store->name;
        $invoice->bill_to_street1 = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->street1 : $invoice->store->street1;
        $invoice->bill_to_city = !empty($invoice->store->storeAddressBilling) ? $invoice->store->storeAddressBilling->city : $invoice->store->city;
        app()->make('view.engine.resolver')->register('blade', function () {
            return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
        });

        $pdf = Pdf::loadView('pdf.rb-general-invoice', ['invoice' => $invoice])->setPaper('a4');

        return Storage::disk('s3')->put('invoices/invoice_' . $invoice->id . '.pdf', $pdf->output());
    }

    public function generateProductionInvoice($invoice)
    {
        switch ($invoice->store_id) {
            case Store::STORE_REDBUBBLE:
                Excel::store(new ProductInvoiceRedBubbleExport($invoice), 'invoices/production_invoice_' . $invoice->id . '.xlsx', 's3');
                break;
            default:
                Excel::store(new ProductInvoiceExport($invoice), 'invoices/production_invoice_' . $invoice->id . '.xlsx', 's3');
                break;
        }

        Invoice::where('id', $invoice->id)->update(['processing_production_at' => null]);
        echo "Production invoice $invoice->id has been processed\n";
    }

    public function generatePromotionInvoice($invoice)
    {
        switch ($invoice->store_id) {
            case Store::STORE_REDBUBBLE:
                return;
                break;
            default:
                Excel::store(new PromotionInvoiceExport($invoice), 'invoices/promotion_invoice_' . $invoice->id . '.xlsx', 's3');
                break;
        }

        Invoice::where('id', $invoice->id)->update(['processing_promotion_at' => null]);
        echo "Promotion invoice $invoice->id has been processed\n";
    }

    public function generateShippingInvoice($invoice)
    {
        switch ($invoice->store_id) {
            case Store::STORE_REDBUBBLE:
                Excel::store(new PackingInvoiceRedBubbleExport($invoice), 'invoices/shipping_invoice_' . $invoice->id . '.xlsx', 's3');
                break;
            default:
                Excel::store(new ShippingInvoiceExport($invoice), 'invoices/shipping_invoice_' . $invoice->id . '.xlsx', 's3');
                break;
        }

        Invoice::where('id', $invoice->id)->update(['processing_shipping_at' => null]);
        echo "Shipping invoice $invoice->id has been processed\n";
    }

    public function generateInsertInvoice($invoice)
    {
        Excel::store(new InsertInvoiceExport($invoice), 'invoices/insert_invoice_' . $invoice->id . '.xlsx', 's3');

        Invoice::where('id', $invoice->id)->update(['processing_insert_at' => null]);
        echo "Insert invoice $invoice->id has been processed\n";
    }

    public function generateSurchargeInvoice($invoice)
    {
        Excel::store(new SurchargeInvoiceExport($invoice), 'invoices/surcharge_invoice_' . $invoice->id . '.xlsx', 's3');
        Invoice::where('id', $invoice->id)->update(['processing_surcharge_at' => null]);
        echo "Surcharge invoice $invoice->id has been processed\n";
    }

    public function generateProductionInvoiceTemp($store, $startDate, $endDate, $printSide, $downloadFulfill = null)
    {
        try {
            $timestamp = now()->timestamp;
            $fileName = "production_invoice_{$timestamp}.xlsx";

            $invoiceDownload = TmpInvoiceDownload::create([
                'store_id' => $store->id,
                'date' => "{$startDate}-{$endDate}",
                'type' => Invoice::TYPE_DOWNLOAD_PRODUCTION,
                'link_url' => $fileName,
            ]);

            // Export data to Excel and store the file
            Excel::store(new ProductInvoiceTempExport($store, $startDate, $endDate, $printSide), 'invoices_tmp/' . $fileName, 's3');

            $invoiceDownload->update(['status' => 1]);

            return true;
        } catch (\Throwable $exception) {
            Log::error('Error generating production invoice temp', [
                'store_id' => $store,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'exception' => $exception->getMessage(),
            ]);

            return false;
        }
    }

    public function generateProductionInvoiceTempSendingEmail($store, $startDate, $endDate, $printSide, $downloadFulfill = null)
    {
        try {
            $fileName = 'production_invoice_' . now()->getTimestamp() . '.xlsx';
            if ($downloadFulfill) {
                $fileName = 'sale_order_' . str_replace('-', '_', $startDate) . '-' . str_replace('-', '_', $endDate) . '.xlsx';
                Excel::store(new FulfillmentExport($downloadFulfill, $startDate, $endDate), $fileName, 'export');
                $setting = Setting::where('label', 'email_fulfill_report')->first();
            } else {
                Excel::store(new ProductInvoiceTempExport($store, $startDate, $endDate, $printSide), $fileName, 'export');
                $setting = Setting::where('label', 'email_production_invoice')->first();
            }
            $mailArr = explode(',', $setting->value);
            $to = trim($mailArr[0]);
            $cc = trim($mailArr[1]);
            $mail = Mail::to($to);
            $emailParams = [
                'store_name' => $store->name,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'mailCc' => $cc,
            ];
            $mail->send(new SendMailProductionInvoice($emailParams, $fileName));
            if (Storage::exists(storage_path('app/public/export/') . $fileName)) {
                Storage::delete(storage_path('app/public/export/') . $fileName);
            }
        } catch (\Exception $exception) {
            echo $exception->getMessage();

            return false;
        }

        return true;
    }

    public function downloadProductionFile($params)
    {
        $store = !empty($params['store_id']) ? Store::findOrFail($params['store_id']) : null;
        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $printSide = $params['print_side'] ?? null;
        $params['ip'] = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $params['type'] = Invoice::TYPE_DOWNLOAD_PRODUCTION;
        $currentTime = Carbon::now();
        $oneMinuteAgo = $currentTime->subMinute();
        $checkDuplicate = DB::table('download_invoice_history')
            ->where('store_id', $params['store_id'])
            ->where('ip', $params['ip'])
            ->whereBetween('created_at', [$oneMinuteAgo, $currentTime])
            ->exists();
        if ($checkDuplicate) {
            return 'Please run again in 1 minute !';
        }
        //$this->generateProductionInvoiceTemp($store, $startDate, $endDate, $printSide);
        $this->createDownloadHistory($params);
        $downloadFulfill = $params['download_fulfill'] ?? null;
        dispatch(new MakeProductionInvoiceTemp($store, $startDate, $endDate, $printSide, $downloadFulfill))->onQueue(Invoice::QUEUE_MAKE_PRODUCTION_INVOICE_TEMP);

        return  [
            'status' => true,
            'message' => 'The invoice will be delivered to your email shortly, depending on the data size. Please keep in mind that this procedure might take approximately 60 minutes.',
        ];
    }

    public function downloadShippingFile($params)
    {
        $store = Store::findOrFail($params['store_id']);
        $startDate = $params['start_date'];
        $endDate = $params['end_date'];
        $params['ip'] = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $params['type'] = Invoice::TYPE_DOWNLOAD_SHIPPING;
        $this->createDownloadHistory($params);

        return Excel::download(new ShippingInvoiceTempExport($store, $startDate, $endDate), 'shipping_invoice_' . now()->getTimestamp() . '.xlsx');
    }

    public function createDownloadHistory($params)
    {
        try {
            DB::table('download_invoice_history')->insert([
                'user_id' => $params['user_id'] ?? null,
                'store_id' => $params['store_id'] ?? null,
                'ip' => $params['ip'] ?? null,
                'invoice_id' => $params['invoice_id'] ?? null,
                'start_at' => $params['start_date'] ?? null,
                'end_at' => $params['end_date'] ?? null,
                'type' => $params['type'],
                'source' => $params['source'] ?? null,
            ]);
            $now = Carbon::now('America/Los_Angeles')->toDateTimeString();
            $source = ($params['type'] ?? '') == 'inventory' ? 'SwiftPOD app' : 'Seller dashboard';
            $store = null;
            if (!empty($params['store_id'])) {
                $store = Store::find($params['store_id']);
            }
            if (!empty($params['function']) && $params['function'] == 'export') {
                $userName = $params['type'] == 'inventory' ? (Auth::user()->username ?? 'User ' . Auth::id()) : 'A user';
                $messageAlert = $userName . " has recently downloaded a ${params['type']} invoice (ID: ${params['invoice_id']}) " . (!empty($store->name) ? "of {$store->name} store" : '') . " via the $source at $now";
            } else {
                $messageAlert = "A user has recently downloaded an invoice (${params['start_date']} to ${params['end_date']}) " . (!empty($store->name) ? "of {$store->name} store" : '') . " through the endpoint at $now.";
            }
            dispatch(new AlertDownloadInvoiceToGoogleChatJob($messageAlert))->onQueue(Invoice::QUEUE_ALERT_DOWNLOAD_INVOICE);
        } catch (Exception $exception) {
            Log::error('Download Invoice: ' . $exception->getMessage());
        }
    }

    public function exportInvoice($id, $type)
    {
        $invoice = Invoice::findOrFail($id);

        $fileName = '';
        $headers = [];

        switch ($type) {
            case Invoice::TYPE_DOWNLOAD_PRODUCTION:
                $fileName = 'invoices/production_invoice_' . $invoice->id . '.xlsx';
                $headers = [
                    'ResponseContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                break;

            case Invoice::TYPE_DOWNLOAD_SHIPPING:
                $fileName = 'invoices/shipping_invoice_' . $invoice->id . '.xlsx';
                $headers = [
                    'ResponseContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                break;

            case Invoice::TYPE_DOWNLOAD_INSERT:
                $fileName = 'invoices/insert_invoice_' . $invoice->id . '.xlsx';
                $headers = [
                    'ResponseContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                break;

            case Invoice::TYPE_DOWNLOAD_SURCHARGE:
                $fileName = 'invoices/surcharge_invoice_' . $invoice->id . '.xlsx';
                $headers = [
                    'ResponseContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                break;
            case Invoice::TYPE_DOWNLOAD_PROMOTION:
                $fileName = 'invoices/promotion_invoice_' . $invoice->id . '.xlsx';
                $headers = [
                    'ResponseContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                break;

            default:
                $fileName = 'invoices/invoice_' . $invoice->id . '.pdf';
                $headers = [
                    'ResponseContentType' => 'application/pdf',
                    'ResponseContentDisposition' => 'attachment; filename="' . $fileName . '"',
                ];
                $type = Invoice::TYPE_DOWNLOAD_GENERAL;
                break;
        }

        if ($fileName == '') {
            throw new Exception('Can not download file!');
        }

        if (Storage::disk('s3')->exists($fileName)) {
            $params = [
                'user_id' => config('jwt.warehouse_id') ? Auth::id() : null,
                'store_id' => $invoice->store_id,
                'ip' => $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '',
                'invoice_id' => $invoice->id,
                'start_date' => $invoice->start_at,
                'end_date' => $invoice->end_at,
                'type' => $type,
                'source' => config('jwt.warehouse_id') ? Invoice::SOURCE_INVENTORY : Invoice::SOURCE_SELLER,
                'function' => 'export'
            ];
            $this->createDownloadHistory($params);
            $presignedUrl = Storage::disk('s3')->temporaryUrl($fileName, now()->addMinutes(10), $headers);

            return ['url' => $presignedUrl];
        }

        switch ($type) {
            case 'production':
                $this->generateProductionInvoice($invoice);
                break;
            case 'shipping':
                $this->generateShippingInvoice($invoice);
                break;
            case 'insert':
                $this->generateInsertInvoice($invoice);
                break;
            case 'surcharge':
                $this->dispatchJobGenerateSurchargeInvoice($invoice);
                break;
            case 'promotion':
                $this->generatePromotionInvoice($invoice);
                break;
            default:
                $this->dispatchJobGenerateInvoice($invoice);
                break;
        }

        return false;
    }

    public function makeGenerateInvoice($request)
    {
        $type = $request->type ?? 'error';
        $invoiceIds = $request->get('invoice_ids', []);

        if (empty($invoiceIds)) {
            return [
                'status' => false,
                'message' => 'Please select invoice to generate'
            ];
        }

        $invoices = Invoice::whereIn('id', $invoiceIds)->get();

        if ($type === 'general') {
            foreach ($invoices as $invoice) {
                $this->dispatchJobGenerateInvoice($invoice);
            }
        } else {
            foreach ($invoices as $invoice) {
                dispatch(new ReGenerateInvoiceError($invoice->id))->onQueue(Invoice::QUEUE_REGENERATE_INVOICE);
            }
        }

        return [
            'status' => true,
            'message' => 'Generating invoice, please wait 30 minutes'
        ];
    }

    // Note: Mr. Tuna use it to re-generate invoice
    public function reGenerateInvoice($params)
    {
        if (isset($params['type']) && $params['type'] == 'mug') {
            if (empty($params['barcode_printed_id'])) {
                return [
                    'status' => false,
                    'message' => 'barcode_printed_id required'
                ];
            }
            ConvertMugsToPdfJob::dispatch($params['barcode_printed_id'])->onQueue(QueueJob::CONVERT_MUG_PDF);

            return [
                'status' => true,
                'message' => 'dispatch success'
            ];
        }
        if (isset($params['type']) && $params['type'] == 'poster') {
            if (empty($params['barcode_printed_id'])) {
                return [
                    'status' => false,
                    'message' => 'barcode_printed_id required'
                ];
            }

            $barcode = BarcodePrinted::where('id', $params['barcode_printed_id'])->first();

            if (!$barcode) {
                return [
                    'status' => false,
                    'message' => 'not found barcode'
                ];
            }
            $barcode->convert_status = 0;
            $barcode->save();

            return [
                'status' => true,
                'message' => 'update barcode success'
            ];
        }
        if (isset($params['type']) && $params['type'] == 'pdf_converted') {
            if (empty($params['barcode_printed_id'])) {
                return [
                    'status' => false,
                    'message' => 'barcode_printed_id required'
                ];
            }

            $barcode = PdfConverted::where('id', $params['barcode_printed_id'])->first();

            if (!$barcode) {
                return [
                    'status' => false,
                    'message' => 'not found barcode'
                ];
            }
            $barcode->convert_status = 0;
            $barcode->save();

            return [
                'status' => true,
                'message' => 'update barcode success'
            ];
        }
        if (isset($params['type']) && $params['type'] == 'deduction') {
            if (empty($params['barcode_printed_id'])) {
                return [
                    'status' => false,
                    'message' => 'barcode_printed_id required'
                ];
            }
            $barcodePrintedIds = explode(',', $params['barcode_printed_id']);
            $barcodesPrinted = BarcodePrinted::whereIn('id', $barcodePrintedIds)
                ->whereNull('pulled_at')
                ->get();
            foreach ($barcodesPrinted as $barcodePrinted) {
                handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $barcodePrinted->id);
            }

            return [
                'status' => true,
                'message' => 'dispatch deduction success'
            ];
        }

        if (isset($params['type']) && $params['type'] == 'snapshot') {
            if (empty($params['barcode_printed_id'])) {
                return [
                    'status' => false,
                    'message' => 'barcode_printed_id required'
                ];
            }
            $saleOrder = SaleOrder::where('id', $params['barcode_printed_id'])->first();
            if (!$saleOrder) {
                return [
                    'status' => false,
                    'message' => 'sale order not exist'
                ];
            }
            StoreSaleOrderPricingSnapshotJob::dispatch($saleOrder->id)
                ->onQueue(SaleOrder::JOB_STORE_PRICING_SNAPSHOT);

            return [
                'status' => true,
                'message' => 'dispatch snapshot success'
            ];
        }
        $invoice = Invoice::find($params['invoice_id']);
        if (!$invoice) {
            return [
                'status' => false,
                'message' => 'Invoice not exist'
            ];
        }

        if (!empty($params['calculate-price'])) {
            $invoice->re_calculate_price_at = now();
            $invoice->save();
            dispatch(new ReGenerateInvoiceError($invoice->id))->onQueue(Invoice::QUEUE_REGENERATE_INVOICE);

            return [
                'status' => true,
                'message' => 're-calculate price + invoice'
            ];
        }

        $invoiceId = $invoice->id;
        $commonFileName = 'invoices/invoice_' . $invoiceId;

        $fileTypes = ['pdf', 'xlsx', 'xlsx'];
        $fileNames = [
            "done delete old file pdf {$commonFileName}.pdf",
            "done delete old file {$commonFileName}_production.xlsx",
            "done delete old file shipping {$commonFileName}_shipping.xlsx"
        ];

        foreach ($fileTypes as $index => $fileType) {
            $fileName = "{$commonFileName}_" . ($index == 0 ? '' : $fileType . '_') . $invoiceId . ".$fileType";
            if (Storage::disk('s3')->exists($fileName)) {
                Storage::disk('s3')->delete($fileName);
                echo $fileNames[$index] . PHP_EOL;
            }
        }

        echo "Dispatch generate new pdf invoice#$invoiceId" . PHP_EOL;
        $this->dispatchJobGenerateInvoice($invoice);

        echo "Dispatch generate new production invoice#$invoiceId" . PHP_EOL;
        $this->dispatchJobGenerateProductionInvoice($invoice);

        echo "Dispatch generate new shipping invoice#$invoiceId" . PHP_EOL;
        $this->dispatchJobGenerateShippingInvoice($invoice);

        echo "Dispatch generate new surcharge invoice#$invoiceId" . PHP_EOL;
        $this->dispatchJobGenerateSurchargeInvoice($invoice);

        echo "Dispatch generate new promotion invoice#$invoiceId" . PHP_EOL;
        $this->dispatchJobGeneratePromotionInvoice($invoice);

        return 'done';
    }

    public function dispatchJobGenerateSurchargeInvoice($invoice)
    {
        if ($invoice->processing_surcharge_at || $invoice->store_id == Store::STORE_REDBUBBLE) {
            //store Red Bubble ko co surcharge invoice
            return;
        }
        dispatch(new MakeSurchargeInvoice($invoice->id))->onQueue(Invoice::QUEUE_MAKE_SURCHARGE_INVOICE);
    }

    public function listProductionInvoiceTemp($input)
    {
        setTimezone();

        $limit = $input['limit'] ?? self::ITEM_PER_PAGE_10;

        $data = TmpInvoiceDownload::with('store:id,code')
            ->orderByDesc('id')
            ->paginate($limit);

        foreach ($data->items() as $item) {
            $item->store_id = $item->store->code ?? null;
            $fileName = "/invoices_tmp/{$item->link_url}";
            $item->link_url = env('AWS_S3_URL') . $fileName . '?v=' . rand();
        }

        return $data;
    }
}
