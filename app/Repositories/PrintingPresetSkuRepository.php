<?php

namespace App\Repositories;

use App\Models\PrintingPresetSku;
use App\Models\PrintingPresetSkuHistory;
use App\Models\Product;
use App\Models\ProductType;
use App\Repositories\Contracts\PrintingPresetSkuRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PrintingPresetSkuRepository implements PrintingPresetSkuRepositoryInterface
{
    const LIMIT = 10;

    public function getList(Request $request): LengthAwarePaginator
    {
        $builder = PrintingPresetSku::with('user')
            ->rightJoin('product', 'product.sku', '=', 'printing_preset_sku.sku')
            ->leftJoin('product_style', 'product_style.name', '=', 'product.style');

        if ($request['no_presets']) {
            $builder->whereNull('printing_preset_sku.id');
        }

        if ($request['sku']) {
            $builder->where('product.sku', 'LIKE', '%' . $request['sku'] . '%');
        }

        $dataList = $builder->clone()
            ->select('printing_preset_sku.*', 'product.sku', DB::raw('product_style.type as product_type'))
            ->latest('printing_preset_sku.id')
            ->paginate($request['limit'] ?? self::LIMIT);

        $dataPrintArea = $builder->join('product_print_area', 'product_print_area.product_style_id', '=', 'product_style.id')
            ->whereIn('printing_preset_sku.id', collect($dataList->items())->pluck('id'))
            ->select('printing_preset_sku.id', 'product_print_area.name as side_name', 'product_print_area.print_method')
            ->get();

        if (!empty($dataPrintArea)) {
            foreach ($dataList as &$item) {
                $printArea = $dataPrintArea->where('id', $item->id)->toArray();
                $printArea = array_map(function ($item) {
                    return [
                        'side_name' => $item['side_name'],
                        'print_method' => $item['print_method'],
                    ];
                }, $printArea);
                $item->print_areas = array_values($printArea);
            }
        }

        return $dataList;
    }

    public function getDetail($id): JsonResponse
    {
        $printingPreset = PrintingPresetSku::find($id);

        if (!$printingPreset) {
            return response()->json([
                'status' => false,
                'message' => 'Printing preset sku not found!'
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'status' => true,
            'data' => $printingPreset
        ]);
    }

    private function buildData($request): array
    {
        return [
            'sku' => $request['sku'],
            'black_ink' => $request['black_ink'],
            'white_ink' => $request['white_ink'],
            'purple_ink' => $request['purple_ink'],
            'mix_ink' => $request['mix_ink'],
            'black_ink_xqc' => $request['black_ink_xqc'],
            'white_ink_xqc' => $request['white_ink_xqc'],
            'purple_ink_xqc' => $request['purple_ink_xqc'],
            'mix_ink_xqc' => $request['mix_ink_xqc'],
            'background_color' => $request['background_color'],
            'front_size' => $request['front_size'],
            'back_size' => $request['back_size'],
            'inner_neck_label_size' => $request['inner_neck_label_size'],
            'outer_neck_label_size' => $request['outer_neck_label_size'],
            'left_sleeve_size' => $request['left_sleeve_size'],
            'right_sleeve_size' => $request['right_sleeve_size'],
            'pocket_size' => $request['pocket_size'],
            'platen_front_size' => $request['platen_front_size'],
            'platen_back_size' => $request['platen_back_size'],
            'platen_left_sleeve_size' => $request['platen_left_sleeve_size'],
            'platen_right_sleeve_size' => $request['platen_right_sleeve_size'],
            'platen_inner_neck_label_size' => $request['platen_inner_neck_label_size'],
            'platen_outer_neck_label_size' => $request['platen_outer_neck_label_size'],
            'platen_pocket_size' => $request['platen_pocket_size'],
            'front_position' => $request['front_position'],
            'back_position' => $request['back_position'],
            'left_sleeve_position' => $request['left_sleeve_position'],
            'right_sleeve_position' => $request['right_sleeve_position'],
            'inner_neck_label_position' => $request['inner_neck_label_position'],
            'outer_neck_label_position' => $request['outer_neck_label_position'],
            'pocket_position' => $request['pocket_position'],
            'above_pocket_size' => $request['above_pocket_size'],
            'above_pocket_position' => $request['above_pocket_position'],
            'platen_above_pocket_size' => $request['platen_above_pocket_size']
        ];
    }

    public function create($request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $input = $request->all();
            $tableName = 'printing_preset_sku';

            foreach ($input as $key => $value) {
                if (Schema::hasColumn($tableName, $key)) {
                    $data[$key] = $value;
                }
            }

            $isSpecialType = $this->isSpecialType($request['sku']);
            $dataCreate = array_merge($data, [
                'user_id' => auth()->user()['id'],
                'admin_edit_only' => $isSpecialType,
                'created_at' => now(),
            ]);
            DB::table($tableName)->updateOrInsert([
                'sku' => $request['sku']
            ], $dataCreate);
            $printingPreset = PrintingPresetSku::where('sku', $request['sku'])->first();
            PrintingPresetSkuHistory::create([
                'user_id' => auth()->user()['id'],
                'data' => json_encode($dataCreate),
                'printing_preset_sku_id' => $printingPreset->id,
                'action' => PrintingPresetSkuHistory::ACTION_CREATE
            ]);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'status' => true,
            'data' => $printingPreset,
            'message' => 'Create printing preset sku successfully!'
        ]);
    }

    public function isSpecialType($sku)
    {
        $product = Product::where('sku', $sku)
            ->with('productStyle:name,type')->first();

        return in_array($product?->productStyle?->type, ProductType::SPECIAL_TYPE);
    }

    public function update($id, $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->all();
            $tableName = 'printing_preset_sku';
            $printingPreset = PrintingPresetSku::where('id', $id)->first();

            if (!$printingPreset) {
                return response()->json([
                    'status' => false,
                    'message' => 'Printing preset sku not found!'
                ], Response::HTTP_NOT_FOUND);
            }

            if ($printingPreset->admin_edit_only && !auth()->user()->is_admin) {
                return response()->json([
                    'status' => false,
                    'message' => 'Forbidden!'
                ], Response::HTTP_FORBIDDEN);
            }

            $dataUpdate = [];

            foreach ($data as $key => $value) {
                if (Schema::hasColumn($tableName, $key)) {
                    $dataUpdate[$key] = $value;
                }
            }

            if (!empty($dataUpdate)) {
                $dataUpdate['user_id'] = auth()->user()['id'];
                $dataUpdate['updated_at'] = date('Y-m-d H:i:s');
                DB::table($tableName)->where('id', $id)->update($dataUpdate);
                PrintingPresetSkuHistory::create([
                    'user_id' => auth()->user()['id'],
                    'data' => json_encode($data),
                    'printing_preset_sku_id' => $printingPreset->id,
                    'action' => PrintingPresetSkuHistory::ACTION_UPDATE
                ]);
                $printingPreset = PrintingPresetSku::where('id', $id)->first();
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'status' => true,
            'data' => $printingPreset,
            'message' => 'Update printing preset sku successfully!'
        ]);
    }

    public function delete($id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $printingPreset = PrintingPresetSku::where('id', $id)->first();

            if (!$printingPreset) {
                return response()->json([
                    'status' => false,
                    'message' => 'Printing preset sku not found!'
                ], Response::HTTP_NOT_FOUND);
            }

            $printingPreset->delete();
            PrintingPresetSkuHistory::create([
                'user_id' => auth()->user()['id'],
                'data' => '',
                'printing_preset_sku_id' => $printingPreset->id,
                'action' => PrintingPresetSkuHistory::ACTION_DELETE
            ]);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'status' => true,
            'message' => 'Delete printing preset sku successfully!'
        ]);
    }

    public function getPrintAreaBySku($sku): array
    {
        return PrintingPresetSku::query()
            ->rightJoin('product', 'product.sku', '=', 'printing_preset_sku.sku')
            ->leftJoin('product_style', 'product_style.name', '=', 'product.style')
            ->join('product_print_area', 'product_print_area.product_style_id', '=', 'product_style.id')
            ->select('product_print_area.name', 'product_print_area.print_method')
            ->where('product.sku', $sku)
            ->get()
            ->toArray();
    }
}
