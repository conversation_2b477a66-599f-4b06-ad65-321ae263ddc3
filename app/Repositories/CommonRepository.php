<?php


namespace App\Repositories;


use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class CommonRepository
{
    const LIMIT = 10;
    /**
     * @var mixed|string
     */
    protected $sortColumn;
    /**
     * @var mixed|string
     */
    protected $sortBy;
    /**
     * @var int|mixed
     */
    protected $limit;

    public function __construct($request = true)
    {
        if ($request) {
            $request = request();
            $this->sortColumn = !empty($request['sort_column']) ? $request['sort_column'] : 'id';
            $this->sortBy = !empty($request['sort_by']) ? $request['sort_by'] : 'DESC';
            $this->limit = !empty($request['limit']) ? $request['limit'] : self::LIMIT;
        }
    }

    protected function handleSuccess(string $message, $data = null): array
    {
        $output = [
            'status' => true,
            'output' => [
                'message' => $message
            ]
        ];
        if (!empty($data)) {
            $output['output']['data'] = $data;
        }
        return $output;
    }

    protected function handleFail(string $message = 'The given data was invalid.', array $errors = []): array
    {
        $output = [
            'status' => false,
            'output' => [
                'message' => $message
            ]
        ];
        if (!empty($errors)) {
            $output['output']['errors'] = $errors;
        }
        return $output;
    }

    /* Future will delete */
    protected function validation(array $data, array $rules)
    {
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            return \response()->json([
                'message' => 'The given data was invalid.',
                'errors' => $validator->errors()
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        return true;
    }

    protected function successResponse(string $message, $data = null): JsonResponse
    {
        $output = [
            'message' => $message,
        ];
        if ($data) {
            $output['data'] = $data;
        }
        return response()->json($output);
    }

    protected function errorResponse(string $message, int $status = Response::HTTP_NOT_FOUND, string $field = null): JsonResponse
    {
        $output = [
            'message' => $message,
        ];
        if ($field) {
            $output['errors'][$field][] = $message;
        }
        return response()->json($output, $status);
    }
    /* Future will delete */
}
