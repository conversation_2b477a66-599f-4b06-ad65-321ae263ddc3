<?php

namespace App\Repositories;

use App\Models\Location;
use App\Models\PurchaseOrder;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class ForecastRepository extends CommonRepository
{
    public function getList($input, $isPaginate = true)
    {
        $from = Carbon::createFromFormat('Y-m-d', $input['start_date']);
        $to = Carbon::createFromFormat('Y-m-d', $input['end_date']);
        $diff = $to->diffInDays($from);
        $yearNow = now()->startOfQuarter()->subQuarter()->year;
        $quarterNow = now()->startOfQuarter()->subQuarter()->quarter;

        $startDate = Carbon::createFromFormat('Y-m-d', $input['start_date'])->subDays($diff)->toDateString();
        $endDate = $from->toDateString();

        $startDateLastYear = Carbon::createFromFormat('Y-m-d', $input['start_date'])->subWeeks(52)->toDateString();
        $endDateLastYear = Carbon::createFromFormat('Y-m-d', $input['end_date'])->subWeeks(52)->toDateString();

        $startDateForeCastLastYear = Carbon::createFromFormat('Y-m-d', $input['start_date'])->subDays($diff)->subWeeks(52)->toDateString();
        $endDateForeCastLastYear = $from->subWeeks(52)->toDateString();

        $warehouseId = $input['warehouse_id'];
        $page = !empty($input['page']) ? $input['page'] : 1;
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;

        $offset = ($page - 1) * $limit;
        $query_count_head = 'SELECT COUNT(*) AS total';
        $query_select_head = "SELECT b.*,
            product_rank_histories.year,
            product_rank_histories.quarter,
            product_rank_histories.total_revenue,
            product_rank_histories.total_unit_sold,
            product_rank_histories.rank,
            IF(b.volume = 0 OR b.volume IS NULL, 0, ROUND(b.in_stock/b.volume, 2)) AS months_remaining, IF(b.last_year = 0 OR b.current = 0 OR b.growth_rate = 'NA', IF (b.current = 0, IF(gtin_case > b.in_stock, gtin_case - b.in_stock , 0), IF(b.current > b.in_stock, b.current - b.in_stock , 0)),
                    IF(b.last_year * ((b.growth_rate + 100)/100) - b.in_stock < 0, 0, FLOOR(b.last_year * ((b.growth_rate + 100)/100) - b.in_stock))) AS need_buy, COALESCE(product_rank_histories.rank, '-') AS `product_rank`
                    FROM (SELECT  t1.id AS product_id,
                   brand.name as brand_name,
                   t1.gtin_case,
                   t1.sku,
                   t1.style,
                   t1.color,
                   t1.size,
                   e.name as employee,
                   bo.backorder,
                   bo.allocated,
                   IFNULL (t3.volume, 0) AS volume,
                    IFNULL (p.incoming_stock, 0) AS incoming,
                    IFNULL ((p.quantity+p.incoming_stock), 0) AS in_stock,
                    IFNULL(t1.quantity, 0) AS current,
                    IFNULL(t2.past_forecast_quantity, 0) as past_forecast_quantity,
                    IFNULL(t2.past_quantity, 0) as last_year,
                     IFNULL(FLOOR(CASE
                      WHEN t2.past_forecast_quantity > 0 THEN (((IFNULL(t1.quantity, 0) - t2.past_forecast_quantity) / t2.past_forecast_quantity) *100)
                    ELSE 0
                    END ), 'NA') AS growth_rate";

        ///Todo : sold của (30 or 60 or 90 ) ngày trước

        $binding1 = [$startDate, $endDate, $warehouseId];
        $query_select_t1 = '
        FROM (SELECT product.id,
                product.gtin_case,
                product.sku,
                product.style,
                product.color,
                product.size,
                product.brand_id,
                t.warehouse_id,
                t.quantity
            FROM product
            LEFT JOIN (SELECT product_id, warehouse_id, SUM(forecast_sale_order.quantity) AS quantity FROM forecast_sale_order
WHERE forecast_sale_order.order_date >= ? AND forecast_sale_order.order_date <= ? AND forecast_sale_order.warehouse_id = ?  GROUP BY product_id) t
on product.id = t.product_id WHERE product.parent_id > 0 ';

        $query_select_t2 = '(SELECT product_id AS id,
        SUM(IF(order_date <= ?, quantity, 0)) AS past_forecast_quantity,
        SUM(IF(order_date >= ? , quantity, 0)) AS past_quantity
        FROM forecast_sale_order
        WHERE order_date >= ? AND order_date <= ? AND warehouse_id = ? ';

        $binding2 = [$endDateForeCastLastYear, $startDateLastYear, $startDateForeCastLastYear, $endDateLastYear, $warehouseId];

        $last30Days = Carbon::now()->subDays(30)->toDateString();

        $binding3 = [$last30Days, $warehouseId];

        $query_select_t3 = '(SELECT product_id AS id,
        SUM(quantity) AS volume
        FROM forecast_sale_order
        WHERE order_date >= ? AND warehouse_id = ? ';

        $group_by_query = '
            LEFT JOIN product_quantity AS p ON (t1.id = p.product_id AND p.warehouse_id = ? )
            LEFT JOIN backorder  AS bo ON t1.sku = bo.product_sku
            LEFT JOIN employee  AS e ON bo.employee_id  = e.id
            LEFT JOIN brand ON t1.brand_id = brand.id
            WHERE ((bo.hide_at + INTERVAL 72 HOUR < NOW()) OR bo.hide_at IS NULL)';

        $bindingGroup = [$warehouseId];

        $query_select_count = '
            FROM (SELECT product.id, product.sku
               FROM forecast_sale_order
               LEFT JOIN product ON product.id = forecast_sale_order.product_id
               LEFT JOIN product_rank_histories
                        ON (product_rank_histories.product_id = forecast_sale_order.product_id
                            AND product_rank_histories.year = ?
                            AND product_rank_histories.quarter = ?)
               WHERE forecast_sale_order.order_date >= ?
                 AND forecast_sale_order.order_date <= ?
                 AND forecast_sale_order.warehouse_id = ? ';

        $bindingSelectCount = [$yearNow, $quarterNow, $startDate, $endDate, $warehouseId];

        if (!empty($input['sku'])) {
            $sku = $input['sku'];
            $query_select_t1 .= ' AND product.sku = ?';
            $query_select_count .= ' AND product.sku = ? ';
            $binding1[] = $sku;
            $bindingSelectCount[] = $sku;
        }
        if (!empty($input['style'])) {
            $style = $input['style'];
            $query_select_t1 .= ' AND product.style = ?';
            $query_select_count .= ' AND product.style = ?';
            $binding1[] = $style;
            $bindingSelectCount[] = $style;
        }
        if (!empty($input['size'])) {
            $size = $input['size'];
            $query_select_t1 .= ' AND product.size = ? ';
            $query_select_count .= ' AND product.size = ? ';
            $binding1[] = $size;
            $bindingSelectCount[] = $size;
        }
        if (!empty($input['color'])) {
            $color = $input['color'];
            $query_select_t1 .= ' AND product.color = ?';
            $query_select_count .= ' AND product.color = ?';
            $binding1[] = $color;
            $bindingSelectCount[] = $color;
        }
        if (!empty($input['brand_id'])) {
            $brand_id = $input['brand_id'];
            $query_select_t1 .= ' AND product.brand_id = ? ';
            $query_select_count .= ' AND product.brand_id = ? ';
            $binding1[] = $brand_id;
            $bindingSelectCount[] = $brand_id;
        }

        $query = $query_select_head
            . $query_select_t1
            . ' GROUP BY product_id) AS t1 LEFT JOIN ' . $query_select_t2 . ' GROUP BY product_id) AS t2 ON t1.id=t2.id'
            . ' LEFT JOIN ' . $query_select_t3 . ' GROUP BY product_id) AS t3 ON t1.id=t3.id'
            . $group_by_query;
        $query .= ' ) as b';
        $query .= ' LEFT JOIN product_rank_histories
                        ON (product_rank_histories.product_id = b.product_id
                            AND product_rank_histories.year = ?
                            AND product_rank_histories.quarter = ?)';
        $bindingQuery = array_merge($binding1, $binding2, $binding3, $bindingGroup);
        $bindingQuery[] = $yearNow;
        $bindingQuery[] = $quarterNow;
        if (!empty($input['rank'])) {
            if ($input['rank'] == '-') {
                $query .= ' Where product_rank_histories.id is Null';
                $query_select_count .= ' AND product_rank_histories.id is Null';
            } else {
                $query .= ' Where product_rank_histories.rank = ? ';
                $query_select_count .= ' AND product_rank_histories.rank = ? ';
                $bindingQuery[] = $input['rank'];
                $bindingSelectCount[] = $input['rank'];
            }
        }

        $group_by_query_for_count = ' GROUP BY product.id) t LEFT JOIN backorder  AS bo ON t.sku = bo.product_sku
            WHERE (bo.hide_at + INTERVAL 72 HOUR < NOW()) OR bo.hide_at IS NULL';

        $query_count = $query_count_head . $query_select_count . $group_by_query_for_count;

        if (!empty($input['sort_column'])) {
            $sortColumn = in_array($input['sort_column'], ['need_buy', 'last_year', 'growth_rate', 'volume', 'months_remaining']) ? $input['sort_column'] : 'in_stock';
            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'desc';
            $query .= " order by $sortColumn $sortBy";
        }

        if ($isPaginate) {
            $query .= " LIMIT $limit OFFSET $offset";
        }

        if (isset($input['get_total'])) {
            $total = DB::select($query_count, $bindingSelectCount);
            $total = $total[0]->total;

            return ['total' => $total];
        }
        $data = DB::select($query, $bindingQuery);
        $productIds = array_column($data, 'product_id');

        $inventory = $this->fetchInventoryRackPullingIncoming($productIds, $warehouseId, $isPaginate);
        $lastPoData = $this->fetchLastPurchaseOrderByProducts($productIds, $warehouseId, $isPaginate);
        $columns = $input['columns'] ?? '';
        $listColumns = !empty($columns) ? explode(',', $columns) : [];
        $data = array_map(function ($item) use ($inventory, $lastPoData, $listColumns) {
            $item->rack = 0;
            $item->pulling = 0;
            if (isset($inventory[$item->product_id])) {
                $inventoryItem = $inventory[$item->product_id];
                $item->rack = $inventoryItem->rack;
                $item->pulling = $inventoryItem->pulling;
            }
            if (isset($lastPoData[$item->product_id])) {
                $lastPo = $lastPoData[$item->product_id];
                $item->last_po = date('m/d/Y', strtotime($lastPo->last_po));
            }

            $item->total = $item->rack + $item->pulling + $item->incoming;
            if (count($listColumns) > 0) {
                $itemOutput = new \stdClass();
                foreach ($listColumns as $column) {
                    $itemOutput->{$column} = $item->{$column} ?? null;
                }

                return $itemOutput;
            }

            return $item;
        }, $data);
        if ($isPaginate) {
            return new Paginator($data, $limit, $page);
        }

        return $data;
    }

    private function fetchInventoryRackPullingIncoming($productIds, $warehouseId, $isPaginate)
    {
        if ($isPaginate) {
            return Location::query()
                ->join('location_product as lp', 'location.id', '=', 'lp.location_id')
                ->select('location.id', 'lp.product_id',
                    DB::raw('
                SUM(CASE WHEN location.type = 0 THEN lp.quantity ELSE 0 END) AS rack,
                SUM(CASE WHEN location.type = 1 THEN lp.quantity ELSE 0 END) +
                SUM(CASE WHEN location.type = 3 THEN lp.quantity ELSE 0 END) AS pulling
            '))
                ->whereIn('lp.product_id', $productIds)
                ->where('location.is_deleted', 0)
                ->where('warehouse_id', $warehouseId)
                ->groupBy('product_id')
                ->get()
                ->keyBy('product_id');
        }
        $inventory = collect();
        $productIdsChunk = array_chunk(array_unique($productIds), 500);
        foreach ($productIdsChunk as $productIdsItem) {
            $data = Location::query()
                ->join('location_product as lp', 'location.id', '=', 'lp.location_id')
                ->select('location.id', 'lp.product_id',
                    DB::raw('
                SUM(CASE WHEN location.type = 0 THEN lp.quantity ELSE 0 END) AS rack,
                SUM(CASE WHEN location.type = 1 THEN lp.quantity ELSE 0 END) +
                SUM(CASE WHEN location.type = 3 THEN lp.quantity ELSE 0 END) AS pulling
            '),
                )
                ->whereIn('lp.product_id', $productIdsItem)
                ->where('location.is_deleted', 0)
                ->where('warehouse_id', $warehouseId)
                ->groupBy('product_id')
                ->get()
                ->keyBy('product_id');

            $inventory = $inventory->union($data);
        }

        return $inventory;
    }

    private function fetchLastPurchaseOrderByProducts($productIds, $warehouseId, $isPaginate)
    {
        if ($isPaginate) {
            return PurchaseOrder::query()
                ->join('purchase_order_item', 'purchase_order.id', '=', 'purchase_order_item.po_id')
                ->where('warehouse_id', $warehouseId)
                ->whereIn('product_id', $productIds)
                ->groupBy('product_id')
                ->select('product_id', DB::raw('MAX(order_date) as last_po'))
                ->get()
                ->keyBy('product_id');
        }
        $inventory = collect();
        $productIdsChunk = array_chunk(array_unique($productIds), 500);
        foreach ($productIdsChunk as $productIdsItem) {
            $data = PurchaseOrder::query()
                ->join('purchase_order_item', 'purchase_order.id', '=', 'purchase_order_item.po_id')
                ->where('warehouse_id', $warehouseId)
                ->whereIn('product_id', $productIdsItem)
                ->groupBy('product_id')
                ->select('product_id', DB::raw('MAX(order_date) as last_po'))
                ->get()
                ->keyBy('product_id');
            $inventory = $inventory->union($data);
        }

        return $inventory;
    }
}
