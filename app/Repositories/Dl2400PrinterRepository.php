<?php

namespace App\Repositories;

use App\Models\Dl2400Printer;
use App\Models\Dl2400PrintLog;

class Dl2400PrinterRepository extends CommonRepository
{
    public function getDevice($deviceId)
    {
        $device = Dl2400Printer::where('device_id', $deviceId)->first();
        $isGenerateName = true;
        $try = 1;

        if (empty($device)) {
            while ($try < 50 && $isGenerateName) {
                $name = 'DL2400-' . str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
                $exist = Dl2400Printer::where('name', $name)->first();
                $isGenerateName = !empty($exist);
                $try++;
            }

            if (empty($name)) {
                abort(422);
            }

            $device = Dl2400Printer::create([
                'device_id' => $deviceId,
                'name' => $name,
            ]);
        }

        return $device;
    }

    public function updateDevice($deviceId, $dataRequest)
    {
        $device = Dl2400Printer::where('device_id', $deviceId)->firstOrFail();
        $device->name = $dataRequest['name'];
        $device->save();

        return $device;
    }

    public function pushLog($deviceId, $dataLog)
    {
        $device = Dl2400Printer::where('device_id', $deviceId)->firstOrFail();
        $dataLog['dl2400_printers_id'] = $device->id;
        $dataLog['label_id'] = resolve(PrintingRepository::class)->removeLabelSide($dataLog['label_id']);

        return Dl2400PrintLog::create($dataLog);
    }
}
