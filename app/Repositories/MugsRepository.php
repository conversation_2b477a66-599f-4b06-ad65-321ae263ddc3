<?php

namespace App\Repositories;

use App\Events\ConvertToAiFileNotification;
use App\Http\Resources\ConvertMugsErrorResource;
use App\Http\Service\ConvertService;
use App\Jobs\ConvertMugsToPdfJob;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\ProductStyle;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MugsRepository extends CommonRepository
{
    protected ConvertService $convertService;

    public function __construct(ConvertService $convertService)
    {
        $this->convertService = $convertService;
    }

    public function countMug($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $total = 0;
        $result = [];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->select(
                DB::raw('COUNT(*) as count'),
                'sale_order_item.product_id',
                'product.style',
                'product.size',
                'product.color',
            )
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_MUGS)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', false)
            ->where('sale_order.is_eps', false);

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $queryData->where('sale_order_item_barcode.store_id', $input['store_id']);
            } else {
                $queryData->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }
        }

        $queryData = $queryData->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->groupBy('sale_order_item.product_id')
            ->get();

        if ($queryData->count()) {
            foreach ($queryData as $item) {
                $barcode_printed = BarcodePrinted::findLastCreatedByProductId($item->product_id, $warehouse_id, $input);

                $total += $item->count;
                $result[] = [
                    'product_id' => $item->product_id,
                    'count' => $item->count,
                    'color' => $item->color,
                    'size' => $item->size,
                    'last_created_at' => optional($barcode_printed)->created_at ? Carbon::parse($barcode_printed->created_at)->format('m/d/Y H:i') : null,
                    'style_name' => $item->style
                ];
            }
        }

        return [
            'total' => $total,
            'data' => $result
        ];
    }

    public function countPendingPriorityStore($input)
    {
        $warehouse_id = $input['warehouse_id'];

        return DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('store', 'sale_order_item_barcode.store_id', '=', 'store.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->select(
                DB::raw('COUNT(*) as count, store.id, store.code'),
            )
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_MUGS)
            ->whereIn('sale_order_item_barcode.store_id', $input['priorityStores'])
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->groupBy('sale_order_item_barcode.store_id')
            ->get();
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return BarcodePrinted::listPdfMugs($warehouse_id, $limit);
    }

    public function historyPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return BarcodePrinted::historyPdf($warehouse_id, $limit, $label_id);
    }

    public function convertPdf($input)
    {
        try {
            DB::beginTransaction();
            $limit = $input['quantity'];
            $employee_id = $input['employee_id'];
            $store_id = $input['store_id'] ?? null;
            $warehouse_id = config('jwt.warehouse_id');
            $result = [];
            $st = SaleOrderItemBarcode::getListBarcodeConverted($warehouse_id, BarcodePrinted::METHOD_MUGS, $input);
            $barcodes = $st->limit($limit)
                ->selectRaw('sale_order_item_barcode.id as barcode_id, product.id as product_id, product.style as product_sku')
                ->get();
            $listBarcodes = $barcodes;
            $barcodes = $barcodes
                ->groupBy('product_id')
                ->map(function ($item) {
                    return [
                        'product_id' => $item[0]['product_id'],
                        'sku' => $item[0]['product_sku'],
                        'count' => count($item),
                    ];
                });
            $hasBarcodeConvert = false;
            $listConvert = [];

            foreach ($barcodes as $item) {
                $data = [
                    'style_sku' => $item['sku'],
                    'employee_id' => $employee_id,
                    'warehouse_id' => $warehouse_id,
                    'store_id' => $store_id,
                    'user_id' => Auth::id(),
                    'convert_percent' => $limit,
                    'quantity_input' => $item['count'],
                    'print_method' => BarcodePrinted::METHOD_MUGS,
                    'product_id' => $item['product_id'],
                    'created_at' => Carbon::now(),
                    'print_status' => BarcodePrinted::INACTIVE,
                    'is_tiktok' => $input['is_tiktok'] ?? null,
                    'is_fba' => $input['is_fba'] ?? null,
                    'is_reroute' => $input['is_reroute'] ?? null,
                    'is_manual' => $input['is_manual'] ?? null,
                    'is_reprint' => $input['is_reprint'] ?? null,
                    'is_xqc' => $input['is_xqc'] ?? null,
                    'is_eps' => $input['is_eps'] ?? null,
                    'is_bulk_order' => $input['is_bulk_order'] ?? null,
                ];
                $barcodePrinted = BarcodePrinted::create($data);
                $ids = [];

                if (($item['count']) > 0) {
                    $listConvert[] = $barcodePrinted->id;
                    $hasBarcodeConvert = true;
                }

                foreach ($listBarcodes as $barcode) {
                    if ($barcode->product_id == $item['product_id']) {
                        $ids[] = $barcode->barcode_id;
                    }
                }

                $this->updateOrCreateBarcodePrintedTime($data);
                SaleOrderItemBarcode::whereIn('id', $ids)->update([
                    'barcode_printed_id' => $barcodePrinted->id
                ]);
                $result[] = $barcodePrinted->load([
                    'employeeConvert:id,name',
                    'product:id,color,size'
                ]);
            }

            if ($hasBarcodeConvert) {
                DB::commit();

                foreach ($listConvert as $barcodePrintedId) {
                    ConvertMugsToPdfJob::dispatch($barcodePrintedId)
                        ->onQueue(QueueJob::CONVERT_MUG_PDF)
                        ->delay(Carbon::now()->addSeconds(5));
                }

                return $result;
            } else {
                DB::rollBack();
                throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
            }
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function downloadPdf($input)
    {
        DB::beginTransaction();
        try {
            $barcode_printed_id = $input['barcode_printed_id'];
            $barcode_printed = BarcodePrinted::find($barcode_printed_id);
            if (!$barcode_printed) {
                throw new Exception('Pdf not found', Response::HTTP_NOT_FOUND);
            }
            if ($input['is_history']) {
                DB::commit();

                return [];
            }

            //log timeline
            if ($barcode_printed->print_status != BarcodePrinted::ACTIVE) {
                $barcodeRepository = new BarcodeRepository();
                $barcodeRepository->logTimeLineOrderByBarcodePrintedId($barcode_printed_id, $barcode_printed->employee_id);
            }
            $barcode_printed->print_status = BarcodePrinted::ACTIVE;
            $barcode_printed->save();
            SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed_id)->update([
                'printed_at' => now(),
                'employee_print_id' => $barcode_printed->employee_id
            ]);
            // lấy các order_id từ bảng SaleOrderItemBarcode để order_printed_status vs order_printed_at trong sale_order
            $saleOrderItems = SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed_id)
            ->groupBy('order_id')
            ->get(['order_id']);

            // cập nhật từng order_id trong batch
            $printingRepository = new PrintingRepository();
            foreach ($saleOrderItems as $saleOrderItem) {
                $printingRepository->updateOrderPrinted($saleOrderItem->order_id);
            }
            DB::commit();
            handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $barcode_printed_id);

            return $barcode_printed;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getConvertMugError($input)
    {
        $limit = $input['limit'] ?? 25;
        $data = SaleOrderItemBarcode::getListConvertErrors($input, $limit);

        return new ConvertMugsErrorResource($data);
    }

    public function downloadMug($barcode_id)
    {
        $saleOrderItemBarcode = SaleOrderItemBarcode::find($barcode_id);

        if (!$saleOrderItemBarcode) {
            throw new Exception('Could not find Barcode Item', Response::HTTP_NOT_FOUND);
        }

        $saleOrderItemBarcode->convert_pdf_status = SaleOrderItemBarcode::CONVERT_PDF_SUCCESS;
        $saleOrderItemBarcode->save();

        return $saleOrderItemBarcode;
    }

    public function countMugs($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->join('store', 'sale_order_item.store_id', '=', 'store.id')
            ->select(
                DB::raw('COUNT(*) as count'),
            )
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_MUGS);

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $queryData->where('sale_order_item_barcode.store_id', $input['store_id']);
            } else {
                $queryData->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }
        }

        return $queryData;
    }

    public function countPendingBulkOrder($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_xqc', '<>', 1)
            ->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function excludeBulkOrder(&$qr)
    {
        return $qr->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->where('sale_order.is_xqc', '=', 1);
                });
        });
    }

    public function countPendingTiktok($input)
    {
        $query = $this->countMugs($input)
            ->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingFBA($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingReroute($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNotNull('sale_order_item_barcode.employee_reroute_id');
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingManualProcess($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingReprint($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNotNull('sale_order_item_barcode.label_root_id');
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingXQC($input)
    {
        $query = $this->countMugs($input);
        $barcodes = $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', true)
            ->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingEPS($input)
    {
        $query = $this->countMugs($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', false)
            ->where('sale_order.is_eps', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingStyle($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $total = 0;
        $result = [];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->select(
                DB::raw('COUNT(*) as count'),
                'sale_order_item.product_id',
                'product.style',
                'product.size',
                'product.color',
            )
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_MUGS)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', false)
            ->where('sale_order.is_eps', false);
        $this->excludeBulkOrder($queryData);

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $queryData->where('sale_order_item_barcode.store_id', $input['store_id']);
            } else {
                $queryData->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }
        }

        $queryData = $queryData->groupBy('sale_order_item.product_id')
            ->get();

        if ($queryData->count()) {
            $productIds = array_column($queryData->toArray(), 'product_id');
            $barcodesPrinted = BarcodePrinted::where('warehouse_id', $warehouse_id)
                ->whereIn('product_id', $productIds)
                ->where('print_method', BarcodePrinted::METHOD_MUGS)
                ->whereNull('is_tiktok')
                ->whereNull('is_fba')
                ->whereNull('is_reroute')
                ->whereNull('is_manual')
                ->whereNull('is_reprint')
                ->whereNull('is_xqc')
                ->whereNull('is_eps')
                ->whereNull('is_bulk_order')
                ->selectRaw('MAX(created_at) AS created_at, product_id')
                ->groupBy('product_id')
                ->get();

            foreach ($queryData as $item) {
                $total += $item->count;
                $barcodePrinted = $barcodesPrinted->first(function ($barcodePrintedItem) use ($item) {
                    return $item->product_id == $barcodePrintedItem->product_id;
                });
                $result[] = [
                    'product_id' => $item->product_id,
                    'count' => $item->count,
                    'color' => $item->color,
                    'size' => $item->size,
                    'last_created_at' => optional($barcodePrinted)->created_at ? Carbon::parse($barcodePrinted->created_at)->format('m/d/Y H:i:s') : null,
                    'style_name' => $item->style
                ];
            }
        }

        return [
            'total' => $total,
            'data' => array_values(collect($result)->sortBy('last_created_at')->toArray()),
        ];
    }

    public function updateOrCreateBarcodePrintedTime($data)
    {
        $barcodePrintedTime = BarcodePrintedTime::where('store_id', $data['store_id'])
            ->where('is_xqc', $data['is_xqc'])
            ->where('is_eps', $data['is_eps'])
            ->where('is_reprint', $data['is_reprint'])
            ->where('style_sku', $data['style_sku'])
            ->where('is_manual', $data['is_manual'])
            ->where('is_reroute', $data['is_reroute'])
            ->where('is_fba', $data['is_fba'])
            ->where('is_tiktok', $data['is_tiktok'] ?? null)
            ->where('is_bulk_order', $data['is_bulk_order'] ?? null)
            ->where('warehouse_id', $data['warehouse_id'])
            ->where('print_method', $data['print_method'])
            ->first();

        if ($barcodePrintedTime) {
            DB::table('barcode_printed_time')->where('id', $barcodePrintedTime->id)
                ->update(['printed_at' => date('Y-m-d H:i:s')]);
        } else {
            $list = [
                'store_id',
                'warehouse_id',
                'is_xqc',
                'style_sku',
                'is_reprint',
                'is_manual',
                'is_reroute',
                'is_fba',
                'is_eps',
                'color_sku',
                'is_tiktok',
                'is_bulk_order',
                'print_method',
            ];
            $insert['printed_at'] = date('Y-m-d H:i:s');

            foreach ($list as $item) {
                $insert[$item] = $data[$item] ?? null;
            }

            BarcodePrintedTime::create($insert);
        }
    }

    public function convertToAi($barcodeId)
    {
        $barcodePrinted = BarcodePrinted::where('print_status', BarcodePrinted::PRINTED_STATUS)
            ->where('id', $barcodeId)
            ->first();
        if (!$barcodePrinted) {
            throw new Exception('Must download the file before converting!', Response::HTTP_BAD_REQUEST);
        }
        broadcast(new ConvertToAiFileNotification((array) json_decode($barcodePrinted->positions)));

        return [
            'data' => $barcodePrinted->positions
        ];
    }
}
