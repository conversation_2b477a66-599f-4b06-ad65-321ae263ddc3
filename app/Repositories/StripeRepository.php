<?php

namespace App\Repositories;

use App\Models\PaymentFee;
use App\Models\Store;
use App\Models\WalletTopup;
use App\Models\WalletTopupLog;
use App\Models\WalletTransaction;
use Exception;
use Illuminate\Support\Facades\Log;
use Stripe\Customer;
use Stripe\FinancialConnections\Session;
use Stripe\PaymentIntent;
use Stripe\Stripe;

class StripeRepository
{
    public function __construct()
    {
        //
    }

    public function initialize()
    {
        Stripe::setApiKey(config('cashier.secret'));

        return $this;
    }

    public function createPayment($topup, $params)
    {
        $options = $this->makeOptionPaymentIntent($topup, $params);

        Log::channel('stripe')->info('Stripe create intent payload: ' . json_encode($options));

        $amountCentUnit = round($topup->amount_requested * 100, 2); // convert to cents
        $response = $topup->store->createPayment($amountCentUnit, $options);
        $paymentData = $response->toArray();

        Log::channel('stripe')->info('Stripe create intent response: ' . json_encode($paymentData));
        WalletTopupLog::create([
            'wallet_topup_id' => $topup->id,
            'data' => json_encode($paymentData),
            'action' => WalletTopupLog::REQUEST_ACTION,
        ]);

        $this->addPaymentMethod([
            'payment_method_id' => $topup->payment_method_id,
            'is_default' => true,
        ]);

        $topup->update([
            'reference_number' => $paymentData['id'],
            'amount_received' => convertCentToDollar($paymentData['amount']),
        ]);

        return $paymentData;
    }

    public function charge($topup, array $options = [], $params = [])
    {
        $options = array_merge(
            $options,
            $this->makeOptionPaymentIntent($topup, $params),
            [
                'confirm' => true,
                'confirmation_method' => 'automatic',
            ],
        );

        $paymentData = $topup->store->createPayment(
            round($topup->amount_requested * 100, 2),
            $options,
        )->toArray();

        $this->addPaymentMethod([
            'payment_method_id' => $topup->payment_method_id,
            'is_default' => true,
        ], $topup->store);

        $topup->update([
            'payment_id' => $paymentData['id'],
            'amount_received' => convertCentToDollar($paymentData['amount']),

        ]);

        return $paymentData;
    }

    public function confirmPayment($topup, $params)
    {
        Log::channel('stripe')->info("Stripe confirm intent {$topup->signature} payload: " . json_encode($params));

        $response = PaymentIntent::retrieve($topup->payment_id)->confirm([
            'payment_method' => $topup->payment_method_id,
            'mandate_data' => [
                'customer_acceptance' => [
                    'type' => 'online',
                    'online' => [
                        'ip_address' => $params['ip_address'],
                        'user_agent' => $params['user_agent'],
                    ],
                ],
            ],
        ]);

        $paymentData = $response->toArray();

        Log::channel('stripe')->info("Stripe confirm intent {$topup->signature} response: " . json_encode($paymentData));

        $topup->update([
            'external_status' => $paymentData['status'],
        ]);

        return $paymentData;
    }

    public function verifyPayment($topup, $params)
    {
        Log::channel('stripe')->info("Stripe verify intent {$topup->signature} payload: " . json_encode($params));

        $response = PaymentIntent::retrieve($topup->payment_id)->verifyMicrodeposits([
            'amounts' => $params['amounts'],
        ]);
        $paymentData = $response->toArray();

        Log::channel('stripe')->info("Stripe verify intent {$topup->signature} response: " . json_encode($paymentData));

        $topup->update([
            'external_status' => $paymentData['status'],
        ]);

        return $paymentData;
    }

    public function cancelPayment($topup)
    {
        Log::channel('stripe')->info('Stripe cancel intent: ' . $topup->signature);

        $response = PaymentIntent::retrieve($topup->payment_id)->cancel();
        $paymentData = $response->toArray();

        $topup->update([
            'external_status' => $paymentData['status'],
        ]);

        return $paymentData;
    }

    public function getPaymentMethods(Store $store = null)
    {
        $store = $store ?? auth()->user();
        $store = $this->checkCustomerExists($store);
        $stripeCustomer = Customer::retrieve($store->stripe_id)->toArray();
        $defaultPaymentMethodId = $stripeCustomer['invoice_settings']['default_payment_method'] ?? null;

        $allCards = $this->allPaymentMethods($store->stripe_id, WalletTopup::CARD_METHOD);
        $allUsBankAccounts = $this->allPaymentMethods($store->stripe_id, WalletTopup::BANK_TRANSFER_METHOD);

        $allPaymentMethod = collect(array_merge($allCards, $allUsBankAccounts))
            ->map(function ($paymentMethod) use ($defaultPaymentMethodId) {
                $paymentMethod['is_default'] = $paymentMethod['id'] === $defaultPaymentMethodId;

                return $paymentMethod;
            });

        return $allPaymentMethod;
    }

    public function getCustomer(Store $store = null)
    {
        $store = $store ?? auth()->user();
        $store = $this->checkCustomerExists($store);

        return Customer::retrieve($store->stripe_id);
    }

    public function retrievePaymentMethod($paymentMethodId, Store $store = null)
    {
        return $this->getCustomer($store)->retrievePaymentMethod($paymentMethodId);
    }

    public function checkCustomerExists(Store $store = null)
    {
        unset($store->member_id, $store->user_login_id);
        if (!$store->hasStripeId()) {
            $store->createAsStripeCustomer([
                'email' => $store->email,
                'name' => $store->name,
                'metadata' => [
                    'domain' => config('app.name'),
                ],
            ]);
            $store->refresh();
        }

        return $store;
    }

    public function addPaymentMethod($params, Store $store = null)
    {
        $store = $store ?? auth()->user();
        $store = $this->checkCustomerExists($store);
        $paymentMethod = $store->addPaymentMethod($params['payment_method_id']);

        if (isset($params['is_default']) && $params['is_default']) {
            $store->updateDefaultPaymentMethod($params['payment_method_id']);
        }

        return $paymentMethod;
    }

    public function removePaymentMethod($paymentMethodId)
    {
        $store = $store ?? auth()->user();
        $store = $this->checkCustomerExists($store);

        return $store->deletePaymentMethod($paymentMethodId);
    }

    public function connection(Store $store = null)
    {
        $store = $store ?? auth()->user();
        $store = $this->checkCustomerExists($store);

        $session = Session::create([
            'account_holder' => [
                'type' => 'customer',
                'customer' => $store->stripe_id,
            ],
            'permissions' => [
                'balances',
                'transactions',
                'payment_method',
                'ownership',
            ],
        ]);

        return $session;
    }

    public function getPaymentFee()
    {
        return PaymentFee::where('payment_gateway', WalletTopup::PAYMENT_GATEWAY_STRIPE)->get();
    }

    public function calculatorTopupAmount($amount, $paymentMethod)
    {
        $settingFee = PaymentFee::where('payment_gateway', WalletTopup::PAYMENT_GATEWAY_STRIPE)
            ->where('payment_method', $paymentMethod)
            ->where('type', WalletTransaction::TRANSACTION_TYPE_TOPUP)
            ->first();

        if (empty($settingFee)) {
            throw new Exception('Fee setting not found!');
        }

        $feeAmount = 0;

        if ($settingFee->fee_percent > 0) {
            $feeAmount = $amount * $settingFee->fee_percent / 100;
        }

        if ($settingFee->max_amount > 0 && $feeAmount > $settingFee->max_amount) {
            $feeAmount = $settingFee->max_amount;
        }

        return round($amount - $feeAmount, 2);
    }

    private function makeOptionPaymentIntent($topup, $params)
    {
        $options = [
            'confirm' => true,
            'setup_future_usage' => 'off_session',
            'payment_method_types' => [$topup->payment_method],
            'metadata' => [
                'signature' => $topup->signature,
                'requested_amount' => $topup->amount_requested,
            ],
        ];

        if ($topup->payment_method === WalletTopup::CARD_METHOD) {
            $options = array_merge(
                $options,
                [
                    'confirmation_method' => 'automatic',
                    'return_url' => config('app.frontend_url'),
                ],
            );
        }

        if ($topup->payment_method === WalletTopup::BANK_TRANSFER_METHOD) {
            $options = array_merge(
                $options,
                [
                    'mandate_data' => [
                        'customer_acceptance' => [
                            'type' => 'online',
                            'online' => [
                                'ip_address' => isset($params['ip_address']) ? $params['ip_address'] : '0.0.0.0',
                                'user_agent' => isset($params['user_agent']) ? $params['user_agent'] : 'Unknown',
                            ],
                        ],
                    ],
                ],
            );
        }

        if (isset($topup->payment_method_id)) {
            $options['payment_method'] = $topup->payment_method_id;
        }

        return $options;
    }

    private function allPaymentMethods($customerId, $type)
    {
        $response = Customer::allPaymentMethods(
            $customerId,
            [
                'limit' => 100,
                'type' => $type
            ],
        )->toArray();

        return $response['data'] ?? [];
    }
}
