<?php

namespace App\Repositories;

use App\Http\Service\GetOrderService;
use App\Models\Product;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrderInsert;
use App\Models\SaleOrderInsertSku;
use App\Models\SaleOrderPricingSnapshot;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\SurchargeFee;

class SaleOrderPricingRepository extends CommonRepository
{
    protected $getOrderService;

    public function __construct(GetOrderService $getOrderService)
    {
        $this->getOrderService = $getOrderService;
        parent::__construct();
    }

    public function saveOrderPricingSnapshot($saleOrder): void
    {
        $addressShip = $saleOrder->addressSaleOrder->first() ?? null;
        $country = strtoupper(optional($addressShip)->country ?? '');
        $destination = in_array($country, StoreShipment::DOMESTIC_SHIPPING)
            ? StoreShipment::DOMESTIC
            : StoreShipment::INTERNATIONAL;

        $snapshot = [
            'print' => [],
            'shipping' => [],
            'surcharge' => [],
            'insert_pricing' => [],
            'insert_shipping' => [],
        ];

        foreach ($saleOrder->items as $item) {
            $printSides = array_unique(str_split(
                $this->getOrderService->checkPrintSidesOption(json_decode($item->options)),
            ));

            $printSideNames = ProductPrintSide::whereIn('code_wip', $printSides)->pluck('name')->toArray();
            $productStyle = ProductStyle::where('sku', $item->product_style_sku)->first();
            if (!$productStyle) {
                continue;
            }

            $printAreaIds = ProductPrintArea::where('product_style_id', $productStyle->id)
                ->whereIn('name', $printSideNames)
                ->pluck('id')
                ->toArray();

            $printAreaIds[] = 0;

            $storeProductRecords = StoreProduct::where('store_id', $saleOrder->store_id)
                ->where('product_id', $item->product_id)
                ->whereIn('product_print_area_id', $printAreaIds)
                ->get();

            foreach ($storeProductRecords as $record) {
                $snapshot['print'][] = [
                    'product_print_area_id' => $record->product_print_area_id,
                    'product_id' => $item->product_id,
                    'price' => (float) $record->price,
                    'print_price' => (float) $record->print_price,
                    'handling_fee' => (float) $record->handling_fee,
                    'print_surcharge' => (float) $record->print_surcharge,
                ];
            }

            $productType = optional($item->getTypeProduct)->type;
            $shipping = $this->getShipping($saleOrder->store_id, $productType, $destination);
            foreach ($shipping as $s) {
                $snapshot['shipping'][] = [
                    'product_type' => $s->product_type,
                    'product_style' => $s->product_style,
                    'destination' => $s->destination,
                    'service_type' => $s->service_type,
                    'price' => (float) $s->price,
                    'addition_price' => (float) $s->addition_price,
                    'size' => $s->size,
                    'status' => $s->status,
                ];
            }
        }

        // surcharge
        $surcharges = SurchargeFee::query()
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->select([
                'surcharge_service.id',
                'surcharge_service.name',
                'surcharge_service.api_value',
                'surcharge_service.per',
                'store_surcharge.value',
                'surcharge_service.product_type',
                'store_surcharge.id as store_surcharge_id'
            ])
            ->where('store_id', $saleOrder->store_id)
            ->get();

        foreach ($surcharges as $surcharge) {
            $snapshot['surcharge'][] = $surcharge->toArray();
        }

        // insert
        $saleOrdersInsert = SaleOrderInsert::where('order_id', $saleOrder->id)->get();
        foreach ($saleOrdersInsert as $insert) {
            $mappingSku = SaleOrderInsertSku::where('size', $insert->size)
                ->where('type', $insert->type)
                ->first();

            if (!$mappingSku) {
                continue;
            }

            $product = Product::with('productStyle')->where('sku', $mappingSku->sku)->first();
            if (!$product || !$product->productStyle) {
                continue;
            }

            $base = StoreProduct::where([
                'store_id' => $saleOrder->store_id,
                'product_id' => $product->id,
                'product_print_area_id' => 0,
            ])->first();

            if ($base) {
                $snapshot['insert_pricing'][] = [
                    'product_print_area_id' => $base->product_print_area_id,
                    'product_id' => $base->product_id,
                    'price' => (float) $base->price,
                    'print_price' => (float) $base->print_price,
                    'handling_fee' => (float) $base->handling_fee,
                    'print_surcharge' => (float) $base->print_surcharge,
                ];
            }

            $printSide = ProductPrintSide::where('code_wip', 'F')->first();
            if ($printSide) {
                $printArea = ProductPrintArea::where([
                    'name' => $printSide->name,
                    'product_style_id' => $product->productStyle->id,
                ])->first();

                if ($printArea) {
                    $insertProduct = StoreProduct::where([
                        'store_id' => $saleOrder->store_id,
                        'product_id' => $product->id,
                        'product_print_area_id' => $printArea->id,
                    ])->first();

                    if ($insertProduct) {
                        $snapshot['insert_pricing'][] = [
                            'product_print_area_id' => $insertProduct->product_print_area_id,
                            'product_id' => $insertProduct->product_id,
                            'price' => (float) $insertProduct->price,
                            'print_price' => (float) $insertProduct->print_price,
                            'handling_fee' => (float) $insertProduct->handling_fee,
                            'print_surcharge' => (float) $insertProduct->print_surcharge,
                        ];
                    }
                }
            }

            $insertShipping = $this->getShipping($saleOrder->store_id, $product->productStyle->type, $destination);
            foreach ($insertShipping as $record) {
                $snapshot['insert_shipping'][] = [
                    'product_type' => $record->product_type,
                    'product_style' => $record->product_style,
                    'destination' => $record->destination,
                    'service_type' => $record->service_type,
                    'price' => (float) $record->price,
                    'addition_price' => (float) $record->addition_price,
                    'size' => $record->size,
                    'status' => $record->status,
                ];
            }
        }

        // save
        SaleOrderPricingSnapshot::updateOrCreate(
            ['order_id' => $saleOrder->id],
            ['snapshot' => $snapshot],
        );
    }

    private function getShipping($storeId, $productType, $destination)
    {
        return StoreShipment::where([
            ['store_id', '=', $storeId],
            ['status', '=', StoreShipment::STATUS_ACTIVE],
            ['product_type', '=', $productType],
            ['destination', '=', $destination],
        ])->get();
    }
}
