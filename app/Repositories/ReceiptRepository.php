<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderRefund;
use App\Models\Store;
use App\Models\SurchargeService;
use App\Models\Tag;
use App\Models\WalletReceipt;
use App\Models\WalletTopup;
use App\Models\WalletTransaction;
use Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\PaymentMethod;
use Stripe\Stripe;

class ReceiptRepository extends CommonRepository
{
    public function getListReceipts($params)
    {
        $query = WalletReceipt::with(['transaction', 'order' => function ($qr) {
            $qr->select('encode_id', 'created_at', 'id');
        }])->where('store_id', Auth::id());

        if (!empty($params['keyword'])) {
            $query->where(function ($qr) use ($params) {
                $qr->where('receipt_number', 'like', '%' . $params['keyword'] . '%')
                 ->orWhereHas('order', function ($q) use ($params) {
                     $q->where('encode_id', 'like', '%' . $params['keyword'] . '%');
                 });
            });
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $startDate = Carbon::parse($params['start_date'])->startOfDay();
            $endDate = Carbon::parse($params['end_date'])->endOfDay();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        if (!empty($params['type'])) {
            $query->whereHas('transaction', function ($q) use ($params) {
                $q->where('type', $params['type']);
            });
        } else {
            $query->whereHas('transaction', function ($q) {
                $q->where('type', '<>', WalletTransaction::TRANSACTION_TYPE_TOPUP);
            });
        }

        $limit = $params['limit'] ?? self::LIMIT;
        $result = $query->orderBy('id', 'desc')->paginate($limit);
        $result->getCollection()->map(function ($item) {
            if ($item->relationLoaded('order')) {
                if ($item->order) {
                    $item->order->makeHidden(['id']); // Ẩn 'id'
                } else {
                    $item->order = null;
                }
            }

            return $item;
        });

        return $result;
    }

    public function downloadReceipt($id)
    {
        setTimezone();
        try {
            $receipt = WalletReceipt::with('transaction')->findOrFail($id);
            $store = Store::with('walletBillingAddress')->findOrFail($receipt->store_id);

            if ($receipt->transaction->type == WalletTransaction::TRANSACTION_TYPE_TOPUP) {
                return $this->getTopupData($receipt, $store);
            }

            $receiptData = json_decode($receipt->data, true);

            $orderData = SaleOrder::findOrFail($receipt->order_id);
            $orderData->items = $receiptData['items'] ?? [];
            $orderData->surcharge_fee = $receiptData['surcharge'] ?? [];
            $orderData->shipping_calculate = $receiptData['shipping'] ?? $orderData->shipping_calculate;
            $orderData->shipping_method = $receiptData['shipping_method'] ?? $orderData->shipping_method;
            $orderRefund = null;

            if ($receipt->transaction->type == WalletTransaction::TRANSACTION_TYPE_REFUND) {
                $orderRefund = SaleOrderRefund::findOrFail($receipt->transaction->object_id);
                $orderData->refund_type = $orderRefund->type;
                $orderData->total_refund = $orderRefund->total_amount;
            }

            if ($receipt->transaction->type == WalletTransaction::TRANSACTION_TYPE_TOPUP) {
                $topup = WalletTopup::findOrFail($receipt->transaction->object_id);
                $orderData->amount_requested = $topup->amount_requested;
            }

            if ($receipt->transaction->type == WalletTransaction::TRANSACTION_TYPE_ADJUSTMENT) {
                $orderData->old_amount = $receiptData['old_amount'] ?? '0.00';
                $orderData->new_amount = $receiptData['new_amount'] ?? '0.00';
            }

            $orderData->transaction_type = $receipt->transaction->type;
            $orderData->billing_address = $store->walletBillingAddress;
            $orderData->receipt_number = $receipt->receipt_number;
            $orderData->issue_date = Carbon::parse($receipt->created_at)->format('M j, Y');
            $orderData->paid_date = Carbon::parse($orderData->created_at)->format('F j, Y');
            $orderData->total_paid_title = $this->formatTitleAmount($receipt->transaction->type, $receipt->transaction->amount, $receipt->created_at);
            $orderData->receipt_note = $this->getReceiptNote($receipt, $orderData);
            $orderData->transaction_note = $orderRefund ? $orderRefund->reason : $receipt->transaction->note ?? '';
            $orderData->transaction_amount = $receipt->transaction->amount;
            $orderData->blank_price = $receiptData['blank_price'] ?? 0;
            $orderData->print_price = $receiptData['print_price'] ?? 0;
            $orderData->total_price = $receiptData['total'] ?? 0;
            $orderData->net_payment = $orderData->total_price - ($orderData->total_refund ?? 0);
            $orderData->receipt_name = match (true) {
                $orderData->transaction_type == WalletTransaction::TRANSACTION_TYPE_REFUND => 'Refund receipt',
                $orderData->transaction_type == WalletTransaction::TRANSACTION_TYPE_ADJUSTMENT => 'Adjustment receipt',
                $orderData->transaction_type == WalletTransaction::TRANSACTION_TYPE_TOPUP => 'Top up receipt',
                default => 'Receipt',
            };
            $orderData->is_label_order = $orderData->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER || ($orderData->order_type == SaleOrder::ORDER_TYPE_NORMAL && (in_array(Tag::LABEL_TAG_ID, explode(',', $orderData->tag)))) ? true : false;

            return $orderData;
        } catch (\Throwable $th) {
            Log::error('ReceiptRepository->downloadReceipt: ', [$th]);
            throw $th;
        }
    }

    public function formatTitleAmount($transactionType, $amount, $date)
    {
        $amount = number_format($amount, 2);
        switch ($transactionType) {
            case WalletTransaction::TRANSACTION_TYPE_PAYMENT:
            case WalletTransaction::TRANSACTION_TYPE_ADJUSTMENT:
                $title = '$' . $amount . ' paid on ' . Carbon::parse($date)->format('F j, Y');
                break;
            case WalletTransaction::TRANSACTION_TYPE_REFUND:
                $title = '$' . $amount . ' refunded on ' . Carbon::parse($date)->format('F j, Y');
                break;
            case WalletTransaction::TRANSACTION_TYPE_TOPUP:
                $title = '$' . $amount . ' credited on ' . Carbon::parse($date)->format('F j, Y');
                break;
            default:
                $title = '';
                break;
        }

        return $title;
    }

    public function getReceiptNote($receipt, $orderData)
    {
        if ($orderData->order_status == SaleOrder::STATUS_LATE_CANCELLED) {
            return 'Order in production cancelled';
        }

        if ($orderData->order_status == SaleOrder::CANCELLED) {
            return 'Order cancelled';
        }

        if ($orderData->order_status == SaleOrder::REJECTED) {
            return 'Order rejected due to ' . ucwords(str_replace('_', ' ', $orderData->rejected_reason));
        }

        return null;
    }

    public function getOrderData($id)
    {
        $order = SaleOrder::with([
            'items' => function ($q) {
                $q->with(['getTypeProduct', 'product', 'productColor', 'productSize']);
            }
        ])->where('id', $id)->firstOrFail();
        $totalAmount = $order->order_total ?? 0;
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $surchargeResults = [];
        $totalHandlingFee = $order->items->sum(function ($item) {
            return $item->handling_fee * $item->quantity;
        });

        if ($totalHandlingFee) {
            $surchargeResults[] = ['name' => SurchargeService::TYPE_HANDLING, 'total' => round($totalHandlingFee, 2)];
        }

        $saleOrdersSurcharge = $surchargeFeeRepository->fetchSaleOrdersSurcharge($id);

        foreach ($saleOrdersSurcharge as $surcharge) {
            $surchargeResults[] = ['name' => $surcharge->name, 'total' => $surcharge->total];
            $totalAmount += $surcharge->total;
        }

        $saleOrderItemsSurcharge = $surchargeFeeRepository->fetchSaleOrdersItemsSurcharge($id);

        foreach ($saleOrderItemsSurcharge as $surcharge) {
            $surchargeResults[] = ['name' => $surcharge->name, 'total' => $surcharge->total];
            $totalAmount += $surcharge->total;
        }

        $insertValue = SaleOrderInsertCalculatePrice::where('order_id', $id)
            ->whereNotNull('calculated_at')
            ->sum('amount_paid');
        if ($insertValue) {
            $surchargeResults[] = ['name' => 'Insert', 'total' => $insertValue];
            $totalAmount += $insertValue;
        }
        $peakSurcharge = $surchargeFeeRepository->fetchPeakSurChargeByOrder($id);
        if ($peakSurcharge) {
            $surchargeResults[] = ['name' => 'Peak Shipping', 'total' => $peakSurcharge->value];
            $totalAmount += $peakSurcharge->value;
        }

        $order->surcharge_fee = $surchargeResults;
        $order->total_amount = $totalAmount;

        return $order;
    }

    public function getTopupData($receipt, $store)
    {
        $topup = WalletTopup::findOrFail($receipt->transaction->object_id);
        $topup->transaction_type = WalletTransaction::TRANSACTION_TYPE_TOPUP;
        $topup->billing_address = $store->walletBillingAddress;
        $topup->receipt_name = 'Top up receipt';
        $topup->transaction_note = $receipt->transaction->note ?? '';
        $topup->receipt_number = $receipt->receipt_number;
        $topup->issue_date = Carbon::parse($receipt->created_at)->format('M j, Y');
        $topup->payment_method = $this->getPaymentMethod($topup->payment_method_id ?? null);
        $topup->total_paid_title = $this->formatTitleAmount($receipt->transaction->type, $receipt->transaction->amount, $receipt->created_at);

        return $topup;
    }

    public function getPaymentMethod($paymentMethodId)
    {
        if (empty($paymentMethodId)) {
            return null;
        }

        setProviderConfig(WalletTopup::PAYMENT_GATEWAY_STRIPE);
        Stripe::setApiKey(config('cashier.secret'));
        $paymentMethod = PaymentMethod::retrieve($paymentMethodId);

        $type = $paymentMethod->type;

        if ($type === WalletTopup::CARD_METHOD) {
            $summary = ucfirst($paymentMethod->card->brand) . '****' . $paymentMethod->card->last4;
        } elseif ($type === WalletTopup::BANK_TRANSFER_METHOD) {
            $summary = 'Bank Account****' . $paymentMethod->us_bank_account->last4;
        } else {
            $summary = 'Unknown Payment Method';
        }

        return $summary;
    }
}
