<?php

namespace App\Repositories;

use App\Mail\SendMailPrinting;
use App\Models\BarcodeScanHistory;
use App\Models\Client;
use App\Models\EmbroideryColorChart;
use App\Models\EmbroideryTask;
use App\Models\Employee;
use App\Models\IntegrateCallbackLog;
use App\Models\PrinterLog;
use App\Models\PrinterWipLog;
use App\Models\PrintingPreset;
use App\Models\PrintingPresetSku;
use App\Models\PrintLogError;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductPrintSide;
use App\Models\ProductStyleIccProfile;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderSla;
use App\Models\Setting;
use App\Models\Store;
use App\Models\StoreIntegrate;
use App\Models\StoreProductStyleResize;
use App\Models\User;
use App\Repositories\Contracts\PrintingRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class PrintingRepository implements PrintingRepositoryInterface
{
    public array $inkColor = [
        0 => 'white_ink',
        1 => 'black_ink',
        2 => 'mix_ink',
        'custom' => 'purple_ink',
    ];

    public array $inkColorXqc = [
        0 => 'white_ink_xqc',
        1 => 'black_ink_xqc',
        2 => 'mix_ink_xqc',
        'custom' => 'purple_ink_xqc'
    ];

    public array $platenSizeToIndex = [
        '16x21' => 0,
        '16x18' => 1,
        '14x16' => 2,
        '10x12' => 3,
        '7x8' => 4,
    ];

    public float $inchToMm = 25.4 * 10;

    public array $inkColorName = [
        0 => 'White Ink Only',
        1 => 'Black Ink Only',
        2 => 'Color Ink'
    ];

    protected $iccImageConvert = false;

    public float $pixelToMmRatio = 0.084667 * 10; // 300 dpi

    /**
     * @param $label 020922-SJ-S-002726-1-0  --> 020922-SJ-S-002726-1
     */
    public function removeLabelSide($label)
    {
        $label = trim($label);
        $i = explode('-', $label);

        if (count($i) == 6) {
            return substr($label, 0, -2);
        }

        return $label;
    }

    /**
     * @param $label 020922-SJ-S-002726-1-0 last - 0/1 is front/back
     */
    public function getLabelPrintSide($label)
    {
        $i = explode('-', $label);
        $side = ProductPrintSide::pluck('code')->toArray();

        if (!isset($i[5])) {
            return 0; // không scan print side sẽ mặc định là Front
        }

        if (in_array($i[5], $side)) {
            return $i[5];
        }

        return -1;
    }

    public function getBarcodeLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)->first();
    }

    public function getImageBySkuSideNew($barcode, $side, $employeeId = 0): array
    {
        $output = new \stdClass();

        if ($barcode->is_deleted == 1) {
            return [
                'status' => false,
                'message' => 'This order has been cancelled'
            ];
        }

        if (!empty($barcode->order_id)) {
            $saleOrder = SaleOrder::where('id', $barcode->order_id)->first();

            if (empty($saleOrder)) {
                return [
                    'status' => false,
                    'message' => 'Order not found'
                ];
            }

            if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
                return [
                    'status' => false,
                    'message' => 'Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)
                ];
            }
        } else {
            $printLogError = new PrintLogError;
            $printLogError->sku = $barcode->label_id;
            $printLogError->save();

            return [
                'status' => false,
                'message' => 'Sku not found'
            ];
        }

        $employee = Employee::query()->where('id', $employeeId)->first();

        if ($employee == false) {
            return [
                'status' => false,
                'message' => 'Employee not found'
            ];
        }

        if ($employee->warehouse_id != $saleOrder->warehouse_id) {
            return [
                'status' => false,
                'message' => 'This label belongs to another warehouse'
            ];
        }

        $store = Store::where('id', $saleOrder->store_id)->first();

        if (empty($store)) {
            return [
                'status' => false,
                'message' => 'Store not found'
            ];
        }

        $sku = $barcode->sku;
        $image = SaleOrderItemImage::query()
            ->with('imageHash', 'product.productStyle')
            ->where('sku', $sku)
            ->where('print_side', $side . '')
            ->where('order_id', $saleOrder->id)
            ->whereNotNull('color_new')->first();

        if (!$image) {
            if ($side == 0) {
                $side = 1;
                $image = SaleOrderItemImage::query()
                    ->with('imageHash', 'product.productStyle')
                    ->where('sku', $sku)
                    ->where('print_side', $side . '')
                    ->where('is_double_side', 0)
                    ->where('order_id', $saleOrder->id)
                    ->whereNotNull('color_new')->first();
            }
            if (!$image) {
                return [
                    'status' => false,
                    'message' => 'Image not found!'
                ];
            }
        }

        $output->image = $image;
        $presetSku = PrintingPresetSku::query()->where('sku', $image->product_sku)->first();

        if (!$presetSku) {
            saleOrderHistory(
                null,
                $employee->id,
                $barcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                'Printing failed due to a missing printing preset',
            );

            return [
                'status' => false,
                'message' => 'Preset not found!'
            ];
        }

        if ($image->manual_color != null) {
            $image->color_new = $image->manual_color;
        }

        if ($saleOrder->is_xqc) {
            $ink = $this->inkColorXqc[$image->color_new] ?? '';

            if ($image->is_purple == SaleOrderItemImage::IS_PURPLE) {
                $ink = $this->inkColorXqc['custom'] ?? '';
            }
        } else {
            $ink = $this->inkColor[$image->color_new] ?? '';

            if ($image->is_purple == SaleOrderItemImage::IS_PURPLE) {
                $ink = $this->inkColor['custom'] ?? '';
            }
        }

        if ($ink == '') {
            return [
                'status' => false,
                'message' => 'Preset not found'
            ];
        }

        $print_side = ProductPrintSide::findByCode($side);

        // update employee scan
        $barcode->update([
            'employee_scan_id' => $employeeId,
            'scanned_at' => date('Y-m-d H:i:s')
        ]);

        $imageWidth = $image->image_width * $this->pixelToMmRatio;
        $imageHeight = $image->image_height * $this->pixelToMmRatio;
        $platen_size = 'platen_' . $print_side->code_name . '_size';

        if (empty($presetSku->{$platen_size})) {
            saleOrderHistory(
                null,
                $employee->id,
                $barcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                'Printing failed due to a missing printing preset',
            );

            return [
                'status' => false,
                'message' => $platen_size . ' is missing'
            ];
        }

        $platenSizeTmp = explode('x', $presetSku->{$platen_size});
        $platenWidth = $platenSizeTmp[0] * $this->inchToMm;
        $platenHeight = $platenSizeTmp[1] * $this->inchToMm;
        $presetSku->platen_size = $presetSku->{$platen_size};

        //doan nay chi kiem tra xe store co off resize hay khong (hoac store off hoac off theo style)
        $isResize = true;

        if (!empty($store->id) && !empty($image->product->productStyle->sku)) {
            if (!$store->is_resize) {
                $isResize = false;
            } else {
                $objTurnOff = StoreProductStyleResize::where('store_id', $store->id)
                    ->where('product_style_sku', $image->product->productStyle->sku)
                    ->where('is_active', StoreProductStyleResize::ACTIVED)
                    ->first();

                if (!empty($objTurnOff)) {
                    $isResize = false;
                }
            }
        }

        $customPaddingLeft = 0;

        //doan nay xu ly neu co custom platen, va mac dinh neu co custom_platen la phai khong resize
        if (!empty($image->custom_platen)) {
            $customPlatenSizeTmp = explode('x', $image->custom_platen);
            $customPlatenWidth = $customPlatenSizeTmp[0] * $this->inchToMm;
            $customPlatenHeight = $customPlatenSizeTmp[1] * $this->inchToMm;
            $customPaddingLeft = ($customPlatenWidth - $platenWidth) / 2;
            $platenWidth = $customPlatenWidth;
            $platenHeight = $customPlatenHeight;
            $presetSku->platen_size = $image->custom_platen;
            $isResize = false;
        }

        $presetName = $presetSku->{$ink};

        if (!$presetName) {
            return [
                'status' => false,
                'message' => 'Preset name not defined'
            ];
        }

        $isImageAllowConvertICC = $store->isAllowConvertICC() ?? false;

        //change custom preset for redbubble client
        if (in_array($store->client_id, Client::REDBUBBLE_CLIENT_ID)) {
            $presetName = PrintingPreset::convertPresetForRedBubble($presetName);
        }

        if ($isImageAllowConvertICC && $image->icc_converted_at != null) {
            $this->iccImageConvert = true;
            $presetName = $this->getIccPresetName($image, $presetName);
        }

        $preset = PrintingPreset::query()->where('name', $presetName)->first();

        if (empty($preset)) {
            return [
                'status' => false,
                'message' => 'Printing preset not found'
            ];
        }

        if (!isset($this->platenSizeToIndex[$presetSku->platen_size])) {
            return [
                'status' => false,
                'message' => 'Platen size not allowed'
            ];
        }

        // change platen by product sku
        $xmlContent = str_replace(
            '<byPlatenSize>2</byPlatenSize>',
            '<byPlatenSize>' . $this->platenSizeToIndex[$presetSku->platen_size] . '</byPlatenSize>',
            $preset->data,
        );
        $xmlContent = str_replace('byGtx4PauseSpan', 'byPauseSpan', $xmlContent);
        $saleOrderItem = SaleOrderItem::query()->where('id', $image->order_item_id)->first();
        $productTypeId = $saleOrderItem->productStyle->productType->id ?? 0;
        $productStyleId = $saleOrderItem->productStyle->id ?? 0;
        /** @var ProductMaterialThicknessRepository $productMaterialThicknessRepo */
        $productMaterialThicknessRepo = resolve(ProductMaterialThicknessRepository::class);
        $materialThickness = $productMaterialThicknessRepo->getMaterialThicknessCode($productTypeId, $side, $productStyleId);

        if ($materialThickness !== null) {
            $materialThicknessXmlPreset = "<bPlatenHeight>true</bPlatenHeight><byPlatenHeightPos>$materialThickness</byPlatenHeightPos></GTOPTION>";
            $xmlContent = str_replace(['</GTOPTION>', '</gtoption>'], $materialThicknessXmlPreset, $xmlContent);
        }

        $preset->data = trim($xmlContent);
        $output->preset = $preset;
        $size = $print_side->code_name . '_size';
        $position = $print_side->code_name . '_position';

        if (empty($presetSku->{$size})) {
            $presetPrintAreaWidth = $platenWidth;
            $presetPrintAreaHeight = $platenHeight;
            $printAreaTop = 0;
            $printAreaLeft = 0;
        } else {
            $presetPrintAreaTmp = explode('x', $presetSku->{$size});
            // convert xxx,x => xxxx
            $presetPrintAreaWidth = $presetPrintAreaTmp[0] * $this->inchToMm;
            $presetPrintAreaHeight = $presetPrintAreaTmp[1] * $this->inchToMm;

            if (empty($presetSku->{$position})) {
                $printAreaTop = 0;
                $printAreaLeft = ($platenWidth - $presetPrintAreaWidth) / 2;
            } else {
                $positionTmp = explode('x', $presetSku->{$position});
                $printAreaTop = $positionTmp[0] * $this->inchToMm;
                $printAreaLeft = $positionTmp[1] * $this->inchToMm + $customPaddingLeft;
            }
        }

        $product = Product::query()->where('sku', $saleOrderItem->product_sku)->first();
        $file_name = "$sku-$side.arx4";
        $image_name = "$sku-$side.png";
        $preset_name = "$sku-$side.xml";

        if ($isResize && $presetSku->{$size} == '14x10') {
            $trimInfo = json_decode($image->pretreat_info);
            $h = isset($trimInfo->top) && isset($trimInfo->height) ? $trimInfo->top + $trimInfo->height : 1;
            $bottomPointHeight = $h * $image->image_height * $this->pixelToMmRatio;
            $adjust = $this->adjustHoodie([
                'image_width' => $imageWidth,
                'image_height' => $imageHeight,
                'preset_print_area_width' => $presetPrintAreaWidth,
                'preset_print_area_height' => $presetPrintAreaHeight,
                'print_area_top' => $printAreaTop,
                'print_area_left' => $printAreaLeft,
                'bottom_point_height' => $bottomPointHeight,
            ]);
        } elseif ((!$isResize || $store->id == Store::PRINTIFY_API_ID) && round($imageWidth) > $platenWidth) {
            $trimInfo = json_decode($image->pretreat_info) ?? (object) [
                'top' => 0,
                'left' => 0,
                'width' => 1,
                'height' => 1,
            ];
            $adjust = $this->adjustImageOverSize([
                'image_width' => $imageWidth,
                'image_height' => $imageHeight,
                'preset_print_area_width' => $presetPrintAreaWidth,
                'preset_print_area_height' => $presetPrintAreaHeight,
                'platen_width' => $platenWidth,
                'platen_height' => $platenHeight,
                'print_area_top' => $printAreaTop,
                'print_area_left' => $printAreaLeft,
            ], $trimInfo);
            $adjust['custom_artwork'] = env('AWS_S3_URL') . "/artwork/{$image->order_date}/trim/{$image_name}";
        } else {
            $adjust = $this->adjust($isResize, [
                'image_width' => $imageWidth,
                'image_height' => $imageHeight,
                'preset_print_area_width' => $presetPrintAreaWidth,
                'preset_print_area_height' => $presetPrintAreaHeight,
                'platen_width' => $platenWidth,
                'platen_height' => $platenHeight,
                'print_area_top' => $printAreaTop,
                'print_area_left' => $printAreaLeft,
            ]);
        }

        $dataOptions = json_decode($saleOrderItem->options);
        $saleOrderItem->front = null;
        $saleOrderItem->back = null;
        $preview_by_side = null;

        foreach ($dataOptions as $item) {
            if (str_contains($item->name, 'PreviewFiles.' . str_replace(' ', '', ucwords(str_replace('_', ' ', $print_side->code_name))))) {
                $preview_by_side = $item->value;
            }
        }

        $thumb = env('STORAGE_URL', '') . "/250/{$image->order_date}/{$image_name}";
        $thumb_s3 = env('AWS_S3_URL') . "/thumb/250/{$image->order_date}/{$image_name}";
        $artwork_s3 = $image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_S3_URL') . "/artwork/{$image->order_date}/{$image_name}" : null;
        $preview = $preview_by_side;
        $preview = $preview == '' ? $thumb_s3 : $preview;
        $platenSize = explode('x', $presetSku->platen_size);

        $printSidesSleeve = SaleOrderItemImage::query()
            ->whereIn('print_side', [3, 4])
            ->where('order_item_id', $image->order_item_id)
            ->first();

        $existsSleeveAndICC = !empty($printSidesSleeve) && !empty($image->icc_converted_at);
        $allowStoreUseICC = $isImageAllowConvertICC && !empty($image->icc_converted_at);

        if ($existsSleeveAndICC || $allowStoreUseICC) {
            $iccColor = $saleOrderItem->productColor->icc_color ?? '';
            $style = $saleOrderItem->productStyle->name ?? '';
            $output->icc_used = "{$style}_{$iccColor}";
            $artwork_s3_original = $artwork_s3;
            $artwork_s3 = env('AWS_S3_URL') . "/artwork/icc/{$image->order_date}/{$image_name}";
        }

        if (!isset($adjust['offset_top'])) {
            $offset_left = sprintf('%04d', $adjust['offset_left']) . '0000';
        } else {
            $offset_left = sprintf('%04d', $adjust['offset_left']) . sprintf('%04d', $adjust['offset_top']);
        }

        $arx4 = [
            'is_label' => true, //
            'is_resize' => $isResize,
            'image_id' => $image->id,
            'ink_color' => $this->inkColorName[$image->color_new],
            'sku' => $sku,
            'label_id' => $barcode->label_id,
            'preview' => $preview,
            'thumb' => $thumb,
            'adjust' => $adjust,
            'platen_size' => $presetSku->platen_size,
            'platen_width' => $platenSize[0] ?? 0,
            'platen_height' => $platenSize[1] ?? 0,
            'platen_width_haft' => $platenSize[0] / 2,
            'platen_height_haft' => $platenSize[1] / 2,
            'print_size' => $presetSku->{$size},
            'print_position' => $presetSku->{$position},
            'image_url' => !empty($adjust['custom_artwork']) ? $adjust['custom_artwork'] : (is_null($artwork_s3) ? $image->image_url : $artwork_s3),
            'image_name' => $image_name,
            'image_size' => sprintf('%04d', $adjust['image_width']) . '' . sprintf('%04d', $adjust['image_height']),
            'offset_left' => $offset_left,
            'preset_name' => $preset_name,
            'preset' => $preset->name,
            'preset_data' => $preset->data,
            'file_name' => $file_name,
            'side' => $print_side->name,
            'thumb_s3' => !empty($adjust['custom_artwork']) ? $adjust['custom_artwork'] : $thumb_s3,
            'artwork_s3' => $artwork_s3,
            'artwork_s3_original' => $artwork_s3_original ?? null,
            'hash' => is_null($image->imageHash) ? null : $image->imageHash->hash_md5
        ];

        $output->product = $product;
        $output->arx4 = $arx4;
        setTimezone();
        $slaOrder = SaleOrderSla::where('order_id', $saleOrderItem->order_id)->first();

        if ($slaOrder && in_array($saleOrder->order_status, [SaleOrder::NEW_ORDER, SaleOrder::STATUS_IN_PRODUCTION])) {
            $slaOrder->sla_expired_at_utc = $slaOrder->expired_at ? shiftTimezoneToUTC($slaOrder->expired_at) : null;
        } else {
            $slaOrder = null;
        }

        $output->slaOrder = $slaOrder;

        BarcodeScanHistory::query()
            ->create([
                'employee_id' => $employee->id,
                'label_id' => $barcode->label_id,
                'side_code' => $side,
                'scanned_at' => BarcodeScanHistory::SWIFTPOD_PRINTING,
                'artwork_type' => !empty($output->icc_used) ? $output->icc_used : BarcodeScanHistory::ARTWORK_TYPE_NORMAL,
            ]);

        return ['status' => true, 'data' => $output];
    }

    public function adjustImageDTF($imageWidth, $imageHeight, $presetPrintAreaWidth, $presetPrintAreaHeight)
    {
        $wRatio = $presetPrintAreaWidth / $imageWidth;
        $hRatio = $presetPrintAreaHeight / $imageHeight;
        $ratio = min($wRatio, $hRatio);
        $imageWidthNew = round($imageWidth * $ratio, 1);
        $imageHeightNew = round($imageHeight * $ratio, 1);

        return [
            'image_width' => $imageWidthNew,
            'image_height' => $imageHeightNew
        ];
    }

    public function adjustHoodie($preset)
    {
        $ratio = min($preset['preset_print_area_width'] / $preset['image_width'], $preset['preset_print_area_height'] / $preset['bottom_point_height']);

        $imageWidthNew = $preset['image_width'] * $ratio;
        $imageHeightNew = $preset['image_height'] * $ratio;
        $offsetLeft = ($preset['preset_print_area_width'] - $imageWidthNew) / 2 + $preset['print_area_left'];
        $offsetTop = $preset['print_area_top'];

        return [
            'offset_left' => round($offsetLeft),
            'offset_top' => round($offsetTop),
            'image_width' => round($imageWidthNew),
            'image_height' => round($imageHeightNew),
            'note' => 'tối ưu cho áo hoodie',
        ];
    }

    public function adjustImageOverSize($preset, $trimInfo)
    {
        $note = 'kích thước ảnh lơn hơn platen';
        // kiem tra neu vung tri doi xung van lon hon platen thi resize anh goc sao cho vung trim doi xung nam tron ven trong platen
        $minLeftRight = min($trimInfo->left, 1 - $trimInfo->left - $trimInfo->width);
        $widthTrimSymmetrical = round($preset['image_width'] * (1 - 2 * $minLeftRight));
        $heightTrimSymmetrical = round($preset['image_height'] * ($trimInfo->top + $trimInfo->height));

        if ($widthTrimSymmetrical > $preset['platen_width'] || $heightTrimSymmetrical > $preset['platen_height']) {
            $ratio = min($preset['platen_width'] / $widthTrimSymmetrical, $preset['platen_height'] / $heightTrimSymmetrical);
            $preset['image_width'] = $preset['image_width'] * $ratio;
            $preset['image_height'] = $preset['image_height'] * $ratio;
            $note = 'vùng trim đối xứng vẫn lớn hơn platen nên resize về platen';
        }

        $imageWidthNew = $preset['image_width'] * ($trimInfo->width ?? 1);
        $imageHeightNew = $preset['image_height'] * ($trimInfo->height ?? 1);
        $offsetLeft = $preset['image_width'] * ($trimInfo->left ?? 0) - ($preset['image_width'] - $preset['platen_width']) / 2;
        $offsetTop = $preset['image_height'] * ($trimInfo->top ?? 0);

        return [
            'offset_left' => round($offsetLeft),
            'offset_top' => round($offsetTop),
            'image_width' => round($imageWidthNew),
            'image_height' => round($imageHeightNew),
            'note' => $note,
        ];
    }

    public function adjust($isResize, $preset)
    {
        $ratio = min($preset['preset_print_area_width'] / $preset['image_width'], $preset['preset_print_area_height'] / $preset['image_height']);

        // platen = 7x8
        if ($isResize && $preset['platen_width'] == 1778) {
            $ratio = $ratio * 0.85;
        }

        $imageWidthNew = $isResize ? $preset['image_width'] * $ratio : $preset['image_width'];
        $imageHeightNew = $isResize ? $preset['image_height'] * $ratio : $preset['image_height'];
        $offsetLeft = ($preset['preset_print_area_width'] - $imageWidthNew) / 2 + $preset['print_area_left'];
        $offsetTop = $preset['print_area_top'];

        if ($isResize && $preset['platen_width'] == 1778) {
            $offsetTop = ($preset['preset_print_area_height'] - $imageHeightNew) / 2 + $preset['print_area_top'];
        }

        return [
            'offset_left' => round($offsetLeft),
            'offset_top' => round($offsetTop),
            'image_width' => round($imageWidthNew),
            'image_height' => round($imageHeightNew),
            'note' => 'adjust normal',
        ];
    }

    public function changeInkColor($image_id, $ink_color)
    {
        $image = SaleOrderItemImage::query()->where('id', $image_id)->first();

        if ($image) {
            $image->update(['manual_color' => $ink_color]);
        }
    }

    public function updateEmployeePrintingLabel($employee_id, $label_id)
    {
        $barcode = SaleOrderItemBarcode::with('order:id,external_number')->where('label_id', $label_id)->first();

        if ($barcode) {
            $barcode->employee_print_id = $employee_id;
            $barcode->printed_at = date('Y-m-d H:i:s');
            $barcode->save();
            $this->updateOrderPrinted($barcode->order_id);
        }

        // post back gelato
        if ($barcode && $barcode->store_id === Store::STORE_GELATO) {
            $integrateCallbackLogOrder = IntegrateCallbackLog::where('order_id', $barcode->order_id)
                ->where('event', IntegrateCallbackLog::ORDER_NOTIFY_EVENT)
                ->where('data', 'like', '%"status":"printed"%')
                ->where('status', true)
                ->first();
            if (!$integrateCallbackLogOrder) {
                $dataPostBack = [
                    'order_id' => $barcode->order_id,
                    'event' => IntegrateCallbackLog::ORDER_NOTIFY_EVENT,
                    'data_send_gelato' => [
                        'timestamp' => Carbon::now()->toIso8601String(),
                        'orderId' => $barcode->order->external_number,
                        'status' => 'printed',
                        'message' => 'Order has been printed',
                    ]
                ];
                handleJob(StoreIntegrate::JOB_POST_BACK_GELATO, $dataPostBack);
            }
        }

        return $barcode;
    }

    public function updateEmployeePrinting($employee_id, $sku)
    {
        $barcode = SaleOrderItemBarcode::whereNull('employee_print_id')
            ->where('sku', $sku)->limit(1)->first();

        if ($barcode) {
            $barcode->employee_print_id = $employee_id;
            $barcode->printed_at = date('Y-m-d H:i:s');
            $barcode->save();
            $this->updateOrderPrinted($barcode->order_id);
        }

        return $barcode;
    }

    public function updateOrderPrinted($order_id)
    {
        $barcodes = SaleOrderItemBarcode::where('order_id', $order_id)->whereNull('printed_at')->where('is_deleted', false)->count();

        if ($barcodes == 0) {
            $saleOrder = SaleOrder::where('id', $order_id)->first();
            $saleOrder->order_printed_status = 1;
            $saleOrder->order_printed_at = date('Y-m-d H:i:s');
            $saleOrder->save();
        }
    }

    public function updateInkCC($image_id, $ink_color_cc, $ink_white_cc)
    {
        SaleOrderItemImage::query()->where('id', $image_id)->update(
            [
                'ink_color_cc' => $ink_color_cc,
                'ink_white_cc' => $ink_white_cc
            ],
        );
    }

    public function doWriteLogPrinter($request)
    {
        $printerLog = new PrinterLog([
            'warehouse_id' => $request->warehouse_id,
            'employee_id' => $request->employee_id,
            'device_id' => $request->device_id,
            'printer' => $request->printer,
            'total_print' => $request->total_print,
            'host_name' => $request->host_name,
        ]);
        $printerLog->save();

        return $printerLog;
    }

    public function insertPrinterBarcodeLog($warehouse_id, $employee_id, $label_id, $hostName, $side, $deviceId, $printer, $iccUsed = '')
    {
        try {
            $barcode = SaleOrderItemBarcode::where('label_id', $label_id)->first();
            $printerLogData = [
                'warehouse_id' => $warehouse_id,
                'employee_id' => $employee_id,
                'label_id' => $label_id,
                'order_id' => $barcode->order_id,
                'host_name' => $hostName,
                'device_id' => $deviceId,
                'side' => $side,
                'printer' => $printer,
            ];
            $printerLog = new PrinterWipLog($printerLogData);
            $printerLog->save();

            if ($printer) {
                $message = "Label {$label_id} printed " . strtoupper($side) . " by printer: {$printer}" . (!empty($hostName) ? " - {$hostName}" : '');

                if ($iccUsed) {
                    $message .= " using  ICC: $iccUsed";
                }

                SaleOrderHistory::create([
                    'employee_id' => $employee_id,
                    'order_id' => $barcode->order_id,
                    'type' => SaleOrderHistory::PRINTING_PRINT_AREA,
                    'message' => $message,
                    'created_at' => now()
                ]);
            }

            return $printerLog;
        } catch (\Exception $exception) {
            return false;
        }
    }

    public function sendMailLogPrinter($request = null)
    {
        $warehouseMexicoId = 18;
        $warehouseId = $request['warehouse_id'] ?? $warehouseMexicoId;
        $growthByHostName = DB::table('printer_log')
            ->select(
                'host_name',
                'printer',
                DB::raw('MAX(CASE WHEN is_max_date = 1 THEN created_at END) AS min_date_log'),
                DB::raw('MAX(CASE WHEN is_max_date IS NULL THEN created_at END) AS max_date_log'),
                DB::raw('MAX(CASE WHEN is_max_date = 1 THEN total_print END) AS total_print_pre_max'),
                DB::raw('MAX(CASE WHEN is_max_date IS NULL THEN total_print END) AS total_print_max'),
                DB::raw('MAX(CASE WHEN is_max_date IS NULL THEN total_print END) - MAX(CASE WHEN is_max_date = 1 THEN total_print END) AS total_print_via_printer'),
            )
            ->where('warehouse_id', $warehouseId)
            ->where('created_at', '>=', '2024-06-16 14:09:41') // Ngay bat dau update
            ->groupBy('host_name', 'printer')
            ->havingRaw('MAX(CASE WHEN is_max_date IS NULL THEN created_at END) > MAX(CASE WHEN is_max_date = 1 THEN created_at END)')
            ->get();

        if ($growthByHostName->count() == 0) {
            return 0;
        }

        foreach ($growthByHostName as $itemHostName) {
            $startDate = $itemHostName->min_date_log;
            $endDate = $itemHostName->max_date_log;
            $resultsViaAppPrinting = DB::table('printer_wip_log AS pwl')
                ->select('pwl.host_name')
                ->selectRaw('COUNT(DISTINCT CONCAT(pwl.label_id, "-", pwl.side)) AS total_print_via_app')
                ->selectRaw('COUNT(DISTINCT CASE WHEN so.order_status = "shipped" THEN pwl.order_id END) AS total_shipped')
                ->leftJoin('sale_order AS so', 'so.id', '=', 'pwl.order_id')
                ->where('pwl.created_at', '>=', $startDate)
                ->where('pwl.created_at', '<=', $endDate)
                ->where('pwl.host_name', $itemHostName->host_name)
                ->where('pwl.printer', $itemHostName->printer)
                ->where('pwl.warehouse_id', $warehouseId)
                ->first();

            if (!$resultsViaAppPrinting) {
                continue;
            }

            $itemHostName->min_date_log = Carbon::parse($itemHostName->min_date_log)->timezone('America/Los_Angeles')->toDateTimeString();
            $itemHostName->max_date_log = Carbon::parse($itemHostName->max_date_log)->timezone('America/Los_Angeles')->toDateTimeString();
            $itemHostName->total_print_via_app = $resultsViaAppPrinting->total_print_via_app;
            $itemHostName->total_shipped = $resultsViaAppPrinting->total_shipped;
        }

        $setting = Setting::where('label', 'email_printer_log')->first();
        $mailArr = explode(',', $setting->value);
        $to1 = trim($mailArr[0]);
        $to2 = trim($mailArr[1]);
        $cc = trim($mailArr[2]);
        $mail = Mail::to([$to1, $to2])->cc($cc);
        $startDate = $growthByHostName->min('min_date_log');
        $endDate = $growthByHostName->max('max_date_log');
        $emailParams = [
            'start_date' => Carbon::parse($startDate)->timezone('America/Los_Angeles')->toDateTimeString(),
            'end_date' => Carbon::parse($endDate)->timezone('America/Los_Angeles')->toDateTimeString(),
            'total_all_print_via_app' => $growthByHostName->sum('total_print_via_app'),
            'total_all_print_via_printer' => $growthByHostName->sum('total_print_via_printer'),
            'total_shipped' => $growthByHostName->sum('total_shipped'),
            'printer' => $growthByHostName,
        ];

        ///Todo : update set max_date để cho lần chạy tiếp theo
        if (!isset($request['is_test'])) {
            DB::table('printer_log')
                ->whereIn('id', function ($query) use ($warehouseId) {
                    $query->selectRaw('MAX(id)')
                        ->from('printer_log')
                        ->where('warehouse_id', $warehouseId)
                        ->whereRaw('is_max_date IS NULL')
                        ->groupBy('host_name', 'printer');
                })
                ->update(['is_max_date' => 1]);
        }

        $mail->send(new SendMailPrinting($emailParams));
    }

    public function updateForOrderItemHasInsertPrinting($orderId): void
    {
        /** @var SaleOrderItemRepository $saleOrderItemRepo */
        $saleOrderItemRepo = resolve(SaleOrderItemRepository::class);
        $saleOrderItemId = $saleOrderItemRepo->orderItemHasProductInsert($orderId);

        if ($saleOrderItemId->count() > 0) {
            SaleOrderItemBarcode::whereIn('order_item_id', $saleOrderItemId)->update([
                'employee_pull_id' => User::SYSTEM,
                'pulled_at' => date('Y-m-d H:i:s'),
                'employee_print_id' => User::SYSTEM,
                'printed_at' => date('Y-m-d H:i:s'),
                'employee_staging_id' => User::SYSTEM,
                'staged_at' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    /**
     * @param $label 020922-SJ-S-002726-1-0  --> 020922-SJ-S-002726-1
     */
    public function removeLabelSideNew($label): string
    {
        $label = trim($label);
        $i = explode('-', $label);

        if (count($i) == 6) {
            return substr($label, 0, (strlen($i[5]) + 1) * -1);
        }

        return $label;
    }

    public function getImageBySkuSideEmbroidery($barcode, $side, $employeeId = 0): array
    {
        $output = new \stdClass();

        if ($barcode->is_deleted == 1) {
            return [
                'status' => false,
                'message' => 'This order has been cancelled'
            ];
        }

        if (!empty($barcode->order_id)) {
            $saleOrder = SaleOrder::where('id', $barcode->order_id)->first();

            if (empty($saleOrder)) {
                return [
                    'status' => false,
                    'message' => 'Order not found'
                ];
            }

            if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
                return [
                    'status' => false,
                    'message' => 'Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)
                ];
            }
        } else {
            $printLogError = new PrintLogError;
            $printLogError->sku = $barcode->label_id;
            $printLogError->save();

            return [
                'status' => false,
                'message' => 'Sku not found'
            ];
        }

        $employee = Employee::query()->where('id', $employeeId)->first();

        if ($employee == false) {
            return [
                'status' => false,
                'message' => 'Employee not found'
            ];
        }

        if ($employee->warehouse_id != $saleOrder->warehouse_id) {
            return [
                'status' => false,
                'message' => 'This label belongs to another warehouse'
            ];
        }

        $store = Store::where('id', $saleOrder->store_id)->first();

        if (empty($store)) {
            return [
                'status' => false,
                'message' => 'Store not found'
            ];
        }

        $sku = $barcode->sku;
        $image = SaleOrderItemImage::query()
            ->with('imageHash', 'product.productStyle')
            ->where('sku', $sku)
            ->where('print_side', $side . '')
            ->where('order_id', $saleOrder->id)
            ->whereNotNull('color_new')->first();

        if (!$image) {
            if ($side == 0) {
                $side = 1;
                $image = SaleOrderItemImage::query()
                    ->with('imageHash', 'product.productStyle')
                    ->where('sku', $sku)
                    ->where('print_side', $side . '')
                    ->where('is_double_side', 0)
                    ->where('order_id', $saleOrder->id)
                    ->whereNotNull('color_new')->first();
            }

            if (!$image) {
                return [
                    'status' => false,
                    'message' => 'Image not found!'
                ];
            }
        }

        $output->image = $image;
        // lấy thông tin của thêu
        $embroidery = EmbroideryTask::query()
            ->where('sale_order_id', $saleOrder->id)
            ->where('sale_order_image_id', $image->id)
            ->where('status', EmbroideryTask::STATUS_COMPLETED)
            ->first();

        if (!$embroidery) {
            return [
                'status' => false,
                'message' => 'Embroidery file not found or not completed'
            ];
        }

        $presetSku = PrintingPresetSku::query()->where('sku', $image->product_sku)->first();

        if (!$presetSku) {
            return [
                'status' => false,
                'message' => 'Preset not found!'
            ];
        }

        if ($image->manual_color != null) {
            $image->color_new = $image->manual_color;
        }

        if ($saleOrder->is_xqc) {
            $ink = $this->inkColorXqc[$image->color_new] ?? '';

            if ($image->is_purple == SaleOrderItemImage::IS_PURPLE) {
                $ink = $this->inkColorXqc['custom'] ?? '';
            }
        } else {
            $ink = $this->inkColor[$image->color_new] ?? '';

            if ($image->is_purple == SaleOrderItemImage::IS_PURPLE) {
                $ink = $this->inkColor['custom'] ?? '';
            }
        }

        if ($ink == '') {
            return [
                'status' => false,
                'message' => 'Preset not found'
            ];
        }

        $print_side = ProductPrintSide::findByCode($side);

        // update employee scan
        $barcode->update([
            'employee_scan_id' => $employeeId,
            'scanned_at' => date('Y-m-d H:i:s')
        ]);

        if (!isset($this->platenSizeToIndex[$presetSku->platen_size])) {
            return [
                'status' => false,
                'message' => 'Platen size not allowed'
            ];
        }

        $saleOrderItem = SaleOrderItem::query()->where('id', $image->order_item_id)->first();
        $product = Product::query()->where('sku', $saleOrderItem->product_sku)->first();
        $image_name = "$sku-$side.png";
        $dataOptions = json_decode($saleOrderItem->options);
        $saleOrderItem->front = null;
        $saleOrderItem->back = null;
        $preview_by_side = null;

        foreach ($dataOptions as $item) {
            if (str_contains($item->name, 'PreviewFiles.' . str_replace(' ', '', ucwords(str_replace('_', ' ', $print_side->code_name))))) {
                $preview_by_side = $item->value;
            }
        }

        $thumb_s3 = env('AWS_S3_URL') . '/thumb/250/' . $image->order_date . '/' . $image_name;
        $artwork_s3 = $image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_S3_URL') . '/artwork/' . $image->order_date . '/' . $image_name : null;
        $preview = $preview_by_side;
        $preview = $preview == '' ? $thumb_s3 : $preview;

        $output->product = $product;
        $output->thumb = $image->thumb_s3;
        $output->preview = $preview;
        $output->artwork_s3 = $artwork_s3;
        $output->print_side = $print_side;
        $output->embroidery = $embroidery;
        $output->image_name = "$barcode->label_id-$side." . pathinfo($embroidery?->link_url, PATHINFO_EXTENSION);
        $output->color_name = "$barcode->label_id-$side." . pathinfo($embroidery?->color_url, PATHINFO_EXTENSION);

        return ['status' => true, 'data' => $output];
    }

    public function getImageByLabelEmbroidery($barcode, $employeeId = 0): array
    {
        if ($barcode->is_deleted == 1) {
            return [
                'status' => false,
                'message' => 'This order has been cancelled'
            ];
        }

        if (!empty($barcode->order_id)) {
            $saleOrder = SaleOrder::where('id', $barcode->order_id)->first();

            if (empty($saleOrder)) {
                return [
                    'status' => false,
                    'message' => 'Order not found'
                ];
            }

            if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
                return [
                    'status' => false,
                    'message' => 'Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)
                ];
            }
        } else {
            $printLogError = new PrintLogError;
            $printLogError->sku = $barcode->label_id;
            $printLogError->save();

            return [
                'status' => false,
                'message' => 'Sku not found'
            ];
        }

        $employee = Employee::query()->where('id', $employeeId)->first();

        if ($employee == false) {
            return [
                'status' => false,
                'message' => 'Employee not found'
            ];
        }

        if ($employee->warehouse_id != $saleOrder->warehouse_id) {
            return [
                'status' => false,
                'message' => 'This label belongs to another warehouse'
            ];
        }

        $store = Store::where('id', $saleOrder->store_id)->first();

        if (empty($store)) {
            return [
                'status' => false,
                'message' => 'Store not found'
            ];
        }

        $sku = $barcode->sku;

        $images = SaleOrderItemImage::query()
            ->with('imageHash', 'product.productStyle')
            ->where('sku', $sku)
            ->where('order_id', $saleOrder->id)
            ->get();

        if (count($images) < 1) {
            return [
                'status' => false,
                'message' => 'Image not found!'
            ];
        }

        $outputs = [];
        $totalSideNotEMB = 0;
        $dataChartName = EmbroideryColorChart::all();

        foreach ($images as $image) {
            $output = new \stdClass();
            $side = $image->print_side;
            $output->image = $image;
            // lấy thông tin của thêu
            $embroidery = EmbroideryTask::query()
                ->where('sale_order_id', $saleOrder->id)
                ->where('sale_order_image_id', $image->id)
                ->where('status', EmbroideryTask::STATUS_COMPLETED)
                ->first();

            if (!$embroidery) {
                $totalSideNotEMB++;

                continue;
            }

            $presetSku = PrintingPresetSku::query()->where('sku', $image->product_sku)->first();

            if (!$presetSku) {
                return [
                    'status' => false,
                    'message' => 'Preset not found!'
                ];
            }

            $print_side = ProductPrintSide::findByCode($side);
            // update employee scan
            $barcode->update([
                'employee_scan_id' => $employeeId,
                'scanned_at' => date('Y-m-d H:i:s')
            ]);
            $saleOrderItem = SaleOrderItem::query()->where('id', $image->order_item_id)->first();
            $product = Product::query()->where('sku', $saleOrderItem->product_sku)->first();
            $image_name = "$sku-$side.png";

            $dataOptions = json_decode($saleOrderItem->options);
            $saleOrderItem->front = null;
            $saleOrderItem->back = null;
            $preview_by_side = null;

            foreach ($dataOptions as $item) {
                if (str_contains($item->name, 'PreviewFiles.' . str_replace(' ', '', ucwords(str_replace('_', ' ', $print_side->code_name))))) {
                    $preview_by_side = $item->value;
                }
            }

            $thumb_s3 = env('AWS_S3_URL') . '/thumb/250/' . $image->order_date . '/' . $image_name;
            $artwork_s3 = $image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_S3_URL') . '/artwork/' . $image->order_date . '/' . $image_name : null;
            $preview = $preview_by_side ?? null;
            $preview = $preview == '' ? $thumb_s3 : $preview;

            $output->product = $product ?? null;
            $output->thumb = $image->thumb_s3 ?? null;
            $output->preview = $preview ?? null;
            $output->artwork_s3 = $artwork_s3;
            $output->print_side = $print_side;
            $output->embroidery = $embroidery;
            $output->preset = $presetSku;
            $printArea = $print_side->code_name . '_size';
            $output->print_size = $presetSku->{$printArea};
            $output->label_id = $barcode->label_id;
            $output->worksheet = $embroidery?->worksheet_url;
            $output->chart_name = $embroidery->threads_colors
                ? $dataChartName->where('chart_name', $embroidery->chart_name)
                    ->whereIn('thread_code', explode(',', str_replace(' ', '', $embroidery->threads_colors)))
                : $dataChartName->where('chart_name', $embroidery->chart_name);

            if (empty($barcode->label_id) || empty($side)) {
                $output->image_name = null;
                $output->color_name = null;
                $output->worksheet_name = null;
            } else {
                $output->image_name = "$barcode->label_id." . pathinfo($embroidery?->link_url, PATHINFO_EXTENSION);
                $output->color_name = '';

                if (!empty($embroidery?->color_url)) {
                    $output->color_name = "$barcode->label_id." . pathinfo($embroidery?->color_url, PATHINFO_EXTENSION);
                }

                $output->worksheet_name = '';

                if (!empty($embroidery?->worksheet_url)) {
                    $output->worksheet_name = "$barcode->label_id." . pathinfo($embroidery?->worksheet_url, PATHINFO_EXTENSION);
                }
            }

            $outputs[] = $output;
        }

        if ($totalSideNotEMB == count($images)) {
            return [
                'status' => false,
                'message' => 'Embroidery file not found or not completed'
            ];
        }

        setTimezone();
        $slaOrder = SaleOrderSla::where('order_id', $saleOrder->id)->first();

        if ($slaOrder && in_array($saleOrder->order_status, [SaleOrder::NEW_ORDER, SaleOrder::STATUS_IN_PRODUCTION])) {
            $slaOrder->sla_expired_at_utc = $slaOrder->expired_at ? shiftTimezoneToUTC($slaOrder->expired_at) : null;
        } else {
            $slaOrder = null;
        }

        return ['status' => true, 'data' => $outputs, 'slaOrder' => $slaOrder];
    }

    public function getIccPresetName($image, $presetName = '')
    {
        $product = $image->product ?? null;

        if (empty($product)) {
            return $presetName;
        }

        $styleICConvertExists = ProductStyleIccProfile::query()
            ->where('product_style', $product->style ?? null)
            ->exists();
        $productColor = ProductColor::query()
            ->where('name', $product->color)
            ->first();
        $productType = $product->productStyle->type ?? null;

        if (empty($styleICConvertExists) || empty($productColor) || empty($productType)) {
            return $presetName;
        }

        if ($productType == ProductType::TEE) {
            $presetName = PrintingPreset::convertPresetDtgColorForTee(strtoupper($productColor->icc_color));
        }

        if ($productType == ProductType::FLEECE) {
            $presetName = PrintingPreset::convertPresetDtgColorForFleece(strtoupper($productColor->icc_color));
        }

        return $presetName;
    }
}
