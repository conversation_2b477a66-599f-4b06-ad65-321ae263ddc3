<?php

namespace App\Repositories;

use App\Http\Service\OrderEditingService;
use App\Jobs\StatusOrderJob;
use App\Models\ImageHash;
use App\Models\IntegrateCallbackLog;
use App\Models\IntegrateLog;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemImageTag;
use App\Models\SaleOrderItemReject;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\Store;
use App\Models\Tag;
use App\Models\Trademark;
use App\Models\TrademarkImage;
use App\Models\TrademarkImageHistory;
use App\Models\VisuaDetectImage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LavenderRepository extends CommonRepository
{
    public function rejectDesign($input)
    {
        try {
            $userId = \auth()?->user()?->id ?? null;
            $trademarks = [];
            $hashIds = [];

            foreach ($input as $item) {
                $hashIds[] = $item['image_hash_id'];
                $trademarks[$item['image_hash_id']] = array_unique(array_merge($item['trademark_ids'], $trademarks[$item['image_hash_id']] ?? []));
                if (!empty($item['tag_ids'])) {
                    $tags = SaleOrderItemImageTag::whereIn('id', $item['tag_ids'])->get();
                    $images = SaleOrderItemImage::with(['tags'])->where('image_hash_id', (string)$item['image_hash_id'])->get();
                    foreach ($images as $image) {
                        $imageTags = $image->tags;
                        // check Image có tags rồi thì update status = 1 chưa có thì tạo mới với type và value
                        foreach ($tags as $tag) {
                            $imageTag = $imageTags->where('value', $tag->value)
                                ->where('type', $tag->type)->first();
                            if ($imageTag) {
                                $imageTag->update([
                                    'status' => true,
                                ]);
                            } else {
                                $image->tags()->create([
                                    'tag_id' => $tag->tag_id,
                                    'type' => $tag->type,
                                    'value' => $tag->value,
                                    'status' => true,
                                ]);
                            }
                        }
                    }
                }
            }

            $imageHashs = ImageHash::whereIn('id', $hashIds)->whereNull('is_user_check_ip_violation')->select('id', 'is_ip_violation')->get();
            $ids = [];

            foreach ($imageHashs as $hashId) {
                if (!empty($hashId) && $hashId->is_ip_violation === null) {
                    $ids[] = $hashId->id;
                }
            }

            if (!empty($ids)) {
                ImageHash::whereIn('id', $ids)->update([
                    'is_user_check_ip_violation' => ImageHash::IS_IP_VIOLATION,
                    'user_check_id' => $userId,
                    'user_check_time' => now(),
                ]);
            }

            foreach ($trademarks as $imageHashId => $trademarkIds) {
                if (in_array($imageHashId, $ids)) {
                    foreach ($trademarkIds as $trademarkId) {
                        $data[] = [
                            'user_id' => $userId,
                            'image_hash_id' => $imageHashId,
                            'trademark_id' => $trademarkId,
                        ];
                    }
                }
            }

            if (!empty($data)) {
                TrademarkImage::insert($data);
            }
            return true;
        } catch (\Exception $exception) {
            Log::error('LavenderRepository.rejectDesign', [$exception]);
            throw new \Exception($exception->getMessage());
        }
    }

    public function passDesign($input)
    {
        try {
            $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
            $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
            $userId = \auth()->user()?->id ?? null;

            if (!empty($input['image_hash_ids'])) {
                $hashIds = $input['image_hash_ids'];
                $imageHashs = ImageHash::whereIn('id', $hashIds)->whereNull('is_user_check_ip_violation')->select('id', 'is_ip_violation')->get();
                $ids = [];

                foreach ($imageHashs as $hashId) {
                    if ($hashId->is_ip_violation === null) {
                        $ids[] = $hashId->id;
                    }
                }

                if (!empty($ids)) {
                    ImageHash::whereIn('id', $ids)->update([
                        'is_user_check_ip_violation' => ImageHash::IS_NOT_IP_VIOLATION,
                        'is_ip_violation' => ImageHash::IS_NOT_IP_VIOLATION,
                        'user_check_id' => $userId,
                        'user_check_time' => now(),
                    ]);
                }

                $imageIds = SaleOrderItemImage::whereIn('image_hash_id', array_map('strval', $ids))->pluck('id')->toArray();
                VisuaDetectImage::whereIn('image_id', $imageIds)
                    ->update([
                        'user_detect_id' => $userId,
                        'is_ip_violation' => VisuaDetectImage::IS_FALSE,
                    ]);
                $orders = DB::table('sale_order')->select(
                    'sale_order.id',
                    'image_hash.is_ip_violation',
                    'sale_order.order_status',
                    'image_hash.id as image_hash_id',
                    DB::raw('SUM(CASE WHEN image_hash.is_ip_violation = 0  THEN 1 ELSE 0 END) AS total_non_violation_images'),
                    DB::raw('count(sale_order_item_image.id) as total_images'),
                )
                    ->leftJoin('sale_order_on_hold', 'sale_order_on_hold.order_id', 'sale_order.id')
                    ->join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
                    ->rightJoin('image_hash', 'sale_order_item_image.image_hash_id', 'image_hash.id')
                    ->where('sale_order_on_hold.store_on_hold', VisuaDetectImage::IS_FALSE)
                    ->where('sale_order_on_hold.manual_on_hold', VisuaDetectImage::IS_FALSE)
                    ->whereIn('sale_order_item_image.image_hash_id', $ids)
                    ->where('sale_order.order_status', SaleOrder::ON_HOLD)
                    ->groupBy('sale_order_item_image.id')
                    ->get();
                $ids = $orders->filter(fn($item) => $item->total_non_violation_images == $item->total_images)
                    ->pluck('id')->toArray();

                if (!empty($ids)) {
                    foreach ($ids as $orderId) {
                        SaleOrder::where('id', $orderId)
                            ->update(['sale_order.order_status' => SaleOrder::NEW_ORDER]);
                    }
                }

                $dataHistory = [];

                foreach ($ids as $id) {
                    $dataHistory[] = [
                        'user_id' => $userId,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => 'Order status changed from "on hold" to "new order"',
                        'order_id' => $id
                    ];
                }

                if (!empty($dataHistory)) {
                    SaleOrderHistory::insert($dataHistory);
                }

                if (!in_array(Store::STORE_REDBUBBLE, $storeExcludeCheckIP)) {
                    $ordersOfStoreRBB = SaleOrder::join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
                        ->whereIn('sale_order_item_image.image_hash_id', $imageIds)
                        ->where('sale_order.store_id', Store::STORE_REDBUBBLE)
                        ->pluck('sale_order.id');

                    foreach ($ordersOfStoreRBB as $id) {
                        $this->rejectOrderRedBubble($id, $userId);
                    }
                }

                SaleOrderOnHold::whereIn('order_id', $ids)
                    ->where('visua_on_hold', 1)
                    ->where('store_on_hold', 0)
                    ->where('manual_on_hold', 0)
                    ->delete();
                SaleOrderOnHold::whereIn('order_id', $ids)
                    ->where('visua_on_hold', 1)
                    ->where(function ($query) {
                        $query->where('store_on_hold', true)
                            ->orWhere('manual_on_hold', true);
                    })
                    ->update(['visua_on_hold' => 0]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('LavenderRepository.passDesign', [$e]);
            throw new \Exception($e->getMessage());
        }
    }

    public function confirmRejectDesign($input)
    {
        if (empty($input)) {
            Log::error('LavenderRepository.confirmRejectDesign No input data!');
            throw new \Exception('No input data!');
        }

        DB::beginTransaction();
        try {
            $userId = \auth()->user()->id ?? null;
            $imageIds = [];
            $trademarks = [];
            $hashIds = [];
            $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
            $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];

            foreach ($input as $item) {
                $imageIds = array_merge($item['image_ids'], $imageIds);
                if (!empty($item['values'])) {
                    foreach ($item['values'] as $value) {
                        if (empty($value['type']) || empty($value['name'])) {
                            continue;
                        }
                        $tradeMark = Trademark::where('type', $value['type'])
                            ->where('name', $value['name'])->first();
                        if (!$tradeMark) {
                            $tradeMark = Trademark::create([
                                'type' => $value['type'],
                                'name' => $value['name'],
                                'created_by' => $userId,
                                'is_active' => Trademark::IS_ACTIVE,
                            ]);
                        }
                        $item['trademark_ids'][] = $tradeMark->id;
                    }
                }
                $hashIds[] = $item['image_hash_id'];
                $trademarks[$item['image_hash_id']] = array_unique(array_merge($item['trademark_ids'], $trademarks[$item['image_hash_id']] ?? []));
            }

            $imageIds = array_unique($imageIds);
            $imageHashs = ImageHash::whereIn('id', $hashIds)->whereNull('is_ip_violation')->where('is_user_check_ip_violation', true)->select('id', 'is_ip_violation')->get();
            $ids = [];

            foreach ($imageHashs as $hashId) {
                if ($hashId?->is_ip_violation === null) {
                    $ids[] = $hashId->id;
                }
            }

            if (!empty($ids)) {
                ImageHash::whereIn('id', $ids)->update([
                    'is_ip_violation' => ImageHash::IS_IP_VIOLATION,
                    'user_review_id' => $userId,
                    'user_review_time' => now(),
                ]);
            }

            $orders = SaleOrder::join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
                ->whereIn('sale_order_item_image.image_hash_id', $ids)
                ->whereNotIn('sale_order.store_id', $storeExcludeCheckIP)
                ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD])
                ->select('sale_order.id', 'sale_order.order_status')->distinct()->get()->toArray();
            $idsUpdate = array_column($orders, 'id');

            foreach ($idsUpdate as $id) {
                $order = SaleOrder::find($id);

                if ($order->store_id == Store::STORE_REDBUBBLE) {
                    $this->rejectOrderRedBubble($order->id, $userId);
                } else {
                    $this->rejectOrder($order->id, $userId);
                }
            }

            $listImageIds = SaleOrderItemImage::whereIn('image_hash_id', array_map('strval', $ids))->pluck('id')->toArray();
            VisuaDetectImage::whereIn('image_id', $listImageIds)
                ->update([
                    'user_detect_id' => $userId,
                    'is_ip_violation' => VisuaDetectImage::IS_TRUE,
                ]);

            $dataHistory = TrademarkImage::whereIn('image_hash_id', $hashIds)->select(['image_hash_id', 'trademark_id', 'user_id'])->get()->toArray();
            TrademarkImageHistory::insert($dataHistory);
            TrademarkImage::whereIn('image_hash_id', $hashIds)->delete();
            $data = [];

            foreach ($trademarks as $imageHashId => $trademarkIds) {
                if (in_array($imageHashId, $ids)) {
                    foreach ($trademarkIds as $trademarkId) {
                        $data[] = [
                            'user_id' => $userId,
                            'image_hash_id' => $imageHashId,
                            'trademark_id' => $trademarkId,
                        ];
                    }
                }
            }

            if (!empty($data)) {
                TrademarkImage::insert($data);
            }

            DB::commit();
            handleJob(QueueJob::SEND_NOTIFY_REJECT_IMAGE, $imageIds);

            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('LavenderRepository.confirmRejectDesign', [$exception]);
            throw new \Exception($exception->getMessage());
        }
    }

    public function confirmPassDesign($input)
    {
        try {
            DB::beginTransaction();
            $userId = \auth()->user()->id ?? null;
            $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
            $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];

            if (!empty($input['image_hash_ids'])) {
                $hashIds = $input['image_hash_ids'];
                $imageHashs = ImageHash::whereIn('id', $hashIds)->whereNull('is_ip_violation')->where('is_user_check_ip_violation', true)->select('id', 'is_ip_violation')->get();
                $ids = [];

                foreach ($imageHashs as $hashId) {
                    if ($hashId->is_ip_violation === null) {
                        $ids[] = $hashId->id;
                    }
                }

                if (!empty($ids)) {
                    ImageHash::whereIn('id', $ids)->update([
                        'is_ip_violation' => ImageHash::IS_NOT_IP_VIOLATION,
                        'user_review_id' => $userId,
                        'user_review_time' => now(),

                    ]);
                }

                $listImageIds = SaleOrderItemImage::whereIn('image_hash_id', array_map('strval', $ids))->pluck('id')->toArray();
                VisuaDetectImage::whereIn('image_id', $listImageIds)
                    ->update([
                        'user_detect_id' => $userId,
                        'is_ip_violation' => VisuaDetectImage::IS_FALSE,
                    ]);

                $orders = DB::table('sale_order')->select(
                    'sale_order.id',
                    'image_hash.is_ip_violation',
                    'sale_order.order_status',
                    'image_hash.id as image_hash_id',
                    DB::raw('SUM(CASE WHEN image_hash.is_ip_violation = 0  THEN 1 ELSE 0 END) AS total_non_violation_images'),
                    DB::raw('count(sale_order_item_image.id) as total_images'),
                )
                    ->leftJoin('sale_order_on_hold', 'sale_order_on_hold.order_id', 'sale_order.id')
                    ->join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
                    ->rightJoin('image_hash', 'sale_order_item_image.image_hash_id', 'image_hash.id')
                    ->where('sale_order_on_hold.store_on_hold', VisuaDetectImage::IS_FALSE)
                    ->where('sale_order_on_hold.manual_on_hold', VisuaDetectImage::IS_FALSE)
                    ->whereIn('sale_order_item_image.image_hash_id', $ids)
                    ->where('sale_order.order_status', SaleOrder::ON_HOLD)
                    ->groupBy('sale_order_item_image.id')
                    ->get();
                $ids = $orders->filter(fn($item) => $item->total_non_violation_images == $item->total_images)
                    ->pluck('id')->toArray();

                if (!empty($ids)) {
                    foreach ($ids as $orderId) {
                        SaleOrder::where('id', $orderId)
                            ->update(['sale_order.order_status' => SaleOrder::NEW_ORDER]);
                    }
                }

                $dataHistory = [];

                foreach ($ids as $id) {
                    $dataHistory[] = [
                        'user_id' => $userId,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => 'Order status changed from "on hold" to "new order"',
                        'order_id' => $id
                    ];
                }

                if (!empty($dataHistory)) {
                    SaleOrderHistory::insert($dataHistory);
                }

                if (!in_array(Store::STORE_REDBUBBLE, $storeExcludeCheckIP)) {
                    $ordersOfStoreRBB = SaleOrder::join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
                        ->whereIn('sale_order_item_image.image_hash_id', $input['image_hash_ids'])
                        ->where('sale_order.store_id', Store::STORE_REDBUBBLE)
                        ->pluck('sale_order.id');

                    foreach ($ordersOfStoreRBB as $id) {
                        $this->rejectOrderRedBubble($id, $userId);
                    }
                }

                SaleOrderOnHold::whereIn('order_id', $ids)
                    ->where('visua_on_hold', 1)
                    ->where('store_on_hold', 0)
                    ->where('manual_on_hold', 0)
                    ->delete();
                SaleOrderOnHold::whereIn('order_id', $ids)
                    ->where('visua_on_hold', 1)
                    ->where(function ($query) {
                        $query->where('store_on_hold', true)
                            ->orWhere('manual_on_hold', true);
                    })
                    ->update(['visua_on_hold' => 0]);
            }
            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('LavenderRepository.confirmPassDesign', [$e]);
            throw new \Exception($e->getMessage());
        }
    }

    public function notCheckIpViolationYet($orderId)
    {
        $query = SaleOrderItemImage::selectRaw('sale_order_item_image.id as sale_order_item_image_id, image_hash.id as image_hash_id')
            ->leftJoin('image_hash', 'image_hash.id', '=', 'sale_order_item_image.image_hash_id')
            ->where('sale_order_item_image.order_id', $orderId)
            ->whereNull('image_hash.is_ip_violation');
        $sql = interpolateQuery($query);
        $result = $query->first();

        return [
            'result' => $result,
            'sql' => $sql,
        ];
    }

    public function rejectOrderRedBubble($orderId, $userIdReject, $tag = null)
    {
        Log::info("=============================Process rejectOrderRedBubble OrderID: $orderId=============================");
        $checkIpViolationYet = $this->notCheckIpViolationYet($orderId);

        if (!empty($checkIpViolationYet['result'])) {
            Log::info('LavenderRepository.rejectOrderRedBubble notCheckIpViolationYet', [
                'order id' => $orderId,
                'request' => request()->path(),
                'data check' => $checkIpViolationYet,
            ]);

            return;
        }

        $orderItemQty = SaleOrderItem::where('order_id', $orderId)->count();
        $orderItemReject = SaleOrderItem::select('sale_order_item.*')
            ->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item.id')
            ->join('image_hash', 'sale_order_item_image.image_hash_id', '=', 'image_hash.id')
            ->where('sale_order_item.order_id', $orderId)
            ->where('image_hash.is_ip_violation', true)
            ->groupBy('sale_order_item.id')
            ->get()
            ->toArray();

        if (empty($orderItemReject)) {
            Log::info('LavenderRepository.rejectOrderRedBubble Order item reject empty', [
                'order id' => $orderId,
                'request' => request()->path(),
            ]);

            return;
        }

        if (count($orderItemReject) < $orderItemQty) {
            $order = SaleOrder::find($orderId);

            if ($order->order_status == SaleOrder::ON_HOLD) {
                $order->order_status = SaleOrder::NEW_ORDER;
                $order->save();

                $dataHistory[] = [
                    'user_id' => $userIdReject,
                    'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                    'message' => 'Order status changed from "on hold" to "new order"',
                    'order_id' => $order->id,
                ];

                SaleOrderHistory::insert($dataHistory);
            }

            SaleOrderItemReject::create([
                'order_id' => $order->id,
                'external_number' => $order->external_number,
                'order_from' => $tag ? 'redbubble.CreateThumbArtworkService' : 'redbubble.rejectDesign',
                'reason' => '"rejected" due to IP Violation',
                'sale_order_item' => json_encode($orderItemReject)
            ]);
            $idOrderItemRejects = collect($orderItemReject)->pluck('id')->toArray();
            $externalOrderItemRejects = collect($orderItemReject)->pluck('external_id')->toArray();
            SaleOrderItem::whereIn('id', $idOrderItemRejects)->delete();
            SaleOrderItemImage::where('order_id', $orderId)->delete();
            SaleOrderItemBarcode::where('order_id', $orderId)->delete();
            $newSaleOrderItemQty = SaleOrderItem::selectRaw('sum(quantity) as quantity')
                ->where('order_id', $orderId)
                ->groupBy('order_id')
                ->first();
            SaleOrder::where('id', $orderId)->update([
                'order_number' => null,
                'order_quantity' => $newSaleOrderItemQty->quantity ?? 0
            ]);
            SaleOrderOnHold::where('order_id', $orderId)
                ->where(function ($query) {
                    $query->where('store_on_hold', true)
                        ->orWhere('manual_on_hold', true);
                })
                ->update(['visua_on_hold' => false]);
            SaleOrderOnHold::where('order_id', $orderId)
                ->where('store_on_hold', false)
                ->where('manual_on_hold', false)
                ->delete();
            $message = 'rejected due to IP Violation. Order external key: ' . $order->external_key . '. Order items rejected: ' . implode(', ', $externalOrderItemRejects);

            $dataUrlCallback = IntegrateLog::where('order_id', $orderId)
                ->where('store_id', $order->store_id)
                ->first();
            $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->first();

            sendGoogleChat($message, $urlGoogleSpace?->value ?? '');
            $dataRebubble = json_decode($dataUrlCallback?->json);
            $urlCallback = $dataRebubble->callback_uri;
            $data = [
                'url_callback' => $urlCallback,
                'external_key' => $order->external_key,
                'item_id_errors' => $externalOrderItemRejects,
                'message' => 'Artwork error',
                'store_id' => $order->store_id,
                'order_id' => $order->id,
                'external_number' => $order->external_key,
                'custom_type' => 'artwork',
            ];
            Log::info('LavenderRepository.rejectOrderRedBubble Reject items', [
                'order id' => $orderId,
                'order items' => $idOrderItemRejects,
            ]);
            handleJob(IntegrateCallbackLog::CALL_BACK_LOG_FAIL_TO_REDBUBBLE, $data);

            // ap dung cho version cu phai dung detect shipping method RB
            $dataRBB = json_decode($dataUrlCallback?->json);

            if ($dataRBB) {
                $shouldHandleJob = $dataRBB->version == '2023-04-11';

                if (!$shouldHandleJob) {
                    foreach ($dataRBB->shipments as $value) {
                        if (
                            $value->shipment_id == $order->external_number
                            && ($value->shipping_info->carrier == 'unknown'
                                || $value->shipping_info->service == 'unknown')
                        ) {
                            $shouldHandleJob = true;

                            break;
                        }
                    }
                }

                if ($shouldHandleJob) {
                    handleJob(SaleOrder::JOB_DETECT_SHIPPING_METHOD_REDBUBBLE, $order->id);
                }
            }
        } else {
            Log::info('LavenderRepository.rejectOrderRedBubble Reject order', [
                'order id' => $orderId,
            ]);
            $this->rejectOrder($orderId, $userIdReject, $tag);
        }
    }

    public function rejectOrder($orderId, $userIdReject, $tag = null)
    {
        $order = SaleOrder::find($orderId);
        $previousStatus = $order->order_status;
        $order->order_status = SaleOrder::REJECTED;
        $order->rejected_reason = SaleOrder::IP_VIOLATION_REJECTED_REASON;
        $order->tag = $tag ?? OrderEditingService::addTag($order->tag, Tag::MANUAL_REJECT_ID);
        $order->rejected_at = now();
        $order->save();

        $dataHistory[] = [
            'user_id' => $userIdReject,
            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
            'message' => 'Order status changed from "' . $previousStatus . ' " to "rejected" due to IP Violation',
            'order_id' => $order->id,
        ];

        SaleOrderHistory::insert($dataHistory);
        SaleOrderOnHold::where('order_id', $order->id)->delete();
    }

    public function rejectOrderRedBubbleWhenDetectSameColor($orderId)
    {
        $order = SaleOrder::find($orderId);
        $previousStatus = $order->order_status;
        SaleOrder::where('id', $orderId)->update([
            'order_status' => SaleOrder::REJECTED,
            'rejected_reason' => SaleOrder::TONE_ON_TONE_REJECTED_REASON,
            'tag' => Tag::SYSTEM_REJECT_ID,
            'rejected_at' => now(),
        ]);
        $dataHistory[] = [
            'user_id' => null,
            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
            'message' => 'Order status changed from "' . $previousStatus . ' " to "rejected" due to Tone on tone',
            'order_id' => $order->id,
        ];
        SaleOrderHistory::insert($dataHistory);

        StatusOrderJob::dispatch($orderId)->onQueue(SaleOrder::JOB_NOTIFY_STATUS_ORDER)->delay(Carbon::now()->addMinutes(5));
    }

    public function rejectOrderItemsRedBubbleWhenDetectSameColor($orderId, $orderItemIdsReject)
    {
        $order = SaleOrder::find($orderId);
        if (!$order) {
            return;
        }
        $externalOrderItemRejects = SaleOrderItem::whereIn('id', $orderItemIdsReject)
            ->pluck('external_id')
            ->toArray();

        $message = sprintf(
            'Rejected due to tone on tone. Order external key: %s. Order items rejected: %s',
            $order->external_key,
            implode(', ', $externalOrderItemRejects),
        );
        SaleOrderItem::whereIn('id', $orderItemIdsReject)->delete();
        SaleOrderItemImage::where('order_id', $orderId)->delete();
        SaleOrderItemBarcode::where('order_id', $orderId)->delete();
        $newQuantity = SaleOrderItem::where('order_id', $orderId)->sum('quantity');

        $order->order_number = null;
        $order->order_quantity = $newQuantity;
        $order->save();
        $dataUrlCallback = IntegrateLog::where([
            'order_id' => $orderId,
            'store_id' => $order->store_id
        ])->first();
        $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->value('value');
        sendGoogleChat($message, $urlGoogleSpace);
        $dataRebubble = json_decode(optional($dataUrlCallback)->json, true);
        $urlCallback = $dataRebubble['callback_uri'] ?? '';
        $data = [
            'url_callback' => $urlCallback,
            'external_key' => $order->external_key,
            'item_id_errors' => $externalOrderItemRejects,
            'message' => 'tone on tone',
            'store_id' => $order->store_id,
            'order_id' => $order->id,
            'external_number' => $order->external_key,
            'custom_type' => 'product',
        ];
        handleJob(IntegrateCallbackLog::CALL_BACK_LOG_FAIL_TO_REDBUBBLE, $data);
    }
}
