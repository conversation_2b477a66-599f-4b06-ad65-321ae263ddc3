<?php

namespace App\Repositories;

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use Carbon\Carbon;

class PickerStationRepository extends CommonRepository
{
    public function throughput($input)
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $date = Carbon::now()->format('Y-m-d');
        $query = SaleOrderItemBarcode::join('barcode_printed', 'sale_order_item_barcode.barcode_printed_id', '=', 'barcode_printed.id')
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::DELETED)
            ->where('barcode_printed.employee_pull_id', Employee::RBT_EMPLOYEE_ID)
            ->where('barcode_printed.print_status', BarcodePrinted::ACTIVE);
        if (!empty($input['date'])) {
            $query->where('barcode_printed.employee_id', $input['employee_id']);
        }

        return $query->count();
    }

    public function getOldestOrderItem($input)
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('location', 'location.rbt_sku', '=', 'sale_order_item.product_sku')
            ->join('location_product', 'location_product.product_id', '=', 'sale_order_item.product_id')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->whereColumn('location.id', '<=', 'location_product.location_id')
            ->where('sale_order.is_test', '=', false)
            ->whereNotIn('sale_order.order_status', [SaleOrder::REJECTED, SaleOrder::ON_HOLD, SaleOrder::DRAFT])
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->whereNull('sale_order_item_barcode.employee_qc_id')
            ->selectRaw('sale_order_item.product_sku as sku, product.name as product_name, count(sale_order_item_barcode.id) as total, min(sale_order_item_barcode.created_at) as created_at, location_product.quantity as available_stock, location.barcode as location_name');

        if (!empty($input['start_date'])) {
            $query->where('sale_order_item.ink_color_detected_at', '>=', $input['start_date']);
        }
        if (!empty($input['warehouse_id'])) {
            $query->where('sale_order.warehouse_id', $input['warehouse_id']);
        }

        return $query->groupBy('sale_order_item.product_sku')->limit(10)->orderByDesc('total')->get();
    }

    public function getCountWIPWithStatus($input)
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->leftJoin('barcode_printed', 'sale_order_item_barcode.barcode_printed_id', '=', 'barcode_printed.id')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->where('sale_order.is_test', '=', false)
            ->whereNotIn('sale_order.order_status', [SaleOrder::REJECTED, SaleOrder::ON_HOLD, SaleOrder::DRAFT])
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->selectRaw('count(IF(barcode_printed.print_status = 1 , 1, null)) as total_completed, count(IF(barcode_printed.print_status <> 1 and sale_order_item_barcode.employee_discard_id is null, 1, null)) as total_pending, count(IF(sale_order_item_barcode.employee_discard_id is not null , 1, null)) as total_discarded');

        if (!empty($input['warehouse_id'])) {
            $query->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id']);
        }
        if (!empty($input['start_date'])) {
            $query->where('sale_order_item.ink_color_detected_at', '>=', $input['start_date']);
        }

        return $query->first();
    }

    public function getCountWIPByStation($input)
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('barcode_printed', 'sale_order_item_barcode.barcode_printed_id', '=', 'barcode_printed.id')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->whereNotNull('barcode_printed.station_id')
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::NOT_DELETED)
            ->where('sale_order.is_test', '=', false)
            ->whereNotIn('sale_order.order_status', [SaleOrder::REJECTED, SaleOrder::ON_HOLD, SaleOrder::DRAFT])
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->selectRaw('barcode_printed.station_id, count(IF(barcode_printed.print_status = 1 , 1, null)) as total_completed, count(IF(barcode_printed.print_status = 0 and sale_order_item_barcode.employee_discard_id is null, 1, null)) as total_pending, count(IF(sale_order_item_barcode.employee_discard_id is not null , 1, null)) as total_discard');

        if (!empty($input['warehouse_id'])) {
            $query->where('sale_order_item_barcode.warehouse_id', $input['warehouse_id']);
        }

        if (!empty($input['start_date'])) {
            $query->where('sale_order_item.ink_color_detected_at', '>=', $input['start_date']);
        }

        $data = $query->groupBy('barcode_printed.station_id')->get();
        $datas = [];
        foreach ($data as $item) {
            $datas[] = [
                'station_name' => $item->station_id,
                'total_completed' => $item->total_completed,
                'total_pending' => $item->total_pending,
                'total_discard' => $item->total_discard,
            ];
        }

        return $datas;
    }
}
