<?php

namespace App\Repositories;

use App\Models\ReportHistory;
use Carbon\Carbon;
use Illuminate\Http\Response;

class ReportRepository extends CommonRepository
{
    public $input;

    public function getList($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = ReportHistory::with('user');

        if (!empty($input['name'])) {
            $query->where('name', 'like', '%' . $input['name'] . '%');
        }

        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        }

        if (!empty($input['start_date'])) {
            $startDate = Carbon::parse($input['start_date'])->startOfDay()->format('Y-m-d H:i:s');
            $query->where('created_at', '>=', $startDate);
        }
        if (!empty($input['end_date'])) {
            $endDate = Carbon::parse($input['end_date'])->endOfDay()->format('Y-m-d H:i:s');
            $query->where('created_at', '<=', $endDate);
        }

        if (!empty($input['user_id'])) {
            $query->where('user_id', $input['user_id']);
        }

        return $query->orderByDesc('id')->paginate($limit);
    }

    public function saleReport($input)
    {
        try {
            $params = $input;
            $filters = $input;
            unset($params['columns']);
            unset($filters['columns']);

            if (isset($params['historyId'])) {
                $reportHistory = ReportHistory::find($params['historyId']);
                $filters = json_decode($reportHistory['filters'], true);
                $ignoreKeys = ['start_date', 'end_date', 'store_id', 'warehouse', 'name', 'order_date'];
                foreach ($ignoreKeys as $key) {
                    unset($filters[$key]);
                }

                $input = array_merge($input, $filters);
            }

            if (!empty($input['name'])) {
                $name = $input['name'];
            } else {
                $time = Carbon::now('America/Los_Angeles')->format('Ymd_His');
                $name = "sales_report_{$time}";
            }
            $data = ReportHistory::create([
                'name' => $name,
                'type' => ReportHistory::SALE_REPORT_TYPE,
                'columns' => json_encode($input['columns'] ?? []),
                'filters' => json_encode($filters ?? []),
                'status' => ReportHistory::PENDING_STATUS,
                'user_id' => auth()->user()->id
            ]);
            $input['report_history_id'] = $data->id;
            $input['name'] = $data->name;

            handleJob(ReportHistory::EXPORT_SALE_REPORT_JOB, $input);

            return $this->successResponse('Success', $data);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
