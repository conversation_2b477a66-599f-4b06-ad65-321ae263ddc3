<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\BoxBarCode;
use App\Models\BoxBarCodePrinted;
use App\Models\BoxMoving;
use App\Models\InternalRequest;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\PurchaseOrderBox;
use App\Models\Warehouse;
use App\Repositories\Contracts\BoxRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Koerel\PdfUnite\PdfUnite;
use Picqer\Barcode\BarcodeGeneratorHTML;
use Validator;

class BoxRepository extends CommonRepository implements BoxRepositoryInterface
{
    const LIMIT = 10;

    private ProductRepositoryInterface $productRepository;

    private LocationRepositoryInterface $locationRepository;

    public function __construct(ProductRepositoryInterface $productRepository, LocationRepositoryInterface $locationRepository)
    {
        $this->productRepository = $productRepository;
        $this->locationRepository = $locationRepository;
    }

    public function fetchAll($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = Box::query()
            ->with(['location', 'product', 'inventoryAddition.country:name,iso2', 'country:name,iso2'])
            ->whereHas('location', function ($q) {
                $q->whereNotIn('type', [Location::PULLING_SHELVES, Location::PENDING_TYPE]);
            })
            ->where('box.warehouse_id', config('jwt.warehouse_id'))
            ->where('box.is_deleted', Box::NOT_DELETED)
            ->active();
        if (!empty($input['barcode'])) {
            $barcode = $input['barcode'];
            $query->where('box.barcode', 'LIKE', '%' . $barcode . '%');
        }

        if (!empty($input['location_id'])) {
            ///Todo : location_id  cho phan Test Count Adjustment
            $query->where('box.location_id', $input['location_id']);

            return $query->get();
        }

        if (!empty($input['type'])) {
            if ($input['type'] === 'product_style') {
                $query->whereHas('product', function ($q) use ($input) {
                    if ($input['style']) {
                        $q->where('style', $input['style']);
                    }
                    if ($input['color']) {
                        $q->where('color', $input['color']);
                    }
                    if ($input['size']) {
                        $q->where('size', $input['size']);
                    }
                });
            } elseif (!empty($input['keyword'])) {
                $keyword = $input['keyword'];
                switch ($input['type']) {
                    case 'box_id':
                        $query->where(function ($q) use ($keyword) {
                            $q->where('box.id', $keyword)
                                ->orWhere('box.barcode', $keyword);
                        });
                        break;
                    case 'location':
                        $query->whereHas('location', function ($q) use ($keyword) {
                            $q->where(function ($q) use ($keyword) {
                                $q->where('id', $keyword)
                                    ->orWhere('barcode', $keyword);
                            });
                        });
                        break;
                    case 'product_sku':
                        $query->whereHas('product', function ($q) use ($keyword) {
                            $q->where('sku', $keyword);
                        });
                        break;
                }
            }
        }

        return $query->orderBy('id', 'desc')->paginate($limit);
    }

    public function create($request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $box = Box::query()
                ->where('warehouse_id', config('jwt.warehouse_id'))
                ->where('barcode', $request['box_id'])
                ->first();
            if ($box) {
                return $this->errorResponse('Box already exists!', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $product = $this->productRepository->getProductByAttribute($request);
            if (!$product) {
                return $this->errorResponse('The product is not found or has been deleted!');
            }
            $location = $this->locationRepository->getDetail($request['location_id']);
            if (!$location) {
                return $this->errorResponse('The location is not found or has been deleted!');
            }
            if ($location->type !== Location::RACK) {
                return $this->errorResponse('The location must be in rack!', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $box = Box::create([
                'warehouse_id' => config('jwt.warehouse_id'),
                'barcode' => $request['box_id'],
                'location_id' => $request['location_id'],
                'product_id' => $product->id,
                'quantity' => $request['quantity'],
                'employee_id' => $request['employee_id']
            ]);
            BoxMoving::create([
                'box_id' => $box->id,
                'location_id' => $request['location_id'],
                'warehouse_id' => config('jwt.warehouse_id'),
                'user_id' => auth()->user()['id'],
                'product_id' => $product->id,
                'quantity' => $request['quantity'],
            ]);
            Inventory::create([
                'direction' => Inventory::DIRECTION_INPUT,
                'type' => Inventory::TYPE_INPUT,
                'product_id' => $product->id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'location_id' => $request['location_id'],
                'user_id' => auth()->user()['id'],
                'object_id' => $box->id,
                'object_name' => 'create box',
                'quantity' => $request['quantity']
            ]);
            ProductQuantityRepository::updateQuantity(config('jwt.warehouse_id'), $product->id, $request['quantity']);
            LocationProductRepository::updateQuantity($request['location_id'], $product->id, $request['quantity']);

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $request['id_time_checking']);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Create box successfully!', $box);
    }

    public function fetch($id)
    {
        return Box::find($id);
    }

    public function update($id, $dataUpdate)
    {
        return Box::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        return Box::where('id', $id)->update(['is_deleted' => 1]);
    }

    public function bulkInsert($input)
    {
        $items = explode("\n", $input['barcode']);
        $totalInsertOk = 0;

        foreach ($items as $item) {
            $data = [
                'warehouse_id' => $input['warehouse_id'],
                'barcode' => trim($item)
            ];
            $validator = Validator::make($data, [
                'barcode' => ['required',
                    Rule::unique('box')->where(function ($query) use ($data) {
                        return $query->where('warehouse_id', $data['warehouse_id'])
                            ->where('barcode', $data['barcode']);
                    })]
            ]);
            if (!$validator->fails()) {
                Box::create($data);
                $totalInsertOk++;
            }
        }

        return response()->json(['totalInsertSuccess' => $totalInsertOk], 201);
    }

    public function getBoxByParams($input)
    {
        return Box::where('is_deleted', 0)
            ->where('warehouse_id', $input['warehouse_id'])
            ->whereNull('location_id')
            ->where('barcode', 'LIKE', '%' . $input['barcode'] . '%')
            ->get();
    }

    public function getOrderBoxByTrackingNumber($trackingNumber, $warehouseId)
    {
        return PurchaseOrderBox::with(['items.product:id,style,size,color', 'purchase_order', 'country'])
            ->whereHas('purchase_order', function ($query) use ($warehouseId) {
                $query->where('warehouse_id', $warehouseId);
            })
            ->where(function ($q) {
                $q->where('status', '!=', PurchaseOrderBox::LOST_STATUS)
                    ->orWhereNull('status');
            })
            ->where('tracking_number', $trackingNumber)
            ->first();
    }

    public function getBoxByBarcode($barcode)
    {
        return DB::table('box')
            ->select('box.*', 'product.name AS product_name', 'location.barcode AS location_name')
            ->join('product', 'box.product_id', '=', 'product.id')
            ->join('location', 'box.location_id', '=', 'location.id')
            ->where('box.barcode', $barcode)
            ->first();
    }

    public function getBoxInLocationForTestCount($input)
    {
        $query = Box::query()
            ->with(['location', 'product'])
            ->whereHas('location', function ($q) {
                $q->where('type', '!=', Location::PULLING_SHELVES);
            })
            ->where('box.warehouse_id', $input['warehouse_id'])
            ->where('box.location_id', $input['location_id'])
            ->active();

        return $query->get()->toArray();
    }

    public function getByBarcodeAndProductId($barcode, $productId)
    {
        return Box::where('barcode', $barcode)
            ->where('product_id', $productId)
            ->where('is_deleted', Box::NOT_DELETED)
            ->first();
    }

    public function fetchBoxInRack($request)
    {
        $limit = !empty($request->limit) ? $request->limit : 10;

        return Box::with(['location:id,barcode', 'product:id,sku,name'])
            ->whereHas('location', function ($query) {
                $query->where('warehouse_id', config('jwt.warehouse_id'))
                    ->where('type', Location::RACK);
            })
            ->where('is_deleted', Box::NOT_DELETED)
            ->where('product_id', $request->product_id)
            ->paginate($limit);
    }

    public function deleteAllBoxInLocation($input, $objectId, $objectName)
    {
        try {
            $totalBox = Box::where('location_id', $input['location_id'])
                ->where('product_id', $input['product_id'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->active()
                ->get();

            if ($totalBox->count() == 0) {
                return $this->handleFail('Not box in location');
            }

            DB::beginTransaction();
            foreach ($totalBox as $itemBox) {
                Box::where('id', $itemBox->id)->update(['is_deleted' => 1]);
                $inputInventory = [
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_ADJUST,
                    'product_id' => $itemBox->product_id,
                    'warehouse_id' => $input['warehouse_id'],
                    'location_id' => $input['location_id'],
                    'user_id' => $input['user_id'],
                    'box_id' => $itemBox->id,
                    'object_id' => $objectId,
                    'object_name' => $objectName,
                    'quantity' => $itemBox->quantity,
                ];
                Inventory::create($inputInventory);
                if (!empty($itemBox->product_id)) {
                    LocationProductRepository::updateQuantity($itemBox->location_id, $inputInventory['product_id'], -1 * $inputInventory['quantity']);
                    ProductQuantityRepository::updateQuantity($input['warehouse_id'], $inputInventory['product_id'], -1 * $inputInventory['quantity']);
                    // Todo : log box notfound in to table box_moving with location_id = null
                    $InputBoxMoving = [
                        'box_id' => $itemBox->id,
                        'location_id' => null,
                        'warehouse_id' => $input['warehouse_id'],
                        'user_id' => $input['user_id'],
                        'product_id' => $inputInventory['product_id'],
                        'quantity' => $itemBox->quantity,
                        'pre_location_id' => $input['location_id'],
                    ];
                    BoxMoving::create($InputBoxMoving);
                }
            }
            DB::commit();

            return $this->handleSuccess('success');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->handleFail($exception->getMessage());
        }
    }

    public function getBoxByLocationAndProduct($productId, $locationId, $warehouseId)
    {
        try {
            $boxs = Box::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('location_id', $locationId)
                ->active()
                ->get();

            if ($boxs->count() == 0) {
                return $this->handleFail('Not box in location');
            }

            return $this->handleSuccess('success', $boxs);
        } catch (\Exception $exception) {
            return $this->handleFail($exception->getMessage());
        }
    }

    public function generateBoxIDs($warehouseId)
    {
        $prefix = ''; // Initialize prefix variable

        // Determine prefix based on warehouse code
        switch ($warehouseId) {
            case Warehouse::WAREHOUSE_SANJOSE_ID:
                $prefix = 'SJ';
                break;
            case Warehouse::WAREHOUSE_TEXAS_ID:
                $prefix = 'TX';
                break;
            case Warehouse::WAREHOUSE_NEVADA_ID:
                $prefix = 'LV';
                break;
            case Warehouse::WAREHOUSE_MEXICO_ID:
                $prefix = 'MX';
                break;
            case Warehouse::WAREHOUSE_VIRGINIA_ID:
                $prefix = 'VA';
                break;
            default:
                $prefix = '';
                break;
        }

        $latestBox = BoxBarCode::where('warehouse_id', $warehouseId)
            ->orderBy('id', 'desc')
            ->first();

        if (!$latestBox) {
            $latestNumber = 0;
        } else {
            $latestNumber = intval(substr($latestBox->box_barcode, 2));
        }

        // Generate 50 Box IDs
        for ($i = 0; $i < 1000; $i++) {
            $nextNumber = $latestNumber + $i + 1;
            $nextNumberString = str_pad($nextNumber, 6, '0', STR_PAD_LEFT); // Ensure 6 digits
            $generatedID = $prefix . $nextNumberString;
            $generatedBoxesBarcode[] = [
                'box_barcode' => $generatedID,
                'warehouse_id' => $warehouseId,
            ];
        }

        // Insert generated boxes into the database
        if (!empty($generatedBoxesBarcode)) {
            $generatedIDsCollection = collect($generatedBoxesBarcode);
            $firstGeneratedID = $generatedIDsCollection->first();
            $lastGeneratedID = $generatedIDsCollection->last();
            $boxBarCodePrinted = [
                'warehouse_id' => $warehouseId,
                'first_box' => $firstGeneratedID['box_barcode'],
                'last_box' => $lastGeneratedID['box_barcode'],
                'employee_id' => Auth::user() ? Auth::user()->id : null

            ];
            $boxBarCodePrinted = BoxBarCodePrinted::create($boxBarCodePrinted);
            $generatedBoxesBarcode = array_map(fn ($item) => array_merge($item, ['box_barcode_printed_id' => $boxBarCodePrinted->id]), $generatedBoxesBarcode);
            BoxBarCode::insert($generatedBoxesBarcode);
            // $this->convertBoxIds($boxBarCodePrinted->id);
            handleJob(Box::JOB_CONVERT_BOX_ID_TO_PDF, $boxBarCodePrinted->id);
        }

        return $boxBarCodePrinted; // Return the array of generated Box records
    }

    public function ListGenerateBox($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $data = BoxBarCodePrinted::where('warehouse_id', $input['warehouse_id'])
            ->orderBy('id', 'desc')->paginate($limit);
        foreach ($data as $item) {
            $fileName = "/box_barcode/$item->id.pdf";
            $item->link_url = env('AWS_S3_URL') . "$fileName?v=" . rand();
        }

        return $data;
    }

    public function convertBoxIds($id)
    {
        $view = 'barcode_box';
        set_time_limit(0);
        ini_set('memory_limit', '10G');
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);

        $log_error = storage_path('logs/pdf.txt');
        try {
            $barcodePrinted = $this->getBarcodePrintedById($id);
            $items = $this->getItemBarcodeById($id);
            $total_item = count($items);
            $render = [];
            $barcode_ids = [];
            $file_number = 0;
            $path = storage_path('app/public/box_barcode');

            // Create the directory if it doesn't exist
            if (!File::exists($path)) {
                File::makeDirectory($path, 07777, true);
            }
            foreach ($items as $key => &$item) {
                $qr = $this->generateCode128Barcode($item->box_barcode); // Set width and height here
                $item->barcode_qr = $qr;
                $render[] = $item;
                if (count($render) == 50 || $key + 1 == $total_item) {
                    app()->make('view.engine.resolver')->register('blade', function () {
                        return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
                    });

                    $pdf = PDF::loadView($view, ['items' => $render])
                        ->setOptions(['dpi' => 203, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => storage_path('logs/')])
                        ->setPaper([0, 0, 2.25 * 72, 1.25 * 72]); // inch to point
                    $save_file = "$path/{$barcodePrinted->id}.pdf";
                    $pdf->setWarnings(true)->save($save_file);
                    $pdf = null;
                    $merge_file[] = $save_file;
                    $render = [];
                    $file_number++;
                }
            }
            if (count($merge_file) > 1) {
                $unite = new PdfUnite();
                $merge_file[] = "$path/$barcodePrinted->id.pdf"; // out put
                $unite->join(...$merge_file);
                foreach ($merge_file as $key => $item) {
                    if ($key + 1 == count($merge_file)) {
                        break;
                    }
                    echo $item . "\n";
                    unlink($item);
                }
            }
            $pathPdf = "$path/$barcodePrinted->id.pdf";

            if (file_exists("$path/$barcodePrinted->id.pdf")) {
                $s3 = Storage::disk('s3')->put("/box_barcode/$barcodePrinted->id.pdf", file_get_contents($pathPdf));
                if ($s3) {
                    $this->updateBoxBarcodePrinted($barcodePrinted->id, [
                        'convert_percent' => 50,
                        'convert_status' => 1,
                        'converted_at' => date('Y-m-d H:i:s'),
                        'print_status' => 0,
                    ]);

                    $this->updateBoxBarcode($barcode_ids, [
                        'convert_pdf_status' => 1,
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
                    unset($s3);
                    File::delete($pathPdf);
                } else {
                    return "upload to s3 fail $barcodePrinted->id\n";
                }
            } else {
                return "error convert $barcodePrinted->id\n";
            }
        } catch (\Exception $e) {
            // Handle exceptions
            file_put_contents($log_error, date('Y-m-d H:i:s'), FILE_APPEND);
            file_put_contents($log_error, $e->getMessage(), FILE_APPEND);
            file_put_contents($log_error, $e->getTraceAsString(), FILE_APPEND);
        }
    }

    public function generateCode128Barcode($text)
    {
        $barcodeGenerator = new BarcodeGeneratorHTML();
        $barcodeHtml = $barcodeGenerator->getBarcode($text, $barcodeGenerator::TYPE_CODE_128, 3, 120);
        $barcodeHtmlWithSize = "<div>$barcodeHtml</div>";

        return $barcodeHtmlWithSize;
    }

    public function getItemBarcodeById($id)
    {
        return DB::table('box_barcode')
            ->where('box_barcode_printed_id', $id)
            ->get();
    }

    public function getBarcodePrintedById($id)
    {
        return DB::table('box_barcode_printed')
            ->where('id', $id)
            ->first();
    }

    public function updateBoxBarcodePrinted($id, $data)
    {
        try {
            return DB::table('box_barcode_printed')
                ->where('id', $id)->update($data);
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    public function updateBoxBarcode($ids, $data)
    {
        try {
            return DB::table('box_barcode')
                ->whereIn('id', $ids)->update($data);
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    public function updatePrintStatusBoxId($id)
    {
        $boxBarCodePrinted = BoxBarCodePrinted::find($id);
        $boxBarCodePrinted->print_status = 1;
        $boxBarCodePrinted->save();
    }

    public function searchBoxByBarcode($barcode, $warehouseId)
    {
        return Box::where('is_deleted', Box::NOT_DELETED)
            ->where('warehouse_id', $warehouseId)
            ->where('barcode', 'LIKE', '%' . $barcode . '%')
            ->get();
    }

    public function buildCollectionExportBox($params)
    {
        $query = Box::query()
            ->join('location', 'location.id', 'box.location_id')
            ->join('product', 'product.id', 'box.product_id')
            ->whereNotIn('location.type', [Location::PULLING_SHELVES, Location::PENDING_TYPE])
            ->where('box.warehouse_id', $params['warehouse_id'])
            ->where('box.is_deleted', Box::NOT_DELETED);

        if ($params['type'] === 'product_style') {
            if (isset($params['style'])) {
                $query->where('product.style', $params['style']);
            }
            if (isset($params['size'])) {
                $query->where('product.size', $params['size']);
            }
            if (isset($params['color'])) {
                $query->where('product.color', $params['color']);
            }
        } elseif ($params['type'] === 'product_sku') {
            $query->where('product.sku', $params['keyword']);
        }

        return $query->active()
            ->select([
                'box.id as box_id',
                'product.sku',
                'product.name',
                'location.id as location_id',
                'location.barcode',
                'box.quantity',
                'box.cost_value',
            ])
            ->orderBy('product.sku', 'ASC')
            ->get();
    }

    public function getBoxNeedMovingToRack($params)
    {
        $query = Box::with(['internalRequest.countSticker'])
            ->where('warehouse_id', $params['warehouse_id'])
            ->whereHas('internalRequest', function ($q) {
                $q->where('status', InternalRequest::CHECKED_STATUS)
                    ->where('is_rbt', InternalRequest::IS_RBT)
                    ->whereNull('new_box_id')
                    ->whereColumn('box.quantity', '<>', 'internal_request.dark_pod_quantity');
            });

        return $query->get();
    }
}
