<?php

namespace App\Repositories;

use App\Models\PartNumber;
use App\Models\PartNumberHistory;
use Illuminate\Support\Facades\DB;

class PartNumberOverviewRepository extends CommonRepository
{
    public function fetchAll($params)
    {
        $query = $this->buildQuery($params);

        return $query->latest('id')->paginate($params['limit'] ?? self::LIMIT);
    }

    public function buildQuery($params)
    {
        return PartNumber::search($params)
            ->with(['product:id,style', 'partNumberCountry:name,iso2', 'productSpec:id,product_id,fabric_content'])
            ->withCount([
                'partNumberFifos as total_balance' => function ($query) {
                    $query->select(DB::raw('SUM(quantity)'))
                        ->where('warehouse_id', config('jwt.warehouse_id'));
                },
                'partNumberHistory as total_import' => function ($query) {
                    $query->select(DB::raw('SUM(quantity)'))
                        ->where('type', PartNumberHistory::TYPE_IMPORT)
                        ->where('warehouse_id', config('jwt.warehouse_id'));
                },
                'partNumberHistory as total_export' => function ($query) {
                    $query->select(DB::raw('SUM(quantity)'))
                        ->where('type', PartNumberHistory::TYPE_EXPORT)
                        ->where('warehouse_id', config('jwt.warehouse_id'));
                },
            ]);
    }

    public function history($params, $partNumber)
    {
        $partNumberData = PartNumber::where('part_number', $partNumber)->firstOrFail();

        $query = PartNumberHistory::search($params)
            ->where('part_number_id', $partNumberData->id)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->with(['employee:id,name']);

        return $query->latest('id')->paginate($params['limit'] ?? self::LIMIT);
    }
}
