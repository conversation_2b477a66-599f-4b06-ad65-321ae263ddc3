<?php

namespace App\Repositories;

use App\Models\Tag;
use App\Repositories\Contracts\TagRepositoryInterface;

class TagRepository implements TagRepositoryInterface
{
    public function fetchAll($input)
    {
        $query = Tag::with('account:id,name');
        if (!empty($input['source'])) {
            $query->where('source', $input['source']);
        }

        return $query->get()->map(function ($tag) {
            if ($tag->id === TAG::NEW_CUSTOMER_HANDLING_TAG_ID) {
                $tag->name = TAG::NEW_CUSTOMER_HANDLING_TAG;
            }
            return $tag;
        });
    }

    public function create($input)
    {
        return Tag::create($input);
    }

    public function update($id, $dataUpdate)
    {
        return Tag::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        return Tag::where('id', $id)->delete();
    }

    public function tagRestrictRemove()
    {
        return Tag::select('id', 'name')
            ->where('is_additional_service', true)
            ->whereNotNull('surcharge_service_id')
            ->where('name', '!=', 'Hologram Stickers')
            ->get();
    }
}
