<?php

namespace App\Repositories;

use App\Models\Setting;
use App\Repositories\Contracts\SettingRepositoryInterface;

class SettingRepository implements SettingRepositoryInterface
{
    const LIMIT = 10;

    public function fetchAll($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;

        return Setting::orderBy('id', 'desc')->paginate($limit);
    }

    public function fetchByKey()
    {
        $key = ['enable_animation', 'animation_image_url1', 'animation_image_url2', 'login_background_image', 'login_holiday_image'];

        return Setting::whereIn('label', $key)->get();
    }

    public function fetch($id)
    {
        return Setting::find($id);
    }

    public function delete($id)
    {
        Setting::where('id', $id)->delete();

        return response()->json(['message' => 'success'], 200);
    }

    public function create($input)
    {
        return Setting::create($input);
    }

    public function update($id, $dataInput)
    {
        Setting::where('id', $id)->update($dataInput);

        return response()->json(['message' => 'success'], 200);
    }

    public function updateOrCreate(string $name, array $data)
    {
        return Setting::updateOrCreate(['name' => $name], $data);
    }

    public function getSettingByName($name)
    {
        return Setting::where('name', $name)->first();
    }

    public function checkIpBacklog($ip)
    {
        $ipAllowBacklog = Setting::where('label', Setting::BACKLOG_LABEL)->first();
        $stringIp = str_replace(' ', '', $ipAllowBacklog->value ?? '');
        $listIp = explode(',', $stringIp);

        if (in_array($ip, $listIp)) {
            return ['status' => true];
        }

        return ['status' => false];
    }
}
