<?php

namespace App\Repositories;

use App\Models\Printer;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrderFulfillmentTotalByMonth;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\StoreStyle;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductStyleRepository extends CommonRepository
{
    public function getList(Request $request)
    {
        $query = ProductStyle::search($request)
            ->with('printers:id,name');

        return $query->latest('id')
            ->paginate($request['limit'] ?? self::LIMIT);
    }

    public function fetchAll()
    {
        return ProductStyle::with('productType:name,is_hard_goods')->get();
    }

    private function validateData(Request $request, int $id = null)
    {
        return $this->validation($request->all(), [
            'name' => "required|max:255|unique:product_style,name,$id,id",
            'sku' => "required|max:255|unique:product_style,sku,$id,id",
            'description' => 'required|max:255',
            'type' => 'required|exists:product_type,name',
            'gender' => 'max:50',
            'print_method' => 'required|exists:print_method,name',
            'sleeve_length' => 'max:255',
        ]);
    }

    public function getProductPrintArea($input)
    {
        $style = ProductStyle::where('name', $input['name'])->first();

        return $style->productPrintAreas;
    }

    private function prepareData($request): array
    {
        return [
            'name' => $request['name'],
            'sku' => $request['sku'],
            'type' => $request['type'] ?? null,
            'gender' => $request['gender'] ?? null,
            'print_method' => $request['print_method'] ?? null,
            'sleeve_length' => $request['sleeve_length'] ?? null,
            'description' => $request['description'] ?? null,
            'icc_white_convert_status' => $request['icc_white_convert_status'] ?? null,
        ];
    }

    public function create($request): JsonResponse
    {
        try {
            $validatorRes = $this->validateData($request);

            if ($validatorRes !== true) {
                return $validatorRes;
            }

            $data = $this->prepareData($request);
            $productStyle = ProductStyle::create($data);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Create product style successfully!', $productStyle);
    }

    public function update($id, $request): JsonResponse
    {
        try {
            $productStyle = ProductStyle::find($id);

            if (!$productStyle) {
                return $this->errorResponse('Product style not found!');
            }

            $validatorRes = $this->validateData($request, $id);

            if ($validatorRes !== true) {
                return $validatorRes;
            }

            $data = $this->prepareData($request);

            foreach ($data as $key => $value) {
                $productStyle[$key] = $value;
            }

            $productStyle->save();
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update product style successfully!', $productStyle);
    }

    public function getType()
    {
        return ProductStyle::select('type')
            ->whereNotNull('type')
            ->groupBy('type')
            ->get();
    }

    public function getTypes()
    {
        return ProductType::query()->get();
    }

    public function getPrintMethod()
    {
        return PrintMethod::query()->get();
    }

    public function searchType()
    {
        return ProductStyle::get();
    }

    public function exportSkuExcelByStyle($request)
    {
        $storeId = $request['store_id'];
        $storeProductPrices = StoreProduct::where('store_id', $storeId)
            ->with(['productPrintArea:id,name'])
            ->get();
        $productPrices = [];

        foreach ($storeProductPrices as $key => $item) {
            $key = isset($item->productPrintArea->name) ? $item->productPrintArea->name : 0;

            if (empty($productPrices[$item->product_id][$key])) {
                $productPrices[$item->product_id][$key] = [
                    'price' => $item->price,
                    'handling_fee' => $item->handling_fee,
                    'print_price' => $item->print_price,
                    'print_surcharge' => $item->print_surcharge,
                ];
            }
        }

        $productPrintArea = ProductPrintSide::pluck('name', 'id');
        $printOfExcel = $productPrintArea->values()->unique();

        if (!empty($request['sku'])) {
            $products = Product::with(['children' => function ($q) {
                $q->select('id', 'sku', 'parent_id');
            }])
                ->whereIn('sku', explode(',', $request['sku']))
                ->where('is_deleted', 0)
                ->get();
        } else {
            $products = Product::with(['children:id,sku,parent_id'])
                ->where('is_deleted', 0)
                ->get();
        }

        return $products->pluck('children')->flatMap(function ($item) {
            return $item;
        })->map(function ($item) use ($productPrintArea, $productPrices, $printOfExcel, $storeId) {
            $productId = $item->id;

            if (empty($productPrices[$productId])) {
                $result = collect([
                    StoreProduct::EXCEL_SKU => $item->sku,
                    StoreProduct::EXCEL_BLANK => null,
                    in_array($storeId, StoreProductRepository::STORE_USE_SHRINKAGE_COST)
                        ? StoreProduct::EXCEL_SHRINKAGE_COST
                        : StoreProduct::EXCEL_HANDLING_FEE => null,
                    StoreProduct::EXCEL_PRINT_SURCHARGE => null,
                ]);

                $printPriceOld = $printOfExcel->flatMap(function ($item) {
                    return [$item => null];
                });
                $result = $result->merge($printPriceOld);
            } else {
                $result = collect([
                    StoreProduct::EXCEL_SKU => $item->sku,
                    StoreProduct::EXCEL_BLANK => !empty($productPrices[$productId][0]['price']) ? $productPrices[$productId][0]['price'] : 0,
                    in_array($storeId, StoreProductRepository::STORE_USE_SHRINKAGE_COST)
                        ? StoreProduct::EXCEL_SHRINKAGE_COST
                        : StoreProduct::EXCEL_HANDLING_FEE => !empty($productPrices[$productId][0]['handling_fee']) ? $productPrices[$productId][0]['handling_fee'] : 0,
                    StoreProduct::EXCEL_PRINT_SURCHARGE => $productPrices[$productId][0]['print_surcharge'] != 0 ? $productPrices[$productId][0]['print_surcharge'] : null,
                ]);
                $keyPrintAreaIdOfProduct = array_keys($productPrices[$productId]);
                $productPrintArea->each(function ($item, $key) use (&$result, $productPrices, $productId, $keyPrintAreaIdOfProduct) {
                    if (in_array($item, $keyPrintAreaIdOfProduct)) {
                        $result = $result->put($item, $productPrices[$productId][$item]['print_price']);
                    } else {
                        $result = $result->put($item, null);
                    }
                });
            }

            return $result;
        });
    }

    private function formatDataExport($data)
    {
        $results = [];
        $result = [
            StoreShipment::EXCEL_SERVICE_TYPE => $data['service'],
            StoreShipment::EXCEL_PRODUCT_TYPE => $data['type'],
            StoreShipment::EXCEL_SIZE => null,
            StoreShipment::EXCEL_PRICE => $data['shipments'][$data['type'] . $data['char'] . $data['shippingType'] . $data['char'] . $data['service']]['price'] ?? null,
            StoreShipment::EXCEL_PRICE_ADDITION => $data['shipments'][$data['type'] . $data['char'] . $data['shippingType'] . $data['char'] . $data['service']]['addition_price'] ?? null
        ];

        if (strtoupper($data['type']) == StoreShipment::TYPE_MUGS) {
            foreach (StoreShipment::SKUS_SIZE_MUG as $skuMug) {
                $result[StoreShipment::EXCEL_SIZE] = $skuMug;
                $result[StoreShipment::EXCEL_PRICE] = $data['shipments'][$data['type'] . $data['char'] . $data['shippingType'] . $data['char'] . $data['service'] . $skuMug]['price'] ?? null;
                $result[StoreShipment::EXCEL_PRICE_ADDITION] = $data['shipments'][$data['type'] . $data['char'] . $data['shippingType'] . $data['char'] . $data['service'] . $skuMug]['addition_price'] ?? null;
                $results[] = array_merge([
                    StoreShipment::EXCEL_SHIPPING_TYPE => $data['shippingType']
                ], $result);
            }
        } else {
            $results[] = array_merge([
                StoreShipment::EXCEL_SHIPPING_TYPE => $data['shippingType']
            ], $result);
        }

        return $results;
    }

    public function exportShippingByStyle($request)
    {
        $store = Store::find($request['store_id']);

        if (!$store->is_calculate_shipping) {
            return [];
        }

        $char = '|-|';
        $types = ProductType::select('name')->get()->pluck('name');
        $shipments = StoreShipment::where('store_id', $request['store_id'])
            ->get()
            ->mapWithKeys(function ($item) use ($char) {
                return [
                    $item->product_type . $char . $item->destination . $char
                    . $item->service_type . (empty($item->size) ? '' : $item->size) => [
                        'price' => $item->price,
                        'addition_price' => $item->addition_price
                    ]
                ];
            });

        return $types->flatMap(function ($type) use ($char, $shipments) {
            $results = [];

            foreach (StoreShipment::allServiceType() as $serviceType) {
                if (!in_array($serviceType, [StoreShipment::SERVICE_STANDARD, StoreShipment::SERVICE_FIRST_CLASS])) {
                    $results = array_merge($results, $this->formatDataExport([
                        'shipments' => $shipments,
                        'char' => $char,
                        'shippingType' => StoreShipment::DOMESTIC,
                        'service' => $serviceType,
                        'type' => $type,
                    ]));

                    continue;
                }

                foreach (StoreShipment::allShippingType() as $shippingType) {
                    $results = array_merge($results, $this->formatDataExport([
                        'shipments' => $shipments,
                        'char' => $char,
                        'shippingType' => $shippingType,
                        'service' => $serviceType,
                        'type' => $type,
                    ]));
                }
            }

            return $results;
        });
    }

    public function pingPrinter($input)
    {
        $printerName = $input['printer_name'];
        $warehouseId = $input['warehouse_id'];
        $printerIds = Printer::findByArrayNameAndWarehouse($printerName, $warehouseId);

        if (!empty($printerIds)) {
            Printer::whereIn('id', $printerIds)->update(['last_printer_active_at' => Carbon::now()]);
        }
    }

    public function getTypesForSeller()
    {
        return SaleOrderFulfillmentTotalByMonth::select('product_type')
            ->whereNotNull('total_orders')
            ->groupBy('product_type')
            ->get();
    }

    public function getStyleByStore($storeId): Collection
    {
        $query = DB::table('store_product_price')
            ->select('product_style.*')
            ->join('product', 'product.id', '=', 'store_product_price.product_id')
            ->join('product_style', 'product_style.name', '=', 'product.style')
            ->where('store_product_price.store_id', $storeId)
            ->join('store_style', function ($join) use ($storeId) {
                $join->on('product.style', '=', 'store_style.style')
                    ->where('store_style.store_id', $storeId);
            })
            ->where('store_product_price.status', StoreProduct::STATUS_ACTIVE)
            ->where('store_style.status', StoreStyle::STATUS_ACTIVE)
            ->groupBy('product_style.id')
            ->orderBy('product_style.name');
        $data = $query->get();

        if (!empty(request()->input('debug'))) {
            Log::info('ProductStyleRepository.getStyleByStore query', [interpolateQuery($query)]);
        }

        return $data;
    }
}
