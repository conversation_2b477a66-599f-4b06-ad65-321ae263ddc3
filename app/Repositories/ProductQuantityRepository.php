<?php

namespace App\Repositories;

use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrderItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductQuantityRepository
{
    public static function updateQuantity($warehouse_id, $product_id, $quantity = 0)
    {
        // $previousStock = self::getStock([$product_id]);
        $productQuantity = ProductQuantity::where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id)
            ->first();
        if (!empty($productQuantity)) {
            ProductQuantity::where('id', $productQuantity->id)
                ->update(['quantity' => DB::raw("quantity + $quantity")]);
        //->update(['quantity' => $productQuantity->quantity + $quantity]);
        } else {
            DB::table('product_quantity')->insert([
                'warehouse_id' => $warehouse_id,
                'product_id' => $product_id,
                'quantity' => $quantity,
            ]);
        }
        // $afterStock = self::getStock([$product_id]);
        // self::verifyStockNotify($previousStock, $afterStock);
        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);
    }

    public static function updateQuantityAndIncomeQuantity($warehouse_id, $product_id, $quantity = 0, $income_quantity = 0)
    {
        // $previousStock = self::getStock([$product_id]);
        $productQuantity = ProductQuantity::where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id)
            ->first();
        if (!empty($productQuantity)) {
            $income_quantity = $productQuantity->incoming_stock + $income_quantity;
            $income_quantity = $income_quantity > 0 ? $income_quantity : 0;
            $quantity = $productQuantity->quantity + $quantity;

            $dataUpdate = [
                'quantity' => $quantity,
                'incoming_stock' => $income_quantity,
            ];
            ProductQuantity::where('id', $productQuantity->id)->update($dataUpdate);
        } else {
            $income_quantity = $income_quantity > 0 ? $income_quantity : 0;

            DB::table('product_quantity')->insert([
                'warehouse_id' => $warehouse_id,
                'product_id' => $product_id,
                'quantity' => $quantity,
                'incoming_stock' => $income_quantity,
            ]);
        }

        // $afterStock = self::getStock([$product_id]);
        // self::verifyStockNotify($previousStock, $afterStock);
        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);
        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);
    }

    public static function updateIncomingByPurchaseOrder($orderId)
    {
        $orderItems = PurchaseOrderItem::query()->where('po_id', $orderId)->get();
        $ids = $orderItems->pluck('product_id')->toArray();
        // $previousStock = self::getStock($ids);
        foreach ($orderItems as $orderItem) {
            handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING, ['product_id' => $orderItem->product_id, 'warehouse_id' => config('jwt.warehouse_id')]);
            handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $orderItem->product_id, 'warehouse_id' => config('jwt.warehouse_id')]);
        }
        // $afterStock = self::getStock($ids);
        // self::verifyStockNotify($previousStock, $afterStock);
    }

    public static function updateInComeQuantity($warehouse_id, $product_id, $income_quantity = 0)
    {
        // $previousStock = self::getStock([$product_id]);
        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);

        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);
    }

    public static function updateIncomingByPurchaseOrderInWarehouse($orderId, $warehouseId)
    {
        $orderItems = PurchaseOrderItem::query()->where('po_id', $orderId)->get();
        $ids = $orderItems->pluck('product_id')->toArray();
        // $previousStock = self::getStock($ids);

        foreach ($orderItems as $orderItem) {
            handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY_INCOMING, ['product_id' => $orderItem->product_id, 'warehouse_id' => $warehouseId]);
            handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $orderItem->product_id, 'warehouse_id' => $warehouseId]);
        }

        // $afterStock = self::getStock($ids);
        // self::verifyStockNotify($previousStock, $afterStock);
    }

    public static function getStock(array $ids)
    {
        return ProductQuantity::select('product_id', DB::raw('SUM(quantity + incoming_stock) as stock'))
            ->whereIn('product_id', $ids)
            ->groupBy('product_id')
            ->pluck('stock', 'product_id')
            ->toArray();
    }

    public static function verifyStockNotify($previousStock, $afterStock)
    {
        $data = [];
        foreach ($afterStock as $productId => $stock) {
            if ($stock <= 0) {
                $data[Product::SKU_OUT_OF_STOCK][] = $productId;

                continue;
            }

            if ((!isset($previousStock[$productId]) || $previousStock[$productId] <= 0) && $stock > 0) {
                $data[Product::SKU_BACK_IN_STOCK][] = $productId;

                continue;
            }
        }

        if (!empty($data[Product::SKU_OUT_OF_STOCK])) {
            self::stockNotify($data[Product::SKU_OUT_OF_STOCK], Product::SKU_OUT_OF_STOCK);
        }

        if (!empty($data[Product::SKU_BACK_IN_STOCK])) {
            self::stockNotify($data[Product::SKU_BACK_IN_STOCK], Product::SKU_BACK_IN_STOCK);
        }
    }

    public static function stockNotify(array $ids, string $event = null, int $storeId = null)
    {
        $dataInsert = [];
        Product::select(['id', 'sku', 'name', 'style', 'brand_id', 'color', 'size'])
            ->whereIn('id', $ids)
            ->with([
                'productQuantities' => function ($q) {
                    $q->select('id', 'product_id', DB::raw('SUM(quantity + incoming_stock) as stock'))
                        ->groupBy('product_id');
                }, 'brand:id,name', 'productStyle.productPrintAreas:id,name,product_style_id',
            ])
            ->get()
            ->map(function ($item) use ($event, &$dataInsert, $storeId) {
                $stock = 0;
                if (count($item->productQuantities) > 0 && $item->productQuantities->first()->stock > 0) {
                    $stock = $item->productQuantities->first()->stock >= 250 ? 9999 : $item->productQuantities->first()->stock;
                }
                $status = Product::STATUS_DISCONTINUED;
                if ($event !== Product::SKU_DISCONTINUED) {
                    $status = $stock > 0 ? Product::STATUS_IN_STOCK : Product::STATUS_OUT_OF_STOCK;
                }
                $printAreas = [];
                if ($item->productStyle?->productPrintAreas) {
                    $printAreas = $item->productStyle?->productPrintAreas->pluck('name')->toArray();
                    foreach ($printAreas as &$printArea) {
                        $printArea = strtolower(str_replace(' ', '_', $printArea));
                    }
                }
                $dataEvent = [
                    'uuid' => Str::uuid(),
                    'name' => 'stock_notify',
                    'timestamp' => Carbon::now()->toISOString(),
                ];
                $dataProduct = [
                    'id' => $item->id,
                    'name' => $item->brand?->name ?? null,
                    'sku' => $item->sku,
                    'color' => $item->color ?? null,
                    'style' => $item->style ?? null,
                    'size' => $item->size ?? null,
                    'brand' => $item->brand?->name ?? null,
                    'print_areas' => $printAreas ?? null,
                    'stock' => $stock,
                    'status' => $status,
                ];

                $payload = json_encode([
                    'event' => $dataEvent,
                    'data' => $dataProduct,
                ]);
                $dataInsert[] = [
                    'product_id' => $item->id,
                    'status' => false,
                    'payload' => $payload,
                    'store_id' => $storeId,
                ];
            });
        DB::table('stock_notify')->insert($dataInsert);
    }

    public static function updateQuantityRbt($warehouse_id, $product_id, $quantity = 0)
    {
        $productQuantity = ProductQuantity::where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id)
            ->first();
        if (!empty($productQuantity)) {
            ProductQuantity::where('id', $productQuantity->id)
                ->update(['quantity' => DB::raw("quantity + $quantity")]);
        //ProductQuantity::where('id', $productQuantity->id)->update(['quantity' => $productQuantity->quantity + $quantity]);
        } else {
            DB::table('product_quantity')->insert([
                'warehouse_id' => $warehouse_id,
                'product_id' => $product_id,
                'quantity' => $quantity,
            ]);
        }
        handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $product_id, 'warehouse_id' => $warehouse_id]);
    }
}
