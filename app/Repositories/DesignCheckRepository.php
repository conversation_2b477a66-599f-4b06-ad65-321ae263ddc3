<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DesignCheckRepository extends CommonRepository
{
    public function getList($rawData)
    {
        $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
        $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
        setTimezone();
        $startImageId = 18877001;

        $query = SaleOrderItemImage::with(['trademarkImages', 'tags'])
            ->select('sale_order_item_image.*', 'sale_order.external_number as external_id', 'sale_order.order_number as order_number', 'store.code as store_name', 'sale_order.order_status')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->join('store', 'store.id', '=', 'sale_order_item_image.store_id')
            ->join('image_hash', 'image_hash.id', '=', 'sale_order_item_image.image_hash_id')
            ->where('sale_order.is_test', '=', 0)
            ->whereNotIn('sale_order.store_id', $storeExcludeCheckIP)
            ->where('sale_order_item_image.thumb_750', '=', 1)
            ->where('sale_order_item_image.id', '>', $startImageId);

        if (isset($rawData['store']) && $rawData['store']) {
            $query->where('sale_order_item_image.store_id', '=', $rawData['store']);
        }
        if (isset($rawData['sku']) && $rawData['sku']) {
            $query->where('sale_order_item_image.sku', $rawData['sku']);
        }
        if (!empty($rawData['order_type'])) {
            $query->where('sale_order.order_type', $rawData['order_type']);
        }
        if (isset($rawData['selected_date']) && $rawData['selected_date']) {
            $query->where('sale_order_item_image.order_date', $rawData['selected_date']);
        } else {
            $startTime = Carbon::now('UTC')->setTimezone('America/Los_Angeles')->subMonth()->toDateTimeString();
            $endTime = Carbon::now('UTC')->setTimezone('America/Los_Angeles')->subHours(2)->toDateTimeString();

            $query->whereBetween('sale_order_item_image.created_at', [$startTime, $endTime]);
        }
        if (empty($rawData['tab']) || $rawData['tab'] == 'manual') {
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            } else {
                $query->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]);
            }
            $query->leftJoin('visua_detect_image', 'visua_detect_image.image_id', '=', 'sale_order_item_image.id')
                ->where(function ($q) {
                    $q->whereNull('visua_detect_image.id')
                        ->orWhere(function ($q1) {
                            $q1->where('visua_detect_image.is_ip_violation', '!=', 1);
                        });
                })
                ->whereNull('image_hash.is_user_check_ip_violation');
        } elseif ($rawData['tab'] == 'visua') {
            $query->join('visua_detect_image', 'visua_detect_image.image_id', '=', 'sale_order_item_image.id')
                ->where('visua_detect_image.is_ip_violation', 1)
                ->whereNull('image_hash.is_user_check_ip_violation')
                ->whereNull('image_hash.is_ip_violation');
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            } else {
                $query->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]);
            }
        } elseif ($rawData['tab'] == 'ip_violation') {
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            }
            $query->where('image_hash.is_ip_violation', 1)
                ->whereNotNull('user_review_id');
        } elseif ($rawData['tab'] == 'review') {
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            } else {
                $query->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]);
            }
            $query->where('image_hash.is_user_check_ip_violation', 1)
                ->whereNull('user_review_id');
        }

        if (isset($rawData['sort'])) {
            if ($rawData['sort'] == 0) {
                $query->orderBy('sale_order_item_image.id', 'DESC');
            }
            if ($rawData['sort'] == 1) {
                $query->orderBy('sale_order_item_image.id', 'ASC');
            }
        } else {
            $query->orderBy('sale_order_item_image.id', 'DESC');
        }

        return $query->groupBy('sale_order_item_image.id')->paginate($this->limit);
    }

    public function getCount($rawData)
    {
        setTimezone();
        $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
        $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
        $startImageId = 18877001;
        $query = DB::table('sale_order_item_image')
            ->select('sale_order_item_image.*', 'sale_order.external_number as external_id', 'sale_order.order_number as order_number', 'store.code as store_name', 'sale_order.order_status')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->join('store', 'store.id', '=', 'sale_order_item_image.store_id')
            ->join('image_hash', 'image_hash.id', '=', 'sale_order_item_image.image_hash_id')
            ->where('sale_order.is_test', '=', 0)
            ->where('sale_order_item_image.thumb_750', '=', 1)
            ->whereNotIn('sale_order.store_id', $storeExcludeCheckIP)
            ->where('sale_order_item_image.id', '>', $startImageId);

        if (isset($rawData['store']) && $rawData['store']) {
            $query->where('sale_order_item_image.store_id', '=', $rawData['store']);
        }
        if (!empty($rawData['order_type'])) {
            $query->where('sale_order.order_type', $rawData['order_type']);
        }
        if (isset($rawData['sku']) && $rawData['sku']) {
            $query->where('sale_order_item_image.sku', $rawData['sku']);
        }
        if (isset($rawData['selected_date']) && $rawData['selected_date']) {
            $query->where('sale_order_item_image.order_date', $rawData['selected_date']);
        } else {
            $date = Carbon::now()->format('Y-m-d');
            $query->where('sale_order_item_image.order_date', $date);
        }
        $output = [
            'visua' => 0,
            'ip_violation' => 0,
        ];
        if (in_array('visua', $rawData['tabs'])) {
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            } else {
                $query->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD]);
            }
            $output['visua'] = $query->clone()->join('visua_detect_image', 'visua_detect_image.image_id', '=', 'sale_order_item_image.id')
                ->where('visua_detect_image.is_ip_violation', 1)
                ->whereNull('image_hash.is_user_check_ip_violation')
                ->whereNull('image_hash.is_ip_violation')->count();
        }
        if (in_array('review', $rawData['tabs'])) {
            if (isset($rawData['status']) && $rawData['status']) {
                $query->where('sale_order.order_status', '=', $rawData['status']);
            }
            $output['review'] = $query->clone()->where('image_hash.is_user_check_ip_violation', 1)
                ->whereNull('user_review_id')->count();
        }

        return $output;
    }

    public function getCountOrder($input)
    {
        return SaleOrder::join('sale_order_item_image', 'sale_order_item_image.order_id', 'sale_order.id')
            ->whereIn('sale_order_item_image.image_hash_id', $input['image_hash_ids'])
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::ON_HOLD, SaleOrder::IN_PRODUCTION])
            ->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 6411823))
            ->select('sale_order.id')->distinct()
            ->get()
            ->count();
    }

    public function getCountPending($rawData)
    {
        [
            'order_item_image_id' => $imageId,
        ] = getBarcodeIdLimit();

        if (empty($imageId)) {
            $image = DB::table('sale_order_item_image')
                ->selectRaw('min(id) as min_id')
                ->whereRaw('created_at >= (NOW()-INTERVAL 10 DAY)')
                ->first();
            $imageId = $image->min_id ?? 0;
        }
        $endTime = Carbon::now('UTC')->setTimezone('America/Los_Angeles')->subHours(2)->toDateTimeString();
        setTimezone();

        $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
        $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];

        $query = DB::table('sale_order_item_image')
            ->select('sale_order_item_image.id', 'sale_order_item_image.created_at')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->join('store', 'store.id', '=', 'sale_order_item_image.store_id')
            ->join('image_hash', 'image_hash.id', '=', 'sale_order_item_image.image_hash_id')
            ->where('sale_order.is_test', '=', 0)
            ->where('sale_order_item_image.thumb_750', '=', 1)
            ->whereNotIn('sale_order.store_id', $storeExcludeCheckIP)
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD])
            ->where('sale_order_item_image.id', '>=', $imageId)
            ->where('sale_order_item_image.created_at', '<=', $endTime);
        $response = [];
        if (!empty($rawData['tab']) && $rawData['tab'] == 'manual') {
            $response['manual'] = $query->leftJoin('visua_detect_image', 'visua_detect_image.image_id', '=', 'sale_order_item_image.id')
                ->where(function ($q) {
                    $q->whereNull('visua_detect_image.id')
                        ->orWhere(function ($q1) {
                            $q1->where('visua_detect_image.is_ip_violation', '!=', 1);
                        });
                })
                ->whereNull('image_hash.is_user_check_ip_violation')
                ->get()
                ->count();
        }
        if (!empty($rawData['tab']) && $rawData['tab'] == 'visua') {
            $response['visua'] = $query->join('visua_detect_image', 'visua_detect_image.image_id', '=', 'sale_order_item_image.id')
                ->where('visua_detect_image.is_ip_violation', 1)
                ->whereNull('image_hash.is_user_check_ip_violation')
                ->whereNull('image_hash.is_ip_violation')
                ->get()
                ->count();
        }
        if (!empty($rawData['tab']) && $rawData['tab'] == 'review') {
            $response['review'] = $query->where('image_hash.is_user_check_ip_violation', 1)
                ->whereNull('image_hash.user_review_id')
                ->get()
                ->count();
        }

        return $response;
    }
}
