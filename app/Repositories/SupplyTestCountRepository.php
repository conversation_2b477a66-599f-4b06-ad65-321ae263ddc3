<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyInventory;
use App\Models\SupplyTestCount;
use App\Models\SupplyTestCountItem;
use App\Models\SupplyTestCountPause;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupplyTestCountRepository extends CommonRepository
{
    public function __construct(
        protected SupplyBoxRepository $supplyBoxRepository,
        protected SupplyLocationRepository $supplyLocationRepository,
        protected TimeCheckingRepository $timeCheckingRepository
    ) {
    }

    const LIMIT = 10;

    public function fetchTestCount($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = SupplyTestCount::with([
            'supplyLocation:id,barcode',
            'employee:id,code,name',
        ])
            ->where('warehouse_id', $input['warehouse_id']);

        if (!empty($input['location'])) {
            $query->whereHas('supplyLocation', function ($q) use ($input) {
                $q->where('barcode', 'LIKE', '%' . $input['location'] . '%');
            });
        }

        return $query->groupBy('id')
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }

    public function complete($input)
    {
        DB::beginTransaction();

        try {
            $barcodeNotFound = !empty($input['barcode_not_found']) ? $input['barcode_not_found'] : [];
            $barcodeExisted = !empty($input['barcode_found']) ? $input['barcode_found'] : [];
            $boxInLocation = array_merge($barcodeNotFound, $barcodeExisted);
            $boxCodeNew = !empty($input['barcode_new']) ? $input['barcode_new'] : [];
            $totalBox = SupplyBox::where('location_id', $input['location_id'])
                ->whereIn('barcode', $boxInLocation)
                ->get();

            $testCount = SupplyTestCount::create([
                'warehouse_id' => $input['warehouse_id'],
                'location_id' => $input['location_id'],
                'user_id' => $input['user_id'],
                'note' => !empty($input['note']) ? $input['note'] : null,
                'box_available' => count($totalBox),
                'box_on_hand' => 0,
                'employee_id' => $input['employee_id']
            ]);

            if (!empty($totalBox)) {
                foreach ($totalBox as $itemBox) {
                    $inputTestCountItem['test_count_id'] = $testCount->id;
                    $inputTestCountItem['location_id'] = $input['location_id'];
                    $inputTestCountItem['box_id'] = $itemBox->id;
                    $inputTestCountItem['type'] = SupplyTestCountItem::TYPE_FOUND;

                    if (in_array($itemBox->barcode, $barcodeNotFound)) {
                        // Case tao box moi, update quantity
                        SupplyBox::where('id', $itemBox->id)->delete();
                        $inputInventory['direction'] = SupplyInventory::DIRECTION_OUTPUT;
                        $inputInventory['type'] = SupplyInventory::TYPE_ADJUST;
                        $inputInventory['supply_id'] = $itemBox->supply_id;
                        $inputInventory['warehouse_id'] = $input['warehouse_id'];
                        $inputInventory['location_id'] = $input['location_id'];
                        $inputInventory['user_id'] = $input['user_id'];
                        $inputInventory['box_id'] = $itemBox->id;
                        $inputInventory['object_id'] = $testCount->id;
                        $inputInventory['object_name'] = SupplyInventory::OBJECT_TEST_COUNT;
                        $inputInventory['quantity'] = $itemBox->quantity;
                        SupplyInventory::create($inputInventory);

                        // Update supply's location quantity và supply_quantity
                        if (!empty($itemBox->supply_id)) {
                            $this->supplyLocationRepository->updateQuantity($itemBox->location_id, $inputInventory['supply_id'], -1 * $inputInventory['quantity']);
                            SupplyQuantityRepository::updateQuantity($input['warehouse_id'], $inputInventory['supply_id'], -1 * $inputInventory['quantity']);
                        }

                        $inputTestCountItem['type'] = SupplyTestCountItem::TYPE_NOT_FOUND;
                    }
                    SupplyTestCountItem::create($inputTestCountItem);
                }
            }

            if (!empty($input['barcode_moving'])) {
                foreach ($input['barcode_moving'] as $itemBarcode) {
                    $box = SupplyBox::where('barcode', $itemBarcode)
                        ->where('warehouse_id', $input['warehouse_id'])
                        ->first();

                    if (!empty($box)) {
                        $this->supplyLocationRepository->updateQuantity($box->location_id, $box->supply_id, $box->quantity * -1);
                        $box->location_id = $input['location_id'];
                        $this->supplyLocationRepository->updateQuantity($box->location_id, $box->supply_id, $box->quantity);
                        $box->save();
                        $inputTestCountItem['test_count_id'] = $testCount->id;
                        $inputTestCountItem['location_id'] = $input['location_id'];
                        $inputTestCountItem['box_id'] = $box->id;
                        $inputTestCountItem['type'] = SupplyTestCountItem::TYPE_BOX_MOVING;
                        SupplyTestCountItem::create($inputTestCountItem);
                    }
                }
            }

            if (!empty($boxCodeNew)) {
                $this->insertBoxNew($boxCodeNew, $input['warehouse_id'], $input['location_id'], $testCount->id);
            }

            $box_on_hand = SupplyTestCountItem::where('test_count_id', $testCount->id)
                ->where('type', '<>', SupplyTestCountItem::TYPE_NOT_FOUND)
                ->count();
            SupplyTestCount::where('id', $testCount->id)->update(['box_on_hand' => $box_on_hand]);

            //Sau khi test count complete thì check xem co test count pause khong, neu co thi xoa test count pause do di
            $testCountPause = SupplyTestCountPause::where('location_id', $input['location_id'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->first();

            if (!empty($testCountPause)) {
                $testCountPause->delete();
            }

            // Update end time  for time checking
            $this->timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('SupplyTestCountRepository.complete', [
                'input' => $input,
                'exception' => $exception,
            ]);

            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(SupplyTestCount::latest('id')->first(), 201);
    }

    private function insertBoxMoving($box, $boxMoving, $input)
    {
        $pre_location_id = $boxMoving->location_id;
        $new_location_id = $input['location_id'];
        $product_id = $boxMoving->product_id;
        $quantity = $boxMoving->quantity;
        $inventory_addition_id = $boxMoving->inventory_addition_id;
        BoxMoving::create([
            'box_id' => $box->id,
            'pre_location_id' => $pre_location_id,
            'location_id' => $new_location_id,
            'warehouse_id' => $input['warehouse_id'],
            'user_id' => $input['user_id'],
            'product_id' => $product_id,
            'inventory_addition_id' => $inventory_addition_id,
            'quantity' => $quantity,
            'created_at' => date('Y-m-d H:i:s'),
        ]);
        Box::where('id', $box->id)->update(['location_id' => $new_location_id]);
        $preLocationProduct = LocationProduct::where('location_id', $pre_location_id)
            ->where('product_id', $product_id)->first();

        if (!empty($preLocationProduct)) {
            ///Todo : update Pre Location
            $quantity = $preLocationProduct->quantity - $quantity;
            LocationProduct::where('id', $preLocationProduct->id)->update(['quantity' => $quantity]);
        }

        $newLocationProduct = LocationProduct::where('location_id', $new_location_id)
            ->where('product_id', $product_id)->first();

        if (!empty($newLocationProduct)) {
            ///Todo : update New Location
            $quantity = $newLocationProduct->quantity + $quantity;
            LocationProduct::where('id', $newLocationProduct->id)->update(['quantity' => $quantity]);
        }
    }

    public function scanBox($input)
    {
        $box = SupplyBox::where('warehouse_id', $input['warehouse_id'])
            ->where('barcode', $input['barcode'])
            ->onlyTrashed()
            ->first();
        $barcodeBox = $input['barcode'];

        if (!empty($box)) {
            return response()->json([
                'barcode' => ["Barcode $barcodeBox is not exist or deleted !"]
            ], 422);
        }

        $box = SupplyBox::with([
            'location',
            'supply'
        ])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('location_id', '<>', $input['location_id'])
            ->where('barcode', $input['barcode'])
            ->first();

        if (!empty($box)) {
            return response()->json([
                'barcode_another_location' => ['Box is another location, do you want moving it to this location ?'],
                'box' => $box,
                'location' => $box->location,
                'supply' => $box->supply
            ], 422);
        }

        $box = SupplyBox::where('warehouse_id', $input['warehouse_id'])
            ->where('location_id', $input['location_id'])
            ->where('barcode', $input['barcode'])
            ->first();

        if ($box) {
            if (is_null($box->supply_id)) {
                return response()->json(['barcode' => ['Box does not contain any supply, please do the addition!']], 422);
            }

            return response()->json(['barcode' => ['Success']], 200);
        }

        $box = SupplyBox::where('warehouse_id', $input['warehouse_id'])
            ->where('barcode', $input['barcode'])
            ->first();
        $barcodeBox = $input['barcode'];

        if (empty($box)) {
            return response()->json([
                'supply_not_having_barcode' => ["Barcode $barcodeBox needs to be added SKU!"]
            ], 422);
        }

        return response()->json(['barcode' => ['Success']], 200);
    }

    public function testCountDetail($input)
    {
        $data = DB::table('supply_test_counts')
            ->select('supply_test_counts.*',
                'supply_locations.barcode as location', 'supply_test_count_items.box_id', 'supply_test_count_items.type', 'supply_boxes.barcode',
                'supply.name as product_name', 'supply_boxes.quantity')
            ->leftJoin('supply_test_count_items', 'supply_test_count_items.test_count_id', '=', 'supply_test_counts.id')
            ->leftJoin('supply_boxes', 'supply_boxes.id', '=', 'supply_test_count_items.box_id')
            ->leftJoin('supply', 'supply.id', '=', 'supply_boxes.supply_id')
            ->leftJoin('supply_locations', 'supply_locations.id', '=', 'supply_test_counts.location_id')
            ->where('supply_test_counts.warehouse_id', $input['warehouse_id'])
            ->where('supply_test_counts.id', $input['id'])
            ->get();
        $totalProductFound = 0;
        $totalProductNotFound = 0;

        foreach ($data as $key => $item) {
            if ($item->type == 'not_found') {
                $totalProductNotFound += $item->quantity;
            } else {
                $totalProductFound += $item->quantity;
            }
        }

        return [
            'data' => $data,
            'total_product' => $totalProductFound,
            'total_product_not_found' => $totalProductNotFound,
            'total_box' => count($data)
        ];
    }

    private function insertBoxNew($input, $warehouse, $location, $testCountId)
    {
        DB::beginTransaction();

        try {
            foreach ($input as $key => $item) {
                $box = SupplyBox::create([
                    'warehouse_id' => $warehouse,
                    'barcode' => $item['barcode'],
                    'location_id' => $location,
                    'supply_id' => $item['supply_id'],
                    'quantity' => $item['quantity']
                ]);

                SupplyInventory::create([
                    'direction' => Inventory::DIRECTION_INPUT,
                    'type' => Inventory::TYPE_ADJUST,
                    'supply_id' => $item['supply_id'],
                    'warehouse_id' => $warehouse,
                    'location_id' => $location,
                    'user_id' => auth()->user()['id'],
                    'object_id' => $testCountId,
                    'box_id' => $box->id,
                    'object_name' => SupplyInventory::OBJECT_TEST_COUNT,
                    'quantity' => $item['quantity']
                ]);

                $this->supplyLocationRepository->updateQuantity($location, $item['supply_id'], $item['quantity']);
                SupplyQuantityRepository::updateQuantity($warehouse, $item['supply_id'], $item['quantity']);

                $inputTestCountItem['test_count_id'] = $testCountId;
                $inputTestCountItem['location_id'] = $location;
                $inputTestCountItem['box_id'] = $box->id;
                $inputTestCountItem['type'] = SupplyTestCountItem::TYPE_BOX_NEW;

                SupplyTestCountItem::create($inputTestCountItem);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('SupplyTestCountRepository.insertBoxNew', [
                'input' => $input,
                'warehouse' => $warehouse,
                'location' => $location,
                'testCountId' => $testCountId,
                'exception' => $exception,
            ]);

            return response()->json($exception->getMessage(), 500);
        }
    }

    public function pauseTestCount($input)
    {
        // TODO: Implement pauseTestCount() method.
        $dataBox = [
            'barcode_found' => $input['barcode_found'],
            'barcode_moving' => $input['barcode_moving'],
            'barcode_not_found' => $input['barcode_not_found'],
            'barcode_new' => $input['barcode_new'],
        ];
        $dataInsert = [
            'location_id' => $input['location_id'],
            'data' => json_encode($dataBox),
            'warehouse_id' => $input['warehouse_id'],
        ];
        $testCountPause = SupplyTestCountPause::where('location_id', $input['location_id'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->first();

        if (!empty($testCountPause)) {
            return $testCountPause->update($dataInsert);
        }

        //todo:  update time end for time checking
        $this->timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

        return SupplyTestCountPause::create($dataInsert);
    }

    public function getAllBoxInLocation($input)
    {
        $dataBox = $this->supplyBoxRepository->getBoxInLocationForTestCount($input);
        $testCountPause = SupplyTestCountPause::where('location_id', $input['location_id'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->first();

        if (empty($testCountPause)) {
            return [
                'test_count_pause' => false,
                'data' => !empty($dataBox) ? $dataBox : []
            ];
        }

        $dataBoxTestCountPause = json_decode($testCountPause['data']);
        $dataTrue = [];
        $dataFalse = [];

        foreach ($dataBox as $key => $item) {
            if (in_array($item['barcode'], $dataBoxTestCountPause->barcode_found)) {
                $item['is_success'] = true;
                $item['product_name'] = $item['supply']['name'];
                $dataTrue[] = $item;
            } else {
                $item['is_success'] = false;
                $item['product_name'] = $item['supply']['name'];
                $dataFalse[] = $item;
            }
        }

        $dataNew = array_merge($dataBoxTestCountPause->barcode_moving, $dataTrue, $dataFalse);
        $dataOut = [
            'test_count_pause' => true,
            'data' => $dataNew,
            'barcode_new' => $dataBoxTestCountPause->barcode_new
        ];

        return $dataOut;
    }

    public function getAllTestCountPause($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = DB::table('paused_supply_test_counts')
            ->select('paused_supply_test_counts.*', 'supply_locations.barcode as location')
            ->where('paused_supply_test_counts.warehouse_id', $input['warehouse_id'])
            ->whereNull('paused_supply_test_counts.deleted_at')
            ->join('supply_locations', 'supply_locations.id', '=', 'paused_supply_test_counts.location_id');
        $data = $query->orderBy('paused_supply_test_counts.id', 'desc')
            ->paginate($limit);

        if (count($data->items()) > 0) {
            foreach ($data->items() as &$item) {
                $item->data = json_decode($item->data);
            }
        }

        return $data;
    }
}
