<?php

namespace App\Repositories;

use App\Events\ConvertToAiFileNotification;
use App\Http\Service\ConvertService;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\Warehouse;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PosterRepository extends CommonRepository
{
    protected ConvertService $convertService;

    public function __construct(ConvertService $convertService)
    {
        $this->convertService = $convertService;
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return BarcodePrinted::listPoster($warehouse_id, $limit);
    }

    public function historyPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return BarcodePrinted::historyPdfPoster($warehouse_id, $limit, $label_id);
    }

    public function convertPdf($input)
    {
        try {
            DB::beginTransaction();
            $limit = $input['quantity'];
            $employee_id = $input['employee_id'];
            $store_id = $input['store_id'] ?? null;
            $warehouse_id = config('jwt.warehouse_id');
            $result = [];
            $listConvert = [];

            $barcodes = SaleOrderItemBarcode::getListBarcodeConverted($warehouse_id, BarcodePrinted::METHOD_POSTER, $input)
                ->limit($limit)
                ->get(['sale_order_item_barcode.id as barcode_id', 'product.id as product_id', 'product.style as sku', 'product.size as size']);

            if ($warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID) {
                $barcodesGrouped = $barcodes->groupBy('product_id')->map(function ($items) {
                    return [
                        'product_id' => $items->first()->product_id,
                        'sku' => $items->first()->sku,
                        'size' => $items->first()->size,
                        'barcode_ids' => $items->pluck('barcode_id')->all(),
                    ];
                });

                foreach ($barcodesGrouped as $itemBarcodeGroup) {
                    $maxItemInFileConvert = $this->getMaxNumberItemInFileConvert($itemBarcodeGroup['size']);
                    if ($maxItemInFileConvert) {
                        foreach (array_chunk($itemBarcodeGroup['barcode_ids'], $maxItemInFileConvert) as $chunk) {
                            $listConvert[] = $this->buildConvertData($input, $employee_id, $warehouse_id, $store_id, $itemBarcodeGroup['sku'], $itemBarcodeGroup['product_id'], count($chunk), $chunk);
                        }
                    }
                }
            } else {
                foreach ($barcodes as $barcode) {
                    $listConvert[] = $this->buildConvertData($input, $employee_id, $warehouse_id, $store_id, $barcode->sku, $barcode->product_id, 1, [$barcode->barcode_id]);
                }
            }

            foreach ($listConvert as $item) {
                $barcodeIds = $item['barcode_ids'];
                unset($item['barcode_ids']);
                $barcodePrinted = BarcodePrinted::create($item);
                $this->updateOrCreateBarcodePrintedTime($item);
                SaleOrderItemBarcode::whereIn('id', $barcodeIds)->update(['barcode_printed_id' => $barcodePrinted->id]);

                $result[] = $barcodePrinted->load(['employeeConvert:id,name', 'product:id,color,size']);
            }

            DB::commit();

            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function buildConvertData($input, $employee_id, $warehouse_id, $store_id, $sku, $product_id, $quantity, $barcode_ids)
    {
        return [
            'style_sku' => $sku,
            'employee_id' => $employee_id,
            'warehouse_id' => $warehouse_id,
            'store_id' => $store_id,
            'user_id' => Auth::id(),
            'convert_percent' => $quantity,
            'quantity_input' => $quantity,
            'barcode_ids' => $barcode_ids,
            'print_method' => BarcodePrinted::METHOD_POSTER,
            'product_id' => $product_id,
            'created_at' => now(),
            'print_status' => BarcodePrinted::INACTIVE,
            'is_tiktok' => $input['is_tiktok'] ?? null,
            'is_fba' => $input['is_fba'] ?? null,
            'is_reroute' => $input['is_reroute'] ?? null,
            'is_manual' => $input['is_manual'] ?? null,
            'is_reprint' => $input['is_reprint'] ?? null,
            'is_xqc' => $input['is_xqc'] ?? null,
            'is_eps' => $input['is_eps'] ?? null,
            'is_bulk_order' => $input['is_bulk_order'] ?? null,
        ];
    }

    private function getMaxNumberItemInFileConvert($posterSize)
    {
        $posterSize = trim(str_replace('Inches', '', $posterSize));

        $posterConfigs = DB::table('poster_layout_config')
            ->where('size', $posterSize)
            ->first();
        if (!$posterConfigs || empty($posterConfigs->config)) {
            return 1;
        }
        $config = json_decode($posterConfigs->config, true);

        return $config['maxColumn'] ?? 1;
    }

    public function downloadPdf($input)
    {
        $barcode_printed_id = $input['barcode_printed_id'];
        $barcode_printed = BarcodePrinted::find($barcode_printed_id);

        if (!$barcode_printed) {
            throw new Exception('Pdf not found', Response::HTTP_NOT_FOUND);
        }

        if ($input['is_history'] == true) {
            return [];
        }
        if ($barcode_printed->print_status != BarcodePrinted::ACTIVE) {
            $barcodeRepository = new BarcodeRepository();
            $barcodeRepository->customLogTimeLineOrderByBarcodePrintedId($barcode_printed_id, $barcode_printed->employee_id, SaleOrderHistory::PRINT_DTF);
        }
        $barcode_printed->print_status = BarcodePrinted::ACTIVE;
        $barcode_printed->save();

        SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed->id)->update([
            'printed_at' => now(),
            'employee_print_id' => $barcode_printed->employee_id
        ]);

        $saleOrderItems = SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed->id)
        ->distinct()
        ->pluck('order_id');

        if ($saleOrderItems->isNotEmpty()) {
            $printingRepository = new PrintingRepository();

            foreach ($saleOrderItems as $orderId) {
                $printingRepository->updateOrderPrinted($orderId);
            }
        }

        handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $barcode_printed_id);

        return $barcode_printed;
    }

    public function convertToAi($request)
    {
        $barcodePrinted = BarcodePrinted::where('print_status', BarcodePrinted::PRINTED_STATUS)
            ->where('id', $request->barcode_id)
            ->first();

        if (!$barcodePrinted) {
            throw new Exception('Must download the file before converting!', Response::HTTP_BAD_REQUEST);
        }

        broadcast(new ConvertToAiFileNotification((array) json_decode($barcodePrinted->positions)));

        return [
            'data' => $barcodePrinted->positions
        ];
    }

    public function excludeBulkOrder(&$qr)
    {
        return $qr->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->where('sale_order.is_xqc', '=', 1);
                });
        });
    }

    public function countPendingTiktok($input)
    {
        $query = $this->countPoster($input)
               ->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingFBA($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingReroute($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNotNull('sale_order_item_barcode.employee_reroute_id');
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingManualProcess($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingReprint($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNotNull('sale_order_item_barcode.label_root_id');
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingXQC($input)
    {
        $query = $this->countPoster($input);
        $barcodes = $query->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', true)
            ->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPendingEPS($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.reprint_status', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', false)
            ->where('sale_order.is_eps', true);
        $this->excludeBulkOrder($query);
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function countPoster($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->join('store', 'sale_order_item.store_id', '=', 'store.id')
            ->select(
                DB::raw('COUNT(*) as count'),
            )
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_POSTER);

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $queryData->where('sale_order_item_barcode.store_id', $input['store_id']);
            } else {
                $queryData->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }
        }

        return $queryData;
    }

    public function countPendingStyle($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $total = 0;
        $result = [];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->select(
                DB::raw('COUNT(*) as count'),
                'sale_order_item.product_id',
                'product.style',
                'product.size',
                'product.color',
            )
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', ProductStyle::METHOD_POSTER)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_fba_order', false)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_manual', false)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order.is_xqc', false)
            ->where('sale_order.is_eps', false);
        $this->excludeBulkOrder($queryData);

        if (!empty($input['priorityStores'])) {
            if (!empty($input['store_id']) && in_array($input['store_id'], $input['priorityStores'])) {
                $queryData->where('sale_order_item_barcode.store_id', $input['store_id']);
            } else {
                $queryData->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }
        }

        $queryData = $queryData->groupBy('sale_order_item.product_id')
            ->get();

        if ($queryData->count()) {
            $productIds = array_column($queryData->toArray(), 'product_id');
            $barcodesPrinted = BarcodePrinted::where('warehouse_id', $warehouse_id)
                ->whereIn('product_id', $productIds)
                ->where('print_method', BarcodePrinted::METHOD_POSTER)
                ->whereNull('is_tiktok')
                ->whereNull('is_fba')
                ->whereNull('is_reroute')
                ->whereNull('is_manual')
                ->whereNull('is_reprint')
                ->whereNull('is_xqc')
                ->whereNull('is_eps')
                ->whereNull('is_bulk_order')
                ->selectRaw('MAX(created_at) AS created_at, product_id')
                ->groupBy('product_id')
                ->get();

            foreach ($queryData as $item) {
                $total += $item->count;
                $barcodePrinted = $barcodesPrinted->first(function ($barcodePrintedItem) use ($item) {
                    return $item->product_id == $barcodePrintedItem->product_id;
                });
                $result[] = [
                    'product_id' => $item->product_id,
                    'count' => $item->count,
                    'color' => $item->color,
                    'size' => $item->size,
                    'last_created_at' => optional($barcodePrinted)->created_at ? Carbon::parse($barcodePrinted->created_at)->format('m/d/Y H:i:s') : null,
                    'style_name' => $item->style
                ];
            }
        }

        return [
            'total' => $total,
            'data' => array_values(collect($result)->sortBy('last_created_at')->toArray()),
        ];
    }

    public function countPendingBulkOrder($input)
    {
        $query = $this->countPoster($input)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_xqc', '<>', 1)
            ->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));
        $barcodes = $query->first();

        return [
            'total' => $barcodes->count ?? 0,
            'sql' => interpolateQuery($query),
        ];
    }

    public function updateOrCreateBarcodePrintedTime($data)
    {
        $attributes = [
            'store_id' => $data['store_id'],
            'warehouse_id' => $data['warehouse_id'],
            'is_xqc' => $data['is_xqc'] ?? null,
            'style_sku' => $data['style_sku'],
            'is_reprint' => $data['is_reprint'] ?? null,
            'is_manual' => $data['is_manual'] ?? null,
            'is_reroute' => $data['is_reroute'] ?? null,
            'is_fba' => $data['is_fba'] ?? null,
            'is_eps' => $data['is_eps'] ?? null,
            'color_sku' => $data['color_sku'] ?? null,
            'is_tiktok' => $data['is_tiktok'] ?? null,
            'is_bulk_order' => $data['is_bulk_order'] ?? null,
            'print_method' => $data['print_method'],
        ];

        BarcodePrintedTime::updateOrCreate($attributes, ['printed_at' => now()]);
    }

    public function fetchAllPosterLayoutsConfig()
    {
        $rawConfigs = DB::table('poster_layout_config')->get();
        $result = [];
        foreach ($rawConfigs as $item) {
            $result[$item->size] = json_decode($item->config, true);
        }

        return $result;
    }
}
