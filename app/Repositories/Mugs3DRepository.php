<?php

namespace App\Repositories;

use App\Http\Service\ConvertService;
use App\Models\BarcodePrinted;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Mugs3DRepository extends CommonRepository
{
    protected ConvertService $convertService;

    public function __construct(ConvertService $convertService)
    {
        $this->convertService = $convertService;
    }

    public function countMug($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $printMethod = $input['print_method'];
        $total = 0;
        $result = [];
        $queryData = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product', 'sale_order_item.product_id', '=', 'product.id')
            ->select(
                DB::raw('COUNT(*) as count'),
                'product_id',
                'product.style',
                'product.size',
                'product.color',
            )
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.retry_convert', '<', SaleOrderItemBarcode::MAX_RETRY)
            ->where('sale_order_item_barcode.warehouse_id', $warehouse_id)
            ->where('print_method', $printMethod)
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->groupBy('product_id')
            ->get();

        if ($queryData->count()) {
            foreach ($queryData as $item) {
                $barcode_printed = BarcodePrinted::findLastCreatedByProductId($item->product_id, $warehouse_id);
                $total += $item->count;
                $result[] = [
                    'product_id' => $item->product_id,
                    'count' => $item->count,
                    'color' => $item->color,
                    'size' => $item->size,
                    'last_created_at' => optional($barcode_printed)->created_at ? Carbon::parse($barcode_printed->created_at)->format('m/d/Y H:i:s') : null,
                    'style_name' => $item->style
                ];
            }
        }

        return [
            'total' => $total,
            'data' => array_values(collect($result)->sortBy('last_created_at')->toArray()),
        ];
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'];

        return BarcodePrinted::listPdf3d($warehouse_id, $limit, $printMethod);
    }

    public function historyPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'] ?? BarcodePrinted::METHOD_UV3D;
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return BarcodePrinted::historyPdf3d($warehouse_id, $limit, $label_id, $printMethod);
    }

    public function convertPdf($input)
    {
        $product_id = $input['product_id'];
        $limit = $input['quantity'];
        $employee_id = $input['employee_id'];
        $printMethod = $input['print_method'] ?? BarcodePrinted::METHOD_UV3D;
        $warehouse_id = config('jwt.warehouse_id');
        $product = Product::with('productStyle')->find($product_id);
        $printingLayoutTemplate = $product->printingLayoutTemplate($printMethod)->first();
        if (!$printingLayoutTemplate) {
            throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
        }

        try {
            DB::beginTransaction();
            $barcode_printed = BarcodePrinted::create([
                'quantity_input' => $limit,
                'style_sku' => $product->productStyle->sku,
                'employee_id' => $employee_id,
                'warehouse_id' => $warehouse_id,
                'user_id' => Auth::id(),
                'convert_percent' => $limit,
                'print_method' => $printMethod,
                'product_id' => $product->id,
                'created_at' => Carbon::now(),
                'print_status' => BarcodePrinted::INACTIVE
            ]);

            SaleOrderItemBarcode::updatePdfConvertId($barcode_printed, $product_id, $limit, $warehouse_id, $printMethod);

            DB::commit();

            return $barcode_printed->load([
                'employeeConvert:id,name',
                'product:id,color,size'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function downloadPdf($input)
    {
        $barcode_printed_id = $input['barcode_printed_id'];
        $barcode_printed = BarcodePrinted::find($barcode_printed_id);

        if (!$barcode_printed) {
            throw new Exception('Pdf not found', Response::HTTP_NOT_FOUND);
        }

        if ($input['is_history'] == true) {
            return [];
        }

        //log timeline
        if ($barcode_printed->print_status != BarcodePrinted::ACTIVE) {
            $barcodeRepository = new BarcodeRepository();
            $barcodeRepository->logTimeLineOrderByBarcodePrintedId($barcode_printed_id, $barcode_printed->employee_id);
        }

        $barcode_printed->print_status = BarcodePrinted::ACTIVE;
        $barcode_printed->save();

        //update print_at

        SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed_id)->update([
            'printed_at' => now(),
            'employee_print_id' => $barcode_printed->employee_id
        ]);

        $saleOrderItems = SaleOrderItemBarcode::where('barcode_printed_id', $barcode_printed_id)
        ->groupBy('order_id')
        ->get(['order_id']);

        // cập nhật từng order_id trong batch
        if (!empty($saleOrderItems)) {
            $printingRepository = new PrintingRepository();

            foreach ($saleOrderItems as $saleOrderItem) {
                $printingRepository->updateOrderPrinted($saleOrderItem->order_id);
            }
        }

        handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $barcode_printed_id);

        return $barcode_printed;
    }
}
