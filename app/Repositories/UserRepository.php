<?php

namespace App\Repositories;

use App\Http\Service\Slack;
use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class UserRepository extends CommonRepository implements UserRepositoryInterface
{
    public function fetchAll($input)
    {
        return DB::table('user')
            ->select('user.*')
            ->join('user_warehouse', 'user.id', '=', 'user_warehouse.user_id')
            ->where('user_warehouse.warehouse_id', $input['warehouse_id'])
            ->groupBy('user.id')
            ->get();
    }

    public function getListAllUser($request)
    {
        setTimezone();

        $query = User::with('warehouses', 'roles', 'department', 'latestAccessLog.country');

        if (!empty($request->name)) {
            $query->where('username', 'like', '%' . $request->name . '%');
        }

        if (!empty($request->role)) {
            $role = strtolower($request->role);
            if ($role == 'admin') {
                $query->where('is_admin', '=', 1);
            } else {
                $query->whereHas('roles', function ($q) use ($request) {
                    $q->where('name', $request->role);
                });
            }
        }

        if (!empty($request->department)) {
            $query->whereHas('department', function ($q) use ($request) {
                $q->where('name', $request->department);
            });
        }

        return $query->get();
    }

    private function buildData($request): array
    {
        $output = [
            'username' => $request['username'],
            'email' => $request['email'],
            'is_admin' => $request['is_admin'],
            'department_id' => $request['department_id'],
            'is_all_warehouse' => $request['is_all_warehouse'],
            'is_all_store' => $request['is_all_store'] ?? 0,
            'change_app_password_status' => $request['change_app_password_status'],
            'change_seller_password_status' => $request['change_seller_password_status'],
            'app_send_to' => $request['app_send_to'],
            'seller_send_to' => $request['seller_send_to'],
        ];
        if (!empty($request['password'])) {
            $output['password'] = bcrypt($request['password']);
        }
        if (!empty($request['seller_password'])) {
            $output['seller_password'] = bcrypt($request['seller_password']);
        }

        return $output;
    }

    public function create($request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $data = $this->buildData($request);
            //tạo random pass nếu k điền app password & seller password
            $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_';
            if (empty($data['password'])) {
                $data['password'] = bcrypt(substr(str_shuffle($characters), 0, 12));
            }
            if (empty($data['seller_password'])) {
                $data['seller_password'] = bcrypt(substr(str_shuffle($characters), 0, 12));
            }
            $user = User::create($data);
            $user->stores()->attach($request['store_ids']);
            if ($user->is_admin != User::ADMIN) {
                $user->warehouses()->attach($request['warehouse_ids']);
                $role = Role::find($request['role_id']);
                $user->assignRole($role->name);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Create user successfully!', $user);
    }

    public function buildDataForLog($request)
    {
        return [
            'ip' => $request->ip(),
            'update_by' => Auth::user()->username,
            'user_agent' => $request->header('User-Agent'),
            'username' => $request['username'],
            'email' => $request['email'],
            'is_admin' => $request['is_admin'],
            'department_id' => $request['department_id'],
            'store_ids' => !empty($request['store_ids']) ? json_encode($request['store_ids']) : '',
            'is_all_warehouse' => $request['is_all_warehouse'],
            'password' => $request['password'],
            'seller_password' => $request['seller_password'],
            'is_bypass_restriction' => $request['is_bypass_restriction'],
        ];
    }

    public function update($id, $request): JsonResponse
    {
        $data = $this->buildData($request);

        $log = $this->buildDataForLog($request);

        $result = Slack::send(json_encode($log), env('ALERT_MONITOR', ''));
        // log result
        Log::info($result);

        try {
            DB::beginTransaction();

            $user = User::find($id);
            if (!$user) {
                return $this->errorResponse('User not found!');
            }

            foreach ($data as $key => $value) {
                $user[$key] = $value;
            }

            // Not update expired_at
            // inventory | 20
            // pulling   | 19
            // qc        | 26
            // Tech Team | 74
            $userNotUpdateExpiredAt = [19, 20, 26, 74];
            if (!empty($data['password']) && !in_array($id, $userNotUpdateExpiredAt)) {
                $user['expired_at'] = now();
            }
            $user->stores()->sync($request['store_ids']);
            $user->save();
            $user->warehouses()->sync($request['warehouse_ids']);
            if ($user->is_admin != User::ADMIN) {
                $role = Role::find($request['role_id']);
                $user->syncRoles([$role->name]);
            } else {
                $user->syncRoles([]);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update user successfully!', $user);
    }

    public function delete(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $user = User::find($id);
            if (!$user) {
                return $this->errorResponse('User not found!');
            }
            if ($user->is_admin != User::ADMIN) {
                $user->warehouses()->detach();
                $user->syncRoles([]);
            }
            $user->delete();
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Delete user successfully!');
    }
}
