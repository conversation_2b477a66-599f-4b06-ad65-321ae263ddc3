<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemError;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemImageUpload;
use App\Models\Setting;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SaleOrderItemImageRepository
{
    private $selectFields = [
        'id',
        'order_id',
        'order_item_id',
        'account_id',
        'store_id',
        'image_url',
        'sku',
        'product_sku',
        'print_side',
        'retry_download_manual_count',
        'last_retry_download_manual',
        'retry_detect_color_count',
        'last_retry_detect_color',
        'thumb_750',
        'skip_retry',
        'retry_count',
        'thumb_250',
        'color_new'
    ];

    protected $saleOrderRepository;

    protected $saleOrderItemRepository;

    public function __construct(SaleOrderRepository $saleOrderRepository, SaleOrderItemRepository $saleOrderItemRepository)
    {
        $this->saleOrderRepository = $saleOrderRepository;
        $this->saleOrderItemRepository = $saleOrderItemRepository;
    }

    public function getCountDownloadError()
    {
        $overdue = date('Y-m-d', strtotime('-30 day'));

        return SaleOrderItemImage::join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION])
            ->where('sale_order.order_date', '>=', $overdue)
            ->where(function ($q) {
                $q->where('thumb_250', SaleOrderItemImage::SYNC_S3_FAIL)
                    ->orWhere('color_new', SaleOrderItemImage::COLOR_ERROR);
            })->count();
    }

    public function getListDownloadError($request)
    {
        $limit = $request['limit'] ?? 25;
        $query = SaleOrderItemImage::addSelect($this->selectFields)
            ->with('order:id,order_number,external_id,external_number,order_date')
            ->with('store:id,name,code')
            ->with('account:id,name')
            ->where('thumb_250', 2)
            ->whereHas('order', function ($q) use ($request) {
                $overdue = date('Y-m-d', strtotime('-30 day'));
                $q->whereIn('order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION]);
                if (!empty($request['order_number'])) {
                    $q->where('order_number', $request['order_number']);
                }
                if (!empty($request['external_number'])) {
                    $q->where('external_number', $request['external_number']);
                }
                if (!empty($request['external_id'])) {
                    $q->where('external_id', $request['external_id']);
                }
                if (!empty($request['order_date'])) {
                    $q->whereDate('order_date', $request['order_date']);
                } else {
                    $q->whereDate('order_date', '>=', $overdue);
                }
            });

        if (!empty($request['store_id']) || !empty($request['store_name'])) {
            $query->whereHas('store', function ($q) use ($request) {
                if (!empty($request['store_id'])) {
                    $q->where('id', $request['store_id']);
                }
                if (!empty($request['store_name'])) {
                    $q->whereLike('name', '%' . $request['store_name'] . '%');
                }
            });
        }

        if (!empty($request['account_id']) || !empty($request['account_name'])) {
            $query->whereHas('account', function ($q) use ($request) {
                if (!empty($request['account_id'])) {
                    $q->where('id', $request['account_id']);
                }
                if (!empty($request['account_name'])) {
                    $q->whereLike('name', '%' . $request['account_name'] . '%');
                }
            });
        }

        if (!empty($request['sku'])) {
            $query->where('sku', $request['sku']);
        }

        if (!empty($request['product_sku'])) {
            $query->where('product_sku', $request['product_sku']);
        }

        return $query->paginate($limit);
    }

    public function getListManualUploaded($request)
    {
        $limit = $request['limit'] ?? 25;
        $query = SaleOrderItemImage::addSelect(($this->selectFields))
            ->with('order:id,order_number,external_id,external_number,order_date')
            ->with('store:id,name,code')
            ->with('account:id,name')
            ->has('uploadLogs')
            ->whereHas('order', function ($q) use ($request) {
                $overdue = date('Y-m-d', strtotime('-30 day'));
                $q->whereIn('order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION]);

                if (!empty($request['order_number'])) {
                    $q->where('order_number', $request['order_number']);
                }

                if (!empty($request['external_number'])) {
                    $q->where('external_number', $request['external_number']);
                }

                if (!empty($request['external_id'])) {
                    $q->where('external_id', $request['external_id']);
                }

                if (!empty($request['order_date'])) {
                    $q->whereDate('order_date', $request['order_date']);
                } else {
                    $q->whereDate('order_date', '>=', $overdue);
                }
            });

        if (!empty($request['store_id']) || !empty($request['store_name'])) {
            $query->whereHas('store', function ($q) use ($request) {
                if (!empty($request['store_id'])) {
                    $q->where('id', $request['store_id']);
                }

                if (!empty($request['store_name'])) {
                    $q->whereLike('name', '%' . $request['store_name'] . '%');
                }
            });
        }

        if (!empty($request['account_id']) || !empty($request['account_name'])) {
            $query->whereHas('account', function ($q) use ($request) {
                if (!empty($request['account_id'])) {
                    $q->where('id', $request['account_id']);
                }

                if (!empty($request['account_name'])) {
                    $q->whereLike('name', '%' . $request['account_name'] . '%');
                }
            });
        }

        if (!empty($request['sku'])) {
            $query->where('sku', $request['sku']);
        }

        if (!empty($request['product_sku'])) {
            $query->where('product_sku', $request['product_sku']);
        }

        return $query->paginate($limit);
    }

    public function getListDetectColorError($request)
    {
        $limit = $request['limit'] ?? 25;
        $query = SaleOrderItemImage::addSelect($this->selectFields)
            ->with('order:id,order_number,external_id,external_number,order_date')
            ->with('store:id,name,code')
            ->with('account:id,name')
            ->where('color_new', 3)
            ->whereHas('order', function ($q) use ($request) {
                $overdue = date('Y-m-d', strtotime('-30 day'));
                $q->whereIn('order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION]);

                if (!empty($request['order_number'])) {
                    $q->where('order_number', $request['order_number']);
                }

                if (!empty($request['external_number'])) {
                    $q->where('external_number', $request['external_number']);
                }

                if (!empty($request['external_id'])) {
                    $q->where('external_id', $request['external_id']);
                }

                if (!empty($request['order_date'])) {
                    $q->whereDate('order_date', $request['order_date']);
                } else {
                    $q->whereDate('order_date', '>=', $overdue);
                }
            });

        if (!empty($request['store_id']) || !empty($request['store_name'])) {
            $query->whereHas('store', function ($q) use ($request) {
                if (!empty($request['store_id'])) {
                    $q->where('id', $request['store_id']);
                }

                if (!empty($request['store_name'])) {
                    $q->whereLike('name', '%' . $request['store_name'] . '%');
                }
            });
        }

        if (!empty($request['account_id']) || !empty($request['account_name'])) {
            $query->whereHas('account', function ($q) use ($request) {
                if (!empty($request['account_id'])) {
                    $q->where('id', $request['account_id']);
                }

                if (!empty($request['account_name'])) {
                    $q->whereLike('name', '%' . $request['account_name'] . '%');
                }
            });
        }

        if (!empty($request['sku'])) {
            $query->where('sku', $request['sku']);
        }

        if (!empty($request['product_sku'])) {
            $query->where('product_sku', $request['product_sku']);
        }

        return $query->paginate($limit);
    }

    protected function handleSuccess(string $message, $data = null): array
    {
        $output = [
            'status' => true,
            'output' => [
                'message' => $message
            ]
        ];

        if (!empty($data)) {
            $output['output']['data'] = $data;
        }

        return $output;
    }

    protected function handleFail(string $message = 'The given data was invalid.', array $errors = []): array
    {
        $output = [
            'status' => false,
            'output' => [
                'message' => $message
            ]
        ];

        if (!empty($errors)) {
            $output['output']['errors'] = $errors;
        }

        return $output;
    }

    public function retryDownload($id)
    {
        $itemImage = SaleOrderItemImage::find($id);

        if (!empty($itemImage)) {
            //update thumb 750 de service download anh (command:thumb-artwork) tu chay
            $itemImage->thumb_750 = 0;
            $itemImage->skip_retry = 0;
            $itemImage->retry_count = 0;
            $itemImage->thumb_250 = 0;
            $itemImage->color_new = null;
            $itemImage->retry_download_manual_count++;
            $itemImage->last_retry_download_manual = Carbon::now()->toDateTimeString();
            $itemImage->save();

            return $this->handleSuccess('Retry download request successful.');
        } else {
            return $this->handleFail('Item image not found.');
        }
    }

    public function retryDetectColor($id)
    {
        $itemImage = SaleOrderItemImage::find($id);

        if (!empty($itemImage)) {
            //update thumb 750 de service detech mau (command:detect-ink-color) tu chay
            $itemImage->thumb_750 = 1;
            $itemImage->color_new = null;
            $itemImage->retry_detect_color_count++;
            $itemImage->last_retry_detect_color = Carbon::now()->toDateTimeString();
            $itemImage->save();

            return $this->handleSuccess('Retry detect color successful.');
        } else {
            return $this->handleFail('Item image not found.');
        }
    }

    public function manualUploadFile($id, $request)
    {
        $itemImage = SaleOrderItemImage::with('printSizeType')->find($id);

        if (!empty($itemImage)) {
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $filename = uniqid() . '-' . uniqid() . '.' . $file->getClientOriginalExtension();
                $filePath = SaleOrderItemImage::FOLDER_UPLOAD . date('Y-m-d') . '/';
                $r = Storage::disk('s3')->putFileAs(
                    $filePath,
                    $file,
                    $filename,
                );

                if (!$r) {
                    return $this->handleFail('Can not upload this file:' . $itemImage->image_url);
                }

                $dataImageOld = $itemImage->image_url;
                $itemImage->image_url = env('AWS_S3_URL', '') . '/' . $filePath . $filename;
                $itemImage->thumb_750 = 0;
                $itemImage->skip_retry = 0;
                $itemImage->retry_count = 0;
                $itemImage->thumb_250 = 0;
                $itemImage->color_new = null;
                $itemImage->save();
                SaleOrderItemImageUpload::create([
                    'user_id' => Auth::user()->id,
                    'url' => $itemImage->image_url,
                    'order_item_image_id' => $itemImage->id
                ]);
                $message = 'The ' . $itemImage->printSizeType?->name . " artwork for SKU $itemImage->sku was updated from \n\"$dataImageOld\"\n to \n\"$itemImage->image_url\"\n via the Print file error feature.";
                $this->saleOrderRepository->saveHistory($itemImage->order_id, SaleOrderHistory::UPDATE_PRINT_FILE_TYPE, $message);

                return $this->handleSuccess('File upload successful.');
            } else {
                return $this->handleFail('Image is invalid.');
            }
        } else {
            return $this->handleFail('Item image not found.');
        }
    }

    public static function pendingUploadToS3($limit = 10, $type = QueueJob::QUEUE_UPLOAD_S3)
    {
        // get 30 days ago
        $overdue = date('Y-m-d', strtotime('-30 day'));

        $saleOrderItemImageModel = new SaleOrderItemImage();
        $q = $saleOrderItemImageModel->select('sale_order_item_image.id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->where('sale_order_item_image.created_at', '>', $overdue) // 2023-01-01
            ->where('sale_order.is_test', 0)
            ->where('sale_order_item_image.thumb_250', 1)
            ->where('sale_order_item_image.thumb_750', 0)
            ->where('sale_order_item_image.skip_retry', 0);

        return $q->orderBy('sale_order_item_image.id', 'ASC')
            ->take($limit)
            ->pluck('id');
    }

    public static function pendingDetectColor($limit = 10)
    {
        $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
        $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
        $saleOrderItemImageModel = new SaleOrderItemImage();
        // get 30 days ago
        $overdue = date('Y-m-d', strtotime('-30 day'));
        $q = $saleOrderItemImageModel->select('sale_order_item_image.id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->join('image_hash', 'image_hash.id', '=', 'sale_order_item_image.image_hash_id')
            ->leftJoin(DB::raw('(
                SELECT images.order_id
                FROM sale_order_item_image AS images
                JOIN image_hash AS ih2 ON ih2.id = images.image_hash_id
                WHERE ih2.is_ip_violation IS NULL
                GROUP BY images.order_id
            ) as violation'), 'violation.order_id', '=', 'sale_order_item_image.order_id')
            //->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)) // 2023-01-01
            ->where('sale_order_item_image.created_at', '>', $overdue) // 2023-01-01
            ->where('sale_order.is_test', 0)
            ->whereNull('sale_order_item_image.color_new')
            ->where('sale_order_item_image.upload_s3_status', 1)
            ->where('sale_order_item_image.skip_retry', 0)
            ->where(function ($query) use ($storeExcludeCheckIP) {
                $query->whereNull('violation.order_id')
                    ->orWhereIn('sale_order_item_image.store_id', $storeExcludeCheckIP);
            });

        return $q->orderBy('sale_order_item_image.id', 'ASC')
            ->take($limit)
            ->pluck('id');
    }

    public static function pendingCreateThumbArtwork($limit = 10, $type = QueueJob::QUEUE_CREATE_THUMB_ARTWORK_URGENT)
    {
        $overdue = date('Y-m-d', strtotime('-30 day'));

        $saleOrderItemImageModel = new SaleOrderItemImage();
        $q = $saleOrderItemImageModel->select('sale_order_item_image.id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->where('sale_order_item_image.created_at', '>', $overdue) // 2023-01-01
            ->where('sale_order.is_test', 0)
            ->where('sale_order_item_image.thumb_750', 0)
            ->where('sale_order_item_image.skip_retry', 0);

        return $q->orderBy('sale_order_item_image.id', 'ASC')
            ->take($limit)
            ->pluck('id');
    }

    public static function pendingUpdateItemColorStatus($limit)
    {
        $overdue = date('Y-m-d', strtotime('-30 day'));

        return \DB::table('sale_order_item')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->select('sale_order_item.id')
            ->where('sale_order.is_test', 0)
            ->where('sale_order.order_status', '<>', SaleOrder::CANCELLED)
            ->whereNotExists(
                fn ($q) => $q->select('id')->from('sale_order_item_image')
                    ->whereColumn('sale_order_item.id', 'sale_order_item_image.order_item_id')
                    ->where(function ($sq) {
                        $sq->where('color_new', 3)->orWhere('color_new', null);
                    })
                    ->where('skip_retry', 0)
                    ->where('delete_status', 0),
            )->where('sale_order_item.created_at', '>', $overdue)
            ->where('sale_order_item.ink_color_status', 0)
            ->orderBy('sale_order_item.id', 'DESC')
            ->take($limit)
            ->get()->pluck('id');
    }

    public function tickPrintFileError($orderIds)
    {
        return SaleOrderItemImage::whereIn('order_id', $orderIds)->update([
            'upload_s3_status' => 0,
            'thumb_250' => 2,
            'skip_retry' => 1
        ]);
    }

    public function resetImages($imageIds)
    {
        $saleOrderItemImages = SaleOrderItemImage::whereIn('id', $imageIds)->get();

        foreach ($saleOrderItemImages as $image) {
            $this->resetImage([
                'order_item_id' => $image->order_item_id,
                'print_side' => $image->print_side,
                'print_file' => $image->image_url,
            ]);
        }

        return count($saleOrderItemImages);
    }

    public function updateArtFile($request)
    {
        $input = $request->validated();
        DB::beginTransaction();

        try {
            $orderItemBarcode = DB::table('sale_order_item_barcode')
                ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('sale_order_item_image', function ($join) {
                    $join->on('sale_order_item_image.sku', '=', 'sale_order_item_barcode.sku');
                    $join->on('sale_order_item_image.order_item_id', '=', 'sale_order_item_barcode.order_item_id');
                })
                ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
                ->join('product_print_side', 'product_print_side.code', '=', 'sale_order_item_image.print_side')
                ->where('sale_order_item.id', $input['order_item_id'])
                ->whereRaw("REPLACE(product_print_side.name, ' ', '') = ?", [$input['print_side']])
                ->first();

            if (!$orderItemBarcode) {
                throw new \Exception('Order item not found.', Response::HTTP_NOT_FOUND);
            }

            if (!in_array($orderItemBarcode->order_status, [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::SHIPPED, SaleOrder::ON_HOLD, SaleOrder::DRAFT])) {
                throw new \Exception('Cannot update art file due to unsatisfactory order status.', Response::HTTP_BAD_REQUEST);
            }

            $input['code'] = $orderItemBarcode->code;
            $oldPreviewFiles = '';
            $orderItem = SaleOrderItem::find($input['order_item_id']);
            $options = json_decode($orderItem->options);

            if (!empty($options)) {
                foreach ($options as $key => $option) {
                    $optionName = explode('.', $option->name);

                    if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PREVIEW_FILES && !empty($optionName[1]) && $optionName[1] == $input['print_side']) {
                        $oldPreviewFiles = $option->value;
                    }
                }
            }

            if ($request->has('print_file_raw') && !empty($request->print_file_raw)) {
                $filename = $orderItem->id . '-' . 'print_files' . '-' . rand() . ".{$request->print_file_raw->guessExtension()}";
                $folderPath = SaleOrderItemImage::IMAGE_FOLDER;
                $path = Storage::disk('s3')->putFileAs($folderPath, $request->print_file_raw, $filename);
                $input['print_file'] = env('AWS_S3_URL', '') . "/$path";
            }
            if ($request->has('preview_file_raw') && !empty($request->preview_file_raw)) {
                $filename = $orderItem->id . '-' . 'preview_files' . '-' . rand() . ".{$request->preview_file_raw->guessExtension()}";
                $folderPath = SaleOrderItemImage::IMAGE_FOLDER;
                $path = Storage::disk('s3')->putFileAs($folderPath, $request->preview_file_raw, $filename);
                $input['preview_file'] = env('AWS_S3_URL', '') . "/$path";
            }

            $printName = !empty($input['print_file_name']) ? $input['print_file_name'] : ($input['print_file'] ?? '');
            $previewName = !empty($input['preview_file_name']) ? $input['preview_file_name'] : ($input['preview_file'] ?? '');

            $this->saleOrderItemRepository->updateWithNewPrintFile($input);
            $employee = Employee::where('id', $input['employee_id'])->first();
            $history = [];

            if (!empty($input['print_file'])) {
                $oldImageUrl = $this->resetImage($input);
                $printSide = ProductPrintSide::whereRaw("REPLACE(name, ' ', '') = ?", [$input['print_side']])->first();
                if (!empty($printSide)) {
                    SaleOrderItemError::where('order_item_id', $input['order_item_id'])
                        ->where('side', $printSide->code_name)
                        ->delete();
                }
                $message = "The {$orderItemBarcode->name} artwork of SKU {$orderItemBarcode->sku} was updated by {$employee->name} from \n\"$oldImageUrl\"\n to \n\"{$printName}\"";
                $history[] = $this->saleOrderRepository->saveHistory($orderItemBarcode->order_id, SaleOrderHistory::UPDATE_ART_FILE_TYPE, $message, $employee->id);
            }

            if (!empty($input['preview_file'])) {
                $message = "The {$orderItemBarcode->name} mockup of SKU {$orderItemBarcode->sku} was updated by {$employee->name} from \n\"$oldPreviewFiles\"\n to \n\"{$previewName}\"";
                $history[] = $this->saleOrderRepository->saveHistory($orderItemBarcode->order_id, SaleOrderHistory::UPDATE_ART_FILE_TYPE, $message, $employee->id);
            }

            DB::commit();
            $data = [
                'history' => $history
            ];

            return [
                'status' => true,
                'code' => Response::HTTP_OK,
                'data' => $data,
                'message' => 'Update successfully.',
            ];
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'status' => false,
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
            ];
        }
    }

    public function resetImage($input)
    {
        $orderItemImage = SaleOrderItemImage::with('product.productStyle')
            ->where('order_item_id', $input['order_item_id'])
            ->where('print_side', $input['code'])->first();
        $oldImageUrl = $orderItemImage->image_url ?? '';

        if ($orderItemImage) {
            $updateSuccess = $orderItemImage->update([
                'image_url' => $input['print_file'],
                'color_new' => null,
                'thumb_750' => 0,
                'upload_s3_status' => 0,
                'thumb_250' => 0,
                'skip_retry' => 0,
                'retry_download_manual_count' => 0,
                'retry_detect_color_count' => 0,
                'last_retry_detect_color' => null,
                'retry_count' => 0,
            ]);
            $side = $orderItemImage->print_side;
            $path = $orderItemImage->order_date . '/' . $orderItemImage->sku . '-' . $side . '.png';

            if (!empty($orderItemImage->product?->productStyle?->type) && $orderItemImage->product?->productStyle?->type == ProductStyle::TYPE_INSERT && $orderItemImage->image_ext == 'pdf') {
                $path = $orderItemImage->order_date . '/' . $orderItemImage->sku . '-' . $side . '.' . $orderItemImage->image_ext;
            }

            if ($updateSuccess > 0) {
                Storage::disk('s3')->delete('artwork/' . $orderItemImage->order_date . '/trim/' . $orderItemImage->sku . '-' . $side . '.png');
                Storage::disk('s3')->delete('artwork/' . $path);
                Storage::disk('s3')->delete('thumb/proof/' . $path);
                Storage::disk('s3')->delete('thumb/250/' . $path);
                Storage::disk('s3')->delete('thumb/750/' . $path);
            }

            return $oldImageUrl;
        }
    }
}
