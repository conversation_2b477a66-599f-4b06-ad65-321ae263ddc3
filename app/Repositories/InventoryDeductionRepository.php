<?php

namespace App\Repositories;

use App\Imports\DeductionImport;
use App\Models\Box;
use App\Models\Country;
use App\Models\Inventory;
use App\Models\InventoryAddition;
use App\Models\InventoryDeduction;
use App\Models\InventoryDeductionCsv;
use App\Models\InventoryDeductionCsvItem;
use App\Models\Location;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\Warehouse;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class InventoryDeductionRepository
{
    const LIMIT = 10;

    private ProductRepositoryInterface $productRepository;

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function fetchDeduction($input)
    {
        $dataQuery = DB::table('inventory_deduction')
            ->select(
                'inventory_deduction.*',
                'product.name AS product_name',
                'user.username',
                'sale_order.external_number',
            )
            ->leftJoin('product', 'product.id', '=', 'inventory_deduction.product_id')
            ->leftJoin('user', 'user.id', '=', 'inventory_deduction.user_id')
            ->leftJoin('sale_order', 'sale_order.id', '=', 'inventory_deduction.sale_order_id')
            ->where('inventory_deduction.warehouse_id', $input['warehouse_id']);

        if (!empty($input['sku'])) {
            $dataQuery->where('inventory_deduction.sale_order_sku', 'LIKE', '%' . $input['sku'] . '%');
        }

        if (!empty($input['keyword'])) {
            $saleOrder = SaleOrder::select('id')
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('external_number', $input['keyword'])
                ->first();
            $dataQuery->where(function ($q) use ($saleOrder, $input) {
                $q->where('inventory_deduction.label_id', $input['keyword']);

                if ($saleOrder) {
                    $q->orWhere('inventory_deduction.sale_order_id', $saleOrder->id);
                }
            });
        }

        if (!empty($input['employee_id'])) {
            $dataQuery->where('inventory_deduction.employee_id', $input['employee_id']);
        }

        if (!empty($input['product_style'])) {
            $dataQuery->where('product.style', $input['product_style']);
        }

        if (!empty($input['date'])) {
            $data = $input['date'];

            if (!empty($data[0])) {
                $dataQuery->where('inventory_deduction.created_at', '>=', Carbon::parse($data[0])->startOfDay());
            }

            if (!empty($data[1])) {
                $dataQuery->where('inventory_deduction.created_at', '<=', Carbon::parse($data[1])->endOfDay());
            }
        }

        $data = $dataQuery->orderBy('inventory_deduction.id', 'desc')
            ->simplePaginate($input['limit'] ?? self::LIMIT);
        $dataPartNumber = PartNumber::all();
        $box_id = [];
        $coo_iso2 = [];

        foreach ($data as $item) {
            if (!empty($item->box_id)) {
                $box_id[] = $item->box_id;
            }

            if (!empty($item->coo_iso2)) {
                $coo_iso2[] = $item->coo_iso2;
            }
        }

        if (!empty($box_id)) {
            $box_id = array_unique($box_id);
            $box = Box::select('id', 'barcode')->whereIn('id', $box_id)->get();
            $box = $box->keyBy('id');
        }

        if (!empty($coo_iso2)) {
            $coo_iso2_id = array_unique($coo_iso2);
            $coo_iso2_id = Country::select('id', 'name', 'iso2')->whereIn('iso2', $coo_iso2_id)->get();
            $coo_iso2_id = $coo_iso2_id->keyBy('iso2');
        }

        foreach ($data as &$item) {
            if (!empty($item->box_id) && isset($box[$item->box_id])) {
                $item->box_barcode = $box[$item->box_id]?->barcode;
            } else {
                $item->box_barcode = '';
            }

            if (!empty($item->coo_iso2) && isset($coo_iso2_id[$item->coo_iso2])) {
                $item->coo_name = $coo_iso2_id[$item->coo_iso2]?->name;
            } else {
                $item->coo_name = '';
            }

            if (!empty($item->product_id) && !empty($item->coo_iso2)) {
                $item->part_number = $dataPartNumber->where('product_id', $item->product_id)->where('country', $item->coo_iso2)->first()?->part_number;
            } else {
                $item->part_number = '';
            }

            $item->created_at_pst = convertTimeUTCToPST($item->created_at)->format('Y-m-d H:i:s');
        }

        return $data;
    }

    public function fetchDeductionBackup($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = DB::table('inventory_deduction')
            ->select(
                'inventory_deduction.*',
                'product.name AS product_name',
                'user.username',
                'sale_order.external_number',
            )
            ->leftJoin('product', 'product.id', '=', 'inventory_deduction.product_id')
            ->leftJoin('user', 'user.id', '=', 'inventory_deduction.user_id')
            ->leftJoin('sale_order', 'sale_order.id', '=', 'inventory_deduction.sale_order_id')
            ->where('inventory_deduction.warehouse_id', $input['warehouse_id']);

        if (!empty($input['sku'])) {
            $query->where('inventory_deduction.sale_order_sku', 'LIKE', '%' . $input['sku'] . '%');
        }

        if (!empty($input['keyword'])) {
            $saleOrder = SaleOrder::select('id')
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('external_number', $input['keyword'])
                ->first();
            $query->where(function ($q) use ($saleOrder, $input) {
                $q->where('inventory_deduction.label_id', $input['keyword']);

                if ($saleOrder) {
                    $q->orWhere('inventory_deduction.sale_order_id', $saleOrder->id);
                }
            });
        }

        if (!empty($input['employee_id'])) {
            $query->where('inventory_deduction.employee_id', $input['employee_id']);
        }

        if (!empty($input['product_style'])) {
            $query->where('product.style', $input['product_style']);
        }

        if (!empty($input['date'])) {
            $data = $input['date'];

            if (!empty($data[0])) {
                $query->whereDate('inventory_deduction.created_at', '>=', $data[0]);
            }

            if (!empty($data[1])) {
                $query->whereDate('inventory_deduction.created_at', '<=', $data[1]);
            }
        }

        $data = $query->orderBy('inventory_deduction.id', 'desc')
            ->paginate($limit);
        $dataPartNumber = PartNumber::all();
        $box_id = [];
        $coo_iso2 = [];

        foreach ($data->items() as $item) {
            if (!empty($item->box_id)) {
                $box_id[] = $item->box_id;
            }

            if (!empty($item->coo_iso2)) {
                $coo_iso2[] = $item->coo_iso2;
            }
        }

        if (!empty($box_id)) {
            $box_id = array_unique($box_id);
            $box = Box::select('id', 'barcode')->whereIn('id', $box_id)->get();
            $box = $box->keyBy('id');
        }

        if (!empty($coo_iso2)) {
            $coo_iso2_id = array_unique($coo_iso2);
            $coo_iso2_id = Country::select('id', 'name', 'iso2')->whereIn('iso2', $coo_iso2_id)->get();
            $coo_iso2_id = $coo_iso2_id->keyBy('iso2');
        }

        foreach ($data->items() as &$item) {
            if (!empty($item->box_id) && isset($box[$item->box_id])) {
                $item->box_barcode = $box[$item->box_id]?->barcode;
            } else {
                $item->box_barcode = '';
            }

            if (!empty($item->coo_iso2) && isset($coo_iso2_id[$item->coo_iso2])) {
                $item->coo_name = $coo_iso2_id[$item->coo_iso2]?->name;
            } else {
                $item->coo_name = '';
            }

            if (!empty($item->product_id) && !empty($item->coo_iso2)) {
                $item->part_number = $dataPartNumber->where('product_id', $item->product_id)->where('country', $item->coo_iso2)->first()?->part_number;
            } else {
                $item->part_number = '';
            }

            $item->created_at_pst = convertTimeUTCToPST($item->created_at)->format('Y-m-d H:i:s');
        }

        return $data;
    }

    public function getCsvHistoryList($request): Paginator
    {
        return InventoryDeductionCsv::with('user')
            ->where('import_status', InventoryDeductionCsv::SUCCESS)
            ->orderBy($request->sort_column ?? 'updated_at', $request->sort_by ?? 'DESC')
            ->paginate($request->limit ?? 10);
    }

    /**
     * @return bool
     */
    public function isLabel($label)
    {
        $label = trim($label);
        $i = explode('-', $label);

        if (!isset($i[1])) {
            return false;
        }

        $warehouse = Warehouse::where('code', $i[1])->first();

        if (!empty($warehouse)) {
            return true;
        }

        return false;
    }

    public function create($input, $quantity = 1, $deductionCsvId = null)
    {
        if (isset($input['label_id'])) {
            $input['sale_order_sku'] = $input['label_id'];
            $input['label_id'] = null;
        }

        $isLabel = $this->isLabel($input['sale_order_sku']);
        $saleOrderItemBarcode = null;

        if ($isLabel) {
            $saleOrderItemBarcode = SaleOrderItemBarcode::with('saleOrder:id,order_status')
                ->where('label_id', $input['sale_order_sku'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->first();

            if (!$saleOrderItemBarcode) {
                return response()->json(['sale_order_sku' => ['Label not found']], 422);
            }

            if ($saleOrderItemBarcode->is_deleted) {
                return response()->json(['sale_order_sku' => ['Barcode not exists']], 422);
            }

            $orderStatus = [
                SaleOrder::DRAFT,
                SaleOrder::ON_HOLD,
                SaleOrder::CANCELLED,
                SaleOrder::REJECTED,
                SaleOrder::STATUS_LATE_CANCELLED,
            ];

            if (in_array($saleOrderItemBarcode->saleOrder->order_status, $orderStatus)) {
                return response()->json([
                    'sale_order_sku' => [
                        'Sale order is ' . str_replace('_', ' ', $saleOrderItemBarcode->saleOrder->order_status),
                    ]
                ], 422);
            }

            $input['sale_order_sku'] = $saleOrderItemBarcode->sku;
            $input['label_id'] = $saleOrderItemBarcode->label_id;
            $sku = substr($saleOrderItemBarcode->sku, -9);
        } else {
            $sku = substr($input['sale_order_sku'], -9);
        }

        $product = Product::where('sku', $sku)->first();

        if (empty($product)) {
            return response()->json(['sale_order_sku' => ['Product not found']], 422);
        }

        $countryBox = null;
        $box = null;
        $partNumber = null;

        if (in_array($input['warehouse_id'], Warehouse::WAREHOUSE_MEXICO)) {
            $box = Box::where('barcode', $input['box_barcode'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->first();

            if (!$box) {
                return response()->json(['box_barcode' => ['Box not found']], 422);
            }

            if (!empty($box->country)) {
                $countryBox = $box->country;
            } else {
                $countryBox = InventoryAddition::where('box_id', $box->id)
                    ->where('warehouse_id', $input['warehouse_id'])
                    ->first()?->country;
            }

            if (!$countryBox) {
                return response()->json(['box_barcode' => ['Inventory addition not found']], 422);
            }

            if ($box->product_id != $product->id) {
                return response()->json(['box_barcode' => ['The scanned box must contain the product corresponding to the Label ID']], 422);
            }

            $partNumber = PartNumber::where('country', $countryBox)
                ->where('product_id', $product->id)
                ->first();

            if (!$partNumber) {
                return response()->json(['sale_order_sku' => ['This SKU and country of origin do not have a corresponding part number.']], 422);
            }
        }

        $input['product_id'] = $product->id;
        $input['quantity'] = $quantity;

        if ($deductionCsvId) {
            $input['deduction_csv_id'] = $deductionCsvId;
        }

        $input = $this->buildData($input);

        try {
            DB::transaction(function () use ($quantity, $saleOrderItemBarcode, $input, $isLabel, $countryBox, $box, $partNumber) {
                $objectName = Inventory::OBJECT_DEDUCTION;

                if ($saleOrderItemBarcode) {
                    $input['sale_order_id'] = $saleOrderItemBarcode->order_id;
                    $input['employee_id'] = $saleOrderItemBarcode->employee_pull_id;
                }

                if ($isLabel) {
                    // check duplicate
                    $existedLabel = InventoryDeduction::query()
                        ->where('label_id', $input['label_id'])
                        ->where('is_deleted', 0)->first();

                    if ($existedLabel) {
                        $input['is_duplicate'] = 1;
                        $objectName = Inventory::OBJECT_DEDUCTION_SPOILAGE;
                        InventoryDeduction::query()->where('label_id', $input['label_id'])->update(['is_duplicate' => 1]);
                    }
                }

                $input['coo_iso2'] = $countryBox;
                $input['box_id'] = $box?->id;

                if (empty($input['sale_order_id'])) {
                    $objectName = Inventory::OBJECT_DEDUCTION_UNLINKED;
                }

                $inventoryDeduction = InventoryDeduction::create($input);

                if ($isLabel) {
                    SaleOrderItemBarcode::where('label_id', $input['label_id'])->update([
                        'pulled_at' => Carbon::now()->toDateTimeString(),
                        'part_number_id' => $partNumber?->id ?? null,
                    ]);
                }

                $inputInventory['direction'] = Inventory::DIRECTION_OUTPUT;
                $inputInventory['type'] = Inventory::TYPE_OUTPUT;
                $inputInventory['product_id'] = $input['product_id'];
                $inputInventory['warehouse_id'] = $input['warehouse_id'];
                $inputInventory['location_id'] = $input['location_id'];
                $inputInventory['user_id'] = $input['user_id'];
                $inputInventory['object_id'] = $inventoryDeduction->id;
                $inputInventory['object_name'] = $objectName;
                $inputInventory['quantity'] = $quantity;
                Inventory::create($inputInventory);

                ///Todo : update quantity product
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -$quantity);
                ///Todo : update Location Product
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -$quantity);

                if (isset($saleOrderItemBarcode->order_id) && $saleOrderItemBarcode->order_id) {
                    $this->updatePulledAt($saleOrderItemBarcode->order_id);
                }

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $inventoryDeduction->sale_order_id);

                ///Todo : log time tracking for Employee
                if (!empty($inventoryDeduction->employee_id)) {
                    TimeCheckingRepository::createTimeTrackingWhenDeduction($inventoryDeduction->employee_id, $inventoryDeduction->created_at);
                }

                if ($isLabel) {
                    saleOrderHistory($input['user_id'], null, $saleOrderItemBarcode->order_id, SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE, 'Deduction label ID ' . $input['label_id'] . ' created successfully');
                }
            });
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        $dataRes = InventoryDeduction::with('country:name,iso2', 'box:id,barcode')->latest('id')->with('product')->first();
        $dataRes->part_number = $partNumber?->part_number ?? null;

        return response()->json($dataRes, 201);
    }

    public function updatePulledAt($orderId)
    {
        $order = SaleOrder::query()->where('id', $orderId)->first();

        // neu order ko phai single thi bo qua
        if ($order->order_quantity > 1) {
            return false;
        }

        $totalBarcodeOfSaleOrderNotPulled = SaleOrderItemBarcode::query()
            ->where('order_id', $orderId)
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->whereNull('employee_pull_id')
            ->count();

        //Todo : kiểm tra tất cả item của sale order đã dc Pull hay chưa ?
        if (!$totalBarcodeOfSaleOrderNotPulled) {
            //Todo : Nếu tất cả đã dc pull hết rồi thì set order_pulled_status = 1 cho sale_order đấy
            // chỉ update cho order có quantity = 1
            return SaleOrder::query()
                ->where('id', $orderId)
                ->update([
                    'order_pulled_status' => 1,
                    'order_pulled_at' => date('Y-m-d H:i:s')
                ]);
        }
    }

    private function buildData(&$input)
    {
        $input['created_at'] = date('Y-m-d H:i:s');
        $locationPullingShelve = Location::where('type', 1)
            ->where('warehouse_id', $input['warehouse_id'])
            ->first();
        $input['location_id'] = $locationPullingShelve->id;

        return $input;
    }

    public function revertDeduction($id)
    {
        try {
            $deduction = InventoryDeduction::query()
                ->where('id', $id)
                ->where('is_deleted', '<>', 1)
                ->first();

            if (!$deduction) {
                return response()->json(['message' => 'Inventory deduction not exists.'], 404);
            }

            DB::transaction(function () use ($id, $deduction) {
                InventoryDeduction::query()
                    ->where('id', $id)
                    ->update(['is_deleted' => 1]);
                $inventoryDeduction = Inventory::query()
                    ->whereIn('object_name', [
                        Inventory::OBJECT_DEDUCTION,
                        Inventory::OBJECT_DEDUCTION_SPOILAGE,
                        Inventory::OBJECT_DEDUCTION_UNLINKED,
                    ])
                    ->where('object_id', $id)
                    ->first();

                $newInventory = $inventoryDeduction->replicate();
                $newInventory->direction = Inventory::DIRECTION_INPUT;
                $newInventory->type = Inventory::TYPE_REVERT;
                $newInventory->object_name = Inventory::OBJECT_REVERT_DEDUCTION;
                $newInventory->fifo_calculated_at = Inventory::FIFO_NOT_CALCULATED;
                $newInventory->stock_qty_ending = 0;
                $newInventory->stock_value_ending = 0;
                $newInventory->remaining_qty = 0;
                $newInventory->cost_total = 0;
                $newInventory->created_at = now('UTC');
                $newInventory->user_id = auth()->id() ?? $inventoryDeduction->user_id;
                $newInventory->replicated_from_id = $inventoryDeduction->id;
                $newInventory->is_deleted = 1;
                $newInventory->save();

                $inventoryDeduction->is_deleted = 1;
                $inventoryDeduction->save();

                if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                    $barcodeLabel = SaleOrderItemBarcode::where('label_id', $deduction->label_id)->where('is_deleted', '<>', 1)->first();

                    if (!$barcodeLabel) {
                        throw new \Exception('The barcode for this deduction is deleted.', 404);
                    }

                    $allInventoryDeduction = InventoryDeduction::where('label_id', $deduction->label_id)->where('is_deleted', '<>', 1)->orderByDesc('id')->get();

                    if ($allInventoryDeduction->count() == 0) {
                        $barcodeLabel->part_number_id = null;
                        $barcodeLabel->save();
                    } else {
                        $partNumber = PartNumber::where('product_id', $allInventoryDeduction[0]->product_id)->where('country', $allInventoryDeduction[0]->coo_iso2)->first();
                        $barcodeLabel->part_number_id = $partNumber->id;
                        $barcodeLabel->save();
                    }
                }

                ///Todo : revert lai quantity cua product
                ProductQuantityRepository::updateQuantity($deduction->warehouse_id, $deduction->product_id, $deduction->quantity);
                ///Todo : update Location Product
                LocationProductRepository::updateQuantity($deduction->location_id, $deduction->product_id, $deduction->quantity);

                if ($deduction->label_id) {
                    saleOrderHistory(
                        auth()->user()->id,
                        null,
                        $deduction->sale_order_id,
                        SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE,
                        'Reverse deduction label ID ' . $deduction->label_id . ' successfully.');
                }

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $deduction->sale_order_id);
            });
        } catch (Exception $exception) {
            if ($exception->getCode() === 404) {
                return response()->json(['message' => $exception->getMessage()], 404);
            }

            return response()->json($exception->getMessage(), 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function verifyCsvFile($file): JsonResponse
    {
        DB::beginTransaction();

        try {
            $fileData = Excel::toCollection(new DeductionImport, $file)[0];
            $validFile = true;
            $verifyResult = [];
            $totalRow = count($fileData) - 1;
            $totalQuantity = 0;
            $totalSuccess = 0;

            foreach ($fileData as $key => $row) {
                if ($key == 0) {
                    continue;
                }

                $productSku = trim($row[0]);
                $quantity = (int) $row[1];

                if (!$productSku && !$quantity) {
                    continue;
                }

                $totalQuantity += $quantity;
                $product = $this->productRepository->existsSku($productSku);
                $status = $product && $quantity > 0;
                $verifyResult[] = [
                    'product_sku' => $row[0],
                    'quantity' => $row[1],
                    'status' => $status
                ];

                if (!$status) {
                    $validFile = false;

                    continue;
                }

                $totalSuccess++;
            }

            if (!count($verifyResult)) {
                DB::rollBack();

                return response()->json([
                    'status' => false,
                    'validation_result' => [],
                    'message' => 'File has no data!'
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $summaryInfo = [
                'validation_result' => $verifyResult,
                'total_quantity' => $totalQuantity,
                'total_row' => $totalRow,
                'total_success' => $totalSuccess,
                'total_error' => $totalRow - $totalSuccess
            ];

            if (!$validFile) {
                DB::rollBack();

                return response()->json(array_merge($summaryInfo, [
                    'status' => false,
                    'message' => 'Sku does not exist on the inventory, please try again!'
                ]), Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $inventoryDeductionCsv = $this->saveCsvFile($file, $summaryInfo);

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            Log::error('InventoryDeductionRepository.verifyCsvFile', [$exception]);

            return response()->json([
                'status' => false,
                'message' => 'An error occurred, please try again later!'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json(array_merge($summaryInfo, [
            'status' => true,
            'csv_id' => $inventoryDeductionCsv->id
        ]));
    }

    private function saveCsvFile($file, $summaryInfo)
    {
        $path = $file->store(InventoryDeductionCsv::FOLDER_UPLOAD, 's3');
        $inventoryDeductionCsv = InventoryDeductionCsv::create([
            'user_id' => auth()->user()['id'],
            'file' => $path,
            'original_file_name' => $file->getClientOriginalName(),
            'warehouse_id' => config('jwt.warehouse_id'),
            'import_status' => InventoryDeductionCsv::PENDING,
            'total_row' => $summaryInfo['total_row'],
            'total_quantity' => $summaryInfo['total_quantity']
        ]);

        foreach ($summaryInfo['validation_result'] as $row) {
            InventoryDeductionCsvItem::create([
                'deduction_csv_id' => $inventoryDeductionCsv->id,
                'sku' => $row['product_sku'],
                'quantity' => $row['quantity'],
                'status' => InventoryDeductionCsvItem::PENDING
            ]);
        }

        return $inventoryDeductionCsv;
    }

    public function importCsvFile(int $id): JsonResponse
    {
        $inventoryDeductionCsv = InventoryDeductionCsv::where([
            'id' => $id,
            'import_status' => InventoryDeductionCsv::PENDING
        ])->first();

        if (!$inventoryDeductionCsv) {
            return response()->json([
                'status' => false,
                'message' => 'Csv file not found!'
            ], Response::HTTP_NOT_FOUND);
        }

        DB::beginTransaction();

        try {
            $inventoryDeductionCsvItems = InventoryDeductionCsvItem::where('deduction_csv_id', $id)->get();

            foreach ($inventoryDeductionCsvItems as $inventoryDeductionCsvItem) {
                $this->create([
                    'sale_order_sku' => $inventoryDeductionCsvItem->sku,
                    'user_id' => $inventoryDeductionCsv->user_id,
                    'warehouse_id' => $inventoryDeductionCsv->warehouse_id
                ], $inventoryDeductionCsvItem->quantity, $id);
                $inventoryDeductionCsvItem->status = InventoryDeductionCsvItem::SUCCESS;
                $inventoryDeductionCsvItem->save();
            }

            $inventoryDeductionCsv->import_status = InventoryDeductionCsv::SUCCESS;
            $inventoryDeductionCsv->save();

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            Log::error('InventoryDeductionRepository.importCsvFile', [$exception]);

            return response()->json($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'status' => true,
            'message' => 'Successfully imported inventory deduction!'
        ]);
    }

    public function deductionSku($sku_deduction, $sale_order_sku, $barcodeBox = null)
    {
        $isLabel = $this->isLabel($sale_order_sku);

        if ($isLabel) {
            $saleOrderItemBarcode = SaleOrderItemBarcode::with('saleOrder')
                ->where('label_id', $sale_order_sku)
                ->first();

            if (!$saleOrderItemBarcode) {
                return response()->json(['sale_order_sku' => ['Label not found']], 422);
            }

            $sale_order_sku = $saleOrderItemBarcode->sku;
            $input['label_id'] = $saleOrderItemBarcode->label_id;
            $sku_wip = substr($saleOrderItemBarcode->sku, -9);
        } else {
            $saleOrderItemBarcode = SaleOrderItemBarcode::with('saleOrder')->where('sku', $sale_order_sku)->first();

            if (!$saleOrderItemBarcode) {
                return response()->json(['sale_order_sku' => ['Label not found']], 422);
            }

            $input['label_id'] = $saleOrderItemBarcode->label_id;
            $sku_wip = substr($sale_order_sku, -9);
        }

        $orderStatus = [
            SaleOrder::DRAFT,
            SaleOrder::ON_HOLD,
            SaleOrder::CANCELLED,
            SaleOrder::REJECTED,
            SaleOrder::STATUS_LATE_CANCELLED,
        ];

        if (!empty($saleOrderItemBarcode->saleOrder) && in_array($saleOrderItemBarcode->saleOrder->order_status, $orderStatus)) {
            return response()->json(['sale_order_sku' => ['Sale order is ' . str_replace('_', ' ', $saleOrderItemBarcode->saleOrder->order_status)]], 422);
        }

        if ($saleOrderItemBarcode->is_deleted) {
            return response()->json(['sale_order_sku' => ['Barcode not exists']], 422);
        }

        $input['sale_order_sku'] = $sale_order_sku;
        $productWIP = Product::where('sku', $sku_wip)->first();
        $productDeduction = Product::where('sku', $sku_deduction)->first();

        if (empty($productWIP) || empty($productDeduction)) {
            return response()->json(['sale_order_sku' => ['Product not found']], 422);
        }

        $input['product_id'] = $productDeduction->id;
        $input['wip_product_id'] = $productWIP->id;
        $input['created_at'] = date('Y-m-d H:i:s');
        $locationPullingShelve = Location::where('type', 1)->where('warehouse_id', config('jwt.warehouse_id'))->first();
        $input['location_id'] = $locationPullingShelve->id;
        $input['warehouse_id'] = config('jwt.warehouse_id');
        $input['user_id'] = auth()->user()->id;
        $input['product_sku'] = $productDeduction->sku;
        $countryBox = null;
        $box = null;
        $partNumber = null;

        if (in_array($input['warehouse_id'], Warehouse::WAREHOUSE_MEXICO)) {
            $box = Box::where('barcode', $barcodeBox)
                ->where('warehouse_id', $input['warehouse_id'])
                ->first();

            if (!$box) {
                return response()->json(['box_barcode' => ['Box not found']], 422);
            }

            if (!empty($box->country)) {
                $countryBox = $box->country;
            } else {
                $countryBox = InventoryAddition::where('box_id', $box->id)->where('warehouse_id', $input['warehouse_id'])->first()?->country;
            }

            if (!$countryBox) {
                return response()->json(['box_barcode' => ['Inventory addition not found']], 422);
            }

            if ($box->product_id != $productDeduction->id) {
                return response()->json(['box_barcode' => ['The scanned box must contain the product corresponding to the Label ID']], 422);
            }

            $partNumber = PartNumber::where('country', $countryBox)->where('product_id', $productDeduction->id)->first();

            if (!$partNumber) {
                return response()->json(['sale_order_sku' => ['This SKU and country of origin do not have a corresponding part number.']], 422);
            }
        }
        try {
            DB::transaction(function () use ($sale_order_sku, $saleOrderItemBarcode, $input, $isLabel, $countryBox, $box, $partNumber) {
                $objectName = Inventory::OBJECT_DEDUCTION;

                if ($saleOrderItemBarcode) {
                    $input['sale_order_id'] = $saleOrderItemBarcode->order_id;
                    $input['employee_id'] = $saleOrderItemBarcode->employee_pull_id;
                }

                if ($isLabel) {
                    // check duplicate
                    $existedLabel = InventoryDeduction::query()->where('label_id', $input['label_id'])
                        ->where('is_deleted', 0)->first();

                    if ($existedLabel) {
                        $objectName = Inventory::OBJECT_DEDUCTION_SPOILAGE;
                        $input['is_duplicate'] = 1;
                        InventoryDeduction::query()->where('label_id', $input['label_id'])->update(['is_duplicate' => 1]);
                    }
                }

                $input['coo_iso2'] = $countryBox;
                $input['box_id'] = $box?->id;

                if (empty($input['sale_order_id'])) {
                    $objectName = Inventory::OBJECT_DEDUCTION_UNLINKED;
                }

                $inventoryDeduction = InventoryDeduction::create($input);

                if (!$isLabel) {
                    SaleOrderItemBarcode::where('sku', $sale_order_sku)->whereNull('pulled_at')->limit(1)->update([
                        'pulled_at' => Carbon::now()->toDateTimeString(),
                        'part_number_id' => $partNumber?->id,
                    ]);
                } else {
                    SaleOrderItemBarcode::where('label_id', $input['label_id'])->update([
                        'pulled_at' => Carbon::now()->toDateTimeString(),
                        'part_number_id' => $partNumber?->id,
                    ]);
                }

                $inputInventory['direction'] = Inventory::DIRECTION_OUTPUT;
                $inputInventory['type'] = Inventory::TYPE_OUTPUT;
                $inputInventory['product_id'] = $input['product_id'];
                $inputInventory['warehouse_id'] = $input['warehouse_id'];
                $inputInventory['location_id'] = $input['location_id'];
                $inputInventory['user_id'] = auth()->user()->id;
                $inputInventory['object_id'] = $inventoryDeduction->id;
                $inputInventory['object_name'] = $objectName;
                $inputInventory['quantity'] = 1;
                Inventory::create($inputInventory);
                ///Todo : update quantity product
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -1);
                ///Todo : update Location Product
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -1);

                if (isset($saleOrderItemBarcode->order_id) && $saleOrderItemBarcode->order_id) {
                    $this->updatePulledAt($saleOrderItemBarcode->order_id);
                }

                ///Todo : log time tracking for Employee
                if (!empty($inventoryDeduction->employee_id)) {
                    TimeCheckingRepository::createTimeTrackingWhenDeduction($inventoryDeduction->employee_id, $inventoryDeduction->created_at);
                }

                //log timeline for order with sku
                saleOrderHistory(
                    $input['user_id'],
                    $input['employee_id'],
                    $input['sale_order_id'],
                    SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE,
                    'Deduction label ID ' . $input['label_id'] . ' for SKU ' . $input['product_sku'] . ' successfully.',
                );
            });
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

        $dataRes = InventoryDeduction::latest('id')
            ->with([
                'product',
                'wipProduct',
                'country:id,iso2,name',
                'box:id,barcode',
            ])
            ->first();
        $dataRes->part_number = $partNumber?->part_number;

        return response()->json($dataRes, 201);
    }
}
