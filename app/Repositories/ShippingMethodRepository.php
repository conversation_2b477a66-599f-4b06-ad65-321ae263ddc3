<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\ShippingCarrierEasypost;
use App\Models\ShippingMethod;
use App\Models\Store;
use Illuminate\Support\Facades\Auth;

class ShippingMethodRepository extends CommonRepository
{
    public function fetchName($request)
    {
        $dataShipping = ShippingMethod::with('shippingCarrier', 'shippingCarrierService')
            ->groupBy('api_shipping_method')
            ->orderBy('store_id', 'ASC')
            ->orderBy('api_shipping_method', 'DESC')
            ->get();

        return $dataShipping->map(function ($item) {
            return [
                'api_shipping_method' => $item->api_shipping_method,
                'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method),
                'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
            ];
        });
    }

    public function getAll($request)
    {
        $dataShipping = ShippingMethod::with('shippingCarrier', 'shippingCarrierService')
            ->groupBy('api_shipping_method')
            ->orderBy('store_id', 'ASC')
            ->orderBy('api_shipping_method', 'DESC')
            ->get();

        $data = [];
        $dataShipping->map(function ($item) use (&$data) {
            if (count($data) == 0) {
                $data[] = [
                    'api_shipping_method' => $item->api_shipping_method,
                    'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method),
                    'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
                ];
            } else {
                $isExist = false;
                foreach ($data as &$itemData) {
                    $shipping_carrier = $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method);
                    $shipping_carrier_service = $item->shippingCarrierService->display_name ?? '';
                    if ($itemData['shipping_carrier'] == $shipping_carrier && $itemData['shipping_carrier_service'] == $shipping_carrier_service) {
                        $isExist = true;
                        $itemData['api_shipping_method'] .= (empty($itemData['api_shipping_method']) ? '' : ',') . $item->api_shipping_method;
                    }
                }
                if (!$isExist) {
                    $data[] = [
                        'api_shipping_method' => $item->api_shipping_method,
                        'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method),
                        'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
                    ];
                }
            }
        });

        return $data;
    }

    public function getShippingAccountByWarehouse($warehouseId)
    {
        return ShippingCarrierEasypost::select('name', 'id', 'status', 'carrier_id')
                ->where('warehouse_id', $warehouseId)
                ->where('status', true)
                ->whereHas('shippingMethod', function ($query) {
                    $query->whereNotIn('api_shipping_method', [SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_PRIORITY, SaleOrder::SHIPPING_METHOD_EXPRESS]);
                })
                ->whereNotIn('store_id', [Store::STORE_DEFAULT_SHIPPING])
                ->groupBy('name')
            ->get();
    }

    public function getAllForSeller($request)
    {
        if (!isset($request['is_support_login']) || $request['is_support_login'] != 1) {
            $request['store_id'] = Auth::id();
        }

        $dataShipping = ShippingMethod::with(['shippingCarrier', 'shippingCarrierService'])
            ->groupBy('api_shipping_method')
            ->orderBy('store_id', 'ASC')
            ->orderBy('api_shipping_method', 'DESC');

        if (!empty($request['store_id'])) {
            $dataShipping->where('store_id', $request['store_id']);
        }

        $dataShipping = $dataShipping->get();

        $data = [];
        $dataShipping->map(function ($item) use (&$data) {
            if (count($data) == 0) {
                $data[] = [
                    'api_shipping_method' => $item->api_shipping_method,
                    'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method),
                    'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
                ];
            } else {
                $isExist = false;
                foreach ($data as &$itemData) {
                    $shipping_carrier = $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method);
                    $shipping_carrier_service = $item->shippingCarrierService->display_name ?? '';
                    if ($itemData['shipping_carrier'] == $shipping_carrier && $itemData['shipping_carrier_service'] == $shipping_carrier_service) {
                        $isExist = true;
                        $itemData['api_shipping_method'] .= (empty($itemData['api_shipping_method']) ? '' : ',') . $item->api_shipping_method;
                    }
                }
                if (!$isExist) {
                    $data[] = [
                        'api_shipping_method' => $item->api_shipping_method,
                        'shipping_carrier' => $item->shippingCarrierService ? $item->shippingCarrier->name ?? '' : ucfirst($item->api_shipping_method),
                        'shipping_carrier_service' => $item->shippingCarrierService->display_name ?? '',
                    ];
                }
            }
        });

        return $data;
    }
}
