<?php

namespace App\Repositories;

use App\Jobs\ReceiveResponseDetectDesignJob;
use App\Models\EasyPostLog;
use App\Models\ImageHash;
use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\Product;
use App\Models\ProductTiktok;
use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentEasypost as ShipmentEasypostModel;
use App\Models\ShipmentItem;
use App\Models\ShipmentItemLabel;
use App\Models\ShippingCarrierEasypost;
use App\Models\VisuaDetectImage;
use EasyPost\EasyPost;
use EasyPost\Error as EasyPostError;
use EasyPost\Shipment as ShipmentEasyPost;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class TechRepository extends CommonRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function updateLabelUrl($input)
    {
        Log::channel('tech_support')->info('Update label url ' . $input['label_url'] . ' for shipment id ' . $input['id']);
        $shipmentId = $input['id'];
        if (Storage::disk('s3')->exists("/label/$shipmentId.pdf")) {
            Storage::disk('s3')->delete("/label/$shipmentId.pdf");
        }
        Shipment::where('id', $shipmentId)->update(['label_url' => $input['label_url']]);

        return $this->successResponse('Label updated successfully.');
    }

    public function updateIPViolationImage($input)
    {
        ImageHash::whereIn('id', $input['images_hash'])->update([
            'is_ip_violation' => null,
            'user_review_id' => null,
            'user_review_time' => null,
            'user_check_time' => null,
            'user_check_id' => null,
            'is_user_check_ip_violation' => null,
        ]);

        return $this->successResponse('Update successfully.');
    }

    public function markIPViolationImage($input)
    {
        ImageHash::whereIn('id', $input['images_hash'])->update([
            'user_id' => ImageHash::ID_EVA,
            'is_ip_violation' => ImageHash::IS_IP_VIOLATION,
        ]);

        return $this->successResponse('Update successfully.');
    }

    public function retryVisuaDetectImageBySessionId($input)
    {
        ReceiveResponseDetectDesignJob::dispatch(['sessionId' => $input['visua_session_id']])
            ->onQueue(VisuaDetectImage::RECEIVE_RESPONSE_DETECT_IMAGE_JOB);

        return $this->successResponse('Retry successfully.');
    }

    public function updateOrderStatus($input)
    {
        $saleOrder = SaleOrder::where('id', $input['id'])->first();
        $saleOrder->order_status = $input['status'];
        $saleOrder->save();

        // tinh toan cho TH don label khi chuyen sang shipped -> update created_at de dua vao invoice
        $shipment = Shipment::with('saleOrder')->where('id', $saleOrder->shipment_id)->first();
        $orderHasInvoice = InvoiceSaleOrder::where('sale_order_id', $saleOrder->id)->exists();
        if ($shipment && $shipment->provider == Shipment::PROVIDER_MARKETPLACE && $shipment->saleOrder->order_status == SaleOrder::STATUS_SHIPPED && !$orderHasInvoice) {
            $shipment->created_at = now();
            $shipment->save();
        }

        return $this->successResponse('Update successfully.');
    }

    public function insertProductTiktok($input)
    {
        $productTiktokIds = ProductTiktok::pluck('product_id');
        $products = Product::whereIn('sku', $input['skus'])->whereNotIn('id', $productTiktokIds)->select('id', 'sku')->get();
        $data = [];
        foreach ($products as $product) {
            $data[] = [
                'product_id' => $product->id,
                'is_active' => true,
            ];
        }
        ProductTiktok::insert($data);

        return $this->successResponse('Insert successfully.');
    }

    public function reGenerateInvoice($input)
    {
        setTimezone();
        /** @var InvoiceRepository $invoiceRepository */
        $invoiceRepository = resolve(InvoiceRepository::class);
        $invoiceIds = $input['invoice_ids'];
        $type = $input['type'];
        $invoices = Invoice::whereIn('id', $invoiceIds)->get();
        foreach ($invoices as $invoice) {
            $saleOrders = SaleOrder::query()
                ->leftJoin('shipment', 'sale_order.shipment_id', '=', 'shipment.id')
                ->leftJoin('invoice_sale_order', function ($join) use ($invoice) {
                    $join->on('sale_order.id', '=', 'invoice_sale_order.sale_order_id')
                        ->where('invoice_sale_order.invoice_id', $invoice->id);
                })
                ->where('sale_order.is_test', SaleOrder::NOT_TEST)
                ->where('sale_order.store_id', $invoice->store_id)
                ->where(function ($query) use ($invoice) {
                    $query->where(function ($queryShipped) use ($invoice) {
                        $queryShipped->where('sale_order.order_status', SaleOrder::SHIPPED)
                            ->where('shipment.created_at', '>=', $invoice->start_at)
                            ->where('shipment.created_at', '<=', $invoice->end_at);
                    })
                        ->orWhere(function ($queryLateCancelled) use ($invoice) {
                            $queryLateCancelled->where('sale_order.order_status', SaleOrder::STATUS_LATE_CANCELLED)
                                ->where('sale_order.cancelled_at', '>=', $invoice->start_at)
                                ->where('sale_order.cancelled_at', '<=', $invoice->end_at);
                        });
                })
                ->whereNotNull('sale_order.calculated_at')
                ->whereNull('invoice_sale_order.invoice_id')
                ->select('sale_order.id')
                ->get();

            $invoice->saleOrders()->attach($saleOrders->pluck('id'));

            $invoiceId = $invoice->id;
            $fileName = "invoices/invoice_$invoiceId.pdf";
            switch ($type) {
                case 'production':
                    $fileName = "invoices/production_invoice_$invoiceId.xlsx";
                    break;

                case 'shipping':
                    $fileName = "invoices/shipping_invoice_$invoiceId.xlsx";
                    break;

                case 'insert':
                    $fileName = "invoices/insert_invoice_$invoiceId.xlsx";
                    break;

                default:
                    break;
            }

            if (Storage::disk('s3')->exists($fileName)) {
                Storage::disk('s3')->delete($fileName);
            }

            $type == 'general' && $invoiceRepository->dispatchJobGenerateInvoice($invoice);
            $type == 'production' && $invoiceRepository->dispatchJobGenerateProductionInvoice($invoice);
            $type == 'shipping' && $invoiceRepository->dispatchJobGenerateShippingInvoice($invoice);
            $type == 'insert' && $invoiceRepository->dispatchJobGenerateInsertInvoice($invoice);
        }

        return $this->successResponse('Re-generate invoice successfully.');
    }

    public function markIpByFileUpload($request)
    {
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,txt',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Invalid file upload',
                'errors' => $validator->errors(),
            ], 422);
        }

        $sheet = Excel::toArray([], $request->file('file'));
        if (empty($sheet) || empty($sheet[0])) {
            return response()->json(['message' => 'Error'], 400);
        }
        $rows = $sheet[0];

        // 3. Lấy header và tìm index 2 cột cần dùng
        $header = array_map('trim', $rows[0]);
        $idxExternal = array_search('external_number', $header);
        $idxImageKey = array_search('image_key', $header);

        if ($idxExternal === false || $idxImageKey === false) {
            return response()->json(['message' => 'external_number or image_key not found'], 400);
        }

        $results = [
            'updated' => [],
            'errors' => [],
        ];

        foreach ($rows as $index => $item) {
            if ($index < 1) {
                continue;
            }

            $external = trim($item[$idxExternal] ?? '');
            $imageKey = trim($item[$idxImageKey] ?? '');
            if ($external === '' || $imageKey === '') {
                $results['warnings'][] = "Line #{$index}: is invalid (external={$external}, image_key={$imageKey})";

                continue;
            }
            $image = SaleOrder::join('sale_order_item_image', 'sale_order.id', 'sale_order_item_image.order_id')
                ->where('sale_order.external_number', $external)
                ->where('sale_order_item_image.image_url', 'like', "%$imageKey%")
                ->select('image_hash_id')
                ->first();
            if (!empty($image->image_hash_id)) {
                $imageHash = ImageHash::where('id', $image->image_hash_id)->update([
                    'is_ip_violation' => 1,
                    'user_review_id' => 174,
                    'user_review_time' => now()->toDateTimeString(),
                    'user_check_time' => now()->toDateTimeString(),
                    'user_check_id' => 174,
                    'is_user_check_ip_violation' => 1,

                ]);
                if (!$imageHash) {
                    $results['errors'][] = "$external-$imageKey";
                } else {
                    $results['updated'][] = "$external-$imageKey";
                }
            } else {
                $results['errors'][] = "$external-$imageKey";
            }
            $imageHashIds = SaleOrder::join('sale_order_item_image', 'sale_order.id', 'sale_order_item_image.order_id')
                ->join('image_hash', 'image_hash.id', 'sale_order_item_image.image_hash_id')
                ->where('sale_order.external_number', $external)
                ->where('image_hash.is_ip_violation', 0)
                ->update([
                    'is_ip_violation' => null,
                    'user_review_id' => null,
                    'user_review_time' => null,
                    'user_check_time' => null,
                    'user_check_id' => null,
                    'is_user_check_ip_violation' => null,

                ]);
        }

        return response()->json($results);
    }

    public function voidShipment($input)
    {
        $dataSaleOrder = SaleOrder::with('store', 'warehouse')->find($input['orderId']);

        if (!$dataSaleOrder) {
            return null;
        }

        $s = Shipment::where('id', $input['shipmentId'])->where('order_id', $input['orderId'])->first();

        $storeAccount = $s->shipment_account == 'swiftpod' ? LabelRepository::DEFAULT_STORE : $dataSaleOrder->store_id;
        $shippingCarrierEasypost = ShippingCarrierEasypost::where('carrier_account', $s->account_shipping_easypost)
            ->where('warehouse_id', $dataSaleOrder->warehouse_id)
            ->where('store_id', $storeAccount)
            ->first();

        if (!$shippingCarrierEasypost) {
            throw new Exception('Error: Shipment not has carrier account', Response::HTTP_BAD_REQUEST);
        }

        EasyPost::setApiKey($shippingCarrierEasypost->api_key_easypost);

        $easyPostLog = ShipmentEasypostModel::where('order_id', $input['orderId'])
            ->where('shipment_id', $input['shipmentId'])
            ->first();
        $shipment_id = str_replace('"', '', $easyPostLog?->easypost_id);

        if (!$easyPostLog) {
            $easyPostLog = EasyPostLog::where('order_id', $input['orderId'])
                ->where('shipment_id', $input['shipmentId'])
                ->first();
            $shipment_id = json_decode($easyPostLog->data)->id;
        }

        if (!empty($shipment_id)) {
            $shipment = ShipmentEasyPost::retrieve($shipment_id);
            if ($shipment && is_null($shipment->refund_status)) {
                try {
                    $refund = $shipment->refund();
                    $result = Shipment::where('id', $input['shipmentId'])->where('order_id', $input['orderId'])->first();
                    $result->refund_status = $refund->refund_status;
                    $result->employee_refund_id = $input['employeeId'];
                    $result->save();

                    if ($input['shipmentId'] === $dataSaleOrder->shipment_id) {
                        $shipmentNotRefund = Shipment::where('order_id', $input['orderId'])->whereNull('refund_status')->first();
                        if ($shipmentNotRefund) {
                            $dataSaleOrder->shipment_id = $shipmentNotRefund->id;
                            $dataSaleOrder->save();
                        }
                    }

                    ShipmentItem::where('shipment_id', $input['shipmentId'])->delete();
                    ShipmentItemLabel::where('shipment_id', $input['shipmentId'])->delete();
                    $labelRepository = new LabelRepository();
                    $labelRepository->createSaleOrderLog($input['orderId'], $s->id, $input['employeeId']);

                    return [
                        'data' => $result->load('employeeRefund:id,code,name'),
                        'success' => true,
                        'message' => 'Refund Successfully'
                    ];
                } catch (EasyPostError $e) {
                    throw new Exception($e->getMessage(), Response::HTTP_BAD_REQUEST);
                }
            }

            throw new Exception('Refund Error', Response::HTTP_BAD_REQUEST);
        }

        throw new Exception('Not Found Shipment', Response::HTTP_NOT_FOUND);
    }
}
