<?php

namespace App\Repositories;

use App\Models\ProductTiktok;
use App\Models\ProductTiktokHistory;
use Illuminate\Support\Facades\DB;

class ProductTiktokRepository extends CommonRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getList($input)
    {
        $limit = !empty($request['limit']) ? $request['limit'] : 25;

        $query = ProductTiktok::with(['product.productStyle', 'product.productColor', 'product.productSize', 'product.brand', 'user']);
        if (!empty($input['product_id'])) {
            $query->where('product_id', $input['product_id']);
        }
        if (!empty($input['style'])) {
            $query->whereHas('product', function ($q) use ($input) {
                $q->where('style', $input['style']);
            });
        }
        if (!empty($input['color'])) {
            $query->whereHas('product', function ($q) use ($input) {
                $q->where('color', $input['color']);
            });
        }
        if (!empty($input['size'])) {
            $query->whereHas('product', function ($q) use ($input) {
                $q->where('size', $input['size']);
            });
        }
        if (!empty($input['sku'])) {
            $query->whereHas('product', function ($q) use ($input) {
                $q->where('sku', 'like', '%' . $input['sku'] . '%');
            });
        }
        $query->orderBy('created_at', 'desc');
        if (!empty($input['is_get_all'])) {
            return $query->get();
        }

        return $query->paginate($limit);
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            $dataInsert = [];
            $data = [];
            foreach ($input['product_ids'] as $productId) {
                $dataInsert[] = [
                    'product_id' => $productId,
                    'is_active' => true,
                    'created_by' => auth()->user()->id
                ];
                $data[] = [
                    'product_id' => $productId,
                    'user_id' => auth()->user()->id,
                    'is_active' => true
                ];
            }
            ProductTiktok::insert($dataInsert);
            ProductTiktokHistory::insert($data);
            DB::commit();

            return [
                'status' => true,
                'message' => 'Created successfully'
            ];
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'status' => false,
                'message' => $exception->getMessage()
            ];
        }
    }

    public function update($id, $input)
    {
        try {
            DB::beginTransaction();
            $productTiktok = ProductTiktok::find($id);
            $productTiktok->is_active = $input['is_active'];
            $productTiktok->save();

            ProductTiktokHistory::create([
                'product_id' => $input['product_id'],
                'is_active' => $input['is_active'] ? 1 : 0,
                'user_id' => auth()->user()->id
            ]);
            DB::commit();

            return [
                'status' => true,
                'message' => 'Updated successfully'
            ];
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'status' => false,
                'message' => $exception->getMessage()
            ];
        }
    }
}
