<?php


namespace App\Repositories;


use App\Models\Box;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\SaleOrder;
use App\Models\WorkOrder;
use App\Models\WorkOrderItem;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class WorkOrderRepository extends CommonRepository
{

    public function fetchSkuOutStock($input)
    {
        //select product.sku, location_product.quantity FROM product JOIN location_product ON location_product.product_id = product.id WHERE location_product.location_id = 1 AND location_product.quantity < product.gtin_case ORDER by quantity ASC;

        $locationRepo = new LocationRepository();
        $pullingShelves = $locationRepo->getPullingShelves($input['warehouse_id']);

        $results = DB::table('product')->join('location_product', 'product.id', '=', 'location_product.id')
            ->where('location_product.warehouse_id', $input['warehouse_id'] ?? 0)
            ->where('location_product.location_id', $pullingShelves->id)
            ->where('location_product.quantity', '<', 'product.gtin_case')
            ->orderBy('location_product.quantity', 'ASC')
            ->paginate($input['limit'] ?? 10);

        return $results;
    }

    public function countPendingWorkOrder($warehouseID, $employeeID = null)
    {
        $r = WorkOrder::query()->where('warehouse_id', $warehouseID);
        if ($employeeID != null) {
            $r->where(function ($query) use ($employeeID) {
                $query->whereNull('employee_id')->orWhere('employee_id', $employeeID);
            });
        }
        return $r->where('status', WorkOrder::STATUS_INCOMPLETED)->count();
    }

    public function createWorkOrder($input)
    {
        DB::beginTransaction();
        try {
            $skuOutStock = $this->getSkuOutStock($input['warehouse_id'], 3);

            if (empty($skuOutStock)) {
                return $this->errorResponse("Not found sku out stock", Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $workOrder = WorkOrder::create([
                'employee_id' => null,
                'warehouse_id' => $input['warehouse_id'],
                'status' => WorkOrder::STATUS_INCOMPLETED,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            foreach ($skuOutStock as $itemSku) {
                WorkOrderItem::create([
                    'work_order_id' => $workOrder->id,
                    'product_sku' => $itemSku->product_sku,
                    'box_number' => $itemSku->box_number,
                    'location_number' => $itemSku->location_number,
                    'created_at' => date('Y-m-d H:i:s'),
                    'status' => WorkOrderItem::STATUS_INCOMPLETED,
                ]);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse("Create a successful work order!", $workOrder);
    }

    public function assignWorkOrder($warehouseId, $employeeId)
    {
        $count = $this->countPendingWorkOrder($warehouseId, $employeeId);
        if ($count == 0) {
            $this->generateWorkOrder($warehouseId);
        }


        $current = WorkOrder::query()
            ->where('status', WorkOrder::STATUS_INCOMPLETED)
            ->with('items')
            ->where('employee_id', $employeeId)
            ->where('warehouse_id', $warehouseId)
            ->with('items.product')
            ->first();

        if (!$current) {
            $current = WorkOrder::query()
                ->where('status', WorkOrder::STATUS_INCOMPLETED)
                ->whereNull('employee_id')
                ->where('warehouse_id', $warehouseId)
                ->with('items.product')
                ->first();

            if ($current) {
                $current->update([
                    'employee_id' => $employeeId,
                    'located_at' => date('Y-m-d H:i:s')
                ]);
            }
        }



        return $current;
    }

    public function generateWorkOrder($warehouseId = 1)
    {
        DB::beginTransaction();
        try {
            // fix warehouse id = 1;
            $skuOutStock = $this->getSkuOutStock($warehouseId);
            if (count($skuOutStock) == 0) return false;

            $workOrder = WorkOrder::create([
                'employee_id' => null,
                'warehouse_id' => $warehouseId,
                'status' => WorkOrder::STATUS_INCOMPLETED,
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            foreach ($skuOutStock as $itemSku) {

                WorkOrderItem::create([
                    'work_order_id' => $workOrder->id,
                    'product_sku' => $itemSku->product_sku,
                    'box_number' => $itemSku->box_number,
                    'location_number' => $itemSku->location_number,
                    'status' => WorkOrderItem::STATUS_INCOMPLETED,
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            }
            DB::commit();
            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            echo $exception->getMessage();
            return false;
        }
    }

    public function fetchWorkOrder($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;

        return WorkOrder::where('warehouse_id', $input['warehouse_id'])->with('items')->paginate($limit);
    }

    private function getSkuOutStock($warehouseId, $limit = 3)
    {
        $locationRepo = new LocationRepository();
        $pullingShelves = $locationRepo->getPullingShelves($warehouseId);
        // tim sku nao outstock va dang ko ton tai sku nao pending

        //select location_product.location_id, location_product.product_id, SUM(location_product.quantity) stock
        //FROM location_product JOIN location ON location.id = location_product.location_id WHERE location.type = 0
        // AND warehouse_id = 1 GROUP BY location_product.product_id;

        // SELECT box.product_id, COUNT(*) FROM box JOIN location ON location.id = box.location_id WHERE box.is_deleted = 0
        // AND location.type = 1 AND box.warehouse_id = 1 GROUP BY box.product_id;

        $boxes = DB::table('box')
            ->select('box.product_id', DB::raw('COUNT(*) total_box'), 'box.barcode as box_number')
            ->join('location', 'location.id', 'box.location_id')
            ->where('box.is_deleted', 0)
            ->where('location.type', 0)
            ->where('location.warehouse_id', $warehouseId)
            ->whereNotIn('box.barcode', function ($q) {
                $q->select('box_number')->from('work_order_item');
            })
            ->groupBy('box.product_id')
            ->having(DB::raw('COUNT(*)'), '>', 0);

        $barcodes = DB::table('sale_order_item')
            ->select('sale_order_item.product_id', DB::raw('COUNT(*) total_wip'))
            ->join('sale_order_item_barcode', 'sale_order_item.id', 'sale_order_item_barcode.order_item_id')
            ->join('sale_order', 'sale_order_item.order_id', 'sale_order.id')
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order.order_status', SaleOrder::IN_PRODUCTION)
            ->where('sale_order.warehouse_id', $warehouseId)
            ->groupBy('sale_order_item.product_id')
            ->having(DB::raw('COUNT(*)'), '>', 0);


        $results = DB::table('product')
            ->select('product.id')
            ->join('location_product', 'product.id', '=', 'location_product.product_id')
            ->where('location_product.location_id', $pullingShelves->id)
            ->where(DB::raw('location_product.quantity - IFNULL(barcodes.total_wip, 0)'), '<',  DB::raw('product.gtin_case*0.17'))
            // ->where('location_product.quantity', '>=', DB::raw(0))
            ->JoinSub($boxes, 'boxes', 'boxes.product_id', 'product.id')
            ->leftJoinSub($barcodes, 'barcodes', 'barcodes.product_id', 'product.id')
            ->whereNotIn('product.sku', function ($q) {
                $q->select('product_sku')
                    ->from('work_order_item')
                    ->where('status', WorkOrderItem::STATUS_INCOMPLETED);
            })
            ->orderBy('location_product.quantity', 'ASC')
            ->limit($limit);
        $list = $results->pluck('id');



        $results = DB::table('product')
            ->select('box.id', 'box.product_id', 'box.barcode as box_number', 'location.barcode as location_number', 'product.sku as product_sku')
            ->join('box', 'product.id', 'box.product_id')
            ->join('location', 'location.id', 'box.location_id')
            ->where('box.is_deleted', 0)
            ->where('location.type', 0)
            ->where('location.warehouse_id', $warehouseId)
            ->whereNotIn('box.barcode', function ($q) {
                $q->select('box_number')->from('work_order_item');
            })->whereIn('box.product_id', $list)
            ->groupBy('product.sku')
            ->limit($limit);



        return $results->get()->toArray();
    }

    public function confirmItem($id)
    {
        return WorkOrderItem::query()->where('id', $id)
            ->update(['status' => WorkOrderItem::STATUS_COMPLETED]);
    }

    public function changeItem($workOrderItemId)
    {
        $item = WorkOrderItem::query()->where('id', $workOrderItemId)->first();
        if (!$item) return false;
        if ($item->is_replaced == 1) return false;
        $box = Box::query()->where('barcode', $item->box_number)->first();
        if (!$box) return false;

        $findNewBox = DB::table('box')
            ->select('box.id', 'box.product_id', 'box.barcode as box_number', 'location.barcode as location_number', 'product.sku as product_sku')
            ->join('product', 'product.id', 'box.product_id')
            ->join('location', 'location.id', 'box.location_id')
            ->where('box.is_deleted', 0)
            ->where('location.type', 0)
            ->where('location.warehouse_id', $box->warehouse_id)
            ->whereNotIn('box.barcode', function ($q) {
                $q->select('box_number')->from('work_order_item');
            })->where('product.sku', $item->product_sku);
        $sameLocation = clone $findNewBox;
        $sameLocation->where('box.location_id', $box->location_id);
        // tìm xem có box nào cùng location không
        $newBox = $sameLocation->first();


        if (!$newBox) {
            // nếu không thì tìm 1 box mới bất kỳ


            $newBox = $findNewBox->first();
        }
        if ($newBox) {
            $item->is_replaced = 1; // đánh dấu đã bị thay
            $item->save();
            WorkOrderItem::create([
                'work_order_id' => $item->work_order_id,
                'product_sku' => $newBox->product_sku,
                'box_number' => $newBox->box_number,
                'location_number' => $newBox->location_number,
                'is_alternative' => 1,
                'status' => WorkOrderItem::STATUS_INCOMPLETED,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            return true;
        }
        return false;
    }

    public function confirmWorkOrder($workOrderId, $timeTrackingId)
    {
        $items = WorkOrderItem::query()->where('work_order_id', $workOrderId)->get();

        $workOder = WorkOrder::query()
            ->where('id', $workOrderId)->first();

        if (!$workOder) {
            throw new \Exception('Work order not found');
        }


        $locationRepo = new LocationRepository();
        $pullingShelves = $locationRepo->getPullingShelves($workOder->warehouse_id);

        $totalItem = count($items);

        $moveCount = 0;

        foreach ($items as $item) {

            $box = Box::query()->where('barcode', $item->box_number)->first();
            // neu khong ton tai box hoac box bi xoa thi bo qua
            if (!$box || $box->is_deleted == 1) {
                $item->status = WorkOrderItem::STATUS_CANCELLED;
                $item->save();
                continue;
            }
            // neu khong tim thay box thi huy box do di va tru trong inventory
            if ($item->status == WorkOrderItem::STATUS_INCOMPLETED) {
                // remove box

                $box->is_deleted = 1;
                $box->save();

                // deduction inventory
                ProductQuantityRepository::updateQuantity($box->warehouse_id, $box->product_id, -1 * $box->quantity);
                LocationProductRepository::updateQuantity($box->location_id, $box->product_id, -1 * $box->quantity);
                $inputInventory = [];
                $inputInventory['direction'] = Inventory::DIRECTION_OUTPUT;
                $inputInventory['type'] = Inventory::TYPE_OUTPUT;
                $inputInventory['product_id'] = $box->product_id;
                $inputInventory['warehouse_id'] = $box->warehouse_id;
                $inputInventory['location_id'] = $box->location_id;
                $inputInventory['user_id'] = auth()->user()->id;
                $inputInventory['object_id'] = $workOrderId;
                $inputInventory['object_name'] = 'work order';
                $inputInventory['quantity'] = $box->quantity;
                Inventory::create($inputInventory);

                continue;
            }
            $moveCount++;
            // neu tim thay box thi thuc hien box moving sang pulling shelves
            $inputMoving = [];
            $inputMoving['warehouse_id'] = $box->warehouse_id;
            $inputMoving['barcode'] = $box->barcode;
            $inputMoving['location_id'] = $pullingShelves->id;
            BoxMovingRepository::manualMoving($inputMoving);
        }

        if ($moveCount == $totalItem) {
            $workOder->status = WorkOrder::STATUS_COMPLETED;
        } else {
            $workOder->status = WorkOrder::STATUS_PARTIAL_COMPLETED;
        }

        $workOder->completed_at = date('Y-m-d H:i:s');
        $workOder->save();

        //todo:  update time end for time checking
        $timeCheckingRepository = new TimeCheckingRepository();
        $timeCheckingRepository->updateTimeChecking(['end_time' =>   date("Y-m-d H:i:s")], $timeTrackingId);

        return true;
    }

    public function countSku($warehouseId)
    {
        $count = 0;
        $locationRepo = new LocationRepository();
        $pullingShelves = $locationRepo->getPullingShelves($warehouseId);

        if (isset($pullingShelves)) {
            $barcodes = DB::table('sale_order_item')
                ->select('sale_order_item.product_id', DB::raw('COUNT(*) total_wip'))
                ->join('sale_order_item_barcode', 'sale_order_item.id', 'sale_order_item_barcode.order_item_id')
                ->join('sale_order', 'sale_order_item.order_id', 'sale_order.id')
                ->where('sale_order_item_barcode.is_deleted', 0)
                ->where('sale_order.order_status', SaleOrder::IN_PRODUCTION)
                ->where('sale_order.warehouse_id', $warehouseId)
                ->groupBy('sale_order_item.product_id')
                ->having(DB::raw('COUNT(*)'), '>', 0);

            $results = DB::table('product')
                ->join('location_product', 'product.id', '=', 'location_product.product_id')
                ->JoinSub($barcodes, 'barcodes', 'barcodes.product_id', 'product.id')
                ->where(DB::raw('location_product.quantity - barcodes.total_wip'), '<',  DB::raw('product.gtin_case*0.17'))
                ->where('location_product.location_id', $pullingShelves->id)
                ->count();

            $count = $results;
        }

        return $count;
    }

    public function getHistory($request)
    {
        $query = WorkOrder::select('work_order.*', 'employee.name')
            ->where('work_order.warehouse_id', $request['warehouse_id'])
            ->withCount(['fillingShelves as fillingShelvesCount', 'items as itemsCount', 'items as itemCompletedCount' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->with('items')
            ->with('items.product')
            ->join('employee', 'work_order.employee_id', '=', 'employee.id');

        if (!empty($request['date'])) {
            $startDate = date("Y-m-d", strtotime($request['date'][0]));
            $endDate = date("Y-m-d", strtotime($request['date'][1]));

            $query = $query->whereDate('work_order.created_at', '>=', $startDate)
                ->whereDate('work_order.created_at', '<=', $endDate);
        }

        if (!empty($request['woi'])) {
            $query = $query->where('employee.name', 'LIKE', '%' . $request['woi'] . '%')
                ->orWhere('work_order.id', $request['woi']);
        }

        if (!empty($request['fs'])) {
            if ($request['fs'] == 1) {
                $query = $query->having('fillingShelvesCount', '>=', DB::raw('itemCompletedCount'));
            } else {
                $query = $query->having('fillingShelvesCount', '<', DB::raw('itemCompletedCount'));
            }
        }

        if (!empty($request['status'])) {
            $query = $query->where('work_order.status', $request['status']);
        }

        return $query->latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }
}
