<?php

namespace App\Repositories;

use App\Models\WalletTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TransactionRepository extends CommonRepository
{
    public function buildQuery($input)
    {
        $query = WalletTransaction::with('receipts');
        if (!empty($input['start_date']) && !empty($input['end_date'])) {
            $startDate = Carbon::parse($input['start_date'])->startOfDay()->toDateTimeString();
            $endDate = Carbon::parse($input['end_date'])->endOfDay()->toDateTimeString();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        $store = Auth::user();
        $query->where('store_id', $store->id);

        if (!empty($input['type'])) {
            $query->where('type', $input['type']);
        }

        if (!empty($input['keyword'])) {
            $query->where('object_number', 'LIKE', '%' . $input['keyword'] . '%');
        }

        return $query;
    }

    public function getList($input)
    {
        setTimezone();
        $query = $this->buildQuery($input);

        if (isset($input['action']) && $input['action'] == 'export') {
            return $query->get();
        }
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;

        return $query->orderByDesc('id')
            ->paginate($limit);
    }

    public function createTransaction($data)
    {
        return WalletTransaction::create($data);
    }
}
