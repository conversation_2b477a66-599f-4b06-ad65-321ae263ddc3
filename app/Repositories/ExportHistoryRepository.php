<?php

namespace App\Repositories;

use App\Models\ExportHistory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ExportHistoryRepository
{
    public function create(array $data)
    {
        return ExportHistory::query()
            ->create($data);
    }

    public function update($id, array $data)
    {
        $downloadHistory = ExportHistory::find($id);

        if (!$downloadHistory) {
            return false;
        }

        $downloadHistory->update($data);

        return $downloadHistory;
    }

    public function getLatest($params = [], int $limit = 10)
    {
        $query = ExportHistory::query()
            ->with(['user:id,username,is_admin'])
            ->orderByDesc('id');

        if (!empty($params['module'])) {
            $query->where('module', $params['module']);
        }

        if (!empty($params['warehouse_id'])) {
            $query->where('warehouse_id', $params['warehouse_id']);
        }

        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        $data = $query->limit($limit)
            ->get();

        foreach ($data as $item) {
            $startFormatted = Carbon::parse($item->start_date)->format('M j, Y');
            $endFormatted = Carbon::parse($item->end_date)->format('M j, Y');

            $item->url = Storage::disk('s3')->url($item->file_path);
            $item->date_range_filter = "{$startFormatted} to {$endFormatted}";
            $item->date = Carbon::parse($item->created_at)->timezone('America/Los_Angeles')->format('M d, Y g:i A');
        }

        return $data;
    }

    public function cleanOldHistory(): void
    {
        $thresholdDate = Carbon::now()->subDays(30);
        $rawTop10Sub = DB::table('export_histories')
            ->selectRaw('
                id,
                module,
                ROW_NUMBER() OVER (PARTITION BY module ORDER BY id DESC) AS row_num
            ');
        $top10Ids = DB::table(DB::raw("({$rawTop10Sub->toSql()}) as ranked"))
            ->mergeBindings($rawTop10Sub)
            ->where('row_num', '<=', 10)
            ->pluck('id');
        $data = ExportHistory::query()
            ->where('created_at', '<', $thresholdDate)
            ->whereNotIn('id', $top10Ids)
            ->get();
        $idDelete = [];

        foreach ($data as $item) {
            if (Storage::disk('s3')->exists($item->file_path)) {
                Storage::disk('s3')->delete($item->file_path);
            }

            $idDelete[] = $item->id;
        }

        if (!empty($idDelete)) {
            ExportHistory::query()->whereIn('id', $idDelete)->delete();
        }
    }
}
