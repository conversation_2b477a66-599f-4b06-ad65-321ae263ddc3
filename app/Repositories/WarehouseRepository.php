<?php

namespace App\Repositories;

use App\Models\Location;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrderAutomationRule;
use App\Models\ShipmentTransit;
use App\Models\ShippingCarrierEasypost;
use App\Models\Warehouse;
use App\Repositories\Contracts\WarehouseRepositoryInterface;
use DateTimeZone;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class WarehouseRepository extends CommonRepository implements WarehouseRepositoryInterface
{
    public function fetchAll($input = null)
    {
        $warehouses = Warehouse::query()->get();
        $saleOrderAutomationRule = SaleOrderAutomationRule::query()
            ->select([
                'warehouse_id',
                'priority',
                'type',
                'limit_daily',
                'is_active',
                'name',
                'code'])
            ->get();

        foreach ($saleOrderAutomationRule as $rule) {
            foreach ($warehouses as $warehouse) {
                if ($warehouse->id == $rule->warehouse_id) {
                    $warehouse->sale_order_automation_rule = $rule;
                }
            }
        }

        return $warehouses;
    }

    public function getRule($warehouseId = null)
    {
        $rule = SaleOrderAutomationRule::where('warehouse_id', $warehouseId)->first();

        if (!$rule) {
            return null;
        }

        $rule->rule = !empty($rule->rule) ? json_decode($rule->rule) : null;
        $shippingCarrierEasyPostDHL = ShippingCarrierEasypost::where('warehouse_id', $warehouseId)->where('name', ShipmentTransit::DHL)->first();

        if ($shippingCarrierEasyPostDHL) {
            $rule->rule->economy = $rule->rule->economy ?? false;
        } else {
            unset($rule->rule->economy);
        }

        $listSku = $rule->rule?->listSku ?? [];

        if (count($listSku) > 0) {
            $skus = array_column($listSku, 'value');
            $product = Product::join('product_quantity', 'product_quantity.product_id', '=', 'product.id')->whereIn('product.sku', $skus)
                ->select(DB::raw('product.id, product.sku, product_quantity.warehouse_id, product_quantity.quantity, product_quantity.incoming_stock'))
                ->get()
                ->keyBy(function ($item) {
                    return $item->sku . '|' . $item->warehouse_id;
                });

            foreach ($listSku as $item) {
                $stock = $this->getStockProduct($item->value, $rule->warehouse_id, $product);
                $item->stock_status = $stock['stock_status'];
                $item->stock = $stock['stock'];
                $item->incoming_stock = $stock['incoming_stock'];
            }

            $rule->rule->listSku = $listSku;
        }

        $rule->rule = json_encode($rule->rule);

        return $rule;
    }

    public function getStockProduct($sku, $warehouseId, $product)
    {
        $productKey = $sku . '|' . $warehouseId;
        $status = [
            'stock' => 0,
            'incoming_stock' => 0,
            'stock_status' => ProductQuantity::OUT_OF_STOCK,
        ];

        if ($product->has($productKey)) {
            $item = $product->get($productKey);

            if ($item->quantity > 0) {
                return [
                    'stock' => $item->quantity,
                    'incoming_stock' => $item->incoming_stock,
                    'stock_status' => $item->quantity,
                    'size' => $item->size
                ];
            } elseif ($item->incoming_stock > 0) {
                return [
                    'stock' => 0,
                    'incoming_stock' => $item->incoming_stock,
                    'stock_status' => ProductQuantity::INCOMING_STOCK,
                    'size' => $item->size

                ];
            } else {
                $status['size'] = $item->size;
            }

            return $status;
        }

        return $status;
    }

    public function create($request)
    {
        try {
            $input = $request->except('image');

            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $filename = $input['code'] . '-' . uniqid() . '.' . $file->getClientOriginalExtension();
                $path = Storage::disk('s3')->putFileAs(
                    Warehouse::IMAGE_PATH,
                    $file,
                    $filename,
                );
                $input['image'] = $path;
            }

            $warehouse = Warehouse::create($input);
            $warehouse = $warehouse->fresh();
            $barcode = DB::select('CALL create_serial("LO")');

            if (!empty($barcode)) {
                Location::insert([
                    [
                        'barcode' => $barcode[0]->number,
                        'warehouse_id' => $warehouse->id,
                        'type' => Location::PULLING_SHELVES,
                    ],
                    [
                        'barcode' => ($input['code'] ?? '') . '-pending',
                        'warehouse_id' => $warehouse->id,
                        'type' => Location::VIRTUAL_LOCATION,
                    ]
                ]);
            }

            return [
                'status' => true,
                'data' => $warehouse,
                'code' => Response::HTTP_CREATED,
            ];
        } catch (\Exception $exception) {
            return [
                'status' => false,
                'message' => $exception->getMessage(),
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
            ];
        }
    }

    public function fetch($id)
    {
        return Warehouse::with('saleOrderAutomationRule')->find($id);
    }

    public function findByCode($code)
    {
        return Warehouse::where('code', $code)->first();
    }

    public function getByCode($code)
    {
        return Warehouse::where('code', $code)->first();
    }

    public function update($id, $dataUpdate)
    {
        try {
            $warehouse = Warehouse::find($id);

            if (!$warehouse) {
                return [
                    'status' => false,
                    'message' => 'Warehouse not found.',
                    'code' => Response::HTTP_NOT_FOUND
                ];
            }

            $input = $dataUpdate->except('image');

            if ($dataUpdate->hasFile('image')) {
                $file = $dataUpdate->file('image');
                $filename = $input['code'] . '-' . uniqid() . '.' . $file->getClientOriginalExtension();
                $path = Storage::disk('s3')->putFileAs(
                    Warehouse::IMAGE_PATH,
                    $file,
                    $filename,
                );
                Storage::disk('s3')->delete($warehouse->image);
                $input['image'] = $path;
            } elseif (empty($dataUpdate->input('image')) && !empty($warehouse->image)) {
                Storage::disk('s3')->delete($warehouse->image);
                $input['image'] = null;
            } else {
                $input['image'] = $dataUpdate->input('image');
            }
            $virtualLocation = Location::where('warehouse_id', $id)
                ->where('type', Location::VIRTUAL_LOCATION)->first();

            if ($virtualLocation) {
                if (!empty($input['code'])) {
                    $virtualLocation->barcode = $input['code'] . '-pending';
                    $virtualLocation->save();
                }
            }

            $warehouse->update($input);

            return [
                'status' => true,
                'message' => 'Update successfully.',
                'code' => Response::HTTP_OK
            ];
        } catch (\Exception $exception) {
            return [
                'status' => false,
                'message' => $exception->getMessage(),
                'code' => 500
            ];
        }
    }

    public function delete($id)
    {
        // return Warehouse::where('id', $id)->delete();
    }

    public function getTimeZone()
    {
        $timeZones = DateTimeZone::listIdentifiers(DateTimeZone::AMERICA);
        $result = [];

        foreach ($timeZones as $timeZone) {
            array_push($result, $timeZone);
        }

        return $result;
    }

    public static function getListWarehouseWithField($field = 'code'): array
    {
        return Warehouse::query()->pluck($field, 'id')->toArray();
    }

    public function listAll()
    {
        return Warehouse::select('name', 'id')->get();
    }
}
