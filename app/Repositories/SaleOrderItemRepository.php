<?php

namespace App\Repositories;

use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrderItem;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SaleOrderItemRepository extends CommonRepository
{
    public function getList(Request $request): LengthAwarePaginator
    {
        return SaleOrderItem::with('order')->search($request)
            ->orderBy($this->sortColumn, $this->sortBy)
            ->with('order')
            ->paginate($this->limit);
    }

    public function exportExcel(Request $request)
    {
        $condition = [];
        $order_date_start = $request['order_date_start'];
        $order_date_end = $request['order_date_end'];
        $store_id = $request['store_id'];

        if (!empty($order_date_start) && !empty($order_date_end)) {
            array_push($condition, "sale_order.order_date >= '$order_date_start' AND sale_order.order_date<= '$order_date_end'");
        }
        if (!empty($store_id)) {
            array_push($condition, "sale_order.store_id = '$store_id'");
        }

        $where = '';

        if (count($condition) > 0) {
            $where = ' WHERE ' . implode(' AND ', $condition);
        }

        return "SELECT sale_order.order_date as order_date,
            CONCAT (sku, '-', CASE WHEN COUNT(sale_order_item.sku) = 2 THEN 'FB' WHEN COUNT(sale_order_item.sku) = 1 AND sale_order_item.print_side = '0' THEN 'F'
            ELSE 'B' END) AS sku_formatted,
            sale_order_item.quantity,
            store.name AS store_name
            from `sale_order_item`
            LEFT JOIN sale_order ON sale_order_item.order_id = sale_order.id
            LEFT JOIN store ON sale_order.store_id = store.id
            $where
            GROUP BY sale_order_item.sku";
    }

    public function getAllIconFromProductItem($saleOrderItem)
    {
        $dataProductType = ProductType::all();
        $productTypeRepository = new ProductTypeRepository();
        $dataIcon = [];
        $saleOrderItem->map(function ($item) use (&$dataIcon, $productTypeRepository, $dataProductType) {
            if (!isset($dataIcon[ucfirst($item->productStyle?->type)])) {
                if ($productTypeRepository->getIconProductType($item->productStyle?->type, $dataProductType)) {
                    $dataIcon[ucfirst($item->productStyle?->type)] = $productTypeRepository->getIconProductType($item->productStyle?->type, $dataProductType);
                }
            }
        });

        return $dataIcon;
    }

    public function checkExistExternalId($externalId)
    {
        return SaleOrderItem::where('external_id', $externalId)->first();
    }

    public function updateWithNewPrintFile($input)
    {
        $orderItem = SaleOrderItem::find($input['order_item_id']);
        if ($orderItem) {
            $options = json_decode($orderItem->options);
            $isUpdatePrintFile = false;
            $isUpdatePreviewFile = false;
            $dataUpdatePrintFile = new \stdClass();
            $dataUpdatePrintFile->name = SaleOrderItem::PRINT_FILES . '.' . $input['print_side'];
            $dataUpdatePrintFile->value = $input['print_file'] ?? '';
            $dataUpdatePreviewFile = new \stdClass();
            $dataUpdatePreviewFile->name = SaleOrderItem::PREVIEW_FILES . '.' . $input['print_side'];
            $dataUpdatePreviewFile->value = $input['preview_file'] ?? '';
            if (!empty($options)) {
                foreach ($options as $key => $option) {
                    $optionName = explode('.', $option->name);
                    if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PRINT_FILES && !empty($optionName[1]) && !empty($input['print_file']) && $optionName[1] == $input['print_side']) {
                        $options[$key] = $dataUpdatePrintFile;
                        $isUpdatePrintFile = true;
                    }
                    if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PREVIEW_FILES && !empty($optionName[1]) && $optionName[1] == $input['print_side']) {
                        if (!empty($input['preview_file'])) {
                            $options[$key] = $dataUpdatePreviewFile;
                        }
                        $isUpdatePreviewFile = true;
                    }
                }
            }
            if (!$isUpdatePrintFile && !empty($input['print_file'])) {
                $options[] = $dataUpdatePrintFile;
            }
            if (!$isUpdatePreviewFile && !empty($input['preview_file'])) {
                $options[] = $dataUpdatePreviewFile;
            }
            Log::debug('options', [$options]);
            $orderItem->options = json_encode($options);
            if (!empty($input['print_file'])) {
                $orderItem->ink_color_status = SaleOrderItem::INACTIVE;
            }
            $orderItem->save();
        }
    }

    public function orderItemHasProductInsert($orderId)
    {
        $saleOrderItemId = SaleOrderItem::with('productStyle')
             ->where('order_id', $orderId)
             ->whereHas('productStyle', function ($query) {
                 $query->where('type', ProductStyle::TYPE_INSERT);
             })
             ->pluck('id');

        return $saleOrderItemId;
    }
}
