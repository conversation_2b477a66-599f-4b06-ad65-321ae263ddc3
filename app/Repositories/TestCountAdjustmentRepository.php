<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\TestCount;
use App\Models\TestCountItem;
use App\Models\TestCountPause;
use App\Models\Warehouse;
use App\Repositories\Contracts\TestCountAdjustmentRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestCountAdjustmentRepository implements TestCountAdjustmentRepositoryInterface
{
    protected BoxRepository $boxRepository;

    public function __construct(BoxRepository $boxRepository)
    {
        $this->boxRepository = $boxRepository;
    }

    const LIMIT = 10;

    public function fetchTestCount($input, $unlimited = false)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = DB::table('test_count')
            ->select(
                'test_count.*',
                'user.username',
                'location.barcode as location',
                'employee.name as employee_name',
            )
            ->join('test_count_item', 'test_count_item.test_count_id', '=', 'test_count.id')
            ->join('user', 'user.id', '=', 'test_count.user_id')
            ->join('location', 'location.id', '=', 'test_count.location_id')
            ->leftJoin('employee', 'employee.code', '=', 'test_count.employee_id')
            ->where('test_count.warehouse_id', $input['warehouse_id']);

        if (!empty($input['location'])) {
            $query->where('location.barcode', 'LIKE', '%' . $input['location'] . '%');
        }

        if (!empty($input['start_date'])) {
            $startDate = Carbon::parse($input['start_date'])->startOfDay()->format('Y-m-d H:i:s');
            $query->where('test_count.created_at', '>=', $startDate);
        }

        if (!empty($input['end_date'])) {
            $endDate = Carbon::parse($input['end_date'])->endOfDay()->format('Y-m-d H:i:s');
            $query->where('test_count.created_at', '<=', $endDate);
        }

        $data = $query->groupBy('test_count.id')
            ->orderBy('test_count.id', 'desc');

        if ($unlimited) {
            $data = $data->get($limit);
            $records = $data->toArray();
        } else {
            $data = $data->paginate($limit);
            $records = $data->items();
        }

        $testCountIds = array_column($records, 'id');
        $testCountItems = TestCountItem::where('type', '!=', TestCountItem::TYPE_NOT_FOUND)
            ->whereIn('test_count_id', $testCountIds)
            ->with('box')
            ->get();

        foreach ($records as &$record) {
            $onHandQuantity = 0;
            $totalValue = 0;

            $items = $testCountItems->filter(function ($item) use ($record) {
                return $item->test_count_id == $record->id;
            });

            foreach ($items as $item) {
                $onHandQuantity += $item->box->quantity;
                $totalValue += $item->box->cost_value;
            }

            $record->quantity_on_hand = $onHandQuantity;
            $record->total_value = $totalValue;
        }

        return $data;
    }

    public function complete($input)
    {
        $barcodeNotFound = !empty($input['barcode_not_found']) ? $input['barcode_not_found'] : [];
        $barcodeExisted = !empty($input['barcode_found']) ? $input['barcode_found'] : [];
        $boxInLocation = array_merge($barcodeNotFound, $barcodeExisted);
        $boxCodeNew = !empty($input['barcode_new']) ? $input['barcode_new'] : [];

        if ($input['warehouse_id'] == Warehouse::WAREHOUSE_MEXICO_ID && !empty($boxCodeNew)) {
            $missingDataBoxHasCountry = '';

            foreach ($boxCodeNew as $item) {
                if (empty($item['country_save'])) {
                    $missingDataBoxHasCountry .= $item['barcode'] . ', ';
                }
            }

            if (!empty($missingDataBoxHasCountry)) {
                return response()->json([
                    'message' => ['Missing country for box: ' . rtrim($missingDataBoxHasCountry, ', ')]
                ], 422);
            }
        }

        $totalBox = Box::where('location_id', $input['location_id'])
            ->whereIn('barcode', $boxInLocation)
            ->get();

        $testCount = TestCount::create([
            'warehouse_id' => $input['warehouse_id'],
            'location_id' => $input['location_id'],
            'user_id' => $input['user_id'],
            'note' => !empty($input['note']) ? $input['note'] : null,
            'box_available' => count($totalBox),
            'box_on_hand' => 0,
            'employee_id' => $input['employee_id']
        ]);

        if (!empty($totalBox)) {
            foreach ($totalBox as $itemBox) {
                $inputTestCountItem['test_count_id'] = $testCount->id;
                $inputTestCountItem['location_id'] = $input['location_id'];
                $inputTestCountItem['box_id'] = $itemBox->id;
                $inputTestCountItem['type'] = TestCountItem::TYPE_FOUND;
                if (in_array($itemBox->barcode, $barcodeNotFound)) {
                    ///Todo : neu scan ko tim thay box trong location
                    Box::where('id', $itemBox->id)->update(['is_deleted' => 1]);
                    $inputInventory['direction'] = Inventory::DIRECTION_OUTPUT;
                    $inputInventory['type'] = Inventory::TYPE_ADJUST;
                    $inputInventory['product_id'] = $itemBox->product_id;
                    $inputInventory['warehouse_id'] = $input['warehouse_id'];
                    $inputInventory['location_id'] = $input['location_id'];
                    $inputInventory['user_id'] = $input['user_id'];
                    $inputInventory['box_id'] = $itemBox->id;
                    $inputInventory['object_id'] = $testCount->id;
                    $inputInventory['object_name'] = Inventory::OBJECT_TEST_COUNT;
                    $inputInventory['quantity'] = $itemBox->quantity;
                    Inventory::create($inputInventory);
                    ///Todo : update quantity location_product và product_quantity
                    if (!empty($itemBox->product_id)) {
                        LocationProductRepository::updateQuantity($itemBox->location_id, $inputInventory['product_id'], -1 * $inputInventory['quantity']);
                        ProductQuantityRepository::updateQuantity($input['warehouse_id'], $inputInventory['product_id'], -1 * $inputInventory['quantity']);
                        // Todo : log box notfound in to table box_moving with location_id = null
                        $InputBoxMoving = [
                            'box_id' => $itemBox->id,
                            'location_id' => null,
                            'warehouse_id' => $input['warehouse_id'],
                            'user_id' => $input['user_id'],
                            'product_id' => $inputInventory['product_id'],
                            'quantity' => $itemBox->quantity,
                            'pre_location_id' => $input['location_id'],
                        ];
                        BoxMoving::create($InputBoxMoving);
                    }

                    $inputTestCountItem['type'] = TestCountItem::TYPE_NOT_FOUND;
                }
                TestCountItem::create($inputTestCountItem);
            }
        }

        if (!empty($input['barcode_moving'])) {
            foreach ($input['barcode_moving'] as $itemBarcode) {
                $itemInputMoving['barcode'] = $itemBarcode;
                $itemInputMoving['location_id'] = $input['location_id'];
                $itemInputMoving['warehouse_id'] = $input['warehouse_id'];
                $itemInputMoving['user_id'] = $input['user_id'];

                $box = Box::where('barcode', $itemBarcode)
                    ->where('warehouse_id', $input['warehouse_id'])
                    ->first();

                $location = Location::where('id', $box->location_id)
                    ->where('warehouse_id', $input['warehouse_id'])
                    ->first();

                if (!empty($box)) {
                    $boxMoving = BoxMoving::where('box_id', $box->id)->latest('id')->first();

                    if ($location->type === 0 && !empty($boxMoving)) { // neu box ma thuoc pullingshelve thi khong moving di
                        BoxMovingRepository::manualMoving($itemInputMoving);
                        $inputTestCountItem['test_count_id'] = $testCount->id;
                        $inputTestCountItem['location_id'] = $input['location_id'];
                        $inputTestCountItem['box_id'] = $box->id;
                        $inputTestCountItem['type'] = TestCountItem::TYPE_BOX_MOVING;
                        TestCountItem::create($inputTestCountItem);
                    }
                }
            }
        }

        if (!empty($boxCodeNew)) {
            $this->insertBoxNew($boxCodeNew, $input['warehouse_id'], $input['location_id'], $testCount->id);
        }

        $box_on_hand = TestCountItem::where('test_count_id', $testCount->id)->where('type', '<>', TestCountItem::TYPE_NOT_FOUND)->count();
        TestCount::where('id', $testCount->id)->update(['box_on_hand' => $box_on_hand]);

        // sau khi test count complete thì check xem co test count pause khong, neu co thi xoa test count pause do di
        $isTestCountPause = TestCountPause::where('location_id', $input['location_id'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('is_delete', 0)
            ->first();

        if (!empty($isTestCountPause)) {
            $isTestCountPause->update(['is_delete' => 1]);
        }

        //todo:  update time end for time checking
        $timeCheckingRepository = new TimeCheckingRepository();
        $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

        return response()->json(TestCount::latest('id')->first(), 201);
    }

    private function insertBoxMoving($box, $boxMoving, $input)
    {
        $pre_location_id = $boxMoving->location_id;
        $new_location_id = $input['location_id'];
        $product_id = $boxMoving->product_id;
        $quantity = $boxMoving->quantity;
        $inventory_addition_id = $boxMoving->inventory_addition_id;
        BoxMoving::create([
            'box_id' => $box->id,
            'pre_location_id' => $pre_location_id,
            'location_id' => $new_location_id,
            'warehouse_id' => $input['warehouse_id'],
            'user_id' => $input['user_id'],
            'product_id' => $product_id,
            'inventory_addition_id' => $inventory_addition_id,
            'quantity' => $quantity,
            'created_at' => date('Y-m-d H:i:s'),
        ]);
        Box::where('id', $box->id)->update(['location_id' => $new_location_id]);
        $preLocationProduct = LocationProduct::where('location_id', $pre_location_id)
            ->where('product_id', $product_id)->first();

        if (!empty($preLocationProduct)) {
            ///Todo : update Pre Location
            $quantity = $preLocationProduct->quantity - $quantity;
            LocationProduct::where('id', $preLocationProduct->id)->update(['quantity' => $quantity]);
        }

        $newLocationProduct = LocationProduct::where('location_id', $new_location_id)
            ->where('product_id', $product_id)->first();

        if (!empty($newLocationProduct)) {
            ///Todo : update New Location
            $quantity = $newLocationProduct->quantity + $quantity;
            LocationProduct::where('id', $newLocationProduct->id)->update(['quantity' => $quantity]);
        }
    }

    public function scanBox($input)
    {
        $box = Box::with('location:id,barcode,type')
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('barcode', $input['barcode'])
            ->first();

        if (!empty($box) && $box->location->type != Location::RACK) {
            return response()->json(['invalid_barcode' => ['You can not scan box in pending locations or pulling shelves!']], 422);
        }

        $box = Box::where('warehouse_id', $input['warehouse_id'])
            ->where('location_id', '<>', $input['location_id'])
            ->where('barcode', $input['barcode'])
            ->where('is_deleted', 0)
            ->first();

        if (!empty($box)) {
            $location = Location::where('warehouse_id', $input['warehouse_id'])
                ->where('id', '=', $box->location_id)
                ->first();
            $product = Product::where('id', '=', $box->product_id)
                ->first();

            return response()->json([
                'barcode_another_location' => ['Box is another location, do you want moving it to this location ?'],
                'box' => $box,
                'location' => $location,
                'product' => $product
            ], 422);
        }

        $box = Box::where('warehouse_id', $input['warehouse_id'])
            ->where('location_id', $input['location_id'])
            ->where('barcode', $input['barcode'])
            ->where('is_deleted', 0)
            ->first();

        if ($box) {
            if (is_null($box->product_id)) {
                return response()->json(['barcode' => ['Box has not product, you must addition product !']], 422);
            }

            return response()->json(['barcode' => ['Success']], 200);
        }

        $box = Box::where('warehouse_id', $input['warehouse_id'])
            ->where('barcode', $input['barcode'])
            ->first();
        $barcodeBox = $input['barcode'];

        if (empty($box)) {
            return response()->json([
                'barcode_not_product' => ["Barcode $barcodeBox needs to be added gtin!"]
            ], 422);
        }

        if ($box) {
            return response()->json([
                'barcode' => ["Barcode $barcodeBox is not exist or deleted !"]
            ], 422);
        }

        return response()->json(['barcode' => ['Success']], 200);
    }

    public function testCountDetail($input)
    {
        $data = DB::table('test_count')
            ->select('test_count.*',
                'user.username', 'location.barcode as location', 'test_count_item.box_id', 'test_count_item.type', 'box.barcode',
                'product.name as product_name', 'box.quantity')
            ->leftJoin('test_count_item', 'test_count_item.test_count_id', '=', 'test_count.id')
            ->leftJoin('box', 'box.id', '=', 'test_count_item.box_id')
            ->leftJoin('product', 'product.id', '=', 'box.product_id')
            ->leftJoin('user', 'user.id', '=', 'test_count.user_id')
            ->leftJoin('location', 'location.id', '=', 'test_count.location_id')
            ->where('test_count.warehouse_id', $input['warehouse_id'])
            ->where('test_count.id', $input['id'])
            ->get();

        $totalProduct = 0;
        $totalProductNotFound = 0;

        foreach ($data as $key => $item) {
            if ($item->type == 'not_found') {
                $totalProductNotFound += $item->quantity;
            } else {
                $totalProduct += $item->quantity;
            }
        }

        return [
            'data' => $data,
            'total_product' => $totalProduct,
            'total_product_not_found' => $totalProductNotFound,
            'total_box' => count($data)
        ];
    }

    /**
     * ko su dung
     */
    public function deleteTestCount($id)
    {
        DB::beginTransaction();

        try {
            $box = Box::query()->where('id', $id)->first();

            // cap nhat lai quantiy
            LocationProductRepository::updateQuantity($box->location_id, $box->product_id, $box->quantity * (-2));
            ProductQuantityRepository::updateQuantity($box->warehouse_id, $box->product_id, $box->quantity * (-2));

            // update lai status box
            $box->update(['is_deleted' => 1]);

            DB::commit();

            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('TestCountAdjustmentRepository.deleteTestCount', [$exception]);

            return false;
        }
    }

    private function insertBoxNew($input, $warehouse, $location, $testCountId)
    {
        DB::beginTransaction();

        try {
            foreach ($input as $key => $item) {
                $box = Box::create([
                    'warehouse_id' => $warehouse,
                    'barcode' => $item['barcode'],
                    'location_id' => $location,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'cost_value' => $item['cost_value'],
                    'country' => $item['country_save'] ?? null,
                ]);

                BoxMoving::create([
                    'box_id' => $box->id,
                    'location_id' => $location,
                    'warehouse_id' => $warehouse,
                    'user_id' => auth()->user()['id'],
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                ]);

                Inventory::create([
                    'direction' => Inventory::DIRECTION_INPUT,
                    'type' => Inventory::TYPE_ADJUST,
                    'product_id' => $item['product_id'],
                    'warehouse_id' => $warehouse,
                    'location_id' => $location,
                    'user_id' => auth()->user()['id'],
                    'object_id' => $testCountId,
                    'box_id' => $box->id,
                    'object_name' => Inventory::OBJECT_TEST_COUNT,
                    'quantity' => $item['quantity'],
                    'cost_total' => $item['cost_value'],
                ]);

                ProductQuantityRepository::updateQuantity($warehouse, $item['product_id'], $item['quantity']);
                LocationProductRepository::updateQuantity($location, $item['product_id'], $item['quantity']);

                $inputTestCountItem['test_count_id'] = $testCountId;
                $inputTestCountItem['location_id'] = $location;
                $inputTestCountItem['box_id'] = $box->id;
                $inputTestCountItem['type'] = TestCountItem::TYPE_BOX_NEW;

                TestCountItem::create($inputTestCountItem);
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('TestCountAdjustmentRepository.insertBoxNew', [$exception]);

            return response()->json($exception->getMessage(), 500);
        }
    }

    public function pauseTestCount($input)
    {
        // TODO: Implement pauseTestCount() method.
        $dataBox = [
            'barcode_found' => $input['barcode_found'],
            'barcode_moving' => $input['barcode_moving'],
            'barcode_not_found' => $input['barcode_not_found'],
            'barcode_new' => $input['barcode_new'],
        ];
        $dataInsert = [
            'location_id' => $input['location_id'],
            'warehouse_id' => $input['warehouse_id'],
            'data' => json_encode($dataBox),
        ];

        $isTestCountPause = TestCountPause::where('location_id', $input['location_id'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('is_delete', 0)
            ->first();

        if (!empty($isTestCountPause)) {
            return $isTestCountPause->update($dataInsert);
        }

        //todo:  update time end for time checking
        $timeCheckingRepository = new TimeCheckingRepository();
        $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

        return TestCountPause::create($dataInsert);
    }

    public function getAllBoxInLocation($input)
    {
        $dataBox = $this->boxRepository->getBoxInLocationForTestCount($input);
        $isTestCountPause = TestCountPause::where('location_id', $input['location_id'])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('is_delete', 0)
            ->first();

        if (empty($isTestCountPause)) {
            $dataOut = [];

            foreach ($dataBox as $key => $item) {
                $item['is_success'] = false;
                $item['product_name'] = $item['product']['name'];
                $dataOut[] = $item;
            }

            return [
                'test_count_pause' => false,
                'data' => $dataOut,
            ];
        }

        $dataBoxTestCountPause = json_decode($isTestCountPause['data']);
        $dataTrue = [];
        $dataFalse = [];

        foreach ($dataBox as $key => $item) {
            if (in_array($item['barcode'], $dataBoxTestCountPause->barcode_found)) {
                $item['is_success'] = true;
                $item['product_name'] = $item['product']['name'];
                $dataTrue[] = $item;
            } else {
                $item['is_success'] = false;
                $item['product_name'] = $item['product']['name'];
                $dataFalse[] = $item;
            }
        }

        $dataNew = array_merge($dataBoxTestCountPause->barcode_moving, $dataTrue, $dataFalse);
        $dataOut = [
            'test_count_pause' => true,
            'data' => $dataNew,
            'barcode_new' => $dataBoxTestCountPause->barcode_new
        ];

        return $dataOut;
    }

    public function getAllTestCountPause($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = DB::table('test_count_pause')
            ->select('test_count_pause.*', 'location.barcode as location')
            ->where('test_count_pause.warehouse_id', $input['warehouse_id'])
            ->where('test_count_pause.is_delete', 0)
            ->join('location', 'location.id', '=', 'test_count_pause.location_id');
        $data = $query->orderBy('test_count_pause.id', 'desc')
            ->paginate($limit);

        if (count($data->items()) > 0) {
            foreach ($data->items() as &$item) {
                $item->data = json_decode($item->data);
            }
        }

        return $data;
    }
}
