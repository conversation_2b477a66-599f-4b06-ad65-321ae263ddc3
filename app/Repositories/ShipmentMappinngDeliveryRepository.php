<?php

namespace App\Repositories;

use App\Models\ShipmentAccount;
use App\Models\ShipmentMappingDelivery;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ShipmentMappinngDeliveryRepository extends CommonRepository
{
    public function reportByRank($data)
    {
        $startDate = $data['start_date'] ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = $data['end_date'] ?? Carbon::now()->endOfMonth()->format('Y-m-d');
        $account = $data['account'] ?? null;

        $topStatesQuery = ShipmentMappingDelivery::whereBetween('delivery_date', [$startDate, $endDate]);
        $topZipcodesQuery = ShipmentMappingDelivery::whereBetween('delivery_date', [$startDate, $endDate]);

        if (!empty($data['warehouse_id'])) {
            $topStatesQuery->where('warehouse_id', $data['warehouse_id']);
            $topZipcodesQuery->where('warehouse_id', $data['warehouse_id']);
        }

        if (!empty($account)) {
            $topStatesQuery->where('shipment_account', $account);
            $topZipcodesQuery->where('shipment_account', $account);
        }

        if (!empty($data['state'])) {
            $topZipcodesQuery->where('state', $data['state']);
        }

        $topStates = $topStatesQuery
            ->select('state', 'state_name', \DB::raw('SUM(delivered_count) as total_delivered'))
            ->groupBy('state')
            ->orderByDesc('total_delivered')
            ->limit(5)
            ->get();

        $topZipcodes = $topZipcodesQuery
            ->select('state', 'state_name', 'zipcode_prefix', \DB::raw('SUM(delivered_count) as total_delivered'))
            ->groupBy('zipcode_prefix')
            ->orderByDesc('total_delivered')
            ->limit(5)
            ->get();

        return !empty($data['state']) ? ['top_zipcodes' => $topZipcodes] : [
            'top_states' => $topStates,
            'top_zipcodes' => $topZipcodes,
        ];
    }

    public function statistic($request)
    {
        $startDate = $request['start_date'] ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = $request['end_date'] ?? Carbon::now()->endOfMonth()->format('Y-m-d');

        $accounts = ShipmentAccount::all();
        $accountNames = $accounts->pluck('name')->toArray();

        $query = ShipmentMappingDelivery::whereBetween('delivery_date', [$startDate, $endDate])
            ->whereIn('shipment_account', $accountNames);

        if (!empty($request['warehouse_id'])) {
            $query->where('warehouse_id', $request['warehouse_id']);
        }

        $topAccounts = $query->select(
            'shipment_account',
            \DB::raw('SUM(delivered_count) as total_delivered'),
        )
            ->groupBy('shipment_account')
            ->pluck('total_delivered', 'shipment_account'); // [shipment_account => total]

        $details = $accounts->map(fn ($account) => [
            'shipment_account' => $account->label,
            'total_delivered' => $topAccounts[$account->name] ?? 0,
            'shipment_account_name' => $account->name,
            'shipment_account_tooltip' => $account->tooltip,
        ]);

        $totalAll = $details->sum('total_delivered');

        return [
            'total_all' => $totalAll,
            'details' => $details,
        ];
    }

    public function reportByState($request)
    {
        $startDate = $request['start_date'] ?? Carbon::now()->startOfMonth()->format('Y-m-d');
        $endDate = $request['end_date'] ?? Carbon::now()->endOfMonth()->format('Y-m-d');
        $account = $request['account'] ?? null;

        // Full map: name => abbreviation
        $stateMap = [
            'alabama' => 'AL',
            'alaska' => 'AK',
            'arizona' => 'AZ',
            'arkansas' => 'AR',
            'california' => 'CA',
            'colorado' => 'CO',
            'connecticut' => 'CT',
            'delaware' => 'DE',
            'district of columbia' => 'DC',
            'florida' => 'FL',
            'georgia' => 'GA',
            'hawaii' => 'HI',
            'idaho' => 'ID',
            'illinois' => 'IL',
            'indiana' => 'IN',
            'iowa' => 'IA',
            'kansas' => 'KS',
            'kentucky' => 'KY',
            'louisiana' => 'LA',
            'maine' => 'ME',
            'maryland' => 'MD',
            'massachusetts' => 'MA',
            'michigan' => 'MI',
            'minnesota' => 'MN',
            'mississippi' => 'MS',
            'missouri' => 'MO',
            'montana' => 'MT',
            'nebraska' => 'NE',
            'nevada' => 'NV',
            'new hampshire' => 'NH',
            'new jersey' => 'NJ',
            'new mexico' => 'NM',
            'new york' => 'NY',
            'north carolina' => 'NC',
            'north dakota' => 'ND',
            'ohio' => 'OH',
            'oklahoma' => 'OK',
            'oregon' => 'OR',
            'pennsylvania' => 'PA',
            'rhode island' => 'RI',
            'south carolina' => 'SC',
            'south dakota' => 'SD',
            'tennessee' => 'TN',
            'texas' => 'TX',
            'utah' => 'UT',
            'vermont' => 'VT',
            'virginia' => 'VA',
            'washington' => 'WA',
            'west virginia' => 'WV',
            'wisconsin' => 'WI',
            'wyoming' => 'WY',
            'puerto rico' => 'PR',
            'guam' => 'GU',
            'american samoa' => 'AS',
            'armed forces americas' => 'AA',
            'armed forces europe' => 'AE',
            'armed forces pacific' => 'AP',
            'virgin islands' => 'VI',
        ];
        $flippedMap = array_flip($stateMap);

        $query = ShipmentMappingDelivery::whereBetween('delivery_date', [$startDate, $endDate]);

        if (!empty($request['warehouse_id'])) {
            $query->where('warehouse_id', $request['warehouse_id']);
        }

        if ($account) {
            $query->where('shipment_account', $account);
        }

        $rawData = $query->select(
            DB::raw('LOWER(state) as state'),
            DB::raw('SUM(delivered_count) as total_delivered'),
        )
            ->groupBy('state')
            ->get()
            ->keyBy(fn ($item) => strtolower(trim($item->state)));

        $totalAll = $rawData->sum('total_delivered');

        $top5ZipCodeByState = DB::select("
            SELECT
                state,
                state_name,
                GROUP_CONCAT(zipcode_prefix ORDER BY total_delivered DESC SEPARATOR ', ') AS top_zipcodes
            FROM (
                SELECT
                    state,
                    state_name,
                    zipcode_prefix,
                    SUM(delivered_count) AS total_delivered,
                    ROW_NUMBER() OVER (PARTITION BY state ORDER BY SUM(delivered_count) DESC) AS rn
                FROM shipment_mapping_delivery
                WHERE delivery_date BETWEEN ? AND ?
                " . (!empty($account) ? 'AND shipment_account = ?' : '') . '
                GROUP BY state, state_name, zipcode_prefix
            ) AS ranked
            WHERE rn <= 5
            GROUP BY state, state_name
            ORDER BY state
        ', array_merge([$startDate, $endDate], !empty($account) ? [$account] : []));

        $top5ZipCodeByState = collect($top5ZipCodeByState)->keyBy('state'); // dễ lookup theo state

        $result = collect($flippedMap)->map(function ($abbr, $name) use ($rawData, $totalAll, $top5ZipCodeByState) {
            $stateKey = strtolower($name);
            $delivered = isset($rawData[$stateKey]) ? $rawData[$stateKey]->total_delivered : 0;
            $topZipcodes = $top5ZipCodeByState->get($name)->top_zipcodes ?? null;

            return [
                'state' => $name,
                'total_delivered' => $delivered,
                'percentage' => $totalAll > 0 ? round(($delivered / $totalAll) * 100, 2) : 0,
                'top_zipcodes' => $topZipcodes,
            ];
        })->sortByDesc('total_delivered')->values();

        return [
            'total_all' => $totalAll,
            'states' => $result,
        ];
    }
}
