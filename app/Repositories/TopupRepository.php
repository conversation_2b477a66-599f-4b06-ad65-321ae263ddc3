<?php

namespace App\Repositories;

use App\Models\AutoRefill;
use App\Models\Setting;
use App\Models\Store;
use App\Models\Wallet;
use App\Models\WalletTopup;
use App\Models\WalletTransaction;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TopupRepository extends CommonRepository
{
    protected $transactionRepository;

    protected $walletRepository;

    private $paymentGateway;

    public function __construct(TransactionRepository $transactionRepository, WalletRepository $walletRepository)
    {
        $this->transactionRepository = $transactionRepository;
        $this->walletRepository = $walletRepository;
        setProviderConfig('stripe');
    }

    public function buildQuery(&$query, $params)
    {
        if (!empty($params['keyword'])) {
            $query->where('reference_number', 'LIKE', '%' . $params['keyword'] . '%')
                ->orWhere('topup_number', 'LIKE', '%' . $params['keyword'] . '%');
        }

        if (!empty($params['store_id'])) {
            $query->where('store_id', $params['store_id']);
        } elseif (empty($params['fetch_all'])) {
            $query->where('store_id', Auth::user()->id);
        }

        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $startDate = Carbon::parse($params['start_date'])->startOfDay()->toDateTimeString();
            $endDate = Carbon::parse($params['end_date'])->endOfDay()->toDateTimeString();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        if (!empty($params['updated_start_date']) && !empty($params['updated_end_date'])) {
            $updatedStartDate = Carbon::parse($params['updated_start_date'])->startOfDay()->toDateTimeString();
            $updatedEndDate = Carbon::parse($params['updated_end_date'])->endOfDay()->toDateTimeString();
            $query->whereBetween('updated_at', [$updatedStartDate, $updatedEndDate]);
        }

        if (!empty($params['payment_method'])) {
            $query->where('payment_method', $params['payment_method']);
        }
    }

    public function getListTopup($params)
    {
        setTimezone();
        $relation = ['store:name,id,code'];
        $query = WalletTopup::query()->with($relation);
        $this->buildQuery($query, $params);

        return $query->orderBy('id', 'desc')->paginate($params['limit'] ?? self::LIMIT);
    }

    public function createTopup(Store $store, $params)
    {
        try {
            $store->load('client');
            $paymentGateway = $params['payment_gateway'] ?? WalletTopup::PAYMENT_GATEWAY_STRIPE;

            AutoRefill::where('store_id', auth()->id())
            ->where('payment_gateway', $paymentGateway)
            ->update([
                'reason' => null,
            ]);

            if (isset($params['payment_method_id']) && empty($params['payment_method_data'])) {
                $paymentMethod = $this->getPaymentGateway($paymentGateway)
                    ->retrievePaymentMethod($params['payment_method_id'])
                    ->toArray();
            }

            $finalAmount = $this->calculatorTopupAmount(
                $params['amount'],
                $paymentGateway,
                $params['payment_method'],
            );

            $topupNumber = getSequenceNumber('topup', $store->id);

            $payload = [
                'amount' => $finalAmount,
                'amount_received' => '0',
                'amount_requested' => $params['amount'] ?? '0',
                'payment_gateway' => $paymentGateway,
                'store_id' => $store->id,
                'approval_method' => WalletTopup::APPROVE_AUTO,
                'payment_method' => $params['payment_method'] ?? null,
                'topup_number' => $topupNumber,
                'status' => WalletTopup::STATUS_PENDING,
                'payment_account_id' => config('cashier.payment_account_id'),
                'is_refill' => $params['is_refill'] ?? false,
                'payment_method_id' => $params['payment_method_id'] ?? null,
                'payment_method_data' => $params['payment_method_data'] ?? $paymentMethod ?? null,
            ];
            $storeName = $store->name ?? '';
            $clientName = $store->client ? $store->client->name : '';
            $paymentMethod = $params['payment_method_data'] ?? $paymentMethod ?? null;
            $method = null;

            if ($paymentMethod['type'] == WalletTopup::CARD_METHOD) {
                $method = $paymentMethod['card']['brand'] . '****' . $paymentMethod['card']['last4'];
            }

            if ($paymentMethod['type'] == WalletTopup::BANK_TRANSFER_METHOD) {
                $method = 'Bank Account' . '****' . $paymentMethod['us_bank_account']['last4'];
            }

            $message = "Top up created #{$topupNumber} \n"
                . "Store: {$storeName} - Client: {$clientName} \n"
                . 'Amount: ' . number_format($params['amount'], 2) . " \n"
                . "Method: {$method} \n";

            $topup = WalletTopup::create($payload);
            $wallet = Wallet::where('store_id', $store->id)->first();

            if ($params['payment_method'] == WalletTopup::BANK_TRANSFER_METHOD && $wallet && $wallet->balance >= 0) {
                $newBalance = $this->walletRepository->update(WalletTransaction::TRANSACTION_TYPE_TOPUP, $finalAmount, $store->id);
                $transaction = WalletTransaction::create([
                    'store_id' => $store->id,
                    'amount' => $topup->amount,
                    'new_balance' => $newBalance,
                    'direction' => WalletTransaction::DIRECTION_IN,
                    'type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_id' => $topup->id,
                    'object_type' => WalletTransaction::TRANSACTION_TYPE_TOPUP,
                    'object_number' => $topup->topup_number,
                    'description' => 'Topup #' . $topup->topup_number,
                    'note' => null,
                ]);
            }

            $url = Setting::where('name', Setting::TOPUP_ALERT_GOOGLE_SPACE)->first()->value ?? '';
            sendGoogleChat($message, $url);

            return $topup;
        } catch (\Throwable $th) {
            Log::error('TopupRepository.createStripeTopup catch', [$th]);
            throw $th;
        }
    }

    public function calculatorTopupAmount($amount, $paymentGateway, $paymentMethod)
    {
        switch ($paymentGateway) {
            case WalletTopup::PAYMENT_GATEWAY_STRIPE:
                $paymentService = $this->getPaymentGateway($paymentGateway);
                $finalAmount = $paymentService->calculatorTopupAmount($amount, $paymentMethod);

                return $finalAmount;

            default:
                throw new Exception('Payment gateway not supported!');
        }
    }

    public function getPaymentGateway($paymentGateway)
    {
        switch ($paymentGateway) {
            case WalletTopup::PAYMENT_GATEWAY_STRIPE:
                $this->paymentGateway = resolve(StripeRepository::class);
                $this->paymentGateway->initialize();
                break;

            default:
                throw new Exception('Payment gateway not supported!');
        }

        return $this->paymentGateway;
    }

    public function getKey($paymentGateway)
    {
        switch ($paymentGateway) {
            case WalletTopup::PAYMENT_GATEWAY_STRIPE:
                return config('cashier.key');

            default:
                throw new Exception('Payment gateway not supported!');
        }
    }

    public function countPending()
    {
        return WalletTopup::where('status', WalletTopup::STATUS_PENDING)
            ->count();
    }

    public function makeDataTopupRefill($store, $paymentGateway, $paymentMethodId = null, $isPrimaryMethod = true)
    {
        $paymentMethod = $this->getPaymentGateway($paymentGateway)
            ->getPaymentMethods($store)
            ->when(
                isset($paymentMethodId),
                fn ($query) => $query->where('id', $paymentMethodId),
                fn ($query) => $query->where('is_default', $isPrimaryMethod),
            )
            ->first();

        if (empty($paymentMethod)) {
            throw new Exception('Payment method not found!');
        }

        $autoRefill = $store->autoRefill()->first();
        $requestAmount = $autoRefill->amount;

        return [
            'amount' => $requestAmount,
            'is_refill' => true,
            'payment_gateway' => $paymentGateway,
            'payment_method' => $paymentMethod['type'],
            'payment_method_id' => $paymentMethod['id'],
            'payment_method_data' => $paymentMethod,
        ];
    }

    public function countTotalTopupByMethod($params)
    {
        $total = [];
        foreach (WalletTopup::listTopupMethod() as $method) {
            $query = WalletTopup::query();
            $input = [
                'payment_method' => $method,
                'status' => WalletTopup::STATUS_APPROVED,
                'start_date' => $params['start_date'] ?? null,
                'end_date' => $params['end_date'] ?? null,
                'store_id' => $params['store_id'] ?? null,
                'keyword' => $params['keyword'] ?? null,
                'fetch_all' => true,
            ];
            $this->buildQuery($query, $input);
            $total[$method] = $query->sum('amount');
        }

        return $total;
    }
}
