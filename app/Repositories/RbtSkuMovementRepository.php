<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\RbtProduct;
use App\Models\RbtSkuMovement;
use App\Models\RbtSkuMovementDetail;
use App\Models\RbtSkuMovementImportHistory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RbtSkuMovementRepository
{
    public function validateRow(array $row, $existDarkPods, $existTasks, $allLocation, $locationBarcode, $existPullingShelves, $allDarkPods, $existRackTasks): array
    {
        $errors = [];
        $validatedRow = null;

        $action = strtolower(trim($row['Action'] ?? ''));

        if (!in_array($action, ['add', 'move_sku', 'move_rack', 'swap_sku', 'swap_rack', 'remove'])) {
            return ['errors' => ['Cannot determine action']];
        }

        if (in_array($action, ['add', 'move_sku', 'swap_sku']) && (empty($row['Rack']) || empty($row['Rack type']))) {
            $errors[] = 'Rack and Rack type are required for this action';
        }

        $originSku = $row['Origin'] ?? null;
        $destination = $row['Destination'] ?? null;

        if ($destination === $originSku) {
            $errors[] = 'Destination cannot be the same as the origin';
        }

        switch ($action) {
            case RbtSkuMovementDetail::ACTION_ADD:
            case RbtSkuMovementDetail::ACTION_MOVE_SKU:
                if (!$originSku) {
                    $errors[] = 'Origin SKU is required';
                }
                if (!$destination) {
                    $errors[] = 'Destination is required';
                }

                if (empty($errors)) {
                    $isAdd = $action === RbtSkuMovementDetail::ACTION_ADD;
                    $isMove = $action === RbtSkuMovementDetail::ACTION_MOVE_SKU;

                    if ($isAdd && $this->checkSkuStatus($originSku, $existDarkPods)) {
                        $errors[] = 'This SKU already exists in Dark Pod';
                    }

                    if ($isMove && !$this->checkSkuStatus($originSku, $existDarkPods)) {
                        $errors[] = 'This SKU does not exist in Dark Pod';
                    }

                    if ($this->hasPendingTask($originSku, $existTasks)) {
                        $errors[] = 'This SKU already has another pending change request';
                    }

                    if ($isAdd) {
                        if (empty($row['Rack type']) || empty($row['Rack'])) {
                            $errors[] = 'Rack type and Rack are required for Add action';
                        }
                    }

                    $isLocationExist = $this->checkLocationExist($destination, $locationBarcode);
                    $locationValid = false;

                    $validateLocationCode = null;

                    if (!empty($row['Rack type']) && !empty($row['Rack'])) {
                        $validateLocationCode = $this->validateLocationCodeByRackType(
                            $destination,
                            $row['Rack type'],
                            $row['Rack'],
                        );

                        if ($validateLocationCode) {
                            $errors[] = $validateLocationCode;
                        }
                    }

                    // Nếu vị trí tồn tại và không vi phạm định dạng, thì kiểm tra trống
                    if ($isLocationExist && empty($validateLocationCode)) {
                        $isEmpty = $this->checkLocationIsEmpty($destination, $allLocation);

                        if ($isMove && !$isEmpty) {
                            $errors[] = 'Destination must be empty when moving SKU';
                        }

                        if ($isAdd && !$isEmpty) {
                            $errors[] = 'This location already contains another SKU';
                        }

                        if ($isEmpty) {
                            $locationValid = true;
                        }
                    }

                    // Nếu vị trí không tồn tại, nhưng mã vẫn hợp lệ thì cho phép ADD/MOVE
                    if (!$isLocationExist && empty($validateLocationCode)) {
                        $locationValid = true;
                    }

                    // Nếu vị trí hợp lệ, tạo dòng validated
                    if ($locationValid) {
                        if ($isAdd) {
                            $product = Product::where('sku', $originSku)->first();
                            if (!$product) {
                                $errors[] = 'SKU does not exist';
                            } else {
                                $validatedRow = [
                                    'product_id' => $product->id,
                                    'sku' => $product->sku,
                                    'action' => RbtSkuMovementDetail::ACTION_ADD,
                                    'new_barcode' => $destination,
                                    'old_barcode' => null,
                                    'type' => $row['Rack type'] ?? null,
                                ];
                            }
                        }

                        if ($isMove) {
                            $darkpod = $allDarkPods->firstWhere('product_sku', $originSku);
                            if (!$darkpod) {
                                $errors[] = 'SKU does not exist';
                            } else {
                                $validatedRow = [
                                    'product_id' => $darkpod->product_id ?? null,
                                    'sku' => $darkpod->product_sku ?? null,
                                    'action' => RbtSkuMovementDetail::ACTION_MOVE_SKU,
                                    'new_barcode' => $destination,
                                    'old_barcode' => $darkpod->location_barcode ?? null,
                                    'type' => $row['Rack type'] ?? null,
                                ];
                            }
                        }
                    }
                }
                break;

            case RbtSkuMovementDetail::ACTION_MOVE_RACK:
                if (!$originSku) {
                    $errors[] = 'Origin rack is required';
                }
                $allRacks = $allDarkPods->pluck('location_barcode')->toArray();
                $allRackHeads = collect($allRacks)->map(function ($barcode) {
                    return Str::before($barcode, '-');
                })->unique()->values()->toArray();

                if (!$this->checkRackStatus($originSku, $allRackHeads)) {
                    $errors[] = 'This rack does not exist in Dark Pod';
                }
                $destination = $row['Destination'] ?? null;

                if (!$destination) {
                    $errors[] = 'Destination is required';
                }

                $filteredRacks = $allDarkPods->filter(function ($item) use ($originSku) {
                    return Str::startsWith($item->location_barcode, $originSku);
                });

                $filteredSkus = $filteredRacks->pluck('product_sku')->values()->toArray();

                if (in_array($originSku, $existRackTasks)) {
                    $errors[] = 'This rack already has another pending change request';
                }

                if (!empty($row['Rack type']) && !empty($row['Rack'])) {
                    $validateLocationCode = $this->validateLocationCodeByRackType(
                        $destination,
                        $row['Rack type'],
                        $row['Rack'],
                    );

                    if ($validateLocationCode) {
                        $errors[] = $validateLocationCode;
                    }
                }

                if (empty($errors)) {
                    $filterLocation = $allLocation->filter(function ($item) use ($destination) {
                        return Str::startsWith($item->barcode, $destination) && $item->type === 3;
                    });

                    $filteredLocation = $filterLocation->map(function ($item) {
                        return substr($item->barcode, 0, 5);
                    })->unique()->values()->toArray();

                    $isLocationExist = $this->checkLocationExist($destination, $filteredLocation);

                    if ($isLocationExist) {
                        if ($this->checkRacksIsEmpty($filterLocation)) {
                            $validatedRow = [
                                'action' => RbtSkuMovementDetail::ACTION_MOVE_RACK,
                                'new_barcode' => $destination,
                                'old_barcode' => $originSku,
                                'type' => $row['Rack type'] ?? null,
                            ];
                        } else {
                            $errors[] = 'This location already contains another SKU';
                        }
                    } else {
                        if (!$this->isValidRackFormat($destination, $allDarkPods)) {
                            $errors[] = '[Destination] has an invalid format';
                        } else {
                            $validatedRow = [
                                'action' => RbtSkuMovementDetail::ACTION_MOVE_RACK,
                                'new_barcode' => $destination,
                                'old_barcode' => $originSku,
                                'type' => $row['Rack type'] ?? null,
                            ];
                        }
                    }
                }

                $locations = Location::where('barcode', 'like', $destination . '%')
                    ->where('type', Location::MOVING_SHELVES)
                    ->where('is_deleted', Location::NOT_DELETED)
                    ->get();
                if ($locations->isNotEmpty()) {
                    $errors[] = 'This destination already exists in Dark Pod';
                }
                break;

            case RbtSkuMovementDetail::ACTION_SWAP_SKU:
                $isSkuValid = $this->checkSkuStatus($originSku, $existDarkPods);
                $hasPending = $this->hasPendingTask($originSku, $existTasks);
                $isLocationExist = $this->checkLocationExist($destination, $locationBarcode);
                $isEmpty = $this->checkLocationIsEmpty($destination, $allLocation);

                if (!$isSkuValid) {
                    $errors[] = 'This SKU does not exist in Dark Pod';
                } else {
                    $location = Location::where('barcode', $destination)
                        ->where('type', Location::MOVING_SHELVES)
                        ->where('is_deleted', Location::NOT_DELETED)
                        ->first();
                    if (!empty($location) && empty($location->rbt_sku)) {
                        $errors[] = 'This Destination does not contain any SKU';
                    } elseif (!empty($location) && $location->rbt_sku == $originSku) {
                        $errors[] = 'This location already contains this SKU';
                    }
                }
                if ($hasPending) {
                    $errors[] = 'This SKU already has another pending change request';
                }

                if (!$isLocationExist) {
                    $errors[] = 'This Destination does not exist in Dark Pod';
                }

                if (!empty($row['Rack type']) && !empty($row['Rack'])) {
                    $validateLocationCode = $this->validateLocationCodeByRackType(
                        $destination,
                        $row['Rack type'],
                        $row['Rack'],
                    );

                    if ($validateLocationCode) {
                        $errors[] = $validateLocationCode;
                    }
                }

                if (empty($errors)) {
                    $darkpod = $allDarkPods->firstWhere('product_sku', $originSku);
                    $validatedRow = [
                        'product_id' => $darkpod->product_id ?? null,
                        'sku' => $darkpod->product_sku ?? null,
                        'action' => RbtSkuMovementDetail::ACTION_SWAP_SKU,
                        'new_barcode' => $destination,
                        'old_barcode' => $darkpod->location_barcode,
                        'type' => $row['Rack type'] ?? null,
                    ];
                }

                break;
            case RbtSkuMovementDetail::ACTION_SWAP_RACK:
                if (!$originSku) {
                    $errors[] = 'Origin rack is required';
                }
                $allRacks = $allDarkPods->pluck('location_barcode')->toArray();
                $allRackHeads = collect($allRacks)->map(function ($barcode) {
                    return Str::before($barcode, '-');
                })->unique()->values()->toArray();

                if (!$this->checkRackStatus($originSku, $allRackHeads)) {
                    $errors[] = 'This rack does not exist in Dark Pod';
                }

                $destination = $row['Destination'] ?? null;

                if (!$destination) {
                    $errors[] = 'Destination is required';
                }

                $filteredRacks = $allDarkPods->filter(function ($item) use ($originSku) {
                    return Str::startsWith($item->location_barcode, $originSku);
                });

                $filteredSkus = $filteredRacks->pluck('product_sku')->values()->toArray();

                if (in_array($originSku, $existRackTasks)) {
                    $errors[] = 'This rack already has another pending change request';
                }

                if (!empty($row['Rack type']) && !empty($row['Rack'])) {
                    $validateLocationCode = $this->validateLocationCodeByRackType(
                        $destination,
                        $row['Rack type'],
                        $row['Rack'],
                    );

                    if ($validateLocationCode) {
                        $errors[] = $validateLocationCode;
                    }
                }

                if (empty($errors)) {
                    $filterLocation = $allLocation->filter(function ($item) use ($destination) {
                        return Str::startsWith($item->barcode, $destination) && $item->type === 3;
                    });

                    $filteredLocation = $filterLocation->map(function ($item) {
                        return substr($item->barcode, 0, 5);
                    })->unique()->values()->toArray();

                    $isLocationExist = $this->checkLocationExist($destination, $filteredLocation);

                    if ($isLocationExist) {
                        $validatedRow = [
                            'action' => RbtSkuMovementDetail::ACTION_SWAP_RACK,
                            'new_barcode' => $destination,
                            'old_barcode' => $originSku,
                            'type' => $row['Rack type'] ?? null,
                        ];
                    } else {
                        $errors[] = 'This Destination does not exist in Dark Pod';
                    }
                }

                break;

            case RbtSkuMovementDetail::ACTION_REMOVE:
                $isSkuValid = $this->checkSkuStatus($originSku, $existDarkPods);
                $hasPending = $this->hasPendingTask($originSku, $existTasks);
                $isLocationExist = $this->checkLocationExist($destination, $locationBarcode);

                if (!$isSkuValid) {
                    $errors[] = 'This SKU does not exist in Dark Pod';
                }

                if ($hasPending) {
                    $errors[] = 'This SKU already has another pending change request';
                }

                if (empty($errors)) {
                    $darkpod = $allDarkPods->firstWhere('product_sku', $originSku);
                    $validatedRow = [
                        'product_id' => $darkpod->product_id ?? null,
                        'sku' => $darkpod->product_sku ?? null,
                        'action' => RbtSkuMovementDetail::ACTION_REMOVE,
                        'new_barcode' => $destination,
                        'old_barcode' => $darkpod->location_barcode,
                        'type' => $row['Rack type'] ?? null,
                    ];
                }
                break;

            default:
                $errors[] = "Action '$action' not yet implemented.";
                break;
        }

        return !empty($errors) ? ['errors' => $errors] : ['data' => $validatedRow ?? $row];
    }

    public function checkSkuStatus(?string $sku, array $existDarkPods): bool
    {
        if (!$sku) {
            return false;
        }

        return in_array($sku, $existDarkPods);
    }

    public function checkRackStatus(?string $rack, array $existDarkPods): bool
    {
        if (!$rack) {
            return false;
        }

        return in_array($rack, $existDarkPods);
    }

    public function hasPendingTask(?string $sku, array $existTasks): bool
    {
        if (!$sku) {
            return false;
        }

        return in_array($sku, $existTasks);
    }

    public function hasPendingRack(array $rackList, array $existTasks): bool
    {
        return !empty(array_intersect($rackList, $existTasks));
    }

    public function checkLocationExist(string $barcode, array $allLocation): bool
    {
        $barcode = strtoupper(trim($barcode));
        $allLocation = array_map(fn ($v) => strtoupper(trim($v)), $allLocation);

        return in_array($barcode, $allLocation);
    }

    public function checkMissingColumn(array $row): ?string
    {
        $requiredCols = ['Destination', 'Rack', 'Rack type'];
        $missingColumns = [];

        foreach ($requiredCols as $col) {
            if (empty($row[$col])) {
                $missingColumns[] = $col;
            }
        }

        if (!empty($missingColumns)) {
            return implode('. ', $missingColumns) . ' is required';
        }

        return null;
    }

    public function checkLocationIsEmpty(string $locationBarcode, $allLocations): bool
    {
        $location = $allLocations->firstWhere('barcode', $locationBarcode);
        if (!$location) {
            return true;
        }

        if ($location->rbt_sku != null) {
            return false;
        }

        return true; // Không có SKU => trống

        // $products = $location->locationProducts;
        // if ($products->isEmpty()) {
        //     return true;
        // }
        // $hasQuantity = $products->contains(function ($item) {
        //     return $item->quantity > 0;
        // });

        // return !$hasQuantity;
    }

    public function checkValidLocation($barcode, $sku)
    {
        $location = Location::where('barcode', $barcode)
            ->where('type', Location::MOVING_SHELVES)
            ->where('is_deleted', Location::NOT_DELETED)
            ->first();
        if ($location) {
            return $location->rbt_sku != $sku;
        }

        return false;
    }

    public function checkRacksIsEmpty($filteredRacks): bool
    {
        // Nếu tất cả đều KHÁC null => return false
        if ($filteredRacks->every(fn ($item) => !is_null($item->rbt_sku))) {
            return false;
        }

        return true; // Có ít nhất một cái là null
    }

    public function validateLocationCodeByRackType(string $locationId, string $rackType, string $rack): ?string
    {
        $validRackType = ['DOUBLE_BIN', 'TRIPLE_BIN', 'HALF_LEVEL', 'FULL_LEVEL'];

        if (!in_array(strtoupper($rackType), $validRackType)) {
            return 'Rack type has an invalid format';
        }

        if (empty($rack) || !preg_match('/^R\d+[A-Z]+\d+$/i', $rack)) {
            return 'Rack has an invalid format';
        }

        $rackType = strtoupper(trim($rackType));
        $locationId = strtoupper(trim($locationId));
        $rack = strtoupper(trim($rack));

        $parts = explode('-', $locationId);

        // ✅ Trường hợp chỉ là rack code (không có dấu -)
        if (count($parts) === 1) {
            if ($locationId !== $rack) {
                return "Rack in Location ID does not match given Rack: expected '{$rack}'. got '{$locationId}'";
            }

            return null; // ✅ Hợp lệ
        }

        // ✅ Trường hợp đầy đủ định dạng RACK-SIDE-CODE
        if (count($parts) < 3) {
            return 'Location ID has an invalid format';
        }

        if ($parts[0] !== $rack) {
            return "Rack in Location ID does not match given Rack: expected '{$rack}'. got '{$parts[0]}'";
        }

        $side = $parts[count($parts) - 2] ?? '';
        $code = $parts[count($parts) - 1] ?? '';

        $valid = [
            'DOUBLE_BIN' => [
                'codes' => ['AD1', 'AD2', 'BD1', 'BD2', 'CD1', 'CD2', 'DD1', 'DD2'],
                'sides' => ['N', 'S'],
            ],
            'TRIPLE_BIN' => [
                'codes' => ['AT1', 'AT2', 'AT3', 'BT1', 'BT2', 'BT3', 'CT1', 'CT2', 'CT3', 'DT1', 'DT2', 'DT3'],
                'sides' => ['N', 'S'],
            ],
            'HALF_LEVEL' => [
                'codes' => ['AO1', 'BO1', 'CO1', 'DO1'],
                'sides' => ['N', 'S'],
            ],
            'FULL_LEVEL' => [
                'codes' => ['A0', 'B0', 'C0', 'D0'],
                'sides' => ['NS'],
            ],
        ];

        $conf = $valid[$rackType];

        if (!in_array($code, $conf['codes'])) {
            return "Invalid code '{$code}' for rack type {$rackType}";
        }

        if (!in_array($side, $conf['sides'])) {
            return "Invalid side '{$side}' for rack type {$rackType}";
        }

        return null; // ✅ Hợp lệ
    }

    public function isValidRackFormat(string $destination, $allDarkPods): bool
    {
        // Kiểm tra định dạng: R + 2 ký tự row (chữ/số) + 2 số column
        if (!preg_match('/^R([A-Z0-9]{2})([0-9]{2})$/', $destination, $matches)) {
            return false;
        }

        $row = $matches[1];     // Ví dụ: "3E"
        $column = $matches[2];  // Ví dụ: "45"
        $rackCode = 'R' . $row . $column; // "R3E45"

        // Lấy danh sách mã rack hợp lệ từ location_barcode
        $validRackCodes = $allDarkPods
            ->map(function ($item) {
                return substr($item->location_barcode, 0, 5);
            })
            ->unique()
            ->values();

        return !$validRackCodes->contains($rackCode);
    }

    public function listTasks($params)
    {
        $query = RbtSkuMovementDetail::query();

        if (!empty($params['status'])) {
            $query->whereIn('status', $params['status']);
        }

        if (!empty($params['task_type'])) {
            $query->whereIn('action', $params['task_type']);
        }

        if (!empty($params['employee_id'])) {
            $query->where('picked_by', $params['employee_id']);
        }

        return $query->with(['createdBy', 'pickedBy', 'oldLocation.locationProducts', 'oldLocation.product', 'newLocation.locationProducts', 'newLocation.product', 'product', 'pullingShelvesQuantity'])
            ->orderBy('created_at')
            ->get();
    }

    public function pickTask($taskId, $input)
    {
        $task = RbtSkuMovementDetail::find($taskId);
        if (!$task) {
            throw new \Exception('Task not found.');
        }

        if ($task->status !== RbtSkuMovementDetail::STATUS_NEW) {
            throw new \Exception('Only tasks with status "new" can be picked.');
        }

        if ($task->picked_by) {
            throw new \Exception('This task has already been picked.');
        }
        $task->status = RbtSkuMovementDetail::STATUS_IN_PROGRESS;
        $task->picked_by = $input['employee_id'] ?? null;
        $task->picked_at = now();
        $task->save();
        $movement = RbtSkuMovement::find($task->rbt_sku_movement_id);

        if (!empty($movement) && $movement->status == RbtSkuMovement::STATUS_NEW) {
            $movement->status = RbtSkuMovement::STATUS_IN_PROGRESS;
            $movement->save();
        }

        updateStatusMovementEmit(RbtSkuMovementDetail::SKU_MOVEMENT, config('jwt.warehouse_id'));

        return $task;
    }

    public function confirmTask($taskId, $input)
    {
        try {
            DB::beginTransaction();
            $task = RbtSkuMovementDetail::with(['oldLocation.locationProducts', 'newLocation.locationProducts'])->find($taskId);
            if (!$task) {
                throw new \Exception('Task not found.');
            }

            if ($task->status !== RbtSkuMovementDetail::STATUS_IN_PROGRESS) {
                throw new \Exception('Only tasks with status "in_progress" can be confirmed.');
            }

            if ($task->confirm_by) {
                throw new \Exception('This task has already been confirmed.');
            }
            $locationProductRepo = new LocationProductRepository();

            switch ($task->action) {
                case RbtSkuMovementDetail::ACTION_ADD:
                    $locationId = null;
                    if (empty($task->newLocation)) {
                        $location = Location::create([
                            'warehouse_id' => config('jwt.warehouse_id') ?? null,
                            'barcode' => $task->new_barcode,
                            'rbt_type' => $task->type,
                            'rbt_sku' => $task->sku,
                            'type' => Location::MOVING_SHELVES,
                        ]);
                        $locationId = $location->id;
                    } elseif (!empty($task->newLocation->rbt_sku)) {
                        throw new \Exception('SKU already exists in the new location.');
                    } else {
                        $task->newLocation->rbt_sku = $task->sku;
                        $task->newLocation->save();
                        $locationId = $task->newLocation->id;
                    }
                    $product = Product::where('sku', $task->sku)->first();
                    $pullingShelves = Location::with('locationProducts')
                        ->where('warehouse_id', config('jwt.warehouse_id'))
                        ->where('type', Location::PULLING_SHELVES)
                        ->first();

                    $locationProduct = LocationProduct::where('location_id', $pullingShelves->id)
                        ->where('product_id', $product->id)
                        ->first();
                    $locationProductRepo->updateQuantityRbt(
                        $locationId,
                        $product->id,
                        $locationProduct ? $locationProduct->quantity : 0,
                    );
                    LocationProduct::where('location_id', $pullingShelves->id)
                        ->where('product_id', $product->id)->delete();

                    $rbtProduct = RbtProduct::where('product_id', $product->id)->first();
                    if (!$rbtProduct) {
                        RbtProduct::create([
                            'product_id' => $task->product_id,
                            'is_active' => true,
                            'created_by' => auth()->user()->id ?? null,
                        ]);
                    } else {
                        $rbtProduct->is_active = true;
                        $rbtProduct->save();
                    }
                    break;
                case RbtSkuMovementDetail::ACTION_MOVE_SKU:

                    if (!empty($task->newLocation->rbt_sku)) {
                        throw new \Exception('SKU already exists in the new location.');
                    }
                    if (empty($task->oldLocation)) {
                        throw new \Exception('Old location is required for moving SKU.');
                    }
                    $newLocationId = $task->newLocation->id ?? null;
                    if (empty($task->newLocation)) {
                        $newLocation = Location::create([
                            'warehouse_id' => config('jwt.warehouse_id') ?? null,
                            'barcode' => $task->new_barcode,
                            'rbt_type' => $task->type,
                            'rbt_sku' => $task->sku,
                            'type' => Location::MOVING_SHELVES,
                        ]);
                        $newLocationId = $newLocation->id;
                    } else {
                        $task->newLocation->rbt_sku = $task->sku;
                        $task->newLocation->save();
                    }
                    $task->oldLocation->rbt_sku = null;
                    $task->oldLocation->save();
                    if (!empty($task->oldLocation->locationProducts[0]->quantity)) {
                        LocationProduct::where('location_id', $task->oldLocation->id)
                            ->where('product_id', $task->product_id)
                            ->delete();

                        $locationProductRepo->updateQuantityRbt(
                            $newLocationId,
                            $task->product_id,
                            $task->oldLocation->locationProducts[0]->quantity,
                        );
                        Box::where('location_id', $task->oldLocation->id)
                            ->where('product_id', $task->product_id)
                            ->update(['location_id' => $newLocationId]);
                    }

                    break;
                case RbtSkuMovementDetail::ACTION_MOVE_RACK:

                    if (empty($task->old_barcode) || empty($task->new_barcode)) {
                        throw new \Exception('Old barcode and new barcode are required for moving rack.');
                    }

                    $newLocations = Location::where('barcode', 'like', $task->new_barcode . '%')->get();

                    if ($newLocations->isNotEmpty()) {
                        throw new \Exception('New barcode already exists in the system.');
                    }

                    $oldLocations = Location::where('barcode', 'like', $task->old_barcode . '%')->get();
                    if ($oldLocations->isEmpty()) {
                        throw new \Exception('No locations found with the old barcode.');
                    }

                    foreach ($oldLocations as $location) {
                        $formattedOldBarcode = explode('-', $location->barcode);
                        if (count($formattedOldBarcode) != 3) {
                            throw new \Exception("Location {$location->barcode} does not match the expected format");
                        }

                        $location->barcode = "{$task->new_barcode}-{$formattedOldBarcode[1]}-{$formattedOldBarcode[2]}";
                        $location->save();
                    }
                    break;
                case RbtSkuMovementDetail::ACTION_SWAP_RACK:
                    // - lấy tất cả các location có prefix là old_barcode
                    $oldLocations = Location::where('barcode', 'like', $task->old_barcode . '%')->get();
                    //remove $oldLocations để update barcode
                    $newLocations = Location::where('barcode', 'like', $task->new_barcode . '%')->get()->toArray();
                    Location::where('barcode', 'like', $task->new_barcode . '%')->delete();

                    foreach ($oldLocations as $location) {
                        $formattedOldBarcode = explode('-', $location->barcode);
                        if (count($formattedOldBarcode) < 3) {
                            throw new \Exception("Location {$location->barcode} does not match the expected format");
                        }
                        unset($formattedOldBarcode[0]);
                        $barcode = implode('-', $formattedOldBarcode);
                        $location->barcode = "{$task->new_barcode}-{$barcode}";
                        $location->save();
                    }
                    $locations = [];
                    foreach ($newLocations as $location) {
                        $formattedNewBarcode = explode('-', $location['barcode']);
                        if (count($formattedNewBarcode) < 3) {
                            throw new \Exception("Location {$location['barcode']} does not match the expected format");
                        }
                        unset($formattedNewBarcode[0]);
                        $barcode = implode('-', $formattedNewBarcode);
                        $location['barcode'] = "{$task->old_barcode}-{$barcode}";
                        $locations[] = $location;
                    }
                    Location::insert($locations);
                    break;
                case RbtSkuMovementDetail::ACTION_SWAP_SKU:
                    $newLocation = Location::with(['locationProducts', 'product'])->where('barcode', $task->new_barcode)->first();
                    if (!$newLocation) {
                        throw new \Exception('New location not found.');
                    }
                    $oldLocation = Location::with(['locationProducts', 'product'])->where('barcode', $task->old_barcode)->first();
                    if (!$oldLocation) {
                        throw new \Exception('Old location not found.');
                    }
                    $oldSku = $oldLocation->rbt_sku;
                    $oldLocation->rbt_sku = $newLocation->rbt_sku;
                    $oldLocation->save();

                    $newLocation->rbt_sku = $oldSku;
                    $newLocation->save();

//                    $location[] = [
//                        'id' => $newLocation->id,
//                        'rbt_sku' => $oldLocation->rbt_sku,
//                        'type' => $oldLocation->type,
//                        'warehouse_id' => $oldLocation->warehouse_id,
//                        'barcode' => $task->new_barcode,
//                        'rbt_type' => $oldLocation->rbt_type,
//                        'is_deleted' => $oldLocation->is_deleted,
//
//                    ];
//                    $location[] = [
//                        'id' => $oldLocation->id,
//                        'rbt_sku' => $newLocation->rbt_sku,
//                        'type' => $newLocation->type,
//                        'warehouse_id' => $newLocation->warehouse_id,
//                        'barcode' => $task->old_barcode,
//                        'rbt_type' => $newLocation->rbt_type,
//                        'is_deleted' => $newLocation->is_deleted,
//                    ];
                    LocationProduct::whereIn('location_id', [$newLocation->id, $oldLocation->id])->delete();

                    $locationProductRepo->updateQuantityRbt(
                        $newLocation->id,
                        $oldLocation->product->id,
                        $oldLocation->locationProducts->sum('quantity'),
                    );

                    $locationProductRepo->updateQuantityRbt(
                        $oldLocation->id,
                        $newLocation->product->id,
                        $newLocation->locationProducts->sum('quantity'),
                    );

                    break;
                case RbtSkuMovementDetail::ACTION_REMOVE:
                    $pullingShelves = Location::with('locationProducts')
                        ->where('warehouse_id', config('jwt.warehouse_id'))
                        ->where('type', Location::PULLING_SHELVES)
                        ->first();
                    if (!$pullingShelves) {
                        throw new \Exception('Pulling shelves location not found.');
                    }
                    $location = Location::with(['locationProducts', 'product'])
                        ->where('type', Location::MOVING_SHELVES)
                        ->where('is_deleted', Location::NOT_DELETED)
                        ->where('rbt_sku', $task->sku)
                        ->first();
                    if (empty($location)) {
                        throw new \Exception('No locations found with the old barcode.');
                    }
                    $quantity = $location->locationProducts->sum('quantity') ?? 0;

                    Box::where('location_id', $location->id)
                        ->where('product_id', $location->product->id ?? null)
                        ->update(['location_id' => $pullingShelves->id]);
                    // update location product quantity to 0
                    LocationProduct::where('location_id', $location->id)
                        ->where('product_id', $location->product->id ?? null)
                        ->update(['quantity' => 0]);
                    $pullingShelves->locationProducts()->updateOrCreate(
                        ['product_id' => $task->product_id],
                        ['quantity' => DB::raw("quantity + $quantity")],
                    );
                    $location->rbt_sku = null;
                    $location->save();
                    $rbtProduct = RbtProduct::where('product_id', $task->product_id)->first();
                    if ($rbtProduct) {
                        $rbtProduct->is_active = false;
                        $rbtProduct->save();
                    }
                    break;
                default:
                    throw new \Exception('Unknown action type.');
            }

            $task->status = RbtSkuMovementDetail::STATUS_COMPLETED;
            $task->confirm_by = $input['employee_id'] ?? null;
            $task->confirm_at = now();
            $task->save();

            $movement = RbtSkuMovement::find($task->rbt_sku_movement_id);
            $taskNotCompleted = RbtSkuMovementDetail::where('rbt_sku_movement_id', $task->rbt_sku_movement_id)
                ->where('status', '!=', RbtSkuMovementDetail::STATUS_COMPLETED)
                ->exists();

            if (!$taskNotCompleted && $movement) {
                $movement->status = RbtSkuMovement::STATUS_COMPLETED;
                $movement->save();
            }
            DB::commit();

            return $task;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    public function getCount($input)
    {
        $query = RbtSkuMovementDetail::query();

        if (!empty($input['employee_id'])) {
            $query->where('picked_by', $input['employee_id'])
                ->where('status', RbtSkuMovementDetail::STATUS_IN_PROGRESS);
        } else {
            $query->where('status', RbtSkuMovementDetail::STATUS_NEW);
        }

        return $query->selectRaw('count(*) as total, action')->groupBy('action')
            ->pluck('total', 'action')
            ->toArray();
    }

    public function listImports($input)
    {
        $limit = $input['limit'] ?? RbtSkuMovement::LIMIT;
        $keyword = $input['keyword'] ?? null;
        $status = $input['status'] ?? null; // thêm status từ input

        $query = RbtSkuMovement::query();

        if (!empty($keyword)) {
            $query->whereHas('details', function ($q) use ($keyword) {
                $q->where('sku', 'like', "%$keyword%")
                  ->orWhere('old_barcode', 'like', "%$keyword%")
                  ->orWhere('new_barcode', 'like', "%$keyword%");
            });
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        // Lấy dữ liệu phân trang
        $movements = $query->with(['details', 'creator:id,username'])
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        // Xử lý collection items
        $items = collect($movements->items());

        // Đếm tổng status
        $statusCounts = $items
            ->groupBy('status')
            ->map(fn ($group) => $group->count())
            ->toArray();

        // Đếm tổng action
        $actionCounts = $items
            ->flatMap(fn ($movement) => $movement->details->pluck('action'))
            ->countBy()
            ->toArray();

        // Thêm đếm action từng movement
        $itemsWithSummary = $items->map(function ($movement) {
            $actions = $movement->details->groupBy('action');

            $actionStatusSummary = [];

            foreach ($actions as $action => $details) {
                $total = $details->count();
                $completedCount = $details->where('status', 'completed')->count();
                $actionStatusSummary[$action] = "{$completedCount}/{$total}";
            }

            $movement->action_summary = $movement->details
                ->pluck('action')
                ->countBy()
                ->toArray();

            $movement->action_status_summary = $actionStatusSummary;

            return $movement;
        });

        // Trả về kết quả với phân trang đầy đủ
        return [
            'data' => $itemsWithSummary,
            'summary' => [
                'action_counts' => $actionCounts,
                'status_counts' => $statusCounts,
            ],
            'pagination' => [
                'current_page' => $movements->currentPage(),
                'last_page' => $movements->lastPage(),
                'per_page' => $movements->perPage(),
                'total' => $movements->total(),
            ],
        ];
    }

    public function listHistories($input)
    {
        $limit = $input['limit'] ?? RbtSkuMovement::LIMIT;

        return RbtSkuMovementImportHistory::query()
            ->with('creator:id,username')
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
    }
}
