<?php

namespace App\Repositories;

use App\Builder\BarcodeDTFBuilder;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BarcodeDTFRepository
{
    protected BarcodeDTFBuilder $barcodeDTFBuilder;

    public function __construct(BarcodeDTFBuilder $barcodeDTFBuilder)
    {
        $this->barcodeDTFBuilder = $barcodeDTFBuilder;
    }

    public function countDtfByOrderType($params)
    {
        $data = [
            'bulk_order' => $this->countPendingBulkOrder($params),
            'fba' => $this->countPendingFba($params),
            'reroute' => $this->countPendingReroute($params),
            'tiktok' => $this->countPendingTiktok($params),
            'manual' => $this->countPendingManualProcess($params),
            'reprint' => $this->countPendingReprint($params),
            'xqc' => $this->countPendingStyleXQC($params),
            'eps' => $this->countPendingStyleEps($params),
            'styles' => $this->countPendingStyles($params),

        ];
        if (!empty($params['print_method']) && $params['print_method'] === PrintMethod::NECK) {
            $data['priority_stores'] = $this->countPendingStore($params, true);
        }

        return $data;
    }

    public function countPendingBulkOrder($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingBulkOrder($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('bulk_order', $args);

        return $r;
    }

    public function countPendingFba($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingFBA($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('fba', $args);

        return $r;
    }

    public function countPendingReroute($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingReroute($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('reroute', $args);

        return $r;
    }

    public function countPendingTiktok($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingTiktok($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('tiktok', $args);

        return $r;
    }

    public function countPendingManualProcess($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingManual($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('manual', $args);

        return $r;
    }

    public function countPendingReprint($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingReprint($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('reprint', $args);

        return $r;
    }

    public function countPendingStyleXQC($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingXQC($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('xqc', $args);

        return $r;
    }

    public function countPendingStyleEps($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingExpress($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('eps', $args);

        return $r;
    }

    public function countPendingStyles($args)
    {
        $query = $this->barcodeDTFBuilder->selectPendingStyles($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total, product_style.name, product_style.sku')
            ->groupBy('product_style.sku')
            ->orderBy('total', 'DESC');

        $styles = $query->get();
        $total = 0;

        foreach ($styles as $style) {
            $total += $style->total;
            $style->printed_at = $this->getLastPrintedTime('style', $args, $style->sku);
        }

        $r['total'] = $total;
        $r['sql'] = interpolateQuery($query);
        $r['data'] = array_values(collect($styles)->sortBy('printed_at')->toArray());

        return $r;
    }

    public function getLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)->first();
    }

    public function getLastPrintedTime($type, $args, $styleSku = null)
    {
        $types = [
            'is_bulk_order' => null,
            'is_fba' => null,
            'is_reroute' => null,
            'is_tiktok' => null,
            'is_manual' => null,
            'is_reprint' => null,
            'is_xqc' => null,
            'is_eps' => null,
            'style_sku' => null,
        ];

        switch ($type) {
            case 'bulk_order':
                $types['is_bulk_order'] = true;
                break;
            case 'fba':
                $types['is_fba'] = true;
                break;
            case 'reroute':
                $types['is_reroute'] = true;
                break;
            case 'tiktok':
                $types['is_tiktok'] = true;
                break;
            case 'manual':
                $types['is_manual'] = true;
                break;
            case 'reprint':
                $types['is_reprint'] = true;
                break;
            case 'xqc':
                $types['is_xqc'] = true;
                break;
            case 'eps':
                $types['is_eps'] = true;
                break;
            case 'style':
                $types['style_sku'] = $styleSku;
                break;
        }

        $printedTime = BarcodePrintedTime::where('print_method', $args['print_method'])
            ->where('warehouse_id', $args['warehouse_id']);
        foreach ($types as $key => $value) {
            if ($value !== null) {
                $printedTime->where($key, $value);

                continue;
            }

            $printedTime->whereNull($key);
        }

        return $printedTime->first()->printed_at ?? null;
    }

    public function confirmPrintDtf($input)
    {
        $style_sku = $input['style_sku'] ?? null;
        $limit = $input['limit'] ?? 10;
        $employee_id = $input['employee_id'] ?? null;
        $printMethod = $input['print_method'] ?? null;
        $warehouse_id = $input['warehouse_id'] = config('jwt.warehouse_id');
        $store_id = $input['store_id'] ?? null;
        $query = null;
        $args = [
            'print_method' => $printMethod,
            'warehouse_id' => $warehouse_id,
            'store_id' => $store_id,
        ];

        if (!empty($input['is_bulk_order'])) {
            $query = $this->barcodeDTFBuilder->selectPendingBulkOrder($args);
        } elseif (!empty($input['is_fba'])) {
            $query = $this->barcodeDTFBuilder->selectPendingFBA($args);
        } elseif (!empty($input['is_reroute'])) {
            $query = $this->barcodeDTFBuilder->selectPendingReroute($args);
        } elseif (!empty($input['is_tiktok'])) {
            $query = $this->barcodeDTFBuilder->selectPendingTiktok($args);
        } elseif (!empty($input['is_manual'])) {
            $query = $this->barcodeDTFBuilder->selectPendingManual($args);
        } elseif (!empty($input['is_reprint'])) {
            $query = $this->barcodeDTFBuilder->selectPendingReprint($args);
        } elseif (!empty($input['is_xqc'])) {
            $query = $this->barcodeDTFBuilder->selectPendingXQC($args);
        } elseif (!empty($input['is_eps'])) {
            $query = $this->barcodeDTFBuilder->selectPendingExpress($args);
        } elseif (!empty($input['style_sku'])) {
            $query = $this->barcodeDTFBuilder->selectPendingStyles($args);
        }

        if (empty($query)) {
            throw new \Exception('Server Error! Not found query.', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            DB::beginTransaction();

            $barcode_printed = BarcodePrinted::create([
                'employee_id' => $employee_id,
                'warehouse_id' => $warehouse_id,
                'style_sku' => $style_sku,
                'quantity_input' => $limit,
                'is_xqc' => $input['is_xqc'] ?? null,
                'is_eps' => $input['is_eps'] ?? null,
                'is_manual' => $input['is_manual'] ?? null,
                'is_reprint' => $input['is_reprint'] ?? null,
                'is_reroute' => $input['is_reroute'] ?? null,
                'is_fba' => $input['is_fba'] ?? null,
                'is_insert' => $input['is_insert'] ?? null,
                'is_tiktok' => $input['is_tiktok'] ?? null,
                'is_bulk_order' => $input['is_bulk_order'] ?? null,
                'store_id' => $input['store_id'] ?? null,
                'user_id' => Auth::id(),
                'created_at' => Carbon::now(),
                'print_method' => $printMethod,
                'print_status' => BarcodePrinted::INACTIVE,
                'convert_status' => BarcodePrinted::FAILED,
                'convert_percent' => 0,
            ]);

            $query = $query->getQuery();

            if (!empty($input['style_sku'])) {
                $query = $query->where('sale_order_item.product_style_sku', $style_sku);
            }

            $barcode_printed->quantity = $query->whereRaw('1 = 1 LIMIT ?', [$limit])->update([
                'barcode_printed_id' => $barcode_printed->id,
                'employee_pull_id' => $employee_id,
                'print_barcode_at' => Carbon::now()
            ]);
            $barcode_printed->convert_status = BarcodePrinted::INACTIVE;
            $barcode_printed->save();

            resolve(BarcodeRepository::class)->updateLastBarcodePrintedTime($input);
            $order_ids = SaleOrderItemBarcode::getArrayOrderIds($barcode_printed->id);
            SaleOrder::changeStatusToInProduction($order_ids);
            DB::commit();

            return $barcode_printed;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function countPendingStore($args, $isPriorityStore = false, $printMethod = PrintMethod::NECK)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $st = DB::table('store')
            ->select(DB::raw('store.id, store.name, store.code, store.account_id,  COUNT(*) as total'))
            ->join('sale_order', 'store.id', 'sale_order.store_id')
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');

        if (!empty($warehouse_id)) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($isPriorityStore) {
                $st->whereIn('sale_order.store_id', $args['priorityStores']);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        } elseif ($isPriorityStore) {
            return collect([]);
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', $printMethod);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);
        $r['sql'] = interpolateQuery($st);
        $r['data'] = $st->groupBy('sale_order_item_barcode.store_id')
            ->orderBy('total', 'DESC')
            ->get();

        return $r;
    }
}
