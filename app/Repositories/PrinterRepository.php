<?php

namespace App\Repositories;

use App\Models\Printer;
use App\Models\Product;
use App\Models\ProductColor;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class PrinterRepository extends CommonRepository
{
    public function store($input)
    {
        $name = $input['name'];
        $warehouseId = $input['warehouse_id'];
        $deviceId = $input['device_id'];
        $printer = Printer::findByNameAndWarehouse($name, $warehouseId);
        if ($printer) {
            throw new Exception('Printer already exists', Response::HTTP_BAD_REQUEST);
        }

        $printer = Printer::create(['name' => $name, 'warehouse_id' => $warehouseId, 'device_id' => $deviceId]);
        foreach ($input['styles'] as $style) {
            $colorExists = DB::table('printer_product')
                ->join('printer', 'printer.id', '=', 'printer_product.printer_id')
                ->where('printer.warehouse_id', $warehouseId)
                ->where('printer_product.style_sku', $style['sku'])
                ->whereIn('printer_product.color_sku', $style['color_skus'])
                ->pluck('printer_product.color_sku')
                ->toArray();

            $addColors = array_diff($style['color_skus'], $colorExists);
            if (!empty($addColors)) {
                $printer->colors()->attach($addColors, ['style_sku' => $style['sku']]);
            }
        }

        return $printer;
    }

    public function index($input)
    {
        $limit = empty($input['limit']) ? self::LIMIT : $input['limit'];
        return Printer::listPrinter($limit, $input['warehouse_id']);
    }

    public function update($input, $id)
    {
        $printer = Printer::find($id);
        if (!$printer) {
            throw new Exception('Printer not found.', Response::HTTP_NOT_FOUND);
        }
        $printer->name = empty($input['name']) ? $printer->name : $input['name'];
        $printer->device_id = empty($input['device_id']) ? $printer->device_id : $input['device_id'];
        $printer->warehouse_id = empty($input['warehouse_id']) ? $printer->warehouse_id : $input['warehouse_id'];

        // Remove style
        if (!empty($input['styles'])) {
            $oldStyles = $printer->styles()->pluck('printer_product.style_sku')->toArray();
            $newStyles = array_column($input['styles'], 'sku');
            $removeStyles = empty($oldStyles) ? [] : array_diff($oldStyles, $newStyles);
            if (!empty($removeStyles)) {
                $printer->styles()->detach($removeStyles);
            }
        }

        foreach ($input['styles'] as $style) {
            // Insert new style
            if (!$printer->styles()->where('product_style.sku', $style['sku'])->exists()) {
                $colorExists = DB::
//
                table('printer_product')
                    ->join('printer', 'printer.id', '=', 'printer_product.printer_id')
                    ->where('printer.warehouse_id', $printer->warehouse_id)
                    ->where('printer_product.style_sku', $style['sku'])
                    ->whereIn('printer_product.color_sku', $style['color_skus'])
                    ->pluck('printer_product.color_sku')
                    ->toArray();

                $addColors = array_diff($style['color_skus'], $colorExists);
                if (!empty($addColors)) {
                    $printer->colors()->attach($addColors, ['style_sku' => $style['sku']]);
                }
            } else { // Update product style
                $oldColors = DB::
//
                table('printer_product')
                    ->join('printer', 'printer.id', '=', 'printer_product.printer_id')
                    ->where('printer.warehouse_id', $printer->warehouse_id)
                    ->where('printer_product.style_sku', $style['sku'])
                    ->pluck('printer_product.color_sku')
                    ->toArray();

                $removeColors = array_diff($oldColors, $style['color_skus']);
                $addColors = array_diff($style['color_skus'], $oldColors);
                if (!empty($removeColors)) {
                    $printer->colors()->wherePivot('style_sku', $style['sku'])
                        ->wherePivotIn('color_sku', $removeColors)->detach();
                }
                if (!empty($addColors)) {
                    $printer->colors()->attach($addColors, ['style_sku' => $style['sku']]);
                }
            }
        }
        $printer->save();

        return $printer;
    }

    public function destroy($id)
    {
        $printer = Printer::find($id);
        if (!$printer) {
            throw new Exception('Printer not found.', Response::HTTP_NOT_FOUND);
        }
        $printer->styles()->detach();
        $printer->colors()->detach();
        $printer->delete();
    }

    public function getColors($input)
    {
        $styleName = $input['style_name'];
        $productParent = Product::findParentByStyle($styleName);
        if (!$productParent) {
            throw new Exception('Product not found', Response::HTTP_NOT_FOUND);
        }

        return ProductColor::getByNames(explode(',', $productParent->color));
    }
}
