<?php

namespace App\Repositories;

use App\Models\AdjustPullingShelves;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Repositories\Contracts\AdjustPullingShelvesRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class AdjustPullingShelvesRepository extends CommonRepository implements AdjustPullingShelvesRepositoryInterface
{
    const LIMIT = 10;

    public function getList($request, $unlimited = false): Collection|LengthAwarePaginator|array
    {
        $query = AdjustPullingShelves::with(
            [
                'product:id,name',
                'user:id,username',
                'employee:code,name',
                'country:name,iso2'
            ])
            ->where('warehouse_id', $request['warehouse_id']);

        if (isset($request['type'])) {
            if ($request['type'] === 'product_style') {
                $query->whereHas('product', function ($q) use ($request) {
                    if (!empty($request['style'])) {
                        $q->where('style', $request['style']);
                    }

                    if (!empty($request['color'])) {
                        $q->where('color', $request['color']);
                    }

                    if (!empty($request['size'])) {
                        $q->where('size', $request['size']);
                    }
                });
            }

            if ($request['type'] === 'product_sku' && $request['keyword']) {
                $query->where('sku', $request['keyword']);
            }

            if ($request['type'] === 'date' && is_array($request['date']) && $request['date'][0] && $request['date'][1]) {
                $query->whereDate('created_at', '>=', $request['date'][0]);
                $query->whereDate('created_at', '<=', $request['date'][1]);
            }
        }

        if (!empty($request['start_date'])) {
            $startDate = Carbon::parse($request['start_date'])->startOfDay()->format('Y-m-d H:i:s');
            $query->where('adjust_pulling_shelves.created_at', '>=', $startDate);
        }

        if (!empty($request['end_date'])) {
            $endDate = Carbon::parse($request['end_date'])->endOfDay()->format('Y-m-d H:i:s');
            $query->where('adjust_pulling_shelves.created_at', '<=', $endDate);
        }

        $query->latest('id');

        if ($unlimited) {
            return $query->get();
        }

        return $query->paginate($request['limit'] ?? self::LIMIT);
    }

    public function create($request): JsonResponse
    {
        $product = Product::where([
            ['style', '=', $request['product_style']],
            ['color', '=', $request['product_color']],
            ['size', '=', $request['product_size']]
        ])
            ->primary()
            ->active()
            ->first();

        if (!$product) {
            return $this->errorResponse('The product is not found or has been deleted!');
        }

        $pullingShelvesLocation = Location::where('warehouse_id', config('jwt.warehouse_id'))->pullingShelves()->first();

        if (!$pullingShelvesLocation) {
            return $this->errorResponse('Pulling shelves location is not found or has been deleted!');
        }

        $locationId = $pullingShelvesLocation->id;
        $locationProduct = LocationProduct::where([
            'location_id' => $locationId,
            'product_id' => $product->id
        ])->first();
        $availableQuantity = $locationProduct ? $locationProduct->quantity : 0;
        $actualQuantity = $request['quantity'];
        $adjustQuantity = $actualQuantity - $availableQuantity;

        if ($adjustQuantity == 0) {
            return $this->errorResponse('The quantity is matched, so no need to adjust!', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        DB::beginTransaction();

        try {
            $direction = $adjustQuantity > 0 ? Inventory::DIRECTION_INPUT : Inventory::DIRECTION_OUTPUT;
            $purchaseOrderRepository = new PurchaseOrderRepository();
            $price = $purchaseOrderRepository->getLatestProductPriceFromPo($product->id);
            $adjustPullingShelves = AdjustPullingShelves::create([
                'user_id' => auth()->user()['id'],
                'warehouse_id' => config('jwt.warehouse_id'),
                'product_id' => $product->id,
                'sku' => $product->sku,
                'product_available' => $availableQuantity,
                'product_on_hand' => $actualQuantity,
                'product_adjust' => $adjustQuantity,
                'employee_id' => $request['employee_id'],
                'country' => $request['country'],
                'cost_value_on_hand' => $actualQuantity * $price,
                'cost_value_adjusted' => $adjustQuantity * $price,
                'cost_value_available' => $availableQuantity * $price,
            ]);
            Inventory::create([
                'direction' => $direction,
                'type' => Inventory::TYPE_ADJUST,
                'product_id' => $product->id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'location_id' => $locationId,
                'user_id' => auth()->user()['id'],
                'object_id' => $adjustPullingShelves->id,
                'object_name' => Inventory::OBJECT_ADJUST_PULLING_SHELVES,
                'quantity' => abs($adjustQuantity),
                'remaining_qty' => $direction == Inventory::DIRECTION_INPUT ? $adjustQuantity : 0,
                'cost_total' => $direction == Inventory::DIRECTION_INPUT ? $adjustQuantity * $price : 0,
            ]);
            ProductQuantityRepository::updateQuantity(config('jwt.warehouse_id'), $product->id, $adjustQuantity);
            LocationProductRepository::updateQuantity($locationId, $product->id, $adjustQuantity);

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $request['id_time_checking']);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Adjust pulling shelves successfully!', $adjustPullingShelves);
    }
}
