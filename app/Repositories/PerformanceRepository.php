<?php

namespace App\Repositories;

use App\Http\Service\PrintAreaService;
use App\Models\PressLog;
use App\Models\PretreatLog;
use App\Models\PrintingPresetSku;
use App\Models\PrintMethod;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\Shipment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PerformanceRepository extends CommonRepository
{
    protected $printAreaService;

    public function __construct()
    {
        $this->printAreaService = new PrintAreaService();
    }

    public function pretreatSKU($input, $saleOrderItemBarcodeId)
    {
        $dataInsert = [
            'employee_pretreat_id' => $input['employee_id'],
            'pretreated_at' => date('Y-m-d H:i:s')
        ];

        PretreatLog::create([
            'label_id' => $input['label'],
            'employee_id' => $input['employee_id'],
            'created_at' => now(), // Laravel's helper for current date and time
        ]);

        SaleOrderItemBarcode::where('id', $saleOrderItemBarcodeId)
            ->where('is_deleted', 0)
            ->limit(1)
            ->update($dataInsert);
    }

    public function scanPretreat($input)
    {
        $dataItemBarcode = SaleOrderItemBarcode::select(
            'sale_order_item_barcode.id', 'sale_order_item_barcode.order_item_id', 'sale_order_item_barcode.sku',
            'sale_order_item.options', DB::raw('store.code AS store_code'), 'sale_order.order_date', DB::raw('product.sku AS product_sku'),
            'sale_order.order_type', 'sale_order_item.print_sides', 'sale_order_item.product_style_sku', 'sale_order.order_status', 'product_color.color_code',
            'sale_order_item.print_side', 'product.name', 'sale_order_item_barcode.label_id', 'sale_order_item_barcode.order_id', 'sale_order.is_fba_order')
            ->join('sale_order_item', 'sale_order_item.sku', '=', 'sale_order_item_barcode.sku')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('store', 'store.id', '=', 'sale_order_item_barcode.store_id')
            ->join('product_color', 'product_color.name', '=', 'product.color')
            ->leftJoin('printing_preset_sku', 'printing_preset_sku.sku', '=', 'sale_order_item.product_sku')
            ->where('sale_order_item_barcode.label_id', $input['label'])
            ->where('sale_order_item_barcode.is_deleted', '=', 0)
            ->first();

        if (empty($dataItemBarcode)) {
            return [];
        }

        $orderType = !empty($dataItemBarcode) ? $dataItemBarcode->order_type : null;
        $orderStatus = !empty($dataItemBarcode) ? $dataItemBarcode->order_status : null;
        if (empty($dataItemBarcode) || blank($orderType) || $orderType == SaleOrder::ORDER_TYPE_BLANK
            || $orderStatus == SaleOrder::STATUS_CANCELLED || $orderStatus == SaleOrder::STATUS_REJECT
            || $orderStatus == SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
        ) {
            return [];
        }
        if ($orderStatus == SaleOrder::STATUS_ON_HOLD) {
            return ['on_hold' => true];
        }
        $printSides = $dataItemBarcode?->print_sides;
        if (empty($printSides)) {
            return [];
        }

        $dataScan = $this->canScanPretreatPerformance($dataItemBarcode?->product_style_sku, str_split($printSides), $input['label']);
        if (!$dataScan['canScanLabel']) {
            return [];
        }
        $dtgPrintSides = $this->getDtgPrintSide($dataItemBarcode?->product_style_sku, str_split($printSides), $input['label']);
        // Update employee pretreat info
        $this->pretreatSKU($input, $dataItemBarcode->id);

        // Log timeline for label
        saleOrderHistory(auth()->user()->id, $input['employee_id'],
            $dataItemBarcode->order_id, SaleOrderHistory::UPDATE_ORDER_PRETREAT_TYPE,
            'Label ID ' . $input['label'] . ' has been pretreated ' . $dataScan['scanned'] + 1 . '/' . $dataScan['total'] . ' times.',
        );

        // Update production status Job
        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $dataItemBarcode->order_id);
        $sku = $dataItemBarcode->sku;
        $printingPreset = PrintingPresetSku::where('sku', $dataItemBarcode->product_sku)->first();
        $presetArr = !empty($printingPreset) ? $printingPreset->toArray() : [];
        $dataItemBarcode = $this->getImageOption($dataItemBarcode, $sku, $dataItemBarcode->order_date, $presetArr, $dtgPrintSides);
        $dataItemBarcode['pretreated_times'] = $dataScan['scanned'] + 1 ?? 0;
        $dataItemBarcode['total_pretreated_times'] = $dataScan['total'] ?? 0;

        return $dataItemBarcode;
    }

    public function canScanPretreatPerformance($productStyleSku, $printSides, $label)
    {
        $printSideNames = ProductPrintSide::whereIn('code_wip', $printSides)->pluck('name')->toArray();
        $productStyle = ProductStyle::where('sku', $productStyleSku)->first();
        $countValidPretreatScan = ProductPrintArea::whereIn('name', $printSideNames)
            ->where('product_style_id', $productStyle->id)
            ->where('print_method', PrintMethod::DTG)
            ->count();
        $scannedLabel = PretreatLog::where('label_id', $label)->count();

        return [
            'canScanLabel' => $countValidPretreatScan > $scannedLabel,
            'total' => $countValidPretreatScan,
            'scanned' => $scannedLabel
        ];
    }

    public function scanPretreatPerformance($productStyleSku, $printSides, $label)
    {
        $printSideNames = ProductPrintSide::whereIn('code_wip', $printSides)->pluck('name')->toArray();
        $productStyle = ProductStyle::where('sku', $productStyleSku)->first();
        $countValidPretreatScan = ProductPrintArea::whereIn('name', $printSideNames)
            ->where('product_style_id', $productStyle->id)
            ->where('print_method', PrintMethod::DTG)
            ->count();
        $scannedLabel = PretreatLog::where('label_id', $label)->count();

        return [
            'canScanLabel' => $countValidPretreatScan > $scannedLabel,
            'total' => $countValidPretreatScan,
            'scanned' => $scannedLabel
        ];
    }

    public function getDtgPrintSide($productStyleSku, $printSides)
    {
        $printSideNames = ProductPrintSide::whereIn('code_wip', $printSides)->get();
        $productStyle = ProductStyle::where('sku', $productStyleSku)->first();
        $dtgPrintSides = collect();
        foreach ($printSideNames as $printSide) {
            $printAreaDtg = ProductPrintArea::where([
                'name' => $printSide->name,
                'product_style_id' => $productStyle->id,
                'print_method' => PrintMethod::DTG
            ])->first();
            if (!empty($printAreaDtg)) {
                $dtgPrintSides->push($printSide);
            }
        }

        return $dtgPrintSides;
    }

    public function getPrintSide($productStyleSku, $printSides)
    {
        $printSideNames = ProductPrintSide::whereIn('code_wip', $printSides)->get();
        $productStyle = ProductStyle::where('sku', $productStyleSku)->first();
        $dtgPrintSides = collect();
        foreach ($printSideNames as $printSide) {
            $printAreaDtg = ProductPrintArea::where([
                'name' => $printSide->name,
                'product_style_id' => $productStyle->id
            ])->first();
            if (!empty($printAreaDtg)) {
                $dtgPrintSides->push($printSide);
            }
        }

        return $dtgPrintSides;
    }

    public function getImageOption($data, $sku = null, $orderDate = null, $printingPreset = [], $dtgPrintSides = [])
    {
        $data = $data->toArray();
        $dataOptions = collect(json_decode($data['options']));

        $data['dtg_print_sides'] = $this->printAreaService->getPrintSideDTGInfo($dataOptions, $sku, $orderDate, $dtgPrintSides);
        foreach ($data['dtg_print_sides'] as &$side) {
            $platenColumn = 'platen_' . $side['print_side_code'] . '_size';
            $side['platen_side'] = $printingPreset[$platenColumn] ?? null;
        }

        return $data;
    }

    public function getDataTrackingForFolding($input)
    {
        $excludedStatuses = [
            SaleOrder::DRAFT,
            SaleOrder::ON_HOLD,
            SaleOrder::CANCELLED,
            SaleOrder::REJECTED,
            SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
        ];
        $shipmentId10DaysAgo = $this->getShipmentId10DaysAgo();
        $shipmentId30DaysAgo = $this->getShipmentId30DaysAgo();
        $query = Shipment::query()
            ->select([
                'shipment.id', 'shipment.ship_date', 'shipment.order_id', 'shipment.carrier_code',
                'shipment.tracking_number', 'shipment.service_code', 'shipment.refund_status'
            ])
            ->with([
                'saleOrder:id,order_number,store_id,external_number,order_quantity,order_status,order_folding_status,is_fba_order',
                'saleOrder.store:id,name,code',
                'shippingCarrier:id,code,name',
                'shippingCarrierService:id,name,display_name',
                'saleOrder.addressSaleOrder' => function ($q) {
                    $q->select(
                        'sale_order_address.id', 'sale_order_address.name', 'sale_order_address.street1', 'sale_order_address.street2',
                        'sale_order_address.city', 'sale_order_address.state', 'sale_order_address.zip', 'sale_order_address.country',
                        'sale_order_address.order_id', 'sale_order_address.type_address', 'sale_order_address.email', 'sale_order_address.phone')
                        ->firstWhere('type_address', 'to_address');
                },
                'saleOrder.barcodeItems' => function ($q) {
                    $q->select('sale_order_item_barcode.id', 'sale_order_item_barcode.order_id', 'sale_order_item_barcode.order_item_id')
                        ->where('is_deleted', '<>', SaleOrderItemBarcode::IS_DELETED);
                },
                'saleOrder.items.productStyle:sku,type'
            ])
            ->whereRaw('"' . $input['label'] . '" LIKE CONCAT("%", tracking_number, "%")')
            ->whereRaw('LENGTH(tracking_number) > 8')
            ->where('warehouse_id', config('jwt.warehouse_id'));

        // Query 10 days first
        $query10daysFirst = clone $query;
        $shipments10Days = $query10daysFirst->where('id', '>=', $shipmentId10DaysAgo)
            ->whereHas('saleOrder', function ($q) use ($excludedStatuses) {
                $q->whereNotIn('order_status', $excludedStatuses);
            })
            ->get();

        // Filter refund_status null and get max id
        $shipmentData = $shipments10Days->filter(function ($shipment) {
            return $shipment->refund_status === null;
        })->sortByDesc('id')->first();

        if (!empty($shipmentData)) {
            return $shipmentData;
        }

        // Query 30 days second
        $query30daysSecond = clone $query;
        $shipments30Days = $query30daysSecond->where('id', '>=', $shipmentId30DaysAgo)
            ->whereHas('saleOrder', function ($q) use ($excludedStatuses) {
                $q->whereNotIn('order_status', $excludedStatuses);
            })
            ->get();

        $shipmentData30Days = $shipments30Days->filter(function ($shipment) {
            return $shipment->refund_status === null;
        })->sortByDesc('id')->first();

        if (!empty($shipmentData30Days)) {
            return $shipmentData30Days;
        }

        // Query 10 days not excluded
        $query10daysFirstNotExcluded = clone $query;
        $shipmentsNotExcluded = $query10daysFirstNotExcluded->where('id', '>=', $shipmentId10DaysAgo)
            ->get();

        $shipmentDataNotExcluded = $shipmentsNotExcluded->filter(function ($shipment) {
            return $shipment->refund_status === null;
        })->sortByDesc('id')->first();

        if (!empty($shipmentDataNotExcluded)) {
            return $shipmentDataNotExcluded;
        }

        // Query 30 days not excluded
        $query30daysSecondNotExcluded = clone $query;
        $shipments30DaysNotExcluded = $query30daysSecondNotExcluded->where('id', '>=', $shipmentId30DaysAgo)
            ->get();

        return $shipments30DaysNotExcluded->filter(function ($shipment) {
            return $shipment->refund_status === null;
        })->sortByDesc('id')->first();
    }

    public function scanLabel($input)
    {
        $dataItemBarcode = SaleOrderItemBarcode::select(
            'sale_order_item_barcode.id', 'sale_order_item_barcode.order_item_id', 'sale_order_item_barcode.sku',
            'sale_order_item.options', DB::raw('store.code AS store_code'), 'sale_order.order_date', DB::raw('product.sku AS product_sku'),
            'sale_order.order_type', 'sale_order_item.print_sides', 'sale_order_item.product_style_sku', 'sale_order.order_status', 'product_color.color_code',
            'sale_order_item.print_side', 'product.name', 'sale_order_item_barcode.label_id', 'sale_order_item_barcode.order_id', 'sale_order.is_fba_order')
            ->join('sale_order_item', 'sale_order_item.sku', '=', 'sale_order_item_barcode.sku')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('store', 'store.id', '=', 'sale_order_item_barcode.store_id')
            ->join('product_color', 'product_color.name', '=', 'product.color')
            ->leftJoin('printing_preset_sku', 'printing_preset_sku.sku', '=', 'sale_order_item.product_sku')
            ->where('sale_order_item_barcode.label_id', $input['label'])
            ->where('sale_order_item_barcode.is_deleted', '=', 0)
            ->first();

        if (empty($dataItemBarcode)) {
            return [];
        }

        $orderType = !empty($dataItemBarcode) ? $dataItemBarcode->order_type : null;
        $orderStatus = !empty($dataItemBarcode) ? $dataItemBarcode->order_status : null;
        if (empty($dataItemBarcode) || blank($orderType) || $orderType == SaleOrder::ORDER_TYPE_BLANK
            || $orderStatus == SaleOrder::STATUS_CANCELLED || $orderStatus == SaleOrder::STATUS_REJECT
            || $orderStatus == SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
        ) {
            return [];
        }
        if ($orderStatus == SaleOrder::STATUS_ON_HOLD) {
            return ['on_hold' => true];
        }
        $printSides = $dataItemBarcode?->print_sides;
        if (empty($printSides)) {
            return [];
        }
        $codes = str_split($printSides);
        $scannedLabel = PressLog::where('label_id', $input['label'])->count();
        if ($scannedLabel >= count($codes)) {
            return [];
        }
        $productPrintSides = $this->getPrintSide($dataItemBarcode?->product_style_sku, $codes);
        $this->pressSKU($input, $dataItemBarcode->id);

        // Log timeline for label
        saleOrderHistory(auth()->user()->id, $input['employee_id'],
            $dataItemBarcode->order_id, SaleOrderHistory::UPDATE_ORDER_PRESS_TYPE,
            'Label ID ' . $input['label'] . ' has been pressed ' . $scannedLabel + 1 . '/' . count($codes) . ' times.',
        );

        // Update production status Job
        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $dataItemBarcode->order_id);
        $sku = $dataItemBarcode->sku;
        $printingPreset = PrintingPresetSku::where('sku', $dataItemBarcode->product_sku)->first();
        $presetArr = !empty($printingPreset) ? $printingPreset->toArray() : [];
        $dataItemBarcode = $this->getImageOption($dataItemBarcode, $sku, $dataItemBarcode->order_date, $presetArr, $productPrintSides);
        $dataItemBarcode['product_print_sides'] = $dataItemBarcode['dtg_print_sides'];

        $dataItemBarcode['pressed_times'] = $scannedLabel + 1 ?? 0;
        $dataItemBarcode['total_pressed_times'] = count($codes) ?? 0;

        return $dataItemBarcode;
    }

    public function pressSKU($input, $saleOrderItemBarcodeId)
    {
        $dataInsert = [
            'employee_press_id' => $input['employee_id'],
            'pressed_at' => date('Y-m-d H:i:s')
        ];

        PressLog::create([
            'label_id' => $input['label'],
            'employee_id' => $input['employee_id']
        ]);

        SaleOrderItemBarcode::find($saleOrderItemBarcodeId)
            ->update($dataInsert);
    }

    public function getShipmentId10DaysAgo()
    {
        // cache shipment ID 10 days ago
        $cacheKey = 'shipment_id_10_days_ago';
        if (!Cache::has($cacheKey)) {
            // get first shipment ID 10 days ago
            $shipmentId = Shipment::query()
                ->select('id')
                ->where('created_at', '>', now()->subDays(10))
                ->first()
                ->id;
            // set cache
            Cache::put($cacheKey, $shipmentId ?? 0, now()->addDays(1));

            return $shipmentId;
        }

        return Cache::get($cacheKey);
    }

    public function getShipmentId30DaysAgo()
    {
        // cache shipment ID 30 days ago
        $cacheKey = 'shipment_id_30_days_ago';
        if (!Cache::has($cacheKey)) {
            // get first shipment ID 30 days ago
            $shipmentId = Shipment::query()
                ->select('id')
                ->where('created_at', '>', now()->subDays(30))
                ->first()
                ->id;
            // set cache
            Cache::put($cacheKey, $shipmentId ?? 0, now()->addDays(1));

            return $shipmentId;
        }

        return Cache::get($cacheKey);
    }
}
