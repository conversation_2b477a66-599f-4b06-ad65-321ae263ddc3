<?php

namespace App\Repositories;

use App\Jobs\InternalRequestTimeoutJob;
use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Department;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\InternalRequestHistory;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\ProductTiktok;
use App\Models\RbtCountSticker;
use App\Models\RbtProduct;
use App\Models\TimeTracking;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class InternalRequestRepository extends CommonRepository
{
    public function getList($request)
    {
        $hasFilter = false;

        if (!empty($request['tab_name']) && empty($request['status'])) {
            $historyStatus = [InternalRequest::REJECTED_STATUS, InternalRequest::UNCHECK_STATUS, InternalRequest::CHECKED_STATUS];
            $requestStatus = [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS];
            $request['status'] = $request['tab_name'] == 'history' ? $historyStatus : $requestStatus;
        }

        $internalRequest = InternalRequest::with([
            'countSticker',
            'product.rbtLocation',
            'box',
            'employeeCreate',
            'employeeReceive',
            'employeeFulfill',
            'employeeConfirm',
            'employeeReject',
            'newBox.employee'

        ])
            ->search($request)
            ->where('is_deleted', 0);

        if (!empty($request['tab_name']) && $request['tab_name'] == 'history') {
            if (!empty($request['tab']) && $request['tab'] == 'restricted') {
                $internalRequest = $internalRequest->where('is_rbt', '=', InternalRequest::IS_RBT);
                if (!empty($request['replenishment_status'])) {
                    if ($request['replenishment_status'] == 'return_needed') {
                        $internalRequest = $internalRequest->where('status', InternalRequest::CHECKED_STATUS)
                            ->whereHas('box', function ($q) {
                                $q->whereColumn('box.quantity', '<>', 'internal_request.dark_pod_quantity');
                            })
                            ->whereNull('internal_request.new_box_id');
                    } elseif ($request['replenishment_status'] == 'returned') {
                        $internalRequest = $internalRequest->where('status', InternalRequest::CHECKED_STATUS)
                            ->whereHas('box', function ($q) {
                                $q->whereColumn('box.quantity', '<>', 'internal_request.dark_pod_quantity');
                            })
                            ->whereNotNull('internal_request.new_box_id');
                    } elseif ($request['replenishment_status'] == 'fully_fulfilled') {
                        $internalRequest = $internalRequest->where('status', InternalRequest::CHECKED_STATUS)
                            ->whereHas('box', function ($q) {
                                $q->whereColumn('box.quantity', '=', 'internal_request.dark_pod_quantity');
                            });
                    } elseif ($request['replenishment_status'] == 'unconfirmed') {
                        $internalRequest = $internalRequest->where('status', InternalRequest::UNCHECK_STATUS);
                    }
                }
            } else {
                $internalRequest = $internalRequest->where('is_rbt', '!=', InternalRequest::IS_RBT);
            }
            $internalRequest = $internalRequest->with(['boxMoving.preLocation', 'boxMoving.location'])
                ->select('*', DB::raw('IF(status = "uncheck", 2, 1) as history_order'))
                ->orderBy('history_order', 'DESC')
                ->orderBy('created_at', 'DESC');
        } else {
            $this->sortColumn = !empty($request['sort_column']) ? $request['sort_column'] : 'priority';
            $internalRequest = $internalRequest->select('*',
                DB::raw('IF(priority = 2, 3, IF(is_rbt = ' . InternalRequest::IS_RBT . ', 2, priority)) as priority'))
                ->orderBy($this->sortColumn, $this->sortBy)
                ->orderBy('id', 'ASC');

            if (!empty($request['request_type'])) {
                if ($request['request_type'] == 'tiktok') {
                    $internalRequest->where('priority', 2);
                } elseif ($request['request_type'] == 'restricted') {
                    $internalRequest->where('is_rbt', InternalRequest::IS_RBT);
                } elseif ($request['request_type'] == 'priority') {
                    $internalRequest->where(function ($q) {
                        $q->where('priority', 1)
                            ->where('is_rbt', '<>', InternalRequest::IS_RBT);
                    });
                } elseif ($request['request_type'] == 'normal') {
                    $internalRequest->where(function ($q) {
                        $q->whereNotIn('priority', [1, 2])
                            ->where('is_rbt', '<>', InternalRequest::IS_RBT);
                    });
                }
            }
        }

        if (empty($request['not_paginate'])) {
            $internalRequest = $internalRequest->paginate($this->limit);
        } else {
            $internalRequest = $internalRequest->get();
        }

        $productIds = $internalRequest->pluck('product_id')->unique();
        $locationBoxesQuery = Location::join('box', 'box.location_id', '=', 'location.id')
            ->whereIn('box.product_id', $productIds)
            ->where('box.is_deleted', BOX::NOT_DELETED)
            ->where('box.quantity', '>', 0)
            ->where('location.type', Location::RACK)
            ->where('location.is_deleted', Location::NOT_DELETED)
            ->select('box.id', 'box.location_id', 'location.barcode', 'box.product_id', 'location.warehouse_id', DB::raw('IF(LEFT(`location`.`barcode`, 1) < "A" OR LEFT(`location`.`barcode`, 1) > "Z" OR LEFT(`location`.`barcode`, 1) != MID(`location`.`barcode`, 2, 1), 1, 0) as is_other_lane'))
            ->orderBy('is_other_lane', 'ASC')
            ->orderBy('location.barcode');

        if (!empty($request['location'])) {
            $lane = substr(strtoupper($request['location']), 0, 2);

            if ($lane[0] != $lane[1] || $lane[0] < 'A' || $lane[0] > 'Z') {
                $locationBoxesQuery->where(function ($q) {
                    $q->whereRaw('LEFT(`location`.`barcode`, 1) != MID(`location`.`barcode`, 2, 1)');
                    $q->orWhereRaw("LEFT(`location`.`barcode`, 1) < 'A'");
                    $q->orWhereRaw("LEFT(`location`.`barcode`, 1) > 'Z'");
                });
            } else {
                $locationBoxesQuery->where('location.barcode', 'like', $lane . '%');
            }
        }

        $locationBoxes = $locationBoxesQuery->get();
        $productQuantities = ProductQuantity::whereIn('product_id', $productIds)
            ->where('incoming_stock', '>', 0)
            ->get();

        foreach ($internalRequest as $item) {
            $locationBarcodeOfItem = [];
            $boxesOfItem = [];
            $stockLevel = 'OOS';

            foreach ($locationBoxes as $subItem) {
                if ($subItem->product_id == $item->product_id && $subItem->warehouse_id == $item->warehouse_id) {
                    $boxesOfItem[] = $subItem;

                    if (!in_array($subItem->barcode, $locationBarcodeOfItem) && count($locationBarcodeOfItem) < 5) {
                        $locationBarcodeOfItem[] = $subItem->barcode;
                    }

                    if (empty($subItem->suggested) && empty($item->location_suggest)) {
                        $item->location_suggest = $subItem->barcode;
                        $subItem->suggested = true;
                    }
                }
            }

            foreach ($productQuantities as $pItem) {
                if ($pItem->product_id == $item->product_id && $pItem->warehouse_id == $item->warehouse_id) {
                    $stockLevel = 'Incoming';
                    break;
                }
            }

            $item->locations = $locationBarcodeOfItem;
            $item->stock_level = count($boxesOfItem) > 0 ? 'In stock' : $stockLevel;
            $item->id_pad = str_pad($item->id, 4, '0', STR_PAD_LEFT);

            if ($item->status == InternalRequest::PICKING_UP_STATUS) {
                $item->expried_at = Carbon::parse($item->received_at)->addMinutes(InternalRequest::PICKING_UP_TIMEOUT)->toDateTimeString();
            }

            $convertTypes = $this->checkConvertRequestType($item);
            $item['can_convert_to_tiktok'] = $convertTypes['can_convert_to_tiktok'] ?? false;
            $item['can_convert_to_priority'] = $convertTypes['can_convert_to_priority'] ?? false;
        }

        if (!empty($request['exclude_stock_level'])) {
            $internalRequest = $internalRequest->filter(function ($item) use ($request) {
                return !in_array($item->stock_level, $request['exclude_stock_level']);
            });
            $hasFilter = true;
        }

        if (!empty($request['is_suggest_location'])) {
            $internalRequest = $internalRequest->filter(function ($item) {
                return !empty($item->location_suggest);
            });
            $hasFilter = true;
        }

        if ($hasFilter) {
            $internalRequest = $internalRequest->values();
        }

        if (!empty($request['count'])) {
            return ['total' => count($internalRequest)];
        }

        return $internalRequest;
    }

    public function getListRBT($request)
    {
        $status = [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS, InternalRequest::UNCHECK_STATUS];

        if (!empty($request['status']) && is_array($request['status'])) {
            $status = $request['status'];
        }

        $query = InternalRequest::with(['countSticker'])
            ->selectRaw('
                internal_request.id,
                product.sku,
                product.style,
                product.color,
                product.size,
                internal_request.status,
                box.barcode as box_id,
                "" as stock_level,
                internal_request.created_at,
                internal_request.updated_at,
                internal_request.fulfilled_at,
                internal_request.confirmed_at,
                internal_request.cancelled_at,
                internal_request.product_id,
                internal_request.warehouse_id
            ')
            ->leftJoin('product', 'product.id', '=', 'internal_request.product_id')
            ->leftJoin('box', 'box.id', '=', 'internal_request.box_id')
            ->where('internal_request.is_rbt', InternalRequest::IS_RBT)
            ->where('internal_request.priority', '<>', InternalRequest::TIKTOK_PRIORITY)
            ->where('internal_request.is_deleted', InternalRequest::NOT_DELETED)
            ->whereIn('internal_request.status', $status)
            ->orderBy('internal_request.id', 'ASC');

        if (!empty($request['sku'])) {
            $query->where('product.sku', $request['sku']);
        }

        $data = $query->paginate(min($request['limit'] ?? 25, 50));
        $productIds = collect($data->items())
            ->pluck('product_id')
            ->unique();
        $locationBoxes = Location::query()
            ->join('box', 'box.location_id', '=', 'location.id')
            ->whereIn('box.product_id', $productIds)
            ->where('box.is_deleted', BOX::NOT_DELETED)
            ->where('box.quantity', '>', 0)
            ->where('location.type', Location::RACK)
            ->where('location.is_deleted', Location::NOT_DELETED)
            ->select(
                'box.id',
                'box.location_id',
                'location.barcode',
                'box.product_id',
                'location.warehouse_id',
                DB::raw('IF(LEFT(`location`.`barcode`, 1) < "A" OR LEFT(`location`.`barcode`, 1) > "Z" OR LEFT(`location`.`barcode`, 1) != MID(`location`.`barcode`, 2, 1), 1, 0) as is_other_lane'),
            )
            ->orderBy('is_other_lane', 'ASC')
            ->orderBy('location.barcode')
            ->get();
        $productQuantities = ProductQuantity::query()
            ->whereIn('product_id', $productIds)
            ->where('incoming_stock', '>', 0)
            ->get();

        foreach ($data as $item) {
            $boxesOfItem = $locationBoxes->where('product_id', $item->product_id)
                ->where('warehouse_id', $item->warehouse_id)
                ->count();

            if ($boxesOfItem > 0) {
                $item->stock_level = 'In stock';
            } else {
                $stockLevel = 'OOS';
                $pItem = $productQuantities->where('product_id', $item->product_id)
                    ->where('warehouse_id', $item->warehouse_id)
                    ->first();

                if (!empty($pItem)) {
                    $stockLevel = 'Incoming';
                }

                $item->stock_level = $stockLevel;
            }

            unset($item->product_id);
            unset($item->warehouse_id);
        }

        return $data;
    }

    public function checkValidTypeBySku($input)
    {
        $product = Product::where([
            'style' => $input['style'],
            'color' => $input['color'],
            'size' => $input['size'],
        ])
            ->where('parent_id', '!=', 0)
            ->first();

        if (empty($product)) {
            return $this->handleFail('Product not found.');
        }

        $flagCreateTikTok = true;
        $flagCreatePriority = true;
        $productTikTok = ProductTiktok::where([
            'product_id' => $product->id,
            'is_active' => true
        ])->first();

        // Check Create TikTok
        if (empty($productTikTok)) {
            $flagCreateTikTok = false;
        }

        return [
            'flag_create_tiktok' => $flagCreateTikTok,
            'flag_create_priority' => $flagCreatePriority
        ];
    }

    public function checkConvertRequestType($internalRequestChecking)
    {
        $product = Product::where([
            'id' => $internalRequestChecking->product_id
        ])->first();

        if (empty($product)) {
            return $this->handleFail('Product not found.');
        }

        if ($internalRequestChecking->priority != InternalRequest::LOW_PRIORITY) {
            return [
                'can_convert_to_tiktok' => false,
                'can_convert_to_priority' => false
            ];
        }

        $canConvertToTikTok = true;
        $canConvertToPriority = true;
        $productTikTok = ProductTiktok::where([
            'product_id' => $product->id,
            'is_active' => true
        ])->first();

        // Check Convert To TikTok
        if (empty($productTikTok)) {
            $canConvertToTikTok = false;
        }

        return [
            'can_convert_to_tiktok' => $canConvertToTikTok,
            'can_convert_to_priority' => $canConvertToPriority
        ];
    }

    public function getCurrentPickingUp($employee_id, $request)
    {
        $internalRequest = InternalRequest::with([
            'product', 'box', 'employeeCreate', 'employeeReceive', 'product.rbtLocation'
        ])
            ->where('status', InternalRequest::PICKING_UP_STATUS)
            ->where('employee_receive_id', $employee_id)
            ->first();

        if (!empty($internalRequest)) {
            $received_at = shiftTimezoneToUTC($internalRequest->received_at);
            $now = Carbon::now();

            if ($received_at->addMinutes(InternalRequest::PICKING_UP_TIMEOUT) <= $now) {
                $this->setTimeout($internalRequest);

                return null;
            }

            $locations = Location::join('box', 'box.location_id', '=', 'location.id')
                ->where('box.product_id', $internalRequest->product_id)
                ->where('box.is_deleted', Box::NOT_DELETED)
                ->where('box.quantity', '>', 0)
                ->where('location.type', Location::RACK)
                ->where('location.is_deleted', Location::NOT_DELETED)
                ->where('location.warehouse_id', $internalRequest->warehouse_id)
                ->select('location.id', 'location.barcode', DB::raw('IF(LEFT(`location`.`barcode`, 1) < "A" OR LEFT(`location`.`barcode`, 1) > "Z" OR LEFT(`location`.`barcode`, 1) != MID(`location`.`barcode`, 2, 1), 1, 0) as is_other_lane'))
                ->orderBy('is_other_lane', 'ASC')
                ->orderBy('location.barcode')
                ->distinct()
                ->get();
            $productQuantity = ProductQuantity::where('product_id', $internalRequest->product_id)
                ->where('warehouse_id', $internalRequest->warehouse_id)
                ->where('incoming_stock', '>', 0)
                ->first();
            $countLocation = count($locations);
            $locationSuggests = [];

            foreach ($locations as $location) {
                if (!empty($request['location'])) {
                    $lane = substr(strtoupper($request['location']), 0, 2);
                    $location->barcode = strtoupper($location->barcode);

                    if ($lane[0] != $lane[1] || $lane[0] < 'A' || $lane[0] > 'Z') {
                        if (!$location->is_other_lane) {
                            continue;
                        }
                    } else {
                        if (strpos($location->barcode, $lane) !== 0) {
                            continue;
                        }
                    }
                }

                if (!in_array($location->barcode, $locationSuggests) && count($locationSuggests) < 5) {
                    $locationSuggests[] = $location->barcode;
                }
            }

            $internalRequest->locations = $locationSuggests;
            $internalRequest->stock_level = $countLocation > 0 ? 'In stock' : (!empty($productQuantity) ? 'Incoming' : 'OOS');
            $internalRequest->expried_at = Carbon::parse($internalRequest->received_at)->addMinutes(InternalRequest::PICKING_UP_TIMEOUT)->toDateTimeString();
        }

        return $internalRequest;
    }

    public function updatePriority($id, $priorityType)
    {
        $internalRequest = InternalRequest::find($id);

        if (!$internalRequest) {
            return $this->handleFail('Internal request not found.');
        }

        $convertTypes = $this->checkConvertRequestType($internalRequest);

        if ($priorityType == InternalRequest::HIGH_PRIORITY && !$convertTypes['can_convert_to_priority']) {
            return $this->handleFail('High priority request with this SKU exists.');
        }

        if ($priorityType == InternalRequest::TIKTOK_PRIORITY && !$convertTypes['can_convert_to_tiktok']) {
            return $this->handleFail('Can not convert this request to TikTok, please check again.');
        }

        $internalRequest->priority = $priorityType;
        $internalRequest->save();
        internalRequestEmit(InternalRequest::EVENT_UPDATE_PRIORITY, $internalRequest->warehouse_id);

        return $this->handleSuccess('Update successfully.', $internalRequest);
    }

    public function receiveRequest($id, $input)
    {
        $internalRequest = InternalRequest::find($id);

        if (!$internalRequest) {
            return $this->handleFail('Internal request not found.');
        }

        if ($internalRequest->status != InternalRequest::NEW_STATUS) {
            return $this->handleFail('This request has been received already.');
        }

        $employee = Employee::where('id', $input['employee_id'])
            ->where('is_deleted', Employee::NOT_DELETED)
            ->first();

        if (!$employee) {
            return $this->handleFail('Employee is not found or deleted.');
        }

        if ($employee->warehouse_id != $internalRequest->warehouse_id) {
            return $this->handleFail('You cannot pick up this request as it belongs to another warehouse.');
        }

        $isExistRequest = InternalRequest::where('employee_receive_id', $input['employee_id'])
            ->where('status', InternalRequest::PICKING_UP_STATUS)
            ->exists();

        if ($isExistRequest) {
            return $this->handleFail('You can only work on one request at a time.');
        }

        DB::beginTransaction();

        try {
            $internalRequest->status = InternalRequest::PICKING_UP_STATUS;
            $internalRequest->employee_receive_id = $input['employee_id'];
            $internalRequest->received_at = now();
            $internalRequest->save();
            $this->logInternalRequestHistory($input['employee_id'], $internalRequest->id, InternalRequestHistory::ACTION_RECEIVE);
            InternalRequestTimeoutJob::dispatch($internalRequest->id)
                ->onQueue(InternalRequest::JOB_INTERNAL_REQUEST_TIMEOUT)
                ->delay(Carbon::now()->addMinutes(InternalRequest::PICKING_UP_TIMEOUT));
            internalRequestEmit(InternalRequest::EVENT_RECEIVE, $internalRequest->warehouse_id);
            DB::commit();

            return $this->handleSuccess('Request received successfully.');
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.receiveRequest Exception', [
                '$exception' => $exception,
                '$internalRequest' => $internalRequest,
                '$employee' => $employee,
            ]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function fulfillRequest($id, $input)
    {
        $internalRequest = InternalRequest::find($id);

        if (!$internalRequest) {
            return $this->handleFail('Internal request not found.');
        }

        if ($internalRequest->status != InternalRequest::PICKING_UP_STATUS) {
            return $this->handleFail('Only "Picking Up" request can be fulfilled.');
        }

        if ($internalRequest->employee_receive_id != $input['employee_id']) {
            return $this->handleFail('This request was picked up by another employee.');
        }

        $employee = Employee::where('id', $input['employee_id'])
            ->where('is_deleted', Employee::NOT_DELETED)
            ->first();

        if (!$employee) {
            return $this->handleFail('Employee is not found or deleted.');
        }

        if ($employee->warehouse_id != $internalRequest->warehouse_id) {
            return $this->handleFail('You cannot fulfill this request as it belongs to a another warehouse.');
        }

        $box = Box::where('barcode', $input['barcode'])
            ->where('warehouse_id', $employee->warehouse_id)
            ->where('is_deleted', Box::NOT_DELETED);

        if (!(clone $box)->first()) {
            return $this->handleFail('Box is not found or deleted.');
        } elseif ((clone $box)->first()->product_id != $internalRequest->product_id) {
            return $this->handleFail('Error. Product SKU in Box ID does not match the requested SKU.');
        }

        $boxRack = (clone $box)->whereHas('location', function ($q) {
            $q->where('type', '!=', Location::RACK);
        })->first();

        if ($boxRack) {
            return $this->handleFail("This box is moving to pulling shelves. You can't move it to another location.");
        }
        DB::beginTransaction();

        if (!empty($internalRequest->is_rbt)) {
            if (empty($input['count_sticker'])) {
                return $this->handleFail('Count sticker is required for RBT request.');
            }
            $countSticker = RbtCountSticker::where('barcode', $input['count_sticker'])
                ->first();
            $isUsedCountSticker = InternalRequest::where('count_sticker_id', $countSticker->id)
                ->first();
            if ($isUsedCountSticker) {
                return $this->handleFail('This count sticker has been used for another request.');
            }
            $internalRequest->count_sticker_id = $countSticker->id;
        }

        try {
            $inputBoxMoving = [
                'barcode' => $input['barcode'],
                'location_type' => empty($internalRequest->is_rbt) ? Location::PENDING_TYPE : Location::REPLENISHMENT_TYPE,
                'location_barcode' => '',
                'employee_id' => (string) $employee->code ?? null,
                'is_internal_request' => true,
                'warehouse_id' => $employee->warehouse_id,
            ];
            $boxMovingRepository = resolve(BoxMovingRepository::class);
            $result = $boxMovingRepository->create($inputBoxMoving);

            if ($result->getStatusCode() == 201) {
                $internalRequest->status = InternalRequest::UNCHECK_STATUS;
                $internalRequest->employee_fulfill_id = $input['employee_id'];
                $internalRequest->fulfilled_at = now();
                $internalRequest->box_id = $box->first()->id;
                $internalRequest->save();
                $this->logInternalRequestHistory($input['employee_id'], $internalRequest->id, InternalRequestHistory::ACTION_FULFILL);
            } else {
                $res = json_decode($result->getContent(), true);
                $errors = array_values($res);
                DB::rollBack();

                return $this->handleFail($errors[0][0] ?? 'Create box moving fail.');
            }

            internalRequestEmit(InternalRequest::EVENT_FULFILL, $internalRequest->warehouse_id);
            DB::commit();

            return $this->handleSuccess('Request fulfilled successfully.');
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.fulfillRequest Exception', [
                '$exception' => $exception,
                '$internalRequest' => $internalRequest,
                '$employee' => $employee,
            ]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function confirm($id, $input)
    {
        $internalRequest = InternalRequest::find($id);

        if (!$internalRequest) {
            return $this->handleFail('Internal request not found.');
        }

        $employee = Employee::where('id', $input['employee_id'])
            ->where('is_deleted', Employee::NOT_DELETED)
            ->first();

        if (!$employee) {
            return $this->handleFail('Employee is not found or deleted.');
        }

        $box = Box::where('barcode', $input['box_id'])
            ->where('warehouse_id', $internalRequest->warehouse_id)
            ->first();

        if (!$box) {
            return $this->handleFail('Box is not found or deleted.');
        }

        if ($box->id != $internalRequest->box_id) {
            return $this->handleFail('Invalid barcode. Please try again.');
        }

        if ($internalRequest->status != InternalRequest::UNCHECK_STATUS) {
            return $this->handleFail('Only "Uncheck" request can be confirmed by Pulling team.');
        }

        DB::beginTransaction();

        try {
            $box = Box::with('location')->find($internalRequest->box_id);

            if (!empty($box->location->type) && $box->location->type == Location::PULLING_SHELVES) {
                $boxMoving = BoxMoving::where('box_id', $box->id)
                    ->where('location_id', $box->location->id)
                    ->first();
                LocationProductRepository::updateQuantity($box->location->id, $boxMoving->product_id, $boxMoving->quantity);
                $internalRequest->status = InternalRequest::CHECKED_STATUS;
                $internalRequest->employee_confirm_id = $input['employee_id'];
                $internalRequest->confirmed_at = now();
                $internalRequest->save();
                $this->logInternalRequestHistory($input['employee_id'], $internalRequest->id, InternalRequestHistory::ACTION_CONFIRM);
            } elseif (!empty($box->location->type) && ($box->location->type == Location::PENDING_TYPE)) {
                $inputBoxMoving = [
                    'barcode' => $box->barcode,
                    'location_type' => Location::PULLING_SHELVES,
                    'location_barcode' => '',
                    'employee_id' => (string) $employee->code ?? null,
                    'is_internal_request' => true,
                    'warehouse_id' => $employee->warehouse_id,
                ];
                $boxMovingRepository = resolve(BoxMovingRepository::class);
                $result = $boxMovingRepository->create($inputBoxMoving);

                if ($result->getStatusCode() == Response::HTTP_CREATED) {
                    $internalRequest->status = InternalRequest::CHECKED_STATUS;
                    $internalRequest->employee_confirm_id = $input['employee_id'];
                    $internalRequest->confirmed_at = now();
                    $internalRequest->save();
                    $this->logInternalRequestHistory($input['employee_id'], $internalRequest->id, InternalRequestHistory::ACTION_CONFIRM);
                } else {
                    $res = json_decode($result->getContent(), true);
                    $errors = array_values($res);
                    DB::rollBack();

                    return $this->handleFail($errors[0][0] ?? 'Create box moving fail.');
                }
            } else {
                DB::rollBack();

                return $this->handleFail('Create box moving fail.');
            }

            internalRequestEmit(InternalRequest::EVENT_CONFIRM, $internalRequest->warehouse_id);
            $box->is_deleted = true;
            $box->save();
            DB::commit();

            return $this->handleSuccess('Request confirmed successfully.', $internalRequest);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.confirm Exception', [
                '$exception' => $exception,
                '$internalRequest' => $internalRequest,
                '$employee' => $employee,
            ]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function getCount($input)
    {
        $query = InternalRequest::where('is_deleted', InternalRequest::NOT_DELETED);

        if (!empty($input['warehouse_id'])) {
            $query->where('warehouse_id', $input['warehouse_id']);
        }

        $queryCountAllType = $query->clone();
        $data = $queryCountAllType->selectRaw('IF(priority = 2, 3, IF(is_rbt = ' . InternalRequest::IS_RBT . ', 2, priority)) as priority')
            ->whereIn('status', [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS])
            ->pluck('priority');
        $overview = [
            'all' => 0,
            'tiktok' => 0,
            'restricted' => 0,
            'priority' => 0,
            'normal' => 0,
        ];

        foreach ($data as $item) {
            $overview['all']++;

            if ($item == 3) {
                $overview['tiktok']++;
            } elseif ($item == 2) {
                $overview['restricted']++;
            } elseif ($item == 1) {
                $overview['priority']++;
            } else {
                $overview['normal']++;
            }
        }

        if (!empty($input['request_type'])) {
            if ($input['request_type'] == 'tiktok') {
                $query->where('priority', 2);
            } elseif ($input['request_type'] == 'restricted') {
                $query->where('is_rbt', InternalRequest::IS_RBT);
            } elseif ($input['request_type'] == 'priority') {
                $query->where(function ($q) {
                    $q->where('priority', 1)
                        ->where(function ($qEmployeeCreate) {
                            $qEmployeeCreate->where('is_rbt', '<>', InternalRequest::IS_RBT);
                        });
                });
            } elseif ($input['request_type'] == 'normal') {
                $query->where(function ($q) {
                    $q->whereNotIn('priority', [1, 2])
                        ->where(function ($qEmployeeCreate) {
                            $qEmployeeCreate->where('is_rbt', '<>', InternalRequest::IS_RBT);
                        });
                });
            }
        }

        $total = $query->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();

        return array_merge([
            InternalRequest::NEW_STATUS => 0,
            InternalRequest::PICKING_UP_STATUS => 0,
            InternalRequest::UNCHECK_STATUS => 0,
            InternalRequest::CHECKED_STATUS => 0,
            InternalRequest::REJECTED_STATUS => 0,
        ], $total, $overview);
    }

    public function checkExistRequest($input)
    {
        $internalRequest = InternalRequest::where('product_id', $input['product_id'] ?? '')
            ->where('status', InternalRequest::PICKING_UP_STATUS)
            ->where('warehouse_id', $input['warehouse_id'])
            ->first();

        if ($internalRequest) {
            return [
                'message' => 'This SKU has already been requested. Do you want to create a new request for it?<br>SKU này đã được yêu cầu trước đó. Bạn có muốn tạo một yêu cầu mới không?',
                'status' => true
            ];
        }

        return [
            'status' => false
        ];
    }

    public function prepareData()
    {
        $internalRequest = InternalRequest::query()->get();
        $inputEmployeeInternalRequest = [];
        $inputBoxInternalRequest = [];

        foreach ($internalRequest as $item) {
            if (!empty($item->request_employee_id)) {
                $inputEmployeeInternalRequest[] = [
                    'employee_id' => $item->request_employee_id,
                    'internal_request_id' => $item->id,
                    'type' => 'create',
                ];
            }

            if (!empty($item->completed_employee_id)) {
                $inputEmployeeInternalRequest[] = [
                    'employee_id' => $item->completed_employee_id,
                    'internal_request_id' => $item->id,
                    'type' => 'fulfill',
                ];
                $inputEmployeeInternalRequest[] = [
                    'employee_id' => $item->completed_employee_id,
                    'internal_request_id' => $item->id,
                    'type' => 'receive',
                ];
                $inputEmployeeInternalRequest[] = [
                    'employee_id' => $item->completed_employee_id,
                    'internal_request_id' => $item->id,
                    'type' => 'confirm',
                ];
            }

            if (!empty($item->box_id)) {
                $inputBoxInternalRequest[] = [
                    'box_id' => $item->box_id,
                    'internal_request_id' => $item->id,
                ];
            }
        }

        DB::table('employee_internal_request')->insert($inputEmployeeInternalRequest);
        DB::table('box_internal_request')->insert($inputBoxInternalRequest);
        InternalRequest::where('status', 'pending')->update([
            'status' => 'new'
        ]);
        InternalRequest::where('status', 'completed')->update([
            'status' => 'checked'
        ]);
    }

    public function getCountStatus($input)
    {
        $total = InternalRequest::where('warehouse_id', $input['warehouse_id'])
            ->where('status', $input['status'])
            ->count();

        return ['total' => $total];
    }

    public function create($input)
    {
        DB::beginTransaction();

        try {
            $employee = Employee::find($input['employee_id']);
            $input['warehouse_id'] = $employee->warehouse_id;

            $isRbtProduct = RbtProduct::where('product_id', $input['product_id'])
                ->where('is_active', true)
                ->exists();
            if ($isRbtProduct && $input['warehouse_id'] == Warehouse::WAREHOUSE_SANJOSE_ID) {
                return $this->handleFail('SKU(s) belong to Dark Pods and cannot be used in this type of request.');
            }
            $totalInRack = Box::join('location', function ($join) {
                $join->on('box.location_id', '=', 'location.id');
            })
                ->where('location.warehouse_id', $input['warehouse_id'])
                ->where('location.is_deleted', Location::NOT_DELETED)
                ->where('location.type', Location::RACK)
                ->where('product_id', $input['product_id'])
                ->where('box.is_deleted', Box::NOT_DELETED)
                ->count('box.id');
            $productTikTok = ProductTiktok::where([
                'is_active' => 1,
                'product_id' => $input['product_id']
            ])->first();

            if (empty($productTikTok) && $input['priority'] == InternalRequest::TIKTOK_PRIORITY) {
                DB::rollBack();

                return $this->handleFail('This TikTok product SKU is not approved.');
            }

            $incomingStock = ProductQuantity::where('warehouse_id', $input['warehouse_id'])
                ->where('product_id', $input['product_id'])
                ->first();

            if (($incomingStock->incoming_stock ?? 0) > 0 || $totalInRack >= $input['box_quantity']) {
                for ($i = 1; $i <= $input['box_quantity']; $i++) {
                    $priority = $input['priority'] ?? InternalRequest::LOW_PRIORITY;
                    $insertRequest = InternalRequest::create([
                        'product_id' => $input['product_id'],
                        'employee_create_id' => $input['employee_id'],
                        'warehouse_id' => $input['warehouse_id'],
                        'priority' => $priority,
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                    $this->logInternalRequestHistory($input['employee_id'], $insertRequest->id, InternalRequestHistory::ACTION_CREATE);
                }

                internalRequestEmit(InternalRequest::EVENT_CREATE, $insertRequest->warehouse_id);
                DB::commit();

                return $this->handleSuccess('Request successfully.');
            } elseif (($incomingStock->incoming_stock ?? 0) <= 0 && $totalInRack <= 0) {
                DB::rollBack();

                return $this->handleFail("This SKU is out of stock. Please reach out to the relevant department for further assistance.\n\nSKU này đã hết hàng, Vui lòng liên hệ với bộ phận liên quan để được hỗ trợ.");
            } else {
                DB::rollBack();

                return $this->handleFail("This SKU does not have enough quantity available in the rack to fulfill your request. Please contact the relevant department for further assistance.\n\nSKU này không đủ số lượng trong kho để đáp ứng yêu cầu của bạn. Vui lòng liên hệ với bộ phận liên quan để được hỗ trợ.");
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.create Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function missingBox($input)
    {
        try {
            $employee = Employee::find($input['employee_id']);
            $input['warehouse_id'] = $employee->warehouse_id;
            // check xem product do co phai trong internal request do khong
            $internalRequest = InternalRequest::with('product')
                ->where('id', $input['internal_request_id'])
                ->where('product_id', $input['product_id'])
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('status', InternalRequest::PICKING_UP_STATUS)
                ->first();

            if (!$internalRequest) {
                return $this->handleFail('not found internal request.');
            }

            $input['user_id'] = User::SYSTEM;
            $product = Product::find($input['product_id']);
            $location = Location::where('warehouse_id', $employee->warehouse_id)
                ->where('type', Location::RACK)
                ->where('barcode', $input['location_barcode'])
                ->first();
            $input['location_id'] = $location->id;
            $boxRepo = app()->make(BoxRepository::class);
            $data = $boxRepo->deleteAllBoxInLocation($input, $input['internal_request_id'], Inventory::OBJECT_INTERNAL_REQUEST);

            if (!$data['status']) {
                return $this->handleFail($data['output']['message']);
            }

            internalRequestEmit(InternalRequest::EVENT_REPORT_MISSING_BOX, $internalRequest->warehouse_id);

            return $this->handleSuccess("Delete all box has product sku $product?->sku success.");
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.missingBox Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function allBoxMissing($input)
    {
        try {
            $employee = Employee::find($input['employee_id']);
            // check xem product do co phai trong internal request do khong
            $internalRequest = InternalRequest::with('product')
                ->where('id', $input['internal_request_id'])
                ->where('product_id', $input['product_id'])
                ->where('warehouse_id', $employee->warehouse_id)
                ->where('status', InternalRequest::PICKING_UP_STATUS)
                ->first();

            if (!$internalRequest) {
                return $this->handleFail('not found internal request.');
            }

            $location = Location::where('warehouse_id', $employee->warehouse_id)
                ->where('type', Location::RACK)
                ->where('barcode', $input['location_barcode'])
                ->first();
            $input['location_id'] = $location->id;
            $boxRepo = app()->make(BoxRepository::class);
            $data = $boxRepo->getBoxByLocationAndProduct($input['product_id'], $input['location_id'], $employee->warehouse_id);

            if (!$data['status']) {
                return $this->handleFail($data['output']['message']);
            }

            return $this->handleSuccess('success', $data['output']['data']);
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.allBoxMissing Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function delete($id, $input)
    {
        try {
            $employee = Employee::where('id', $input['employee_id'])->where('is_deleted', false)->first();

            if (!$employee) {
                return $this->handleFail('Employee not found.');
            }

            $internalRequest = InternalRequest::where('id', $id)
                ->where('warehouse_id', $employee->warehouse_id)
                ->first();

            if (!$internalRequest) {
                return $this->handleFail('Internal request not found.');
            }

            if ($internalRequest->status != InternalRequest::NEW_STATUS) {
                return $this->handleFail("Only status 'New' of internal request can be deleted.");
            }

            $internalRequest->is_deleted = InternalRequest::DELETED;
            $internalRequest->save();
            $this->logInternalRequestHistory($input['employee_id'], $id, InternalRequestHistory::ACTION_DELETE);
            internalRequestEmit(InternalRequest::EVENT_DELETE, $internalRequest->warehouse_id);

            return $this->handleSuccess('Delete internal request successfully.', $internalRequest);
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.delete Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function reject($id, $input)
    {
        try {
            $employee = Employee::where('id', $input['employee_id'])->where('is_deleted', false)->first();

            if (!$employee) {
                return $this->handleFail('Employee not found.');
            }

            $internalRequest = InternalRequest::where('id', $id)
                ->where('warehouse_id', $employee->warehouse_id)
                ->first();

            if (!$internalRequest) {
                return $this->handleFail('Internal request not found.');
            }

            if (!in_array($internalRequest->status, [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS])) {
                return $this->handleFail('Only “New” and “Picking Up” requests can be rejected.');
            }

            $totalInRack = Box::join('location', function ($join) use ($internalRequest) {
                $join->on('box.location_id', '=', 'location.id')
                    ->where('location.warehouse_id', $internalRequest->warehouse_id)
                    ->where('location.type', Location::RACK);
            })
                ->where('product_id', $internalRequest->product_id)
                ->active()
                ->count('box.id');
            $incomingStock = ProductQuantity::where('warehouse_id', $internalRequest->warehouse_id)
                ->where('product_id', $internalRequest->product_id)->first();

            if ((($incomingStock->incoming_stock ?? 0) > 0 || $totalInRack > 0) && $employee->department == Department::DEPARTMENT_INVENTORY) {
                return $this->handleFail('There are still products in stock or incoming stock');
            }

            $internalRequest->status = InternalRequest::REJECTED_STATUS;
            $internalRequest->employee_reject_id = $input['employee_id'];
            $internalRequest->rejected_at = date('Y-m-d H:i:s');
            $internalRequest->save();
            $this->logInternalRequestHistory($input['employee_id'], $id, InternalRequestHistory::ACTION_REJECT);
            internalRequestEmit(InternalRequest::EVENT_REJECT, $internalRequest->warehouse_id);

            return $this->handleSuccess('Reject internal request successfully.', $internalRequest);
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.reject Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function logInternalRequestHistory($employeeId, $internalRequestId, $action)
    {
        try {
            $internalRequestHistory = InternalRequestHistory::create([
                'internal_request_id' => $internalRequestId,
                'employee_id' => $employeeId,
                'action' => $action,
            ]);

            return $this->handleSuccess('success', $internalRequestHistory);
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.logInternalRequestHistory Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function login($input)
    {
        try {
            $employee = Employee::where('code', $input['code'])->where('is_deleted', false)->first();
            $timeTrackingId = null;

            if ($employee->department == Department::DEPARTMENT_INVENTORY) {
                $input['job_type'] = TimeTracking::JOB_TYPE_CREATE_BOX_MOVING;
                $input['id'] = $employee->id;
                $timeTrackingRepo = new TimeCheckingRepository();
                $timeTrackingId = $timeTrackingRepo->insertTimeChecking($input);
            }
            $employee = $employee->toArray();

            if (!empty($employee['avatar'])) {
                $employee['avatar'] = env('AWS_S3_URL', '') . '/' . $employee['avatar'];
            }

            return [
                'employee' => $employee,
                'time_tracking_id' => $timeTrackingId,
            ];
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.login Exception', [$exception]);

            return $exception->getMessage();
        }
    }

    public function releaseRequest($id, $input)
    {
        DB::beginTransaction();
        try {
            $internalRequest = InternalRequest::find($id);

            if (!$internalRequest) {
                return $this->handleFail('Internal request not found.');
            }

            if ($internalRequest->status != InternalRequest::PICKING_UP_STATUS) {
                return $this->handleFail('Only "Picking Up" request can be released.');
            }

            if ($internalRequest->employee_receive_id != $input['employee_id']) {
                return $this->handleFail('This request was release by another employee.');
            }

            $employee = Employee::where('id', $input['employee_id'])
                ->where('is_deleted', Employee::NOT_DELETED)
                ->first();

            if (!$employee) {
                return $this->handleFail('Employee is not found or deleted.');
            }

            $internalRequest->status = InternalRequest::NEW_STATUS;
            $internalRequest->employee_receive_id = null;
            $internalRequest->received_at = null;
            $internalRequest->save();
            $this->logInternalRequestHistory($input['employee_id'], $id, InternalRequestHistory::ACTION_RELEASE);
            internalRequestEmit(InternalRequest::EVENT_RELEASE, $internalRequest->warehouse_id);
            DB::commit();

            return $this->handleSuccess('Release internal request successfully.', $internalRequest);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('InternalRequestRepository.releaseRequest Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function setTimeout($internalRequest)
    {
        // chuyển trạng thái internal request về new
        // chuyển set null employee_id và received_at
        InternalRequest::where('id', $internalRequest->id)->update([
            'status' => InternalRequest::NEW_STATUS,
            'employee_receive_id' => null,
            'received_at' => null
        ]);

        //insert history
        $internalRequestHistory = [
            'internal_request_id' => $internalRequest->id,
            'action' => InternalRequestHistory::ACTION_TIME_OUT,
            'employee_id' => 0
        ];
        InternalRequestHistory::insert($internalRequestHistory);
    }

    public function availableBox($input)
    {
        try {
            $countInternalRequest = InternalRequest::where('product_id', $input['product_id'] ?? '')
                ->whereIn('status', [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS])
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('is_deleted', InternalRequest::NOT_DELETED)
                ->count();
            $totalInRack = Box::join('location', function ($join) {
                $join->on('box.location_id', '=', 'location.id');
            })
                ->where('location.warehouse_id', $input['warehouse_id'])
                ->where('location.is_deleted', Location::NOT_DELETED)
                ->where('location.type', Location::RACK)
                ->where('product_id', $input['product_id'])
                ->where('box.is_deleted', Box::NOT_DELETED)
                ->count('box.id');
            $incomingStock = ProductQuantity::where('warehouse_id', $input['warehouse_id'])
                ->where('product_id', $input['product_id'])->first();
            $rbtExistsRequests = InternalRequest::where('product_id', $input['product_id'] ?? '')
                ->whereIn('status', [InternalRequest::NEW_STATUS, InternalRequest::PICKING_UP_STATUS, InternalRequest::UNCHECK_STATUS])
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('is_rbt', InternalRequest::IS_RBT)
                ->where('is_deleted', InternalRequest::NOT_DELETED)
                ->get();
            if (!empty($input['is_rbt'])) {
                return [
                    'total_box' => $totalInRack,
                    'incoming_stock' => $incomingStock->incoming_stock ?? 0,
                    'rbt_exists_requests' => $rbtExistsRequests ?? [],
                    'exist_request' => $rbtExistsRequests->count() > 0,
                    'count_pending' => $rbtExistsRequests->count(),
                ];
            } else {
                return [
                    'total_box' => $totalInRack,
                    'incoming_stock' => $incomingStock->incoming_stock ?? 0,
                    'count_pending' => $countInternalRequest,
                    'exist_request' => $countInternalRequest > 0,
                    'rbt_exists_requests' => $rbtExistsRequests ?? [],
                ];
            }
        } catch (\Exception $exception) {
            Log::error('InternalRequestRepository.availableBox Exception', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function getDetail($id)
    {
        return InternalRequest::find($id);
    }

    public function getLocationAndRequest($barcode)
    {
        $prefix = Str::before($barcode, '-');

        $locations = Location::whereHas('product.internalRequests', function ($query) {
            $query->where('status', InternalRequest::UNCHECK_STATUS)
                ->where('is_rbt', InternalRequest::IS_RBT)
                ->where('is_deleted', InternalRequest::NOT_DELETED);
        })
            ->with(['product:id,sku', 'product.internalRequests.box:id,barcode', 'product.internalRequests.countSticker:id,barcode', 'product.internalRequests' => function ($query) {
                $query->where('status', InternalRequest::UNCHECK_STATUS)
                    ->where('is_rbt', InternalRequest::IS_RBT)
                    ->where('is_deleted', InternalRequest::NOT_DELETED);
            }])
            ->where('barcode', 'like', $prefix . '%')
            ->where('is_deleted', Location::NOT_DELETED)
            ->where('type', Location::MOVING_SHELVES)
            ->get();

        foreach ($locations as $location) {
            $location->product->internalRequests->each(function ($request) use ($location) {
                $request->location_barcode = $location->barcode;
                $request->location_id = $location->id;
            });
            $location->internalRequests = $location->product->internalRequests;
            unset($location->product);
        }

        return $locations;
    }

    public function getCountRbtRequests($input)
    {
        $query = InternalRequest::where('is_deleted', InternalRequest::NOT_DELETED)
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('is_rbt', InternalRequest::IS_RBT);

        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        }

        return $query->groupBy('status')
            ->select('status', DB::raw('count(*) as total'))
            ->pluck('total', 'status')
            ->toArray();
    }
}
