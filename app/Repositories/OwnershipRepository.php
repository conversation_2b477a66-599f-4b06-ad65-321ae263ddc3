<?php

namespace App\Repositories;

use App\Models\Ownership;

class OwnershipRepository extends CommonRepository
{
    public function getList($request)
    {
    }

    public function getAll()
    {
        return Ownership::all();
    }

    public function create($request)
    {
        try {
            $trademark = Ownership::create([
                'name' => $request['name'],
                'created_by' => auth()->user()->id
            ]);

            return $this->successResponse('Create ownership successfully', $trademark);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), $exception->getCode());
        }
    }
}
