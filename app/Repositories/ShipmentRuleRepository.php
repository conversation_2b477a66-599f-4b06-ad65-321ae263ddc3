<?php

namespace App\Repositories;

use App\Models\ProductColor;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Validator;

class ShipmentRuleRepository
{
    public function detectService($order = null)
    {
        $saleOrderRepo = new SaleOrderRepository(false);
        //585241 // 8 tee
        //585697 // 4 tee
        //685193 // 1 tee
        //585507 // 3 tee
        //585204 // 2 hoodie 1 tee => not match
        //585343 // 1 fleece 1 tee 2XL


        $saleOrder = $saleOrderRepo->getDetail(585343);
        $rules = $this->fetchShipmentRule();
        $typeObject = $this->getOrderProductTypes($saleOrder);

        foreach ($rules as $ruleItem) {
            // check type
            $typeList = array_keys($typeObject);

            if (!$this->arrayEqual($typeList, $ruleItem->type)) {
                continue;
            }

            // check quantity & size
            $flag = true;

            $listTypes =  (array)$ruleItem->rule;

            foreach ($listTypes as $ruleType => $ruleVal) {


                if (!isset($typeObject[$ruleType])) {
                    $flag = false;
                    break;
                }

                $type = $typeObject[$ruleType];


                // check size
                if ($ruleVal->size != 'any') {

                    $allowSizes = explode(',', $ruleVal->size);

                    $typeSizes = array_keys($type);

                    if (!$this->checkSize($typeSizes, $allowSizes)) {

                        $flag = false;
                        break;
                    }
                }

                // check quantity
                $tmp = $ruleVal->quantity;

                $ruleQuantityType = key($tmp);
                $ruleQuantityValue = $ruleVal->quantity->{$ruleQuantityType};

                $total = array_sum($type);

                switch ($ruleQuantityType) {
                    case "equal":
                        if ($ruleQuantityValue != $total)
                            $flag = false;

                        break;
                    case "range":
                        $tmp = explode("-", $ruleQuantityValue);
                        if ($tmp[0] > $total || $tmp[1] < $total)
                            $flag = false;
                        break;
                    case "greater":
                        if ($total < $ruleQuantityValue)
                            $flag = false;

                        break;
                    default:
                        $flag = false;
                }

                if ($flag == false) {
                    break;
                }

            }

            if ($flag == true) {
                echo "$ruleItem->name\n";
            }
        }

    }

    public function arrayEqual($a, $b) {
        return (
            is_array($a)
            && is_array($b)
            && count($a) == count($b)
            && array_diff($a, $b) === array_diff($b, $a)
        );
    }

    public function checkSize($sizes, $allowSizes)
    {
        foreach ($sizes as $size) {
            if (!in_array($size, $allowSizes)) return false;
        }
        return true;
    }

    public function getOrderProductTypes($saleOrder)
    {
        $styles = [];
        $styleSkus = [];
        $sizes = [];
        $quantity = 0;
        foreach ($saleOrder->items as $item) {
            $quantityTmp = $styles[$item->product_style_sku][$item->product_size_sku] ?? 0;
            $styles[$item->product_style_sku][$item->product_size_sku] = $quantityTmp + $item->quantity;
            $sizes[] = $item->product_size_sku;
            $styleSkus[] = $item->product_style_sku;
            $quantity = $item->quantity + $quantity;
        }


        $sizes = array_unique($sizes);
        $styleSkus = array_unique($styleSkus);

        $typeNames = ProductStyle::query()->whereIn('sku', $styleSkus)->pluck('type', 'sku');

        foreach ($typeNames as $key => $typeName) {
            $typeNames[$key] = strtolower($typeName);
        }

        $sizeNames = ProductSize::query()->whereIn('sku', $sizes)->pluck('name', 'sku');


        $types = [];
        foreach ($styles as $typeName => $styleVal) {
            foreach ($styleVal as $sizeName => $sizeVal) {
                $quantityTmp = $types[$typeNames[$typeName]][$sizeNames[$sizeName]] ?? 0;
                $types[$typeNames[$typeName]][$sizeNames[$sizeName]] = $quantityTmp + $sizeVal;
            }
        }
        return $types;
    }

    public function fetchShipmentRule()
    {
        $rule = DB::table('shipment_rule')->get();
        $rule->map(function ($item) {
            $item->rule = json_decode($item->rule);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo $item->name . "\n";
            }
            $item->type = explode(',', $item->type);
            return $item;
        });
        return $rule;
    }

}
