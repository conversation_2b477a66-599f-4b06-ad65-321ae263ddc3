<?php

namespace App\Repositories;

use App\Models\ScreenOrder;
use App\Models\ScreenOrderPackaging;
use Illuminate\Support\Facades\DB;

class ScreenOrderPackagingRepository extends CommonRepository
{
    public function getDetail($id)
    {
        return ScreenOrder::with([
            //            'packagings.packaging.instructions',
            'packagings.instructions',
            'client',
        ])->with(['shippingAddress' => function ($query) {
            $query->where('type', 'to_address');
        }])->where('id', $id)->first();
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();

            $packagingIds = $input['packaging_ids'];
            $orderId = $input['order_id'];
            $existingPackagingIds = ScreenOrderPackaging::where('screen_order_id', $orderId)
                ->pluck('screen_packaging_id')
                ->toArray();
            $newPackagingIds = array_diff($packagingIds, $existingPackagingIds);
            $toDeletePackagingIds = array_diff($existingPackagingIds, $packagingIds);
            if (!empty($toDeletePackagingIds)) {
                ScreenOrderPackaging::where('screen_order_id', $orderId)
                    ->whereIn('screen_packaging_id', $toDeletePackagingIds)
                    ->delete();
            }
            if (!empty($newPackagingIds)) {
                $data = array_map(function ($packagingId) use ($orderId) {
                    return [
                        'screen_order_id' => $orderId,
                        'screen_packaging_id' => $packagingId,
                    ];
                }, $newPackagingIds);
                ScreenOrderPackaging::insert($data);
            }
            DB::commit();

            return $this->successResponse('Create Screen packaging success.');
        } catch (\throw  $e) {
            DB::rollBack();
        }
    }

    public function delete($id)
    {
        try {
            ScreenOrderPackaging::where('id', $id)->delete();

            return $this->successResponse('Delete Screen order packaging success');
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
