<?php

namespace App\Repositories;

use App\Models\ProductColor;
use App\Repositories\Contracts\ProductColorRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ProductColorRepository extends CommonRepository implements ProductColorRepositoryInterface
{
    public function getList(Request $request)
    {
        $query = ProductColor::search($request);

        return $query->latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }

    public function fetchAll()
    {
        return ProductColor::all();
    }

    private function validateData(Request $request, int $id = null)
    {
        return $this->validation($request->all(), [
            'name' => "required|max:255|unique:product_color,name,$id,id",
            'sku' => "required|max:255|unique:product_color,sku,$id,id",
            'color_code' => 'required|max:7',
            'neck_label_color' => 'required|string|max:7',
            'icc_color' => 'nullable|string',
        ]);
    }

    private function prepareData($request): array
    {
        return [
            'name' => $request['name'],
            'sku' => $request['sku'],
            'color_code' => $request['color_code'],
            'neck_label_color' => $request['neck_label_color'],
            'icc_color' => $request['icc_color'] ?? null,
        ];
    }

    public function create(Request $request): JsonResponse
    {
        try {
            $validatorRes = $this->validateData($request);
            if ($validatorRes !== true) {
                return $validatorRes;
            }
            $data = $this->prepareData($request);
            $productColor = ProductColor::create($data);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Create product color successfully!', $productColor);
    }

    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $productColor = ProductColor::find($id);
            if (!$productColor) {
                return $this->errorResponse('Product color not found!');
            }
            $validatorRes = $this->validateData($request, $id);
            if ($validatorRes !== true) {
                return $validatorRes;
            }
            $data = $this->prepareData($request);
            foreach ($data as $key => $value) {
                $productColor[$key] = $value;
            }
            $productColor->save();
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update product color successfully!', $productColor);
    }
}
