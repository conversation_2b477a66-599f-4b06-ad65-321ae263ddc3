<?php


namespace App\Repositories;


use App\Models\ProductSize;
use App\Repositories\Contracts\ProductSizeRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ProductSizeRepository extends CommonRepository implements ProductSizeRepositoryInterface
{
    public function getList($request)
    {
        return ProductSize::latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }

    public function fetchAll()
    {
        return ProductSize::all();
    }

    private function validateData(Request $request, int $id = null)
    {
        return $this->validation($request->all(), [
            'name' => "required|max:255|unique:product_size,name,$id,id",
            'sku' => "required|max:255|unique:product_size,sku,$id,id"
        ]);
    }

    private function prepareData($request): array
    {
        return [
            'name' => $request['name'],
            'sku' => $request['sku']
        ];
    }

    public function create(Request $request): JsonResponse
    {
        try {
            $validatorRes = $this->validateData($request);
            if ($validatorRes !== true) {
                return $validatorRes;
            }
            $data = $this->prepareData($request);
            $productSize = ProductSize::create($data);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return $this->successResponse('Create product size successfully!', $productSize);
    }

    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $productSize = ProductSize::find($id);
            if (!$productSize) {
                return $this->errorResponse('Product size not found!');
            }
            $validatorRes = $this->validateData($request, $id);
            if ($validatorRes !== true) {
                return $validatorRes;
            }
            $data = $this->prepareData($request);
            foreach ($data as $key => $value) {
                $productSize[$key] = $value;
            }
            $productSize->save();
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return $this->successResponse('Update product size successfully!', $productSize);
    }
}
