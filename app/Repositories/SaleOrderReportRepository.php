<?php

namespace App\Repositories;

use App\Models\PrintMethod;
use App\Models\SaleOrderFulfillmentReport;
use App\Models\SaleOrderFulfillmentTotalByMonth;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SaleOrderReportRepository extends CommonRepository
{
    public function fetchFulfillmentReportByMonth(Request $request)
    {
        $months = $this->generateMonths($request->start_month, $request->end_month);
        $query = SaleOrderFulfillmentTotalByMonth::whereIn('month_year', $months);
        if ($request->warehouse) {
            $query->where('warehouse_id', $request->warehouse);
        }
        if ($request->store_id) {
            $query->where('store_id', $request->store_id);
        }
        if ($request->product_type) {
            $query->whereIn('product_type', $request->product_type);
        }
        if ($request->print_method) {
            $query->whereIn('print_method', $request->print_method);
        }

        if ($request->is_business_day) {
            $query->selectRaw('type_report,
             SUM(printed_business_day) AS total_printed_day,
            SUM(printed_to_fulfill_business_day) AS total_printed_to_fulfill_day,
            SUM(fulfillment_day) AS total_fulfillment_day,
            SUM(total_orders) AS total_orders') ;
        }else{
            $query->selectRaw('type_report,
             SUM(printed_day) AS total_printed_day,
            SUM(printed_to_fulfill_day) AS total_printed_to_fulfill_day,
            SUM(fulfillment_day) AS total_fulfillment_day,
            SUM(total_orders) AS total_orders') ;
        }
        $result = $query->groupBy('type_report')->get();
        $averages = [];
        foreach ($result as $item) {
            $printedDay = $item['total_printed_day'];
            $printedToFulfillmentDay = $item['total_printed_to_fulfill_day'];
            $fulfillmentDay = $item['total_fulfillment_day'];
            $totalOrders = $item['total_orders'];
            $avgPrinted = ($totalOrders > 0) ? round($printedDay / $totalOrders, 2) : 0;
            $avgPrintedToFulfill = ($totalOrders > 0) ? round($printedToFulfillmentDay / $totalOrders, 2) : 0;
            $avgFulfillment = ($totalOrders > 0) ? round($fulfillmentDay / $totalOrders, 2) : 0;
            $averages[$item['type_report']] = [
                "type" => $item['type_report'],
                "total_orders" => $totalOrders,
                "total_printed_day" => $printedDay,
                "total_printed_to_fulfill_day" => $printedToFulfillmentDay,
                "avg_printed" => $avgPrinted,
                "avg_printed_to_fulfill" => $avgPrintedToFulfill,
                "avg_fulfillment" => $avgFulfillment,
            ];
        }
        $orderTypes = [
            SaleOrderFulfillmentReport::SINGLE_ORDER_TYPE,
            SaleOrderFulfillmentReport::TWO_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::THREE_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::FOUR_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::FIVE_ITEMS_ORDER_TYPE,
        ];
        $sortedData = [];
        foreach ($orderTypes as $type) {
            $sortedData[] = $averages[$type] ?? [
                    "type" => $type,
                    "total_orders" => 0,
                    "total_printed_day" => 0,
                    "total_printed_to_fulfill_day" => 0,
                    "avg_printed" => 0,
                    "avg_printed_to_fulfill" => 0,
                    "avg_fulfillment" => 0,
                ];
        }
        return $sortedData;
    }

    public function fetchFulfillmentPrintMethodReportByMonth(Request $request)
    {
        $months = $this->generateMonths($request->start_month, $request->end_month);
        $query = SaleOrderFulfillmentTotalByMonth::query()
            ->join('print_method', 'sale_order_fulfillment_total_by_month.print_method', '=', 'print_method.name')
            ->whereIn('month_year', $months);
        if ($request->warehouse) {
            $query->where('warehouse_id', $request->warehouse);
        }
        if ($request->store_id) {
            $query->where('store_id', $request->store_id);
        }
        if ($request->product_type) {
            $query->whereIn('product_type', $request->product_type);
        }
        if ($request->is_business_day) {
            $query->selectRaw('print_method, (SUM(printed_business_day) + SUM(printed_to_fulfill_business_day)) AS total_fulfillment_day, SUM(total_orders) AS total_orders');
        }else{
            $query->selectRaw('print_method, (SUM(printed_day) + SUM(printed_to_fulfill_day)) AS total_fulfillment_day, SUM(total_orders) AS total_orders');
        }
        $result = $query
            ->groupBy('print_method')
            ->get();
        $averages = [];
        foreach ($result as $item) {
            $fulfillmentDay = $item['total_fulfillment_day'];
            $totalOrders = $item['total_orders'];
            $avg = ($totalOrders > 0) ? round($fulfillmentDay / $totalOrders, 2) : 0;
            $printMethod = $item['print_method'];
            if ($printMethod == "MUGS") {
                $printMethod = PrintMethod::MUGS;
            }
            $averages[$printMethod] = [
                "print_method" => $printMethod,
                "total_orders" => $totalOrders,
                "total_fulfillment_day" => $fulfillmentDay,
                "avg" => $avg,
            ];
        }
        $orderTypes = [
            PrintMethod::DTG,
            PrintMethod::DTF,
            PrintMethod::UV,
            PrintMethod::MUGS,
        ];
        $sortedData = [];
        foreach ($orderTypes as $type) {
            $sortedData[] = $averages[$type] ?? [
                    "print_method" => $type,
                    "total_orders" => 0,
                    "total_fulfillment_day" => 0,
                    "avg" => 0,
                ];
        }
        return $sortedData;
    }


    public function fetchFulfillmentReportByWarehouse(Request $request)
    {
        $months = $this->generateMonths($request->start_month, $request->end_month);
        //$months = $this->generateMonths("02-2022", $request->end_month);
        $query = SaleOrderFulfillmentTotalByMonth::query()
            ->whereIn('month_year', $months);
        if ($request->store_id) {
            $query->where('store_id', $request->store_id);
        }
        if ($request->product_type) {
            $query->whereIn('product_type', $request->product_type);
        }
        if ($request->print_method) {
            $query->whereIn('print_method', $request->print_method);
        }
        if ($request->is_business_day) {
            $query->selectRaw('type_report,
            warehouse_id,
            ( SUM(printed_business_day) + SUM(printed_to_fulfill_business_day)) AS total_fulfillment_day,
            SUM(total_orders) AS total_orders');
        }else{
            $query->selectRaw('type_report,
            warehouse_id,
            ( SUM(printed_day) + SUM(printed_to_fulfill_day)) AS total_fulfillment_day,
            SUM(total_orders) AS total_orders');
        }
        $result = $query
            ->groupBy('type_report', 'sale_order_fulfillment_total_by_month.warehouse_id')
            ->orderBy('sale_order_fulfillment_total_by_month.id', 'asc')
            ->get();

        $groupedData = [];
        $warehouseIdHasReport = [];
        foreach ($result->toArray() as $item) {
            $warehouseId = $item['warehouse_id'];
            if (!isset($groupedData[$warehouseId])) {
                $groupedData[$warehouseId] = [
                    'warehouse_id' => $warehouseId,
                    'data' => [],
                ];
            }
            $groupedData[$warehouseId]['data'][] = [
                'type_report' => $item['type_report'],
                'total_fulfillment_day' => $item['total_fulfillment_day'],
                'total_orders' => $item['total_orders'],
                'avg' => $item['total_orders'] > 0 ? round($item['total_fulfillment_day'] / $item['total_orders'], 2) : 0
            ];
        }
        $warehousesData = array_values($groupedData);
        $orderTypes = ['single_order', '2_items_order', '3_items_order', '4_items_order', '5_items_order'];
        foreach ($warehousesData as &$item) {
            $this->reorderData($item, $orderTypes);
        }
        if ($warehousesData) {
            $warehouseIdHasReport = array_column($warehousesData, 'warehouse_id');
        }
        $warehouses = Warehouse::query()
            ->select('id as warehouse_id', 'code', 'name')
            ->limit(5)
            ->get()
            ->toArray();
        foreach ($warehouses as &$warehouse) {
            if (in_array($warehouse['warehouse_id'], $warehouseIdHasReport)) {
                $warehouse['data'] = $this->searchDataFulfillFromArray($warehousesData, $warehouse['warehouse_id']);
            } else {
                $warehouse['data'] = $this->buildDataFulfillDefault();
            }
        }

        return $warehouses;
    }

    private function searchDataFulfillFromArray($warehouses, $targetWarehouseId): array
    {
        $result = [];
        foreach ($warehouses as $warehouse) {
            if ($warehouse['warehouse_id'] === $targetWarehouseId) {
                $result = $warehouse['data'];
                break;
            }
        }
        return $result;
    }

    private function buildDataFulfillDefault(): array
    {
        $orderTypes = ['single_order', '2_items_order', '3_items_order', '4_items_order', '5_items_order'];
        $data = [];
        foreach ($orderTypes as $type) {
            $data[] = [
                'type_report' => $type,
                'total_fulfillment_day' => "0",
                'total_orders' => "0",
                'avg' => 0
            ];
        }
        return $data;
    }

    private function reorderData(&$warehouse, $orderTypes): void
    {
        $existingTypes = array_column($warehouse['data'], 'type_report');
        $missingTypes = array_diff($orderTypes, $existingTypes);

        foreach ($missingTypes as $type) {
            $warehouse['data'][] = [
                'type_report' => $type,
                'total_fulfillment_day' => "0",
                'total_orders' => "0",
                'avg' => 0
            ];
        }
        // Sort the data based on the predefined order of order types
        usort($warehouse['data'], function ($a, $b) use ($orderTypes) {
            return array_search($a['type_report'], $orderTypes) - array_search($b['type_report'], $orderTypes);
        });
    }

    private function generateMonths($startMonth, $endMonth): array
    {
        $start = Carbon::createFromFormat('m-Y', $startMonth);
        $end = Carbon::createFromFormat('m-Y', $endMonth);
        $months = [];
        while ($start->lessThanOrEqualTo($end)) {
            $months[] = $start->format('m-Y');
            $start->addMonth();
        }
        return $months;
    }

    public function fetchFulfillmentReportForSeller(Request $request)
    {
        $months = $this->generateMonths($request->start_month, $request->end_month);
        $query = SaleOrderFulfillmentTotalByMonth::whereIn('month_year', $months);
        if ($request->product_type) {
            $query->whereIn('product_type', $request->product_type);
        }            
        $result = $query
            ->selectRaw('type_report,
            SUM(printed_business_day) AS total_printed_day,
            SUM(total_orders) AS total_orders')
            ->groupBy('type_report')
            ->get();
        $averages = [];
        foreach ($result as $item) {
            $printedDay = $item['total_printed_day'];
            $totalOrders = $item['total_orders'];
            $avgPrintedToFulfill = ($totalOrders > 0) ? round($printedDay / $totalOrders, 2) : 0;
            $averages[$item['type_report']] = [
                "type" => $item['type_report'],
                "total_orders" => $totalOrders,
                "avg_fulfillment" => $avgPrintedToFulfill,
            ];
        }
        $orderTypes = [
            SaleOrderFulfillmentReport::SINGLE_ORDER_TYPE,
            SaleOrderFulfillmentReport::TWO_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::THREE_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::FOUR_ITEMS_ORDER_TYPE,
            SaleOrderFulfillmentReport::FIVE_ITEMS_ORDER_TYPE,
        ];
        $sortedData = [];
        foreach ($orderTypes as $type) {
            $sortedData[] = $averages[$type] ?? [
                    "type" => $type,
                    "total_orders" => 0,
                    "avg_fulfillment" => 0,
                    ];
        }
        return $sortedData;
    }

}
