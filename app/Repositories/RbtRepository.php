<?php

namespace App\Repositories;

use App\Imports\RerouteImport;
use App\Models\AdjustShelvesFace;
use App\Models\BarcodePrinted;
use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\InternalRequestHistory;
use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\RbtBatchNumber;
use App\Models\RbtPerformanceReport;
use App\Models\RbtProduct;
use App\Models\RbtWipReceived;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderInsert;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\ShipmentItem;
use App\Models\ShipmentItemLabel;
use App\Models\Store;
use App\Models\StoreShipment;
use App\Models\Tag;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Koerel\PdfUnite\PdfUnite;
use Maatwebsite\Excel\Facades\Excel;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class RbtRepository extends CommonRepository
{
    public const TAG_TIKTOK = 'tiktok';

    public const TAG_SINGLE = 'single';

    public const TAG_MULTIPLE = 'multiple';

    public const TAG_BULK = 'bulk';

    public const TAG_EXPRESS = 'express';

    public const TAG_REPRINT = 'reprint';

    public const TAG_MANUAL = 'manual';

    public const TAG_SAMPLE = 'sample';

    public const TAG_NECK_PRINT = 'neck_print';

    public const TAG_SLEEVE = 'sleeve';

    public const TAG_POCKET = 'pocket';

    public const TAG_BABY = 'baby';

    public const TAG_NO_PRETREAT = 'no_pretreat';

    public const TAG_16X18 = '16x18';

    public function __construct()
    {
        parent::__construct();
    }

    public function updateBarcodePrintedItem($args)
    {
        $time = date('Y-m-d H:i:s');
        $st = DB::table('sale_order_item_barcode')
            ->select(['sale_order_item_barcode.id'])
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id');

        // ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
        // ->join('product', 'product.sku', 'sale_order_item.product_sku');
        $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->whereIn('sale_order_item_barcode.print_method', [PrintMethod::DTG, PrintMethod::NECK])
            ->where('sale_order_item.ink_color_status', 1)
           // ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereIn('sale_order_item_barcode.label_id', $args['wip_ids'])
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT);

        $st->update([
            'employee_pull_id' => $args['employee_id'] ?? RbtProduct::RBT_EMPLOYEE_ID,
            'barcode_printed_id' => $args['barcode_printed_id'],
            'print_barcode_at' => $time
        ]);
        $saleOrderIds = SaleOrderItemBarcode::where('barcode_printed_id', $args['barcode_printed_id'])->pluck('order_id')->toArray();

        if (count($saleOrderIds) <= 0) {
            return false;
        }

        $chunkOrderIds = array_chunk(array_unique($saleOrderIds), 100);

        foreach ($chunkOrderIds as $orderIds) {
            // Update barcode printed status and time
            $newOrderIds = SaleOrder::whereIn('id', $orderIds)->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();

            if (!empty($newOrderIds)) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
            }

            SaleOrder::whereIn('id', $orderIds)
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update([
                    'order_status' => SaleOrder::IN_PRODUCTION,
                    'order_production_at' => $time
                ]);
        }

        DB::table('barcode_printed')->where('id', $args['barcode_printed_id'])
            ->update(['quantity' => count($saleOrderIds)]);

        return true;
    }

    public function updateLastBarcodePrintedTime($data)
    {
        $check = DB::table('barcode_printed_time')
            ->where('station_id', $data['station_id'])
            ->first();

        if ($check) {
            DB::table('barcode_printed_time')
                ->where('id', $check->id)
                ->update(['printed_at' => date('Y-m-d H:i:s')]);
        } else {
            $list = [
                'store_id',
                'warehouse_id',
                'account_id',
                'is_xqc',
                'style_sku',
                'is_reprint',
                'is_manual',
                'is_reroute',
                'is_fba',
                'is_eps',
                'color_sku',
                'is_tiktok',
                'is_bulk_order',
            ];
            $insert['printed_at'] = date('Y-m-d H:i:s');
            $insert['station_id'] = $data['station_id'];
            $insert['warehouse_id'] = $data['warehouse_id'] ?? 1;

            foreach ($list as $item) {
                $insert[$item] = $data[$item] ?? null;
            }

            DB::table('barcode_printed_time')->insert($insert);
        }
    }

    public function convertBarcodeV5($id, $view = 'barcode', $isMobile = false)
    {
        set_time_limit(0);
        ini_set('memory_limit', '10G');
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);

        $log_error = storage_path('logs/pdf.txt');
        $processUUID = (string) Str::uuid(); // Generate a UUID for this process

        try {
            $barcodeRepository = new BarcodeRepository();
            $barcodePrinted = $barcodeRepository->getBarcodePrintedById($id);
            $items = $this->fetchBarcodeByPrintedID($id);
            Log::info("[Process: $processUUID] id: $id");
            $total_item = count($items);

            if ($barcodePrinted->quantity == 0) {
                Log::info("[Process: $processUUID] {$barcodePrinted->id} quantity = 0, skip");
                $barcodeRepository->updateBarcodePrinted($id, [
                    'convert_status' => 1,
                    'print_status' => 1,
                    'note' => 'skipped',
                    'converted_at' => date('Y-m-d H:i:s')
                ]);

                return false;
            }

            if ($total_item == 0) {
                throw new \Exception('Bug error: item == 0');
            }

            $page = ceil($total_item / 50);
            Log::info("[Process: $processUUID] Total items: $total_item");
            $format_side = [
                0 => 'F',
                1 => 'B',
                2 => 'FB',
            ];
            $render = [];
            $file_number = 1;
            $merge_file = [];
            $path = storage_path('app/public/barcode');
            $first_sku = '';
            $last_sku = '';
            $styleName = null;
            $colorName = null;
            $lastWip = null;

            foreach ($items as $key => &$item) {
                $isThankYouCardOrder = SaleOrderInsert::where([
                    'order_id' => $item->order_id,
                    'type' => SaleOrderInsert::THANKYOU_CARD_TYPE
                ])->exists();
                $item->isThankYouCardOrder = $isThankYouCardOrder;
                $item->isHexa = $this->checkHexa($item);
                $styleName = is_null($styleName) ? $item->style_formatted : $styleName;
                $colorName = is_null($colorName) ? $item->color_formatted : $colorName;
                $item->isLargePlaten = $this->checkPlaten16x21($item);
                $item->isCustomPlaten16x18 = $this->checkPlaten16x18($item);
                $item->counter = $this->counter($item);

                if ($key == 0) {
                    $first_sku = $item->sku;

                    if ($total_item == 1) {
                        $last_sku = $item->sku;
                        $lastWip = $item->label_id;
                    }
                } elseif ($key == ($total_item - 1)) {
                    $last_sku = $item->sku;
                    $lastWip = $item->label_id;
                }

                $item->print_side_formatted = ProductPrintSide::getByArrayCodeWip(str_split($format_side[$item->print_side]), $item->product_style_id);

                if (!is_null($item->print_sides)) {
                    $arraySides = str_split($item->print_sides);
                    $item->print_side_formatted = ProductPrintSide::getByArrayCodeWip($arraySides, $item->product_style_id);
                }

                $item->order_date = $item->order_date ?? date('Y-m-d');
                $item->order_date_formatted = Carbon::parse($item->created_at, 'UTC')->setTimezone('America/Los_Angeles')->format('m/d');
                $item->ink_color_detected_at = Carbon::parse($item->ink_color_detected_at, 'UTC')->setTimezone('America/Los_Angeles')->format('mdH');
                $item->isSingle = false;
                $item->labelIdText = '';

                if ($item->label_id) {
                    $labelIdText = explode('-', $item->label_id);

                    if (!isset($labelIdText[2])) {
                        Log::error("[Process: $processUUID] Label ID formatting issue.", ['item' => $item]);
                    }

                    if ($labelIdText[2] == 'S') {
                        $item->isSingle = true;
                    }

                    $labelIdText = $labelIdText[3] . '-' . $labelIdText[4];
                    $item->labelIdText = $labelIdText;
                }

                if ($item->account_id == 1) {
                    $qr = QrCode::size(125)->generate($item->label_id);
                    $item->label_2 = null;
                    $item->label_1 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr) . '"  width="125" height="125" />';
                    $item->barcode_qr = $item->label_1;
                } else {
                    $qr = QrCode::size(120)->generate($item->label_id);
                    $item->label_2 = null;
                    $item->label_1 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr) . '"  width="120" height="120" />';
                    $item->barcode_qr = $item->label_1;
                }

                if ($item->print_side == 2) {
                    $qr2 = QrCode::size(120)->generate($item->label_id . '-1');
                    $item->label_2 = '<img src="data:image/svg+xml;base64,' . base64_encode($qr2) . '"  width="120" height="120" />';
                }

                $item->label_id = str_replace('SJ', 'RB', $item->label_id);
                $render[] = $item;

                if (count($render) == 50 || $key + 1 == $total_item) {
                    app()->make('view.engine.resolver')->register('blade', function () {
                        return new \Illuminate\View\Engines\CompilerEngine(app()->get('blade.compiler'));
                    });

                    $pdf = PDF::loadView($view, ['items' => $render])
                        ->setOptions(['dpi' => 203, 'logOutputFile' => storage_path('logs/pdf.log'), 'tempDir' => storage_path('logs/')])
                        ->setPaper([0, 0, 2.25 * 72, 1.25 * 72]);
                    $save_file = $page == 1 ? "$path/{$id}.pdf" : "$path/{$id}-{$file_number}.pdf";
                    $pdf->setWarnings(true)->save($save_file);
                    Log::info("[Process: $processUUID] page: $file_number/$page");
                    $pdf = null;
                    $merge_file[] = $save_file;
                    $barcodeRepository->updateBarcodePrinted($id, ['convert_percent' => $key + 1]);
                    $file_number++;
                    $render = [];
                }
            }

            if (count($merge_file) > 1) {
                $unite = new PdfUnite();
                $merge_file[] = "$path/$id.pdf";
                $unite->join(...$merge_file);

                foreach ($merge_file as $key => $item) {
                    if ($key + 1 == count($merge_file)) {
                        break;
                    }

                    Log::info("[Process: $processUUID] Deleting file: $item");
                    unlink($item);
                }
            }

            $pathPdf = $isMobile ? "$path/$id-merge.pdf" : "$path/$id.pdf";

            if (file_exists("$path/$id.pdf")) {
                $s3 = Storage::disk('s3')->put("/barcode/$id.pdf", file_get_contents($pathPdf));

                if ($s3) {
                    Log::info("[Process: $processUUID] Upload to S3 completed for $id");
                    $barcodeRepository->updateBarcodePrinted($id, [
                        'convert_status' => 1,
                        'last_sku' => $last_sku,
                        'first_sku' => $first_sku,
                        'converted_at' => date('Y-m-d H:i:s')
                    ]);
                    unset($s3);
                    File::delete($pathPdf);
                } else {
                    throw new \Exception("[Process: $processUUID] Upload to S3 failed for $id");
                }
            } else {
                $barcodeRepository->updateBarcodePrinted($id, [
                    'convert_status' => 2,
                    'converted_at' => date('Y-m-d H:i:s')
                ]);
                throw new \Exception("[Process: $processUUID] Error converting $id");
            }
        } catch (\Exception $e) {
            $barcodeRepository->updateBarcodePrinted($id, [
                'convert_status' => 2,
                'converted_at' => date('Y-m-d H:i:s')
            ]);

            Log::error("[Process: $processUUID] RbtRepository.convertBarcodeV5", ['exception' => $e]);
            file_put_contents($log_error, date('Y-m-d H:i:s'), FILE_APPEND);
            file_put_contents($log_error, $e->getMessage(), FILE_APPEND);
            file_put_contents($log_error, $e->getTraceAsString(), FILE_APPEND);
        }

        Log::info("[Process: $processUUID] RbtRepository.convertBarcodeV5 conversion done, id: $id");

        return ['last_wip_id' => $lastWip];
    }

    public function checkPlaten16x21($item)
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('custom_platen', '16x21')
            ->first();

        return !empty($image) && $item->store_id != Store::PRINTIFY_API_ID;
    }

    public function checkHexa($item)
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('is_og', SaleOrderItemImage::IS_OG)
            ->where('is_purple', '!=', SaleOrderItemImage::IS_PURPLE)
            ->first();

        return !empty($image);
    }

    public function counter($item)
    {
        $settingOrderDate = Setting::query()->where('name', 'order_date_counter_wip')->first();
        $saleOrder = SaleOrder::find($item->order_id);
        $orderDate = convertTimeUTCToPST($saleOrder->created_at);
        $startOrderDate = Carbon::parse($settingOrderDate->value . ' 00:00:00', 'America/Los_Angeles');

        if ($startOrderDate->greaterThan($orderDate)) {
            return null;
        } else {
            $diffHour = $startOrderDate->diffInHours($orderDate);
            $result = (intval($diffHour / 6) + 1) % 40;

            return $result == 0 ? 40 : $result;
        }
    }

    public function createInternal($input)
    {
        DB::beginTransaction();

        try {
            $employeeCode = !empty($input['employee_id']) ? $input['employee_id'] : RbtProduct::RBT_EMPLOYEE_CODE;
            $employee = Employee::where('code', $employeeCode)
                ->where('is_deleted', Employee::NOT_DELETED)
                ->first();
            if (empty($employee)) {
                DB::rollBack();

                return $this->handleFail('Employee not found or has been deleted.');
            }

            $input['warehouse_id'] = $employee->warehouse_id;
            $totalInRack = Box::join('location', function ($join) {
                $join->on('box.location_id', '=', 'location.id');
            })
                ->where('location.warehouse_id', $input['warehouse_id'])
                ->where('location.is_deleted', Location::NOT_DELETED)
                ->where('location.type', Location::RACK)
                ->where('product_id', $input['product_id'])
                ->where('box.is_deleted', Box::NOT_DELETED)
                ->count('box.id');
            $incomingStock = ProductQuantity::where('warehouse_id', $input['warehouse_id'])
                ->where('product_id', $input['product_id'])->first();

            if (($incomingStock->incoming_stock ?? 0) > 0 || $totalInRack >= $input['box_quantity']) {
                $result = [];

                for ($i = 1; $i <= $input['box_quantity']; $i++) {
                    $insertRequest = InternalRequest::create([
                        'product_id' => $input['product_id'],
                        'employee_create_id' => $employee->id,
                        'warehouse_id' => 1,
                        'priority' => $input['priority'] ?? 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_rbt' => InternalRequest::IS_RBT,
                    ]);
                    $this->logInternalRequestHistory($input['employee_id'], $insertRequest->id, InternalRequestHistory::ACTION_CREATE);

                    $result[] = $insertRequest->id;
                }

                internalRequestEmit(InternalRequest::EVENT_CREATE, $insertRequest->warehouse_id);
                DB::commit();

                return $this->handleSuccess('', $result);
            } elseif (($incomingStock->incoming_stock ?? 0) <= 0 && $totalInRack <= 0) {
                DB::rollBack();

                return $this->handleFail('This SKU is out of stock. Please reach out to the relevant department for further assistance.');
            } else {
                DB::rollBack();

                return $this->handleFail('This SKU does not have enough quantity available in the rack to fulfill your request. Please contact the relevant department for further assistance.');
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('RbtRepository.createInternal', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function logInternalRequestHistory($employeeId, $internalRequestId, $action)
    {
        try {
            $internalRequestHistory = InternalRequestHistory::create([
                'internal_request_id' => $internalRequestId,
                'employee_id' => $employeeId,
                'action' => $action,
            ]);

            return $this->handleSuccess('success', $internalRequestHistory);
        } catch (\Exception $exception) {
            Log::error('RbtRepository.logInternalRequestHistory', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function createOrders($data)
    {
        $date = Carbon::now()->timestamp;
        $externalId = 'RBT' . '.' . $date;
        $listRbtSku = RbtProduct::select('p.id', 'p.sku')
            ->join('product as p', 'p.id', '=', 'rbt_product.product_id')
            ->get();
        $store = Store::find(SaleOrder::STORE_RBT);
        $orderCount = $data['total_orders'] ?? 50;

        for ($i = 0; $i < $orderCount; $i++) {
            $random = rand(1, 3);
            $saleOrder = SaleOrder::factory([
                'external_number' => $externalId . '.' . $i,
                'store_id' => SaleOrder::STORE_RBT,
                'order_quantity' => $random,
                'created_at' => now(),
                'warehouse_id' => 1,
                'shipping_method' => StoreShipment::SERVICE_STANDARD,
                'order_status' => SaleOrder::STATUS_NEW_ORDER,
                'source' => 'api',
                'order_date' => Carbon::now()->format('Y-m-d'),
                'order_number' => null,
                'order_time' => Carbon::now()->toDateTimeString(),
                'account_id' => $store->account_id
            ])
                ->has(SaleOrderAddress::factory([
                    'type_address' => SaleOrderAddress::TO_ADDRESS,
                    'name' => 'Lynda Adner',
                    'email' => '*********',
                    'company' => '676767',
                    'phone' => '******-419-8616 ext. 60877',
                    'street1' => '5 N CLARKSON AVE',
                    'street2' => '',
                    'city' => 'MASSENA',
                    'state' => 'NY',
                    'country' => 'US',
                    'zip' => '13662-1764'
                ]), 'address')
                ->has(SaleOrderAddress::factory([
                    'type_address' => SaleOrderAddress::RETURN_ADDRESS,
                    'name' => 'Lynda Adner',
                    'email' => '*********',
                    'company' => '676767',
                    'phone' => '******-419-8616 ext. 60877',
                    'street1' => '5 N CLARKSON AVE',
                    'street2' => '',
                    'city' => 'MASSENA',
                    'state' => 'NY',
                    'country' => 'US',
                    'zip' => '13662-1764'
                ]), 'address')  // Adding the RETURN_ADDRESS
                ->has(
                    SaleOrderItem::factory()->count($random)->state(
                        function (array $attributes) use ($listRbtSku, $i, $externalId) {
                            // Select a random SKU
                            $randomSku = $listRbtSku->random();

                            return [
                                'warehouse_id' => 1,
                                'store_id' => SaleOrder::STORE_RBT,
                                'quantity' => 1,
                                'product_id' => $randomSku->id,
                                'product_sku' => $randomSku->sku,
                                'product_style_sku' => substr($randomSku->sku, 0, 4),
                                'product_color_sku' => substr($randomSku->sku, 4, 2),
                                'product_size_sku' => substr($randomSku->sku, 6, 3),
                                'product_style_color_sku' => substr($randomSku->sku, 0, 6),
                                'external_id' => $externalId . '.' . $i,
                                'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"},{"name":"PreviewFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"}]'
                            ];
                        },
                    ),
                    'items',
                )
                ->create();
            // Assign custom SKU format for each SaleOrderItem
            foreach ($saleOrder->items as $saleOrderItem) {
                $sku = 'S' . $saleOrderItem->id . $saleOrderItem->product_sku;  // Custom SKU format
                $saleOrderItem->sku = $sku;
                $saleOrderItem->save();  // Save the updated SKU
            }
        }

        return true;
    }

    public function buildBaseQueryGetWip(): Builder
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();

        return SaleOrderItemBarcode::query()
            ->selectRaw('
                sale_order_item_barcode.id,
                sale_order_item_barcode.order_item_id,
                sale_order_item_barcode.label_id as label_id,
                sale_order_item_barcode.barcode_number as barcode_number,
                sale_order_item_barcode.barcode_printed_id as barcode_printed_id,
                sale_order_item_barcode.label_root_id as label_root_id,
                sale_order_item_barcode.employee_reroute_id as employee_reroute_id,
                sale_order_item_barcode.employee_reprint_id as employee_reprint_id,
                sale_order_item_barcode.employee_discard_id as employee_discard_id,
                sale_order_item_barcode.discarded_at as discard_at,
                sale_order_item_barcode.is_deleted as is_deleted,
                sale_order_item_barcode.pulled_at as pulled_at,
                sale_order_item_barcode.created_at as created_at,
                sale_order_item_barcode.kitted_at as kitted_at,
                sale_order.created_at as sale_order_created_at,
                sale_order_item_barcode.updated_at as updated_at,
                sale_order.id as order_id,
                sale_order.order_type as order_type,
                sale_order.is_eps as order_is_eps,
                sale_order.is_manual as order_is_manual,
                sale_order.is_xqc as order_is_xqc,
                sale_order.order_status as order_status,
                sale_order.order_quantity as order_quantity,
                sale_order_item.product_color_sku as order_item_product_color_sku,
                product.sku as product_sku,
                product.style as product_style,
                product.color as product_color,
                product.size as product_size,
                rbt_wip_received.created_at as rbt_wip_received_created_at,
                sale_order_kitting.id as kitting_id,
                product_style.gender as product_style_gender,
                COALESCE (
                    (
                        SELECT label_reprint.employee_reprint_id
                        FROM sale_order_item_barcode AS label_reprint
                        WHERE label_reprint.label_root_id = sale_order_item_barcode.label_root_id
                          AND label_reprint.reprint_status = 1
                        ORDER BY label_reprint.id DESC
                        LIMIT 1
                    ),
                    (
                        SELECT label_root.employee_reprint_id
                        FROM sale_order_item_barcode AS label_root
                        WHERE label_root.label_id = sale_order_item_barcode.label_root_id
                          AND label_root.reprint_status = 1
                        ORDER BY label_root.id DESC
                        LIMIT 1
                    )
                ) AS last_employee_reprint,
                sale_order_reprint.id as order_reprint_id
            ')
            ->with(['orderItem.images:id,order_item_id,print_side,custom_platen'])
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->leftJoin('rbt_wip_received', 'rbt_wip_received.label_id', '=', 'sale_order_item_barcode.label_id')
            ->leftJoin('sale_order_kitting', 'sale_order_kitting.sale_order_item_barcode_id', '=', 'sale_order_item_barcode.id')
            ->leftJoin('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('sale_order_reprint', 'sale_order_reprint.order_id', 'sale_order_item_barcode.order_id')
            ->where('sale_order_item.ink_color_status', '<>', SaleOrderItem::INACTIVE)
            ->where('sale_order_item_barcode.is_deleted', SaleOrder::INACTIVE)
            ->where('sale_order.warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->whereNotIn('sale_order.order_status', [
                SaleOrder::STATUS_ON_HOLD,
                SaleOrder::STATUS_REJECT,
                SaleOrder::STATUS_CANCELLED,
                SaleOrder::STATUS_LATE_CANCELLED,
            ])
            ->where('sale_order.id', '>=', $orderId)
            ->where('sale_order_item.id', '>=', $orderItemId)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId);
    }

    public function fetchWips($input)
    {
        $baseQuery = $this->buildBaseQueryGetWip()
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT);

        if (!empty($input['status_received']) && $input['status_received'] == 'received') {
            $baseQuery->whereExists(function ($sub) {
                $sub->select(DB::raw(1))
                    ->from('rbt_wip_received')
                    ->whereColumn('rbt_wip_received.label_id', 'sale_order_item_barcode.label_id')
                    ->where('rbt_wip_received.created_at', '>=', Carbon::now()->subDays(30));
            });
        }

        if (!empty($input['status_received']) && $input['status_received'] == 'not_received') {
            $baseQuery->whereNotExists(function ($sub) {
                $sub->select(DB::raw(1))
                    ->from('rbt_wip_received')
                    ->whereColumn('rbt_wip_received.label_id', 'sale_order_item_barcode.label_id')
                    ->where('rbt_wip_received.created_at', '>=', Carbon::now()->subDays(30));
            });
        }

        $subQuery1 = $baseQuery->clone()
            ->where('sale_order_item_barcode.barcode_printed_id', 0);
        $subQuery2 = $baseQuery->clone()
            ->leftJoin('barcode_printed', 'barcode_printed.id', '=', 'sale_order_item_barcode.barcode_printed_id')
            ->where('barcode_printed.print_status', '<>', BarcodePrinted::PRINTED_STATUS);
        $query = $subQuery1->union($subQuery2)
            ->orderBy('created_at');
        $totalCount = $query->count();

        // Get total count before pagination
        $limit = $input['limit'];
        $currentPage = $input['page'] ?? 1;
        $lastPage = (int) ceil($totalCount / $limit);
        $paginatedData = $query->simplePaginate($limit)
            ->through(fn ($item) => $this->buildWip($item))
            ->withQueryString();

        // Manually build pagination links
        $links = [];

        for ($page = 1; $page <= $lastPage; $page++) {
            $links[] = [
                'url' => $paginatedData->url($page),
                'label' => (string) $page,
                'active' => $page === $currentPage,
            ];
        }

        return [
            'current_page' => $currentPage,
            'data' => $paginatedData->items(),
            'first_page_url' => $paginatedData->url(1),
            'from' => $paginatedData->firstItem(),
            'last_page' => $lastPage,
            'last_page_url' => $paginatedData->url($lastPage),
            'links' => $links,
            'next_page_url' => $paginatedData->nextPageUrl(),
            'path' => $paginatedData->path(),
            'per_page' => (int) $limit,
            'prev_page_url' => $paginatedData->previousPageUrl(),
            'to' => $paginatedData->lastItem(),
            'total' => $totalCount,
        ];
    }

    public function buildWip($item)
    {
        $result = [
            'wip_id' => $item->label_id,
            'wip_sequence' => $item->barcode_number,
            'order_id' => $item->order_id ?? null,
            'order_status' => $item->order_status,
            'order_created_at' => Carbon::parse($item->sale_order_created_at)->toISOString(),
            'total_order_quantity' => $item->order_quantity ?? 0,
            'batch_id' => $item->barcode_printed_id,
            'sku' => $item->product_sku,
            'style' => $item->product_style,
            'color' => $item->product_color,
            'size' => $item->product_size,
            'is_deleted' => $item->is_deleted,
            'pulled_at' => $item->pulled_at,
            'received_at' => $item->rbt_wip_received_created_at
                ? Carbon::parse($item->rbt_wip_received_created_at)->toISOString()
                : null,
            'wip_created_at' => Carbon::parse($item->created_at)->toISOString(),
            'updated_at' => $item->updated_at ? Carbon::parse($item->updated_at)->toISOString() : null,
            'kitted_at' => $item->kitted_at ? Carbon::parse($item->kitted_at)->toISOString() : null,
            'wip_tags' => $this->getWipTags($item),
        ];

        return $result;
    }

    public function getWipTags($item): array
    {
        $tags = [];

        if (in_array($item->order_type, [SaleOrder::ORDER_TYPE_LABEL_ORDER, SaleOrder::ORDER_TYPE_TIKTOK_ORDER])) {
            $tags[] = self::TAG_TIKTOK;
        }

        if ($item->order_quantity >= env('BULK_ORDER_MIN_ITEM_QUANTITY', 20)) {
            $tags[] = self::TAG_BULK;
        } else {
            $tags[] = $item->order_quantity > 1 ? self::TAG_MULTIPLE : self::TAG_SINGLE;
        }

        if (!empty($item->order_is_eps)) {
            $tags[] = self::TAG_EXPRESS;
        }

        if (!empty($item->order_reprint_id)
            || (!empty($item->label_root_id)
                && empty($item->employee_reroute_id) && empty($item->order_is_manual) && empty($item->employee_discard_id))
        ) {
            $tags[] = self::TAG_REPRINT;
        }

        if (!empty($item->order_is_manual)) {
            $tags[] = self::TAG_MANUAL;
        }

        if (!empty($item->order_is_xqc)) {
            $tags[] = self::TAG_SAMPLE;
        }

        $printSideNeckCodes = [5, 6];
        $neckItem = $item?->orderItem?->images->whereIn('print_side', $printSideNeckCodes)->isNotEmpty();

        if (!empty($neckItem)) {
            $tags[] = self::TAG_NECK_PRINT;
        }

        $printSideSleeveCodes = [3, 4, 30, 31];
        $sleeveItem = $item?->orderItem?->images->whereIn('print_side', $printSideSleeveCodes)->isNotEmpty();

        if (!empty($sleeveItem)) {
            $tags[] = self::TAG_SLEEVE;
        }

        $printSidePocketCodes = [2, 7, 15, 16];
        $pocketItem = $item?->orderItem?->images->whereIn('print_side', $printSidePocketCodes)->isNotEmpty();

        if (!empty($pocketItem)) {
            $tags[] = self::TAG_POCKET;
        }

        if ($item->product_style_gender == self::TAG_BABY) {
            $tags[] = self::TAG_BABY;
        }

        if ($item->order_item_product_color_sku = '1W') {
            $tags[] = self::TAG_NO_PRETREAT;
        }

        // 16x18 tag
        if ($item?->orderItem?->images->where('custom_platen', '16x18')->isNotEmpty()) {
            $tags[] = self::TAG_16X18;
        }

        return $tags;
    }

    public function getWip($id)
    {
        $query = $this->buildBaseQueryGetWip()
            ->where('sale_order_item_barcode.label_id', $id)
            ->first();

        if (!$query) {
            return false;
        }

        return $this->buildWip($query);
    }

    public function createAdjustment($request, $isManual = false)
    {
        $product = Product::where('id', $request['product_id'])
            ->primary()
            ->active()
            ->first();

        if (!$product) {
            return false;
        }

        $locationId = $request['location_id'];
        $locationProduct = LocationProduct::where([
            'location_id' => $locationId,
            'product_id' => $product->id
        ])->first();
        $availableQuantity = $locationProduct ? $locationProduct->quantity : 0;
        $actualQuantity = $request['quantity'];
        $adjustQuantity = $actualQuantity - $availableQuantity;
        $direction = $adjustQuantity > 0 ? Inventory::DIRECTION_INPUT : Inventory::DIRECTION_OUTPUT;
        $purchaseOrderRepository = new PurchaseOrderRepository();
        $price = $purchaseOrderRepository->getLatestProductPriceFromPo($product->id);
        $userId = empty(\auth()->user()->id) ? $request['employee_id'] : \auth()->user()->id;
        $adjustPullingShelves = AdjustShelvesFace::create([
            'user_id' => $userId,
            'warehouse_id' => $request['warehouse_id'],
            'product_id' => $product->id,
            'sku' => $product->sku,
            'product_available' => $availableQuantity,
            'product_on_hand' => $actualQuantity,
            'product_adjust' => $adjustQuantity,
            'employee_id' => $request['employee_id'],
            'country' => null,
            'cost_value_on_hand' => $actualQuantity * $price,
            'cost_value_adjusted' => $adjustQuantity * $price,
            'cost_value_available' => $availableQuantity * $price,
            'type' => Location::MOVING_SHELVES
        ]);
        Inventory::create([
            'direction' => $direction,
            'type' => Inventory::TYPE_ADJUST,
            'product_id' => $product->id,
            'warehouse_id' => $request['warehouse_id'],
            'location_id' => $locationId,
            'user_id' => $userId,
            'object_id' => $adjustPullingShelves->id,
            'object_name' => Inventory::OBJECT_ADJUST_SHELVE_FACE,
            'quantity' => abs($adjustQuantity),
            'cost_total' => $direction == Inventory::DIRECTION_INPUT ? $adjustQuantity * $price : 0,
        ]);
        ProductQuantityRepository::updateQuantityRbt($request['warehouse_id'], $product->id, $adjustQuantity);
        LocationProductRepository::updateQuantityRbt($locationId, $product->id, $isManual ? $actualQuantity : 0);

        return [
            'previous_stock' => $availableQuantity,
            'quantity_adjusted' => $adjustQuantity,
            'updated_stock' => $actualQuantity
        ];
    }

    public function receivedBox($box, $internalRequest, $location, $data = [])
    {
        DB::beginTransaction();

        try {
            $employeeCode = !empty($data['employee_id']) ? $data['employee_id'] : RbtProduct::RBT_EMPLOYEE_CODE;
            $employee = Employee::where('code', $employeeCode)
                ->where('is_deleted', Employee::NOT_DELETED)
                ->first();
            $box = Box::with('location')->find($internalRequest->box_id);
            $inputBoxMoving = [
                'barcode' => $box->barcode,
                'location_type' => $location->type,
                'location_barcode' => $location->id,
                'employee_id' => $employee->code,
                'is_internal_request' => true,
                'warehouse_id' => 1,
            ];
            if (isset($data['quantity'])) {
                $inputBoxMoving['quantity'] = $data['quantity'];
                $internalRequest->dark_pod_quantity = $data['quantity'];
            } else {
                $inputBoxMoving['quantity'] = $box->quantity;
                $internalRequest->dark_pod_quantity = $box->quantity;
            }
            if (isset($data['station_id'])) {
                $internalRequest->station_id = $data['station_id'];
            }
            $result = $this->createBoxMoving($inputBoxMoving);

            if ($result->getStatusCode() == Response::HTTP_CREATED) {
                $internalRequest->status = InternalRequest::CHECKED_STATUS;
                $internalRequest->employee_confirm_id = $employee->id;
                $internalRequest->confirmed_at = now();

                $internalRequest->save();
                $this->logInternalRequestHistory($employee->id, $internalRequest->id, InternalRequestHistory::ACTION_CONFIRM);
            }

            internalRequestEmit(InternalRequest::EVENT_CONFIRM, $internalRequest->warehouse_id);
            $box->is_deleted = true;
            $box->save();
            if (!empty($data['id_time_checking'])) {
                $endTime = Carbon::now()->toDateTimeString();
                $timeCheckingRepository = app()->make(TimeCheckingRepository::class);
                $timeCheckingRepository->updateTimeChecking(['end_time' => $endTime], $data['id_time_checking']);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('RbtRepository.receivedBox', [$exception]);

            return $this->handleFail($exception->getMessage());
        }
    }

    public function fetchBarcodeByPrintedID($id)
    {
        $st = DB::table('sale_order_item_barcode')
            ->select([DB::Raw('sale_order_item_barcode.label_id, sale_order_item_barcode.id,
            sale_order_item_barcode.sku ,sale_order_item_barcode.order_id,
            sale_order.is_xqc,
            sale_order.created_at,
            sale_order.is_eps,
            sale_order_item_barcode.barcode_number,
            sale_order_item_barcode.order_quantity,
            product.color color_formatted,
            product.size size_formatted,
            product.style style_formatted,
            product_style.id product_style_id,
            product_style.type product_type,
            product_style.sku product_style_sku,
            sale_order_account.name account_name,
            sale_order.account_id,
            store.name store_name,
            store.code store_code,
            store.id store_id,
            store.is_resize is_resize,
            sale_order.external_number order_external_number,
            sale_order.is_fba_order is_fba,
            sale_order.order_type,
            sale_order.order_date,
            sale_order_item.print_side,
            sale_order_item.print_sides,
            sale_order_item.ink_color,
            sale_order_item.ink_color_detected_at,
            sale_order_item.id as order_item_id,
            sale_order_item_barcode.print_method,
            pretreat_preset.preset_name pretreat_name,
            latest_batch.batch_number,
            sale_order.is_manual,
            sale_order_item.is_rbt'),
                DB::raw('(
                    SELECT barcode FROM location
                    WHERE location.rbt_sku = sale_order_item.product_sku
                    LIMIT 1
                ) as location_barcode')
            ])
            ->join('sale_order_item', function ($join) {
                $join->on('sale_order_item.id', 'sale_order_item_barcode.order_item_id')
                    ->leftJoin('pretreat_preset_sku', function ($subJoin) {
                        $subJoin->on('sale_order_item.product_style_sku', 'pretreat_preset_sku.style');
                        $subJoin->on('sale_order_item.product_color_sku', 'pretreat_preset_sku.color');
                        $subJoin->leftJoin('pretreat_preset', 'pretreat_preset.id', '=', 'pretreat_preset_sku.pretreat_preset_id');
                    });
            })
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_account', 'sale_order_account.id', '=', 'sale_order.account_id')
            ->leftJoin('store', 'store.id', '=', 'sale_order.store_id')
            ->leftJoin(DB::raw('
            (SELECT rbt1.label_id, rbt1.batch_number
             FROM rbt_batch_number rbt1
             WHERE rbt1.created_at = (
                 SELECT MAX(rbt2.created_at)
                 FROM rbt_batch_number rbt2
                 WHERE rbt2.label_id = rbt1.label_id
             )
            ) AS latest_batch'),
                'latest_batch.label_id', '=', 'sale_order_item_barcode.label_id',
            )
            ->where('barcode_printed_id', $id)
            ->groupBy('sale_order_item_barcode.label_id');
        $st->orderBy('latest_batch.batch_number', 'ASC');

        return $st->get();
    }

    public function createBoxMoving($input): JsonResponse
    {
        if (!empty($input['is_internal_request'])) {
            $warehouseId = $input['warehouse_id'];
        } else {
            $warehouseId = config('jwt.warehouse_id');
        }

        $box = Box::with('location')
            ->where('barcode', $input['barcode'])
            ->where('warehouse_id', $warehouseId)
            ->active()
            ->first();
        $newLocation = Location::find(id: $input['location_barcode']);

        if (empty($box)) {
            return response()->json(['barcode' => ['The selected barcode is not found or deleted.']], 422);
        }

        if (empty($newLocation)) {
            return response()->json(['location' => ['The selected location is not found or deleted.']], 422);
        }

        if (isset($box->location->type) && $box->location->type === Location::PULLING_SHELVES) {
            return response()->json(['barcode' => ["This box has been moved to pulling shelves. You can't move it to another location"]], 422);
        }

        $boxMoving = BoxMoving::where('box_id', $box->id)->latest('id')->first();

        if (empty($boxMoving)) {
            return response()->json(['barcode' => ['Cannot create box moving !']], 422);
        }

        try {
            DB::transaction(function () use ($input, $newLocation, $box, $boxMoving, $warehouseId) {
                $pre_location_id = $boxMoving->location_id;
                $new_location_id = $newLocation->id;
                $product_id = $boxMoving->product_id;
                $quantity = $input['quantity'] ?? $boxMoving->quantity;
                $inventory_addition_id = $boxMoving->inventory_addition_id;
                BoxMoving::create([
                    'box_id' => $box->id,
                    'pre_location_id' => $pre_location_id,
                    'location_id' => $new_location_id,
                    'warehouse_id' => $warehouseId,
                    'user_id' => auth()->user()['id'] ?? 0, // 0 được hiểu là action của hệ thống hiện đang sự trong tính năng fulfill internal request
                    'product_id' => $product_id,
                    'inventory_addition_id' => $inventory_addition_id,
                    'quantity' => $quantity,
                    'employee_id' => $input['employee_id'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                $box = Box::find($box->id);
                $box->location_id = $new_location_id;
                $box->save();

                if ($pre_location_id > 0) {
                    LocationProductRepository::updateQuantity($pre_location_id, $product_id, -1 * $quantity);
                }

                LocationProductRepository::updateQuantity($new_location_id, $product_id, $quantity);

                //todo:  update time end for time checking
                $timeCheckingRepository = new TimeCheckingRepository();

                if (!empty($input['id_time_checking'])) {
                    $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);
                }
            });
        } catch (\Exception $exception) {
            Log::error('RbtRepository.createBoxMoving', [$exception]);

            return response()->json($exception->getMessage(), 500);
        }

        $boxMovingResult = BoxMoving::with('product')->latest('id')->first();
        $boxMovingResult->cost_value = $box->cost_value;

        return response()->json($boxMovingResult, 201);
    }

    public function deduction($data)
    {
        DB::beginTransaction();

        try {
            $barcodePrinted = BarcodePrinted::where('id', $data['barcode_id'])
                ->where('print_status', BarcodePrinted::ACTIVE)
                ->whereNull('pulled_at')
                ->firstOrFail();
            $locationPullingShelve = Location::find($data['location_id']);
            $saleOrderItemBarcodes = SaleOrderItemBarcode::with('saleOrder')
                ->where('is_deleted', 0)
                ->where('barcode_printed_id', $data['barcode_id'])
                ->get();
            $dataOrderHistory = [];

            foreach ($saleOrderItemBarcodes as $saleOrderItemBarcode) {
                if (in_array($saleOrderItemBarcode->saleOrder->order_status, [
                    SaleOrder::DRAFT,
                    SaleOrder::ON_HOLD,
                    SaleOrder::CANCELLED,
                    SaleOrder::REJECTED,
                    SaleOrder::STATUS_LATE_CANCELLED
                ])) {
                    continue;
                }

                if ($saleOrderItemBarcode->pulled_at) {
                    continue;
                }

                $sku = substr($saleOrderItemBarcode->sku, -9);
                $product = Product::where('sku', $sku)->first();

                if (!$product) {
                    continue;
                }

                $input = [
                    'sale_order_sku' => $saleOrderItemBarcode->sku,
                    'label_id' => $saleOrderItemBarcode->label_id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'location_id' => $locationPullingShelve->id,
                    'user_id' => $barcodePrinted->user_id,
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'employee_id' => $barcodePrinted->employee_id,
                    'created_at' => now(),
                    'sale_order_id' => $saleOrderItemBarcode->order_id,
                ];
                $inventoryDeduction = InventoryDeduction::create($input);
                $saleOrderItemBarcode->employee_pull_id = $barcodePrinted->employee_id;
                $saleOrderItemBarcode->pulled_at = now();
                $saleOrderItemBarcode->save();
                $inputInventory = [
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_OUTPUT,
                    'product_id' => $input['product_id'],
                    'warehouse_id' => $saleOrderItemBarcode->warehouse_id,
                    'location_id' => $input['location_id'],
                    'user_id' => $input['user_id'],
                    'object_id' => $inventoryDeduction->id,
                    'object_name' => Inventory::OBJECT_DEDUCTION,
                    'quantity' => $input['quantity'],
                ];
                Inventory::create($inputInventory);
                ProductQuantityRepository::updateQuantity($input['warehouse_id'], $input['product_id'], -$input['quantity']);
                LocationProductRepository::updateQuantity($input['location_id'], $input['product_id'], -$input['quantity']);
                $this->updatePulledAtStatus($saleOrderItemBarcode->saleOrder);
                // log time line cho sale order

                if (isset($dataOrderHistory[$saleOrderItemBarcode->order_id])) {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $dataOrderHistory[$saleOrderItemBarcode->order_id] . ', ' . $saleOrderItemBarcode->label_id;
                } else {
                    $dataOrderHistory[$saleOrderItemBarcode->order_id] = $saleOrderItemBarcode->label_id;
                }

                handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->order_id);
            }

            $dataImportHistory = [];
            $barcodeId = $data['barcode_id'];

            foreach ($dataOrderHistory as $key => $value) {
                $dataImportHistory[] = [
                    'user_id' => null,
                    'employee_id' => null,
                    'order_id' => $key,
                    'type' => SaleOrderHistory::UPDATE_ORDER_WIP_TYPE,
                    'message' => "Deduction label ID $value in batch ID $barcodeId",
                    'created_at' => Carbon::now()->toDateTimeString()
                ];
            }

            if (!empty($dataImportHistory)) {
                SaleOrderHistory::insert($dataImportHistory);
            }

            $barcodePrinted->pulled_at = now();
            $barcodePrinted->save();

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error('RbtRepository.deduction', [$exception]);
        }
    }

    private function updatePulledAtStatus($order)
    {
        if ($order->order_quantity > 1) {
            return false;
        }

        $remainingBarcodes = SaleOrderItemBarcode::query()
            ->where('order_id', $order->id)
            ->where('is_deleted', 0)
            ->whereNull('pulled_at')
            ->whereNull('employee_pull_id')
            ->count();

        if (!$remainingBarcodes) {
            return $order->update([
                'order_pulled_status' => 1,
                'order_pulled_at' => now()
            ]);
        }

        return false;
    }

    public function getBox($id)
    {
        $box = Box::query()
            ->with(['product'])
            ->where('box.barcode', $id)
            ->where('box.is_deleted', false)
            ->where('box.warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
            ->first();

        if (!$box) {
            return false;
        }

        return [
            'status' => 'success',
            'box_id' => $box->barcode,
            'sku' => $box->product->sku ?? null,
            'units' => $box->quantity ?? 0,
        ];
    }

    public function countConfirmWip($id)
    {
        $listBarcode = RbtBatchNumber::where('batch_id', $id)->pluck('label_id')->toArray();
        $confirmBarcode = SaleOrderItemBarcode::where('barcode_printed_id', $id)->pluck('label_id')->toArray();
        $confirmedWips = array_intersect($listBarcode, $confirmBarcode);
        $discardedWips = array_diff($listBarcode, $confirmBarcode);

        return [
            'status' => 'success',
            'batch_id' => $id,
            'confirmed_wips' => array_values($confirmedWips),
            'discarded_wips' => array_values($discardedWips),
            'total_confirmed_wips' => count($confirmedWips),
            'total_wips' => count($listBarcode),
        ];
    }

    public function InsertRbtPerformance($data)
    {
        $employee = Employee::where('code', $data['employee_id'])->first();
        $timeTrackingId = $data['time_tracking_id'] ?? null;
        $employeeId = $employee->id;
        $jobType = TimeTracking::JOB_TYPE_RBT_PULLING;
        $timeTracking = TimeTracking::where('id', $timeTrackingId)
            ->where('job_type', $jobType)
            ->first();

        if ($timeTracking) {
            $timeTracking->update([
                'end_time' => now(),
                'quantity' => $timeTracking->quantity + $data['total_confirmed_wips']
            ]);
        } else {
            TimeTracking::create([
                'employee_id' => $employeeId,
                'job_type' => $jobType,
                'quantity' => $data['total_confirmed_wips'],
                'start_time' => now(),
                'end_time' => now(),
            ]);
        }

        // Insert or  RbtPerformanceReport
        $currentDate = Carbon::now('America/Los_Angeles')->toDateString();
        $rbtPerformance = RbtPerformanceReport::where('employee_id', $employeeId)
            ->where('date', $currentDate)
            ->where('job_type', $jobType)
            ->first();

        if ($rbtPerformance) {
            $rbtPerformance->update([
                'quantity' => $rbtPerformance->quantity + $data['total_confirmed_wips'],
                'updated_at' => now(), // Cập nhật thời gian hiện tại
            ]);
        } else {
            RbtPerformanceReport::create([
                'employee_id' => $employeeId,
                'job_type' => $jobType,
                'quantity' => $data['total_confirmed_wips'],
                'date' => $currentDate,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    public function preConfirmationCount($data)
    {
        $listWip = RbtBatchNumber::where('batch_id', $data['batch_id'])
            ->orderBy('batch_number', 'asc')
            ->get();
        $lastWipId = $data['last_wip_id'];

        // Get WIPs that come after last_wip_id
        $notPrintedWip = $listWip->skipUntil(fn ($value) => $value->label_id === $lastWipId)
            ->skip(1)
            ->values();

        // Remaining WIPs excluding discarded ones
        $remainingWip = $listWip->reject(fn ($wip) => $notPrintedWip->contains('label_id', $wip->label_id))->values();

        return [
            'status' => 'success',
            'batch_id' => $data['batch_id'],
            'scanned_wips' => $remainingWip->pluck('label_id')->all(),  // Extract label_id only
            'discarded_wips' => $notPrintedWip->pluck('label_id')->all(), // Extract label_id only
            'total_scanned' => $remainingWip->count(),
            'total_discarded' => $notPrintedWip->count(),
            'total_wips' => $listWip->count(),
        ];
    }

    public function rerouteImportCsvFile($input)
    {
        $data = Excel::toArray(new RerouteImport, $input->file)[0];
        $data = array_filter(array_unique(array_map(fn ($item) => trim($item[0] ?? ''), $data)));

        if (empty($data)) {
            return [
                'total' => 0,
                'valid' => 0,
                'invalid' => 0,
            ];
        }

        $total = count($data);

        // Fetch products that exist and don't have ProductRbt
        $products = [];
        Product::whereIn('sku', $data)
            ->doesntHave('ProductRbt')
            ->select('id as product_id', 'sku')
            ->chunk(500, function ($items) use (&$products) {
                $products = array_merge($products, $items->toArray()); // Fixing array merge issue
            });

        // Extract SKUs from valid products
        $productSkus = array_column($products, 'sku');
        $valid = count($products);
        $invalid = $total - $valid;
        $data = [
            'total' => $total,
            'valid' => $valid,
            'invalid' => $invalid,
            'products' => $products,
        ];

        return $this->rerouteUpdateByOrders($data);
    }

    private function rerouteUpdateByOrders($input)
    {
        DB::beginTransaction();

        try {
            $items = array_chunk($input['products'], 500);

            foreach ($items as $batch) {
                $productIds = array_column($batch, 'product_id'); // Extract product IDs

                // Prepare data for insertion
                $insertData = array_map(fn ($id) => [
                    'product_id' => $id,
                    'created_by' => Auth::id()

                ], $productIds);

                // Insert into RbtProduct table
                RbtProduct::insert($insertData);
            }

            DB::commit();
            unset($input['products']);

            return $this->successResponse('Import Skus successfully!', $input);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function removeSkus()
    {
        RbtProduct::query()->delete(); // Delete all records

        return $this->successResponse('Deleted successfully!');
    }

    public function unMarkSaleOrderRbt(array $input): array
    {
        $itemIds = [];

        if (!empty($input['order_id'])) {
            $itemIds = SaleOrderItem::query()
                ->whereIn('order_id', $input['order_id'])
                ->where('is_rbt', SaleOrderItem::IS_RBT)
                ->pluck('id')
                ->toArray();
        }

        if (!empty($input['wip_id'])) {
            $ids = SaleOrderItemBarcode::query()
                ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
                ->whereIn('sale_order_item_barcode.label_id', $input['wip_id'])
                ->groupBy('sale_order_item_barcode.order_item_id')
                ->pluck('sale_order_item_barcode.order_item_id')
                ->toArray();
            $itemIds = array_merge($itemIds, $ids);
        }

        if (empty($itemIds)) {
            return [
                'status' => 'success',
                'updated_count' => 0,
                'wip_ids' => [],
            ];
        }

        $wipIdUpdated = SaleOrderItemBarcode::query()
            ->whereIn('order_item_id', $itemIds)
            ->pluck('label_id');

        SaleOrderItem::query()
            ->whereIn('id', $itemIds)
            ->update(['is_rbt' => SaleOrderItem::IS_NOT_RBT]);

        $orderIds = SaleOrderItem::query()
            ->whereIn('id', $itemIds)
            ->groupBy('order_id')
            ->pluck('order_id');

        $unmarkRbtOrderLevel = SaleOrderItem::query()
            ->selectRaw('
                order_id,
                COUNT(CASE WHEN is_rbt = 0 THEN 1 END) AS qty_not_rbt,
                count(*) as total_item
            ')
            ->whereIn('order_id', $orderIds)
            ->groupBy('order_id')
            ->havingRaw('qty_not_rbt = total_item')
            ->pluck('order_id')
            ->toArray();

        if (empty($unmarkRbtOrderLevel)) {
            return [
                'status' => 'success',
                'updated_count' => $wipIdUpdated->count(),
                'wip_ids' => $wipIdUpdated,
            ];
        }

        $rbtOrders = SaleOrder::query()
            ->whereIn('id', $unmarkRbtOrderLevel)
            ->get();

        foreach ($rbtOrders as $order) {
            $tags = array_filter(
                array_map('trim', explode(',', $order->tag)),
                function ($tag) {
                    return $tag != Tag::TAG_RBT_ID;
                },
            );

            $order->tag = implode(',', $tags);
            $order->is_rbt = SaleOrderItem::IS_NOT_RBT;
            $order->save();
        }

        return [
            'status' => 'success',
            'updated_count' => $wipIdUpdated->count(),
            'wip_ids' => $wipIdUpdated,
        ];
    }

    public function destroyBatch($id, $employeeId)
    {
        $totalWips = SaleOrderItemBarcode::where('barcode_printed_id', $id)->count();

        // Get label_ids before updating
        $discardedWips = SaleOrderItemBarcode::where('barcode_printed_id', $id)
            ->pluck('label_id')
            ->toArray();

        if (!empty($discardedWips)) {
            $this->discard([
                'label_ids' => $discardedWips,
                'employee_id' => $employeeId,
                'batch_id' => $id,
            ]);
        }

        // Update records
        SaleOrderItemBarcode::where('barcode_printed_id', $id)
            ->update(['barcode_printed_id' => 0, 'print_barcode_at' => null]);

        return [
            'status' => 'success',
            'batch_id' => $id,
            'confirmed_wips' => [],
            'discarded_wips' => $discardedWips,
            'total_confirmed_wips' => 0,
            'total_wips' => $totalWips,
        ];
    }

    public function checkPlaten16x18($item): bool
    {
        $image = SaleOrderItemImage::where('order_item_id', $item->order_item_id)
            ->where('custom_platen', '16x18')
            ->first();

        return !empty($image) && $item->store_id == Store::PRINTIFY_API_ID;
    }

    public function reprint($data)
    {
        $employee = Employee::where('code', $data['employee_id'])->first();

        foreach ($data['label_ids'] as $labelId) {
            $barcode = SaleOrderItemBarcode::query()
                ->where('label_id', $labelId)
                ->where('reprint_status', '<>', SaleOrderItemBarcode::REPRINTED)
                ->active()
                ->first();
            $lastBarcodeReprint = null;

            if (!empty($barcode->label_root_id)) {
                $lastBarcodeReprint = SaleOrderItemBarcode::query()
                    ->where(function ($q) use ($barcode) {
                        $q->where('label_root_id', $barcode->label_root_id)
                            ->orWhere('label_id', $barcode->label_root_id);
                    })
                    ->where('reprint_status', '=', SaleOrderItemBarcode::REPRINTED)
                    ->orderByDesc('id')
                    ->first();
            }

            if (!$barcode) {
                continue;
            }

            $newBarcode = $barcode->replicate(['id']);

            // Update existing barcode record
            $barcode->update([
                'is_deleted' => SaleOrderItemBarcode::INACTIVE,
                'reprint_status' => SaleOrderItemBarcode::REPRINTED,
                'reprinted_at' => now(),
                'employee_reprint_id' => $lastBarcodeReprint->employee_reprint_id ?? RbtProduct::RBT_EMPLOYEE_ID,
            ]);

            // Create new barcode
            $label = $this->generateLabelId($barcode->label_id, $barcode->label_root_id);
            $newBarcode->label_id = $label['label_id'];
            $newBarcode->employee_pull_id = null;
            $newBarcode->label_root_id = $label['root_id'];
            $newBarcode->reprint_status = SaleOrderItemBarcode::NOT_REPRINTED;
            $newBarcode->created_at = now();
            $newBarcode->save();

            // Remove label from shipment
            $shipmentItemLabel = ShipmentItemLabel::where('label_id', $barcode->label_id)->first();
            if ($shipmentItemLabel) {
                $shipmentItem = ShipmentItem::where('order_id', $barcode->order_id)
                    ->where('id', $shipmentItemLabel->shipment_item_id)
                    ->first();

                if ($shipmentItem) {
                    if ($shipmentItem->quantity > 1) {
                        $shipmentItem->decrement('quantity');
                    } else {
                        $shipmentItem->delete();
                    }
                }

                $shipmentItemLabel->delete();
            }

            saleOrderHistory(
                auth()->id(),
                $employee->id ?? RbtProduct::RBT_EMPLOYEE_ID,
                $barcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
                'Discarded label ID ' . $labelId . ' in batch ' . $data['batch_id'] . ', created new label ' . $newBarcode->label_id,
            );
        }

        return response()->json($this->handleSuccess('Reprint successfully.'), Response::HTTP_OK);
    }

    public function discard($data)
    {
        $employee = Employee::where('code', $data['employee_id'])->first();

        foreach ($data['label_ids'] as $labelId) {
            $barcode = SaleOrderItemBarcode::query()
                ->where('label_id', $labelId)
                ->where('reprint_status', '<>', SaleOrderItemBarcode::REPRINTED)
                ->active()
                ->first();
            $lastBarcodeReprint = null;

            if (!empty($barcode->label_root_id)) {
                $lastBarcodeReprint = SaleOrderItemBarcode::query()
                    ->where(function ($q) use ($barcode) {
                        $q->where('label_root_id', $barcode->label_root_id)
                            ->orWhere('label_id', $barcode->label_root_id);
                    })
                    ->where('reprint_status', '=', SaleOrderItemBarcode::REPRINTED)
                    ->orderByDesc('id')
                    ->first();
            }

            if (!$barcode) {
                continue;
            }

            $newBarcode = $barcode->replicate(['id']);

            // Update existing barcode record
            $barcode->update([
                'is_deleted' => SaleOrderItemBarcode::INACTIVE,
            ]);

            // Create new barcode
            $label = $this->generateLabelId($barcode->label_id, $barcode->label_root_id);
            $newBarcode->label_id = $label['label_id'];
            $newBarcode->employee_pull_id = null;
            $newBarcode->label_root_id = $label['root_id'];
            $newBarcode->reprint_status = SaleOrderItemBarcode::NOT_REPRINTED;
            $newBarcode->created_at = now();
            $newBarcode->discarded_at = now();
            $newBarcode->employee_discard_id = $employee->id ?? RbtProduct::RBT_EMPLOYEE_ID;
            $newBarcode->save();

            // Remove label from shipment
            $shipmentItemLabel = ShipmentItemLabel::where('label_id', $barcode->label_id)->first();
            if ($shipmentItemLabel) {
                $shipmentItem = ShipmentItem::where('order_id', $barcode->order_id)
                    ->where('id', $shipmentItemLabel->shipment_item_id)
                    ->first();

                if ($shipmentItem) {
                    if ($shipmentItem->quantity > 1) {
                        $shipmentItem->decrement('quantity');
                    } else {
                        $shipmentItem->delete();
                    }
                }

                $shipmentItemLabel->delete();
            }

            saleOrderHistory(
                auth()->id(),
                $employee->id ?? RbtProduct::RBT_EMPLOYEE_ID,
                $barcode->order_id,
                SaleOrderHistory::DISCARD,
                'Discarded label ID ' . $labelId . ' in batch ' . $data['batch_id'] . ', created new label ' . $newBarcode->label_id,
            );
        }

        return response()->json($this->handleSuccess('Reprint successfully.'), Response::HTTP_OK);
    }

    public function generateLabelId($oldLabelId, $rootId)
    {
        $indexLastPhrase = strrpos($oldLabelId, '(');

        if ($indexLastPhrase > 0) {
            $labelTmp = explode('(', $oldLabelId);
            $lastReprint = str_replace(')', '', $labelTmp[1]);
            $nextReprint = $lastReprint + 1;

            return [
                'label_id' => $labelTmp[0] . '(' . $nextReprint . ')',
                'root_id' => $labelTmp[0]
            ];
        } else {
            return [
                'label_id' => $oldLabelId . '(1)',
                'root_id' => $oldLabelId
            ];
        }
    }

    public function formatLabelNotPrinted($ids)
    {
        $saleOrderStatus = [
            SaleOrder::STATUS_ON_HOLD,
            SaleOrder::STATUS_REJECT,
            SaleOrder::STATUS_CANCELLED,
            SaleOrder::STATUS_LATE_CANCELLED,
        ];
        $saleOrderStatusString = implode("','", $saleOrderStatus);

        return SaleOrderItemBarcode::query()
            ->selectRaw("
                sale_order_item_barcode.label_id as wip_id,
                CASE
                    WHEN sale_order_item_barcode.is_deleted = 1 AND sale_order.order_status NOT IN ('$saleOrderStatusString')  THEN 'discarded'
                    ELSE sale_order.order_status
                END as reason
            ")
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->whereIn('sale_order_item_barcode.label_id', $ids)
            ->get()
            ->toArray();
    }

    public function movingReplenishmentToRack($input)
    {
        $box = Box::where('barcode', $input['box_barcode'])
            ->first();

        if ($box) {
            return $this->handleFail('New box already exists');
        }

        $location = Location::where('id', $input['location_id'])
            ->where('is_deleted', false)
            ->first();

        if (!$location) {
            return $this->handleFail('Location not found or has been deleted.');
        }
        if ($location->type != Location::RACK) {
            return $this->handleFail('Location must be a rack type.');
        }

        try {
            DB::beginTransaction();
            if (!empty($input['internal_request_id'])) {
                $internalRequest = InternalRequest::with('box')->where('id', $input['internal_request_id'])
                    ->first();
                if (!$internalRequest) {
                    return $this->handleFail('Internal request not found.');
                }
                if ($internalRequest->status != InternalRequest::CHECKED_STATUS) {
                    return $this->handleFail('Internal request must be in checked status.');
                }
                if ($input['quantity'] != (int) $internalRequest->box->quantity - (int) $internalRequest->dark_pod_quantity) {
                    return $this->handleFail('Quantity must be equal to the box quantity minus dark pod quantity.');
                }
                $costValuePerUnit = $internalRequest->box->cost_value / $internalRequest->box->quantity;
                $productId = $internalRequest->product_id;
            }
            if (!empty($input['box_id'])) {
                $box = Box::with('internalRequest')
                    ->whereHas('internalRequest', function ($query) {
                        $query->where('status', InternalRequest::CHECKED_STATUS);
                    })
                    ->where('id', $input['box_id'])
                    ->first();
                if (!$box) {
                    return $this->handleFail('Box is invalid');
                }

                if ($box->quantity - $box->internalRequest->dark_pod_quantity != $input['quantity']) {
                    return $this->handleFail('Quantity must be equal to the box quantity minus dark pod quantity.');
                }
                $costValuePerUnit = $box->cost_value / $box->quantity;
                $productId = $box->product_id;
                $internalRequest = $box->internalRequest;
            }
            $employee = Employee::where('id', $input['employee_id'])
                ->first();
            if (!$employee) {
                return $this->handleFail('Employee not found.');
            }
            $boxData = [
                'barcode' => $input['box_barcode'],
                'warehouse_id' => $input['warehouse_id'],
                'product_id' => $productId ?? null,
                'quantity' => $input['quantity'],
                'employee_id' => $input['employee_id'],
                'location_id' => $location->id,
            ];
            if (!empty($costValuePerUnit)) {
                $boxData['cost_value'] = $costValuePerUnit * $input['quantity'];
            } else {
                $boxData['cost_value'] = 0;
            }
            $box = Box::create($boxData);
            if (!empty($internalRequest)) {
                $internalRequest->new_box_id = $box->id;
                $internalRequest->save();
            }
            $input['box_id'] = $box->id;

            $preLocation = Location::where('type', Location::REPLENISHMENT_TYPE)
                ->where('warehouse_id', Warehouse::WAREHOUSE_SANJOSE_ID)
                ->first();
            if (!$preLocation) {
                return $this->handleFail('Warehouse Invalid');
            }
            BoxMoving::create([
                'box_id' => $box->id ?? null,
                'pre_location_id' => $preLocation->id,
                'location_id' => $location->id,
                'warehouse_id' => config('jwt.warehouse_id') ?? Warehouse::WAREHOUSE_SANJOSE_ID,
                'user_id' => \auth()->user()->id,
                'product_id' => $productId ?? null,
                'quantity' => $input['quantity'],
                'employee_id' => $employee?->code ?? null,
            ]);
            LocationProductRepository::updateQuantity($preLocation->id, $productId, -1 * $input['quantity']);
            LocationProductRepository::updateQuantity($input['location_id'], $productId, $input['quantity']);

            DB::commit();

            return $this->handleSuccess('Box created successfully.', [
                'box' => $box,
            ]);
        } catch (Exception $exception) {
            DB::rollBack();

            return $this->handleFail('An error occurred while creating the box: ' . $exception->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function getSkuByWip($input)
    {
        ['order_item_barcode_id' => $barcodeId] = getBarcodeIdLimit();
        $wip = SaleOrderItemBarcode::with('orderItem')
            ->where('id', '>=', $barcodeId ?? 0)
            ->where('label_id', $input['wip_id'])
            ->where('is_deleted', false)
            ->first();
        if (!$wip) {
            throw new Exception('WIP not found or has been deleted.');
        }

        return [
            'wip_id' => $wip->label_id,
            'sku' => $wip->orderItem->product_sku ?? null,
        ];
    }

        public function buildQueryRbtInventoryOverview($input)
        {
            $warehouseId = $input['warehouse_id'];
            $query = " FROM
            (SELECT
                product.id AS product_id,
                product.sku,
                product.style,
                product.size,
                product.color,
                product.gtin,
                tmp_product_box.quantity_product,
                tmp_product_box.box_quantiy
            FROM
                (
                    (SELECT
                        location_product.product_id,
                        location_product.quantity_product,
                        box.box_quantiy
                    FROM
                        (SELECT
                            SUM(location_product.quantity) quantity_product,
                                location_product.product_id
                        FROM
                            location_product
                        JOIN location ON location_product.location_id = location.id
                            AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "' ";
            $query .= " GROUP BY location_product.product_id) AS location_product
                        LEFT JOIN
                    (SELECT
                            SUM(box.quantity) box_quantiy,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";
            $query .= " GROUP BY box.product_id) AS box ON box.product_id = location_product.product_id
                UNION SELECT
                    box.product_id,
                    location_product.quantity_product,
                    box.box_quantiy

                FROM
                    (SELECT
                        SUM(location_product.quantity) quantity_product,
                            location_product.product_id
                    FROM
                        location_product
                    JOIN location ON location_product.location_id = location.id
                        AND location.type = 1 AND location.warehouse_id = '" . addslashes($warehouseId) . "'";
            $query .= " GROUP BY location_product.product_id) AS location_product
                        RIGHT JOIN
                    (SELECT
                        SUM(box.quantity) box_quantiy,
                            box.product_id
                    FROM
                        box
                    JOIN location ON location.id = box.location_id
                        AND location.type = 0
                        AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        AND box.is_deleted = 0
                        AND box.product_id IS NOT NULL";
            if (!empty($input['location'])) {
                $location = $input['location'];
                $query .= " AND location.barcode LIKE '%" . addslashes(addslashes($location)) . "%'";
            }
            $query .= " GROUP BY box.product_id) AS box  ON box.product_id = location_product.product_id) AS tmp_product_box
                    RIGHT JOIN product
                        ON product.id = tmp_product_box.product_id) WHERE product.parent_id != 0 AND product.is_deleted = 0) AS tb
                    LEFT JOIN (
                        SELECT
                            location_product.product_id,
                            SUM(location_product.quantity) AS quantity_product
                        FROM location_product
                        JOIN location
                            ON location_product.location_id = location.id
                        WHERE location.type = 3
                            AND location.warehouse_id = '" . addslashes($warehouseId) . "'
                        GROUP BY location_product.product_id
                    ) AS moving_product_box
                    ON tb.product_id = moving_product_box.product_id
                    ";

            // Nối phần điều kiện sku vào cuối query
            if (!empty($input['skus'])) {
                $sanitizedSkus = array_map(function ($sku) {
                    return "'" . addslashes(trim($sku)) . "'";
                }, $input['skus']);

                $skuList = implode(',', $sanitizedSkus);
                $query .= " WHERE tb.sku IN ($skuList)";
            } else {
                $query .= ' WHERE 1=1'; // để giữ câu lệnh WHERE nếu cần nối thêm AND
            }

            $query .= ' GROUP BY tb.`product_id`';

            return $query;
        }

    public function fetchRbtInventoryOverview($input)
    {
        if (!empty($input['skus'])) {
            $input['skus'] = array_map('strtoupper', $input['skus']);
            unset($input['batch_id']);
        } elseif (!empty($input['batch_id'])) {
            $input['skus'] = SaleOrderItemBarcode::where('barcode_printed_id', $input['batch_id'])
                ->where('is_deleted', false)
                ->pluck('sku')
                ->map(fn ($sku) => substr($sku, 9))
                ->unique()
                ->toArray();
            if (empty($input['skus'])) {
                return collect();
            }
        }
        $queryFrom = $this->buildQueryRbtInventoryOverview($input);

        $querySelect = '
            SELECT
                tb.sku,
                tb.style,
                tb.size,
                tb.color,
                COALESCE(moving_product_box.quantity_product, 0) as quantity,
                (
                    SELECT location.barcode
                    FROM location
                    WHERE location.rbt_sku = tb.sku
                    LIMIT 1
                ) as location,
                COALESCE(tb.box_quantiy, 0) AS rack,
                COALESCE(tb.quantity_product, 0) AS shelves
        ';

        $fullQuery = $querySelect . $queryFrom;
        $data = DB::select($fullQuery);
        $warehouseCode = Warehouse::find($input['warehouse_id'])?->code ?? 'N/A';
        $orderMap = !empty($input['skus']) ? array_flip($input['skus']) : [];

        return collect($data)
            ->sortBy(function ($row) use ($orderMap) {
                return $orderMap[$row->sku] ?? PHP_INT_MAX;
            })
            ->values()
            ->map(function ($row) use ($warehouseCode) {
                $shelves = (int) $row->shelves;
                $rack = (int) $row->rack;

                return [
                    'sku' => $row->sku,
                    'style' => $row->style,
                    'color' => $row->color,
                    'size' => $row->size,
                    'total' => $shelves + $rack + ((int) $row->quantity ?? 0),
                    'inventory' => [
                        $warehouseCode => [
                            'shelves' => $shelves,
                            'dark_pod' => [
                                'quantity' => (int) $row->quantity ?? 0,
                                'location' => $row->location ?? null,
                            ],
                            'rack' => $rack,
                        ]
                    ]
                ];
            });
    }

    public function generateBatch($data)
    {
        try {
            $sku = $data['sku'];
            DB::beginTransaction();
            [
                'order_id' => $orderId,
                'order_item_id' => $orderItemId,
                'order_item_barcode_id' => $orderItemBarcodeId,
            ] = getBarcodeIdLimit();
            $barcodes = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
                ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
                ->where('sale_order.id', '>=', $orderId)
                ->where('sale_order_item.id', '>=', $orderItemId)
                ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
                ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('sale_order_item.product_sku', $sku)
                ->where('sale_order_item_barcode.is_deleted', false)
                ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
                ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
                ->where('sale_order_item_barcode.barcode_printed_id', 0)
                ->select('sale_order_item_barcode.*', 'sale_order_item.product_sku as product_sku')
                ->orderBy('sale_order_item_barcode.id', 'asc')
                ->selectRaw('sale_order_item_barcode.label_id as label_id, sale_order_item.product_sku')
                ->get();
            if ($barcodes->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No barcodes found for the given SKU.',
                ], 422);
            }
            $result = [];
            $params = [
                'employee_id' => RbtProduct::RBT_EMPLOYEE_ID,
                'warehouse_id' => $params['warehouse_id'] ?? 1,
                'store_id' => null,
                'style_sku' => $sku ?? null,
                'account_id' => $data['account_id'] ?? null,
                'quantity_input' => count($barcodes) ?? 0,
                'is_xqc' => $data['is_xqc'] ?? null,
                'is_eps' => $data['is_eps'] ?? null,
                'is_manual' => $data['is_manual'] ?? null,
                'is_reprint' => $data['is_reprint'] ?? null,
                'is_reroute' => $data['is_reroute'] ?? null,
                'is_fba' => $data['is_fba'] ?? null,
                'is_insert' => $data['is_insert'] ?? null,
                'is_tiktok' => $data['is_tiktok'] ?? null,
                'is_bulk_order' => $data['is_bulk_order'] ?? null,
                'user_id' => RbtProduct::RBT_EMPLOYEE_ID,
                'created_at' => date('Y-m-d H:i:s'),
                'print_method' => BarcodePrinted::METHOD_DTG,
                'station_id' => $data['station_id'] ?? null,
                'is_created_by_spa' => true,
            ];

            $barcodePrinted = BarcodePrinted::create($params);
            $params['wip_ids'] = $barcodes->pluck('label_id')->toArray();
            $params['barcode_printed_id'] = $barcodePrinted->id;
            $this->updateBarcodePrintedItem($params);
            $this->updateLastBarcodePrintedTime($params);

            $arrLabel = [];
            $i = 1;

            $listBarcode = $params['wip_ids'];
            $confirmBarcode = SaleOrderItemBarcode::where('barcode_printed_id', $params['barcode_printed_id'])->pluck('label_id')->toArray();
            $labelPrinted = array_intersect($listBarcode, $confirmBarcode);
            $labelNotPrinted = array_diff($listBarcode, $confirmBarcode);
            $formatLabelNotPrinted = $this->formatLabelNotPrinted($labelNotPrinted);

            foreach ($labelPrinted as $barcode) {
                $arrLabel[] = [
                    'batch_id' => $params['barcode_printed_id'],
                    'label_id' => $barcode,
                    'batch_number' => $i++,
                ];
                RbtWipReceived::updateOrCreate(
                    ['label_id' => $barcode],
                    [
                        'updated_at' => now(),
                    ],
                );
            }

            if (!empty($arrLabel)) {
                RbtBatchNumber::insert($arrLabel);
            }

            $lastWip = $this->convertBarcodeV5($params['barcode_printed_id']);
            BarcodePrinted::where('id', $params['barcode_printed_id'])->update(['convert_status' => BarcodePrinted::ACTIVE]);
            $product = Product::with('rbtLocation')->where('sku', $sku)->first();
            $result[] = [
                'batch_id' => $params['barcode_printed_id'],
                'lastWip' => $lastWip,
                'wip_ids' => $params['wip_ids'],
                'product_sku' => $sku,
                'style' => $product->style ?? null,
                'color' => $product->color ?? null,
                'size' => $product->size ?? null,
                'location' => $product->rbtLocation->barcode ?? null,
                'quantity' => count($barcodes),
                'station_id' => $data['station_id'] ?? null,
                'label_not_printed' => $formatLabelNotPrinted,
                'pdf_url' => env('AWS_S3_URL') . '/barcode/' . $params['barcode_printed_id'] . '.pdf',
            ];
            DB::commit();

            return response()->json([
                'status' => 'success',
                'data' => $result,
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getSKUInLocation($data)
    {
        $query = Location::where('is_deleted', Location::NOT_DELETED)
            ->whereNotNull('rbt_sku');
        if (empty($data['warehouse_id'])) {
            $query->where('warehouse_id', $data['warehouse_id']);
        }
        if (!empty($data['location'])) {
            $query->where('barcode', 'like', '%' . $data['location'] . '%');
        }
        $skus = $query->pluck('rbt_sku')->toArray();
        if (!$skus) {
            return response()->json([
                'status' => 'error',
                'message' => 'No RBT SKUs found for the given location.',
            ], 404);
        }
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $barcodeSku = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->where('sale_order.id', '>=', $orderId)
            ->where('sale_order_item.id', '>=', $orderItemId)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->whereIn('sale_order_item.product_sku', $skus)
            ->where('sale_order_item_barcode.is_deleted', false)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->groupBy('sale_order_item.product_sku')
            ->select('sale_order_item.product_sku as product_sku')
            ->get()->toArray();

        return response()->json([
            'status' => 'success',
            'data' => $barcodeSku,
        ]);
    }

    public function darkPodToRack($input)
    {
        $box = Box::where('barcode', $input['barcode'])
            ->first();

        if ($box && $box->is_deleted) {
            throw new Exception('Box ID has been deleted and cannot be reused');
        } elseif ($box) {
            throw new Exception('Box ID already exists');
        }

        $location = Location::where('id', $input['location_id'])
            ->where('is_deleted', false)
            ->first();

        if (!$location) {
            throw new Exception('Location not found or has been deleted.');
        }
        if ($location->type != Location::RACK) {
            throw new Exception('Location must be a rack type.');
        }

        $product = Product::with(['ProductRbt'])->find($input['product_id']);
        if (empty($product->ProductRbt->is_active)) {
            throw new Exception('Product not found.');
        }

        try {
            DB::beginTransaction();
            $employee = Employee::where('id', $input['employee_id'])
                ->first();
            if (!$employee) {
                throw new Exception('Employee not found.');
            }
            $preLocation = Location::with('locationProducts')
                ->where('type', Location::MOVING_SHELVES)
                ->where('warehouse_id', $input['warehouse_id'])
                ->where('is_deleted', false)
                ->where('rbt_sku', $product->sku)
                ->first();
            if (!$preLocation) {
                throw new Exception('Pre-location not found or has been deleted.');
            }
            if (empty($preLocation->locationProducts[0]->quantity) || $preLocation->locationProducts[0]->quantity < $input['quantity']) {
                throw new Exception('Invalid quantity. Must not exceed available stock for this SKU');
            }

            $lastPoItemsHasProduct = PurchaseOrderItem::query()
                ->select('purchase_order_item.price')
                ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
                ->where('purchase_order_item.product_id', $product->id)
                ->where('purchase_order.order_status', '<>', PurchaseOrder::CANCELLED_STATUS)
                ->where('purchase_order_item.price', '>', 0)
                ->orderBy('purchase_order.order_date', 'desc')
                ->first();
            $price = $lastPoItemsHasProduct?->price ?? 0;
            $boxData = [
                'barcode' => $input['barcode'],
                'warehouse_id' => $input['warehouse_id'],
                'product_id' => $product->id ?? null,
                'quantity' => $input['quantity'],
                'employee_id' => $input['employee_id'],
                'location_id' => $location->id,
                'cost_value' => $price * $input['quantity'] ?? 0,
            ];
            $box = Box::create($boxData);
            $input['box_id'] = $box->id;

            $boxMoving = BoxMoving::create([
                'box_id' => $box->id ?? null,
                'pre_location_id' => $preLocation->id,
                'location_id' => $location->id,
                'warehouse_id' => config('jwt.warehouse_id') ?? Warehouse::WAREHOUSE_SANJOSE_ID,
                'user_id' => \auth()->user()->id,
                'product_id' => $product->id ?? null,
                'quantity' => $input['quantity'],
                'employee_id' => $employee?->code ?? null,
            ]);
            LocationProductRepository::updateQuantity($preLocation->id, $product->id, -1 * $input['quantity']);
            LocationProductRepository::updateQuantity($input['location_id'], $product->id, $input['quantity']);

            DB::commit();

            return $boxMoving;
        } catch (Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage());
        }
    }
}
