<?php


namespace App\Repositories;


use App\Models\BarcodePrinted;
use App\Models\TimeTracking;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TimeCheckingRepository
{

    public function insertTimeChecking(array $input): int
    {
        $now = now();                                      // Carbon instance (UTC app timezone)
        $dataInsert = [
            'employee_id' => $input['id'],
            'job_type'    => $input['job_type'],
            'start_time'  => $now,
            'end_time'    => $now,
        ];

        // Chỉ thêm parent_id khi job_type nằm trong 2 giá trị đặc biệt
        if (
            !empty($input['id_parent']) &&
            in_array($input['job_type'], [
                TimeTracking::QUALITY_CONTROL_JOB_TYPE,
                TimeTracking::STAGE_JOB_TYPE
            ], true)
        ) {
            $dataInsert['parent_id'] = $input['id_parent'];
        }

        return DB::table('time_tracking')->insertGetId($dataInsert);
    }


    public function updateTimeChecking($data, $id)
    {
        $extraData = collect($data)->except('quantity')->toArray();

        return DB::table('time_tracking')
            ->where('id', $id)
            ->increment('quantity', 1, $extraData);
    }

    public function updateTimeCheckingForLogout($data, $id)
    {
        return DB::table('time_tracking')
            ->where('id', $id)
            ->update($data);
    }

    public static function createTimeTrackingWhenDeduction($employeeId, $endTime)
    {
        $dateTime12HoursBefore = Carbon::now()->subHours(12)->toDateTimeString();

        ///Todo : employee đã có log time trong vòng 12h
        $employeeTimeLog = TimeTracking::where('employee_id', $employeeId)
            ->where('start_time', '>', $dateTime12HoursBefore)
            ->where('job_type', TimeTracking::DEDUCTION_JOB_TYPE)
            ->first();

        if (!empty($employeeTimeLog)) {
            $quantity = $employeeTimeLog->quantity;
            $employeeTimeLog->end_time = $endTime;
            $employeeTimeLog->quantity = $quantity + 1;

            return $employeeTimeLog->save();

        } else {
            ///Todo : get dây barcode đầu tiên dc tạo ra trong vòng 12h để tạo TimeTracking cho employee
            $barcode = BarcodePrinted::where('employee_id', $employeeId)
                ->where('created_at', '>', $dateTime12HoursBefore)
                ->orderBy('created_at', 'ASC')
                ->first();
            if (!empty($barcode)) {

                $dataTimeTracking = [
                    'start_time' => $barcode->created_at,
                    'end_time' => $endTime,
                    'employee_id' => $employeeId,
                    'job_type' => TimeTracking::DEDUCTION_JOB_TYPE,
                    'quantity' => 1,
                    'parent_id' => 0,
                ];
                return TimeTracking::create($dataTimeTracking);
            }
        }
    }
}
