<?php

namespace App\Repositories;
use App\Models\SupplyCategory;
use App\Repositories\Contracts\SupplyCategoryRepositoryInterface;
use Validator;

class SupplyCategoryRepository extends CommonRepository implements SupplyCategoryRepositoryInterface
{
    public function fetchAll($request)
    {
        $query = SupplyCategory::search($request);
        if($request->has('without_pagination')){
            return $query->get();
        }
        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function create($input)
    {
        $supplyUnit = new SupplyCategory();
        $supplyUnit->name = $input['name'];
        $supplyUnit->save();
        return $supplyUnit;
    }

    public function fetch($id)
    {
        return SupplyCategory::find($id);
    }

    public function update($id, $dataUpdate)
    {
        $supplyUnit = SupplyCategory::find($id);
        if (!$supplyUnit) {
            return null;
        }
        $supplyUnit->name = $dataUpdate['name'];
        $supplyUnit->save();
        return $supplyUnit;
    }

    public function delete($id)
    {
        return SupplyCategory::where('id', $id)->delete();
    }
}
