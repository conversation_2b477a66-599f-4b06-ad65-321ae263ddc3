<?php

namespace App\Repositories;

use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Models\SkuBulbSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class SkuBulbPowerRepository extends CommonRepository
{
    const LIMIT = 25;

    public function getSkuBulbPower($input, $settingType): LengthAwarePaginator
    {
        $query = SkuBulbSetting::with([
            'color:id,name,sku,color_code',
            'style:id,name,sku',
            'user:id,username'
        ])
            ->where('type', $settingType);

        if (!empty($input['style_name_or_sku'])) {
            $query->whereHas('style', function ($q) use ($input) {
                $q->where('name', 'LIKE', '%' . $input['style_name_or_sku'] . '%');
                $q->orWhere('sku', 'LIKE', '%' . $input['style_name_or_sku'] . '%');
            });
        }

        if (!empty($input['color_name_or_sku'])) {
            $query->whereHas('color', function ($q) use ($input) {
                $q->where('name', 'LIKE', '%' . $input['color_name_or_sku'] . '%');
                $q->orWhere('sku', 'LIKE', '%' . $input['color_name_or_sku'] . '%');
            });
        }

        return $query->latest('id')->paginate($input['limit'] ?? self::LIMIT);
    }

    public function getDetailSkuBulbPower($id)
    {
        return SkuBulbSetting::with([
            'color:id,name,sku,color_code',
            'style:id,name,sku'
        ])
            ->where('id', $id)->first();
    }

    public function createSkuBulbPower($input, $bulbType)
    {
        $setting = SkuBulbSetting::whereHas('color', function ($q) use ($input) {
            $q->where('sku', $input['color_sku']);
        })->whereHas('style', function ($q) use ($input) {
            $q->where('sku', $input['style_sku']);
        })->where('type', $bulbType)->first();

        if (!empty($setting)) {
            throw new \Exception('Bulb powers setting for this style and color exists!');
        }
        $colorAttribute = ProductColor::where('sku', $input['color_sku'])->first();
        $styleAttribute = ProductStyle::where('sku', $input['style_sku'])->first();

        if (empty($colorAttribute)) {
            throw new \Exception('Product color not found!');
        }
        if (empty($styleAttribute)) {
            throw new \Exception('Product style not found!');
        }

        $bulbSetting = SkuBulbSetting::create([
            'type' => $bulbType,
            'color_id' => $colorAttribute->id,
            'style_id' => $styleAttribute->id,
            'data' => json_encode($input['bulb_setting_data']),
            'user_id' => auth()->user()->id
        ]);

        return [
            'status' => true,
            'color' => $colorAttribute,
            'bulbPowers' => $bulbSetting
        ];
    }

    public function updateSkuBulbPower($id, $input)
    {
        $setting = SkuBulbSetting::where('id', $id)->first();
        if (empty($setting)) {
            throw new \Exception('Bulb powers setting not found!');
        }

        $setting->update([
            'data' => json_encode($input['bulb_setting_data'])
        ]);

        return [
            'status' => true,
            'message' => 'Update bulb powers setting successfully!',
            'bulbPowers' => $setting->data
        ];
    }

    public function toggleActivePower($id, $activeStatus)
    {
        $setting = SkuBulbSetting::where('id', $id)->first();
        if (empty($setting)) {
            throw new \Exception('Bulb powers setting not found!');
        }

        // Update active status only
        $setting->update([
            'is_active' => $activeStatus
        ]);

        return [
            'status' => true,
            'message' => 'Update active status successfully!',
        ];
    }
}
