<?php


namespace App\Repositories;


use App\Models\ShipmentTransit;
use App\Models\ShipmentTransitReportByMonth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ShipmentTransitRepository extends CommonRepository
{
    public function getPerformance($input)
    {
        $months = $this->generateMonths($input['start_month'], $input['end_month']);
        $query = ShipmentTransitReportByMonth::whereIn('month_year', $months);
        if (empty($input['warehouse'])) {
            $query->where('shipment_transit_report_by_month.warehouse_id', config('jwt.warehouse_id'));
        } elseif ($input['warehouse'] != 'all') {
            $query->where('shipment_transit_report_by_month.warehouse_id', $input['warehouse']);
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $warehouseIds = $user->warehouses->pluck('id')->toArray();
                $query->whereIn('shipment_transit_report_by_month.warehouse_id', $warehouseIds);
            }
        }
        if (!empty($input['ship_zone'])) {
            $query->join('ship_zone_destination', 'ship_zone_destination.id', '=', 'shipment_transit_report_by_month.destination_id')
                ->join('ship_zone_mapping', 'ship_zone_mapping.id', '=', 'ship_zone_destination.ship_zone_mapping_id');
            if ($input['ship_zone'] == ShipmentTransit::DOMESTIC) {
                $query->where('ship_zone_mapping.ship_zone', $input['ship_zone'])
                    ->whereIn('shipment_transit_report_by_month.shipment_carrier', ShipmentTransit::DOMESTIC_CARRIER)
                    ->whereIn('shipment_transit_report_by_month.shipment_service', ShipmentTransit::DOMESTIC_SERVICE);

            } elseif ($input['ship_zone'] == ShipmentTransit::INTERNATIONAL) {
                $query->where('ship_zone', $input['ship_zone'])
                    ->whereIn('shipment_transit_report_by_month.shipment_carrier', ShipmentTransit::INTERNATIONAL_CARRIER)
                    ->whereIn('shipment_transit_report_by_month.shipment_service', ShipmentTransit::INTERNATIONAL_SERVICE);
            } else {
                $query->whereIn('shipment_transit_report_by_month.shipment_carrier', array_merge(ShipmentTransit::INTERNATIONAL_CARRIER, ShipmentTransit::DOMESTIC_CARRIER))
                    ->whereIn('shipment_transit_report_by_month.shipment_service', array_merge(ShipmentTransit::INTERNATIONAL_SERVICE, ShipmentTransit::DOMESTIC_SERVICE));
            }
        }
        $data = $query->selectRaw('shipment_transit_report_by_month.shipment_carrier, shipment_transit_report_by_month.shipment_service, SUM(shipment_transit_report_by_month.total_shipments) as total_shipments, SUM(shipment_transit_report_by_month.total_transit_day) as total_transit_day, SUM(shipment_transit_report_by_month.total_fulfillment_day) as total_fulfillment_day ')
            ->groupBy('shipment_transit_report_by_month.shipment_service')->get()->toArray();
        return $this->formatData($data, $input);
    }

    public function getPerformanceByZone($input)
    {
        $months = $this->generateMonths($input['start_month'], $input['end_month']);
        $query = ShipmentTransitReportByMonth::whereIn('month_year', $months);
        if (empty($input['warehouse'])) {
            $query->where('shipment_transit_report_by_month.warehouse_id', config('jwt.warehouse_id'));
        } elseif ($input['warehouse'] != 'all') {
            $query->where('shipment_transit_report_by_month.warehouse_id', $input['warehouse']);
        } else {
            $user = auth()->user();
            if (!$user->is_admin) {
                $warehouseIds = $user->warehouses->pluck('id')->toArray();
                $query->whereIn('shipment_transit_report_by_month.warehouse_id', $warehouseIds);
            }
        }
        if (!empty($input['ship_zone'])) {
            $query->join('ship_zone_destination', 'ship_zone_destination.id', '=', 'shipment_transit_report_by_month.destination_id')
                ->join('ship_zone_mapping', 'ship_zone_mapping.id', '=', 'ship_zone_destination.ship_zone_mapping_id');
            if ($input['ship_zone'] == ShipmentTransit::DOMESTIC) {
                $query->where('ship_zone_mapping.ship_zone', $input['ship_zone'])
                    ->whereIn('shipment_transit_report_by_month.shipment_carrier', ShipmentTransit::DOMESTIC_CARRIER)
                    ->whereIn('shipment_transit_report_by_month.shipment_service', ShipmentTransit::DOMESTIC_SERVICE);

            } elseif ($input['ship_zone'] == ShipmentTransit::INTERNATIONAL) {
                $query->where('ship_zone', $input['ship_zone'])
                    ->whereIn('shipment_transit_report_by_month.shipment_carrier', ShipmentTransit::INTERNATIONAL_CARRIER)
                    ->whereIn('shipment_transit_report_by_month.shipment_service', ShipmentTransit::INTERNATIONAL_SERVICE);
            }
        }
        $data = $query->selectRaw('ship_zone_mapping.zone_name, SUM(shipment_transit_report_by_month.total_shipments) as total_shipments, SUM(shipment_transit_report_by_month.total_transit_day) as total_transit_day, SUM(shipment_transit_report_by_month.total_fulfillment_day) as total_fulfillment_day ')
            ->groupBy('ship_zone_mapping.zone_name')->get()->toArray();
        return $this->setupDataByZone($data, $input);

    }

    private function setupDataByZone($data, $input)
    {
        $zoneNames = [];
        if (!empty($input['ship_zone'])) {
            if ($input['ship_zone'] == ShipmentTransit::DOMESTIC) {
                $zoneNames = DB::table('ship_zone_mapping')
                    ->where('ship_zone', ShipmentTransit::DOMESTIC)
                    ->pluck('zone_name')
                    ->toArray();



            } else {
                $zoneNames = DB::table('ship_zone_mapping')
                    ->where('ship_zone', ShipmentTransit::INTERNATIONAL)
                    ->pluck('zone_name')
                    ->toArray();
            }
        }
        foreach ($zoneNames as $zoneName) {
            $hasZone = false;
            foreach ($data as $item) {
                if ($item['zone_name'] == $zoneName) {
                    $hasZone = true;
                    break;
                }

            }
            if (!$hasZone) {
                array_unshift($data, [
                    'zone_name' => $zoneName,
                    'total_shipments' => 0,
                    'total_transit_day' => 0,
                    'total_fulfillment_day' => 0,
                ]);
            }
        }
        usort($data, function ($item1, $item2) {
            if ($item1['total_shipments'] == 0 ) {
                return -1;
            }
            if ($item2['total_shipments'] == 0 ) {
                return 1;
            }
            $value1 = ($item1['total_transit_day'] + $item1['total_fulfillment_day']) / (int) $item1['total_shipments'];
            $value2 = ($item2['total_transit_day'] + $item2['total_fulfillment_day']) / (int) $item2['total_shipments'];

            if ($value1 == $value2) {
                return 0;
            }

            return ($value1 < $value2) ? -1 : 1;
        });

        return $data;
    }


    private function formatData($data, $input)
    {
        if (!empty($input['ship_zone'])) {
            $service = [];
            if ($input['ship_zone'] == ShipmentTransit::DOMESTIC) {
                $service = ShipmentTransit::DOMESTIC_SERVICE;
            } elseif ($input['ship_zone'] == ShipmentTransit::INTERNATIONAL) {
                $service = ShipmentTransit::INTERNATIONAL_SERVICE;
            } else {
                $service = array_merge(ShipmentTransit::DOMESTIC_SERVICE, ShipmentTransit::INTERNATIONAL_SERVICE);
            }

            foreach ($service as $key => $item) {
                $hasService = false;
                foreach ($data as $key2 => $item2) {
                    if ($item == $item2['shipment_service']) {
                        $hasService = true;
                        break;
                    }
                }
                if (!$hasService) {
                    if ($input['ship_zone'] != ShipmentTransit::INTERNATIONAL) {
                        switch ($item) {
                            case ShipmentTransit::FIRST:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::USPS,
                                    'shipment_service' => ShipmentTransit::FIRST,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::PRIORITY:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::USPS,
                                    'shipment_service' => ShipmentTransit::PRIORITY,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::GROUND_ADVANTAGE:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::USPS,
                                    'shipment_service' => ShipmentTransit::GROUND_ADVANTAGE,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::EXPRESS:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::USPS,
                                    'shipment_service' => ShipmentTransit::EXPRESS,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::DHL_PARCEL_GROUND:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::DHL,
                                    'shipment_service' => ShipmentTransit::DHL_PARCEL_GROUND,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::SECOND_DAY_AIR:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::UPS,
                                    'shipment_service' => ShipmentTransit::SECOND_DAY_AIR,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                        }
                    }
                    if($input['ship_zone'] != ShipmentTransit::DOMESTIC) {
                        switch ($item) {
                            case ShipmentTransit::EPAQ_PLUS:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::ASENDIA_USA,
                                    'shipment_service' => ShipmentTransit::EPAQ_PLUS,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::DHL_PACKET_INTERNATIONAL:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::DHL,
                                    'shipment_service' => ShipmentTransit::DHL_PACKET_INTERNATIONAL,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                            case ShipmentTransit::FIRST_CLASS_PACKAGE_INTERNATIONAL_SERVICE:
                                array_unshift($data, [
                                    'shipment_carrier' => ShipmentTransit::USPS,
                                    'shipment_service' => ShipmentTransit::FIRST_CLASS_PACKAGE_INTERNATIONAL_SERVICE,
                                    'total_shipments' => 0,
                                    'total_transit_day' => 0,
                                    'total_fulfillment_day' => 0,
                                ]);
                                break;
                        }
                    }

                }
            }
            if ($input['ship_zone'] != ShipmentTransit::INTERNATIONAL) {
                $firstClassService = null;
                foreach ($data as $key => $item) {
                    if ($item['shipment_service'] == ShipmentTransit::FIRST) {
                        $firstClassService = $item;
                        $firstClassService['shipment_service'] = ShipmentTransit::GROUND_ADVANTAGE;
                        unset($data[$key]);
                        break;
                    }
                }
                $hasGroundAdvantage = false;
                foreach ($data as $key => $item) {
                    if ($item['shipment_service'] == ShipmentTransit::GROUND_ADVANTAGE) {
                        $hasGroundAdvantage = true;
                        $data[$key]['total_shipments'] += $firstClassService['total_shipments'];
                        $data[$key]['total_transit_day'] += $firstClassService['total_transit_day'];
                        $data[$key]['total_fulfillment_day'] += $firstClassService['total_fulfillment_day'];
                    }
                }
                if (!$hasGroundAdvantage) {
                    $data[] = $firstClassService;
                }
            }






//            if ($input['ship_zone'] == ShipmentTransit::DOMESTIC) {
//                $domesticService =  ShipmentTransit::DOMESTIC_SERVICE;
//                foreach ($domesticService as $key => $item) {
//                    $hasService = false;
//                    foreach ($data as $key2 => $item2) {
//                        if ($item == $item2['shipment_service']) {
//                            $hasService = true;
//                            break;
//                        }
//                    }
//                    if (!$hasService) {
//                        switch ($item) {
//                            case ShipmentTransit::FIRST:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::USPS,
//                                    'shipment_service' => ShipmentTransit::FIRST,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::PRIORITY:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::USPS,
//                                    'shipment_service' => ShipmentTransit::PRIORITY,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::GROUND_ADVANTAGE:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::USPS,
//                                    'shipment_service' => ShipmentTransit::GROUND_ADVANTAGE,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::EXPRESS:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::USPS,
//                                    'shipment_service' => ShipmentTransit::EXPRESS,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::DHL_PARCEL_GROUND:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::DHL,
//                                    'shipment_service' => ShipmentTransit::DHL_PARCEL_GROUND,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::SECOND_DAY_AIR:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::UPS,
//                                    'shipment_service' => ShipmentTransit::SECOND_DAY_AIR,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                        }
//
//                    }
//                }
//                $firstClassService = null;
//                foreach ($data as $key => $item) {
//                    if ($item['shipment_service'] == ShipmentTransit::FIRST) {
//                        $firstClassService = $item;
//                        $firstClassService['shipment_service'] = ShipmentTransit::GROUND_ADVANTAGE;
//                        unset($data[$key]);
//                        break;
//                    }
//                }
//                $hasGroundAdvantage = false;
//                foreach ($data as $key => $item) {
//                    if ($item['shipment_service'] == ShipmentTransit::GROUND_ADVANTAGE) {
//                        $hasGroundAdvantage = true;
//                        $data[$key]['total_shipments'] += $firstClassService['total_shipments'];
//                        $data[$key]['total_transit_day'] += $firstClassService['total_transit_day'];
//                        $data[$key]['total_fulfillment_day'] += $firstClassService['total_fulfillment_day'];
//                    }
//                }
//                if (!$hasGroundAdvantage) {
//                    $data[] = $firstClassService;
//                }
//            } elseif ($input['ship_zone'] == ShipmentTransit::INTERNATIONAL) {
//                $internationalService =  ShipmentTransit::INTERNATIONAL_SERVICE;
//                foreach ($internationalService as $key => $item) {
//                    $hasService = false;
//                    foreach ($data as $key2 => $item2) {
//                        if ($item == $item2['shipment_service']) {
//                            $hasService = true;
//                            break;
//                        }
//                    }
//                    if (!$hasService) {
//                        switch ($item) {
//                            case ShipmentTransit::EPAQ_PLUS:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::ASENDIA_USA,
//                                    'shipment_service' => ShipmentTransit::EPAQ_PLUS,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::DHL_PACKET_INTERNATIONAL:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::DHL,
//                                    'shipment_service' => ShipmentTransit::DHL_PACKET_INTERNATIONAL,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                            case ShipmentTransit::FIRST_CLASS_PACKAGE_INTERNATIONAL_SERVICE:
//                                array_unshift($data, [
//                                    'shipment_carrier' => ShipmentTransit::USPS,
//                                    'shipment_service' => ShipmentTransit::FIRST_CLASS_PACKAGE_INTERNATIONAL_SERVICE,
//                                    'total_shipments' => 0,
//                                    'total_transit_day' => 0,
//                                    'total_fulfillment_day' => 0,
//                                ]);
//                                break;
//                        }
//
//                    }
//                }
//            }
        }
        usort($data, function ($item1, $item2) {
            if ($item1['total_shipments'] == 0 ) {
                return -1;
            }
            if ($item2['total_shipments'] == 0 ) {
                return 1;
            }
            $value1 = ($item1['total_transit_day'] + $item1['total_fulfillment_day']) / (int) $item1['total_shipments'];
            $value2 = ($item2['total_transit_day'] + $item2['total_fulfillment_day']) / (int) $item2['total_shipments'];

            if ($value1 == $value2) {
                return 0;
            }

            return ($value1 < $value2) ? -1 : 1;
        });

        return $data;
    }

    private function generateMonths($startMonth, $endMonth)
    {
        $start = Carbon::createFromFormat('m-Y', $startMonth);
        $end = Carbon::createFromFormat('m-Y', $endMonth);
        $months = [];
        while ($start->lessThanOrEqualTo($end)) {
            $months[] = $start->format('m-Y');
            $start->addMonth();
        }
        return $months;
    }

}
