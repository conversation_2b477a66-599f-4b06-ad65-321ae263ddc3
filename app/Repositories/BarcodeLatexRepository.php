<?php

namespace App\Repositories;

use App\Builder\BarcodeLatexBuilder;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\PrintMethod;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class BarcodeLatexRepository
{
    protected BarcodeLatexBuilder $barcodeLatexBuilder;

    public function __construct(BarcodeLatexBuilder $barcodeLatexBuilder)
    {
        $this->barcodeLatexBuilder = $barcodeLatexBuilder;
    }

    public function countLatexByOrderType($params): array
    {
        return [
            'bulk_order' => $this->countPendingBulkOrder($params),
            'fba' => $this->countPendingFba($params),
            'reroute' => $this->countPendingReroute($params),
            'tiktok' => $this->countPendingTiktok($params),
            'manual' => $this->countPendingManualProcess($params),
            'reprint' => $this->countPendingReprint($params),
            'xqc' => $this->countPendingStyleXQC($params),
            'eps' => $this->countPendingStyleEps($params),
            'styles' => $this->countPendingStyles($params),

        ];
    }

    public function fetchPrinting($input)
    {
        return BarcodePrinted::query()
            ->with([
                'employeeConvert:id,name',
                'style:sku,name',
                'store:id,name,code'
            ])
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('print_status', BarcodePrinted::INACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', PrintMethod::LATEX)
            ->orderByDesc('id')
            ->paginate($input['limit']);
    }

    public function fetchHistory($input)
    {
        $data = BarcodePrinted::query()
            ->with([
                'employeeConvert:id,name',
                'style:sku,name'
            ])
            ->whereHas('barcodes', function ($query) use ($input) {
                if (!is_null($input['label_id'])) {
                    $query->where('label_id', 'LIKE', '%' . $input['label_id'] . '%');
                }
            })
            ->where('warehouse_id', $input['warehouse_id'])
            ->where('print_status', BarcodePrinted::ACTIVE)
            ->whereNotNull('employee_id')
            ->where('print_method', PrintMethod::LATEX)
            ->orderByDesc('id')
            ->paginate($input['limit']);

        $data->getCollection()
            ->transform(function ($wip) {
                $fileName = "/barcode/$wip->id.pdf";
                $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

                if (Storage::disk('public')->exists($fileName)) {
                    $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
                }

                return $wip;
            });

        return $data;
    }

    public function countPendingBulkOrder($args): array
    {
        $query = $this->barcodeLatexBuilder->selectPendingBulkOrder($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('bulk_order', $args);

        return $r;
    }

    public function countPendingFba($args): array
    {
        $query = $this->barcodeLatexBuilder->selectPendingFBA($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('fba', $args);

        return $r;
    }

    public function countPendingReroute($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingReroute($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('reroute', $args);

        return $r;
    }

    public function countPendingTiktok($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingTiktok($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('tiktok', $args);

        return $r;
    }

    public function countPendingManualProcess($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingManual($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('manual', $args);

        return $r;
    }

    public function countPendingReprint($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingReprint($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('reprint', $args);

        return $r;
    }

    public function countPendingStyleXQC($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingXQC($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('xqc', $args);

        return $r;
    }

    public function countPendingStyleEps($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingExpress($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total');

        $r['total'] = $query->first()->total ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('eps', $args);

        return $r;
    }

    public function countPendingStyles($args)
    {
        $query = $this->barcodeLatexBuilder->selectPendingStyles($args)
            ->getQuery()
            ->selectRaw('COUNT(*) AS total, product_style.name, product_style.sku')
            ->groupBy('product_style.sku')
            ->orderBy('total', 'DESC');

        $styles = $query->get();
        $total = 0;

        foreach ($styles as $style) {
            $total += $style->total;
            $style->printed_at = $this->getLastPrintedTime('style', $args, $style->sku);
        }

        $r['total'] = $total;
        $r['sql'] = interpolateQuery($query);
        $r['data'] = array_values(collect($styles)->sortBy('printed_at')->toArray());

        return $r;
    }

    public function getLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)->first();
    }

    public function getLastPrintedTime($type, $args, $styleSku = null)
    {
        $types = [
            'is_bulk_order' => null,
            'is_fba' => null,
            'is_reroute' => null,
            'is_tiktok' => null,
            'is_manual' => null,
            'is_reprint' => null,
            'is_xqc' => null,
            'is_eps' => null,
            'style_sku' => null,
        ];

        switch ($type) {
            case 'bulk_order':
                $types['is_bulk_order'] = true;
                break;
            case 'fba':
                $types['is_fba'] = true;
                break;
            case 'reroute':
                $types['is_reroute'] = true;
                break;
            case 'tiktok':
                $types['is_tiktok'] = true;
                break;
            case 'manual':
                $types['is_manual'] = true;
                break;
            case 'reprint':
                $types['is_reprint'] = true;
                break;
            case 'xqc':
                $types['is_xqc'] = true;
                break;
            case 'eps':
                $types['is_eps'] = true;
                break;
            case 'style':
                $types['style_sku'] = $styleSku;
                break;
        }

        $printedTime = BarcodePrintedTime::where('print_method', PrintMethod::LATEX)
            ->where('warehouse_id', $args['warehouse_id']);
        foreach ($types as $key => $value) {
            if ($value !== null) {
                $printedTime->where($key, $value);

                continue;
            }

            $printedTime->whereNull($key);
        }

        return $printedTime->first()->printed_at ?? null;
    }

    public function confirm($input)
    {
        $style_sku = $input['style_sku'] ?? null;
        $limit = $input['limit'] ?? 10;
        $employee_id = $input['employee_id'] ?? null;
        $warehouse_id = $input['warehouse_id'] = config('jwt.warehouse_id');
        $store_id = $input['store_id'] ?? null;
        $query = null;
        $args = [
            'warehouse_id' => $warehouse_id,
            'store_id' => $store_id,
        ];

        if (!empty($input['is_bulk_order'])) {
            $query = $this->barcodeLatexBuilder->selectPendingBulkOrder($args);
        } elseif (!empty($input['is_fba'])) {
            $query = $this->barcodeLatexBuilder->selectPendingFBA($args);
        } elseif (!empty($input['is_reroute'])) {
            $query = $this->barcodeLatexBuilder->selectPendingReroute($args);
        } elseif (!empty($input['is_tiktok'])) {
            $query = $this->barcodeLatexBuilder->selectPendingTiktok($args);
        } elseif (!empty($input['is_manual'])) {
            $query = $this->barcodeLatexBuilder->selectPendingManual($args);
        } elseif (!empty($input['is_reprint'])) {
            $query = $this->barcodeLatexBuilder->selectPendingReprint($args);
        } elseif (!empty($input['is_xqc'])) {
            $query = $this->barcodeLatexBuilder->selectPendingXQC($args);
        } elseif (!empty($input['is_eps'])) {
            $query = $this->barcodeLatexBuilder->selectPendingExpress($args);
        } elseif (!empty($input['style_sku'])) {
            $query = $this->barcodeLatexBuilder->selectPendingStyles($args);
        }

        if (empty($query)) {
            throw new \Exception('Server Error! Not found query.', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            DB::beginTransaction();

            $barcode_printed = BarcodePrinted::create([
                'employee_id' => $employee_id,
                'warehouse_id' => $warehouse_id,
                'style_sku' => $style_sku,
                'quantity_input' => $limit,
                'is_xqc' => $input['is_xqc'] ?? null,
                'is_eps' => $input['is_eps'] ?? null,
                'is_manual' => $input['is_manual'] ?? null,
                'is_reprint' => $input['is_reprint'] ?? null,
                'is_reroute' => $input['is_reroute'] ?? null,
                'is_fba' => $input['is_fba'] ?? null,
                'is_insert' => $input['is_insert'] ?? null,
                'is_tiktok' => $input['is_tiktok'] ?? null,
                'is_bulk_order' => $input['is_bulk_order'] ?? null,
                'store_id' => $input['store_id'] ?? null,
                'user_id' => Auth::id(),
                'created_at' => Carbon::now(),
                'print_method' => PrintMethod::LATEX,
                'print_status' => BarcodePrinted::INACTIVE,
                'convert_status' => BarcodePrinted::FAILED,
                'convert_percent' => 0,
            ]);

            $query = $query->getQuery();

            if (!empty($input['style_sku'])) {
                $query = $query->where('sale_order_item.product_style_sku', $style_sku);
            }

            $barcode_printed->quantity = $query->whereRaw('1 = 1 LIMIT ?', [$limit])->update([
                'barcode_printed_id' => $barcode_printed->id,
                'employee_pull_id' => $employee_id,
                'print_barcode_at' => Carbon::now()
            ]);
            $barcode_printed->convert_status = BarcodePrinted::INACTIVE;
            $barcode_printed->save();

            $input['print_method'] = PrintMethod::LATEX;
            resolve(BarcodeRepository::class)->updateLastBarcodePrintedTime($input);
            $order_ids = SaleOrderItemBarcode::getArrayOrderIds($barcode_printed->id);
            SaleOrder::changeStatusToInProduction($order_ids);
            DB::commit();

            return $barcode_printed;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function fetchBarcodeConfirmed()
    {
        return BarcodePrinted::query()
            ->where('convert_status', 0)
            ->whereNull('color_sku')
            ->where('print_method', PrintMethod::LATEX)
            ->first();
    }
}
