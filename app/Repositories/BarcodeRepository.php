<?php

namespace App\Repositories;

use App\Events\CountPendingNotification;
use App\Events\UpdateStatusPrintNotification;
use App\Events\WipConvertedNotification;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedHistory;
use App\Models\BarcodePrintedTime;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Printer;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductRoulette;
use App\Models\ProductStyle;
use App\Models\RbtProduct;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Warehouse;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class BarcodeRepository
{
    public function countPendingBulkOrder($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS `total`')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st = $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('product_style.type', '<>', Product::TYPE_INSERT_PRINTING)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order.is_xqc', '<>', 1)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingFba($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order.is_fba_order', SaleOrder::ACTIVE)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $this->excludeBulkOrder($st);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function excludeBulkOrder(Builder &$query)
    {
        return $query->where(function ($query) {
            $query->where('sale_order.order_quantity', '<', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->where('sale_order.is_xqc', '=', 1);
                })
                ->orWhere(function ($subCondition) {
                    $subCondition->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20))
                        ->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
                });
        });
    }

    public function countPendingTiktok($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingOrderInsert($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $account_id = $args['account_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total, barcode_printed_time.printed_at')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id, $account_id) {
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->where('barcode_printed_time.account_id', $account_id);
                $join->whereNull('barcode_printed_time.is_xqc');
                $join->whereNull('barcode_printed_time.is_eps');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
                $join->where('barcode_printed_time.is_insert', BarcodePrintedTime::ACTIVE);
            });
        $this->optimizeFilter($st);

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->where('sale_order.is_manual', SaleOrder::INACTIVE);
        $st->where('sale_order.is_xqc', SaleOrder::INACTIVE);
        $st->where('sale_order.is_eps', SaleOrder::INACTIVE);
        $st->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->where('product_style.type', Product::TYPE_INSERT_PRINTING);

        return $st->first();
    }

    public function countPendingReroute($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->appendFilter($st, $args);
        $this->optimizeFilter($st);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereNotNull('sale_order_item_barcode.employee_reroute_id')
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $this->excludeBulkOrder($st);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingReprint($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->whereNotNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.reprint_status', 0)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $this->excludeBulkOrder($st);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingManualProcess($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $args['is_manual'] = SaleOrder::IS_MANUAL;
        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', SaleOrder::IS_MANUAL)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $this->excludeBulkOrder($st);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingStyleAll($params)
    {
        $styleColors = $this->getStyleColorsMobileWip($params);
        $styles = $this->countPendingStyle($params, $styleColors);
        $styleMobiles = $this->countPendingStyle($params, $styleColors, true);
        foreach ($styles as $style) {
            $style->totalPC = $style->total;
            $style->totalMobile = 0;
            foreach ($styleMobiles as $styleMobile) {
                if ($style->product_style_sku == $styleMobile->product_style_sku) {
                    $style->total += $styleMobile->total;
                    $style->totalMobile = $styleMobile->total;
                    $styleMobile->is_sync = true;
                }
            }
        }
        foreach ($styleMobiles as $styleMobile) {
            if (empty($styleMobile->is_sync)) {
                unset($styleMobile->is_sync);
                $styleMobile->totalPC = 0;
                $styleMobile->totalMobile = $styleMobile->total;
                $styles[] = $styleMobile;
            }
        }

        return $styles;
    }

    public function getStyleColorsMobileWip($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;

        $styleColorsQuery = DB::table('printer_product')
            ->select(DB::raw('CONCAT(style_sku, color_sku) as style_color'));
        if ($warehouse_id > 0) {
            $styleColorsQuery->join('printer', 'printer.id', 'printer_product.printer_id')
                ->where('printer.warehouse_id', $warehouse_id);
        }

        $styleColors = $styleColorsQuery->pluck('style_color')->toArray();

        return $styleColors;
    }

    public function countPendingStyle($args, $styleColors, $is_mobile_wip = false)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $account_id = $args['account_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('product_style.name, sale_order_item.product_style_sku, COUNT(*) total'))
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->where('sale_order.is_xqc', 0)
            ->where('sale_order.is_eps', 0)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $this->optimizeFilter($st);
        $this->excludeBulkOrder($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if (!empty($store_id) && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        if ($account_id > 0) {
            $st->where('sale_order.account_id', $account_id);
        }

        if ($is_mobile_wip) {
            $st->whereIn('sale_order_item.product_style_color_sku', $styleColors);
        } else {
            $st->whereNotIn('sale_order_item.product_style_color_sku', $styleColors);
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->groupBy('sale_order_item.product_style_sku')
            ->orderBy('total', 'DESC');

        if (isset($_GET['debug'])) {
            print_r([$args, $st->toSql()]);
        }

        return $st->get();
    }

    public function countPendingStyleXQC($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('COUNT(*) as total'))
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $st->where('sale_order.is_xqc', 1);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if (!empty($store_id) && !empty($args['priorityStores']) && in_array($store_id, $args['priorityStores'])) {
            $st->where('sale_order_item_barcode.store_id', $store_id);
        } elseif (!empty($args['priorityStores'])) {
            $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
        }

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function countPendingStyleEps($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('COUNT(*) as total'))
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->where('sale_order.is_eps', 1)
            ->where('sale_order.is_xqc', 0)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if (!empty($store_id) && !empty($args['priorityStores']) && in_array($store_id, $args['priorityStores'])) {
            $st->where('sale_order_item_barcode.store_id', $store_id);
        } elseif (!empty($args['priorityStores'])) {
            $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
        }

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $this->excludeBulkOrder($st);
        $r = $st->first();
        $r->sql = interpolateQuery($st);

        return $r;
    }

    public function fetchBarcodeByPrintedID($id)
    {
        $query = SaleOrderItemBarcode::query()
            ->selectRaw('
                sale_order.is_xqc,
                sale_order.created_at,
                sale_order.is_eps,
                sale_order.account_id,
                sale_order.external_number order_external_number,
                sale_order.is_fba_order is_fba,
                sale_order.order_type,
                sale_order.order_date,
                sale_order.is_manual,
                sale_order_item.print_side,
                sale_order_item.print_sides,
                sale_order_item.ink_color,
                sale_order_item.ink_color_detected_at,
                sale_order_item.id as order_item_id,
                sale_order_item.is_rbt,
                sale_order_item_barcode.label_id,
                sale_order_item_barcode.label_urgent,
                sale_order_item_barcode.id,
                sale_order_item_barcode.sku,
                sale_order_item_barcode.order_id,
                sale_order_item_barcode.barcode_number,
                sale_order_item_barcode.order_quantity,
                sale_order_item_barcode.print_method,
                product.color as color_formatted,
                product.size as size_formatted,
                product.style as style_formatted,
                product_style.id as product_style_id,
                product_style.type as product_type,
                product_style.sku as product_style_sku,
                sale_order_account.name as account_name,
                store.name as store_name,
                store.code as store_code,
                store.id as store_id,
                store.is_resize as is_resize,
                pretreat_preset.preset_name as pretreat_name,
                (
                    select sale_order_item_image.custom_platen from sale_order_item_image
                    where sale_order_item_image.order_item_id = sale_order_item.id
                        and sale_order_item_image.custom_platen = "16x18"
                    limit 1
                ) as custom_platen
            ')
            ->join('sale_order_item', function ($join) {
                $join->on('sale_order_item.id', 'sale_order_item_barcode.order_item_id')
                    ->leftJoin('pretreat_preset_sku', function ($subJoin) {
                        $subJoin->on('sale_order_item.product_style_sku', 'pretreat_preset_sku.style');
                        $subJoin->on('sale_order_item.product_color_sku', 'pretreat_preset_sku.color');
                        $subJoin->leftJoin('pretreat_preset', 'pretreat_preset.id', '=', 'pretreat_preset_sku.pretreat_preset_id');
                    });
            })
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_account', 'sale_order_account.id', '=', 'sale_order.account_id')
            ->leftJoin('store', 'store.id', '=', 'sale_order.store_id')
            ->where('barcode_printed_id', $id)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::NOT_DELETED)
            ->orderByDesc('custom_platen')
            ->orderBy('product.sku', 'ASC')
            ->orderBy('sale_order.order_time', 'ASC')
            ->orderBy('sale_order_item_barcode.id', 'ASC');

        return $query->get();
    }

    public function countPendingStore($args, $isPriorityStore = false)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $st = DB::table('store')
            ->select(DB::raw('store.id, store.name, store.code, store.account_id,  COUNT(*) as total'))
            ->join('sale_order', 'store.id', 'sale_order.store_id')
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);

        if ($warehouse_id != null) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['priorityStores'])) {
            if ($isPriorityStore) {
                $st->whereIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
                if (empty($args['is_top_screen']) && empty($args['is_manual']) && (empty($args['store_id']) || !in_array($args['store_id'], $args['priorityStores']))) {
                    $st->leftJoin('product_roulette', function ($join) {
                        $join->on('product_roulette.product_id', '=', 'sale_order_item.product_id')
                            ->where('product_roulette.is_active', ProductRoulette::STATUS_ACTIVE);
                    })
                        ->whereNull('product_roulette.id');
                }
            }
        } elseif ($isPriorityStore) {
            return collect([]);
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);

        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->groupBy('sale_order_item_barcode.store_id')
            ->orderBy('total', 'DESC')
            ->get();
    }

    public function countPendingWarehouse($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? 0;
        $st = DB::table('sale_order')
            ->select(DB::raw('sale_order_item_barcode.warehouse_id, COUNT(*) as total'))
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);

        if (!empty($args['store_id']) && !empty($args['priorityStores']) && in_array($args['store_id'], $args['priorityStores'])) {
            $st->where('sale_order_item_barcode.store_id', $args['store_id']);
        } elseif (!empty($args['priorityStores'])) {
            $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
        }

        $query = $st->groupBy('sale_order_item_barcode.warehouse_id');
        $result = $query->first();
        $sql = interpolateQuery($query);

        if (empty($result)) {
            $result = new \stdClass();
            $result->warehouse_id = null;
            $result->total = 0;
        }

        $result->name = Warehouse::where('id', $result->warehouse_id)->first()->name ?? null;
        $result->sql = $sql;

        return $result;
    }

    public function countPendingAccount($params)
    {
        $warehouse_id = $params['warehouse_id'] ?? 0;
        $st = DB::table('sale_order')
            ->select(DB::raw('sale_order_account.id, sale_order_account.name, COUNT(*) as total'))
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('sale_order_account', 'sale_order_account.id', 'sale_order.account_id');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $params);

        if ($warehouse_id > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $warehouse_id);
        }

        if (!empty($args['store_id']) && !empty($args['priorityStores']) && in_array($args['store_id'], $args['priorityStores'])) {
            $st->where('sale_order_item_barcode.store_id', $args['store_id']);
        } elseif (!empty($args['priorityStores'])) {
            $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->groupBy('sale_order_account.id')
            ->orderBy('total', 'DESC')
            ->get()
            ->whereNotNull('id');
    }

    public static function fetchPendingBarcodePrintedMobile($limit = 10)
    {
        $q = DB::table('barcode_printed')->select('barcode_printed.id')
            ->where('convert_status', BarcodePrinted::INACTIVE)->where('print_method', BarcodePrinted::METHOD_DTG)
            ->whereNotNull('color_sku');

        return $q->orderBy('barcode_printed.id', 'ASC')
            ->take($limit)
            ->pluck('id');
    }

    public function fetchPendingBarcodePrinted()
    {
        $st = DB::table('barcode_printed');

        return $st->where('convert_status', 0)->where('print_method', BarcodePrinted::METHOD_DTG)
            ->whereNull('color_sku')
            ->first();
    }

    public function fetchPendingBarcodeOrmtPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->whereNull('color_sku')
            ->where('print_method', BarcodePrinted::METHOD_UV)
            ->whereHas('barcodes.orderItem.productStyle', function ($query) {
                $query->whereNotIn('type', ['Poster', 'Sticker']);
            })
            ->first();
    }

    public function fetchPendingBarcodeDTFPrinted()
    {
        $st = DB::table('barcode_printed');

        return $st->where('convert_status', 0)
            ->whereNull('color_sku')
            ->whereIn('print_method', [BarcodePrinted::METHOD_DTF, BarcodePrinted::METHOD_DTF_FILM, BarcodePrinted::METHOD_DTF_NECK])
            ->first();
    }

    public function fetchPendingBarcodeBlankShirtPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->where('print_method', BarcodePrinted::METHOD_BLANK)
            ->whereNull('color_sku')
            ->first();
    }

    public function fetchPendingBarcodePretreatedShirtPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->where('print_method', BarcodePrinted::METHOD_PRETREATED)
            ->whereNull('color_sku')
            ->first();
    }

    public function fetchPendingBarcodeInsertPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->where('is_insert', BarcodePrinted::ACTIVE)
            ->first();
    }

    public function updateBarcodePrinted($id, $data)
    {
        try {
            return DB::table('barcode_printed')
                ->where('id', $id)->update($data);
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    public function fetchBarcodePrinted($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : 10;
        $keyword = !empty($input['keyword']) ? trim($input['keyword']) : '';
        $pending = !empty($input['pending']) ? trim($input['pending']) : '';
        $print_status = !empty($input['print_status']) ? trim($input['print_status']) : '';
        $warehouse_id = !empty($input['warehouse_id']) ? trim($input['warehouse_id']) : '';

        $st = DB::table('barcode_printed')
            ->select([
                'barcode_printed.*', 'employee.code', 'employee.name as employee_name',
                'sale_order_account.name as account_name',
                'store.name as store_name',
                'product_style.name as style_name',
                'store.code as store_code',
                'product_color.name as color_name'
            ]);

        if ($keyword != '') {
            $st->join('sale_order_item_barcode', function ($join) use ($keyword) {
                $join->on('sale_order_item_barcode.barcode_printed_id', 'barcode_printed.id');
                $join->where(function ($query) use ($keyword) {
                    $query->where('sale_order_item_barcode.sku', $keyword);
                    $query->orWhere('sale_order_item_barcode.label_id', '=', $keyword);
                });
            });
        }

        $st->leftJoin('employee', 'employee.id', 'barcode_printed.employee_id')
            ->leftJoin('sale_order_account', 'sale_order_account.id', 'barcode_printed.account_id')
            ->leftJoin('store', 'store.id', 'barcode_printed.store_id')
            ->leftJoin('product_style', 'product_style.sku', 'barcode_printed.style_sku')
            ->leftJoin('product_color', 'product_color.sku', 'barcode_printed.color_sku');

        if ($pending != '') {
            $st->where(function ($query) {
                $query->where('barcode_printed.user_id', '!=', RbtProduct::RBT_EMPLOYEE_ID);

                $query->where('barcode_printed.print_status', 0);
            });
        }

        if (!empty($warehouse_id)) {
            $st->where('barcode_printed.warehouse_id', $warehouse_id);
        }

        if (!empty($input['employee_id']) && \auth()->user()->is_admin == false) {
            $st->where('barcode_printed.employee_id', $input['employee_id']);
        }
        if (!empty($input['is_top_screen'])) {
            $st->where('barcode_printed.is_top_style', BarcodePrinted::ACTIVE);
        } else {
            $st->where('barcode_printed.is_top_style', BarcodePrinted::INACTIVE);
        }

        if ($print_status != '') {
            $st->where('barcode_printed.print_status', $print_status);
        }

        $listWip = $st->where('barcode_printed.print_method', ProductStyle::METHOD_DTG)
            ->orderBy('barcode_printed.id', 'desc')->paginate($limit);

        $barcodePrintedId = collect($listWip->items())->pluck('id')->toArray();
        $barcodeRBTPrintedId = [];

        if (!empty($barcodePrintedId)) {
            $barcodeRBTPrintedId = SaleOrderItem::query()
                ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
                ->where('sale_order_item.is_rbt', SaleOrderItem::IS_RBT)
                ->whereIn('sale_order_item_barcode.barcode_printed_id', $barcodePrintedId)
                ->pluck('sale_order_item_barcode.barcode_printed_id')
                ->toArray();
        }

        foreach ($listWip->items() as $wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();
            $wip->is_rbt = in_array($wip->id, $barcodeRBTPrintedId);

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }
        }

        return $listWip;
    }

    public function insertBarcodePrinted($data)
    {
        return DB::table('barcode_printed')->insertGetId($data);
    }

    public function updateBarcodePrintedItem($args)
    {
        $styleColors = $this->getStyleColorsMobileWip($args);
        $time = date('Y-m-d H:i:s');
        $st = DB::table('sale_order_item_barcode')
            ->select(['sale_order_item_barcode.id'])
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product', 'product.sku', 'sale_order_item.product_sku');
        $this->optimizeFilter($st);
        $this->appendFilter($st, $args);

        if ($args['warehouse_id'] > 0) {
            $st->where('sale_order_item_barcode.warehouse_id', $args['warehouse_id']);
        }

        $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item.is_rbt', '!=', SaleOrderItem::IS_RBT)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item_barcode.is_deleted', 0);

        if (!empty($args['priorityStores'])) {
            if (!empty($args['store_id']) && in_array($args['store_id'], $args['priorityStores'])) {
                $st->where('sale_order_item_barcode.store_id', $args['store_id']);
            } else {
                $st->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
            }
        }

        if ($args['is_bulk_order'] == 1) {
            $st->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER])
                ->where('sale_order.is_xqc', '<>', 1)
                ->where('sale_order.order_quantity', '>=', env('BULK_ORDER_MIN_ITEM_QUANTITY', 20));
        } elseif ($args['is_tiktok'] == 1) {
            $st->whereIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
        } else {
            if (empty($args['all'])) {
                $st->whereNotIn('sale_order.order_type', [SaleOrder::ORDER_TYPE_TIKTOK_ORDER, SaleOrder::ORDER_TYPE_LABEL_ORDER]);
            }

            $this->excludeBulkOrder($st);

            if ($args['is_manual'] == SaleOrder::IS_MANUAL) {
                $st->where('sale_order.is_manual', SaleOrder::IS_MANUAL);
                $st->whereNull('sale_order_item_barcode.employee_reroute_id');
                $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
            } elseif ($args['is_reroute'] == 1) {
                $st->whereNotNull('sale_order_item_barcode.employee_reroute_id');
                $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
            } elseif ($args['is_fba'] == 1) {
                $st->where('sale_order.is_fba_order', SaleOrder::ACTIVE);
            } elseif ($args['is_reprint'] != 1) {
                if ($args['is_xqc'] != 1 && $args['is_eps'] != 1) {
                    if (!empty($args['store_id']) && $args['store_id'] > 0) {
                        $st->where('sale_order_item_barcode.store_id', $args['store_id']);
                    }

                    if (!empty($args['account_id']) && $args['account_id'] > 0) {
                        $st->where('sale_order.account_id', $args['account_id']);
                    }

                    if ($args['style_sku'] != null) {
                        $st->where('sale_order_item.product_style_sku', $args['style_sku']);
                    }

                    $st->whereRaw(' ( sale_order.is_xqc = 0 AND sale_order.is_eps = 0 ) ');
                    $st->whereNotIn('sale_order_item.product_style_color_sku', $styleColors);
                } elseif ($args['is_xqc'] == 1) {
                    $st->where('sale_order.is_xqc', 1);
                } else {
                    $st->where('sale_order.is_eps', 1);
                    $st->where('sale_order.is_xqc', 0);
                }

                // o phai reprint
                if (empty($args['priorityStores']) || empty($args['store_id']) || !in_array($args['store_id'], $args['priorityStores'])) {
                    $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
                    $st->whereNull('sale_order_item_barcode.label_root_id');
                    $st->where('sale_order.is_manual', SaleOrder::IS_NOT_MANUAL);
                    $st->whereNull('sale_order_item_barcode.employee_reroute_id');
                    $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
                }
            } else {
                //chung them dong duoi de reprint se khong lay cac don reprint cua manual
                $st->where('sale_order.is_manual', SaleOrder::IS_NOT_MANUAL);
                $st->whereNotNull('sale_order_item_barcode.label_root_id');
                $st->whereNull('sale_order_item_barcode.employee_reroute_id');
                $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
            }
        }

        if (isset($args['count']) && $args['count'] == 1) {
            return ['sql' => $st->toSql(), 'arg' => $args, 'count' => $st->count()];
        }

        $st->whereRaw('1 = 1 LIMIT ?', [$args['limit']]);
        $st->update([
            'employee_pull_id' => $args['employee_id'],
            'barcode_printed_id' => $args['barcode_printed_id'],
            'print_barcode_at' => $time
        ]);

        $saleOrderIds = SaleOrderItemBarcode::where('barcode_printed_id', $args['barcode_printed_id'])->pluck('order_id')->toArray();
        $chunkOrderIds = array_chunk(array_unique($saleOrderIds), 100);

        foreach ($chunkOrderIds as $orderIds) {
            // Update barcode printed status and time
            $newOrderIds = SaleOrder::whereIn('id', $orderIds)->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();

            if (!empty($newOrderIds)) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
            }
            SaleOrder::whereIn('id', $orderIds)
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update([
                    'order_status' => SaleOrder::IN_PRODUCTION,
                    'order_production_at' => $time
                ]);
        }

        DB::table('barcode_printed')->where('id', $args['barcode_printed_id'])
            ->update(['quantity' => count($saleOrderIds)]);
    }

    public function updateLastBarcodePrintedTime($data)
    {
        $check = DB::table('barcode_printed_time')
            ->where('store_id', $data['store_id'] ?? null)
            ->where('account_id', $data['account_id'] ?? null)
            ->where('is_xqc', $data['is_xqc'] ?? null)
            ->where('is_eps', $data['is_eps'] ?? null)
            ->where('is_reprint', $data['is_reprint'] ?? null)
            ->where('style_sku', $data['style_sku'] ?? null)
            ->where('is_manual', $data['is_manual'] ?? null)
            ->where('is_reroute', $data['is_reroute'] ?? null)
            ->where('is_fba', $data['is_fba'] ?? null)
            ->where('is_tiktok', $data['is_tiktok'] ?? null)
            ->where('is_bulk_order', $data['is_bulk_order'] ?? null)
            ->where('warehouse_id', $data['warehouse_id'] ?? null)
            ->whereNull('color_sku');

        if (!empty($data['print_method'])) {
            $check = $check->where('print_method', $data['print_method']);
        }

        $check = $check->first();

        if ($check) {
            DB::table('barcode_printed_time')->where('id', $check->id)
                ->update(['printed_at' => date('Y-m-d H:i:s')]);
        } else {
            $list = ['store_id', 'warehouse_id', 'account_id', 'is_xqc', 'style_sku', 'is_reprint', 'is_manual', 'is_reroute', 'is_fba', 'is_eps', 'color_sku', 'is_tiktok', 'is_bulk_order'];
            $insert['printed_at'] = date('Y-m-d H:i:s');

            foreach ($list as $item) {
                $insert[$item] = $data[$item] ?? null;
            }

            if (!empty($data['print_method'])) {
                $insert['print_method'] = $data['print_method'];
            }

            DB::table('barcode_printed_time')->insert($insert);
        }
    }

    public function getStartEndBarcodePrinted($id)
    {
        return DB::table('barcode_printed')->select('*')
            ->where('id', $id)->first();
    }

    public function getLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)->first();
    }

    public function updateBarcodeById($id, $rawData)
    {
        return DB::table('sale_order_item_barcode')
            ->where('id', $id)
            ->whereNull('pulled_at')
            ->update($rawData);
    }

    public function getItemBarcodeById($id)
    {
        return DB::table('sale_order_item_barcode')
            ->where('id', $id)
            ->first();
    }

    public function getBarcodePrintedById($id)
    {
        return DB::table('barcode_printed')
            ->where('id', $id)
            ->first();
    }

    private function countBarcodePendingViaPrintMethod($warehouseId, $printMethod)
    {
        $query = DB::table('sale_order_item_barcode')
            ->join('sale_order', 'sale_order_item_barcode.order_id', '=', 'sale_order.id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
            ->select(
                DB::raw('COUNT(*) as count'),
                'product_style.name',
                'product_style.sku',
            );
        $this->optimizeFilter($query);

        return $query->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.retry_convert', '<', SaleOrderItemBarcode::MAX_RETRY)
            ->where('sale_order_item_barcode.warehouse_id', $warehouseId)
            ->where('sale_order_item_barcode.print_method', $printMethod)
            ->groupBy('product_style.sku')
            ->get();
    }

    public function fetchDtf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'];
        $wips = BarcodePrinted::listBarcodeDtf($warehouse_id, $limit, $printMethod);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }

            return $wip;
        });

        return $wips;
    }

    public function historyDtf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;
        $wips = BarcodePrinted::listHistoryBarcodeDtf($warehouse_id, $limit, $label_id, $printMethod);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }

            return $wip;
        });

        return $wips;
    }

    private function getOrderTypeFromKeys($data, $keys)
    {
        $filtered = array_filter($keys, function ($key) use ($data) {
            return isset($data[$key]) && $data[$key] !== null;
        });
        $type = reset($filtered);

        return $type ?: null;
    }

    public function countPendingPriorityStoreByOrderType($input)
    {
        $query = SaleOrderItem::query()
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('store', 'sale_order_item.store_id', '=', 'store.id')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id');
        $this->optimizeFilter($query);

        if (!empty($input['priorityStores'])) {
            $query = $query->whereIn('store.id', $input['priorityStores']);
        }

        return $query->where('sale_order.order_type', $input['order_type'] ?? 3)
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order.warehouse_id', $input['warehouse_id'] ?? null)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
            ->whereNotNull('sale_order_item_barcode.print_method')
            ->where('sale_order_item_barcode.print_method', '!=', '')
            ->groupBy('sale_order_item_barcode.store_id')
            ->selectRaw('COUNT(*) as count, store.id, store.code')->get();
    }

    public function countByOrderType($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $orderType = $input['order_type'];

        switch ($orderType) {
            case SaleOrder::ORDER_TYPE_BLANK:
                $printMethod = BarcodePrinted::METHOD_BLANK;
                break;
            default:
                $printMethod = BarcodePrinted::METHOD_PRETREATED;
                break;
        }

        $productStyles = SaleOrderItem::query()
            ->select('sale_order_item.product_style_sku', DB::Raw('count(*) as count'))
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id');
        $this->optimizeFilter($productStyles);
        $productStyles = $productStyles->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order.warehouse_id', $warehouse_id)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
            ->where('print_method', $printMethod);
        if (!empty($input['store_id'])) {
            $productStyles = $productStyles->where('sale_order_item.store_id', $input['store_id']);
        } else {
            $productStyles = $productStyles->whereNotIn('sale_order_item.store_id', $input['priorityStores']);
        }

        $productStyles = $productStyles->groupBy('sale_order_item.product_style_sku')->get();
        $total = 0;
        $styles = $productStyles->pluck('product_style_sku')->toArray();
        $styleName = ProductStyle::query()->whereIn('sku', $styles)->get()->pluck('name', 'sku')->toArray();
        $results = [];

        foreach ($productStyles as $key => $style) {
            $time = BarcodePrinted::query()->where('warehouse_id', $warehouse_id)
                ->where('style_sku', $style->product_style_sku)
                ->orderBy('id', 'DESC')
                ->first();
            $total += $style->count;
            $results[] = [
                'name' => $styleName[$style->product_style_sku],
                'count' => $style->count,
                'sku' => $style->product_style_sku,
                'last_printed_at' => $time->created_at ?? null
            ];
        }

        return [
            'total' => $total,
            'data' => array_values(collect($results)->sortBy('last_printed_at')->toArray()),
        ];
    }

    public function fetchByOrderType($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $wips = BarcodePrinted::with([
            'employeeConvert:id,name',
            'style:sku,name',
            'store:id,name,code',
        ])
            ->select('barcode_printed.*')
            ->where('print_method', SaleOrder::ARRAY_ORDER_TYPE[$input['order_type']])
            ->where('warehouse_id', $warehouse_id)
            ->where('print_status', BarcodePrinted::INACTIVE)
            ->orderByDesc('id')
            ->paginate($limit);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            return $wip;
        });

        return $wips;
    }

    public function historyByOrderType($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;
        $wips = BarcodePrinted::with([
            'employeeConvert:id,name',
            'style:sku,name'
        ])->select('barcode_printed.*')
            ->when(!empty($label_id), function ($q) use ($label_id) {
                $q->join('sale_order_item_barcode', 'barcode_printed.id', '=', 'sale_order_item_barcode.barcode_printed_id')
                    ->where('sale_order_item_barcode.label_id', 'LIKE', '%' . $label_id . '%');
            })
            ->where('barcode_printed.print_method', SaleOrder::ARRAY_ORDER_TYPE[$input['order_type']])
            ->where('barcode_printed.warehouse_id', $warehouse_id)
            ->where('barcode_printed.print_status', BarcodePrinted::ACTIVE)
            ->orderByDesc('id')
            ->paginate($limit);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            return $wip;
        });

        return $wips;
    }

    public function confirmPrintByOrderType($input)
    {
        $style_sku = $input['style_sku'];
        $limit = $input['quantity'];
        $employee_id = $input['employee_id'];
        $warehouse_id = config('jwt.warehouse_id');
        $orderType = $input['order_type'];
        switch ($orderType) {
            case SaleOrder::ORDER_TYPE_BLANK:
                $printMethod = BarcodePrinted::METHOD_BLANK;
                break;
            default:
                $printMethod = BarcodePrinted::METHOD_PRETREATED;
                break;
        }

        try {
            DB::beginTransaction();
            $barcode_printed = BarcodePrinted::create([
                'quantity_input' => $limit,
                'style_sku' => $style_sku,
                'employee_id' => $employee_id,
                'warehouse_id' => $warehouse_id,
                'user_id' => Auth::id(),
                'convert_percent' => 0,
                'created_at' => Carbon::now(),
                'print_status' => BarcodePrinted::INACTIVE,
                'print_method' => SaleOrder::ARRAY_ORDER_TYPE[$input['order_type']],
                'convert_status' => BarcodePrinted::FAILED,
                'store_id' => $input['store_id'] ?? null,
            ]);

            $quantityQuery = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
                ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id');
            $this->optimizeFilter($quantityQuery);
            $query = $quantityQuery->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('sale_order.is_test', '=', SaleOrder::NOT_TEST)
                ->where('print_method', $printMethod)
                ->where('sale_order_item.product_style_sku', '=', $style_sku)
                ->where('sale_order_item_barcode.is_deleted', '=', SaleOrderItemBarcode::ACTIVE)
                ->where('sale_order_item_barcode.barcode_printed_id', '=', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
                ->where('sale_order_item_barcode.warehouse_id', '=', $warehouse_id);
            if (!empty($input['store_id'])) {
                $query->where('sale_order_item_barcode.store_id', '=', $input['store_id']);
            } else {
                $query->whereNotIn('sale_order_item_barcode.store_id', $input['priorityStores']);
            }

            $quantity = $query->orderBy('sale_order_item_barcode.id', 'ASC')
                ->whereRaw('1 = 1 LIMIT ' . $limit)
                ->update([
                    'sale_order_item_barcode.barcode_printed_id' => $barcode_printed->id,
                    'sale_order_item_barcode.employee_pull_id' => $employee_id,
                    'sale_order_item_barcode.print_barcode_at' => Carbon::now()
                ]);
            $barcode_printed->quantity = $quantity;
            $barcode_printed->convert_status = BarcodePrinted::INACTIVE;
            $barcode_printed->save();

            // change status order to in production
            $order_ids = SaleOrderItemBarcode::getArrayOrderIds($barcode_printed->id);
            SaleOrder::changeStatusToInProduction($order_ids);
            DB::commit();

            return $barcode_printed;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function confirmMobilePrintBarcode($input)
    {
        $styleName = $input['style'];
        $colorName = $input['color'];
        $limit = $input['quantity'];
        $employeeCode = $input['employee_code'];
        $checkAttributes = $this->checkAttributesBeforePrint($styleName, $colorName, $employeeCode);

        if (!$checkAttributes['status']) {
            throw new Exception($checkAttributes['message'], $checkAttributes['code']);
        }

        $style = $checkAttributes['style'];
        $color = $checkAttributes['color'];
        $employee = $checkAttributes['employee'];
        $checkWip = $this->checkWipBeforePrint($style, $color, $employee);

        if ($checkWip) {
            return $checkWip;
        }

        $warehouseId = $employee->warehouse_id;

        try {
            DB::beginTransaction();
            $barcodePrinted = BarcodePrinted::create([
                'quantity_input' => $limit,
                'style_sku' => $style->sku,
                'employee_id' => $employee->id,
                'warehouse_id' => $warehouseId,
                'convert_percent' => 0,
                'created_at' => Carbon::now(),
                'print_status' => BarcodePrinted::INACTIVE,
                'convert_status' => BarcodePrinted::FAILED,
                'color_sku' => $color->sku,
                'status_print' => BarcodePrinted::STATUS_CONVERTING
            ]);
            $quantityQuery = SaleOrderItemBarcode::join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
                ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
                ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku');
            $this->optimizeFilter($quantityQuery);
            $quantity = $quantityQuery->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('sale_order.is_test', SaleOrder::NOT_TEST)
                ->where('sale_order_item.product_style_sku', $style->sku)
                ->where('sale_order_item.product_color_sku', $color->sku)
                ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
                ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
                ->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED)
                ->where('sale_order_item_barcode.warehouse_id', $warehouseId)
                ->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL)
                ->whereNull('sale_order_item_barcode.label_root_id')
                ->whereNull('sale_order_item_barcode.employee_reroute_id')
                ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
                ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
                ->where('sale_order.is_eps', SaleOrder::INACTIVE)
                ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
                ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
                ->where('product_style.type', '!=', ProductStyle::TYPE_INSERT)
                ->whereRaw('1 = 1 LIMIT ' . $limit)
                ->update([
                    'sale_order_item_barcode.barcode_printed_id' => $barcodePrinted->id,
                    'sale_order_item_barcode.employee_pull_id' => $employee->id,
                    'sale_order_item_barcode.print_barcode_at' => Carbon::now()
                ]);

            if ($quantity == 0) {
                DB::rollBack();
                $barcodeExists = DB::table('barcode_printed')
                    ->where('style_sku', $style->sku)
                    ->where('color_sku', $color->sku)
                    ->latest('id')
                    ->first();

                $employeeExists = DB::table('employee')
                    ->where('id', $barcodeExists?->employee_id)
                    ->first();

                $employeeName = $employeeExists ? $employeeExists->name : 'Other employee';
                throw new Exception("$employeeName has already printed this WIP. \nDây WIP đã được in bởi $employeeName", Response::HTTP_BAD_REQUEST);
            }

            // Notification status converting to employee
            broadcast(new UpdateStatusPrintNotification([
                'batch_id' => $barcodePrinted->id,
                'status' => BarcodePrinted::STATUS_CONVERTING
            ]));

            $barcodePrinted->quantity = $quantity;
            $barcodePrinted->convert_status = BarcodePrinted::INACTIVE;
            $barcodePrinted->save();
            BarcodePrintedTime::updateTimePrint($style->sku, $color->sku, $warehouseId);

            // Sync time printed for style
            BarcodePrintedTime::updateTimePrintForStyle($style->sku, $warehouseId);

            // change status order to in production
            $saleOrderIds = SaleOrderItemBarcode::getArrayOrderIds($barcodePrinted->id);
            $chunkOrderIds = array_chunk(array_unique($saleOrderIds), 100);

            foreach ($chunkOrderIds as $orderIds) {
                $newOrderIds = SaleOrder::whereIn('id', $orderIds)->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();
                if (!empty($newOrderIds)) {
                    handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
                }
                // Update barcode printed status and time
                SaleOrder::whereIn('id', $orderIds)
                    ->where('order_status', SaleOrder::NEW_ORDER)
                    ->update([
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => Carbon::now()
                    ]);
            }

            DB::commit();

            // socket
            if ($barcodePrinted->convert_status == BarcodePrinted::INACTIVE) {
                broadcast(new CountPendingNotification([
                    "{$style->name}_{$color->name}_$warehouseId" => [
                        'total' => $barcodePrinted->quantity,
                        'printed_at' => shiftTimezoneToPST(Carbon::now()),
                        'status' => BarcodePrinted::WIP_STATUS_PENDING_PRINT
                    ]
                ]));

                handleJob(BarcodePrinted::JOB_ALERT_WIP_PENDING_SCAN_BATCH, $barcodePrinted->id);
            }

            $barcodePrinted->employee_name = $employee->name;
            $barcodePrinted->style_name = $style->name;
            $barcodePrinted->color_name = $color->name;

            return $barcodePrinted;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    protected function checkAttributesBeforePrint($styleName, $colorName, $employeeCode)
    {
        $style = ProductStyle::findByName($styleName);

        if (!$style) {
            return [
                'status' => false,
                'message' => 'Style not found',
                'code' => Response::HTTP_NOT_FOUND
            ];
        }

        $color = ProductColor::findByName($colorName);
        $product = Product::findParentByStyle($styleName);

        if (!$product) {
            return [
                'status' => false,
                'message' => 'Product not found',
                'code' => Response::HTTP_NOT_FOUND
            ];
        }

        if (!$color || !str_contains($product->color, $colorName)) {
            return [
                'status' => false,
                'message' => 'Color not found',
                'code' => Response::HTTP_NOT_FOUND
            ];
        }

        $employee = Employee::findEmployeeByDepartment($employeeCode, Department::DEPARTMENT_PULLING);

        if (!$employee) {
            return [
                'status' => false,
                'message' => 'Employee not found',
                'code' => Response::HTTP_NOT_FOUND
            ];
        }

        $checkPrinter = DB::table('printer_product')
            ->join('printer', 'printer.id', 'printer_product.printer_id')
            ->where('printer.warehouse_id', $employee->warehouse_id)
            ->where('printer_product.style_sku', $style->sku)
            ->where('printer_product.color_sku', $color->sku)
            ->first();

        // check config printer
        if (!$checkPrinter) {
            return [
                'status' => false,
                'message' => "Unable to print because the style and color have not been configured for any printer. \nKhông thể in do chưa có máy in nào được cài đặt style và color này.",
                'code' => Response::HTTP_BAD_REQUEST
            ];
        }

        // check printer online
        if (is_null($checkPrinter->last_printer_active_at) || (Carbon::now()->diffInSeconds(Carbon::parse($checkPrinter->last_printer_active_at)) > Printer::PRINTER_TIME_OUT)) {
            return [
                'status' => false,
                'message' => "Unable to print because the Durian app is not turned on. \nKhông thể in do ứng dụng Durian chưa được bật.",
                'code' => Response::HTTP_BAD_REQUEST
            ];
        }

        return [
            'status' => true,
            'style' => $style,
            'color' => $color,
            'employee' => $employee,
        ];
    }

    protected function checkWipBeforePrint($style, $color, $employee)
    {
        // check another wip of current employee is pending confirm batch
        $checkWipEmployee = BarcodePrinted::checkWipPendingByEmployee($employee->id, $employee->warehouse_id);

        if ($checkWipEmployee) {
            $oldStyle = ProductStyle::findByStyleSku($checkWipEmployee->style_sku);
            $oldColor = ProductColor::findBySku($checkWipEmployee->color_sku);
            $oldEmployee = Employee::findById($checkWipEmployee->employee_id);
            $checkWipEmployee->is_wip_pending = true;
            $checkWipEmployee->style_name = $oldStyle->name;
            $checkWipEmployee->color_name = $oldColor->name;
            $checkWipEmployee->employee_name = $oldEmployee->name;

            return $checkWipEmployee;
        }

        // check another wip of current employee is pending deduct
        $checkWipDeductEmployee = BarcodePrinted::checkWipPendingDeductByEmployee($employee->id, $employee->warehouse_id);

        if ($checkWipDeductEmployee) {
            $oldStyle = ProductStyle::findByStyleSku($checkWipDeductEmployee->style_sku);
            $oldColor = ProductColor::findBySku($checkWipDeductEmployee->color_sku);
            $oldEmployee = Employee::findById($checkWipDeductEmployee->employee_id);
            $checkWipDeductEmployee->is_deduct_pending = true;
            $checkWipDeductEmployee->style_name = $oldStyle->name;
            $checkWipDeductEmployee->color_name = $oldColor->name;
            $checkWipDeductEmployee->employee_name = $oldEmployee->name;

            return $checkWipDeductEmployee;
        }

        // check wip converted and not print yet
        $checkWipPending = BarcodePrinted::checkWipPending($style->sku, $color->sku, $employee->warehouse_id);

        if ($checkWipPending) {
            if ($employee->is_leader != Employee::IS_LEADER && $employee->id != $checkWipPending->employee_id) {
                $checkWipPending->is_another_employee = true;
                $employeeConvert = Employee::find($checkWipPending->employee_id);
                $checkWipPending->employee_name = $employeeConvert?->name;

                return $checkWipPending;
            }
            $oldStyle = ProductStyle::findByStyleSku($checkWipPending->style_sku);
            $oldColor = ProductColor::findBySku($checkWipPending->color_sku);
            $oldEmployee = Employee::findById($checkWipPending->employee_id);
            $checkWipPending->is_wip_pending = true;
            $checkWipPending->style_name = $oldStyle->name;
            $checkWipPending->color_name = $oldColor->name;
            $checkWipPending->employee_name = $oldEmployee->name;

            return $checkWipPending;
        }

        // check wip pending deduct
        $checkWipPendingDeduct = BarcodePrinted::checkWipPendingDeduct($style->sku, $color->sku, $employee->warehouse_id);
        if ($checkWipPendingDeduct) {
            $oldStyle = ProductStyle::findByStyleSku($checkWipPendingDeduct->style_sku);
            $oldColor = ProductColor::findBySku($checkWipPendingDeduct->color_sku);
            $oldEmployee = Employee::findById($checkWipPendingDeduct->employee_id);
            $checkWipPendingDeduct->is_deduct_pending = true;
            $checkWipPendingDeduct->style_name = $oldStyle->name;
            $checkWipPendingDeduct->color_name = $oldColor->name;
            $checkWipPendingDeduct->employee_name = $oldEmployee->name;

            return $checkWipPendingDeduct;
        }

        // check wip convert error or print error
        $checkWipError = BarcodePrinted::checkWipError($style->sku, $color->sku, $employee->warehouse_id);

        if ($checkWipError) {
            if ($employee->is_leader != Employee::IS_LEADER && $employee->id != $checkWipError->employee_id) {
                $employeeConvert = Employee::find($checkWipError->employee_id);
                $checkWipError->employee_name = $employeeConvert?->name;
                $checkWipError->is_another_employee = true;

                return $checkWipError;
            }

            $checkWipError->is_scan_leader = true;

            return $checkWipError;
        }

        return false;
    }

    public function confirmMobileRePrintBarcode($input)
    {
        $leaderCode = $input['leader_code'];
        $department = Department::DEPARTMENT_PULLING;
        $batchId = $input['batch_id'];
        $barcodePrinted = BarcodePrinted::find($batchId);

        if (!$barcodePrinted) {
            throw new Exception('Batch does not exists', Response::HTTP_NOT_FOUND);
        }

        $leader = Employee::findLeaderByDepartment($leaderCode, $department);

        if (!$leader) {
            throw new Exception('Leader does not exist', Response::HTTP_NOT_FOUND);
        }

        $printer = Printer::findByStyleColorWarehouse($barcodePrinted);

        if (!$printer) {
            throw new Exception('Printer not found', Response::HTTP_NOT_FOUND);
        }

        // Notification status converting to employee
        broadcast(new UpdateStatusPrintNotification([
            'batch_id' => $barcodePrinted->id,
            'status' => BarcodePrinted::STATUS_PRINTING
        ]));

        $barcodePrinted->reprint_approved_by = $leader->id;
        $barcodePrinted->is_error_print = BarcodePrinted::INACTIVE;
        $barcodePrinted->status_print = BarcodePrinted::STATUS_PRINTING;
        $barcodePrinted->save();

        broadcast(new WipConvertedNotification([
            'id' => $barcodePrinted->id,
            'printer' => $printer->name,
            'url' => env('AWS_S3_URL') . '/barcode/' . $barcodePrinted->id . '.pdf',
            'file_name' => $barcodePrinted->id . '.pdf',
            'device_id' => $printer->device_id,
            'warehouse_id' => $barcodePrinted->warehouse_id
        ]));

        $employee = Employee::findById($barcodePrinted->employee_id);
        $style = ProductStyle::findByStyleSku($barcodePrinted->style_sku);
        $color = ProductColor::findBySku($barcodePrinted->color_sku);
        $barcodePrinted->employee_name = $employee->name;
        $barcodePrinted->style_name = $style->name;
        $barcodePrinted->color_name = $color->name;

        return $barcodePrinted;
    }

    public function mobileListReprintBarcode($input)
    {
        $limit = empty($input['limit']) ? 25 : $input['limit'];
        $barcodePrinted = new BarcodePrinted();

        return $barcodePrinted::listReprintBarcode($limit, $input['warehouse_id']);
    }

    public function mobileBarcodePrinted($input)
    {
        $batchId = $input['batch_id'];
        $employeeCode = $input['employee_code'];
        $employee = Employee::findEmployeeByDepartment($employeeCode, Department::DEPARTMENT_PULLING);

        if (!$employee) {
            throw new Exception('Employee not found', Response::HTTP_NOT_FOUND);
        }

        $warehouseId = $employee->warehouse_id;
        $barcodePrinted = BarcodePrinted::where('id', $batchId)->where('warehouse_id', $warehouseId)
            ->where('convert_status', BarcodePrinted::ACTIVE)
            ->whereNotNull('color_sku')->first();

        if (!$barcodePrinted) {
            throw new Exception('Batch does not exists or not converted yet', Response::HTTP_NOT_FOUND);
        }

        if ($employee->is_leader != Employee::IS_LEADER && $barcodePrinted->employee_id != $employee->id) {
            $employeePrint = Employee::findById($barcodePrinted->employee_id);
            $employeeName = $employeePrint ? $employeePrint->name : 'Other employee';
            throw new Exception("$employeeName has already printed this WIP. \nDây WIP đã được in bởi $employeeName", Response::HTTP_BAD_REQUEST);
        }

        $barcodePrinted->print_status = BarcodePrinted::ACTIVE;
        $barcodePrinted->save();
        $style = DB::table('product_style')->where('sku', $barcodePrinted->style_sku)->first();
        $color = DB::table('product_color')->where('sku', $barcodePrinted->color_sku)->first();
        $countWip = BarcodePrinted::countWipByStyleAndColorAndWarehouse($style->name, $color->name, $warehouseId);

        broadcast(new CountPendingNotification([
            "{$style->name}_{$color->name}_$warehouseId" => [
                'total' => $countWip->total,
                'printed_at' => shiftTimezoneToPST(Carbon::now()),
                'status' => BarcodePrinted::WIP_STATUS_DONE
            ]
        ]));

        return $barcodePrinted;
    }

    public function printError($input)
    {
        $batchId = $input['batch_id'];
        $barcodePrinted = BarcodePrinted::find($batchId);

        if (!$barcodePrinted) {
            throw new Exception('Batch does not exists', Response::HTTP_NOT_FOUND);
        }

        $barcodePrinted->is_error_print = BarcodePrinted::ACTIVE;
        $barcodePrinted->save();

        // Send notification to leader
        handleJob(BarcodePrinted::JOB_SEND_NOTIFICATION, $barcodePrinted);

        return $barcodePrinted;
    }

    public function mobileBarcode($input)
    {
        $styleName = $input['style'];
        $colorName = $input['color'];
        $employeeCode = $input['employee_code'];
        $checkAttributes = $this->checkAttributesBeforePrint($styleName, $colorName, $employeeCode);

        if (!$checkAttributes['status']) {
            throw new Exception($checkAttributes['message'], $checkAttributes['code']);
        }

        $style = $checkAttributes['style'];
        $color = $checkAttributes['color'];
        $employee = $checkAttributes['employee'];
        $checkWip = $this->checkWipBeforePrint($style, $color, $employee);

        if ($checkWip) {
            return $checkWip;
        }

        $warehouseId = $employee->warehouse_id;
        $barcodePrinted = BarcodePrinted::countWipByStyleAndColorAndWarehouse($styleName, $colorName, $warehouseId);

        if ($barcodePrinted->total == 0) {
            throw new Exception('All wips have been printed', Response::HTTP_NOT_FOUND);
        }

        return $barcodePrinted;
    }

    public function mobileBarcodeStatusPrint($input, $id)
    {
        $statusPrint = $input['status_print'];
        $barcodePrinted = BarcodePrinted::find($id);
        $barcodePrinted->status_print = $statusPrint;
        $barcodePrinted->save();

        // Notification status printed or error to employee
        broadcast(new UpdateStatusPrintNotification([
            'batch_id' => $barcodePrinted->id,
            'status' => $statusPrint
        ]));
    }

    public function mobileBarcodeDetail($id)
    {
        $barcodePrinted = BarcodePrinted::with('employeeConvert')->where('id', $id)->first();

        if (!$barcodePrinted) {
            throw new Exception('Batch not found', Response::HTTP_NOT_FOUND);
        }

        return $barcodePrinted;
    }

    public function checkBarcodePrintedById($input)
    {
        $batchId = $input['batch_id'];
        $employeeCode = $input['employee_code'];
        $employee = Employee::findByCode($employeeCode);

        if (!$employee) {
            throw new Exception('Employee not found', Response::HTTP_NOT_FOUND);
        }

        $warehouseId = $employee->warehouse_id;
        $barcodePrinted = BarcodePrinted::where('id', $batchId)->where('warehouse_id', $warehouseId)
            ->where('print_status', BarcodePrinted::ACTIVE)
            ->whereNotNull('color_sku')
            ->first();

        if (!$barcodePrinted) {
            throw new Exception('Batch does not exists or not converted yet', Response::HTTP_NOT_FOUND);
        }

        if ($barcodePrinted->is_deduct) {
            $employeeDeduct = Employee::findById($barcodePrinted->employee_id);
            throw new Exception("This Batch ID has been deducted by {$employeeDeduct->name}. \nBatch này đã được deduct bởi {$employeeDeduct->name}.", Response::HTTP_NOT_FOUND);
        }

        handleJob(BarcodePrinted::JOB_DEDUCTION_INVENTORY, $barcodePrinted->id);
        $barcodePrinted->is_deduct = true;
        $barcodePrinted->save();

        return true;
    }

    public function testPrint($input)
    {
        broadcast(new WipConvertedNotification([
            'id' => $input['id'],
            'printer' => $input['printer'],
            'url' => $input['url'],
            'file_name' => $input['file_name'],
            'device_id' => $input['device_id'],
            'warehouse_id' => 1,
        ]));

        return true;
    }

    public function getLastPrintedTime($barcodePrintedTimes, $type, $warehouse_id, $store_id, $account_id, $value = null)
    {
        $printedTime = null;

        switch ($type) {
            case 'bulk_order':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->is_bulk_order == 1
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'tiktok':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->is_tiktok == 1
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'fba':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->is_fba == 1
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'reroute':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->is_reroute == 1
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'manual':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id
                        && $item->is_manual == SaleOrder::IS_MANUAL;
                })->first();

                break;
            case 'reprint':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($warehouse_id, $store_id, $account_id) {
                    return $item->is_reprint == 1
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'xqc':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($store_id, $warehouse_id, $account_id) {
                    return $item->is_xqc == 1
                        && $item->store_id == $store_id
                        && $item->warehouse_id == $warehouse_id
                        && $item->account_id == $account_id;
                })->first();
                break;
            case 'eps':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($store_id, $warehouse_id, $account_id) {
                    return $item->is_eps == 1
                        && $item->store_id == $store_id
                        && $item->warehouse_id == $warehouse_id
                        && $item->account_id == $account_id;
                })->first();

                break;
            case 'style':
                $printedTime = $barcodePrintedTimes->filter(function ($item) use ($store_id, $warehouse_id, $account_id, $value) {
                    return $item->style_sku == $value
                        && $item->warehouse_id == $warehouse_id
                        && $item->store_id == $store_id
                        && $item->account_id == $account_id;
                })->first();
                break;
        }

        return $printedTime ? $printedTime->printed_at : null;
    }

    public function printBarcode($id)
    {
        try {
            DB::beginTransaction();
            $barcodePrinted = BarcodePrinted::find($id);

            if (!empty($barcodePrinted->printed_at)) {
                throw new Exception('This WIP has been printed.', 500);
            }
            $barcodePrinted->printed_at = now();
            $barcodePrinted->save();

            BarcodePrintedHistory::create([
                'barcode_printed_id' => $id,
                'user_id' => \auth()->user()->id,
                'type' => BarcodePrintedHistory::PRINT_TYPE
            ]);
            DB::commit();

            return $barcodePrinted;
        } catch (Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage());
        }
    }

    public function reprintBarcode($id)
    {
        try {
            DB::beginTransaction();
            $barcodePrinted = BarcodePrinted::find($id);

            if (empty($barcodePrinted)) {
                throw new Exception('WIP not found', 404);
            }
            $barcodePrinted->printed_at = now();
            $barcodePrinted->save();

            BarcodePrintedHistory::create([
                'barcode_printed_id' => $id,
                'user_id' => \auth()->user()->id,
                'type' => BarcodePrintedHistory::REPRINT_TYPE
            ]);

            DB::commit();

            return $barcodePrinted;
        } catch (Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage());
        }
    }

    public function logTimeLineOrderByBarcodePrintedId($barcodePrintedId, $employeeId)
    {
        $saleOrderItemBarcodeRepo = new SaleOrderItemBarcodeRepository();
        $dataSaleOrderItemBarcodes = $saleOrderItemBarcodeRepo->getSaleOrderItemBarcodeByBarcodePrintedId($barcodePrintedId);
        $dataImportHistory = [];
        $dataOrderHistory = [];
        foreach ($dataSaleOrderItemBarcodes as $dataSaleOrderItemBarcode) {
            if (isset($dataOrderHistory[$dataSaleOrderItemBarcode->order_id])) {
                $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] = $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] . ', ' . $dataSaleOrderItemBarcode->label_id;
            } else {
                $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] = $dataSaleOrderItemBarcode->label_id;
            }
        }

        foreach ($dataOrderHistory as $key => $value) {
            $dataImportHistory[] = [
                'user_id' => auth()?->user()?->id,
                'employee_id' => $employeeId,
                'order_id' => $key,
                'type' => SaleOrderHistory::UPDATE_ORDER_WIP_TYPE,
                'message' => "WIP printed with label ID $value in batch ID $barcodePrintedId",
                'created_at' => Carbon::now()->toDateTimeString()
            ];
        }

        SaleOrderHistory::insert($dataImportHistory);
    }

    public function appendFilter(Builder &$query, $args): void
    {
        $inkIndicators = [];
        $productColors = [];

        if (empty($args['is_manual']) && empty($args['is_top_screen']) && (empty($args['store_id']) || empty($args['priorityStores']) || !in_array($args['store_id'], $args['priorityStores']))) {
            $query->leftJoin('product_roulette', function ($join) {
                $join->on('product_roulette.product_id', '=', 'sale_order_item.product_id')
                    ->where('product_roulette.is_active', ProductRoulette::STATUS_ACTIVE);
            })
                ->whereNull('product_roulette.id');
        }
        if (!empty($args['ink_indicators'])) {
            $inkIndicators = explode(',', $args['ink_indicators']);
        }

        if (!empty($args['product_colors'])) {
            $productColors = explode(',', $args['product_colors']);
        }

        if (!empty($args['is_pretreat'])) {
            $query->join('pretreat_preset_sku', function ($join) {
                $join->on('pretreat_preset_sku.style', '=', 'sale_order_item.product_style_sku')
                    ->on('pretreat_preset_sku.color', '=', 'sale_order_item.product_color_sku');
            });
        } elseif (isset($args['is_pretreat']) && (string) $args['is_pretreat'] === '0') {
            $query->leftJoin('pretreat_preset_sku', function ($join) {
                $join->on('pretreat_preset_sku.style', '=', 'sale_order_item.product_style_sku')
                    ->on('pretreat_preset_sku.color', '=', 'sale_order_item.product_color_sku');
            })->whereNull('pretreat_preset_sku.id');
        }

        if (!empty($inkIndicators)) {
            if (in_array('HEXA', $inkIndicators) && in_array('BK', $inkIndicators)) {
                $query->leftJoin('sale_order_item_image', 'sale_order_item_image.order_item_id', 'sale_order_item.id')
                    ->where(function ($condition) {
                        $condition->where('sale_order_item.ink_color', true)
                            ->orWhere(function ($orCondition) {
                                $orCondition->where('sale_order_item_image.is_og', true)
                                    ->where('sale_order_item_image.is_purple', '<>', true);
                            });
                    });
            } elseif (in_array('HEXA', $inkIndicators)) {
                $query->leftJoin('sale_order_item_image', 'sale_order_item_image.order_item_id', 'sale_order_item.id')
                    ->where('sale_order_item_image.is_og', true)
                    ->where('sale_order_item_image.is_purple', '<>', true);
            } elseif (in_array('BK', $inkIndicators)) {
                $query->where('sale_order_item.ink_color', true);
            }
        }

        if (!empty($productColors)) {
            $query->join('product_color', 'product_color.sku', 'sale_order_item.product_color_sku');
            $query->whereIn('sale_order_item.product_color_sku', $productColors);
        }

        if (!empty($args['start']) || !empty($args['end'])) {
            setTimezone();
        }

        if (!empty($args['start'])) {
            $query->where('sale_order.created_at', '>=', Carbon::parse($args['start'])->startOfDay()->format('Y-m-d H:i:s'));
        }

        if (!empty($args['end'])) {
            $query->where('sale_order.created_at', '<=', Carbon::parse($args['end'])->endOfDay()->format('Y-m-d H:i:s'));
        }
    }

    public function optimizeFilter(EloquentBuilder|Builder &$query): void
    {
        [
            'order_id' => $orderId,
            'order_item_id' => $orderItemId,
            'order_item_barcode_id' => $orderItemBarcodeId,
        ] = getBarcodeIdLimit();
        $query->where('sale_order.id', '>=', $orderId)
            ->where('sale_order_item.id', '>=', $orderItemId)
            ->where('sale_order_item_barcode.id', '>=', $orderItemBarcodeId);
    }

    public function regenerateBarcode($id)
    {
        $barcodePrinted = BarcodePrinted::find($id);
        if (!$barcodePrinted) {
            return response()->json(['message' => "Wip ID: $id not found"], Response::HTTP_NOT_FOUND);
        }
        $barcodePrinted->convert_status = 0;
        $barcodePrinted->save();

        return response()->json($barcodePrinted);
    }

    public function customLogTimeLineOrderByBarcodePrintedId($barcodePrintedId, $employeeId, $type)
    {
        $saleOrderItemBarcodeRepo = new SaleOrderItemBarcodeRepository();
        $dataSaleOrderItemBarcodes = $saleOrderItemBarcodeRepo->getSaleOrderItemBarcodeByBarcodePrintedId($barcodePrintedId);
        $dataImportHistory = [];
        $dataOrderHistory = [];
        foreach ($dataSaleOrderItemBarcodes as $dataSaleOrderItemBarcode) {
            if (isset($dataOrderHistory[$dataSaleOrderItemBarcode->order_id])) {
                $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] = $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] . ', ' . $dataSaleOrderItemBarcode->label_id;
            } else {
                $dataOrderHistory[$dataSaleOrderItemBarcode->order_id] = $dataSaleOrderItemBarcode->label_id;
            }
        }

        foreach ($dataOrderHistory as $key => $value) {
            $dataImportHistory[] = [
                'user_id' => auth()?->user()?->id,
                'employee_id' => $employeeId,
                'order_id' => $key,
                'type' => $type,
                'message' => 'Label ' . $value . ' printed',

                'created_at' => Carbon::now()->toDateTimeString()
            ];
        }

        SaleOrderHistory::insert($dataImportHistory);
    }

    public function generateLabelUrgent($warehouseId, $blockTime, $limit, Carbon|null $timeNow = null): int
    {
        setTimezone();

        if (empty($timeNow)) {
            $timeNow = now('America/Los_Angeles');
        }

        ['order_item_barcode_id' => $orderItemBarcodeId] = getBarcodeIdLimit();
        $queryCountWipUrgent = SaleOrderItemBarcode::query()
            ->where('warehouse_id', $warehouseId)
            ->where('id', '>=', $orderItemBarcodeId)
            ->where('label_urgent', 'like', 'URG-' . $timeNow->format('d') . '%');
        $wipUrgent = $queryCountWipUrgent->count();
        $queryGetWipDelay = SaleOrder::query()
            ->select('sale_order_item_barcode.id')
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->where('sale_order_item_barcode.warehouse_id', $warehouseId)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->whereIn('sale_order_item_barcode.print_method', [PrintMethod::DTG, PrintMethod::NECK])
            ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->whereNull('sale_order_item_barcode.label_urgent')
            ->where('sale_order.created_at', '<=', $timeNow->clone()->subHours($blockTime))
            ->orderBy('sale_order.created_at')
            ->orderBy('sale_order_item_barcode.id');
        $this->optimizeFilter($queryGetWipDelay);
        $queryGetWipDelay = $queryGetWipDelay->limit($limit);
        $wipDelay = $queryGetWipDelay->get();
        $qtyUrgentLabelGenerated = 0;

        foreach ($wipDelay as $item) {
            $wipUrgent++;
            $qtyUrgentLabelGenerated++;
            $wipUrgentLabel = 'URG-' . $timeNow->format('d') . '-' . str_pad($wipUrgent, 4, '0', STR_PAD_LEFT);

            SaleOrderItemBarcode::query()
                ->where('id', $item->id)
                ->update(['label_urgent' => $wipUrgentLabel]);
        }

        return $qtyUrgentLabelGenerated;
    }

    public function getTotalBarcode($args)
    {
        $query = DB::table('sale_order_item_barcode')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product_roulette', 'product_roulette.product_id', 'product.id')
            ->where('product_roulette.is_active', ProductRoulette::STATUS_ACTIVE)
            ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', false)
            ->where('sale_order_item_barcode.is_deleted', 0);
        if (!empty($args['warehouse_id'])) {
            $query->where('sale_order_item_barcode.warehouse_id', $args['warehouse_id']);
        }
        if (!empty($args['priorityStores'])) {
            $query->whereNotIn('sale_order_item_barcode.store_id', $args['priorityStores']);
        }
        $this->optimizeFilter($query);

        return $query->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
            ->count();
    }

    public function getLastPrinted($args)
    {
        $query = BarcodePrinted::where('is_top_style', BarcodePrinted::ACTIVE)
            ->where('warehouse_id', $args['warehouse_id']);
        if (!empty($args['employee_id'])) {
            $query->where('employee_id', $args['employee_id']);
        }

        return $query->orderByDesc('id')->first();
    }

    public function generateBatch($args)
    {
        try {
            DB::beginTransaction();
            $timeGap = Setting::where('label', Setting::WIP_ASSIGNMENT_TIME_GAP)->first();
            if ($timeGap) {
                $time = Carbon::now()->subMinutes($timeGap->value)->format('Y-m-d H:i:s');
                $barcodePrinted = BarcodePrinted::where('employee_id', $args['employee_id'])
                    ->where('is_top_style', BarcodePrinted::ACTIVE)
                    ->where('created_at', '>', $time)
                    ->orderByDesc('id')
                    ->first();
                if (!empty($barcodePrinted)) {
                    throw new Exception('You have a batch in progress. Please complete it before starting a new one.', Response::HTTP_BAD_REQUEST);
                }
            }

            $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
                ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
                ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
                ->join('product_roulette', 'product_roulette.product_id', 'sale_order_item.product_id')
                ->where('product_roulette.is_active', ProductRoulette::STATUS_ACTIVE)
                ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
                ->where('sale_order_item.ink_color_status', 1)
                ->where('sale_order_item_barcode.barcode_printed_id', 0)
                ->where('sale_order.is_manual', false)
                ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
            if (!empty($args['warehouse_id'])) {
                $query->where('sale_order.warehouse_id', $args['warehouse_id']);
            }
            if (!empty($args['priorityStores'])) {
                $query->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
            $this->optimizeFilter($query);

            $product = $query->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('sale_order_item_barcode.is_deleted', 0)
                ->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING)
                ->select('sale_order_item.product_id', 'product_style.sku', 'product_style.name')
                ->orderBy('sale_order_item_barcode.id')
                ->first();

            if (empty($product)) {
                throw new Exception('There are currently no pending WIPs.', Response::HTTP_NOT_FOUND);
            }
            $params = [
                'employee_id' => $args['employee_id'] ?? null,
                'warehouse_id' => $args['warehouse_id'],
                'store_id' => $args['store_id'] ?? null,
                'style_sku' => $product->sku,
                'account_id' => $args['account_id'] ?? null,
                'quantity_input' => $args['limit'] ?? 0,
                'is_xqc' => $args['is_xqc'] ?? null,
                'is_eps' => $args['is_eps'] ?? null,
                'is_manual' => $args['is_manual'] ?? null,
                'is_reprint' => $args['is_reprint'] ?? null,
                'is_reroute' => $args['is_reroute'] ?? null,
                'is_fba' => $args['is_fba'] ?? null,
                'is_insert' => $args['is_insert'] ?? null,
                'is_tiktok' => $args['is_tiktok'] ?? null,
                'is_bulk_order' => $args['is_bulk_order'] ?? null,
                'user_id' => Auth::id(),
                'created_at' => date('Y-m-d H:i:s'),
                'is_top_style' => BarcodePrinted::ACTIVE,
                'print_method' => BarcodePrinted::METHOD_DTG
            ];
            $barcodePrinted = BarcodePrinted::create($params);

            $query = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
                ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
                ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
                ->where('sale_order_item.is_rbt', SaleOrderItem::IS_NOT_RBT)
                ->where('sale_order_item.ink_color_status', 1)
                ->where('sale_order_item_barcode.barcode_printed_id', 0)
                ->where('sale_order.is_manual', false)
                ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG);
            if (!empty($args['warehouse_id'])) {
                $query->where('sale_order.warehouse_id', $args['warehouse_id']);
            }
            if (!empty($args['priorityStores'])) {
                $query->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
            $this->optimizeFilter($query);
            $time = date('Y-m-d H:i:s');
            $barcodes = $query->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
                ->where('sale_order_item_barcode.is_deleted', 0)
                ->where('sale_order_item.product_id', $product->product_id)
                ->update([
                    'employee_pull_id' => $args['employee_id'],
                    'barcode_printed_id' => $barcodePrinted->id,
                    'print_barcode_at' => $time
                ]);

            $saleOrderIds = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)->pluck('order_id')->toArray();
            $chunkOrderIds = array_chunk(array_unique($saleOrderIds), 100);

            foreach ($chunkOrderIds as $orderIds) {
                // Update barcode printed status and time
                $newOrderIds = SaleOrder::whereIn('id', $orderIds)->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();

                if (!empty($newOrderIds)) {
                    handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
                }
                SaleOrder::whereIn('id', $orderIds)
                    ->where('order_status', SaleOrder::NEW_ORDER)
                    ->update([
                        'order_status' => SaleOrder::IN_PRODUCTION,
                        'order_production_at' => $time
                    ]);
            }
            $barcodePrinted->quantity = count($saleOrderIds);
            $barcodePrinted->quantity_input = count($saleOrderIds);
            $barcodePrinted->save();
            $this->updateLastBarcodePrintedTime($params);

            DB::commit();

            return $barcodePrinted;
        } catch (Exception $exception) {
            DB::rollBack();
            throw new Exception($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
