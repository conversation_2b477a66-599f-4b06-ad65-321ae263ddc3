<?php

namespace App\Repositories;

use App\Models\Supply;
use App\Models\SupplyInventory;
use App\Models\SupplyTestCount;
use App\Repositories\Contracts\SupplyInventoryRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupplyInventoryRepository extends CommonRepository implements SupplyInventoryRepositoryInterface
{
    public function __construct(protected TimeCheckingRepository $timeCheckingRepository,
         protected SupplyPurchaseOrderRepository $supplyPurchaseOrderRepository)
    {
        parent::__construct();
    }

    public function fetchAll($request)
    {
        $query = Supply::select('supply.*', 'supply_category.name as category_name', 'supply_unit.name as unit_name', 'supply_quantity.supply_id', 'supply_quantity.quantity', 'supply_quantity.incoming_stock');
        if (!empty($request->category_id)) {
            $query->where('category_id', $request->category_id);
        }
        $query->leftJoin('supply_category', 'supply.category_id', '=', 'supply_category.id')
            ->leftJoin('supply_unit', 'supply.unit_id', '=', 'supply_unit.id')
            ->leftJoin('supply_quantity', function ($join) use ($request) {
                $join->on('supply.id', '=', 'supply_quantity.supply_id')
                    ->where('supply_quantity.warehouse_id', '=', $request->warehouse_id);
            })
            ->search($request);

        if ($request->has('without_pagination')) {
            return $query->get();
        }
        $sortColumns = [
            'id' => 'supply.id',
            'name' => 'supply.name',
            'quantity' => 'supply_quantity.quantity',
            'incoming_stock' => 'supply_quantity.incoming_stock',
        ];
        $sortColumn = $request->sort_column;
        $sortBy = $this->sortBy;
        if (!array_key_exists($sortColumn, $sortColumns)) {
            $sortColumn = 'id';
            $sortBy = 'asc';
        }
        $query->orderBy($sortColumns[$sortColumn], $sortBy);

        return $query->paginate($this->limit);
    }

    public function createTestCount($request)
    {
        try {
            $supply = Supply::where([
                ['id', $request['supply_id']],
            ])->with(['inventoryQuantity'])->first();

            if (!$supply) {
                return $this->errorResponse('The supply product is not found or has been deleted!');
            }

            $availableQuantity = $supply->inventoryQuantity?->quantity ?? 0;
            $actualQuantity = intval($request['quantity_on_hand']);
            $adjustedQuantity = $actualQuantity - $availableQuantity;
            if ($adjustedQuantity == 0) {
                return $this->errorResponse('The quantity is matched, so no need to adjust!', Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $direction = $adjustedQuantity > 0 ? SupplyInventory::DIRECTION_INPUT : SupplyInventory::DIRECTION_OUTPUT;

            DB::beginTransaction();
            // Create supply test count
            $supplyTestCountRecord = SupplyTestCount::create([
                'warehouse_id' => config('jwt.warehouse_id'),
                'supply_id' => $supply->id,
                'quantity_available' => $availableQuantity,
                'quantity_on_hand' => $actualQuantity,
                'quantity_adjusted' => $adjustedQuantity,
                'employee_id' => $request['employee_id'],
            ]);
            // Adjust supply inventory quantity
            SupplyInventory::create([
                'direction' => $direction,
                'type' => SupplyInventory::TYPE_ADJUST,
                'supply_id' => $supply->id,
                'warehouse_id' => config('jwt.warehouse_id'),
                'user_id' => auth()->user()['id'],
                'object_id' => $supplyTestCountRecord->id,
                'object_name' => SupplyInventory::OBJECT_ADJUSTMENT,
                'quantity' => abs($adjustedQuantity)
            ]);
            // Update supply quantity
            SupplyQuantityRepository::updateQuantity(config('jwt.warehouse_id'), $supply->id, $adjustedQuantity);

            //todo:  update time end for time checking
            $this->timeCheckingRepository->updateTimeChecking(['end_time' => now()->format('Y-m-d H:i:s')], $request['id_time_checking']);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception->getTrace());

            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Adjust supply inventory successfully!', $supplyTestCountRecord);
    }

    public function fetchTestCount($request): LengthAwarePaginator
    {
        $query = SupplyTestCount::with(
            [
                'supply',
                'supply.category:id,name',
                'supply.unit:id,name',
                'supply.unitConversion:id,name',
                'employee:code,name'
            ],
        )->where('warehouse_id', config('jwt.warehouse_id'));

        if (isset($request['supply_category']) && !empty($request['supply_category'])) {
            $query->whereHas('supply.category', function ($q) use ($request) {
                $q->where('name', $request['supply_category']);
            });
        }

        if (isset($request['name_or_sku']) && !empty($request['name_or_sku'])) {
            $query->whereHas('supply', function ($q) use ($request) {
                $q->where('name', 'LIKE', '%' . $request['name_or_sku'] . '%');
                $q->orWhere('sku', 'LIKE', '%' . $request['name_or_sku'] . '%');
            });
        }

        return $query->latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }

    public function fetchInventoryEndUnit($startDate, $endDate, $warehouseId, $isFirstYearMonth = false)
    {
        $sql = "SELECT supply.sku,
                    supply.id as supply_id,
                    SUM(CASE
                        WHEN direction = 0 AND DATE(supply_inventory.created_at) >= DATE('" . addslashes($startDate) . "') AND DATE(supply_inventory.created_at) <= DATE('" . addslashes($endDate) . "')
                                AND object_name = 'addition'
                            THEN supply_inventory.quantity
                        ELSE 0
                    END) AS addition_quantity,
                    SUM(CASE
                        WHEN direction = 0 AND DATE(supply_inventory.created_at) >= DATE('" . addslashes($startDate) . "') AND DATE(supply_inventory.created_at) <= DATE('" . addslashes($endDate) . "')
                            THEN supply_inventory.quantity
                        ELSE 0
                    END) - SUM(CASE
                            WHEN direction = 1 AND DATE(supply_inventory.created_at) >= DATE('" . addslashes($startDate) . "') AND DATE(supply_inventory.created_at) <= DATE('" . addslashes($endDate) . "')
                                THEN supply_inventory.quantity
                                ELSE 0
                            END) AS adjustment_quantity ";

        // If isFirstYearMonth = true, end_unit = adjustment_quantity
        if ($isFirstYearMonth) {
            $sql .= ", 0 AS start_unit, SUM(CASE
                    WHEN direction = 0 AND DATE(supply_inventory.created_at) >= DATE('" . addslashes($startDate) . "') AND DATE(supply_inventory.created_at) <= DATE('" . addslashes($endDate) . "')
                            THEN supply_inventory.quantity
                        ELSE 0

                    END) - SUM(CASE
                            WHEN direction = 1 AND DATE(supply_inventory.created_at) >= DATE('" . addslashes($startDate) . "') AND DATE(supply_inventory.created_at) <= DATE('" . addslashes($endDate) . "')
                                THEN supply_inventory.quantity
                                    ELSE 0

                            END) AS end_unit ";
        }

        $sql .= "   FROM supply_inventory
           JOIN supply ON supply_inventory.supply_id = supply.id
           WHERE supply_inventory.is_deleted = 0 AND supply_inventory.warehouse_id = '" . addslashes($warehouseId) . "'
           GROUP BY supply.id  ORDER BY supply.id DESC ";

        if (!$isFirstYearMonth) {
            $previousYearMonth = Carbon::parse($startDate)->subMonth()->toDateString();
            $sql = 'SELECT supply_inventory_temp.*, COALESCE(fifo_supply_inventory.end_unit, 0) AS start_unit,
                (COALESCE(fifo_supply_inventory.end_unit, 0) + adjustment_quantity) AS end_unit from ( ' . $sql . ') AS supply_inventory_temp';

            $sql .= " LEFT JOIN fifo_supply_inventory
                ON supply_inventory_temp.supply_id = fifo_supply_inventory.supply_id
                AND DATE(fifo_supply_inventory.start_date) = DATE('" . addslashes($previousYearMonth) . "') AND warehouse_id = '" . addslashes($warehouseId) . "'
            ";
        }

        return DB::select($sql);
    }

    public function getOldestSupplyYearMonth($warehouseId)
    {
        return SupplyInventory::where([
            'is_deleted' => 0,
            'warehouse_id' => $warehouseId
        ])
        ->select('id', 'supply_id', 'warehouse_id', 'created_at')
        ->orderBy('created_at', 'ASC')
        ->first()?->created_at;
    }

    public function calculateReport($startDate, $endDate, $warehouseId, $isFirstMonth = false)
    {
        echo "startDate: {$startDate} - endDate: {$endDate}, warehouse_id: {$warehouseId}" . PHP_EOL;
        $supplyInventoryData = $this->fetchInventoryEndUnit($startDate, $endDate, $warehouseId, $isFirstMonth);
        if (empty($supplyInventoryData)) {
            return;
        }
        foreach ($supplyInventoryData as $supplyInventory) {
            echo "supply_id: {$supplyInventory->supply_id}" . PHP_EOL;
            $purchaseOrders = $this->supplyPurchaseOrderRepository->getPurchaseOrderBySupply($endDate, $supplyInventory->supply_id, $warehouseId);
            $totalPriceValue = 0;
            $endUnit = $supplyInventory->end_unit;

            foreach ($purchaseOrders as $po) {
                $quantity = $po->quantity;
                $endUnit = $endUnit - $quantity;
                $itemQuantity = $endUnit >= 0 ? $quantity : $endUnit + $quantity;
                $totalPriceValue += $itemQuantity * $po->price;

                if ($endUnit <= 0) {
                    echo 'endUnit < 0' . PHP_EOL;
                    break;
                }
            }

            if ($endUnit > 0) {
                $oldestSupplyCost = $this->supplyPurchaseOrderRepository->getOldestSupplyPrice($supplyInventory->supply_id, $warehouseId) ?? 0;
                $totalPriceValue += $endUnit * $oldestSupplyCost;
            }
            $costAvg = !empty($supplyInventory->end_unit) && $supplyInventory->end_unit > 0
                ? round($totalPriceValue / $supplyInventory->end_unit, 2)
                : 0;

            $additionQuantity = $supplyInventory->addition_quantity ?? 0;
            echo "sku: {$supplyInventory->sku}, end_unit: {$supplyInventory->end_unit}, cost_avg: {$costAvg}, addition_quantity: {$additionQuantity}" . PHP_EOL;
            DB::table('fifo_supply_inventory')->updateOrInsert(
                [
                    'supply_id' => $supplyInventory->supply_id,
                    'warehouse_id' => $warehouseId,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ],
                [
                    'sku' => $supplyInventory->sku,
                    'end_unit' => $supplyInventory->end_unit,
                    'cost_avg' => $costAvg,
                    'addition_quantity' => $supplyInventory->addition_quantity ?? 0
                ],
            );
        }
    }
}
