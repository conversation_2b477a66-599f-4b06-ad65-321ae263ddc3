<?php


namespace App\Repositories;


use App\Models\PrintingPreset;
use App\Models\PrintingPresetHistory;
use App\Repositories\Contracts\PrintingPresetRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class PrintingPresetRepository implements PrintingPresetRepositoryInterface
{
    const LIMIT = 10;

    public function getList($request): LengthAwarePaginator
    {
        return PrintingPreset::with('user')->latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }

    public function getDetail($id): JsonResponse
    {
        $printingPreset = PrintingPreset::find($id);
        if (!$printingPreset) {
            return response()->json([
                'status' => false,
                'message' => 'Printing preset not found!'
            ], Response::HTTP_NOT_FOUND);
        }
        return response()->json([
            'status' => true,
            'data' => $printingPreset
        ]);
    }

    public function create($request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $printingPreset = PrintingPreset::create([
                'name' => $request['name'],
                'data' => $request['data'],
            ]);
            PrintingPresetHistory::create([
                'data' => json_encode([
                    'name' => $request['name'],
                    'data' => $request['data'],
                ]),
                'printing_preset_id' => $printingPreset->id,
                'action' => PrintingPresetHistory::ACTION_CREATE
            ]);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json([
            'status' => true,
            'data' => $printingPreset,
            'message' => 'Create printing preset successfully!'
        ]);
    }

    public function update($id, $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $printingPreset = PrintingPreset::where('id', $id)->first();
            if (!$printingPreset) {
                return response()->json([
                    'status' => false,
                    'message' => 'Printing preset not found!'
                ], Response::HTTP_NOT_FOUND);
            }
            $printingPreset->name = $request['name'];
            $printingPreset->data = $request['data'];
            $printingPreset->save();
            PrintingPresetHistory::create([
                'data' => json_encode([
                    'name' => $request['name'],
                    'data' => $request['data'],
                ]),
                'printing_preset_id' => $printingPreset->id,
                'action' => PrintingPresetHistory::ACTION_UPDATE
            ]);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json([
            'status' => true,
            'data' => $printingPreset,
            'message' => 'Update printing preset successfully!'
        ]);
    }

    public function delete($id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $printingPreset = PrintingPreset::where('id', $id)->first();
            if (!$printingPreset) {
                return response()->json([
                    'status' => false,
                    'message' => 'Printing preset not found!'
                ], Response::HTTP_NOT_FOUND);
            }
            $printingPreset->delete();
            PrintingPresetHistory::create([
                'data' => '',
                'printing_preset_id' => $printingPreset->id,
                'action' => PrintingPresetHistory::ACTION_DELETE
            ]);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json([
            'status' => true,
            'message' => 'Delete printing preset successfully!'
        ]);
    }
}
