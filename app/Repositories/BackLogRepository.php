<?php

namespace App\Repositories;

use App\Models\BackLog;
use App\Models\ProductColor;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BackLogRepository extends CommonRepository
{
    private $lineProduction = ['print_barcode_at', 'pulled_at', 'printed_at', 'qc_at', 'staged_at', 'shipped_at', 'folded_at'];

    public function countBackLog($warehouseId, $jobType, $startDate)
    {
        $query = SaleOrderItemBarcode::select(['product_style.type', DB::raw('COUNT(*) as total')])
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product_style', 'product_style.sku', '=', 'sale_order_item.product_style_sku');

        if ($jobType['name'] == 'Pretreat') {
            $colorSkus = ProductColor::where('color_code', '#ffffff')->pluck('sku');
            $query->join('sale_order_item_image', 'sale_order_item_image.order_item_id', '=', 'sale_order_item.id')
                ->whereNotIn('sale_order_item.product_color_sku', $colorSkus)
                ->where('sale_order_item_image.color_new', '!=', 1);
        }

        if ($jobType['prev_step'] == 'staged_at') {
            $query->where(function ($querySub) {
                $querySub->where(function ($q) {
                    $q->whereNotNull('staged_at')
                        ->where('sale_order_item_barcode.order_quantity', '>', 1);
                })->orWhere(function ($q) {
                    $q->whereNotNull('qc_at')
                        ->where('sale_order_item_barcode.order_quantity', 1);
                });
            });
        } else {
            $query->whereNotNull($jobType['prev_step']);
        }

        if ($jobType['next_step'] == 'staged_at') {
            $query->where('sale_order_item_barcode.order_quantity', '>', 1);
        }

        $check = false;
        foreach ($this->lineProduction as $item) {
            if ($jobType['next_step'] == $item) {
                $check = true;
            }
            if ($check) {
                $query->whereNull($item);
            }
        }

        $query->where('sale_order_item_barcode.warehouse_id', $warehouseId)
            ->where('sale_order_item_barcode.is_deleted', 0)
            ->where('sale_order_item_barcode.created_at', '>=', $startDate);

        return $query->groupBy('product_style.type')->get();
    }

    public function countBackLogShippingLabel($warehouseId, $jobType, $startDate)
    {
        $query = SaleOrder::select([
            DB::raw('CASE WHEN order_quantity = 1 THEN "single order" ELSE "multiple order" END as type'),
            DB::raw('COUNT(*) as total')
        ]);

        $query->where(function ($querySub) {
            $querySub->where(function ($q) {
                $q->whereDoesntHave('barcodeItems', function (Builder $b) {
                    $b->whereNull('staged_at');
                })->where('order_quantity', '>', 1);
            })->orWhere(function ($q) {
                $q->whereHas('barcodeItems', function (Builder $b) {
                    $b->whereNotNull('qc_at');
                })->where('order_quantity', 1);
            });
        });

        $query->whereDoesntHave('barcodeItems', function (Builder $q) use ($jobType) {
            $q->whereNotNull($jobType['next_step']);
            $check = false;
            foreach ($this->lineProduction as $item) {
                if ($check) {
                    $q->orWhereNotNull($item);
                }
                if ($jobType['next_step'] == $item) {
                    $check = true;
                }
            }
        });

        $query->where('warehouse_id', $warehouseId)
            ->where('order_status', SaleOrder::IN_PRODUCTION)
            ->where('created_at', '>=', $startDate);

        return $query->groupBy('type')->get();
    }

    public function getBackLog($warehouseId, $jobType)
    {
        return Backlog::select('type', 'total')
            ->where('warehouse_id', $warehouseId)
            ->where('job_type', $jobType)
            ->get();
    }

    public function fetchBackLog($input)
    {
        $cacheBackLog = Cache::store(config('cache.redis_store'))->get('CACHE_BACKLOG');

        return [
            'total' => $cacheBackLog[$input['warehouse_id']][$input['job_type']] ?? 0
        ];
    }
}
