<?php

namespace App\Repositories;

use App\Models\ProductStyleIccProfile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class ProductStyleIccProfileRepository extends CommonRepository
{
    public function getList($input)
    {
        setTimezone();
        $query = ProductStyleIccProfile::query();

        if (!empty($input['product_style'])) {
            $query->where('product_style', $input['product_style']);
        }

        if (!empty($input['start_date'])) {
            $startDate = Carbon::parse($input['start_date'])->startOfDay();
            $query->where('created_at', '>=', $startDate);
        }

        if (!empty($input['end_date'])) {
            $endDate = Carbon::parse($input['end_date'])->endOfDay();
            $query->where('created_at', '<=', $endDate);
        }

        return $query->orderByDesc('id')->paginate($input['limit'] ?? self::LIMIT);
    }

    public function storeProfile($request)
    {
        ProductStyleIccProfile::create([
            'product_style' => $request['product_style'],
            'black_name' => $request['black_name'] ?? null,
            'black_url' => $request['black_url'] ?? null,
            'white_name' => $request['white_name'] ?? null,
            'white_url' => $request['white_url'] ?? null,
            'colored_name' => $request['colored_name'] ?? null,
            'colored_url' => $request['colored_url'] ?? null,
        ]);

        return $this->successResponse('Create profile successfully!');
    }

    public function sanitizeFileName(string $fileName): string
    {
        // Loại bỏ khoảng trắng, ký tự đặc biệt, giữ lại chữ, số, dấu chấm, gạch dưới, gạch ngang
        $fileName = preg_replace('/[^A-Za-z0-9\.\-_]/', '_', $fileName);

        // Đề phòng tên file trùng lặp — thêm timestamp (tuỳ chọn)
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $name = pathinfo($fileName, PATHINFO_FILENAME);

        return $name . '_' . time() . '.' . $extension;
    }

    public function generateSignedUrl($request)
    {
        $originalName = $request->file_name;
        $fileName = $this->sanitizeFileName($request->file_name);
        $folder = ProductStyleIccProfile::PRODUCT_STYLE_ICC_PROFILE_FOLDER;
        $adapter = Storage::disk('s3')->getAdapter();
        $client = $adapter->getClient();
        $path = "{$folder}/{$fileName}";
        $command = $client->getCommand('PutObject', [
            'Bucket' => $adapter->getBucket(),
            'Key' => $path,
            'ContentType' => 'application/octet-stream',
        ]);
        $request = $client->createPresignedRequest($command, '+10 minutes');

        return response()->json([
            'file_name' => $originalName,
            'signed_url' => (string) $request->getUri(),
            'final_url' => Storage::disk('s3')->url($path),
        ]);
    }
}
