<?php

namespace App\Repositories;

use App\Console\Commands\AutoCreateLabelShipping;
use App\Http\Service\PrintAreaService;
use App\Jobs\WebhookEasyPost;
use App\Models\EasyPostLog;
use App\Models\Employee;
use App\Models\IntegrateLogShipment;
use App\Models\InvoiceSaleOrder;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\ProductTypeWeight;
use App\Models\QueueJob;
use App\Models\RedbubblePacking;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemProductImage;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentEasypost as ShipmentEasypostModel;
use App\Models\ShipmentFee;
use App\Models\ShipmentItem;
use App\Models\ShipmentItemLabel;
use App\Models\ShipmentLabelPrinted;
use App\Models\ShipmentPackage;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierEasypost;
use App\Models\ShippingCarrierPackage;
use App\Models\ShippingCarrierService;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StoreAddress;
use App\Models\StoreProductWeight;
use App\Models\StoreShipment;
use App\Models\Tag;
use App\Models\TimeTracking;
use App\Models\User;
use App\Models\Warehouse;
use App\Models\WeightCubic;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use EasyPost\EasyPost;
use EasyPost\Error as EasyPostError;
use EasyPost\Shipment as ShipmentEasyPost;
use EasyPost\Util;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class LabelRepository
{
    const DEFAULT_STORE = '1';

    const CUSTOMS_DEFAULT = '3926.20';

    const CUSTOMS_SIGNER = 'FULFILLMENT CENTER';

    const CUSTOMS_TYPE = 'merchandise';

    const EEL_PFC = 'NOEEI 30.37(a)';

    const NON_DELIVERY_OPTION = 'abandon';

    const PHONE_DEFAULT = '************';

    const DEFAULT_STORE_HAS_CARRIER = 1;

    const DEFAULT_CARRIER_DOMESTIC = 'USPS';

    const DEFAULT_CARRIER_INTERNATIONAL = 'AsendiaUsa';

    const RATE_SHIP_INTERNATIONAL_DEFAULT = 'ePAQPlus';

    const ORDER_QUANTITY = 20;

    const NAME_DEFAULT_FOR_ADDRESS = 'FULFILLMENT CENTER';

    const SERVICE_FIRST_USPS = 'First';

    const SERVICE_EXPRESS_USPS = 'Express';

    const SERVICE_PRIORITY_USPS = 'Priority';

    const DEFAULT_DESCRIPTION_CUSTOMS_ITEM = 'Clothes';

    const DEFAULT_ORIGIN_COUNTRY = 'US';

    const IS_AUTO = true;

    const APPEND_TO_NAME_ADDRESS_RETURN = 'store';

    const SHIPPING_METHOD_DEFAULT = ['standard', 'express'];

    const SHIPPING_METHOD_DEFAULT_AUTO = ['standard', 'express', 'priority', ShippingMethod::SHIPPING_METHOD_FIRST_CLASS];

    const SERVICE_GROUND_ADVANTAGE_USPS = 'GroundAdvantage';

    const DEFAULT_CARRIER_HARD_GOOD_DOMESTIC = 'DhlEcs';

    const SERVICE_DHL_PARCEL_GROUND = 'DHLParcelGround';

    const RATE_SHIP_INTERNATIONAL_CANADA = 'ePAQSelectDirectAccessCanadaDDP';

    const MODE_SINGLE_OR_MULTIPLE = 1;

    const MODE_SHIRT_PRINTED = 2;

    const MODE_WIP_PRINTED = 3;

    const MODE_QC = 4;

    const DATE_ADVANCE_MANIFEST_EASYPOST = 2;

    const COMPANY_DEFAULT_FOR_ADDRESS = 'Fulfillment Center';

    protected $performanceRepository;

    public $printAreaSevice;

    public function __construct()
    {
        $this->performanceRepository = new PerformanceRepository();
        $this->printAreaSevice = new PrintAreaService();
    }

    public function getDataByLabel($labelId)
    {
        try {
            $isLabelId = DB::table('sale_order_item_barcode')
                ->select('sale_order.*', 'sale_order_item_barcode.label_id')
                ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
                ->where('sale_order_item_barcode.label_id', $labelId)
                ->where('sale_order.warehouse_id', config('jwt.warehouse_id'))
                // ->whereIn('sale_order.order_status', [SaleOrder::STATUS_IN_PRODUCTION, SaleOrder::STATUS_NEW_ORDER, SaleOrder::STATUS_SHIPPED])
                ->where('sale_order_item_barcode.is_deleted', 0)
                ->first();

            if (!$isLabelId) {
                return null;
            }

            $orderId = $isLabelId->id;
            setTimezone('America/Los_Angeles');
            $data = SaleOrder::with(['store', 'account', 'warehouse', 'addressSaleOrder', 'storeAddress', 'orderInsert'])
                ->with(['items' => function ($q) {
                    $q->orderByDesc('id')->with(
                        'product',
                        'images:sku,order_id,id,order_item_id,order_date,print_side',
                        'productStyle:sku,type',
                        'productStyle.productType:name,is_hard_goods',
                    )
                        ->with(['barcodes' => function ($q) {
                            $q->select('id', 'label_id', 'order_item_id', 'order_id', 'kitted_at')->where('is_deleted', false);
                        }]);
                }])
                ->with(['timeline' => function ($q) {
                    $q->orderByDesc('id')->with('user:username,created_at,id')->first();
                }])
                ->with(['shipment' => function ($q) {
                    $q->orderByDesc('id')
                        ->with(['address', 'employeeRefund:id,code,name'])
                        ->with(['items' => function ($q) {
                            $q->orderByDesc('id');
                        }])
                        ->with(['shipmentLabelPrinted' => function ($q) {
                            $q->orderByDesc('id');
                        }])
                        ->with(['shippingCarrier:name,code,tracking_url']);
                }])
                ->with(['shippingCarrierEasypost' => function ($q) {
                    $q->with('carrier.shippingCarrierPredefinedPackage.shippingCarrier:code,name,id')
                        ->where('warehouse_id', config('jwt.warehouse_id'));
                }])
                ->with('sla')
                ->find($orderId);

            if (strpos($data->tag, Tag::TAG_PHOTO_REQUIRED) !== false) {
                $saleOrderItemProductImage = SaleOrderItemProductImage::where('order_id', $data->id)
                    ->groupBy('order_item_id')
                    ->get();
                if ($data->items->count() !== $saleOrderItemProductImage->count()) {
                    return [
                        'status' => false,
                        'message' => 'Order Requires Photo Upload.'
                    ];
                }
            }

            if ($data && $data->shipment) {
                $data->shipment->each(function ($shipment) {
                    if ($shipment->warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID) {
                        $shipment->load(['shipmentItemLabels' => function ($q) {
                            $q->orderByDesc('id')->with('shipmentItem');
                        }]);
                    } else {
                        $shipment->load(['shipmentItemLabels' => function ($q) {
                            $q->where('employee_id', '<>', 0)
                                ->orderByDesc('id')
                                ->with('shipmentItem');
                        }]);
                    }
                });
            }

            $isDomestic = true;
            $dataAddressTo = $data->addressSaleOrder->where('type_address', 'to_address')->first();
            if ($dataAddressTo) {
                $isDomestic = in_array(strtoupper($dataAddressTo->country), StoreShipment::DOMESTIC_SHIPPING) ? true : false;
            }

            if (!empty($data->sla)) {
                $data->sla_expired_at_utc = $data->sla->expired_at ? shiftTimezoneToUTC($data->sla->expired_at) : null;
            }

            if (count($data->addressSaleOrder) == 1) {
                $dataStoreAddress = $data->storeAddress->where('type_address', 'return_address')->first();
                if ($dataStoreAddress) {
                    $dataStoreAddress->default = true;
                    $dataStoreAddress->type_address = StoreAddress::TYPE_ADDRESS_RETURN;
                    $data->addressSaleOrder->push($dataStoreAddress);
                } else {
                    $data->wareHouse->default = true;
                    $data->wareHouse->type_address = StoreAddress::TYPE_ADDRESS_RETURN;
                    $data->addressSaleOrder->push($data->wareHouse);
                }
            }

            if (count($data->shipment) > 0) {
                foreach ($data->shipment as $item) {
                    if (!$item->url_tracking_easypost && $item->shippingCarrier) {
                        $item->url_tracking_easypost = isset($item->shippingCarrier->tracking_url) ? str_replace('{tracking_code}', $item->tracking_number, $item->shippingCarrier->tracking_url) : '';
                    }
                    $item->age_24h = false;
                    if ($item->created_at <= Carbon::now()->subDay() && !$item->refund_status) {
                        $item->age_24h = true;
                    }
                }
            }

            $data = $this->getImageFromOption($data);

            // check carrier, neu khong co thi lay mac dinh voi store 1 cua minh
            if ($data->shippingCarrierEasypost->isEmpty() || (empty($data->store->easypost_api_key) && in_array($data->shipping_method, self::SHIPPING_METHOD_DEFAULT_AUTO))) {
                $dataShippingCarrier = ShippingCarrierEasypost::with(['carrier', 'carrier.shippingCarrierPredefinedPackage.shippingCarrier:code,name,id'])
                    ->where('store_id', '=', self::DEFAULT_STORE)
                    ->where('warehouse_id', config('jwt.warehouse_id'))
                    ->get()->toArray();
                unset($data->shippingCarrierEasypost);
                $data->shipping_carrier_easypost = $dataShippingCarrier;
            } elseif (!empty($data->store->easypost_api_key) && !in_array($data->shipping_method, self::SHIPPING_METHOD_DEFAULT_AUTO)) {
                $dataShippingMethod = ShippingMethod::where('api_shipping_method', $data->shipping_method)
                    ->first();
                if ($dataShippingMethod) {
                    $dataShippingCarrier = ShippingCarrierEasypost::with(['carrier', 'carrier.shippingCarrierPredefinedPackage.shippingCarrier:code,name,id'])
                        ->where('store_id', '=', $dataShippingMethod->store_id)
                        ->where('carrier_id', '=', $dataShippingMethod->carrier_id)
                        ->where('warehouse_id', config('jwt.warehouse_id'))
                        ->get()->toArray();
                    unset($data->shippingCarrierEasypost);
                    $data->shipping_carrier_easypost = $dataShippingCarrier;
                }
            } elseif (empty($data->store->easypost_api_key) && !in_array($data->shipping_method, self::SHIPPING_METHOD_DEFAULT_AUTO)) {
                $dataShippingMethod = ShippingMethod::where('api_shipping_method', $data->shipping_method)
                    ->first();
                if ($dataShippingMethod) {
                    $dataShippingCarrier = ShippingCarrierEasypost::with(['carrier', 'carrier.shippingCarrierPredefinedPackage.shippingCarrier:code,name,id'])
                        ->where('store_id', '=', $dataShippingMethod->store_id)
                        ->where('carrier_id', '=', $dataShippingMethod->carrier_id)
                        ->where('name', $dataShippingMethod->name)
                        ->where('warehouse_id', config('jwt.warehouse_id'))
                        ->get()->toArray();
                    unset($data->shippingCarrierEasypost);
                    $data->shipping_carrier_easypost = $dataShippingCarrier;
                }
            }
            $data->shipping_method_origin = $data->shipping_method;
            $isProductHardGood = $data->items->where('productStyle.productType.is_hard_goods', true)->isNotEmpty();
            $storeHardGoodsShipUsps = Setting::where('name', Setting::STORE_HARD_GOODS_SHIP_USPS)->first();
            $arrStoreHardGoodsShipUsps = $storeHardGoodsShipUsps && $storeHardGoodsShipUsps->value != '' ? array_map('strtoupper', array_map('trim', explode(',', $storeHardGoodsShipUsps->value))) : [];

            //them logic lay weight cua printify
            $storeProductWeight = [];
            if ($data->store_id == Store::PRINTIFY_API_ID) {
                $listSku = $data->items->pluck('product_sku')->unique()->toArray();
                $storeProductWeight = StoreProductWeight::where('store_id', $data->store_id)
                    ->whereIn('product_sku', $listSku)
                    ->get()->pluck('weight', 'product_sku');
            }
            $weight = calculatorWeightForSaleOrder($data, $storeProductWeight);
            $data->carrier_get_rate = $isDomestic ? self::DEFAULT_CARRIER_DOMESTIC : self::DEFAULT_CARRIER_INTERNATIONAL;
            $data->package_get_rate = ShippingCarrierPackage::PACKAGE_DEFAULT;
            if ($isDomestic) {
                if ($isProductHardGood && !in_array($data->store?->id, $arrStoreHardGoodsShipUsps) && $data->shipping_method !== ShippingMethod::SHIPPING_METHOD_FIRST_CLASS) {
                    $data->carrier_get_rate = $weight < 16 ? self::DEFAULT_CARRIER_DOMESTIC : self::DEFAULT_CARRIER_HARD_GOOD_DOMESTIC;
                }
            }
            if (!in_array($data->shipping_method, self::SHIPPING_METHOD_DEFAULT)) {
                switch ($data->shipping_method) {
                    case ShippingMethod::ECONOMY:
                        $data->carrier_get_rate = self::DEFAULT_CARRIER_HARD_GOOD_DOMESTIC;
                        break;

                    case ShippingMethod::SHIPPING_METHOD_FIRST_CLASS:
                        if (!$isDomestic) {
                            $data->carrier_get_rate = self::DEFAULT_CARRIER_INTERNATIONAL;
                        } else {
                            $data->carrier_get_rate = ShippingCarrier::USPS_ECOMMERCE_CODE;
                            $data->package_get_rate = ShippingCarrierPackage::PACKAGE_LETTER_USPS;
                        }
                        break;

                    default:
                        $dataShippingMethod = ShippingMethod::with(['shippingCarrier', 'shippingCarrierService'])
                            ->where('api_shipping_method', $data->shipping_method)
                            ->first();

                        if ($dataShippingMethod && $dataShippingMethod->shippingCarrier && $dataShippingMethod->shippingCarrierService) {
                            $data->shipping_method = $dataShippingMethod->shippingCarrier->name . ' - ' . $dataShippingMethod->shippingCarrierService->display_name;
                            $data->carrier_get_rate = $dataShippingMethod->shippingCarrier?->code ?? '';
                        }
                        break;
                }
            }
            // neu express va su dung tai khoan swiftpod se dung upsDap cua swiftpod
            if (empty($data->store->easypost_api_key) && $data->shipping_method == ShippingMethod::EXPRESS) {
                $data->carrier_get_rate = ShippingCarrier::UPSDAP_CODE;
            }

            // check xem mug có phai la 11oz hay ko
            $data = $this->calculatorDimension($data, $data->carrier_get_rate, $weight);

            $saleOrderInsertRepo = new SaleOrderInsertRepository();
            $data->order_inserts = $saleOrderInsertRepo->getInsert($orderId);

            return $data;
        } catch (Exception $e) {
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getImageFromOption($data)
    {
        $productTypeRepo = new ProductTypeRepository();
        $dataProductType = ProductType::all();
        $dataArrayItem = $data->items;
        $dataTotal = 0;
        $dataTotalVerify = 0;
        $dataIconProduct = [];

        $dataShipments = $data->shipment;
        $dataSaleOrderItem = [];
        foreach ($dataShipments as $shipment) {
            $dataTotalVerify += $shipment->shipmentItemLabels->count();
            foreach ($shipment->shipmentItemLabels as $shipmentItemLabel) {
                $orderItemId = $shipmentItemLabel->shipmentItem->order_item_id;
                if (isset($dataSaleOrderItem[$orderItemId])) {
                    $dataSaleOrderItem[$orderItemId] += 1;
                } else {
                    $dataSaleOrderItem[$orderItemId] = 1;
                }
            }
        }

        foreach ($dataArrayItem as $key => $item) {
            $item->shipment_item_count = isset($dataSaleOrderItem[$item->id]) ? $dataSaleOrderItem[$item->id] : 0;
            $dataTotal += $item->quantity;
            $dataOption = json_decode($item->options);
            $item->map_options = $this->printAreaSevice->mapOptionSaleOrderItem($dataOption, $item->sku, $data->order_date);
            $productType = $productTypeRepo->getIconProductType($item->productStyle?->type, $dataProductType);
            if (!isset($dataIconProduct[ucfirst($item->productStyle?->type)]) && $productType) {
                $dataIconProduct[ucfirst($item->productStyle?->type)] = $productType;
            }
        }

        $data->iconProduct = $dataIconProduct;
        $data->total = $dataTotal;
        $data->total_verify = $dataTotalVerify;

        return $data;
    }

    public function createShipment($input)
    {
        // tao shipment thi phai call sang easypost de lay duoc cac goi service ve
        // chon goi service ben easypost gui sang va gui kem theo id shipment cung rate id va call sang easypost de tao label cho shipment do
        // de goi bao hiem insurance = 0
        try {
            $dataSaleOrder = SaleOrder::with(
                'store',
                'warehouse',
                'items.barcodes.partNumber:id,product_id',
                'items.product',
                'items.productStyle',
                'items.productSize',
            )
                ->with(['storeAddress' => function ($q) {
                    $q->where('type_address', '=', 'return_address');
                }])
                ->with(['barcodeItems' => function ($q) {
                    $q->select('id', 'order_item_id', 'order_id', 'part_number_id', 'label_id')
                        ->with(['partNumber' => function ($q) {
                            $q->select('id', 'product_id', 'part_number');
                        }])
                        ->where('is_deleted', '!=', 1);
                }])
                ->find($input['order_id']);
            if (!$dataSaleOrder) {
                return [
                    'data' => [],
                    'message' => 'Order not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $text = '';
            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $groupedDataPartNumber = [];
                foreach ($dataSaleOrder->barcodeItems as $barcode) {
                    $partNumberId = $barcode->partNumber?->id;
                    // xử lý với TH cũ là chưa map với part_number
                    if (!$partNumberId) {
                        throw new \Exception('No part number is available for the barcode ' . $barcode->label_id);
                    }
                    $groupedDataPartNumber[] = [
                        'part_number' => substr($barcode->partNumber?->part_number ?? '', 0, -9),
                        'quantity' => 1,
                    ];
                }

                $groupedData = [];
                foreach ($groupedDataPartNumber as $value) {
                    $key = $value['part_number'] ?? '';
                    if (!isset($groupedData[$key])) {
                        $groupedData[$key] = $value['quantity'] ?? 0;
                    } else {
                        $groupedData[$key] += $value['quantity'] ?? 0;
                    }
                }
                foreach ($groupedData as $key => $value) {
                    if ($value > 1) {
                        $text .= $key . 'x' . $value . ', ';
                    } else {
                        $text .= $key . ', ';
                    }
                }
                $text = rtrim($text, ', ');
            }
            $urlHook = Setting::where('name', 'alert_slack_easypost')->first();

            // tao data shipment sau do gui sang ben easypost
            $dataSaleOrderAddress = SaleOrderAddress::where('order_id', $input['order_id'])->get();

            $addressTo = $this->dataAddressTo($dataSaleOrderAddress, !self::IS_AUTO, $dataSaleOrder);

            if (empty($addressTo)) {
                return [
                    'data' => [],
                    'message' => 'Order does not to Address. Please provide.',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            if (empty($addressTo['country'])) {
                return [
                    'data' => [],
                    'message' => 'Order does not country for address to. Please provide.',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            if (strtoupper($addressTo['country']) !== 'US') {
                $customsInfo = $this->dataCustomsInfoV2($dataSaleOrder);
            } else {
                $customsInfo = in_array($addressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($addressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE) ? $this->dataCustomsInfoV2($dataSaleOrder) : '';
            }

            $addressReturn = $this->dataAddressReturn($dataSaleOrder->storeAddress, $dataSaleOrder->merchant_name, $dataSaleOrderAddress);

            if (empty($addressReturn)) {
                $storeName = $dataSaleOrder->store->name;
                $this->messageToSlack('Store has not return address', $urlHook->value ?? '', $storeName, $dataSaleOrder->store->id);

                return [
                    'data' => [],
                    'message' => "Store $storeName does not have Return Address. Please provide.",
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            $carrierPackage = DB::table('shipping_carrier_package')
                ->select('shipping_carrier_package.*')
                ->where('shipping_carrier_package.id', $input['predefinedPackageId'])
                ->first();

            if (!$carrierPackage) {
                return [
                    'data' => [],
                    'message' => 'Carrier package not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            $dataGetShippingMethod = $this->getDataShippingMethodByStoreAndCarrier($this->getShippingMethodEasypost(), $dataSaleOrder->shipping_method, $dataSaleOrder->store->id, $carrierPackage->carrier_id);

            if (!$dataGetShippingMethod) {
                return [
                    'data' => [],
                    'message' => 'Shipping method not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            $accountData = ShippingCarrierEasypost::with('carrier')->where('carrier_id', $dataGetShippingMethod->carrier_id)
                ->where('store_id', $dataGetShippingMethod->store_id)
                ->where('name', $dataGetShippingMethod->name)
                ->where('warehouse_id', $dataSaleOrder->warehouse_id)
                ->where('status', true)
                ->first();

            $dataParcel = [
                'length' => $input['length'] || $input['length'] !== 0 ? $input['length'] : '',
                'width' => $input['width'] || $input['width'] !== 0 ? $input['width'] : '',
                'height' => $input['height'] || $input['height'] !== 0 ? $input['height'] : '',
                'weight' => $this->convertWeightToOZ($input['weight_unit'], $input['weight_value']),
                'predefined_package' => strtoupper($carrierPackage->predefined_package) == strtoupper(ShippingCarrierPackage::PACKAGE_DEFAULT) ? '' : $carrierPackage->predefined_package
            ];

            if (empty($accountData->carrier_account)) {
                return [
                    'data' => [],
                    'message' => 'Carrier account of store not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            $addressFrom = $this->dataAddressFrom($dataSaleOrder->warehouse, $dataSaleOrder->store?->name, $accountData->carrier?->code);

            $data = [
                'carrier_accounts' => $accountData->carrier_account,
                'to_address' => $addressTo,
                'from_address' => $accountData->carrier?->code == ShippingCarrier::ONTRACV3_CODE ? $addressReturn : $addressFrom,
                'parcel' => $dataParcel,
                'return_address' => $accountData->carrier?->code == ShippingCarrier::ONTRACV3_CODE ? $addressFrom : $addressReturn,
                'reference' => $dataSaleOrder->order_number,
                'customs_info' => $customsInfo
            ];
            $options = [];
            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $options['print_custom_1'] = $text;
            }

            if (
                $dataSaleOrder->store_id == Store::STORE_REDBUBBLE
                && in_array($accountData->carrier?->code, [ShippingCarrier::UPS_MI_CODE, ShippingCarrier::FEDEX_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])
            ) {
                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
            } elseif (in_array($accountData->carrier?->code, [ShippingCarrier::DHL_ECOMMERCE_CODE])) {
                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
            }

            // set
            if ($accountData->carrier?->code == ShippingCarrier::USPS_ECOMMERCE_CODE) {
                $options['date_advance'] = self::DATE_ADVANCE_MANIFEST_EASYPOST;
            }

            if (strtoupper($addressTo['country']) !== 'US' && !empty($dataSaleOrder->ioss_number)) {
                $options['print_custom_2'] = ($dataSaleOrder->tax_id_type ?? SaleOrder::TAX_ID_TYPE_IOSS) . ': #' . $dataSaleOrder->ioss_number;
            }

            if (in_array($dataSaleOrder->store_id, [Store::STORE_PMALL, Store::STORE_SAMCLUB]) && $accountData->carrier?->code == ShippingCarrier::FEDEX_CODE) {
                $options['payment'] = Shipment::PAYMENT_SHIPMENT[$dataSaleOrder->store_id];
            }

            if (!empty($options)) {
                $data['options'] = $options;
            }

            if (!empty($dataSaleOrder->ioss_number)) {
                $data['tax_identifiers'] = [
                    [
                        'entity' => 'SENDER',
                        'tax_id' => $dataSaleOrder->ioss_number,
                        'tax_id_type' => $dataSaleOrder->tax_id_type,
                        'issuing_country' => $addressTo['country'],
                    ]
                ];
            }

            $services = ShippingCarrierService::get()->pluck('display_name', 'name');
            try {
                $shipment = $this->createShipmentFromEasypost($data, $accountData->api_key_easypost);
            } catch (EasyPostError $e) {
                return [
                    'data' => Util::convertEasyPostObjectToArray($e),
                    'message' => $e->getMessage(),
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            if (count($shipment->messages) > 0) {
                return [
                    'data' => Util::convertEasyPostObjectToArray($shipment),
                    'message' => $shipment->messages[0]->message,
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            $shipment = Util::convertEasyPostObjectToArray($shipment);
            foreach ($shipment['rates'] as $key => &$item) {
                $item['service_name'] = isset($services[$item['service']]) ? $services[$item['service']] : $item['service'];
            }
            $shipment['rates'] = array_values($shipment['rates']);
            usort($shipment['rates'], function ($item1, $item2) {
                return $item1['rate'] <=> $item2['rate'];
            });

            return [
                'data' => $shipment,
                'message' => 'success',
                'code' => response::HTTP_OK
            ];
        } catch (\Exception $exception) {
            return [
                'status' => false,
                'message' => $exception->getMessage(),
                'code' => response::HTTP_BAD_REQUEST
            ];
        }
    }

    public function scanLabelIdToVerify($input)
    {
        $label = DB::table('sale_order_item_barcode')
            ->select(
                'sale_order_item_barcode.order_id',
                'sale_order_item_barcode.order_item_id',
                'sale_order_item_barcode.sku',
                'sale_order_item.product_id',
                'sale_order_item_barcode.label_id',
                'sale_order_item.product_sku',
                'sale_order_item.quantity',
                'sale_order_item.id',
                'sale_order_item.account_id',
                'sale_order_item.warehouse_id',
                'sale_order_item.store_id',
                'sale_order_item_barcode.part_number_id',
            )
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->where('sale_order_item_barcode.label_id', $input['label_id'])
            ->where('sale_order_item_barcode.is_deleted', false)
            ->where('sale_order_item.is_deleted', false)
            ->where('sale_order_item_barcode.order_id', $input['order_id'])
            ->first();

        $shipmentId = $input['shipment_id'];
        if (!empty($input['id_time_checking'])) {
            TimeTracking::where('id', $input['id_time_checking'])->update(['end_time' => now()]);
        }

        if (!$label) {
            return null;
        }

        $shipmentItemRepository = new ShipmentItemRepository();
        $shipmentItemLabelRepository = new ShipmentItemLabelRepository();
        $shipmentItem = $shipmentItemRepository->checkShipmentItem($label->order_id, $label->order_item_id, $shipmentId);

        $saleOrderItemInsert = SaleOrderItem::with('barcodes:order_item_id,order_id,label_id,is_deleted', 'product.productStyle', 'shipmentItem')
            ->where('order_id', $label->order_id)
            ->whereHas('product.productStyle', function ($query) {
                $query->where('type', ProductStyle::TYPE_INSERT);
            })
            ->get();

        try {
            DB::beginTransaction();
            $shipmentItemLabel = ShipmentItemLabel::where('label_id', $input['label_id'])
                ->first();
            if ($shipmentItemLabel && $shipmentItemLabel?->employee_id != 0) {
                DB::commit();

                return [
                    'data' => $label,
                    'message' => 'Label has been scanned',
                    'code' => 'label_scanned'
                ];
            }

            if ($shipmentItem) {
                // se check xem lable đo da scan chua o bang shipment_item_label, neu co roi thi khong update quantity nua
                // chua co thi se update quantity cho record o bang shipment_item va them record scan label do vao bang shipment_item_label

                if ($shipmentItemLabel) {
                    $shipmentItemLabel->employee_id = $input['employee_id'];
                    $shipmentItemLabel->save();
                } else {
                    // insert shipment item label
                    $dataInsertShipmentItemLabel = [
                        'label_id' => $input['label_id'],
                        'employee_id' => $input['employee_id'],
                        'shipment_id' => $shipmentId,
                        'shipment_item_id' => $shipmentItem->id,
                        'part_number_id' => $label->part_number_id,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                    $shipmentItemLabelRepository->insertShipmentItemLabel($dataInsertShipmentItemLabel);

                    // update quantity for shipment item
                    $quantityLabel = $shipmentItem->quantity + 1;
                    $shipmentItemRepository->updateShipmentItemByShipmentItemId($shipmentItem->id, ['quantity' => $quantityLabel, 'updated_at' => date('Y-m-d H:i:s')]);
                }
                //update quantity for shipment
                Shipment::find($shipmentId)->increment('shipment_quantity');
                $orderItemVerify = $this->verifyLabelOfInsert($saleOrderItemInsert, $shipmentId, $input['employee_id']);
                DB::commit();

                return [
                    'data' => array_merge($orderItemVerify, [[
                        'order_item_id' => $label->order_item_id,
                        'total_verify' => 1,
                    ]]),
                    'message' => 'Scan label success',
                    'code' => 'label_success'
                ];
            }

            if ($shipmentItemLabel) {
                // TH nay phai tru di trong shipmentItem mà đang chứa label đó đi và cộng sang cho shipment item mới
                $shipmentItemHasLabel = ShipmentItem::where('id', $shipmentItemLabel->shipment_item_id)->first();
                if ($shipmentItemHasLabel && $shipmentItemHasLabel->quantity == 1) {
                    $shipmentItemHasLabel->delete();
                } else {
                    $shipmentItemHasLabel->quantity = $shipmentItemHasLabel->quantity - 1;
                    $shipmentItemHasLabel->save();
                }
                $shipmentItemLabel->delete();
            }
            // chua co shipment item thi tao shipment item va them record ung voi label da scan o bang shipment_item_label
            $dataInsert = [
                'account_id' => $label->account_id,
                'store_id' => $label->store_id,
                'warehouse_id' => $label->warehouse_id,
                'order_id' => $label->order_id,
                'order_item_id' => $label->order_item_id,
                'shipment_id' => $shipmentId,
                'sku' => $label->sku,
                'quantity' => 1,
                'product_id' => $label->product_id,
                'product_sku' => $label->product_sku,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            $shipmentItemId = $shipmentItemRepository->insertGetId($dataInsert);
            // insert shipment item label
            $dataInsertShipmentItemLabel = [
                'label_id' => $input['label_id'],
                'employee_id' => $input['employee_id'],
                'shipment_id' => $shipmentId,
                'shipment_item_id' => $shipmentItemId,
                'part_number_id' => $label->part_number_id,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            $shipmentItemLabelRepository->insertShipmentItemLabel($dataInsertShipmentItemLabel);
            $orderItemVerify = $this->verifyLabelOfInsert($saleOrderItemInsert, $shipmentId, $input['employee_id']);
            //update quantity for shipment
            Shipment::find($shipmentId)->increment('shipment_quantity');

            DB::commit();
        } catch (\Throwable $exception) {
            DB::rollBack();

            return null;
        }

        return [
            'data' => array_merge($orderItemVerify, [[
                'order_item_id' => $label->order_item_id,
                'total_verify' => 1,
            ]]),
            'message' => 'Scan label success',
            'code' => 'label_insert'
        ];
    }

    public function convertWeightToOZ($weightUnit, $weightValue)
    {
        // 1 lb = 16 oz
        if ($weightUnit == 'lb') {
            return floor($weightValue * ProductTypeWeight::LB_OZ * 100) / 100;
        }

        return $weightValue;
    }

    public function buyAShipment($input)
    {
        //lay apiKey
        $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();

        $easypostApiKeyDefault = $easypostApiKeyDefault ? $easypostApiKeyDefault->value : '';
        $dataSaleOrder = SaleOrder::with('store', 'warehouse', 'shipment')->find($input['order_id']);
        $urlHook = Setting::where('name', 'alert_slack_easypost')->first();
        $storeUsesAccountSwiftpod = Setting::where('name', Setting::STORE_USE_ACCOUNT_SHIP_SWIFTPOD)->first();
        $arrStoreUsesAccountSwiftpod = $storeUsesAccountSwiftpod && $storeUsesAccountSwiftpod->value != '' ? array_map('trim', explode(',', $storeUsesAccountSwiftpod->value)) : [];
        array_push($arrStoreUsesAccountSwiftpod, 1);
        if (!$dataSaleOrder) {
            return null;
        }

        $carrierId = ShippingCarrier::where('code', $input['rate']['carrier'])->where('is_deleted', false)->first()?->id;

        if (!$carrierId) {
            return null;
        }

        $dataGetShippingMethod = $this->getDataShippingMethodByStoreAndCarrier($this->getShippingMethodEasypost(), $dataSaleOrder->shipping_method, $dataSaleOrder->store->id, $carrierId);
        if (!$dataGetShippingMethod) {
            return null;
        }
        $accountData = ShippingCarrierEasypost::where('carrier_id', $dataGetShippingMethod->carrier_id)
            ->where('store_id', $dataGetShippingMethod->store_id)
            ->where('name', $dataGetShippingMethod->name)
            ->where('warehouse_id', $dataSaleOrder->warehouse_id)
            ->where('status', true)
            ->first();
        if (!$accountData) {
            return null;
        }
        try {
            try {
                $easypostApiKey = $accountData->api_key_easypost;
                $data = $this->buyAShipmentFromEasyPost($input['id_shipment'], $input['rate'], $easypostApiKey);
            } catch (EasyPostError $e) {
                if ($e->ecode === 'PAYMENT_REQUIRED') {
                    if ($urlHook) {
                        $this->messageToSlack('PAYMENT_REQUIRED - ' . $e->getMessage(), $urlHook->value ?? '', $dataSaleOrder->store->name, $dataSaleOrder->store->id);
                    }
                }
                $this->logConvertErrorCreateShippingLabel($dataSaleOrder, $e->getHttpBody());
                $this->messageToSlack($e->getHttpBody(), $urlHook->value ?? '', $dataSaleOrder->store->name, $dataSaleOrder->store->id . ' ,Order Number: ' . $dataSaleOrder?->order_number);

                return response()->json([
                    'status' => false,
                    'message' => 'An error occurred, please try again later!',
                    'errorEasypost' => json_decode($e->getHttpBody())
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
            DB::beginTransaction();
            if (!empty($data)) {
                $shipingAccount = $easypostApiKeyDefault === $easypostApiKey
                    ? (in_array($accountData->store_id, $arrStoreUsesAccountSwiftpod) || in_array($accountData->name, $this->getNameShippingMethodSwiftpod()->toArray())) ? 'swiftpod' : 'swiftpod_store'
                    : 'store';
                $shipmentId = $this->storeShipment($data, $dataSaleOrder, $input['employeeId'], $shipingAccount, !self::IS_AUTO, $accountData->carrier_account);
                $shipmentStatus = $data->status ?? null;
                $shipmentEasypost = Util::convertEasyPostObjectToArray($data);
                // log vao bang easypost_log
                $dataLog = [
                    'order_id' => $input['order_id'],
                    'shipment_id' => $shipmentId,
                    'easypost_id' => isset($shipmentEasypost['id']) ? $shipmentEasypost['id'] : null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'track_status' => $shipmentStatus
                ];
                ShipmentEasypostModel::insert($dataLog);

                // kiem tra xem sale order co shipment chưa, neu chua co thi add shipmnentId vao
                // chi add shipment cho lan dau tao
                if (!$dataSaleOrder->shipment_id) {
                    $dataSaleOrder->shipment_id = $shipmentId;
                }

                $isShipmentRefund = Shipment::where('id', $dataSaleOrder->shipment_id)->whereNotNull('refund_status')->first();
                if ($isShipmentRefund) {
                    SaleOrder::where('id', $input['order_id'])->update(['shipment_id' => $shipmentId]);
                }

                if ($dataSaleOrder->order_status === SaleOrder::IN_PRODUCTION || $dataSaleOrder->order_status === SaleOrder::NEW_ORDER) {
                    $dataSaleOrder->order_status = SaleOrder::SHIPPED;
                }

                $dataSaleOrder->save();
                //lưu lịch sử tạo shipment
                saleOrderHistory(auth()->user()['id'], $input['employeeId'], $input['order_id'], SaleOrderHistory::CREATE_LABEL, "Label $data?->tracking_code successfully created");
            }
            if ($dataSaleOrder->store_id == Store::STORE_REDBUBBLE) {
                $isShipmentPackageByOrderId = ShipmentPackage::query()->where('order_id', $dataSaleOrder->id)->first();
                if (!$isShipmentPackageByOrderId) {
                    $dataRbPackage = $this->getConditonPacking();
                    // add shipment với TH package
                    $dataShipmentPackage[] = [
                        'shipment_id' => $shipmentId,
                        'quantity' => 1,
                        'order_id' => $dataSaleOrder->id,
                        'sku' => $this->addShipmentWithPackage($dataSaleOrder->order_quantity, $dataRbPackage)
                    ];
                    // add insert với TH insert
                    $dataInsertSku = $this->getInsertForRedbubble()?->rb_sku;
                    if (!empty($dataInsertSku)) {
                        $dataShipmentPackage[] = [
                            'shipment_id' => $shipmentId,
                            'quantity' => 1,
                            'order_id' => $dataSaleOrder->id,
                            'sku' => $dataInsertSku
                        ];
                    }
                    ShipmentPackage::insert($dataShipmentPackage);
                }
            }

            // luu shipment fee của easypost tra ve luc mua label
            $dataShipmentFee = [];
            foreach ($data->fees as $itemShipmentFee) {
                $dataShipmentFee[] = [
                    'shipment_id' => $shipmentId,
                    'type' => $itemShipmentFee->type ?? null,
                    'amount' => $itemShipmentFee->amount ?? null
                ];
            }

            if (!empty($dataShipmentFee)) {
                ShipmentFee::insert($dataShipmentFee);
            }

            DB::commit();
            // khi co shipment item -> gắn các label với shipment
            if (($dataSaleOrder->shipment->count() == 0 || $isShipmentRefund) && $dataSaleOrder->warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID) {
                $saleOrderItemsBarcode = SaleOrderItemBarcode::where('order_id', $dataSaleOrder->id)->get();
            } elseif (
                $dataSaleOrder->shipment->count() == 0
                && !empty($input['is_shipment'])
                && !empty($input['label_verify'])
                && $dataSaleOrder->warehouse_id !== Warehouse::WAREHOUSE_SANJOSE_ID
            ) {
                $saleOrderItemsBarcode = SaleOrderItemBarcode::whereIn('label_id', $input['label_verify'])
                    ->where('order_id', $dataSaleOrder->id)
                    ->get();
            } else {
                $saleOrderItemsBarcode = collect();
            }

            foreach ($saleOrderItemsBarcode as $item) {
                $this->scanLabelIdToVerify([
                    'order_id' => $dataSaleOrder->id,
                    'label_id' => $item->label_id,
                    'shipment_id' => $shipmentId,
                    'employee_id' => $input['employeeId']
                ]);
            }

            if (in_array($shipingAccount, ['swiftpod', 'swiftpod_store'])) {
                // dispatch queue recycled tracking number
                $dataDispatchTracking = [
                    'tracking_code' => $data->tracking_code,
                    'order_id' => $dataSaleOrder->id,
                    'shipment_id' => $shipmentId,
                    'order_number' => $dataSaleOrder->order_number,
                    'order_type' => $dataSaleOrder->order_type
                ];
                handleJob(QueueJob::QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER, $dataDispatchTracking);
            }
        } catch (\Throwable $exception) {
            DB::rollBack();
            $this->logConvertErrorCreateShippingLabel($dataSaleOrder, $exception->getMessage());

            return response()->json([
                'status' => false,
                'message' => 'An error occurred, please try again later!',
                'error' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $data;
    }

    public function storeShipment($data, $order, $employeeId, $shipmentAccount, $isAuto, $accountEasypost)
    {
        $dataInsert = [
            'order_id' => $order->id,
            'account_id' => $order->account_id,
            'store_id' => $order->store_id,
            'warehouse_id' => $order->warehouse_id,
            'ship_date' => Carbon::parse($data->created_at)->toDateTimeString(),
            'shipment_cost' => $data->selected_rate->rate,
            'insurance_cost' => $data->insurance ?? 0,
            'carrier_code' => $data->selected_rate->carrier,
            'tracking_number' => $data->tracking_code,
            'tracking_status' => $data->status,
            'service_code' => $data->selected_rate->service,
            'package_code' => Shipment::PACKAGE_DEFAULT,
            'weight_value' => $data->parcel->weight,
            'weight_unit' => Shipment::WEIGHT_DEFAULT,
            'dimension_length' => $data->parcel->length,
            'dimension_width' => $data->parcel->width,
            'dimension_height' => $data->parcel->height,
            'dimension_unit' => Shipment::DIMENSION_DEFAULT,
            'label_url' => $data->postage_label->label_url ?? '',
            'url_tracking_easypost' => $data->tracker->public_url ?? '',
            'shipment_quantity' => 0,
            'provider' => Shipment::PROVIDER_EASYPOST,
            'is_auto_created' => $isAuto,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'employee_create_id' => $employeeId,
            'shipment_account' => $shipmentAccount,
            'label_zpl_url' => $data->postage_label->label_zpl_url ?? '',
            'account_shipping_easypost' => $accountEasypost,
            'usps_zone' => $data?->usps_zone ? (int) preg_replace('/[^0-9]/', '', $data?->usps_zone) : null,
            'est_delivery_at' => $data->tracker?->est_delivery_date ? Carbon::parse($data->tracker->est_delivery_date)->toDateTimeString() : null,
        ];

        $idShipment = DB::table('shipment')
            ->insertGetId($dataInsert);
        handleJob(QueueJob::QUEUE_SYNC_ORDER_DESK_TRACKING, $idShipment);
        if ($order->store_id == Store::PRINTIFY_API_ID) {
            handleJob(SaleOrder::QUEUE_PRINTIFY_SEND_NOTIFY_TRACKING, $idShipment);
        }

        return $idShipment;
    }

    public function generateLabel($shipmentId)
    {
        $shipment = Shipment::query()->with(['store', 'saleOrder:id,order_type'])->where('id', $shipmentId)->first();
        if (!$shipment) {
            return false;
        }
        if (Shipment::PROVIDER_SHIPSTATION == $shipment->provider) {
            return $shipment;
        }

        if (Storage::disk('s3')->exists("/label/$shipmentId.pdf")) {
            $shipment->label_pdf_url = $shipmentId . '.pdf';

            return $shipment;
        }

        $img = file_get_contents($shipment->label_url);

        if (($shipment->saleOrder?->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER && isValidPdf($img)) || $shipment->provider == Shipment::PROVIDER_GELATO
            || $shipment->carrier_code == ShippingCarrier::ONTRACV3_CODE) {
            Storage::disk('s3')->put("/label/$shipmentId.pdf", $img);
            $shipment->label_pdf_url = $shipmentId . '.pdf';

            return $shipment;
        }
        $dpi = 203;

        $pageWidth = 4 * $dpi;
        $pageHeight = 6 * $dpi;
        $needRotation = false;

        if (
            $shipment->carrier_code == ShippingCarrier::USPS_ECOMMERCE_CODE
            && in_array($shipment->service_code, [ShippingCarrierService::SERVICE_FIRST_CLASS, ShippingCarrierService::SERVICE_FIRST_CLASS_INTERNATIONAL])
        ) {
            $needRotation = true;
        }

        $imageString = imagecreatefromstring($img);
        $notes = [];

        // Chỉ xoay ảnh nếu cần
        if ($needRotation) {
            $rotatedImage = imagerotate($imageString, 90, 0);
            imagedestroy($imageString);
            $imageString = $rotatedImage;
            // Sau khi xoay, cần điều chỉnh kích thước ảnh để vừa với trang
            $imageString = imagescale($imageString, $pageWidth, $pageHeight);
        }

        if (imagesy($imageString) > $pageHeight && $shipment->carrier_code == Shipment::CARRIER_CODE_ASENDIA) {
            $images = [];

            if (imagesy($imageString) % $pageHeight > $pageHeight * 2 / 3) {
                $images = $this->cropImage(ceil(imagesy($imageString) / $pageHeight), $imageString, $pageHeight, $pageWidth);
            } else {
                $page = floor(imagesy($imageString) / $pageHeight);
                $imageScale = imagescale($imageString, $pageWidth, $pageHeight * $page);
                $images = $this->cropImage($page, $imageScale, $pageHeight, $pageWidth);
            }

            // Check store note on label
            if ($shipment->store->has_note == Store::HAS_NOTE) {
                $shipment = $shipment->load(['saleOrder.items.productSize', 'saleOrder.items.productColor']);

                $notes['ref_number'] = $shipment->saleOrder->external_number;
                $notes['customer_note'] = $shipment->saleOrder->customer_note;
                $notes['line_items'] = $shipment->saleOrder->items;
            }

            $pdf = PDF::loadView('label_multiple_pages', ['images' => $images, 'notes' => $notes])->setOptions([
                'dpi' => $dpi,
                'logOutputFile' => storage_path('logs/pdf.log'),
                'tempDir' => storage_path('logs/')
            ]);
        } else {
            // Chuyển ảnh đã xoay thành base64
            ob_start();
            imagepng($imageString);
            $imageData = ob_get_clean();
            imagedestroy($imageString);

            $shipment->image = '<img width="' . $pageWidth . '" height="' . $pageHeight . '" src="data:image/png;base64, ' . base64_encode($imageData) . '" />';            // Check store note on label
            if ($shipment->store->has_note == Store::HAS_NOTE) {
                $shipment = $shipment->load(['saleOrder.items.productSize', 'saleOrder.items.productColor']);

                $notes['ref_number'] = $shipment->saleOrder->external_number;
                $notes['customer_note'] = $shipment->saleOrder->customer_note;
                $notes['line_items'] = $shipment->saleOrder->items;
            }

            $viewName = 'label';
            if (
                $shipment->carrier_code == ShippingCarrier::USPS_ECOMMERCE_CODE
                && in_array($shipment->service_code, [
                    ShippingCarrierService::SERVICE_FIRST_CLASS,
                    ShippingCarrierService::SERVICE_FIRST_CLASS_INTERNATIONAL
                ])
            ) {
                $viewName = 'label/label64';
            }
            $view = view($viewName, [
                'item' => $shipment,
                'width' => $pageWidth,
                'height' => $pageHeight,
                'notes' => $notes
            ])->render();

            if ($needRotation) {
                // Thêm CSS để đảm bảo ảnh không bị chia thành nhiều trang
                $view = str_replace('</head>', '<style>@page { size: 4in 6in; margin: 0; } body { margin: 0; padding: 0; }</style></head>', $view);
            }

            $pdf = PDF::loadHTML($view)->setOptions([
                'dpi' => $dpi,
                'logOutputFile' => storage_path('logs/pdf.log'),
                'tempDir' => storage_path('logs/')
            ]);
        }

        $pdf->setWarnings(true);
        Storage::disk('s3')->put("/label/$shipmentId.pdf", $pdf->output());
        $shipment->label_pdf_url = $shipmentId . '.pdf';

        return $shipment;
    }

    protected function cropImage($page, $image, $pageHeight, $pageWidth)
    {
        $images = [];

        for ($i = 0; $i < $page; $i++) {
            $imgCrop = imagecrop($image, [
                'x' => 0,
                'y' => $pageHeight * $i,
                'width' => $pageWidth,
                'height' => $pageHeight
            ]);

            if ($imgCrop !== false) {
                ob_start();
                imagepng($imgCrop);
                $rawImageBytes = ob_get_clean();
                $images[] = '<img width="' . $pageWidth . '" height="' . $pageHeight . '" src="data:image/png;base64, ' . base64_encode($rawImageBytes) . '" />';
            }
        }

        return $images;
    }

    public function calculatorWeight($orderID, $dataWeight)
    {
        $items = SaleOrderItem::with('productStyle', 'productSize')->where('order_id', $orderID)->get();
        $weightOrder = 0;
        foreach ($items as $item) {
            if ($item->productStyle == null) {
                continue;
            }
            if ($item->productSize == null) {
                continue;
            }

            $typeProductStyle = ucfirst($item->productStyle->type);
            $typeProductSize = ucfirst($item->productSize->name);

            $weightProduct = array_filter($dataWeight, function ($var) use ($typeProductStyle, $typeProductSize) {
                return $var['name'] === $typeProductStyle && $var['size'] === $typeProductSize;
            });

            if (empty($weightProduct)) {
                $weightProduct = array_filter($dataWeight, function ($v) use ($typeProductStyle) {
                    return $v['name'] === $typeProductStyle && $v['size'] === 'any';
                });
            }
            $weightProduct = empty($weightProduct) ? 0 : array_shift($weightProduct)['weight_oz'];
            $weightOrder += (float) $weightProduct * $item->quantity;
        }

        return $weightOrder;
    }

    public function getCubic($weightSaleOrder, $dataWeightCubic)
    {
        $dataCubic = '';
        foreach ($dataWeightCubic as $item) {
            if ($weightSaleOrder == $item['weight_end'] && $weightSaleOrder == WeightCubic::WEIGHT_END_HAS_CUBIC) {
                $dataCubic = $item['cubic'];
                break;
            }
            if ($item['weight_start'] <= $weightSaleOrder && $weightSaleOrder < $item['weight_end']) {
                $dataCubic = $item['cubic'];
                break;
            }
        }

        return $dataCubic;
    }

    public function calculatorDimension($saleOrder, $carrierCode, $weight)
    {
        $isMug11O = false;
        $isMug15O = false;
        foreach ($saleOrder->items as $item) {
            if ($item->product_size_sku === ProductSize::SIZE_11OZ_SKU) {
                $isMug11O = true;
            }
            if ($item->product_size_sku === ProductSize::SIZE_15OZ_SKU) {
                $isMug15O = true;
            }
        }
        $dataCubic = WeightCubic::get()->toArray();
        // neu khoi luong < 16 oz hoac > 60 oz
        if ($weight < WeightCubic::WEIGHT_START_HAS_CUBIC || $weight > WeightCubic::WEIGHT_END_HAS_CUBIC) {
            $data = explode('x', '0x0x0');
        } else {
            $cubicOrder = $this->getCubic($weight, $dataCubic);
            $data = !empty($cubicOrder) ? explode('x', $cubicOrder) : explode('x', '0x0x0');
        }

        if ($saleOrder->store_id == Store::PRINTIFY_API_ID) {
            $dataParcel = [
                'length' => '',
                'width' => '',
                'height' => '',
            ];
        } else {
            $dataParcel = [
                'length' => ($data[0] == 0 && ($isMug11O || $isMug15O) && $weight < WeightCubic::WEIGHT_END_HAS_CUBIC)
                    ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[0] : ProductSize::DIMENSION_MUG_15O[0])
                    : ($data[0] == 0
                        ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC && $weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 9 : '')
                        : (int) $data[0]),
                'width' => ($data[1] == 0 && ($isMug11O || $isMug15O) && $weight < WeightCubic::WEIGHT_END_HAS_CUBIC)
                    ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[1] : ProductSize::DIMENSION_MUG_15O[1])
                    : ($data[1] == 0
                        ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC && $weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 6 : '')
                        : (int) $data[1]),
                'height' => ($data[2] == 0 && ($isMug11O || $isMug15O) && $weight < WeightCubic::WEIGHT_END_HAS_CUBIC)
                    ? ($isMug11O ? ProductSize::DIMENSION_MUG_11O[2] : ProductSize::DIMENSION_MUG_15O[2])
                    : ($data[2] == 0
                        ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC && $weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 2 : '')
                        : (int) $data[2]),
            ];
        }

        if ($carrierCode == ShippingCarrier::ONTRACV3_CODE || $carrierCode == ShippingCarrier::PASSPORT_CODE) {
            $dataParcel = [
                'length' => $data[0] == 0 ? ($weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 9 : '') : (int) $data[0],
                'width' => $data[1] == 0 ? ($weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 6 : '') : (int) $data[1],
                'height' => $data[2] == 0 ? ($weight < WeightCubic::WEIGHT_START_HAS_CUBIC ? 2 : '') : (int) $data[2],
            ];
        }

        $dataParcel['weight'] = $weight;
        $saleOrder->estimate_dimension = $dataParcel;
        $saleOrder->isMug11oz = $isMug11O;
        $saleOrder->isMug15oz = $isMug15O;

        return $saleOrder;
    }

    public function logPrintedLabelShipment($input, $isRbt = false)
    {
        $isChecked = false;
        $employee = Employee::find($input['employeeId']);
        if (!$employee) {
            return false;
        }
        $shipment = Shipment::with('saleOrder')->find($input['shipmentId']);
        if (!$shipment) {
            return false;
        }
        if ($shipment->employee_printed_id != null && $shipment->employee_printed_id == $input['employeeId']) {
            $isChecked = true;
        }
        $dataPrinted = ShipmentLabelPrinted::create([
            'employee_id' => $input['employeeId'],
            'printed_date' => date('Y-m-d H:i:s'),
            'shipment_id' => $input['shipmentId']
        ]);
        $dataPrinted['labeled'] = $isChecked;
        SaleOrderItemBarcode::where('order_id', $shipment->order_id)->update(['shipped_at' => now(), 'employee_ship_id' => $input['employeeId']]);

        if (
            $shipment->saleOrder->order_status == SaleOrder::IN_PRODUCTION
            || $shipment->saleOrder?->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER
            || ($shipment->saleOrder?->order_type == SaleOrder::ORDER_TYPE_NORMAL && in_array(Tag::LABEL_TAG_ID, explode(',', $shipment?->saleOrder?->tag)))
        ) {
            $shipment->saleOrder->order_status = SaleOrder::STATUS_SHIPPED;
            $shipment->saleOrder->save();
        }

        //xu ly cho logic don reroute va khong muon ban order notify cho KH nua
        $isReroute = SaleOrderHistory::where('order_id', $shipment->saleOrder->id)->where('type', SaleOrderHistory::IMPORT_REROUTE)->first();
        $orderHasInvoice = InvoiceSaleOrder::where('sale_order_id', $shipment->saleOrder->id)->exists();
        if ((($isReroute && $shipment->saleOrder->order_status == SaleOrder::IN_PRODUCTION) || $shipment->provider == Shipment::PROVIDER_MARKETPLACE) && !$orderHasInvoice) {
            $shipment->created_at = now();
            SaleOrder::where('id', $shipment->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_SHIPPED]);
        }

        // chi th tao manual thi gui tracking notify
        if (!$shipment->is_auto_created && $shipment->employee_printed_id == null) {
            $tag = explode(',', $shipment->saleOrder->tag);
            $isLabelOrder = $shipment->saleOrder->order_type == SaleOrder::ORDER_TYPE_LABEL_ORDER;
            $isNormalWithLabelTag = $shipment->saleOrder->order_type == SaleOrder::ORDER_TYPE_NORMAL
                && in_array(SaleOrder::LABEL_TAG_ID, $tag);
            $isDifferentShipment = $input['shipmentId'] !== $shipment->saleOrder->shipment_id;
            if ($isLabelOrder || $isNormalWithLabelTag || $isDifferentShipment) {
                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $shipment->order_id);
            }
        }
        $shipment->employee_printed_id = $input['employeeId'];
        $shipment->save();
        //todo:  update time end for time checking
        if (!$isChecked && !$isRbt) {
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);
        }

        SaleOrderHistory::create([
            'user_id' => auth()->user()['id'] ?? null,
            'employee_id' => $input['employeeId'],
            'order_id' => $shipment->order_id,
            'type' => SaleOrderHistory::PRINT_LABEL,
            'message' => $isRbt ? 'Shipping label printed via API (tracking: ' . ($shipment->tracking_number ? $shipment->tracking_number : 'Null') . ')' : 'Print label',
            'created_at' => Carbon::now()->toDateTimeString()
        ]);

        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $shipment->order_id);

        return $dataPrinted;
    }

    public function refundShipment($input)
    {
        $dataSaleOrder = SaleOrder::with('store', 'warehouse')->find($input['orderId']);

        if (!$dataSaleOrder) {
            return null;
        }

        $s = Shipment::where('id', $input['shipmentId'])->where('order_id', $input['orderId'])->first();

        if ($s && $s->created_at <= Carbon::now()->subDay()) {
            throw new Exception('Error: Shipment cannot be voided after 24 hours', Response::HTTP_BAD_REQUEST);
        }

        $s = Shipment::where('id', $input['shipmentId'])->where('order_id', $input['orderId'])->first();
        $storeAccount = $s->shipment_account == 'swiftpod' ? LabelRepository::DEFAULT_STORE : $dataSaleOrder->store_id;
        $shippingCarrierEasypost = ShippingCarrierEasypost::where('carrier_account', $s->account_shipping_easypost)
            ->where('warehouse_id', $dataSaleOrder->warehouse_id)
            ->where('store_id', $storeAccount)
            ->first();

        if (!$shippingCarrierEasypost) {
            throw new Exception('Error: Shipment not has carrier account', Response::HTTP_BAD_REQUEST);
        }

        EasyPost::setApiKey($shippingCarrierEasypost->api_key_easypost);

        $easyPostLog = ShipmentEasypostModel::where('order_id', $input['orderId'])
            ->where('shipment_id', $input['shipmentId'])
            ->first();
        $shipment_id = str_replace('"', '', $easyPostLog?->easypost_id);

        if (!$easyPostLog) {
            $easyPostLog = EasyPostLog::where('order_id', $input['orderId'])
                ->where('shipment_id', $input['shipmentId'])
                ->first();
            $shipment_id = json_decode($easyPostLog->data)->id;
        }

        if (!empty($shipment_id)) {
            $shipment = ShipmentEasyPost::retrieve($shipment_id);
            if ($shipment && is_null($shipment->refund_status)) {
                try {
                    $refund = $shipment->refund();
                    $result = Shipment::where('id', $input['shipmentId'])->where('order_id', $input['orderId'])->first();
                    $result->refund_status = $refund->refund_status;
                    $result->employee_refund_id = $input['employeeId'];
                    $result->save();
                    ShipmentItem::where('shipment_id', $input['shipmentId'])->delete();
                    ShipmentItemLabel::where('shipment_id', $input['shipmentId'])->delete();
                    if ($input['shipmentId'] === $dataSaleOrder->shipment_id) {
                        $shipmentNotRefund = Shipment::where('order_id', $input['orderId'])->whereNull('refund_status')->first();
                        if ($shipmentNotRefund) {
                            $dataSaleOrder->shipment_id = $shipmentNotRefund->id;
                            $dataSaleOrder->save();
                            // khi co shipment sẽ gắn các label với shipment mới
                            if ($dataSaleOrder->warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID) {
                                $saleOrderItemsBarcode = SaleOrderItemBarcode::where('order_id', $dataSaleOrder->id)->get();
                                foreach ($saleOrderItemsBarcode as $item) {
                                    $this->scanLabelIdToVerify([
                                        'order_id' => $dataSaleOrder->id,
                                        'label_id' => $item->label_id,
                                        'shipment_id' => $shipmentNotRefund->id,
                                        'employee_id' => $input['employeeId']
                                    ]);
                                }
                            }
                        }
                    }
                    $this->createSaleOrderLog($input['orderId'], $s->id, $input['employeeId']);

                    return [
                        'data' => $result->load('employeeRefund:id,code,name'),
                        'success' => true,
                        'message' => 'Refund Successfully'
                    ];
                } catch (EasyPostError $e) {
                    throw new Exception($e->getMessage(), Response::HTTP_BAD_REQUEST);
                }
            }

            throw new Exception('Refund Error', Response::HTTP_BAD_REQUEST);
        }

        throw new Exception('Not Found Shipment', Response::HTTP_NOT_FOUND);
    }

    public function createSaleOrderLog($orderId, $shipmentId, $employeeId)
    {
        $employee = Employee::where('id', $employeeId)->first();

        SaleOrderHistory::create([
            'user_id' => auth()->user()['id'],
            'employee_id' => isset($employee) ? $employeeId : null,
            'order_id' => $orderId,
            'type' => SaleOrderHistory::VOID_SHIPMENT_TYPE,
            'message' => 'Void shipment #' . $shipmentId,
            'created_at' => Carbon::now()->toDateTimeString()
        ]);
    }

    private function getTimeByStatus($status, $input)
    {
        foreach ($input as $item) {
            if ($item['status'] == $status) {
                return $item['datetime'];
            }
        }
    }

    public function webhooks($request)
    {
        $requestData = $request->all();
        if ($requestData && isset($requestData['result']) && $request['result']['object'] == 'Batch') {
            unset($requestData['result']['shipments']);
        }

        if ($requestData && isset($requestData['result']) && $request['result']['object'] == 'ScanForm') {
            unset($requestData['result']['tracking_codes']);
        }

        WebhookEasyPost::dispatch($requestData)->onConnection('sqs')->onQueue('webhook');

        return true;
    }

    public function logConvertErrorCreateShippingLabel($saleOrder, $message)
    {
        DB::table('shipment_create_error_log')
            ->insert([
                'order_id' => $saleOrder->id,
                'note' => $message,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        $saleOrder->is_shipment_create_error = SaleOrder::IS_SHIPMENT_CREATE_ERROR;
        $saleOrder->save();
    }

    public function messageToSlack($message, $urlHook, $storeName, $storeId)
    {
        $dateTime = date('Y-m-d H:i:s');
        $data
            = [
                'blocks' => [
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => " *`Error`* _ $storeName _ $storeId"
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => "$message"
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => "When: $dateTime"
                            ]
                        ]
                    ],
                    [
                        'type' => 'divider'
                    ]
                ]
            ];

        $ch = curl_init($urlHook);
        $headers = [
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    public function dataAddressTo($saleOrderAddress, $auto, $saleOrder, $arrayConditionGetAddress = [])
    {
        if ($auto) {
            $dataSaleOrder = $saleOrderAddress->where('type_address', SaleOrderAddress::TO_ADDRESS)->whereIn('verified_status', $arrayConditionGetAddress)->first();
        } else {
            $dataSaleOrder = $saleOrderAddress->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
        }
        if (!$dataSaleOrder) {
            return null;
        }

        $data = [
            'name' => trim(self::remove_emoji($dataSaleOrder->name)),
            'street1' => $dataSaleOrder->street1,
            'street2' => $dataSaleOrder->street2,
            'city' => $dataSaleOrder->city,
            'state' => $dataSaleOrder->state,
            'zip' => $dataSaleOrder->zip,
            'country' => $dataSaleOrder->country,
            'phone' => self::getNumberPhone($dataSaleOrder->phone, $dataSaleOrder->country),
            'email' => filter_var($dataSaleOrder->email, FILTER_VALIDATE_EMAIL) ? $dataSaleOrder->email : '',
            'residential' => $dataSaleOrder->residential ? true : false,
            'company' => $dataSaleOrder->company
        ];

        return $data;
    }

    public function dataAddressReturn($returnStoreAddress, $nameMerchant, $saleOrderAddressReturn)
    {
        $dataSaleOrderReturn = $saleOrderAddressReturn->where('type_address', SaleOrderAddress::RETURN_ADDRESS)->first();
        if ($dataSaleOrderReturn) {
            return [
                'name' => $nameMerchant ?? self::getNameForReturnAddress($dataSaleOrderReturn->name),
                'street1' => $dataSaleOrderReturn->street1 ?? '',
                'street2' => $dataSaleOrderReturn->street2 ?? '',
                'city' => $dataSaleOrderReturn->city,
                'state' => $dataSaleOrderReturn->state,
                'zip' => $dataSaleOrderReturn->zip,
                'country' => $dataSaleOrderReturn->country,
                'phone' => empty($dataSaleOrderReturn->phone) ? SaleOrder::PHONE_DEFAULT : self::getNumberPhone($dataSaleOrderReturn->phone, $dataSaleOrderReturn->country),
                'email' => filter_var($dataSaleOrderReturn->email, FILTER_VALIDATE_EMAIL) ? $dataSaleOrderReturn->email : '',
                'company' => $dataSaleOrderReturn->company ?? self::COMPANY_DEFAULT_FOR_ADDRESS
            ];
        }
        $dataAddressReturn = $returnStoreAddress->where('type_address', StoreAddress::TYPE_ADDRESS_RETURN)->first();
        if (!$dataAddressReturn) {
            return '';
        }

        return [
            'name' => $nameMerchant ?? self::getNameForReturnAddress($dataAddressReturn->name),
            'street1' => $dataAddressReturn->street1 ?? '',
            'street2' => $dataAddressReturn->street2 ?? '',
            'city' => $dataAddressReturn->city,
            'state' => $dataAddressReturn->state,
            'zip' => $dataAddressReturn->zip,
            'country' => $dataAddressReturn->country,
            'phone' => empty($dataAddressReturn->phone) ? SaleOrder::PHONE_DEFAULT : self::getNumberPhone($dataAddressReturn->phone, $dataAddressReturn->country),
            'email' => filter_var($dataAddressReturn->email, FILTER_VALIDATE_EMAIL) ? $dataAddressReturn->email : '',
            'company' => $dataAddressReturn->company ?? self::COMPANY_DEFAULT_FOR_ADDRESS
        ];
    }

    public function getUrlZpl($idShipment)
    {
        $shipment = Shipment::query()->where('id', $idShipment)->where('provider', Shipment::PROVIDER_EASYPOST)->first();
        if (!$shipment) {
            return false;
        }
        if (empty($shipment->label_zpl_url)) {
            $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();
            $easypostApiKeyDefault = $easypostApiKeyDefault ? $easypostApiKeyDefault->value : '';
            $store = Store::query()->where('id', $shipment->store_id)->first();
            $easypostApiKey = !empty($store->easypost_api_key) ? $store->easypost_api_key : $easypostApiKeyDefault;
            EasyPost::setApiKey($easypostApiKey);

            $easyPostLog = EasyPostLog::where('shipment_id', $idShipment)
                ->first();
            EasyPost::setApiKey($easypostApiKey);
            $getShipmentEasypost = ShipmentEasyPost::retrieve(json_decode($easyPostLog->data)->id);
            $data = $getShipmentEasypost->label(['file_format' => 'ZPL']);
            $shipment->label_zpl_url = $data->postage_label->label_zpl_url;
            $shipment->save();

            return $data->postage_label->label_zpl_url;
        }

        return $shipment->label_zpl_url;
    }

    public static function getNumberPhone($phone, $country = 'US')
    {
        if ($phone === null || $phone === '') {
            return '';
        }

        $phoneNumber = preg_replace('/\D/', '', $phone);
        $length = strlen($phoneNumber);

        //Đầu số Mỹ thì bỏ +1 và lấy 10 số cuối
        if ($length > 10 && ($country == 'US' || $country == 'USA')) {
            $phoneNumber = substr($phoneNumber, -10);
        }
        //TODO: Các quốc gia khác: chỉ lấy 10 số cuối nếu dài hơn

        if ($length < 10) {
            $phoneNumber = str_pad($phoneNumber, 10, '0', STR_PAD_RIGHT);
        }

        return $phoneNumber;
    }

    public static function getNameForReturnAddress($name)
    {
        if (empty($name)) {
            return self::CUSTOMS_SIGNER;
        }

        if (str_word_count($name) == 1) {
            return self::remove_emoji(preg_replace('/\s+/', ' ', preg_replace('/\s+/', ' ', str_ireplace('store', ' ', $name)) . ' ' . self::APPEND_TO_NAME_ADDRESS_RETURN));
        }

        return trim(self::remove_emoji(preg_replace('/\s+/', ' ', $name)));
    }

    public static function remove_emoji($string)
    {
        // Match Enclosed Alphanumeric Supplement
        $regex_alphanumeric = '/[\x{1F100}-\x{1F1FF}]/u';
        $clear_string = preg_replace($regex_alphanumeric, '', $string);

        // Match Miscellaneous Symbols and Pictographs
        $regex_symbols = '/[\x{1F300}-\x{1F5FF}]/u';
        $clear_string = preg_replace($regex_symbols, '', $clear_string);

        // Match Emoticons
        $regex_emoticons = '/[\x{1F600}-\x{1F64F}]/u';
        $clear_string = preg_replace($regex_emoticons, '', $clear_string);

        // Match Transport And Map Symbols
        $regex_transport = '/[\x{1F680}-\x{1F6FF}]/u';
        $clear_string = preg_replace($regex_transport, '', $clear_string);

        // Match Supplemental Symbols and Pictographs
        $regex_supplemental = '/[\x{1F900}-\x{1F9FF}]/u';
        $clear_string = preg_replace($regex_supplemental, '', $clear_string);

        // Match Miscellaneous Symbols
        $regex_misc = '/[\x{2600}-\x{26FF}]/u';
        $clear_string = preg_replace($regex_misc, '', $clear_string);

        // Match Dingbats
        $regex_dingbats = '/[\x{2700}-\x{27BF}]/u';
        $clear_string = preg_replace($regex_dingbats, '', $clear_string);

        return $clear_string;
    }

    public function createLabelAutoWithMode($mode, $typeString, $totalThread, $numberThread, $warehouseId)
    {
        echo "Start with $typeString! \n";
        /*
         * Case invalid
         * - Out of money or failed when call api
         * - Not found store address
         * - Api key wrong
         **/
        $storeInvalid = [];
        $sleepTimeCycle = 10 * 1;
        while (true) {
            try {
                $newStoreInvalid = [];
                foreach ($storeInvalid as $storeId => $timeBreak) {
                    if (now()->diffInMinutes($timeBreak) < 30) {
                        $newStoreInvalid[$storeId] = $timeBreak;
                    }
                }
                $storeInvalid = $newStoreInvalid;
                $storeIds = $warehouseId ? $this->getStoreHasAutoCreateShipping($newStoreInvalid) : $this->getStoreWithHasAutoCreateShipping($mode, $storeInvalid);

                $warehouseIds = $warehouseId ? [$warehouseId] : $this->getWareHouseHasAutoCreateShipping();

                $allSetting = Setting::all();

                $urlHook = $allSetting->where('name', Setting::ALERT_SLACK_EASYPOST)->first();

                // loai tru nhung san pham khong tao shipping tu dong
                $productSkipNotAuto = $allSetting->where('name', Setting::PRODUCT_SKIP_NOT_AUTO_CREATE_SHIPPING)->first();
                $arrValueConditionProductType = $productSkipNotAuto && $productSkipNotAuto->value != '' ? array_map('strtoupper', array_map('trim', explode(',', $productSkipNotAuto->value))) : [];

                if (!$urlHook) {
                    echo "Error: Can't find url hook slack push notification! \n";
                    sleep($sleepTimeCycle);

                    continue;
                }

                $urlGoogleChat = $allSetting->where('name', Setting::ALERT_GOOGLE_CHAT_EASYPOST)->first();
                if (!$urlGoogleChat) {
                    echo "Error: Can't find url hook google push notification! \n";
                    sleep($sleepTimeCycle);

                    continue;
                }

                $storeHardGoodsShipUsps = $allSetting->where('name', Setting::STORE_HARD_GOODS_SHIP_USPS)->first();
                $arrStoreHardGoodsShipUsps = $storeHardGoodsShipUsps && $storeHardGoodsShipUsps->value != '' ? array_map('strtoupper', array_map('trim', explode(',', $storeHardGoodsShipUsps->value))) : [];

                $conditionGetAddressTo = $allSetting->where('name', Setting::CONDITION_GET_ADDRESS_TO_FOR_CREATE_LABEL)->first();
                $arrValueConditionGetAddressTo = $conditionGetAddressTo && $conditionGetAddressTo->value != '' ? explode(',', $conditionGetAddressTo->value) : [1];

                $storeUsesAccountSwiftpod = $allSetting->where('name', Setting::STORE_USE_ACCOUNT_SHIP_SWIFTPOD)->first();
                $arrStoreUsesAccountSwiftpod = $storeUsesAccountSwiftpod && $storeUsesAccountSwiftpod->value != '' ? array_map('trim', explode(',', $storeUsesAccountSwiftpod->value)) : [];
                array_push($arrStoreUsesAccountSwiftpod, 1);

                $shippingCarriers = $this->getShippingCarriers();
                if ($shippingCarriers->count() == 0) {
                    echo "Can't find carrier! \n";
                    $this->messageToSlack("Can't find Shipping carrier!", $urlHook->value ?? '', 'Shipping carrier', 'Not found');
                    $this->sendGoogleChat("Can't find Shipping carrier!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $carrierAccounts = $this->getShippingCarrierEasypost();
                if ($carrierAccounts->count() == 0) {
                    echo "Can't find carrier USPS account! \n";
                    $this->messageToSlack("Can't find carrier account!", $urlHook->value ?? '', 'Carrier account', 'Not found carrier account');
                    $this->sendGoogleChat("Can't find carrier account!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $dataShippingMethods = $this->getShippingMethodEasypost();

                if ($dataShippingMethods->isEmpty()) {
                    echo "Can't find data shipping method! \n";
                    $this->messageToSlack("Can't find data shipping method", $urlHook->value ?? '', 'Data shipping method', 'Not found shipping method');
                    $this->sendGoogleChat("Can't find data shipping method", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $dataWeight = ProductTypeWeight::get();
                if ($dataWeight->isEmpty()) {
                    echo "Can't find data weight! \n";
                    $this->messageToSlack("Can't find data weight!", $urlHook->value ?? '', 'Data weight', 'Not found data Weight');
                    $this->sendGoogleChat("Can't find data weight!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $dataCubic = WeightCubic::get();
                if ($dataCubic->isEmpty()) {
                    echo "Can't find data cubic! \n";
                    $this->messageToSlack("Can't find data cubic!", $urlHook->value ?? '', 'Data cubic', 'Not found data cubic');
                    $this->sendGoogleChat("Can't find data cubic!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $easypostApiKeyDefault = $this->getApiKeyDefaultEasypost();
                if (!$easypostApiKeyDefault) {
                    echo "Can't find easypost apikey default! \n";
                    $this->messageToSlack("Can't find easypost apikey default!", $urlHook->value ?? '', 'Easypost aip key', 'Not found Easypost aip key');
                    $this->sendGoogleChat("Can't find easypost apikey default!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $getNameShippingMethodSwiftpod = $this->getNameShippingMethodSwiftpod()->toArray();
                if (empty($getNameShippingMethodSwiftpod)) {
                    echo "Can't find name shipping method swiftpod! \n";
                    $this->messageToSlack("Can't find name shipping method swiftpod!", $urlHook->value ?? '', 'Name shipping method swiftpod', 'Not found name shipping method swiftpod');
                    $this->sendGoogleChat("Can't find name shipping method swiftpod!", $urlGoogleChat->value);
                    sleep($sleepTimeCycle);

                    continue;
                }

                $saleOrders = $this->getOrderForCreateLabelAuto($storeIds, $warehouseIds, $mode, $totalThread, $numberThread, $warehouseId);

                if ($saleOrders->count() == 0) {
                    echo "Can't scan any orders matching, sleep 10min ! \n";
                    sleep($sleepTimeCycle);

                    continue;
                }

                $dataRbPackage = $this->getConditonPacking();

                foreach ($saleOrders as $saleOrder) {
                    try {
                        echo "Start create label order: $saleOrder->id / $saleOrder->order_number \n";
                        if (!$saleOrder->store) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with store.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        if (isset($storeInvalid[$saleOrder->store_id])) {
                            echo "order has in store invalid \n";

                            continue;
                        }

                        if (!$saleOrder->warehouse) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with warehouse.", $urlHook, $urlGoogleChat);

                            continue;
                        }
                        if ($saleOrder->barcodeItems->count() === 0) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not found sale order item barcode.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        // check product type not create auto shipping
                        $checkProductTypeNotAuto = $this->checkProductTypeAutoShipping($saleOrder->items, $arrValueConditionProductType);
                        if ($checkProductTypeNotAuto) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: has product not create label auto.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        if (empty($saleOrder->shipping_method)) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not found shipping method.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        // check product of order is hard goods
                        // true: has product hard goods
                        // false: not product hard goods
                        $isProductHardGood = $saleOrder->items->where('productStyle.productType.is_hard_goods', true)->isNotEmpty();
                        $isStoreHardGoodUseUsps = $isProductHardGood && in_array($saleOrder->store_id, $arrStoreHardGoodsShipUsps) ? true : false;

                        $dataAddressTo = $this->dataAddressTo($saleOrder->addressSaleOrder, true, $saleOrder, $arrValueConditionGetAddressTo);
                        if (!$dataAddressTo) {
                            echo "Sale order: $saleOrder->order_number has not address to. \n";
                            $this->logConvertErrorCreateShippingLabel($saleOrder, 'Not found address to.');
                            $this->messageToSlack("Sale order: $saleOrder->order_number has not address to", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                            continue;
                        }
                        if (empty($dataAddressTo['country'])) {
                            $this->logConvertErrorCreateShippingLabel($saleOrder, 'Not found address to.');
                            $this->messageToSlack("Sale order: $saleOrder->order_number has not country of address to", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                            continue;
                        }

                        $isDataAddressUS = strtoupper($dataAddressTo['country']) == 'US';
                        $customsInfo = $this->getDataCustomsInfo($saleOrder, $isDataAddressUS, $dataAddressTo);
                        $dataAddressReturn = $this->dataAddressReturn($saleOrder->storeAddress, $saleOrder->merchant_name, $saleOrder->addressSaleOrder);
                        if (empty($dataAddressReturn)) {
                            $this->logConvertErrorCreateShippingLabel($saleOrder, "Sale order $saleOrder->order_number: has not return address.");
                            $this->messageToSlack("Sale order $saleOrder->order_number: has not return address.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                            continue;
                        }
                        $dataAddressFrom = $this->dataAddressFrom($saleOrder->warehouse);

                        $weightOrder = calculatorWeightForSaleOrder($saleOrder);
                        if ($weightOrder == 0) {
                            $this->logConvertErrorCreateShippingLabel($saleOrder, 'sale order has weight = 0oz.');
                            $this->messageToSlack("Sale order $saleOrder->order_number: has weight = 0oz.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                            continue;
                        }
                        if ($weightOrder > 60) {
                            $this->logConvertErrorCreateShippingLabel($saleOrder, 'sale order has weight > 60oz.');
                            $this->messageToSlack("Sale order $saleOrder->order_number: has weight > 60oz.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                            continue;
                        }

                        $dataCubicSaleOrder = $this->getDataCubicSaleOrder($weightOrder, $dataCubic);
                        if (count($dataCubicSaleOrder) != 3 && count($dataCubicSaleOrder) != 0) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order: $saleOrder->order_number has data cubic invalid.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        // check api key easypost
                        // true: has api key
                        // false: not api key
                        $isStoreHasApiKey = !empty($saleOrder->store->easypost_api_key);
                        $isDomestic = in_array(strtoupper($dataAddressTo['country']), StoreShipment::DOMESTIC_SHIPPING);
                        $carrierId = $this->getCarrierForAutoCreateShippingLabel($isDomestic, $saleOrder->shipping_method, $shippingCarriers, $dataShippingMethods, $saleOrder->store->id, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps, $weightOrder);
                        if (!$carrierId) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping carrier.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        $carrierCode = $shippingCarriers->where('id', $carrierId)->first()->code ?? '';
                        if (empty($carrierCode)) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping carrier.", $urlHook, $urlGoogleChat);

                            continue;
                        }

                        if ($saleOrder->store_id == Store::PRINTIFY_API_ID) {
                            $dataParcel = [
                                'length' => '',
                                'width' => '',
                                'height' => '',
                            ];
                        } else {
                            $dataParcel = [
                                'length' => $dataCubicSaleOrder[0] == 0 ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC ? '9' : '') : $dataCubicSaleOrder[0],
                                'width' => $dataCubicSaleOrder[1] == 0 ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC ? '6' : '') : $dataCubicSaleOrder[1],
                                'height' => $dataCubicSaleOrder[2] == 0 ? ($carrierCode == self::DEFAULT_CARRIER_DOMESTIC ? '2' : '') : $dataCubicSaleOrder[2],
                            ];
                        }

                        // neu kl >= 16 thi USPS can gui sang voi KL tu 304 oz tro len
                        if ($saleOrder->store_id == Store::PRINTIFY_API_ID) {
                            $dataParcel['weight'] = $weightOrder;
                        } else {
                            $dataParcel['weight'] = $weightOrder >= WeightCubic::WEIGHT_START_HAS_CUBIC && $isDomestic && in_array($carrierCode, ShippingCarrier::CARRIER_NEED_SIZE_CUBIC) ? $this->weightSendToEasypost($weightOrder) : $weightOrder;
                        }

                        $dataGetShippingMethod = $this->getDataShippingMethodByStoreAndCarrier($dataShippingMethods, $saleOrder->shipping_method, $saleOrder->store->id, $carrierId);

                        if (!$dataGetShippingMethod) {
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order $saleOrder->order_number: not map with shipping method.", $urlHook, $urlGoogleChat);

                            continue;
                        }
                        $dataShippingCarrierEasypost = $carrierAccounts->where('carrier_id', $dataGetShippingMethod->carrier_id)
                            ->where('store_id', $dataGetShippingMethod->store_id)
                            ->where('name', $dataGetShippingMethod->name)
                            ->where('warehouse_id', $saleOrder->warehouse_id)
                            ->where('status', true)
                            ->first();

                        if (!$dataShippingCarrierEasypost || (empty($dataShippingCarrierEasypost->carrier_account) || empty($dataShippingCarrierEasypost->api_key_easypost))) {
                            $storeInvalid[$saleOrder->store->id] = now();
                            $this->logErrorSendToSlackGoogleChat($saleOrder, "Sale order: $saleOrder->order_number Not found carrier account or easypost apikey.", $urlHook, $urlGoogleChat);

                            continue;
                        }
                        $text = '';
                        if (in_array($saleOrder->warehouse_id, Warehouse::WAREHOUSE_MEXICO)) {
                            $text = $this->getTextForLabelOfMexico($saleOrder->barcodeItems);
                        }

                        try {
                            EasyPost::setApiKey($dataShippingCarrierEasypost->api_key_easypost);
                            $data = [
                                'carrier_accounts' => $dataShippingCarrierEasypost->carrier_account,
                                'from_address' => $dataAddressFrom,
                                'to_address' => $dataAddressTo,
                                'parcel' => $dataParcel,
                                'return_address' => $dataAddressReturn,
                                'reference' => $saleOrder->order_number,
                                'customs_info' => $customsInfo
                            ];
                            $options = [];

                            // add part number for create label
                            if (in_array($saleOrder->warehouse_id, Warehouse::WAREHOUSE_MEXICO)) {
                                $options['print_custom_1'] = $text;
                            }

                            if ($saleOrder->store_id == Store::STORE_REDBUBBLE && in_array($carrierCode, [ShippingCarrier::UPS_MI_CODE, ShippingCarrier::FEDEX_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])) {
                                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
                            } elseif (in_array($carrierCode, [ShippingCarrier::DHL_ECOMMERCE_CODE])) {
                                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
                            }

                            if (!empty($options)) {
                                $data['options'] = $options;
                            }

                            if (!empty($saleOrder->ioss_number)) {
                                $data['tax_identifiers'] = [
                                    [
                                        'entity' => 'SENDER',
                                        'tax_id' => $saleOrder->ioss_number,
                                        'tax_id_type' => $saleOrder->tax_id_type,
                                        'issuing_country' => $dataAddressTo['country'],
                                    ]
                                ];
                            }

                            $shipment = ShipmentEasyPost::create($data);

                            // check create shipment error
                            if (!empty($shipment->messages)) {
                                foreach ($shipment->messages as $error) {
                                    $this->logConvertErrorCreateShippingLabel($saleOrder, "Sale order: $saleOrder->order_number " . $error->type . ' ' . $error->message);
                                    $this->messageToSlack("Sale order: $saleOrder->order_number " . $error->type . ' ' . $error->message, $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);
                                }

                                continue;
                            }
                            if (in_array($saleOrder->shipping_method, self::SHIPPING_METHOD_DEFAULT_AUTO)) {
                                $rate = $isDomestic
                                    ? $this->getRateDomestic($saleOrder, $shipment, $weightOrder, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps)
                                    : $this->getRateInternational($shipment, $dataAddressTo['country'], $saleOrder);
                            } elseif ($saleOrder->shipping_method == ShippingMethod::ECONOMY && $isDomestic) {
                                $rate = $this->getRateDomestic($saleOrder, $shipment, $weightOrder, $isProductHardGood, $isStoreHasApiKey, $isStoreHardGoodUseUsps);
                            } else {
                                $rate = $this->getRateWithShippingMethod($dataGetShippingMethod->shippingCarrierService->name ?? null, $shipment);
                            }
                            if (empty($rate)) {
                                $this->logConvertErrorCreateShippingLabel($saleOrder, "Sale order $saleOrder->order_number: not map rate with Easypost.");
                                $this->messageToSlack("Sale order $saleOrder->order_number: not map rate with Easypost.", $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);

                                continue;
                            }

                            $isCheckOrderHasShipment = $this->isCheckOrderHasShipment($saleOrder->id);
                            // sale order da co shipment
                            if ($isCheckOrderHasShipment) {
                                echo "Order $saleOrder->id has shipment \n";

                                continue;
                            }

                            $label = $shipment
                                ->buy([
                                    'rate' => $rate,
                                    'insurance' => 0
                                ])
                                ->label(['file_format' => 'ZPL']);
                        } catch (EasyPostError $e) {
                            if ($e->ecode === 'PAYMENT_REQUIRED') {
                                $this->messageToSlack('PAYMENT_REQUIRED - ' . $e->getMessage(), $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,order number: ' . $saleOrder->order_number);
                                $this->sendGoogleChat('`Store: ' . $saleOrder->store->name . '`' . 'PAYMENT_REQUIRED - ' . $e->getMessage(), $urlGoogleChat->value);
                                echo "Create label error for PAYMENT_REQUIRED \n";
                                $storeInvalid[$saleOrder->store->id] = now();

                                continue;
                            }
                            $this->messageToSlack($e->getHttpBody(), $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);
                            echo "Create label error $saleOrder->id \n";
                            $this->logConvertErrorCreateShippingLabel($saleOrder, $e->getHttpBody());

                            continue;
                        }
                        DB::beginTransaction();
                        $shippingAccount = $easypostApiKeyDefault->value === $dataShippingCarrierEasypost->api_key_easypost
                            ? ((in_array($dataShippingCarrierEasypost->store_id, $arrStoreUsesAccountSwiftpod) || in_array($dataShippingCarrierEasypost->name, $getNameShippingMethodSwiftpod)) ? 'swiftpod' : 'swiftpod_store')
                            : 'store';
                        $shipmentId = $this->storeShipment($label, $saleOrder, null, $shippingAccount, self::IS_AUTO, $dataShippingCarrierEasypost->carrier_account);
                        echo "shipment_id $shipmentId \n";
                        $shipmentEasypost = Util::convertEasyPostObjectToArray($label);
                        $dataLog = [
                            'order_id' => $saleOrder->id,
                            'shipment_id' => $shipmentId,
                            'easypost_id' => isset($shipmentEasypost['id']) ? $shipmentEasypost['id'] : null,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        ShipmentEasypostModel::insert($dataLog);
                        $saleOrder->shipment_id = $shipmentId;
                        $saleOrder->order_status = SaleOrder::SHIPPED;
                        $saleOrder->save();

                        // gan shipment voi package với store của RB
                        if ($saleOrder->store_id == Store::STORE_REDBUBBLE) {
                            $isShipmentPackageByOrderId = ShipmentPackage::query()->where('order_id', $saleOrder->id)->first();
                            $dataShipmentPackage = [];
                            if (!$isShipmentPackageByOrderId) {
                                $dataShipmentPackage[] = [
                                    'shipment_id' => $shipmentId,
                                    'quantity' => 1,
                                    'order_id' => $saleOrder->id,
                                    'sku' => $this->addShipmentWithPackage($saleOrder->order_quantity, $dataRbPackage)
                                ];
                                $dataInsertSku = $this->getInsertForRedbubble()?->rb_sku;
                                if (!empty($dataInsertSku)) {
                                    $dataShipmentPackage[] = [
                                        'shipment_id' => $shipmentId,
                                        'quantity' => 1,
                                        'order_id' => $saleOrder->id,
                                        'sku' => $dataInsertSku
                                    ];
                                }
                                ShipmentPackage::insert($dataShipmentPackage);
                            }
                        }

                        //verify label id tu dong gan voi shipment da chon
                        $this->verifyLabelIdWithShipmentAuto($saleOrder->items, $shipmentId);

                        saleOrderHistory(User::SYSTEM, null, $saleOrder->id, SaleOrderHistory::CREATE_LABEL, "Label $label?->tracking_code successfully created by Auto Shipping.");
                        DB::commit();
                        echo "Create label shipping for sale order $saleOrder->id \n";
                    } catch (\Throwable $exception) {
                        DB::rollBack();
                        $this->logConvertErrorCreateShippingLabel($saleOrder, $exception->getMessage());
                        $this->messageToSlack("Sale order $saleOrder->order_number error: " . $exception->getMessage(), $urlHook->value ?? '', $saleOrder->store->name, $saleOrder->store->id . ' ,Order number: ' . $saleOrder->order_number);
                        echo "sleep 1/2 min! \n";
                        sleep(60 * 0.5);
                    }
                }
                echo "done create shipment \n";
                sleep($sleepTimeCycle);
            } catch (\Throwable $exception) {
                echo $exception->getMessage() . "\n";
                sleep($sleepTimeCycle);
            }
        }
    }

    public function getWareHouseHasAutoCreateShipping()
    {
        return Warehouse::where('is_auto_create_shipping', Warehouse::AUTO_CREATE_SHIPPING)
            ->get('id')
            ->pluck('id');
    }

    public function getStoreWithHasAutoCreateShipping($mode, $storeInvalid)
    {
        $storeIds = Store::where('is_auto_create_shipping', $mode)
            ->whereNotNull('date_start_auto_label')
            ->whereNotIn('id', array_keys($storeInvalid))
            ->where('is_active', Store::STATUS_ACTIVE)
            ->get('id')
            ->pluck('id');

        return $storeIds;
    }

    public function getShippingCarriers()
    {
        return ShippingCarrier::select('id', 'code', 'name')->where('is_deleted', '<>', 1)->get();
    }

    public function getShippingCarrierEasypost()
    {
        return ShippingCarrierEasypost::where('status', ShippingCarrierEasypost::ACTIVE)->get();
    }

    public function getApiKeyDefaultEasypost()
    {
        return Setting::where('name', Setting::EASYPOST_API_KEY)->first();
    }

    public function getDataProductTypeWeight()
    {
        return ProductTypeWeight::get()->toArray();
    }

    public function getDataWeightCubic()
    {
        return WeightCubic::get()->toArray();
    }

    public function getOrderForCreateLabelAuto($storeIds, $warehouseIds, $mode, $totalThread = 1, $numberThread = 0, $warehouseId = null)
    {
        // Tìm order cách đấy 10 ngày
        $skipOrderId = SaleOrder::getIDBeforeDays(10);

        $currentDateTime = Carbon::now(); // Lấy thời gian hiện tại
        $minuteThreshold = 90; // số phút mà bạn muốn so sánh
        $saleOrders = SaleOrder::select(
            'sale_order.id',
            'sale_order.store_id',
            'sale_order.warehouse_id',
            'sale_order.merchant_name',
            'sale_order.order_number',
            'sale_order.shipping_method',
            'sale_order.shipment_id',
            'sale_order.order_status',
            'sale_order.created_at',
            'sale_order.order_quantity',
            'sale_order.ioss_number',
            'sale_order.tax_id_type',
        )
            ->with([
                'store:id,name,easypost_api_key',
                'warehouse',
                'addressSaleOrder',
                'items' => function ($q) {
                    $q->select(
                        'sale_order_item.order_id',
                        'sale_order_item.product_id',
                        'sale_order_item.quantity',
                        'sale_order_item.sku',
                        'sale_order_item.product_sku',
                        'sale_order_item.product_style_sku',
                        'sale_order_item.product_size_sku',
                        'sale_order_item.is_deleted',
                        'sale_order_item.id',
                        'sale_order_item.quantity',
                        'sale_order_item.account_id',
                        'sale_order_item.warehouse_id',
                        'sale_order_item.store_id',
                    )
                        ->with('product:id,weight_single,weight_multiple,style,size', 'productStyle:type,sku', 'productStyle.productType:name,is_hard_goods', 'productSize')
                        ->with(['barcodes' => function ($q) {
                            $q->select(
                                'sale_order_item_barcode.id',
                                'sale_order_item_barcode.order_id',
                                'sale_order_item_barcode.order_item_id',
                                'sale_order_item_barcode.label_id',
                                'sale_order_item_barcode.part_number_id',
                            )
                                ->where('is_deleted', '<>', SaleOrderItemBarcode::DELETED);
                        }]);
                },
                'storeAddress' => function ($q) {
                    $q->where('type_address', 'return_address');
                },
                'barcodeItems' => function ($q) {
                    $q->select('sale_order_item_barcode.id', 'sale_order_item_barcode.order_id', 'sale_order_item_barcode.label_id', 'sale_order_item_barcode.part_number_id')
                        ->with('partNumber:id,product_id,part_number')
                        ->where('is_deleted', '<>', SaleOrderItemBarcode::DELETED);
                }
            ])
            ->join('store', 'store.id', '=', 'sale_order.store_id')
            ->where('sale_order.id', '>=', $skipOrderId)
            ->whereIn('sale_order.store_id', $storeIds)
            ->where('sale_order.is_shipment_create_error', '<>', SaleOrder::IS_SHIPMENT_CREATE_ERROR)
            ->where('sale_order.order_status', SaleOrder::IN_PRODUCTION)
            ->where('sale_order.is_fba_order', false)
            ->where('sale_order.order_quantity', '<', self::ORDER_QUANTITY)
            ->where('sale_order.barcode_printed_status', '>', SaleOrder::BARCODE_NOT_PRINTED)
            ->whereNull('sale_order.shipment_id')
            ->where('sale_order.shipping_method', '<>', SaleOrder::SHIPPING_METHOD_EXPRESS)
            ->whereRaw('sale_order.id % ? = ?', [$totalThread, $numberThread])
            ->whereIn('sale_order.warehouse_id', $warehouseIds)
            ->whereRaw('TIMESTAMPDIFF(MINUTE, sale_order.created_at, ?) >= ?', [$currentDateTime, $minuteThreshold]);

        if ($warehouseId) {
            $saleOrders->where('sale_order.order_printed_status', '>', SaleOrder::ORDER_NOT_PRINTED);
        } else {
            // Single Deduction & Multiple Staging
            if ($mode == AutoCreateLabelShipping::MODE_SINGLE_OR_MULTIPLE) {
                $saleOrders->where(function ($q) {
                    $q->where('sale_order.order_pulled_status', '>', SaleOrder::ORDER_NOT_PULLED);
                    $q->orWhere('sale_order.order_staged_status', '>', SaleOrder::ORDER_NOT_STAGED);
                });
            }
            // Shirt Printed
            if ($mode == AutoCreateLabelShipping::MODE_SHIRT_PRINTED) {
                $saleOrders->where('sale_order.order_printed_status', '>', SaleOrder::ORDER_NOT_PRINTED);
            }
            // Wip printed
            if ($mode == AutoCreateLabelShipping::MODE_WIP_PRINTED) {
                $saleOrders->where('sale_order.barcode_printed_status', '>', SaleOrder::ORDER_NOT_WIP_PRINTED);
            }

            if ($mode == AutoCreateLabelShipping::MODE_QC) {
                $saleOrders->where('sale_order.order_qc_status', '>', SaleOrder::ORDER_NOT_QC);
            }
        }

        $saleOrders = $saleOrders->orderBy('sale_order.id', 'ASC')
            ->limit(100)
            ->get();

        return $saleOrders;
    }

    public function dataCustomsInfoV1($saleOrder, $dataWeight)
    {
        $dataCustomsInfo = [
            'customs_certify' => true,
            'customs_signer' => $saleOrder->store->name ?? LabelRepository::CUSTOMS_SIGNER,
            'contents_type' => LabelRepository::CUSTOMS_TYPE,
            'restriction_type' => 'none',
            'contents_explanation' => '',
            'eel_pfc' => LabelRepository::EEL_PFC,
            'non_delivery_option ' => LabelRepository::NON_DELIVERY_OPTION,
            'customs_items' => $this->dataCustomsItemsV1($saleOrder, $dataWeight),
        ];

        return $dataCustomsInfo;
    }

    public function dataCustomsItemsV1($saleOrder, $dataWeight)
    {
        $dataCustomsItems = [];
        foreach ($saleOrder->items as $saleOrderItem) {
            if (!$saleOrderItem->productStyle) {
                // $this->info("SKU not map to product style");
                throw new \Exception('Not get type product');
            }
            if (!$saleOrderItem->productSize) {
                //  $this->info("Size sku not map to product size");
                throw new \Exception('Size sku not map to product size');
            }
            $typeProductStyle = ucfirst($saleOrderItem->productStyle->type);
            $typeProductSize = ucfirst($saleOrderItem->productSize->name);
            $weightProduct = $dataWeight->filter(function ($var) use ($typeProductStyle, $typeProductSize) {
                return $var['name'] == $typeProductStyle && $var['size'] == $typeProductSize;
            });
            if ($weightProduct->isEmpty()) {
                $weightProduct = $dataWeight->filter(function ($v) use ($typeProductStyle) {
                    return $v['name'] == $typeProductStyle && $v['size'] == 'any';
                });
            }
            $weightProduct = $weightProduct->isEmpty() ? 0 : $weightProduct->first()->weight_oz;
            $sumWeightSaleOrder = (int) $weightProduct * $saleOrderItem->quantity;
            $dataCustomsItems[] = [
                'description' => $saleOrderItem->productStyle ? $saleOrderItem->productStyle->type . ' - ' . $saleOrderItem->productStyle->sku : self::DEFAULT_DESCRIPTION_CUSTOMS_ITEM,
                'quantity' => $saleOrderItem->quantity,
                'weight' => $sumWeightSaleOrder,
                'value' => 10,
                'hs_tariff_number' => LabelRepository::CUSTOMS_DEFAULT,
                'origin_country' => self::DEFAULT_ORIGIN_COUNTRY,
                'code' => $saleOrderItem->product_style_sku,
            ];
        }

        return $dataCustomsItems;
    }

    public function getCarrierAccountDefault($shippingCarriers, $carrierAccounts, $nameCarrier, $warehouseId)
    {
        $shippingCarrierId = $shippingCarriers->where('code', $nameCarrier)->first();
        if (!$shippingCarrierId) {
            return null;
        }
        $carrierAccount = $carrierAccounts->where('store_id', self::DEFAULT_STORE_HAS_CARRIER)
            ->where('carrier_id', $shippingCarrierId->id)
            ->where('warehouse_id', $warehouseId)
            ->first();
        if (!$carrierAccount) {
            return null;
        }

        return $carrierAccount->carrier_account;
    }

    public function getDataAccountEasyPostByCarrier($shippingCarriers, $dataCarrierOrder, $carrierName, $warehouseId)
    {
        $carrier = $shippingCarriers->where('code', $carrierName)->first();
        if (!$carrier) {
            return null;
        }
        foreach ($dataCarrierOrder as $item) {
            if ($item->carrier_id === $carrier->id && $item->warehouse_id === $warehouseId) {
                return $item->carrier_account;
            }
        }

        return null;
    }

    public function sendGoogleChat($message, $url)
    {
        $params = '{"text": "' . $message . '"}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, ($params));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    public function logErrorSendToSlackGoogleChat($saleOrder, $message, $urlHook, $urlGoogleChat)
    {
        echo "Error: $message \n ";
        $this->logConvertErrorCreateShippingLabel($saleOrder, $message);
        $this->messageToSlackV1($message, $urlHook?->value, 'create shipping label');
        $this->sendGoogleChat($message, $urlGoogleChat?->value);
    }

    public function messageToSlackV1($message, $urlHook, $messageTittle)
    {
        $dateTime = date('Y-m-d H:i:s');
        $data
            = [
                'blocks' => [
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => " *`Error`* _ $messageTittle"
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => "$message"
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'fields' => [
                            [
                                'type' => 'mrkdwn',
                                'text' => "When: $dateTime"
                            ]
                        ]
                    ],
                    [
                        'type' => 'divider'
                    ]
                ]
            ];

        $ch = curl_init($urlHook);
        $headers = [
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    public function dataAddressFrom($saleOrderWarehouse, $storeName, $carrierCode)
    {
        $addressFrom = [
            'name' => $saleOrderWarehouse->from_name ?? self::NAME_DEFAULT_FOR_ADDRESS,
            'company' => $carrierCode == ShippingCarrier::PASSPORT_CODE ? $storeName : 'SWIFTPOD LLC',
            'street1' => $saleOrderWarehouse->street1 ?? '',
            'street2' => $saleOrderWarehouse->street2 ?? '',
            'city' => $saleOrderWarehouse->city ?? '',
            'state' => $saleOrderWarehouse->state ?? '',
            'zip' => $saleOrderWarehouse->zip ?? '',
            'country' => $saleOrderWarehouse->country ?? '',
            'phone' => LabelRepository::getNumberPhone($saleOrderWarehouse->phone, $saleOrderWarehouse->country ?? ''),
            'email' => '',
            'carrier_facility' => $saleOrderWarehouse->carrier_facility ?? null
        ];

        return $addressFrom;
    }

    public function getRateDomestic($saleOrder, $shipment, $weightOrder, $isProductHardGood, $isStoreHasApiKey, $storeHardGoodsShipUsps)
    {
        echo $saleOrder->shipping_method . "\n";
        $rate = [];
        $serviceName = '';
        //neu la don hard good thi lay rate la DHLParcelGround
        if ($isProductHardGood) {
            if ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_ECONOMY) {
                $serviceName = self::SERVICE_DHL_PARCEL_GROUND;
            } else {
                $serviceName = $storeHardGoodsShipUsps
                    // check neu ma la priority thi chon priority con khong thi theo rule khoi luong
                    ? ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_PRIORITY ? self::SERVICE_PRIORITY_USPS : self::SERVICE_GROUND_ADVANTAGE_USPS)
                    : ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_PRIORITY ? self::SERVICE_PRIORITY_USPS
                        : ($weightOrder < 16 ? self::SERVICE_GROUND_ADVANTAGE_USPS : self::SERVICE_DHL_PARCEL_GROUND)
                    );
            }
        } else {
            if ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_STANDARD) {
                $serviceName = self::SERVICE_GROUND_ADVANTAGE_USPS;
            }
            if ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_ECONOMY) {
                $serviceName = self::SERVICE_DHL_PARCEL_GROUND;
            }
            if ($saleOrder->shipping_method == SaleOrder::SHIPPING_METHOD_PRIORITY) {
                $serviceName = self::SERVICE_PRIORITY_USPS;
            }
        }

        if ($saleOrder->shipping_method == ShippingMethod::SHIPPING_METHOD_FIRST_CLASS) {
            $serviceName = ShippingCarrierService::SERVICE_FIRST_CLASS;
        }

        if (empty($serviceName)) {
            return [];
        }

        foreach ($shipment->rates as $item) {
            if ($item['service'] && $serviceName === $item['service']) {
                $rate = $item;
                break;
            }
        }

        return $rate;
    }

    public function getRateInternational($shipment, $country, $saleOrder)
    {
        $rate = [];
        $rateName = self::RATE_SHIP_INTERNATIONAL_DEFAULT;
        // bo di theo slack https://swiftpod.slack.com/archives/C03KTBJHTDZ/p1727454198260369
        // them theo slack https://swiftpod.slack.com/archives/C03KTBJHTDZ/p1747331439837839
        if (strtoupper($country) == 'CA' && $saleOrder->store_id == Store::PRINTIFY_API_ID) {
            $rateName = self::RATE_SHIP_INTERNATIONAL_CANADA;
        }

        foreach ($shipment->rates as $item) {
            if ($item['service'] && $rateName === $item['service']) {
                $rate = $item;
                break;
            }
        }

        return $rate;
    }

    public function isCheckOrderHasShipment($saleOrderId)
    {
        return SaleOrder::where('id', $saleOrderId)
            ->whereNotNull('shipment_id')
            ->first();
    }

    public function getDataCustomsInfo($saleOrder, $isDataAddressUS, $dataAddressTo, $storeProductWeight = [])
    {
        if ($isDataAddressUS) {
            $customsInfo = in_array($dataAddressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($dataAddressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE)
                ? $this->dataCustomsInfoV2($saleOrder, $storeProductWeight) : '';
        } else {
            $customsInfo = $this->dataCustomsInfoV2($saleOrder, $storeProductWeight);
        }

        return $customsInfo;
    }

    public function weightSendToEasypost($weight)
    {
        return 19 * 16;
    }

    public function getDataCubicSaleOrder($weightOrder, $dataCubic)
    {
        if ($weightOrder < WeightCubic::WEIGHT_START_HAS_CUBIC || $weightOrder > WeightCubic::WEIGHT_END_HAS_CUBIC) {
            $dataCubicSaleOrder = explode('x', '0x0x0');
        } else {
            $cubicOrder = $this->getCubicV1($weightOrder, $dataCubic);
            $dataCubicSaleOrder = !empty($cubicOrder) ? explode('x', $cubicOrder) : [];
        }

        return $dataCubicSaleOrder;
    }

    public function getCubicV1($weightSaleOrder, $dataWeightCubic)
    {
        $dataCubic = '';
        foreach ($dataWeightCubic as $item) {
            if ($weightSaleOrder == $item['weight_end'] && $weightSaleOrder == WeightCubic::WEIGHT_END_HAS_CUBIC) {
                $dataCubic = $item->cubic;
                break;
            }
            if ($item->weight_start <= $weightSaleOrder && $weightSaleOrder < $item->weight_end) {
                $dataCubic = $item->cubic;
                break;
            }
        }

        return $dataCubic;
    }

    public function createShippingFba($input)
    {
        $saleOrderItemBarcode = SaleOrderItemBarcode::with('saleOrder')
            ->where('label_id', $input['label_id'])
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->where('is_deleted', false)
            ->first();

        if (!$saleOrderItemBarcode) {
            throw new Exception('Not found label id.', Response::HTTP_NOT_FOUND);
        }

        if (empty($saleOrderItemBarcode->saleOrder)) {
            throw new Exception('Not found sale order.', Response::HTTP_NOT_FOUND);
        }

        if (!$saleOrderItemBarcode->saleOrder->is_fba_order) {
            throw new Exception('The label ID you scanned is not associated with an FBA order. Please try again.', Response::HTTP_NOT_FOUND);
        }

        $shipment = Shipment::where('order_id', $saleOrderItemBarcode->saleOrder->id)
            ->where('store_id', $saleOrderItemBarcode->saleOrder->store_id)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->first();

        if ($shipment) {
            return $this->getDataShipmentLabel($saleOrderItemBarcode->saleOrder->id);
        }

        $dataInsert = [
            'order_id' => $saleOrderItemBarcode->saleOrder->id,
            'account_id' => $saleOrderItemBarcode->saleOrder->account_id,
            'store_id' => $saleOrderItemBarcode->saleOrder->store_id,
            'warehouse_id' => $saleOrderItemBarcode->saleOrder->warehouse_id,
            'ship_date' => Carbon::now()->format('Y-m-d'),
            'shipment_cost' => 0,
            'insurance_cost' => 0,
            'carrier_code' => null,
            'tracking_number' => null,
            'tracking_status' => null,
            'service_code' => null,
            'package_code' => Shipment::PACKAGE_DEFAULT,
            'weight_value' => 0,
            'weight_unit' => Shipment::WEIGHT_DEFAULT,
            'dimension_length' => 0,
            'dimension_width' => 0,
            'dimension_height' => 0,
            'dimension_unit' => Shipment::DIMENSION_DEFAULT,
            'label_url' => $saleOrderItemBarcode->saleOrder->fba_shipping_label ?? '',
            'url_tracking_easypost' => null,
            'shipment_quantity' => 0,
            'provider' => Shipment::PROVIDER_AMAZON,
            'is_auto_created' => false,
            'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            'employee_create_id' => $input['employee_id'],
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_AMAZON,
            'label_zpl_url' => null
        ];
        try {
            DB::beginTransaction();

            $shipmentId = Shipment::insertGetId($dataInsert);
            SaleOrder::where('id', $saleOrderItemBarcode->saleOrder->id)->update(['order_status' => SaleOrder::SHIPPED, 'shipment_id' => $shipmentId]);
            SaleOrderItemBarcode::where('order_id', $saleOrderItemBarcode->saleOrder->id)->where('is_deleted', false)->update(['shipped_at' => now(), 'employee_ship_id' => $input['employee_id']]);

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['time_tracking_id']);

            saleOrderHistory(
                auth()->user()['id'],
                $input['employee_id'],
                $saleOrderItemBarcode->saleOrder->id,
                SaleOrderHistory::CREATE_LABEL,
                'The order moved from “in production” to “shipped”.',
            );

            DB::commit();

            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrderItemBarcode->saleOrder->id);

            return $this->getDataShipmentLabel($saleOrderItemBarcode->saleOrder->id);
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getDataShipmentLabel($orderId)
    {
        $dataSaleOrder = SaleOrder::with(['store', 'warehouse', 'storeAddress'])
            ->with(['items' => function ($q) {
                $q->orderByDesc('id')->with('product', 'shipmentItem', 'images:sku,order_id,id,order_item_id,order_date,print_side', 'productStyle:sku,type');
            }])
            ->with(['timeline' => function ($q) {
                $q->orderByDesc('id')->with('user:username,created_at,id')->first();
            }])
            ->with(['shipment' => function ($q) {
                $q->orderByDesc('id')
                    ->with(['employeeRefund:id,code,name'])
                    ->with(['items' => function ($q) {
                        $q->orderByDesc('id');
                    }])
                    ->with(['shipmentLabelPrinted' => function ($q) {
                        $q->orderByDesc('id');
                    }]);
            }])
            ->find($orderId);

        return $this->getImageFromOption($dataSaleOrder);
    }

    public function getDataShippingMethodByStoreAndCarrier($dataShippingMethod, $shippingMethodSaleOrder, $storeId, $carrierId)
    {
        $dataGetShippingMethod = $dataShippingMethod->where('api_shipping_method', $shippingMethodSaleOrder)->where('carrier_id', $carrierId)
            ->where('store_id', $storeId)
            ->first();

        if (!$dataGetShippingMethod) {
            $dataGetShippingMethod = $dataShippingMethod->where('api_shipping_method', $shippingMethodSaleOrder)->where('carrier_id', $carrierId)
                ->where('store_id', self::DEFAULT_STORE)
                ->first();
        }

        return $dataGetShippingMethod;
    }

    protected function createShipmentFromEasypost($data, $apikey)
    {
        return ShipmentEasyPost::create($data, $apikey);
    }

    protected function buyAShipmentFromEasyPost($shipmentIdEasyPost, $dataRate, $apiKey)
    {
        $shipment = ShipmentEasyPost::retrieve($shipmentIdEasyPost, $apiKey);
        $dataBuy = [
            'rate' => $dataRate,
            'insurance' => 0
        ];
        if ($dataRate['carrier'] == ShippingCarrier::ONTRACV3_CODE) {
            $data = $shipment
                ->buy($dataBuy);
        } else {
            $data = $shipment
                ->buy($dataBuy)
                ->label(['file_format' => 'PDF']);
        }

        return $data;
    }

    public function checkProductTypeAutoShipping($saleOrderItems, $listProductType)
    {
        if ($listProductType == null || count($listProductType) == 0) {
            return false;
        }
        $checkProductType = false;
        foreach ($saleOrderItems as $saleOrderItem) {
            if ($saleOrderItem?->productStyle?->type && in_array(strtoupper($saleOrderItem?->productStyle?->type), $listProductType)) {
                $checkProductType = true;
                break;
            }
        }

        return $checkProductType;
    }

    public function getShippingMethodEasypost()
    {
        return ShippingMethod::with('shippingCarrierService')->get();
    }

    public function getCarrierForAutoCreateShippingLabel($isDataAddressUS, $shippingMethod, $shippingCarriers, $dataShippingMethods, $storeId, $isProductHardGood, $isStoreHasApiKey, $storeHardGoodsShipUsps, $weightOrder = 0)
    {
        // neu la don hard good va khong co api key
        if ($isProductHardGood && (in_array($shippingMethod, self::SHIPPING_METHOD_DEFAULT_AUTO))) {
            if ($isDataAddressUS) {
                $carrierCode = $storeHardGoodsShipUsps
                    ? self::DEFAULT_CARRIER_DOMESTIC
                    : ($weightOrder < 16 || $shippingMethod == SaleOrder::SHIPPING_METHOD_PRIORITY
                        ? self::DEFAULT_CARRIER_DOMESTIC
                        : self::DEFAULT_CARRIER_HARD_GOOD_DOMESTIC
                    );
            } else {
                $carrierCode = self::DEFAULT_CARRIER_INTERNATIONAL;
            }

            return $shippingCarriers->where('code', $carrierCode)->first()?->id ?? null;
        }

        if ((in_array($shippingMethod, self::SHIPPING_METHOD_DEFAULT_AUTO) && $isDataAddressUS)) {
            $carrierId = $shippingCarriers->where('code', self::DEFAULT_CARRIER_DOMESTIC)->first()?->id ?? null;

            return $carrierId;
        }
        if ((in_array($shippingMethod, self::SHIPPING_METHOD_DEFAULT) && !$isDataAddressUS)) {
            $carrierId = $shippingCarriers->where('code', self::DEFAULT_CARRIER_INTERNATIONAL)->first()?->id ?? null;

            return $carrierId;
        }
        if (!in_array($shippingMethod, self::SHIPPING_METHOD_DEFAULT)) {
            $carrierId = $dataShippingMethods->where('api_shipping_method', $shippingMethod)->where('store_id', $storeId)->first()->carrier_id ?? null;
            if (!$carrierId) {
                return $dataShippingMethods->where('api_shipping_method', $shippingMethod)->where('store_id', self::DEFAULT_STORE)->first()->carrier_id ?? null;
            }

            return $carrierId;
        }

        return null;
    }

    public function getRateWithShippingMethod($serviceMethod, $shipment)
    {
        $rate = [];
        foreach ($shipment->rates as $item) {
            if ($item['service'] && $serviceMethod == $item['service']) {
                $rate = $item;
                break;
            }
        }

        return $rate;
    }

    public function verifyLabelOfInsert($data, $shipmentId, $employeeId)
    {
        $shipmentItemRepository = new ShipmentItemRepository();
        $shipmentItemLabelRepository = new ShipmentItemLabelRepository();
        $orderItemReturn = [];
        foreach ($data as $item) {
            if ($item->shipmentItem->count() == 0) {
                $dataInsert = [
                    'account_id' => $item->account_id,
                    'store_id' => $item->store_id,
                    'warehouse_id' => $item->warehouse_id,
                    'order_id' => $item->order_id,
                    'order_item_id' => $item->id,
                    'shipment_id' => $shipmentId,
                    'sku' => $item->sku,
                    'quantity' => $item->quantity,
                    'product_id' => $item->product_id,
                    'product_sku' => $item->product_sku,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                $shipmentItemId = $shipmentItemRepository->insertGetId($dataInsert);
                // insert shipment item label
                foreach ($item->barcodes as $barcode) {
                    $dataInsertShipmentItemLabel = [
                        'label_id' => $barcode->label_id,
                        'employee_id' => $employeeId,
                        'shipment_id' => $shipmentId,
                        'shipment_item_id' => $shipmentItemId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                    $shipmentItemLabelRepository->insertShipmentItemLabel($dataInsertShipmentItemLabel);
                    Shipment::find($shipmentId)->increment('shipment_quantity');
                }
                $orderItemReturn[] = [
                    'order_item_id' => $item->id,
                    'total_verify' => $item->quantity,
                ];
            }
        }

        return $orderItemReturn;
    }

    public function getStoreHasAutoCreateShipping($storeInvalid)
    {
        $storeIds = Store::where('is_auto_create_shipping', '>', 0)
            ->whereNotNull('date_start_auto_label')
            ->whereNotIn('id', array_keys($storeInvalid))
            ->where('is_active', Store::STATUS_ACTIVE)
            ->get('id')
            ->pluck('id');

        return $storeIds;
    }

    /**
     * @param $saleOrderBarcode
     * $saleOrderBarcode need part number
     */
    public function getTextForLabelOfMexico($saleOrderBarcode)
    {
        $text = '';
        if ($saleOrderBarcode->count() == 0) {
            throw new \Exception('No barcode is available for the order');
        }

        $groupedDataPartNumber = [];
        foreach ($saleOrderBarcode as $barcode) {
            if (empty($barcode->part_number_id)) {
                throw new \Exception('No part number is available for the barcode ' . $barcode->label_id);
            }
            $groupedDataPartNumber[] = [
                'part_number' => substr($barcode->partNumber?->part_number ?? '', 0, -9),
                'quantity' => 1,
            ];
        }
        $groupedData = [];
        foreach ($groupedDataPartNumber as $value) {
            $key = $value['part_number'] ?? '';
            if (!isset($groupedData[$key])) {
                $groupedData[$key] = $value['quantity'] ?? 0;
            } else {
                $groupedData[$key] += $value['quantity'] ?? 0;
            }
        }
        foreach ($groupedData as $key => $value) {
            if ($value > 1) {
                $text .= $key . 'x' . $value . ', ';
            } else {
                $text .= $key . ', ';
            }
        }
        $text = rtrim($text, ', ');

        return $text;
    }

    public function getNameShippingMethodSwiftpod()
    {
        return ShippingMethod::where('store_id', self::DEFAULT_STORE)->groupBy('name')->pluck('name');
    }

    public function getLabelByLabelIds($labelIds)
    {
        if (count($labelIds) > 20) {
            array_pop($labelIds);
        }
        $addressType = 'to_address';

        return DB::table('sale_order_item_barcode')
            ->select(
                'sale_order_item_barcode.id',
                'sale_order_item_barcode.label_id',
                'product.style AS style',
                'product.color AS color',
                'product.size AS size',
                'sale_order_item_image.image_url',
                'sale_order_address.name AS address_name',
                'shipment_label_printed.printed_date',
                'shipment.id AS shipment_id',
                'employee.name AS employee',
                'shipment_label_printed.id AS id_printed',
            )
            ->leftJoin('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->leftJoin('product', 'sale_order_item.product_id', '=', 'product.id')
            ->leftJoin('sale_order_item_image', 'sale_order_item_barcode.sku', '=', 'sale_order_item_image.sku')
            ->leftJoin('sale_order_address', 'sale_order_item_barcode.order_id', '=', 'sale_order_address.order_id')
            ->leftJoin('shipment', 'sale_order_item_barcode.order_id', '=', 'shipment.order_id')
            ->leftJoin('shipment_label_printed', 'shipment_label_printed.shipment_id', '=', 'shipment.id')
            ->leftJoin('employee', 'employee.id', '=', 'shipment_label_printed.employee_id')
            ->whereIn('sale_order_item_barcode.label_id', $labelIds)
            ->where('sale_order_address.type_address', $addressType)
            ->groupBy('shipment_label_printed.id')
            ->orderBy('shipment_label_printed.printed_date', 'DESC')
            ->limit(20)
            ->get();
    }

    public function addShipmentWithPackage($quantity, $conditionPacking)
    {
        $rb_sku = '';
        foreach ($conditionPacking as $condition) {
            if (dynamicComparison($quantity, $condition['condition_package'])) {
                $rb_sku = $condition['rb_sku'];
            }
        }

        return $rb_sku;
    }

    public function getConditonPacking()
    {
        return RedbubblePacking::query()->where('packaging_active', 1)
            ->whereNotNull('condition_package')
            ->get();
    }

    public function getInsertForRedbubble()
    {
        return RedbubblePacking::query()->where('packaging_active', 1)
            ->where('packaging_type', RedbubblePacking::PACKAGING_TYPE_INSERT)
            ->first();
    }

    public function verifyLabelIdWithShipmentAuto($saleOrderItem, $shipmentId)
    {
        try {
            foreach ($saleOrderItem as $itemSaleOrder) {
                $dataShipmentItem = [
                    'account_id' => $itemSaleOrder->account_id,
                    'store_id' => $itemSaleOrder->store_id,
                    'warehouse_id' => $itemSaleOrder->warehouse_id,
                    'order_id' => $itemSaleOrder->order_id,
                    'order_item_id' => $itemSaleOrder->id,
                    'shipment_id' => $shipmentId,
                    'sku' => $itemSaleOrder->sku,
                    'quantity' => $itemSaleOrder->quantity,
                    'product_id' => $itemSaleOrder->product_id,
                    'product_sku' => $itemSaleOrder->product_sku,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                $shipmentItemId = ShipmentItem::insertGetId($dataShipmentItem);
                $dataShipmentItemLabel = [];
                foreach ($itemSaleOrder->barcodes as $itemSaleOrderLabel) {
                    $dataShipmentItemLabel[] = [
                        'label_id' => $itemSaleOrderLabel?->label_id,
                        'employee_id' => $itemSaleOrderLabel->employee_kitted_id ? $itemSaleOrderLabel->employee_kitted_id : 0,
                        'shipment_id' => $shipmentId,
                        'shipment_item_id' => $shipmentItemId,
                        'part_number_id' => $itemSaleOrderLabel?->part_number_id,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }
                ShipmentItemLabel::insert($dataShipmentItemLabel);
            }
        } catch (\Throwable $exception) {
            throw $exception;
        }
    }

    public function dataCustomsInfoV2($saleOrder, $storeProductWeight = [])
    {
        $dataCustomsInfo = [
            'customs_certify' => true,
            'customs_signer' => $saleOrder->store->name ?? LabelRepository::CUSTOMS_SIGNER,
            'contents_type' => LabelRepository::CUSTOMS_TYPE,
            'restriction_type' => 'none',
            'contents_explanation' => '',
            'eel_pfc' => LabelRepository::EEL_PFC,
            'non_delivery_option ' => LabelRepository::NON_DELIVERY_OPTION,
            'customs_items' => $this->dataCustomsItemsV2($saleOrder, $storeProductWeight),
        ];

        return $dataCustomsInfo;
    }

    public function dataCustomsItemsV2($saleOrder, $storeProductWeight = [])
    {
        $dataCustomsItems = [];
        foreach ($saleOrder->items as $saleOrderItem) {
            $weightProduct = $this->getWeightProduct($saleOrderItem, $saleOrder->order_quantity, $storeProductWeight);
            $sumWeightSaleOrderItem = (float) $weightProduct * $saleOrderItem->quantity;
            $dataCustomsItems[] = [
                'description' => $saleOrderItem->productStyle ? $saleOrderItem->productStyle->type . ' - ' . $saleOrderItem->productStyle->sku : self::DEFAULT_DESCRIPTION_CUSTOMS_ITEM,
                'quantity' => $saleOrderItem->quantity,
                'weight' => $sumWeightSaleOrderItem,
                'value' => 10,
                'hs_tariff_number' => LabelRepository::CUSTOMS_DEFAULT,
                'origin_country' => self::DEFAULT_ORIGIN_COUNTRY,
                'code' => $saleOrderItem->product_style_sku,
            ];
        }

        return $dataCustomsItems;
    }

    public function getWeightProduct($saleOrderItem, $orderQuantity, $storeProductWeight = [])
    {
        if (!$saleOrderItem->product) {
            throw new \Exception('Not found data product.');
        }

        if (!empty($storeProductWeight[$saleOrderItem->product?->sku])) {
            return $storeProductWeight[$saleOrderItem->product?->sku];
        }

        if ($orderQuantity > 1 && empty($saleOrderItem->product->weight_multiple)) {
            throw new \Exception('Weight not defined for product SKU ' . $saleOrderItem->product?->sku . ' (' . $saleOrderItem->product?->style . ' ' . $saleOrderItem->productSize?->name . ') for multiple item orders.');
        }
        if ($orderQuantity == 1 && empty($saleOrderItem->product->weight_single)) {
            throw new \Exception('Weight not defined for product SKU ' . $saleOrderItem->product?->sku . ' (' . $saleOrderItem->product?->style . ' ' . $saleOrderItem->productSize?->name . ') for single item order.');
        }

        return $orderQuantity > 1 ? $saleOrderItem->product?->weight_multiple : $saleOrderItem->product?->weight_single;
    }

    public function createShippingGelato($input)
    {
        $saleOrder = SaleOrder::where('id', $input['order_id'])
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->first();

        if (!$saleOrder) {
            throw new Exception('Not found sale order.', Response::HTTP_NOT_FOUND);
        }

        $gelatoRepository = new GelatoRepository();
        $shipment = $gelatoRepository->getOrderFromGelato($saleOrder->external_number);
        $shipmentBody = json_decode($shipment['data']);
        if (!$shipment['status']) {
            throw new Exception($shipmentBody->error, Response::HTTP_NOT_FOUND);
        }

        foreach ($shipmentBody->documents as $item) {
            if ($item->type == 'label') {
                //tao data log vao bang shipment
                $dataInsert = $gelatoRepository->buildDataShipment($saleOrder, $shipmentBody->trackingCode, $item->url, $shipmentBody?->trackingLink ?? null);

                DB::beginTransaction();
                $idShipment = DB::table('shipment')
                    ->insertGetId($dataInsert);
                $saleOrder->shipment_id = $idShipment;
                $saleOrder->order_status = SaleOrder::SHIPPED;
                $saleOrder->save();

                //lưu response trả về thành công
                IntegrateLogShipment::create([
                    'store_id' => $saleOrder->store_id,
                    'shipment_id' => $idShipment,
                    'data' => json_encode($shipmentBody),
                    'created_at' => Carbon::now()->toDateTimeString()
                ]);

                //lưu lịch sử tạo shipment
                SaleOrderHistory::create([
                    'user_id' => auth()->user()['id'],
                    'employee_id' => $input['employee_id'],
                    'order_id' => $saleOrder->id,
                    'type' => SaleOrderHistory::CREATE_LABEL,
                    'message' => 'Create label from Gelato',
                    'created_at' => Carbon::now()->toDateTimeString()
                ]);
                DB::commit();
            }
        }
        handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $saleOrder->id);

        //        return $this->getDataShipmentLabel($saleOrder->id);
        return 'success';
    }

    public function buyAShipmentOneCall($input)
    {
        try {
            $dataSaleOrder = SaleOrder::with(
                'store',
                'warehouse',
                'items.barcodes.partNumber:id,product_id',
                'items.product',
                'items.productStyle',
                'items.productSize',
                'shipment',
            )
                ->with(['storeAddress' => function ($q) {
                    $q->where('type_address', '=', 'return_address');
                }])
                ->with(['barcodeItems' => function ($q) {
                    $q->select('id', 'order_item_id', 'order_id', 'part_number_id', 'label_id')
                        ->with(['partNumber' => function ($q) {
                            $q->select('id', 'product_id', 'part_number');
                        }])
                        ->where('is_deleted', '!=', 1);
                }])
                ->find($input['order_id']);
            if (!$dataSaleOrder) {
                return [
                    'data' => [],
                    'message' => 'Order not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $text = '';
            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $groupedDataPartNumber = [];
                foreach ($dataSaleOrder->barcodeItems as $barcode) {
                    $partNumberId = $barcode->partNumber?->id;
                    // xử lý với TH cũ là chưa map với part_number
                    if (!$partNumberId) {
                        throw new \Exception('No part number is available for the barcode ' . $barcode->label_id);
                    }
                    $groupedDataPartNumber[] = [
                        'part_number' => substr($barcode->partNumber?->part_number ?? '', 0, -9),
                        'quantity' => 1,
                    ];
                }

                $groupedData = [];
                foreach ($groupedDataPartNumber as $value) {
                    $key = $value['part_number'] ?? '';
                    if (!isset($groupedData[$key])) {
                        $groupedData[$key] = $value['quantity'] ?? 0;
                    } else {
                        $groupedData[$key] += $value['quantity'] ?? 0;
                    }
                }
                foreach ($groupedData as $key => $value) {
                    if ($value > 1) {
                        $text .= $key . 'x' . $value . ', ';
                    } else {
                        $text .= $key . ', ';
                    }
                }
                $text = rtrim($text, ', ');
            }
            $urlHook = Setting::where('name', 'alert_slack_easypost')->first();
            $easypostApiKeyDefault = Setting::where('name', 'easypost_api_key')->first();
            $easypostApiKeyDefault = $easypostApiKeyDefault ? $easypostApiKeyDefault->value : '';
            $storeUsesAccountSwiftpod = Setting::where('name', Setting::STORE_USE_ACCOUNT_SHIP_SWIFTPOD)->first();
            $arrStoreUsesAccountSwiftpod = $storeUsesAccountSwiftpod && $storeUsesAccountSwiftpod->value != '' ? array_map('trim', explode(',', $storeUsesAccountSwiftpod->value)) : [];
            array_push($arrStoreUsesAccountSwiftpod, 1);
            // tao data shipment sau do gui sang ben easypost
            $dataSaleOrderAddress = SaleOrderAddress::where('order_id', $input['order_id'])->get();
            $addressTo = $this->dataAddressTo($dataSaleOrderAddress, !self::IS_AUTO, $dataSaleOrder);
            if (empty($addressTo)) {
                return [
                    'data' => [],
                    'message' => 'Order does not to Address. Please provide.',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            if (empty($addressTo['country'])) {
                return [
                    'data' => [],
                    'message' => 'Order does not country for address to. Please provide.',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            if (strtoupper($addressTo['country']) !== 'US') {
                $customsInfo = $this->dataCustomsInfoV2($dataSaleOrder);
            } else {
                $customsInfo = in_array($addressTo['city'], SaleOrderAddress::IN_US_NEED_CUSTOMS) || in_array($addressTo['state'], SaleOrderAddress::IN_US_NEED_CUSTOMS_FOR_STATE) ? $this->dataCustomsInfoV2($dataSaleOrder) : '';
            }
            $addressReturn = $this->dataAddressReturn($dataSaleOrder->storeAddress, $dataSaleOrder->merchant_name, $dataSaleOrderAddress);
            if (empty($addressReturn)) {
                $storeName = $dataSaleOrder->store->name;
                $this->messageToSlack('Store has not return address', $urlHook->value ?? '', $storeName, $dataSaleOrder->store->id);

                return [
                    'data' => [],
                    'message' => "Store $storeName does not have Return Address. Please provide.",
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $carrierPackage = DB::table('shipping_carrier_package')
                ->select('shipping_carrier_package.*')
                ->where('shipping_carrier_package.id', $input['predefinedPackageId'])
                ->first();
            if (!$carrierPackage) {
                return [
                    'data' => [],
                    'message' => 'Carrier package not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $dataGetShippingMethod = $this->getDataShippingMethodByStoreAndCarrier($this->getShippingMethodEasypost(), $dataSaleOrder->shipping_method, $dataSaleOrder->store->id, $carrierPackage->carrier_id);
            if (!$dataGetShippingMethod) {
                return [
                    'data' => [],
                    'message' => 'Shipping method not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $accountData = ShippingCarrierEasypost::with('carrier')->where('carrier_id', $dataGetShippingMethod->carrier_id)
                ->where('store_id', $dataGetShippingMethod->store_id)
                ->where('name', $dataGetShippingMethod->name)
                ->where('warehouse_id', $dataSaleOrder->warehouse_id)
                ->where('status', true)
                ->first();

            $dataParcel = [
                'length' => $input['length'] || $input['length'] !== 0 ? $input['length'] : '',
                'width' => $input['width'] || $input['width'] !== 0 ? $input['width'] : '',
                'height' => $input['height'] || $input['height'] !== 0 ? $input['height'] : '',
                'weight' => $this->convertWeightToOZ($input['weight_unit'], $input['weight_value']),
                'predefined_package' => strtoupper($carrierPackage->predefined_package) == strtoupper(ShippingCarrierPackage::PACKAGE_DEFAULT) ? '' : $carrierPackage->predefined_package
            ];
            if (empty($accountData->carrier_account)) {
                return [
                    'data' => [],
                    'message' => 'Carrier account of store not found!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            $addressFrom = $this->dataAddressFrom($dataSaleOrder->warehouse, $dataSaleOrder->store?->name, $accountData->carrier?->code);
            $data = [
                'carrier_accounts' => $accountData->carrier_account,
                'to_address' => $addressTo,
                'from_address' => $addressFrom,
                'parcel' => $dataParcel,
                'return_address' => $addressReturn,
                'reference' => $dataSaleOrder->order_number,
                'customs_info' => $customsInfo,
                'service' => $dataGetShippingMethod->shippingCarrierService?->name
            ];
            $options = [];
            if (in_array(config('jwt.warehouse_id'), Warehouse::WAREHOUSE_MEXICO)) {
                $options['print_custom_1'] = $text;
            }
            if (
                $dataSaleOrder->store_id == Store::STORE_REDBUBBLE
                && in_array($accountData->carrier?->code, [ShippingCarrier::UPS_MI_CODE, ShippingCarrier::FEDEX_CODE, ShippingCarrier::USPS_ECOMMERCE_CODE])
            ) {
                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
            } elseif (in_array($accountData->carrier?->code, [ShippingCarrier::DHL_ECOMMERCE_CODE])) {
                $options['endorsement'] = 'CHANGE_SERVICE_REQUESTED';
            }
            // set
            if ($accountData->carrier?->code == ShippingCarrier::USPS_ECOMMERCE_CODE) {
                $options['date_advance'] = self::DATE_ADVANCE_MANIFEST_EASYPOST;
            }
            if (strtoupper($addressTo['country']) !== 'US' && !empty($dataSaleOrder->ioss_number)) {
                $options['print_custom_2'] = ($dataSaleOrder->tax_id_type ?? SaleOrder::TAX_ID_TYPE_IOSS) . ': #' . $dataSaleOrder->ioss_number;
            }
            if (!empty($options)) {
                $data['options'] = $options;
            }
            if (!empty($dataSaleOrder->ioss_number)) {
                $data['tax_identifiers'] = [
                    [
                        'entity' => 'SENDER',
                        'tax_id' => $dataSaleOrder->ioss_number,
                        'tax_id_type' => $dataSaleOrder->tax_id_type,
                        'issuing_country' => $addressTo['country'],
                    ]
                ];
            }
            try {
                $shipment = $this->buyAShipmentFromEasyPostOneCall($data, $accountData->api_key_easypost);
            } catch (EasyPostError $e) {
                if ($e->ecode === 'PAYMENT_REQUIRED') {
                    if ($urlHook) {
                        $this->messageToSlack('PAYMENT_REQUIRED - ' . $e->getMessage(), $urlHook->value ?? '', $dataSaleOrder->store->name, $dataSaleOrder->store->id);
                    }
                }
                $this->logConvertErrorCreateShippingLabel($dataSaleOrder, $e->getHttpBody());
                $this->messageToSlack($e->getHttpBody(), $urlHook->value ?? '', $dataSaleOrder->store->name, $dataSaleOrder->store->id . ' ,Order Number: ' . $dataSaleOrder?->order_number);

                return [
                    'data' => [],
                    'status' => false,
                    'message' => 'An error occurred, please try again later!',
                    'errorEasypost' => json_decode($e->getHttpBody()),
                    'code' => response::HTTP_NOT_FOUND
                ];
            }

            if (count($shipment->messages) > 0) {
                return [
                    'data' => Util::convertEasyPostObjectToArray($shipment),
                    'message' => $shipment->messages[0]->message ?? 'An error occurred, please try again later!',
                    'code' => response::HTTP_NOT_FOUND
                ];
            }
            DB::beginTransaction();
            if (!empty($data)) {
                $easypostApiKey = $accountData->api_key_easypost;
                $shippingAccount = $easypostApiKeyDefault === $easypostApiKey
                    ? (in_array($accountData->store_id, $arrStoreUsesAccountSwiftpod) || in_array($accountData->name, $this->getNameShippingMethodSwiftpod()->toArray())) ? 'swiftpod' : 'swiftpod_store'
                    : 'store';
                $shipmentId = $this->storeShipment($shipment, $dataSaleOrder, $input['employeeId'], $shippingAccount, !self::IS_AUTO, $accountData->carrier_account);
                $shipmentStatus = $shipment->status ?? null;
                $shipmentEasypost = Util::convertEasyPostObjectToArray($shipment);
                // log vao bang easypost_log
                $dataLog = [
                    'order_id' => $input['order_id'],
                    'shipment_id' => $shipmentId,
                    'easypost_id' => isset($shipmentEasypost['id']) ? $shipmentEasypost['id'] : null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'track_status' => $shipmentStatus
                ];
                ShipmentEasypostModel::insert($dataLog);

                // kiem tra xem sale order co shipment chưa, neu chua co thi add shipmnentId vao
                // chi add shipment cho lan dau tao
                if (!$dataSaleOrder->shipment_id) {
                    $dataSaleOrder->shipment_id = $shipmentId;
                }

                $isShipmentRefund = Shipment::where('id', $dataSaleOrder->shipment_id)->whereNotNull('refund_status')->first();
                if ($isShipmentRefund) {
                    SaleOrder::where('id', $input['order_id'])->update(['shipment_id' => $shipmentId]);
                }

                if ($dataSaleOrder->order_status === SaleOrder::IN_PRODUCTION || $dataSaleOrder->order_status === SaleOrder::NEW_ORDER) {
                    $dataSaleOrder->order_status = SaleOrder::SHIPPED;
                }

                $dataSaleOrder->save();
                //lưu lịch sử tạo shipment
                saleOrderHistory(
                    auth()->user()['id'],
                    $input['employeeId'],
                    $input['order_id'],
                    SaleOrderHistory::CREATE_LABEL,
                    "Label $shipment?->tracking_code successfully created",
                );
            }
            if ($dataSaleOrder->store_id == Store::STORE_REDBUBBLE) {
                $isShipmentPackageByOrderId = ShipmentPackage::query()->where('order_id', $dataSaleOrder->id)->first();
                if (!$isShipmentPackageByOrderId) {
                    $dataRbPackage = $this->getConditonPacking();
                    // add shipment với TH package
                    $dataShipmentPackage[] = [
                        'shipment_id' => $shipmentId,
                        'quantity' => 1,
                        'order_id' => $dataSaleOrder->id,
                        'sku' => $this->addShipmentWithPackage($dataSaleOrder->order_quantity, $dataRbPackage)
                    ];
                    // add insert với TH insert
                    $dataInsertSku = $this->getInsertForRedbubble()?->rb_sku;
                    if (!empty($dataInsertSku)) {
                        $dataShipmentPackage[] = [
                            'shipment_id' => $shipmentId,
                            'quantity' => 1,
                            'order_id' => $dataSaleOrder->id,
                            'sku' => $dataInsertSku
                        ];
                    }
                    ShipmentPackage::insert($dataShipmentPackage);
                }
            }

            // luu shipment fee của easypost tra ve luc mua label
            $dataShipmentFee = [];
            foreach ($shipment->fees as $itemShipmentFee) {
                $dataShipmentFee[] = [
                    'shipment_id' => $shipmentId,
                    'type' => $itemShipmentFee->type ?? null,
                    'amount' => $itemShipmentFee->amount ?? null
                ];
            }
            if (!empty($dataShipmentFee)) {
                ShipmentFee::insert($dataShipmentFee);
            }
            DB::commit();
            // khi co shipment item -> gắn các label với shipment
            if ($dataSaleOrder->shipment->count() == 0 && $input['is_shipment'] && isset($input['label_verify'])) {
                $saleOrderItemsBarcode = SaleOrderItemBarcode::whereIn('label_id', $input['label_verify'])->where('order_id', $dataSaleOrder->id)->get();
                foreach ($saleOrderItemsBarcode as $item) {
                    $this->scanLabelIdToVerify([
                        'order_id' => $dataSaleOrder->id,
                        'label_id' => $item->label_id,
                        'shipment_id' => $shipmentId,
                        'employee_id' => $input['employeeId']
                    ]);
                }
            }
            if (in_array($shippingAccount, ['swiftpod', 'swiftpod_store'])) {
                // dispatch queue recycled tracking number
                $dataDispatchTracking = [
                    'tracking_code' => $shipment->tracking_code,
                    'order_id' => $dataSaleOrder->id,
                    'shipment_id' => $shipmentId,
                    'order_number' => $dataSaleOrder->order_number,
                    'order_type' => $dataSaleOrder->order_type
                ];
                handleJob(QueueJob::QUEUE_CREATE_CHECK_RECYCLED_TRACKING_NUMBER, $dataDispatchTracking);
            }

            return [
                'data' => $shipment,
                'message' => 'success',
                'code' => response::HTTP_OK
            ];
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'data' => [],
                'status' => false,
                'message' => $exception->getMessage(),
                'code' => response::HTTP_BAD_REQUEST
            ];
        }
    }

    public function buyAShipmentFromEasyPostOneCall($data, $apiKey)
    {
        $shipment = ShipmentEasyPost::create($data, $apiKey);
        $data = $shipment
            ->label(['file_format' => 'ZPL']) // chuyen doi sang pdf
;

        return $data;
    }
}
