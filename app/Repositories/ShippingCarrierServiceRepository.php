<?php

namespace App\Repositories;
use App\Models\ShippingCarrierService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class ShippingCarrierServiceRepository extends CommonRepository
{

    public function fetchAll(Request $request)
    {
        $query = ShippingCarrierService::search($request);
        $query->whereHas('shippingCarrier', function($q) {
            $q->where('is_deleted', '<>', 1);
        });
        if(empty($request['page'])) {
            return $query->with('shippingCarrier:id,name,code')->get();
        } else {
            $query->latest('shipping_carrier_package.id')->paginate($request['limit'] ?? self::LIMIT);
        }
    }

    private function validateData(Request $request, int $id = null)
    {
        return $this->validation($request->all(), [
            'name' => 'required',
            'display_name' => 'required',
            'carrier_id' => 'required',
        ]);
    }

    public function prepareData($request): array
    {
        return [
            'name' => $request['name'],
            'display_name' => $request['display_name'],
            'carrier_id' => $request['carrier_id']
        ];
    }

    public function create($request): JsonResponse
    {
        try {

            $validatorRes = $this->validateData($request);
            if ($validatorRes !== true) {
                return $validatorRes;
            }

            $data = $this->prepareData($request);
            $shippingCarrierService = ShippingCarrierService::create($data);

        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Create Shipping Carrier Service successfully!', $shippingCarrierService);
    }

    public function update($id, $request): JsonResponse
    {
        try {
            $shippingCarrierService = ShippingCarrierService::find($id);
            if (!$shippingCarrierService) {
                return $this->errorResponse('Shipping carrier service not found!');
            }

            $validatorRes = $this->validateData($request, $id);
            if ($validatorRes !== true) {
                return $validatorRes;
            }

            $data = $this->prepareData($request);
            foreach ($data as $key => $value) {
                $shippingCarrierService[$key] = $value;
            }
            $shippingCarrierService->save();
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return $this->successResponse('Update Shipping Carrier Service successfully!', $shippingCarrierService);
    }

    public function delete($id)
    {
        $deleted = ShippingCarrierService::where('id', $id)->delete();

        return $this->successResponse('Delete Shipping Carrier Service successfully!', $deleted);
    }
}
