<?php

namespace App\Repositories;

use App\Models\Brand;
use App\Models\Cost2021;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\ProductBlankCostYear;
use App\Models\ProductColor;
use App\Models\ProductHistory;
use App\Models\ProductQuantity;
use App\Models\ProductSize;
use App\Models\ProductSpec;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\SaleOrderAutomationRule;
use App\Repositories\Contracts\ProductRepositoryInterface;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Validator;

class ProductRepository extends CommonRepository implements ProductRepositoryInterface
{
    public function fetchAll($input)
    {
        $yearNow = Carbon::now('America/Los_Angeles')->year;
        $query = Product::with([
            'user',
            'productStyle',
            'productSpec',
            'blankCostYear' => function ($relative) use ($yearNow) {
                $relative->where('year', $yearNow - 1);
            }
        ])->active();

        $query->select([
            'product.*',
            'product_quantity.quantity as quantity',
            'product_quantity.incoming_stock as coming_stock',
            DB::raw('IFNULL(SUM(CASE WHEN purchase_order.id IS NOT NULL THEN purchase_order_item.price ELSE 0 END), 0) as total_price'),
            DB::raw('COUNT(purchase_order.id) AS total_po'),
            DB::raw("COALESCE(product_rank_histories.rank, '-') AS `product_rank`")
        ])
            ->leftJoin('purchase_order_item', 'purchase_order_item.product_id', '=', 'product.id')
            ->leftJoin('purchase_order', function ($join) use ($yearNow) {
                $join->on('purchase_order.id', '=', 'purchase_order_item.po_id')
                    ->whereIn('purchase_order.order_status', [PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::PARTIAL_RECEIVED_STATUS])
                    ->whereYear('purchase_order.order_date', $yearNow);
            })
            ->leftJoin('product_rank_histories', function ($join) {
                $join->on('product_rank_histories.product_id', 'product.id')
                    ->where('product_rank_histories.year', now()->startOfQuarter()->subQuarter()->year)
                    ->where('product_rank_histories.quarter', now()->startOfQuarter()->subQuarter()->quarter);
            });

        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;

        if (!empty($input['rank'])) {
            if ($input['rank'] == '-') {
                $query->whereNull('product_rank_histories.id');
            } else {
                $query->where('product_rank_histories.rank', $input['rank']);
            }
        }

        if (!empty($input['brand_id'])) {
            $query->where('brand_id', $input['brand_id']);
        }

        if (!empty($input['in_stock'])) {
            $query->where('in_stock', $input['in_stock']);
        }

        if (!empty($input['gtin'])) {
            if ($input['gtin'] === 'yes') {
                $query->whereNotIn('product.gtin', ['', '#N/A'])->whereNotNull('product.gtin');
            } else {
                $query->whereIn('product.gtin', ['', '#N/A'])->whereNull('product.gtin');
            }
        }

        $query->leftJoin('product_quantity', function ($join) use ($input) {
            $join->on('product_quantity.product_id', '=', 'product.id');
            $join->where('product_quantity.warehouse_id', '=', $input['warehouse_id']);
        });

        if (!empty($input['type']) && !empty($input['keyword'])) {
            $keyword = $input['keyword'];

            switch ($input['type']) {
                case 'sku':
                    $query->where('product.sku', 'LIKE', '%' . $keyword . '%');
                    break;
                case 'gtin':
                    $query->where('product.gtin', 'LIKE', '%' . $keyword . '%');
                    break;
                case 'name':
                    $query->where('product.name', 'LIKE', '%' . $keyword . '%');
                    break;
                default:
                    $query->where(function ($query) use ($keyword) {
                        $query->orWhere('product.sku', 'LIKE', '%' . $keyword . '%')
                            ->orWhere('product.gtin', 'LIKE', '%' . $keyword . '%')
                            ->orWhere('product.name', 'LIKE', '%' . $keyword . '%');
                    });
                    break;
            }
        }

        if (!empty($input['fetch_variant'])) {
            $query->where('product.parent_id', '<>', 0);
        } else {
            $query->where('product.parent_id', 0);
        }

        if (!empty($input['spec'])) {
            if ($input['spec'] === 'yes') {
                $query->whereHas('productSpec');
            } else {
                $query->whereDoesntHave('productSpec');
            }
        }

        if (!empty($input['stock_status'])) {
            if ($input['stock_status'] === 'yes') {
                $query->where(function ($qr) {
                    $qr->whereNotNull('product_quantity.quantity')
                        ->whereNotNull('product_quantity.incoming_stock')
                        ->whereRaw('product_quantity.incoming_stock + product_quantity.quantity > 0');
                });
            } else {
                $query->where(function ($qr) {
                    $qr->whereNull('product_quantity.quantity')
                        ->orWhereNull('product_quantity.incoming_stock')
                        ->orWhereRaw('product_quantity.incoming_stock + product_quantity.quantity <= 0');
                });
            }
        }

        return $query->groupBy('product.id')
            ->orderBy('product.id', 'desc')
            ->paginate($limit)
            ->through(function ($product) {
                $product->image = env('AWS_S3_URL', '') . '/' . $product->image;

                return $product;
            });
    }

    public function getList($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = Product::select(['product.id', 'product.parent_id', 'brand.name as brand_name'])
            ->leftJoin('brand', 'product.brand_id', '=', 'brand.id')
            ->leftJoin(
                DB::raw('(select product_id, sum(quantity + incoming_stock) as stock from product_quantity group by product_id) as B'),
                function ($join) {
                    $join->on('product.id', '=', 'B.product_id');
                },
            )
            ->with(['productParent' => function ($query) {
                $query->select('id', 'name', 'style', 'size', 'color', 'image')
                    ->where('is_hide', Product::NOT_HIDE);
            }
            ])
            ->where('product.parent_id', '!=', 0)
            ->where('product.is_hide', Product::NOT_HIDE)
            ->groupBy('product.parent_id')
            ->orderBy('product.is_popular', 'DESC');

        if (!empty($input['brand'])) {
            $query = $query->where('brand.name', 'LIKE', '%' . $input['brand'] . '%');
        }

        if (!empty($input['style'])) {
            $query = $query->where('product.style', 'LIKE', '%' . $input['style'] . '%');
        }

        if (!empty($input['new'])) {
            $query = $query->orderBy('product.parent_id', $input['new']);
        }

        $productColors = ProductColor::all();

        return $query->paginate($limit)->through(function ($product) use ($productColors) {
            $product->image = env('AWS_S3_URL', '') . '/' . $product->image;

            if (isset($product->productParent)) {
                $colorParent = explode(',', strtoupper($product->productParent->color));
                $product->productParent->color_code = $productColors->filter(function ($color) use ($colorParent) {
                    return $color->color_code && in_array(strtoupper($color->name), $colorParent);
                })->pluck('color_code', 'name');
                $product->productParent->image = env('AWS_S3_URL', '') . '/' . $product->productParent->image;
            }

            return $product;
        });
    }

    public function getProductListWithAttribute($request)
    {
        $query = Product::select('name', 'id', 'sku', 'style', 'color', 'size', 'gtin', 'gtin_case');

        if (!empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where('product.sku', 'LIKE', '%' . $keyword . '%')
                ->orWhere('product.name', 'LIKE', '%' . $keyword . '%');
        }

        return $query->primary()->get();
    }

    private function createOrUpdateParentProduct($request)
    {
        $parentProduct = Product::where('style', $request['style'])->parent()->first();
        $newColor = [
            'name' => 'color',
            'value' => $request['color'],
            'hex' => null,
            'image' => null
        ];
        $newSize = [
            'name' => 'size',
            'value' => $request['size']
        ];

        if (!$parentProduct) {
            $setting = [
                ['name' => 'color', 'values' => [$newColor]],
                ['name' => 'size', 'values' => [$newSize]]
            ];
            $parentProduct = Product::create([
                'parent_id' => Product::PARENT_PRODUCT,
                'user_id' => auth()->user()['id'],
                'name' => $request['style'],
                'style' => $request['style'],
                'color' => $request['color'],
                'size' => $request['size'],
                'sku' => $request['parent_sku'],
                'brand_id' => $request['brand_id'] ?? null,
                'setting' => $setting
            ]);
        } else {
            $colors = explode(',', $parentProduct->color) ?? [];
            $sizes = explode(',', $parentProduct->size) ?? [];

            if (!in_array($request['color'], $colors) || !in_array($request['size'], $sizes)) {
                $colors[] = $request['color'];
                $sizes[] = $request['size'];
                $parentProduct->color = implode(',', array_unique($colors));
                $parentProduct->size = implode(',', array_unique($sizes));
                $parentProduct->sku = $request['parent_sku'];
                $settings = $parentProduct->setting;

                foreach ($settings as &$setting) {
                    if ($setting['name'] === 'color') {
                        if (!array_search($request['color'], array_column($setting['values'], 'value'))) {
                            $setting['values'][] = $newColor;
                        }
                    }

                    if ($setting['name'] === 'size') {
                        if (!array_search($request['size'], array_column($setting['values'], 'value'))) {
                            $setting['values'][] = $newSize;
                        }
                    }
                }

                $parentProduct->setting = $settings;
                $parentProduct->save();
            }
        }

        return $parentProduct;
    }

    public function update(int $id, $request)
    {
        try {
            $product = Product::find($id);

            if (!$product) {
                $this->errorResponse('Product not found!');
            }

            if (!empty($request['gtin'])) {
                $checkGITN = Product::where([
                    ['id', '!=', $id],
                    ['gtin', '=', $request['gtin']]
                ])->first();

                if ($checkGITN) {
                    return $this->errorResponse('Gtin already exists!', Response::HTTP_UNPROCESSABLE_ENTITY, 'gtin');
                }
            }

            $outputErr = [];

            if (isset($request['cost']) && floatval($request['cost']) < 0) {
                $outputErr['errors']['cost'][] = 'Product price number must be greater than or equal to 0.';
            }

            if (empty($request['weight_single']) || !is_numeric($request['weight_single']) || floatval($request['weight_single']) <= 0) {
                $outputErr['errors']['weight_single'][] = 'Single weight must be greater than 0.';
            }

            if (empty($request['weight_multiple']) || !is_numeric($request['weight_multiple']) || floatval($request['weight_multiple']) <= 0) {
                $outputErr['errors']['weight_multiple'][] = 'Multiple weight must be greater than 0.';
            }

            if (!empty($outputErr['errors'])) {
                $outputErr['message'] = 'Invalid data.';

                return response()->json($outputErr, Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $data = [
                'gtin' => $request['gtin'] ?? null,
                'gtin_case' => $request['gtin_case'],
                'description' => $request['description'] ?? null,
                'user_id' => auth()->user()['id'],
                'brand_id' => $request['brand_id'],
                'cost' => $request['cost'] ?? null,
                'weight_single' => $request['weight_single'] ?? null,
                'weight_multiple' => $request['weight_multiple'] ?? null,
                'is_on_demand' => $request['is_on_demand'] ?? 0,
            ];

            foreach ($data as $key => $value) {
                $product[$key] = $value;
            }

            $product->save();
            ProductHistory::create([
                'user_id' => auth()->user()['id'],
                'product_id' => $product->id,
                'data' => json_encode($data),
                'action' => ProductHistory::ACTION_UPDATE
            ]);
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update product successfully!', $product);
    }

    public function upload($style, $request)
    {
        try {
            $products = Product::where('style', $style);

            if (!$products) {
                $this->errorResponse('Product not found!');
            }

            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $filename = uniqid() . '-' . $file->getClientOriginalName();
                Storage::disk('s3')->putFileAs(
                    Product::PRODUCT_IMAGE_FOLDER,
                    $file,
                    $filename,
                );
            }

            $products->update([
                'image' => Product::PRODUCT_IMAGE_FOLDER . '/' . $filename
            ]);
            $products = Product::where('style', $style)->get();

            foreach ($products as $product) {
                $data = [
                    'gtin' => $product->gtin ?? null,
                    'gtin_case' => $product->gtin_case ?? null,
                    'description' => $product->description ?? null,
                    'user_id' => auth()->user()['id'],
                    'image ' => Product::PRODUCT_IMAGE_FOLDER . '/' . $filename,
                ];
                ProductHistory::create([
                    'user_id' => auth()->id(),
                    'product_id' => $product->id,
                    'data' => json_encode($data),
                    'action' => ProductHistory::ACTION_UPDATE
                ]);
            }
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Upload image successfully!');
    }

    public function updateById($id, $request)
    {
        try {
            $product = Product::find($id);

            if (!$product) {
                $this->errorResponse('Product not found!');
            }

            $product->update($request);
            $product->refresh();
            $data = [
                'gtin' => $product->gtin ?? null,
                'gtin_case' => $product->gtin_case ?? null,
                'description' => $product->description ?? null,
                'user_id' => auth()->id(),
                'image ' => null,
            ];
            $data = array_merge($data, $request);
            ProductHistory::create([
                'user_id' => auth()->user()['id'],
                'product_id' => $product->id,
                'data' => json_encode($data),
                'action' => ProductHistory::ACTION_UPDATE
            ]);

            //send webhook stock & facility notify
            if (!empty($request['is_discontinued'])) {
                // ProductQuantityRepository::stockNotify([$id], Product::SKU_DISCONTINUED);
                handleJob(ProductQuantity::JOB_UPDATE_PRODUCT_QUANTITY, ['product_id' => $id, 'is_discontinued' => true]);
            }
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update successfully!');
    }

    public function updateByStyle($style, $request)
    {
        try {
            $products = Product::where('style', $style);

            if (!(clone $products)->first()) {
                $this->errorResponse('Product not found!');
            }

            $products->update($request);

            foreach ($products as $product) {
                $data = [
                    'gtin' => $product->gtin ?? null,
                    'gtin_case' => $product->gtin_case ?? null,
                    'description' => $product->description ?? null,
                    'user_id' => auth()->id(),
                    'image ' => null,
                ];
                $data = array_merge($data, $request);
                ProductHistory::create([
                    'user_id' => auth()->id(),
                    'product_id' => $product->id,
                    'data' => json_encode($data),
                    'action' => ProductHistory::ACTION_UPDATE
                ]);
            }
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->successResponse('Update successfully!');
    }

    private function buildProductVariant(&$item, $product_id)
    {
        $item['parent_id'] = $product_id;
        $item['in_stock'] = 0;

        if (!empty($item['qty'])) {
            $item['in_stock'] = 1;
            //   $item['quantity'] = $item['qty'];
        }

        foreach ($item['values'] as $option) {
            $item[$option['name']] = $option['value'];
        }

        return $item;
    }

    public function validateCreateProductVariant($productVariants)
    {
        $error = [];
        foreach ($productVariants as $item) {
            $messages2['name'] = 'Name is required';
            $rules2 = [];
            $rules2['name'] = 'required|max:250';

            if (!empty($item['sku'])) {
                $rules2['sku'] = 'unique:product';
                $sku = $item['sku'];
                $messages2['sku.unique'] = "$sku";
            }

            if (!empty($item['gtin'])) {
                $rules2['gtin'] = 'unique:product';
                $gtin = $item['gtin'];
                $messages2['gtin.unique'] = "$gtin";
            }

            $validator2 = Validator::make($item, $rules2, $messages2);

            if ($validator2->fails()) {
                $tmp[] = $validator2->errors()->toArray();
            }
        }

        if (!empty($tmp)) {
            if ($sku = $this->convertError(array_column($tmp, 'sku'))) {
                $error['sku'] = $sku;
            }

            if ($gtin = $this->convertError(array_column($tmp, 'gtin'))) {
                $error['gtin'] = $gtin;
            }
        }

        return $error;
    }

    private function convertError($error)
    {
        $res = [];

        if (!empty($error)) {
            foreach ($error as $item) {
                $res[] = $item[0];
            }
        }

        return $res;
    }

    private function buildProductOption(&$input)
    {
        if (!empty($input['setting'])) {
            foreach ($input['setting'] as $item) {
                $input[$item['name']] = implode(',', array_column($item['values'], 'value'));
            }
        }

        return $input;
    }

    public function fetch($id)
    {
        $product = Product::where('id', $id)->first();

        if (!empty($product)) {
            $product = $product->toArray();
            $product = $this->buildRawProduct($product);
            $productVariants = Product::where('parent_id', $product['id'])
                ->where('is_deleted', 0)
                ->get()->toArray();
            $product['items'] = null;

            if (!empty($productVariants)) {
                $productVariants = $this->buildRawProductVariants($productVariants);

                if (!empty($productVariants)) {
                    $product['items'] = $productVariants;
                }
            }
        }

        return !empty($product) ? $product : [];
    }

    private function buildRawProductVariants($productVariants)
    {
        $data = [];

        foreach ($productVariants as $variant) {
            $item['id'] = $variant['id'];
            $item['price'] = $variant['price'];
            $item['cost'] = $variant['cost'];
            $item['gtin'] = $variant['gtin'];
            $item['sku'] = $variant['sku'];
            $item['name'] = $variant['name'];
            $item['values'] = [
                [
                    'value' => $variant['color'],
                    'name' => 'color'
                ],
                [
                    'value' => $variant['size'],
                    'name' => 'size'
                ],
            ];

            $item['color'] = $variant['color'];
            $item['size'] = $variant['size'];
            $item['gtin_case'] = $variant['gtin_case'];

            array_push($data, $item);
        }

        return $data;
    }

    private function buildRawProduct($product)
    {
        $data['id'] = $product['id'];
        $data['name'] = $product['name'];
        $data['style'] = $product['style'];
        $data['description'] = $product['description'];
        $data['brand_id'] = $product['brand_id'];
        $data['setting'] = $product['setting'];

        return $data;
    }

    private function buildRawProductVariantForUpdate($product)
    {
        $dataUpdate['name'] = $product['name'];
        $dataUpdate['brand_id'] = !empty($product['brand_id']) ? $product['brand_id'] : null;
        $dataUpdate['size'] = $product['size'];
        $dataUpdate['color'] = $product['color'];
        $dataUpdate['size'] = $product['size'];
        $dataUpdate['sku'] = $product['sku'];
        $dataUpdate['gtin'] = $product['gtin'];
        $dataUpdate['gtin_case'] = $product['gtin_case'];
        $dataUpdate['price'] = $product['price'];
        $dataUpdate['cost'] = $product['cost'];

        return $dataUpdate;
    }

    private function buildRawProductParentForUpdate($input)
    {
        $dataUpdate['name'] = $input['name'];
        $dataUpdate['style'] = $input['style'];
        $dataUpdate['description'] = $input['description'];
        $dataUpdate['brand_id'] = $input['brand_id'];
        $dataUpdate['setting'] = $input['setting'];
        $dataUpdate['size'] = $input['size'];
        $dataUpdate['color'] = $input['color'];

        return $dataUpdate;
    }

    public function delete($id)
    {
        try {
            DB::transaction(function () use ($id) {
                Product::where('id', $id)->update(['is_deleted' => 1]);
                Product::where('parent_id', $id)->update(['is_deleted' => 1]);
            });
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 500);
        }

        return response()->json(['message' => 'success'], 200);
    }

    public function validateUpdateProductVariant($productVariants)
    {
        $error = [];

        foreach ($productVariants as $item) {
            $messages2['name'] = 'Name is required';
            $rules2 = [];
            $rules2['name'] = 'required|max:250';

            if (!empty($item['sku'])) {
                $rules2['sku'] = !empty($item['id']) ? 'unique:product,sku,' . $item['id'] : 'unique:product';
                $sku = $item['sku'];
                $messages2['sku.unique'] = "$sku";
            }

            if (!empty($item['gtin'])) {
                $rules2['gtin'] = !empty($item['id']) ? 'unique:product,gtin,' . $item['id'] : 'unique:product';
                $gtin = $item['gtin'];
                $messages2['gtin.unique'] = "$gtin";
            }

            $validator2 = Validator::make($item, $rules2, $messages2);

            if ($validator2->fails()) {
                $tmp[] = $validator2->errors()->toArray();
            }
        }
        if (!empty($tmp)) {
            if ($sku = $this->convertError(array_column($tmp, 'sku'))) {
                $error['sku'] = $sku;
            }

            if ($gtin = $this->convertError(array_column($tmp, 'gtin'))) {
                $error['gtin'] = $gtin;
            }
        }

        return $error;
    }

    public function fetchProductForCreateOrder($input)
    {
        $query = Product::where('parent_id', 0)->where('is_deleted', 0);
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $keyword = !empty($input['keyword']) ? $input['keyword'] : null;
        $query->whereHas('product_variants', function ($query1) use ($keyword) {
            if (!empty($keyword)) {
                $query1->where('name', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('sku', 'LIKE', '%' . $keyword . '%');
            }
        });

        $query->with(['product_variants' => function ($query1) use ($keyword) {
            if (!empty($keyword)) {
                $query1->where('name', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('sku', 'LIKE', '%' . $keyword . '%');
            }
        }]);

        return $query->paginate($limit);
    }

    public function getProductByParams($input)
    {
        $gtin = $input['gtin'];
        $query = "SELECT * FROM product
                    WHERE COALESCE(INSTR(?, gtin)) > 0 AND gtin is not null and gtin != '' AND is_deleted = 0 LIMIT 1";
        $data = DB::select($query, [$gtin]);
        if (!empty($data)) {
            return collect($data[0]);
        }

        return [];
    }

    public function existsSku($sku): bool
    {
        return Product::query()->where('sku', $sku)->active()->exists();
    }

    public function getProductAttributeList(): array
    {
        $data = [];
        $products = Product::query()
            ->select('style', 'color', 'size')
            ->whereNotNull('style')
            ->active()
            ->parent()
            ->get();
        foreach ($products as $product) {
            $style = $product['style'];
            $colorArray = $product['color'] ? explode(',', $product['color']) : [];
            $sizeArray = $product['size'] ? explode(',', $product['size']) : [];

            if (!$style) {
                continue;
            }

            if (!isset($data[$style])) {
                $item = new \stdClass();
                $item->style = $style;
                $item->colors = $colorArray;
                $item->sizes = $sizeArray;
                $data[$style] = $item;

                continue;
            }

            $data[$style]->colors = array_unique(array_merge($data[$style]->colors, $colorArray));
            $data[$style]->sizes = array_unique(array_merge($data[$style]->sizes, $sizeArray));
        }

        return $data;
    }

    public function getPullingShelvesProductQuantity($request)
    {
        $product = Product::where([
            ['style', '=', $request['product_style']],
            ['color', '=', $request['product_color']],
            ['size', '=', $request['product_size']]
        ])
            ->where('parent_id', '<>', 0)
            ->active()
            ->primary()
            ->first();

        if (!$product) {
            return $this->errorResponse('The product is not found or has been deleted!');
        }

        $pullingShelvesLocation = Location::where('warehouse_id', config('jwt.warehouse_id'))->pullingShelves()->first();

        if (!$pullingShelvesLocation) {
            return $this->errorResponse('Pulling shelves location is not found or has been deleted!');
        }

        $locationProduct = LocationProduct::where([
            'location_id' => $pullingShelvesLocation->id,
            'product_id' => $product->id
        ])->first();
        $price = resolve(PurchaseOrderRepository::class)->getLatestProductPriceFromPo($product->id);

        return [
            'quantity' => $locationProduct ? $locationProduct->quantity : 0,
            'price' => $price,
        ];
    }

    public function getProductByAttribute($input)
    {
        return Product::with('productSpec')
            ->where('style', $input['style'] ?? '')
            ->where('color', $input['color'] ?? '')
            ->where('size', $input['size'] ?? '')
            ->where('is_deleted', 0)
            ->primary()
            ->first();
    }

    public function fetchProductByAttribute($input)
    {
        $query = Product::with('productQuantities');

        if (!empty($input['style'])) {
            $query->where('style', $input['style']);
        }

        if (!empty($input['color'])) {
            $query->where('color', $input['color']);
        }

        if (!empty($input['size'])) {
            $query->where('size', $input['size']);
        }

        if (!empty($input['sku'])) {
            $query->where('sku', $input['sku']);
        }

        if (!empty($input['sku'])) {
            $query->where('sku', $input['sku']);
        }
        $products = $query->where('is_deleted', 0)
            ->primary()
            ->select('id', 'sku', 'style', 'color', 'size', 'name')
            ->get();
        $orderRules = SaleOrderAutomationRule::where('warehouse_id', $input['warehouse_id'] ?? null)->first();
        $listSKUs = [];

        if ($orderRules) {
            $listSKUs = json_decode($orderRules->rule, true)['listSku'] ?? [];
        }

        foreach ($products as $product) {
            if (count($listSKUs) == 0) {
                $product->status = false;
            } elseif (count($listSKUs) > 0) {
                foreach ($listSKUs as $item) {
                    if ($item['value'] == $product->sku) {
                        $product->status = $item['status'];
                    }
                }
            }
        }

        return $products;
    }

    public function fetchProducts($input)
    {
        $query = Product::where('is_deleted', 0)
            ->primary()
            ->select('id', 'sku', 'style', 'color', 'size', 'name');

        if (!empty($input['style'])) {
            $query->where('style', $input['style']);
        }

        if (!empty($input['color'])) {
            $query->where('color', $input['color']);
        }

        if (!empty($input['size'])) {
            $query->where('size', $input['size']);
        }

        if (!empty($input['sku'])) {
            $query->where('sku', $input['sku']);
        }

        if (!empty($input['product_id'])) {
            $query->where('id', $input['product_id']);
        }

        if (!empty($input['is_not_product_tiktok'])) {
            $query->whereDoesntHave('productTiktok');
        }

        return $query->get();
    }

    public function getProductByGTIN($input)
    {
        return Product::where('gtin', $input['gtin'] ?? '')
            ->where('is_deleted', 0)
            ->primary()
            ->first();
    }

    public function getProductBySku($input)
    {
        return Product::with('productSpec')
            ->where('sku', $input['sku'] ?? '')
            ->primary()
            ->first();
    }

    public function getProductBySkuOrLabel($input)
    {
        return Product::query()
            ->where(function ($query) use ($input) {
                $query->where('sku', $input['keyword'] ?? '')
                    ->orWhereHas('orderItems.barcodes', function ($q) use ($input) {
                        $q->where('label_id', $input['keyword'] ?? '');
                    });
            })
            ->primary()
            ->first();
    }

    public function getCatalogDetail($id)
    {
        $parentProduct = Product::select(['id', 'name', 'style', 'sku', 'size', 'color', 'brand_id', 'image'])
            ->with([
                'brand:id,name',
                'product_variants' => function ($query) {
                    $query->select('id', 'parent_id', 'sku', 'is_discontinued')
                        ->with(['productQuantities' => function ($q) {
                            $q->select('id', 'product_id', DB::raw('SUM(quantity + incoming_stock) as quantity'))
                            ->groupBy('product_id');
                        }]);
                },
                'productStyle:id,name,sku'
            ])
            ->where('parent_id', 0)
            ->where('is_hide', Product::NOT_HIDE)
            ->findOrFail($id);

        $parentProduct->color_info = ProductColor::whereIn('name', explode(',', $parentProduct->color))
            ->select('color_code', 'sku', 'name')
            ->get();

        $parentProduct->size_info = ProductSize::whereIn('name', explode(',', $parentProduct->size))
            ->select(['name', 'sku'])
            ->get();

        $parentProduct->image = env('AWS_S3_URL', '') . '/' . $parentProduct->image;

        return $parentProduct;
    }

    public static function fetchProductsGroupByKey($arrItems, $field = 'gtin')
    {
        return Product::query()->active()->whereIn($field, $arrItems)->get()->keyBy($field);
    }

    public function getInfo($input)
    {
        return Product::query()->active()
            ->select('style', 'size', 'color', 'sku')
            ->whereIn('sku', $input['sku'])
            ->get()
            ->toArray();
    }

    public function create($request)
    {
        $input = $request->all();

        if (count($input) <= 0) {
            return [
                'status' => 422,
                'data' => [
                    'message' => 'Invalid data'
                ]
            ];
        }

        $createdProducts = [];
        $failedProducts = [];
        $productIds = [];

        try {
            DB::beginTransaction();
            foreach ($input as $item) {
                if (empty($item['style']) || empty($item['color'])
                    || (isset($item['cost']) && floatval($item['cost']) < 0)
                    || empty($item['brand_id']) || empty($item['size']) || empty($item['weight_single']) || empty($item['weight_multiple'])
                    || !is_numeric($item['weight_single']) || floatval($item['weight_single']) <= 0
                    || !is_numeric($item['weight_multiple']) || floatval($item['weight_multiple']) <= 0
                ) {
                    $failedProducts[] = [
                        'item' => $item,
                        'error' => 'Invalid Data.'
                    ];

                    continue;
                }

                if (isset($item['cost']) && !empty($item['cost'])) {
                    $item['cost'] = number_format(floatval($item['cost']), 2, '.', '');
                } elseif (isset($item['cost']) && floatval($item['cost']) == 0) {
                    $item['cost'] = 0;
                } else {
                    $item['cost'] = null;
                }

                $style = ProductStyle::where('name', $item['style'])->first();
                $color = ProductColor::where('name', $item['color'])->first();
                $size = ProductSize::where('name', $item['size'])->first();

                if (!$style || !$color || !$size) {
                    $failedProducts[] = [
                        'item' => $item,
                        'error' => 'Incorrect information.'
                    ];

                    continue;
                }

                $item['parent_sku'] = $style->sku;
                $brand = Brand::find($item['brand_id']);
                if (!$brand) {
                    $failedProducts[] = [
                        'item' => $item,
                        'error' => 'Brand invalid'
                    ];

                    continue;
                }
                $item['brand_id'] = $brand->id;
                $parentProduct = $this->createOrUpdateParentProduct($item);
                $name = $item['style'] . ' / ' . $item['color'] . ' / ' . $item['size'];
                $sku = $style->sku . $color->sku . $size->sku;
                if (Product::where('sku', $sku)->exists()) {
                    $failedProducts[] = [
                        'item' => $item,
                        'error' => 'Duplicate product.'
                    ];

                    continue;
                }
                // check gtin
                if (!empty($item['gtin'])) {
                    $gtinExist = $this->scopeGtin($item['gtin']);
                    if ($gtinExist) {
                        $failedProducts[] = [
                            'item' => $item,
                            'error' => 'Invalid GTIN.'
                        ];

                        continue;
                    }
                }

                $data = [
                    'parent_id' => $parentProduct->id,
                    'name' => $name,
                    'sku' => $sku,
                    'user_id' => auth()->user()['id'],
                    'style' => $item['style'],
                    'color' => $item['color'],
                    'size' => $item['size'],
                    'brand_id' => $brand->id,
                    'gtin' => $item['gtin'] ?? null,
                    'gtin_case' => $item['gtin_case'],
                    'description' => $item['description'] ?? null,
                    'cost' => $item['cost'],
                    'weight_single' => $item['weight_single'],
                    'weight_multiple' => $item['weight_multiple']
                ];

                $product = Product::create($data);
                ProductHistory::create([
                    'user_id' => auth()->user()['id'],
                    'product_id' => $product->id,
                    'data' => json_encode($data),
                    'action' => ProductHistory::ACTION_CREATE
                ]);

                $productIds[] = $product->id;
                $createdProducts[] = $product;
            }

            DB::commit();

            if (!empty($failedProducts)) {
                return [
                    'status' => 422,
                    'data' => [
                        'message' => 'Some products could not be created.',
                        'created_products' => $createdProducts,
                        'failed_products' => $failedProducts
                    ]
                ];
            }

            return [
                'status' => 201,
                'data' => [
                    'message' => 'Create products successfully!',
                    'created_products' => $createdProducts,
                    'product_ids' => $productIds,
                ]
            ];
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'status' => 500,
                'data' => [
                    'message' => $exception->getMessage(),
                    'code' => $exception->getCode()
                ]
            ];
        }
    }

    public function scopeGtin($keyword)
    {
        $product = Product::whereNotNull('gtin')
            ->where('gtin', '<>', '')
            ->where('is_deleted', Product::NOT_DELETED)
            ->where(function ($q) use ($keyword) {
                $q->whereRaw('COALESCE(INSTR(?, gtin)) > 0', [$keyword])
                    ->orWhereRaw('COALESCE(INSTR(gtin, ?)) > 0', [$keyword]);
            });

        return $product->first();
    }

    public function validateVariantsCsv($file)
    {
        $data = Excel::toCollection(new Collection(), $file)->first();
        $invalidDataRows = [];
        $validData = [];
        $countDuplicate = 0;
        $fileHeading = ['Style', 'Color', 'Size', 'Brand', 'Description', 'GTIN', 'GTIN Case', 'Single weight', 'Multiple weight'];
        $productStyles = ProductStyle::select('id', 'name', 'sku')->get();
        $uppercaseProductStyles = $productStyles->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        $productColors = ProductColor::select('id', 'name', 'sku')->get();
        $uppercaseProductColors = $productColors->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        $productSizes = ProductSize::select('id', 'name', 'sku')->get();
        $uppercaseProductSizes = $productSizes->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        $productBrands = Brand::get()->pluck('id', 'name')
            ->mapWithKeys(function ($id, $name) {
                return [strtoupper($name) => $id];
            })
            ->toArray();

        $productGtinList = Product::distinct()->whereNotNull('gtin')->pluck('gtin');

        foreach ($data as $key => $row) {
            if ($key === 0) {
                if (!($row->toArray() === $fileHeading)) {
                    return [
                        'status' => false,
                        'message' => 'Import file does not match template.',
                        'countValid' => 0,
                        'countInvalid' => 0,
                        'invalid' => [],
                        'valid' => []
                    ];
                }

                continue;
            }

            $style = strtoupper(trim($row[0]));
            $color = strtoupper(trim($row[1]));
            $size = strtoupper(trim($row[2]));
            $brand = strtoupper(trim($row[3]));
            $description = trim($row[4]);
            $gtin = trim($row[5]);
            $gtinCase = trim($row[6]);
            $singleWeight = trim($row[7]);
            $multipleWeight = trim($row[8]);

            $isRowHasData = !empty($style) || !empty($color) || !empty($size) || !empty($brand)
                || !empty($description) || !empty($sku) || !empty($gtin) || !empty($gtinCase)
                || !empty($singleWeight) || !empty($multipleWeight);

            if (!$isRowHasData) {
                continue;
            }

            $reasonMsg = '';
            $isValidCheck = true;

            if (!empty($style) && !empty($color) && !empty($size) && isset($validData["$style-$color-$size"])) {
                $countDuplicate++;

                continue;
            }

            // Check style exists
            if (empty($style)) {
                $isValidCheck = false;
                $reasonMsg .= 'Style name is missing. ';
            } elseif (empty($uppercaseProductStyles->where('name', $style)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The style does not exist in the system. ';
            }

            // Check color exists
            if (empty($color)) {
                $isValidCheck = false;
                $reasonMsg .= 'Color name is missing. ';
            } elseif (empty($uppercaseProductColors->where('name', $color)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The color does not exist in the system. ';
            }

            // Check size exists
            if (empty($size)) {
                $isValidCheck = false;
                $reasonMsg .= 'Size name is missing. ';
            } elseif (empty($uppercaseProductSizes->where('name', $size)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The size does not exist in the system. ';
            }

            // Check single weight is required and is number
            if (empty($singleWeight)) {
                $isValidCheck = false;
                $reasonMsg .= 'Single weight is missing. ';
            } elseif (!is_numeric($singleWeight) || floatval($singleWeight) <= 0) {
                $isValidCheck = false;
                $reasonMsg .= 'Single weight must be a positive number. ';
            }

            // Check single weight is required and is number
            if (empty($multipleWeight)) {
                $isValidCheck = false;
                $reasonMsg .= 'Multiple weight is missing. ';
            } elseif (!is_numeric($multipleWeight) || floatval($multipleWeight) <= 0) {
                $isValidCheck = false;
                $reasonMsg .= 'Multiple weight must be a positive number. ';
            }

            $currentStyle = $uppercaseProductStyles->where('name', $style)->first();
            $currentColor = $uppercaseProductColors->where('name', $color)->first();
            $currentSize = $uppercaseProductSizes->where('name', $size)->first();

            if (!empty($style) && !empty($color) && !empty($size)
                && !empty($currentStyle) && !empty($currentColor) && !empty($currentSize)
            ) {
                $productSku = $currentStyle['sku'] . $currentColor['sku'] . $currentSize['sku'];
                $product = Product::where('sku', $productSku)->first();

                if (!empty($product)) {
                    $isValidCheck = false;
                    $reasonMsg .= 'This product SKU generated by style-color-size SKU already exists in the system. ';
                }
            }

            // Check if product by style-color-size exists in the system
            if ($isValidCheck) {
                $product = Product::where([
                    'style' => $style,
                    'color' => $color,
                    'size' => $size,
                ])->where('parent_id', '!=', 0)->first();

                if (!empty($product)) {
                    $isValidCheck = false;
                    $msg = 'This product already exists in the system. ';
                    $reasonMsg .= $msg;
                    $invalidDataRows[] = array_merge($row->toArray(), [$reasonMsg]);

                    continue;
                }
            }

            // Check Brand is required
            if (empty($brand)) {
                $isValidCheck = false;
                $reasonMsg .= 'Brand name is missing. ';
            } elseif (empty($productBrands[$brand])) {
                $isValidCheck = false;
                $reasonMsg .= 'The brand does not exist in the system. ';
            }

            // Check GTIN exists
            if (!empty($gtin) && $productGtinList->contains($gtin)) {
                $isValidCheck = false;
                $reasonMsg .= 'Gtin value exists. ';
            }

            // Check GTIN CASE is required
            if (empty($gtinCase)) {
                $isValidCheck = false;
                $reasonMsg .= 'GTIN Case is missing. ';
            } elseif (!is_int(intval($gtinCase))) {
                $isValidCheck = false;
                $reasonMsg .= 'Gtin case must be an integer number. ';
            }

            if (!$isValidCheck) {
                $invalidDataRows[] = array_merge($row->toArray(), [$reasonMsg]);

                continue;
            }

            if (!isset($validData["$style-$color-$size"])) {
                $productValues = [
                    'style' => strtoupper(trim($row[0])),
                    'color' => strtoupper(trim($row[1])),
                    'size' => strtoupper(trim($row[2])),
                    'brand_id' => $productBrands[strtoupper(trim($row[3]))],
                    'description' => trim($row[4]),
                    'sku' => $currentStyle['sku'] . $currentColor['sku'] . $currentSize['sku'],
                    'gtin' => trim($row[5]),
                    'gtin_case' => intval(trim($row[6])),
                    'parent_sku' => $currentStyle['sku'],
                    'weight_single' => floatval($singleWeight),
                    'weight_multiple' => floatval($multipleWeight),
                ];
                $validData["$style-$color-$size"] = $productValues;
            }
        }

        return [
            'countDuplicate' => $countDuplicate,
            'countValid' => count($validData),
            'countInvalid' => count($invalidDataRows),
            'valid' => $validData,
            'invalid' => $invalidDataRows,
        ];
    }

    public function importVariants($file)
    {
        $res = $this->validateVariantsCsv($file);

        if (isset($res['countInvalid']) && $res['countValid'] < 1) {
            return [
                'status' => false,
                'message' => 'No valid record to import.'
            ];
        }

        try {
            DB::beginTransaction();
            foreach ($res['valid'] as $record) {
                $parentProduct = $this->createOrUpdateParentProduct($record);
                $productStyle = ProductStyle::where('name', $record['style'])->first()?->name;
                $productColor = ProductColor::where('name', $record['color'])->first()?->name;
                $productSize = ProductSize::where('name', $record['size'])->first()?->name;
                $importData = [
                    'style' => $productStyle,
                    'color' => $productColor,
                    'size' => $productSize,
                    'name' => "$productStyle / $productColor / $productSize",
                    'user_id' => auth()->user()->id,
                    'brand_id' => $record['brand_id'],
                    'description' => $record['description'],
                    'sku' => $record['sku'],
                    'gtin' => !empty($record['gtin']) ? $record['gtin'] : null,
                    'gtin_case' => $record['gtin_case'],
                    'parent_id' => $parentProduct->id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'weight_single' => $record['weight_single'],
                    'weight_multiple' => $record['weight_multiple'],
                ];
                $product = Product::create($importData);
                ProductHistory::create([
                    'user_id' => auth()->user()['id'],
                    'product_id' => $product->id,
                    'data' => json_encode($importData),
                    'action' => ProductHistory::ACTION_CREATE
                ]);
            }

            DB::commit();

            return [
                'status' => true,
                'message' => 'Import product variants successfully.',
                'countValid' => $res['countValid'],
                'countInvalid' => $res['countInvalid'],
                'countDuplicate' => $res['countDuplicate'],
                's3LinkForInvalid' => '',
                // 's3LinkForInvalid' => $res['countInvalid'] > 0 ? env('AWS_S3_URL') . '/' . $filePath : ''
            ];
        } catch (Exception $e) {
            DB::rollBack();

            return [
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function validateWeightsCsv($file)
    {
        $data = Excel::toCollection(new Collection(), $file)->first();
        $invalidDataRows = [];
        $validData = [];
        $countDuplicate = 0;
        $fileHeading = ['Style', 'Size', 'Style+Size SKU', 'Multiple', 'Single'];

        $products = Product::select('id', 'name', 'style', 'size', 'parent_id')->get();
        $uppercaseProducts = $products->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        $productStyles = ProductStyle::select('id', 'name', 'sku')->get();
        $uppercaseProductStyles = $productStyles->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        $productSizes = ProductSize::select('id', 'name', 'sku')->get();
        $uppercaseProductSizes = $productSizes->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });

        foreach ($data as $key => $row) {
            if ($key === 0) {
                if (!($row->toArray() === $fileHeading)) {
                    return [
                        'status' => false,
                        'message' => 'Import file does not match template.'
                    ];
                }

                continue;
            }

            $style = strtoupper(trim($row[0]));
            $size = strtoupper(trim($row[1]));
            $styleSizeSku = strtoupper(trim($row[2]));
            $multiple = trim($row[3]);
            $single = trim($row[4]);

            $isRowHasData = !empty($style) || !empty($size) || !empty($styleSizeSku)
                || !empty($multiple) || !empty($single);

            if (!$isRowHasData) {
                continue;
            }

            $reasonMsg = '';
            $isValidCheck = true;

            if (!empty($style) && !empty($size) && isset($validData["$style-$size"])) {
                $countDuplicate++;

                continue;
            }

            // Check style exists
            if (empty($style)) {
                $isValidCheck = false;
                $reasonMsg .= 'Style name is missing. ';
            } elseif (empty($uppercaseProductStyles->where('name', $style)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The style does not exist in the system. ';
            }

            // Check style size exists
            if (empty($size)) {
                $isValidCheck = false;
                $reasonMsg .= 'Size name is missing. ';
            } elseif (empty($uppercaseProductSizes->where('name', $size)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The size does not exist in the system. ';
            }

            // Check styleSizeSku exists
            if (empty($styleSizeSku)) {
                $isValidCheck = false;
                $reasonMsg .= 'Style size SKU is missing. ';
            }

            // Check multiple value
            if (empty($multiple)) {
                $isValidCheck = false;
                $reasonMsg .= 'Weight for multiple is missing. ';
            } elseif (!is_numeric($multiple)) {
                $isValidCheck = false;
                $reasonMsg .= 'Weight for multiple must be a number. ';
            }

            // Check single value
            if (empty($single)) {
                $isValidCheck = false;
                $reasonMsg .= 'Weight for single is missing. ';
            } elseif (!is_numeric($single)) {
                $isValidCheck = false;
                $reasonMsg .= 'Weight for single must be a number. ';
            }

            if (!empty($style) && !empty($size)) {
                $product = $uppercaseProducts->where('style', $style)->where('size', $size)
                    ->where('parent_id', '!=', 0)->first();

                if (empty($product)) {
                    $isValidCheck = false;
                    $reasonMsg .= 'The product using this style and size is not found in the system. ';
                }
            }

            $currentStyle = $uppercaseProductStyles->where('name', $style)->first();
            $currentSize = $uppercaseProductSizes->where('name', $size)->first();

            if (!empty($style) && !empty($size) && !empty($currentStyle) && !empty($currentSize)) {
                $productStyleSizeSKU = $currentStyle['sku'] . $currentSize['sku'];

                if ($productStyleSizeSKU != $styleSizeSku) {
                    $isValidCheck = false;
                    $reasonMsg .= 'This product SKU generated by style-size SKU is not found in the system. ';
                }
            }

            if (!$isValidCheck) {
                $invalidDataRows[] = array_merge($row->toArray(), [$reasonMsg]);

                continue;
            }

            if (!isset($validData["$style-$size"])) {
                $productValues = [
                    'style' => strtoupper(trim($row[0])),
                    'size' => strtoupper(trim($row[1])),
                    'styleSizeSKU' => strtoupper(trim($row[2])),
                    'multiple' => trim($row[3]),
                    'single' => trim($row[4]),
                ];
                $validData["$style-$size"] = $productValues;
            }
        }

        return [
            'status' => true,
            'countDuplicate' => $countDuplicate,
            'countValid' => count($validData),
            'countInvalid' => count($invalidDataRows),
            'valid' => $validData,
            'invalid' => $invalidDataRows,
        ];
    }

    public function importWeights($file)
    {
        $res = $this->validateWeightsCsv($file);

        if (!$res['status']) {
            return [
                'status' => false,
                'message' => $res['message']
            ];
        } elseif ($res['countValid'] < 1) {
            return [
                'status' => false,
                'message' => 'No valid record to import.'
            ];
        }

        try {
            foreach ($res['valid'] as $record) {
                $products = Product::where([
                    'style' => $record['style'],
                    'size' => $record['size'],
                ])->where('parent_id', '!=', 0)->get();

                $updateData = [
                    'weight_single' => $record['single'],
                    'weight_multiple' => $record['multiple'],
                ];

                foreach ($products as $product) {
                    $product->weight_single = $record['single'];
                    $product->weight_multiple = $record['multiple'];
                    $product->save();
                    $data = array_merge([
                        'sku' => $product->sku,
                        'style' => $product->style,
                        'color' => $product->color,
                        'size' => $product->size,
                        'parent_id' => $product->parent_id,
                    ], $updateData);

                    ProductHistory::create([
                        'user_id' => auth()->user()->id,
                        'product_id' => $product->id,
                        'data' => json_encode($data),
                        'action' => ProductHistory::ACTION_UPDATE
                    ]);
                }
            }

            return [
                'status' => true,
                'message' => 'Import product weights by style and size successfully.',
                'countValid' => $res['countValid'],
                'countInvalid' => $res['countInvalid'],
                'countDuplicate' => $res['countDuplicate'],
            ];
        } catch (Exception $e) {
            return [
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getColorsByStyle($input)
    {
        $style = ProductStyle::where('sku', $input['style_sku'])->first();
        $productColors = Product::where([
            'style' => $style->name,
            'parent_id' => Product::PARENT_PRODUCT
        ])->first()?->color;

        return !empty($productColors) ? explode(',', $productColors) : [];
    }

    public function coverProductBlankCostByYear($year)
    {
        Product::select(
            'product.id',
            'product.sku',
            DB::raw('IFNULL(SUM(CASE WHEN purchase_order.id IS NOT NULL THEN purchase_order_item.price ELSE 0 END), 0) as total_price'),
            DB::raw('COUNT(purchase_order.id) AS total_po'),
        )->leftJoin('purchase_order_item', 'purchase_order_item.product_id', '=', 'product.id')
            ->leftJoin('purchase_order', function ($join) use ($year) {
                $join->on('purchase_order.id', '=', 'purchase_order_item.po_id')
                    ->whereIn('purchase_order.order_status', [PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::PARTIAL_RECEIVED_STATUS])
                    ->whereYear('purchase_order.order_date', $year);
            })
            ->groupBy('product.id')
            ->orderBy('product.id', 'ASC')
            ->chunk(100, function ($products) use ($year) {
                $productIds = $products->pluck('id');

                $dataProductInventory = Inventory::select(
                    'product_id',
                    DB::raw('(SUM(CASE WHEN direction = 0 THEN inventory.quantity ELSE 0 END) - SUM(CASE WHEN direction = 1  THEN inventory.quantity ELSE 0 END)) AS end_unit'),
                )
                    ->where('is_deleted', 0)
                    ->where('created_at', '>=', $year . '-01-01 00:00:00')
                    ->where('created_at', '<=', $year . '-12-31 23:59:59')
                    ->whereIn('product_id', $productIds)
                    ->groupBy('product_id')
                    ->get()
                    ->keyBy('product_id');

                $blankCostLastYears = ProductBlankCostYear::whereIn('product_id', $productIds)
                    ->where('year', $year - 1)
                    ->get()
                    ->keyBy('product_id');

                $dataInsert = [];
                foreach ($products as $product) {
                    $costLastYear = 0;
                    if (isset($blankCostLastYears[$product->id]) && $blankCostLastYears[$product->id]->end_unit > 0) {
                        $costLastYear = $blankCostLastYears[$product->id]->cost;
                    }

                    $totalPrice = $product->total_price + $costLastYear;
                    $totalPo = $costLastYear > 0 ? ($product->total_po + 1) : $product->total_po;

                    if ($totalPo != 0) {
                        $blankCost = round($totalPrice / $totalPo, 2);
                    } elseif (isset($blankCostLastYears[$product->id])) {
                        $blankCost = $blankCostLastYears[$product->id]->cost;
                    } else {
                        $cost2021 = Cost2021::where('sku', substr($product->sku, 0, 4))->first();
                        $blankCost = isset($cost2021) ? round($cost2021->cost, 2) : 0;
                    }

                    $dataInsert[] = [
                        'product_id' => $product->id,
                        'year' => $year,
                        'cost' => $blankCost,
                        'end_unit' => isset($dataProductInventory[$product->id]) ? $dataProductInventory[$product->id]->end_unit : 0,
                    ];
                }

                if (!empty($dataInsert)) {
                    ProductBlankCostYear::insert($dataInsert);
                }
            });
    }

    public function getProductHistoryBlankCostYear($productId)
    {
        $data = Product::with([
            'blankCostYear',
        ])
            ->where('id', $productId)
            ->first();

        $poItems = PurchaseOrderItem::select([
            'purchase_order.id',
            'purchase_order.po_number',
            'purchase_order.order_date',
            'purchase_order_item.price',
            'purchase_order_item.quantity',
            'purchase_order_item.total',
            DB::raw('YEAR(purchase_order.order_date) AS order_year')
        ])
            ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
            ->whereIn('purchase_order.order_status', [PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::PARTIAL_RECEIVED_STATUS])
            ->where('purchase_order_item.product_id', $productId)
            ->whereYear('purchase_order.order_date', '>=', 2022)
            ->orderBy('purchase_order.order_date', 'DESC')
            ->get()
            ->groupBy('order_year');

        $data['po_items'] = $poItems;

        return $data;
    }

    public function validationVariantsCsvManySheet($file)
    {
        $allSheets = Excel::toCollection(new Collection(), $file);
        if ($allSheets->count() != 2) {
            return [
                'status' => false,
                'message' => 'Import file does not match template.',
                'countValid' => 0,
                'countInvalid' => 0,
                'invalid' => [],
                'valid' => []
            ];
        }

        $dataSheetApparel = $allSheets[0];
        $dataSheetFabric = $allSheets[1];

        $resApparel = $this->validationSheetApparel($dataSheetApparel);
        if(!$resApparel['status']) {
            return $resApparel;
        }
        $resAccessories = $this->validationSheetFabric($dataSheetFabric, $resApparel);
        dd($resAccessories);

        return [
            'status' => true,
            'countValidProduct' => $resApparel['countValid'],
            'countInvalidProduct' => $resApparel['countInvalid'],
            'countDuplicateProduct' => $resApparel['countDuplicate'],
            'invalidProduct' => $resApparel['invalid'],
            'validProduct' => $resApparel['valid'],
            'countValidProductSpec' => $resAccessories['countValid'],
            'countInvalidProductSpec' => $resAccessories['countInvalid'],
            'countDuplicateProductSpec' => $resAccessories['countDuplicate'],
            'invalidProductSpec' => $resAccessories['invalid'],
            'validProductSpec' => $resAccessories['valid'],
            'statusImport' => ($resApparel['countInvalid'] + $resAccessories['countInvalid'] + $resApparel['countDuplicate'] + $resAccessories['countDuplicate']) ? false : true,
        ];
    }

    public function validationSheetApparel($data)
    {
        $invalidDataRows = [];
        $validData = [];
        $countDuplicate = 0;
        $styleIsHardGoods = [];
        $fileHeading = ['Style', 'Color', 'Size', 'Brand', 'Description', 'GTIN', 'GTIN Case', 'Single weight', 'Multiple weight'];
        $productStyles = ProductStyle::select('id', 'name', 'sku', 'type')->get();
        $productTypes = ProductType::all()->mapWithKeys(function ($item) {
            return [strtoupper($item->name) => $item->is_hard_goods];
        })->toArray();
        $uppercaseProductStyles = $productStyles->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });
        $productColors = ProductColor::select('id', 'name', 'sku')->get();
        $uppercaseProductColors = $productColors->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });
        $productSizes = ProductSize::select('id', 'name', 'sku')->get();
        $uppercaseProductSizes = $productSizes->map(function ($item) {
            return array_map('strtoupper', $item->toArray());
        });
        $productBrands = Brand::get()->pluck('id', 'name')
            ->mapWithKeys(function ($id, $name) {
                return [strtoupper($name) => $id];
            })
            ->toArray();
        $productGtinList = Product::distinct()->whereNotNull('gtin')->pluck('gtin');
        foreach ($data as $key => $row) {
            if ($key === 0) {
                if (!($row->toArray() === $fileHeading)) {
                    return [
                        'status' => false,
                        'message' => 'Import file does not match template.',
                        'countValid' => 0,
                        'countInvalid' => 0,
                        'invalid' => [],
                        'valid' => [],
                        'countDuplicate' => $countDuplicate,
                        'styleIsHardGoods' => $styleIsHardGoods,
                    ];
                }

                continue;
            }

            $style = strtoupper(trim($row[0]));
            $color = strtoupper(trim($row[1]));
            $size = strtoupper(trim($row[2]));
            $brand = strtoupper(trim($row[3]));
            $description = trim($row[4]);
            $gtin = trim($row[5]);
            $gtinCase = trim($row[6]);
            $singleWeight = trim($row[7]);
            $multipleWeight = trim($row[8]);

            $isRowHasData = !empty($style) || !empty($color) || !empty($size) || !empty($brand)
                || !empty($description) || !empty($sku) || !empty($gtin) || !empty($gtinCase)
                || !empty($singleWeight) || !empty($multipleWeight) || !empty($isHardGoods);

            if (!$isRowHasData) {
                continue;
            }

            $reasonMsg = '';
            $isValidCheck = true;

            if (!empty($style) && !empty($color) && !empty($size) && isset($validData["$style-$color-$size"])) {
                $countDuplicate++;

                continue;
            }

            // Check style exists
            if (empty($style)) {
                $isValidCheck = false;
                $reasonMsg .= 'Style name is missing. ';
            } elseif (empty($uppercaseProductStyles->where('name', $style)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The style does not exist in the system. ';
            }

            // Check color exists
            if (empty($color)) {
                $isValidCheck = false;
                $reasonMsg .= 'Color name is missing. ';
            } elseif (empty($uppercaseProductColors->where('name', $color)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The color does not exist in the system. ';
            }

            // Check size exists
            if (empty($size)) {
                $isValidCheck = false;
                $reasonMsg .= 'Size name is missing. ';
            } elseif (empty($uppercaseProductSizes->where('name', $size)->first())) {
                $isValidCheck = false;
                $reasonMsg .= 'The size does not exist in the system. ';
            }

            // Check single weight is required and is number
            if (empty($singleWeight)) {
                $isValidCheck = false;
                $reasonMsg .= 'Single weight is missing. ';
            } elseif (!is_numeric($singleWeight) || floatval($singleWeight) <= 0) {
                $isValidCheck = false;
                $reasonMsg .= 'Single weight must be a positive number. ';
            }

            // Check single weight is required and is number
            if (empty($multipleWeight)) {
                $isValidCheck = false;
                $reasonMsg .= 'Multiple weight is missing. ';
            } elseif (!is_numeric($multipleWeight) || floatval($multipleWeight) <= 0) {
                $isValidCheck = false;
                $reasonMsg .= 'Multiple weight must be a positive number. ';
            }

            $currentStyle = $uppercaseProductStyles->where('name', $style)->first();
            $currentColor = $uppercaseProductColors->where('name', $color)->first();
            $currentSize = $uppercaseProductSizes->where('name', $size)->first();

            if (!empty($style) && !empty($color) && !empty($size)
                && !empty($currentStyle) && !empty($currentColor) && !empty($currentSize)
            ) {
                $productSku = $currentStyle['sku'] . $currentColor['sku'] . $currentSize['sku'];
                $product = Product::where('sku', $productSku)->first();

                if (!empty($product)) {
                    $isValidCheck = false;
                    $reasonMsg .= 'This product SKU generated by style-color-size SKU already exists in the system. ';
                }
            }

            // Check if product by style-color-size exists in the system
            if ($isValidCheck) {
                $product = Product::where([
                    'style' => $style,
                    'color' => $color,
                    'size' => $size,
                ])->where('parent_id', '!=', 0)->first();

                if (!empty($product)) {
                    $isValidCheck = false;
                    $msg = 'This product already exists in the system. ';
                    $reasonMsg .= $msg;
                    $invalidDataRows[] = array_merge($row->toArray(), [$reasonMsg]);

                    continue;
                }
            }

            // Check Brand is required
            if (empty($brand)) {
                $isValidCheck = false;
                $reasonMsg .= 'Brand name is missing. ';
            } elseif (empty($productBrands[$brand])) {
                $isValidCheck = false;
                $reasonMsg .= 'The brand does not exist in the system. ';
            }

            // Check GTIN exists
            if (!empty($gtin) && $productGtinList->contains($gtin)) {
                $isValidCheck = false;
                $reasonMsg .= 'Gtin value exists. ';
            }

            // Check GTIN CASE is required
            if (empty($gtinCase)) {
                $isValidCheck = false;
                $reasonMsg .= 'GTIN Case is missing. ';
            } elseif (!is_int(intval($gtinCase))) {
                $isValidCheck = false;
                $reasonMsg .= 'Gtin case must be an integer number. ';
            }

            if (!$isValidCheck) {
                $invalidDataRows[] = array_merge($row->toArray(), [$reasonMsg]);

                continue;
            }

            if (!isset($validData["$style-$color-$size"])) {
                $productValues = [
                    'style' => strtoupper(trim($row[0])),
                    'color' => strtoupper(trim($row[1])),
                    'size' => strtoupper(trim($row[2])),
                    'brand_id' => $productBrands[strtoupper(trim($row[3]))],
                    'description' => trim($row[4]),
                    'sku' => $currentStyle['sku'] . $currentColor['sku'] . $currentSize['sku'],
                    'gtin' => trim($row[5]),
                    'gtin_case' => intval(trim($row[6])),
                    'parent_sku' => $currentStyle['sku'],
                    'weight_single' => floatval($singleWeight),
                    'weight_multiple' => floatval($multipleWeight),
                ];
                $validData["$style-$color-$size"] = $productValues;
            }

            if (!empty($currentStyle) && !isset($styleIsHardGoods[$style]) && isset($productTypes[$currentStyle['type']])) {
                $styleIsHardGoods[$style] = $productTypes[$currentStyle['type']];
            }
        }

        return [
            'countDuplicate' => $countDuplicate,
            'countValid' => count($validData),
            'countInvalid' => count($invalidDataRows),
            'valid' => $validData,
            'invalid' => $invalidDataRows,
            'styleIsHardGoods' => $styleIsHardGoods,
            'status' => true,
        ];
    }

    public function validationSheetFabric($data, $resApparel)
    {
        $data = $this->transformDataSheetFabric($data);
        $invalidData = [];
        $validData = [];
        $processedStyles = []; // Để theo dõi các style đã được xử lý
        $sheet1Styles = [];
        $countDuplicate = 0;

        // Tạo một tập hợp các style từ sheet 1
        if (isset($resApparel['styleIsHardGoods'])) {
            $sheet1Styles = array_keys($resApparel['styleIsHardGoods']);
        }

        // Đầu tiên, check duplicate trong chính $data (sheet 2)
        $dataStyles = [];
        foreach ($data as $key => $row) {
            $style = strtoupper(trim($row['STYLE']));
            $color = strtoupper(trim($row['COLOR']));
            if (empty($style) || empty($color)) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Invalid data.',
                ];
                continue;
            }

            // Check duplicate trong chính $data
            if (in_array($style, $dataStyles)) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Duplicate style in sheet 2.',
                ];
                $countDuplicate++;
                continue;
            }

            $dataStyles[] = $style;

            // Kiểm tra style có tồn tại trong sheet 1 không
            if (in_array($style, $sheet1Styles)) {
                // Chỉ xử lý khi style từ sheet 2 có trong sheet 1
                if (!isset($validData[$style])) {
                    $validData[$style] = $this->tranformData($row, $resApparel['valid']);
                } else {
                    $countDuplicate++;

                    continue;
                }
            } else {
                // Chỉ thêm lỗi một lần cho mỗi style
                if (!in_array($style, $processedStyles)) {
                    $invalidData[] = [
                        'row' => ['STYLE' => $style],
                        'reason' => 'Style từ sheet 2 không có trong sheet 1.',
                    ];
                    $processedStyles[] = $style;
                }
            }
        }

        // Kiểm tra các style apparel từ sheet 1 có trong sheet 2 không
        if (isset($resApparel['styleIsHardGoods'])) {
            // Thu thập tất cả các style từ sheet 2 (đảm bảo là mảng)
            $sheet2Styles = [];
            foreach ($data as $row) {
                if (!empty($row['STYLE'])) {
                    $sheet2Styles[] = strtoupper(trim($row['STYLE']));
                }
            }
            $sheet2Styles = array_unique($sheet2Styles);

            foreach ($resApparel['styleIsHardGoods'] as $style => $isHardGoods) {
                // Nếu không phải là hard good (tức là apparel) và không có trong sheet 2
                if (!$isHardGoods && !in_array($style, $sheet2Styles)) {
                    $invalidData[] = [
                        'row' => ['STYLE' => $style],
                        'reason' => 'Apparel style từ sheet 1 không có trong sheet 2.',
                    ];
                }
            }
        }

        return [
            'valid' => $validData,
            'invalid' => $invalidData,
            'countValid' => count($validData),
            'countInvalid' => count($invalidData),
            'countDuplicate' => $countDuplicate,
        ];
    }

    public function tranformData($data, $dataApparels)
    {
        if (empty($data['FABRIC CONTENT'])) {
            $data['FABRIC CONTENT'] = null;
        }
        $data = array_map(function ($value) {
            if (is_string($value) && in_array(strtolower($value), ['y', 'yes'])) {
                return 1;
            }
            if ($value === '') {
                return 0;
            }

            return $value;
        }, $data);
        $result = [];
        foreach ($dataApparels as $key => $dataApparel) {
            if ($dataApparel['style'] == $data['STYLE'] && $dataApparel['color'] == $data['COLOR'] && !isset($result[$key])) {
                $result[$key] = [
                    'product_id' => $dataApparel,
                    'fabric_content' => $data['FABRIC CONTENT'],
                    'is_cold_water_wash' => $data['MACHINE WASH IN COLD WATER'],
                    'is_warm_water_wash' => $data['MACHINE WASH IN WARM WATER'],
                    'is_non_chlorine_bleach' => $data['ONLY NON-CHLORINE BLEACH (WHEN NEEDED)'],
                    'is_tumble_dry_low' => $data['TUMBLE DRY LOW'],
                    'is_tumble_dry_medium' => $data['TUMBLE DRY MEDIUM'],
                    'is_low_iron' => $data['IRON AT LOW TEMPERATURE'],
                    'is_medium_iron' => $data['IRON AT MEDIUM TEMPERATURE'],
                    'not_iron' => $data['DO NOT IRON'],
                    'not_dryclean' => $data['DO NOT DRYCLEAN'],
                    'not_bleach' => $data['DO NOT BLEACH'],
                    'is_natural_dry' => $data['NATURAL DRYING'],
                    'is_line_to_dry' => $data['LINE TO DRY/HANG TO DRY'],
                    'is_air_dry' => $data['AIR DRY'],
                    'not_twist' => $data['DO NOT TWIST'],
                    'not_tumble_dry' => $data['DO NOT TUMBLE DRY'],
                    'is_hot_water_wash' => $data['MACHINE WASH IN HOT WATER'],
                    'any_bleach' => $data['ANY BLEACH (WHEN NEEDED)'],
                    'is_tumble_dry_high' => $data['TUMBLE DRY HIGH'],
                    'is_dry_flat' => $data['DRY FLAT'],
                    'is_dry_in_shade' => $data['DRY IN THE SHADE'],
                    'is_high_iron' => $data['IRON AT HIGH TEMPERATURE'],
                    'is_dry_cleanable' => $data['DRY CLEANABLE'],
                ];
            }
        }

        return $result;
    }

    public function transformDataSheetFabric($data)
    {
        $header = $data->first()->toArray();

        return $data->slice(1)->map(function ($row) use ($header) {
            return array_combine($header, $row->toArray());
        });
    }

    public function importVariantsCsvManySheet($request)
    {
        $res = $this->validationVariantsCsvManySheet($request);
        if (!$res['statusImport']) {
            return [
                'status' => false,
                'message' => 'No valid record to import.'
            ];
        }
        $validProductSpec = $res['validProductSpec'];

        $productSpecs = [];
        foreach ($validProductSpec as $group) {
            $productSpecs = array_merge($productSpecs, $group);
        }

        try {
            DB::beginTransaction();
            foreach ($productSpecs as $key => &$productSpec) {
                $record = $productSpec['product_id'];
                $parentProduct = $this->createOrUpdateParentProduct($record);
                $productStyle = ProductStyle::where('name', $record['style'])->first()?->name;
                $productColor = ProductColor::where('name', $record['color'])->first()?->name;
                $productSize = ProductSize::where('name', $record['size'])->first()?->name;
                $importData = [
                    'style' => $productStyle,
                    'color' => $productColor,
                    'size' => $productSize,
                    'name' => "$productStyle / $productColor / $productSize",
                    'user_id' => auth()->user()->id,
                    'brand_id' => $record['brand_id'],
                    'description' => $record['description'],
                    'sku' => $record['sku'],
                    'gtin' => !empty($record['gtin']) ? $record['gtin'] : null,
                    'gtin_case' => $record['gtin_case'],
                    'parent_id' => $parentProduct->id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'weight_single' => $record['weight_single'],
                    'weight_multiple' => $record['weight_multiple'],
                ];
                $product = Product::create($importData);
                ProductHistory::create([
                    'user_id' => auth()->user()['id'],
                    'product_id' => $product->id,
                    'data' => json_encode($importData),
                    'action' => ProductHistory::ACTION_CREATE
                ]);
                unset($productSpec['product_id']);
                $productSpec['product_id'] = $product->id;
            }
            $dataImportProductSpec = array_values($productSpecs);
            ProductSpec::upsert(
                $dataImportProductSpec,
                ['product_id'],
                [
                    'fabric_content',
                    'is_cold_water_wash',
                    'is_warm_water_wash',
                    'is_non_chlorine_bleach',
                    'is_tumble_dry_low',
                    'is_tumble_dry_medium',
                    'not_tumble_dry',
                    'is_low_iron',
                    'is_medium_iron',
                    'not_iron',
                    'not_dryclean',
                    'not_bleach',
                    'is_natural_dry',
                    'is_line_to_dry',
                    'is_air_dry',
                    'not_twist',
                    'is_hot_water_wash',
                    'any_bleach',
                    'is_tumble_dry_high',
                    'is_dry_flat',
                    'is_dry_in_shade',
                    'is_high_iron',
                    'is_dry_cleanable',
                ],
            );
            DB::commit();

            return [
                'status' => true,
                'message' => 'Import product variants successfully.',
            ];
        } catch (Exception $e) {
            DB::rollBack();

            return [
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function validateVariantsCsvProductSpec($request)
    {
        $data = (new FastExcel())->import($request->file('file'));
        $dataProduct = json_decode($request->input('data'), true);

        // Khởi tạo các mảng phân loại
        $validDataCsv = [];        // Valid từ CSV
        $validDataProduct = [];    // Valid từ Product Data
        $invalidDataCsv = [];      // Invalid từ CSV
        $invalidDataProduct = [];  // Invalid từ Product Data

        $productStyle = null;
        $productColor = [];
        $csvStyleColors = [];      // Lưu trữ style-color từ CSV
        $validCsvStyleColors = []; // Lưu trữ valid style-color từ CSV

        // Chuẩn bị dữ liệu từ product input
        $productStyles = [];
        $productColors = [];
        $productStyleColors = [];

        foreach ($dataProduct as &$product) {
            $product['parent_sku'] = $product['style'];
            $productStyles[] = $product['style'];
            $productColors[] = $product['color'];

            // Tạo style-color combination cho từng product
            $styleColor = $product['style'] . '_' . $product['color'];
            $productStyleColors[] = $styleColor;
        }

        $productStyles = array_unique($productStyles);
        $productColors = array_unique($productColors);
        $productStyleColors = array_unique($productStyleColors);

        // Kiểm tra CSV → Product Data: Mọi style/color trong CSV phải có trong product data
        foreach ($data as $key => $row) {
            if (empty($row['STYLE']) || empty($row['COLOR'])) {
                $invalidDataCsv[] = [
                    'row' => $row,
                    'reason' => 'Invalid data: Style and Color are required.',
                    'source' => 'csv'
                ];

                continue;
            }

            $csvStyleColor = $row['STYLE'] . '_' . $row['COLOR'];
            $csvStyleColors[] = $csvStyleColor;

            // Kiểm tra style-color combination có trong product data không
            if (!in_array($csvStyleColor, $productStyleColors)) {
                $reason = '';
                if (!in_array($row['STYLE'], $productStyles)) {
                    $reason .= "Style '{$row['STYLE']}' is not in the allowed styles: " . implode(', ', $productStyles) . '. ';
                }
                if (!in_array($row['COLOR'], $productColors)) {
                    $reason .= "Color '{$row['COLOR']}' is not in the allowed colors: " . implode(', ', $productColors) . '. ';
                }
                if (in_array($row['STYLE'], $productStyles) && in_array($row['COLOR'], $productColors)) {
                    $reason .= "Style-Color combination '{$row['STYLE']}-{$row['COLOR']}' does not exist in product data. ";
                }

                $invalidDataCsv[] = [
                    'row' => $row,
                    'reason' => trim($reason),
                    'source' => 'csv'
                ];

                continue;
            }

            // Kiểm tra duplicate trong CSV
            if (!isset($validDataCsv[$csvStyleColor])) {
                $validDataCsv[$csvStyleColor] = $this->tranformDataProductSpec($row, $dataProduct);
                $validCsvStyleColors[] = $csvStyleColor;
            } else {
                $invalidDataCsv[] = [
                    'row' => $row,
                    'reason' => 'Duplicate data in CSV.',
                    'source' => 'csv'
                ];

                continue;
            }
        }

        // Loại bỏ duplicate trong CSV style-colors
        $csvStyleColors = array_unique($csvStyleColors);
        $validCsvStyleColors = array_unique($validCsvStyleColors);

        // Kiểm tra Product Data → CSV: Phân loại valid/invalid cho product data
        foreach ($productStyleColors as $productStyleColor) {
            $parts = explode('_', $productStyleColor);
            $style = $parts[0];
            $color = $parts[1];

            if (in_array($productStyleColor, $validCsvStyleColors)) {
                // Product data này có trong CSV và valid
                $validDataProduct[$productStyleColor] = [
                    'style' => $style,
                    'color' => $color,
                    'source' => 'product_data',
                    'matched_in_csv' => true
                ];
            } else {
                // Product data này không có trong CSV hoặc CSV invalid
                $invalidDataProduct[] = [
                    'row' => ['STYLE' => $style, 'COLOR' => $color],
                    'reason' => "Product data contains Style '{$style}' and Color '{$color}' but this combination is missing in CSV file.",
                    'source' => 'product_data'
                ];
            }
        }

        // Tổng hợp tất cả valid và invalid
        $allValidData = array_merge($validDataCsv, $validDataProduct);
        $allInvalidData = array_merge($invalidDataCsv, $invalidDataProduct);

        $result = [
            // Valid data phân loại
            'valid' => $allValidData,                    // Tất cả valid (để tương thích với code cũ)
            'validCsv' => $validDataCsv,                // Valid từ CSV
            'validProduct' => $validDataProduct,        // Valid từ Product Data

            // Invalid data phân loại
            'invalid' => $allInvalidData,               // Tất cả invalid (để tương thích với code cũ)
            'invalidCsv' => $invalidDataCsv,           // Invalid từ CSV
            'invalidProduct' => $invalidDataProduct,    // Invalid từ Product Data

            // Count phân loại
            'countValid' => count($allValidData),              // Tổng số valid (để tương thích với code cũ)
            'countValidCsv' => count($validDataCsv),          // Số valid từ CSV
            'countValidProduct' => count($validDataProduct),   // Số valid từ Product Data

            'countInvalid' => count($allInvalidData),          // Tổng số invalid (để tương thích với code cũ)
            'countInvalidCsv' => count($invalidDataCsv),      // Số invalid từ CSV
            'countInvalidProduct' => count($invalidDataProduct), // Số invalid từ Product Data

            'status' => count($allInvalidData) > 0 ? false : true,
        ];

        return $result;
    }

    public function tranformDataProductSpec($data, $dataApparels)
    {
        if (empty($data['FABRIC CONTENT'])) {
            $data['FABRIC CONTENT'] = null;
        }

        $data = array_map(function ($value) {
            if (is_string($value) && in_array(strtolower($value), ['y', 'yes'])) {
                return 1;
            }
            if ($value === '') {
                return 0;
            }

            return $value;
        }, $data);

        foreach ($dataApparels as $key => $dataApparel) {
            if ($dataApparel['style'] == $data['STYLE'] && $dataApparel['color'] == $data['COLOR'] && !isset($result[$key])) {
                $result[$key] = [
                    'product_id' => $dataApparel,
                    'fabric_content' => $data['FABRIC CONTENT'],
                    'is_cold_water_wash' => $data['MACHINE WASH IN COLD WATER'],
                    'is_warm_water_wash' => $data['MACHINE WASH IN WARM WATER'],
                    'is_non_chlorine_bleach' => $data['ONLY NON-CHLORINE BLEACH (WHEN NEEDED)'],
                    'is_tumble_dry_low' => $data['TUMBLE DRY LOW'],
                    'is_tumble_dry_medium' => $data['TUMBLE DRY MEDIUM'],
                    'is_low_iron' => $data['IRON AT LOW TEMPERATURE'],
                    'is_medium_iron' => $data['IRON AT MEDIUM TEMPERATURE'],
                    'not_iron' => $data['DO NOT IRON'],
                    'not_dryclean' => $data['DO NOT DRYCLEAN'],
                    'not_bleach' => $data['DO NOT BLEACH'],
                    'is_natural_dry' => $data['NATURAL DRYING'],
                    'is_line_to_dry' => $data['LINE TO DRY/HANG TO DRY'],
                    'is_air_dry' => $data['AIR DRY'],
                    'not_twist' => $data['DO NOT TWIST'],
                    'not_tumble_dry' => $data['DO NOT TUMBLE DRY'],
                    'is_hot_water_wash' => $data['MACHINE WASH IN HOT WATER'],
                    'any_bleach' => $data['ANY BLEACH (WHEN NEEDED)'],
                    'is_tumble_dry_high' => $data['TUMBLE DRY HIGH'],
                    'is_dry_flat' => $data['DRY FLAT'],
                    'is_dry_in_shade' => $data['DRY IN THE SHADE'],
                    'is_high_iron' => $data['IRON AT HIGH TEMPERATURE'],
                    'is_dry_cleanable' => $data['DRY CLEANABLE'],
                ];
            }
        }

        return $result;
    }

    public function createProductWithSpec($request)
    {
        $res = $this->validateVariantsCsvProductSpec($request);
        if (!$res['status']) {
            return [
                'status' => false,
                'message' => 'No valid record to import.'
            ];
        }
        $validProductSpec = $res['invalidCsv'];
        $productSpecs = [];
        foreach ($validProductSpec as $group) {
            $productSpecs = array_merge($productSpecs, $group);
        }

        $failedProducts = [];
        foreach ($productSpecs as &$productSpec) {
            $item = $productSpec['product_id'];
            if (empty($item['style']) || empty($item['color'])
                || (isset($item['cost']) && floatval($item['cost']) < 0)
                || empty($item['brand_id']) || empty($item['size']) || empty($item['weight_single']) || empty($item['weight_multiple'])
                || !is_numeric($item['weight_single']) || floatval($item['weight_single']) <= 0
                || !is_numeric($item['weight_multiple']) || floatval($item['weight_multiple']) <= 0
            ) {
                $failedProducts[] = [
                    'item' => $item,
                    'error' => 'Invalid Data.'
                ];

                continue;
            }

            if (isset($item['cost']) && !empty($item['cost'])) {
                $item['cost'] = number_format(floatval($item['cost']), 2, '.', '');
            } elseif (isset($item['cost']) && floatval($item['cost']) == 0) {
                $item['cost'] = 0;
            } else {
                $item['cost'] = null;
            }
            $style = ProductStyle::where('name', $item['style'])->first();
            $color = ProductColor::where('name', $item['color'])->first();
            $size = ProductSize::where('name', $item['size'])->first();
            if (!$style || !$color || !$size) {
                $failedProducts[] = [
                    'item' => $item,
                    'error' => 'Incorrect information.'
                ];

                continue;
            }
            $productSpec['product_id']['parent_sku'] = $style->sku;
            $brand = Brand::find($item['brand_id']);
            if (!$brand) {
                $failedProducts[] = [
                    'item' => $item,
                    'error' => 'Brand invalid'
                ];

                continue;
            }
            $productSpec['product_id']['brand_id'] = $brand->id;
            $name = $item['style'] . ' / ' . $item['color'] . ' / ' . $item['size'];
            $sku = $style->sku . $color->sku . $size->sku;
            if (Product::where('sku', $sku)->exists()) {
                $failedProducts[] = [
                    'item' => $item,
                    'error' => 'Duplicate product.'
                ];

                continue;
            }
            // check gtin
            if (!empty($item['gtin'])) {
                $gtinExist = $this->scopeGtin($item['gtin']);
                if ($gtinExist) {
                    $failedProducts[] = [
                        'item' => $item,
                        'error' => 'Invalid GTIN.'
                    ];

                    continue;
                }
            }
            $productSpec['product_id']['name'] = $name;
        }
        if (count($failedProducts) > 1) {
            return [
                'status' => false,
                'data' => $failedProducts,
                'message' => 'No valid record to import.'
            ];
        }
        try {
            DB::beginTransaction();
            foreach ($productSpecs as $key => &$productSpec) {
                $record = $productSpec['product_id'];
                $parentProduct = $this->createOrUpdateParentProduct($record);
                $productStyle = ProductStyle::where('name', $record['style'])->first()?->name;
                $productColor = ProductColor::where('name', $record['color'])->first()?->name;
                $productSize = ProductSize::where('name', $record['size'])->first()?->name;
                $importData = [
                    'style' => $productStyle,
                    'color' => $productColor,
                    'size' => $productSize,
                    'name' => "$productStyle / $productColor / $productSize",
                    'user_id' => auth()->user()->id,
                    'brand_id' => $record['brand_id'],
                    'description' => $record['description'],
                    'sku' => $record['sku'],
                    'gtin' => !empty($record['gtin']) ? $record['gtin'] : null,
                    'gtin_case' => $record['gtin_case'],
                    'parent_id' => $parentProduct->id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'weight_single' => $record['weight_single'],
                    'weight_multiple' => $record['weight_multiple'],
                ];
                $product = Product::create($importData);
                ProductHistory::create([
                    'user_id' => auth()->user()['id'],
                    'product_id' => $product->id,
                    'data' => json_encode($importData),
                    'action' => ProductHistory::ACTION_CREATE
                ]);
                unset($productSpec['product_id']);
                $productSpec['product_id'] = $product->id;
            }
            $dataImportProductSpec = array_values($productSpecs);
            ProductSpec::upsert(
                $dataImportProductSpec,
                ['product_id'],
                [
                    'fabric_content',
                    'is_cold_water_wash',
                    'is_warm_water_wash',
                    'is_non_chlorine_bleach',
                    'is_tumble_dry_low',
                    'is_tumble_dry_medium',
                    'not_tumble_dry',
                    'is_low_iron',
                    'is_medium_iron',
                    'not_iron',
                    'not_dryclean',
                    'not_bleach',
                    'is_natural_dry',
                    'is_line_to_dry',
                    'is_air_dry',
                    'not_twist',
                    'is_hot_water_wash',
                    'any_bleach',
                    'is_tumble_dry_high',
                    'is_dry_flat',
                    'is_dry_in_shade',
                    'is_high_iron',
                    'is_dry_cleanable',
                ],
            );
            DB::commit();

            return [
                'status' => true,
                'message' => 'Import product variants successfully.',
            ];
        } catch (Exception $e) {
            DB::rollBack();

            return [
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
