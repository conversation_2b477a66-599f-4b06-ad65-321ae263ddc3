<?php

namespace App\Repositories;

use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderHistory;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyQuantity;
use App\Repositories\Contracts\SupplyPurchaseOrderRepositoryInterface;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SupplyPurchaseOrderRepository extends CommonRepository implements SupplyPurchaseOrderRepositoryInterface
{
    public function fetchAll($request)
    {
        $query = SupplyPurchaseOrder::with(['items', 'user:id,username', 'vendor:id,name'])
            ->withSum('items as total_price', 'price')
            ->withSum('items as total_quantity', 'quantity')
            ->withSum('items as total_quantity_onhand', 'quantity_onhand')
            ->search($request);

        if (!empty($request->get_total)) {
            return ['total' => $query->count()];
        }
        if (!empty($request->export)) {
            if (!empty($request['start_date']) && !empty($request['end_date'])) {
                $from = Carbon::parse($request['start_date'])->startOfDay();
                $to = Carbon::parse($request['end_date'])->endOfDay();
                $query->whereBetween('order_date', [$from, $to]);
            }

            return $query->with('items.supply')->get();
        }
        $results = $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
        $results->getCollection()->transform(function ($order) {
            $order->total_incoming = $order->order_status === SupplyPurchaseOrder::CANCELLED_STATUS || $order->order_status === SupplyPurchaseOrder::COMPLETED_STATUS
                ? 0
                : $order->total_quantity - $order->total_quantity_onhand;

            return $order;
        });

        return $results;
    }

    public function fetchSummary($request)
    {
        $query = SupplyPurchaseOrder::query()
            ->selectRaw("
                SUM(items.quantity) AS total_quantity,
                SUM(items.quantity_onhand) AS total_quantity_onhand,
                SUM(DISTINCT supply_purchase_order.total) AS amount,
                 SUM(
                    CASE 
                        WHEN supply_purchase_order.order_status NOT IN ('"
                        . SupplyPurchaseOrder::CANCELLED_STATUS . "', '"
                        . SupplyPurchaseOrder::COMPLETED_STATUS . "')
                        THEN (COALESCE(items.quantity, 0) - COALESCE(items.quantity_onhand, 0))
                        ELSE 0
                    END
                ) AS total_incoming
            ", )
            ->join('supply_purchase_order_item as items', 'items.po_id', '=', 'supply_purchase_order.id')
            ->search($request);

        $result = $query->first();

        return [
            'total_quantity' => round($result->total_quantity ?? 0, 2),
            'total_quantity_onhand' => round($result->total_quantity_onhand ?? 0, 2),
            'total_incoming' => round($result->total_incoming ?? 0, 2),
            'amount' => round($result->amount ?? 0, 2),
        ];
    }

    public function create($request)
    {
        DB::beginTransaction();
        try {
            $supplyPurchaseOrder = new SupplyPurchaseOrder();
            $supplyPurchaseOrder->warehouse_id = config('jwt.warehouse_id');
            $supplyPurchaseOrder->user_id = auth()->user()['id'];
            $this->extracted($request, $supplyPurchaseOrder);
            foreach ($request->items as $item) {
                $supplyPurchaseOrderItem = new SupplyPurchaseOrderItem();
                $supplyPurchaseOrderItem->po_id = $supplyPurchaseOrder->id;
                $supplyPurchaseOrderItem->supply_id = $item['supply_id'];
                $supplyPurchaseOrderItem->quantity = $item['quantity'];
                $supplyPurchaseOrderItem->price = $item['price'];
                $supplyPurchaseOrderItem->total = $item['total'];
                $supplyPurchaseOrderItem->save();
                handleJob(SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB, [
                    'supply_id' => $item['supply_id'],
                    'warehouse_id' => config('jwt.warehouse_id'),
                ]);
            }
            $history = new SupplyPurchaseOrderHistory();
            $history->po_id = $supplyPurchaseOrder->id;
            $history->user_id = $supplyPurchaseOrder->user_id;
            $history->type = SupplyPurchaseOrderHistory::TYPE_CREATE;
            $history->save();
            DB::commit();

            return response()->json(['message' => 'Supply purchase order created successfully.'], 201);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json(['message' => 'Failed to create supply purchase order.'], 500);
        }
    }

    public function fetch($id)
    {
        return SupplyPurchaseOrder::with(['items.supply.unit', 'user:id,username', 'vendor:id,name'])
            ->with(['history' => function ($query) {
                $query->with('user:id,username')
                    ->orderBy('created_at', 'desc');
            }])
            ->find($id);
    }

    public function update($id, $request)
    {
        DB::beginTransaction();
        try {
            $supplyPurchaseOrder = SupplyPurchaseOrder::findOrFail($id);
            if ($supplyPurchaseOrder->order_status == SupplyPurchaseOrder::COMPLETED_STATUS) {
                return response()->json([
                    'po_number' => ['The purchase order cannot be updated!']
                ], 422);
            }
            $arrItemIdOld = $supplyPurchaseOrder->items->pluck('id')->toArray();
            $this->extracted($request, $supplyPurchaseOrder);

            $itemsData = [];
            foreach ($request->items as $item) {
                $itemsData[] = [
                    'po_id' => $supplyPurchaseOrder->id,
                    'supply_id' => $item['supply_id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['total'],
                    'id' => $item['id'] ?? 0,
                ];
            }
            $arrIdDeleted = array_diff($arrItemIdOld, array_column($itemsData, 'id'));

            if (!empty($arrIdDeleted)) {
                $deletedItems = SupplyPurchaseOrderItem::whereIn('id', $arrIdDeleted)->get();
                foreach ($deletedItems as $deletedItem) {
                    $supplyQuantity = SupplyQuantity::query()
                        ->where('supply_id', $deletedItem->supply_id)
                        ->where('warehouse_id', config('jwt.warehouse_id'))
                        ->first();
                    if (!empty($supplyQuantity)) {
                        handleJob(SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB, [
                            'supply_id' => $deletedItem->supply_id,
                            'warehouse_id' => config('jwt.warehouse_id'),
                        ]);
                    }
                }
                SupplyPurchaseOrderItem::whereIn('id', $arrIdDeleted)->delete();
            }

            foreach ($itemsData as $orderItem) {
                // Find or create a supply quantity record
                handleJob(SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB, [
                    'supply_id' => $orderItem['supply_id'] ?? null,
                    'warehouse_id' => config('jwt.warehouse_id'),
                ]);
            }
            SupplyPurchaseOrderItem::upsert($itemsData, ['id'], ['quantity', 'price', 'total']);
            $history = new SupplyPurchaseOrderHistory();
            $history->po_id = $supplyPurchaseOrder->id;
            $history->user_id = auth()->user()['id'];
            $history->type = SupplyPurchaseOrderHistory::TYPE_UPDATE;
            $history->save();
            DB::commit();

            return response()->json(['message' => 'Supply purchase order updated successfully.']);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json(['message' => 'Failed to create supply purchase order.'], 500);
        }
    }

    public function extracted($request, $supplyPurchaseOrder): void
    {
        $fillableFields = $supplyPurchaseOrder->getFillable();
        $supplyPurchaseOrder->fill($request->only($fillableFields));
        $supplyPurchaseOrder->save();
    }

    public function delete($id)
    {
        return SupplyPurchaseOrder::where('id', $id)->delete();
    }

    public function updateOnlyField($request)
    {
        try {
            DB::beginTransaction();
            $supplyPurchaseOrder = SupplyPurchaseOrder::find($request->id);
            $oldValue = $supplyPurchaseOrder->{$request->field};
            if ($request->field == 'order_status') {
                $orderStatus = $supplyPurchaseOrder->order_status;
                $incomingStockModifier = 0;

                if ($orderStatus == SupplyPurchaseOrder::CANCELLED_STATUS && $request->value != SupplyPurchaseOrder::COMPLETED_STATUS) {
                    $incomingStockModifier = 1;
                } elseif (in_array($request->value, [SupplyPurchaseOrder::COMPLETED_STATUS, SupplyPurchaseOrder::CANCELLED_STATUS]) && $orderStatus != SupplyPurchaseOrder::CANCELLED_STATUS) {
                    $incomingStockModifier = -1;
                }
                if ($incomingStockModifier != 0) {
                    $orderItems = SupplyPurchaseOrderItem::query()->where('po_id', $request->id)->get();
                    foreach ($orderItems as $orderItem) {
                        handleJob(SupplyQuantity::UPDATE_SUPPLY_INCOMING_QUANTITY_JOB, [
                            'supply_id' => $orderItem->supply_id,
                            'warehouse_id' => config('jwt.warehouse_id'),
                        ]);
                    }
                }
            }
            $supplyPurchaseOrder->{$request->field} = $request->value;
            $this->extracted($request, $supplyPurchaseOrder);
            $history = new SupplyPurchaseOrderHistory();
            $history->po_id = $supplyPurchaseOrder->id;
            $history->user_id = auth()->user()['id'];
            $history->type = 'update';
            $history->message = "Update Purchase Order : $request->field from $oldValue to $request->value";
            $history->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['message' => 'Failed to create supply purchase order.'], 500);
        }

        return response()->json(['message' => 'Supply purchase order updated successfully.']);
    }

    public function addMessage($request)
    {
        $comment = new SupplyPurchaseOrderHistory();
        $comment->po_id = $request->po_id;
        $comment->user_id = auth()->user()['id'];
        $comment->type = SupplyPurchaseOrderHistory::TYPE_COMMENT;
        $comment->message = $request->message;
        $comment->save();

        return $comment;
    }

    public function getPurchaseOrderBySupply($endDate, $supplyId, $warehouseId)
    {
        return SupplyPurchaseOrderItem::select('supply_id', 'quantity', 'price', 'po_id')
            ->whereHas('purchaseOrder', function ($query) use ($endDate, $warehouseId) {
                $query->withoutGlobalScope('warehouse');
                $query->where('order_date', '<=', $endDate)
                    ->where('order_status', SupplyPurchaseOrder::COMPLETED_STATUS)
                    ->where('warehouse_id', $warehouseId);
            })
            ->where('supply_id', $supplyId)
            ->orderBy('po_id', 'desc')->get();
    }

    public function getOldestSupplyPrice($supplyId, $warehouseId)
    {
        return SupplyPurchaseOrderItem::select('supply_id', 'quantity', 'price', 'po_id')
            ->whereHas('purchaseOrder', function ($query) use ($warehouseId) {
                $query->withoutGlobalScope('warehouse');
                $query->where('order_status', SupplyPurchaseOrder::COMPLETED_STATUS)
                    ->where('warehouse_id', '=', $warehouseId)
                    ->orderBy('order_date', 'asc');
            })
            ->where('supply_id', $supplyId)
            ->first()?->price;
    }
}
