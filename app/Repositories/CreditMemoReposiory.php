<?php

namespace App\Repositories;

use App\Models\CreditMemo;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class CreditMemoReposiory extends CommonRepository
{
    public function fetcthList($input)
    {
        setTimezone();
        $query = CreditMemo::with([
            'store' => function ($q) {
                $q->select('id', 'name', 'code', 'client_id');
            },
            'store.client' => function ($q) {
                $q->select('id', 'name');
            },
            'invoice',
            'attachments',
        ]);

        $storeId = $request['store_id'] ?? null;

        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        if (!empty($input['name'])) {
            $query->where('name', 'like', '%' . $input['name'] . '%');
        }

        if (!empty($input['client_id'])) {
            $query->whereHas('store', function ($q) use ($input) {
                $q->where('client_id', $input['client_id']);
            });
        }

        if (!empty($input['start_date'])) {
            $startDate = Carbon::parse($input['start_date'])->startOfDay()->toDateTimeString();
            $query->where('created_at', '>=', $startDate);
        }

        if (!empty($input['end_date'])) {
            $endDate = Carbon::parse($input['end_date'])->endOfDay()->toDateTimeString();
            $query->where('created_at', '<=', $endDate);
        }

        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        }

        $query->orderBy($input['sort_by'] ?? 'id', $input['sort_order'] ?? 'desc');

        if (empty($input['limit'])) {
            return $query->get();
        }

        return $query->paginate($input['limit'] ?? self::LIMIT);
    }

    public function readMemoPdfFile($file)
    {
        $geminiSetting = Setting::where('label', Setting::READ_LABEL_SHIPMENT_JSON)->first();

        if (!$geminiSetting) {
            return response()->json(['message' => 'Gemini setting not found'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $setting = json_decode($geminiSetting->value, true);
        $geminiUrl = $setting['gemini']['url'] ?? '';
        $geminiKey = $setting['gemini']['key'] ?? '';
        $prompt = '
            Extract data from the credit memo:
                1.  Credit Memo Number (Credit #)
                2.  Credit Memo Date (use the "DATE" label, format as MM/DD/YYYY)
                3.  Total Credit Amount (numerical value, no currency symbol)

            Return the results with "name" as "CREDIT MEMO #[MEMO NUMBER] - [DATE]".';
        $pdfContent = file_get_contents($file->getRealPath());
        $base64Pdf = base64_encode($pdfContent);

        $responseSchemaProperties = [
            'name' => ['type' => 'string'],
            'amount' => ['type' => 'number'],
        ];

        $payload = [
            'generation_config' => [
                'temperature' => 0.7,
                'top_p' => 0.95,
                'top_k' => 40,
                'max_output_tokens' => 2048,
                'response_schema' => [
                    'type' => 'object',
                    'properties' => $responseSchemaProperties,
                ],
                'response_mime_type' => 'application/json'
            ],
            'contents' => [
                [
                    'parts' => [
                        [
                            'inlineData' => [
                                'mimeType' => 'application/pdf',
                                'data' => $base64Pdf,
                            ]
                        ],
                        [
                            'text' => $prompt,
                        ]
                    ]
                ]
            ]
        ];
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post("$geminiUrl?key=$geminiKey", $payload);

        if ($response->failed()) {
            $this->errorResponse('Cannot get file content, please try again', Response::HTTP_BAD_REQUEST);
        }

        $result = $response->json();
        $rawData = $result['candidates'][0]['content']['parts'][0]['text'] ?? '';

        if (empty($rawData)) {
            $this->errorResponse('Cannot read file content, please try again', Response::HTTP_BAD_REQUEST);
        }

        return response()->json(json_decode($rawData, true));
    }

    public function createMemo($input)
    {
        $creditMemo = CreditMemo::create([
            'store_id' => $input['store_id'],
            'name' => $input['name'],
            'amount' => $input['amount'],
            'status' => CreditMemo::STATUS_AVAILABLE,
        ]);

        foreach ($input->file('attachments') as $file) {
            $filename = uniqid() . '-' . $file->getClientOriginalName();
            $path = Storage::disk('s3')->putFileAs(CreditMemo::CREDIT_MEMO_PATH, $file, $filename);
            $creditMemo->attachments()->create([
                'name' => $file->getClientOriginalName(),
                'url' => Storage::disk('s3')->url($path),
            ]);
        }

        return $this->successResponse('Credit memo created successfully', $creditMemo);
    }
}
