<?php

namespace App\Repositories;

use App\Http\Service\AlertService;
use App\Models\Box;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderBox;
use App\Models\PurchaseOrderBoxItem;
use App\Models\PurchaseOrderItem;
use App\Models\StockTransfer;
use App\Models\StockTransferBoxLog;
use App\Models\StockTransferItem;
use App\Models\Vendor;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockTransferRepository extends CommonRepository
{
    public function fetchAll($request)
    {
        return StockTransfer::search($request)
            ->with([
                'employee:id,code,name',
                'warehouseDestination:id,code,name',
                'employeeFulfill:id,code,name',
                'fulfillLog',
                'fromWarehouse:id,name',
                'items.product:id,name,sku',
            ])
            ->orderByRaw("CASE WHEN status = 'partial_completed' THEN 0 ELSE 1 END")
            ->orderBy('updated_at', 'desc')
            ->paginate($request['limit'] ?? self::LIMIT);
    }

    public function create($request)
    {
        DB::beginTransaction();

        try {
            $inValidProductIds = Product::primary()
                ->where('is_deleted', Product::DELETED)
                ->pluck('id');
            $productIds = array_column($request->items, 'product_id');
            $queryTotalBox = Box::whereIn('product_id', $productIds)
                ->whereHas('location', function ($q) {
                    $q->where('type', Location::RACK);
                })
                ->where('warehouse_id', $request->from_warehouse_id)
                ->where('is_deleted', Box::NOT_DELETED)
                ->selectRaw('product_id,  COUNT(*) as total_box')
                ->groupBy('product_id')
                ->get()
                ->keyBy('product_id');

            foreach ($request->items as $key => $item) {
                $productId = $item['product_id'];

                if ($inValidProductIds->contains($productId)) {
                    $product = Product::find($productId);
                    $errors["items.{$key}.product_id"] = ["Invalid product sku: {$product->sku}"];
                } else {
                    $boxQuantity = $queryTotalBox->has($productId) ? $queryTotalBox[$productId]->total_box : 0;

                    if ($item['request_box'] > $boxQuantity) {
                        $product = Product::find($productId);
                        $errors["items.{$key}.request_box"] = ["Not enough boxes in inventory for SKU: {$product->sku}"];
                    }
                }
            }

            if (isset($errors)) {
                DB::rollBack();

                return response()->json($errors, 422);
            }

            $requestNumber = $this->generateRequestNumber($request->from_warehouse_id);
            $stockTransfer = StockTransfer::create([
                'employee_id' => $request->employee_id,
                'destination_warehouse_id' => $request->destination_warehouse_id,
                'from_warehouse_id' => $request->from_warehouse_id,
                'request_number' => $requestNumber,
                'status' => StockTransfer::PENDING_STATUS,
                'total_box' => array_sum(array_column($request->items, 'request_box')),
                'total_quantity' => array_sum(array_column($request->items, 'quantity')),
            ]);

            foreach ($request->items as $item) {
                StockTransferItem::create([
                    'stock_transfer_id' => $stockTransfer->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'request_box' => $item['request_box'],
                ]);
            }

            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $request->id_time_checking);

            DB::commit();

            return response()->json(['message' => 'Stock transfer request has been successfully created']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('StockTransferRepository.create', [
                'request' => $request,
                'exception' => $e,
            ]);

            return response()->json(['message' => 'Failed to create stock transfer'], 500);
        }
    }

    public function generateRequestNumber($warehouseId)
    {
        $fromWarehouse = Warehouse::find($warehouseId);
        $stockTransferCount = StockTransfer::where('from_warehouse_id', $fromWarehouse->id)->count();
        $mmdd = Carbon::now()->format('md');

        return "{$fromWarehouse->code}{$mmdd}-" . ($stockTransferCount + 1);
    }

    public function fetchDetail($stockTransferId)
    {
        return StockTransfer::with([
            'warehouseDestination:id,name',
            'fromWarehouse:id,name',
            'items.product:id,name,sku',
            'fulfillLog',
        ])->find($stockTransferId);
    }

    public function doScanBox($request)
    {
        $box = Box::with('location')
            ->where('is_deleted', Box::NOT_DELETED)
            ->where('barcode', $request->box_number)
            ->first();

        if (!$box) {
            $errors = ['box_number' => ['Box ID is not exists']];
        } elseif ($box->warehouse_id !== $request->warehouse_id) {
            $errors = ['box_number' => ['Invalid Box ID']];
        } elseif ($box->location->type === Location::PULLING_SHELVES) {
            $errors['box_number'] = ["The box {$box->barcode} on the pulling shelves cannot be fulfilled."];
        } elseif ($box->location->type === Location::PENDING_TYPE) {
            $errors['box_number'] = ["The box {$box->barcode} cannot be fulfilled due to awaiting confirmation of an internal request."];
        } else {
            $stockTransferItemData = StockTransferItem::where('stock_transfer_id', $request->stock_transfer_id)->get();
            $stockTransferProductIds = $stockTransferItemData->pluck('product_id');

            if (!$stockTransferProductIds->contains($box->product_id)) {
                $errors = ['box_number' => ['The box does not contain the requested product SKU']];
            }
        }

        if (isset($errors)) {
            return response()->json($errors, 422);
        }

        return $box;
    }

    public function fulfill($request)
    {
        $boxData = [];
        $stockTransfer = StockTransfer::with(['items', 'fromWarehouse', 'warehouseDestination'])
            ->where('from_warehouse_id', $request->warehouse_id)
            ->where('id', $request->stock_transfer_id)
            ->first();
        $errors = [];

        if (!$stockTransfer) {
            $errors['stock_transfer_id'] = ['Stock transfer not exist'];
        } elseif (!in_array($stockTransfer->status, [StockTransfer::PENDING_STATUS, StockTransfer::PARTIAL_COMPLETED_STATUS])) {
            $errors['stock_transfer_id'] = ['Invalid Stock transfer'];
        } else {
            if ($stockTransfer->destination_warehouse_id == Warehouse::WAREHOUSE_MEXICO_ID) {
                if (empty($request->coo)) {
                    $errors['coo'] = ['Mexico warehouse required COO'];
                }
            }

            $boxData = Box::with('location')
                ->where('warehouse_id', $stockTransfer->from_warehouse_id)
                ->whereIn('barcode', $request->box_scanned)
                ->select('id', 'barcode', 'product_id', 'location_id', 'quantity', 'is_deleted')
                ->get();
            $allBoxBarcodes = $boxData->pluck('barcode');

            foreach ($request->box_scanned as $key => $boxBarcode) {
                $box = $boxData->firstWhere('barcode', $boxBarcode);

                if (!$allBoxBarcodes->contains($boxBarcode)) {
                    $errors["box_scanned.$key"] = ["Box ID {$boxBarcode} is not exist"];
                } elseif ($box->is_deleted === Box::DELETED) {
                    $errors["box_scanned.$key"] = ["Invalid box ID {$boxBarcode}"];
                } elseif (!$stockTransfer->items->pluck('product_id')->contains($box->product_id)) {
                    $errors["box_scanned.$key"] = ["Product in the box ID {$boxBarcode} does not match the requested product."];
                } elseif ($box->location->type === Location::PULLING_SHELVES) {
                    $errors["box_scanned.$key"] = ["The box {$boxBarcode} on the pulling shelves cannot be fulfilled."];
                } elseif ($box->location->type === Location::PENDING_TYPE) {
                    $errors["box_scanned.$key"] = ["The box {$boxBarcode} cannot be fulfilled due to awaiting confirmation of an internal request."];
                }
            }
        }

        if (!empty($errors)) {
            return response()->json($errors, 422);
        }

        [$insertData, $deleteData] = $this->prepareDataForStockTransfer($stockTransfer, $boxData);

        if (empty($insertData)) {
            return response()->json(['box_scanned' => ['Invalid box data']], 422);
        }

        $status = $this->saveFulfill($stockTransfer, $boxData, $insertData, $deleteData, $request->id_time_checking, $request->coo ?? [], $request->employee_id);

        if ($status) {
            return response()->json(['message' => 'Fulfill has been successfully completed']);
        }

        return response()->json(['message' => 'Failed to create fulfill'], 500);
    }

    private function saveFulfill($stockTransfer, $boxData, $insertData, $deleteData, $idTimeChecking, $coo, $employeeId)
    {
        DB::beginTransaction();

        try {
            $warehouseCode = $stockTransfer->fromWarehouse->code;
            $warehouseName = $stockTransfer->warehouseDestination->name;
            $vendor = Vendor::where('name', 'SwiftPOD ' . $warehouseCode)->first();
            $po = PurchaseOrder::create([
                'warehouse_id' => $stockTransfer->destination_warehouse_id,
                'po_number' => $stockTransfer->request_number,
                'order_number' => $stockTransfer->request_number,
                'invoice_number' => $stockTransfer->request_number,
                'vendor_id' => $vendor ? $vendor->id : '',
                'order_status' => PurchaseOrder::NEW_ORDER_STATUS,
                'is_fulfill' => PurchaseOrder::IS_FULFILL,
                'created_at' => now(),
                'updated_at' => now(),
                'order_date' => now(),
            ]);
            $totalPrice = 0;
            $totalQuantityStockTransfer = 0;
            $totalBoxStockTransfer = 0;
            StockTransferBoxLog::where('stock_transfer_id', $stockTransfer->id)->delete();

            foreach ($insertData['items'] as $itemInsert) {
                $poItem = PurchaseOrderItem::create([
                    'po_id' => $po->id,
                    'product_id' => $itemInsert['product_id'],
                    'quantity' => array_sum(array_column($itemInsert['requested_boxes'], 'quantity')),
                ]);
                $poItemsExist = PurchaseOrderItem::where('product_id', $itemInsert['product_id'])
                    ->whereNotNull('price')
                    ->where('price', '>', 0)
                    ->orderBy('id', 'desc')
                    ->first();

                if ($poItemsExist) {
                    $poItem->price = $poItemsExist->price ?? 0;
                    $poItem->total = $poItem->price * $poItem->quantity;
                    $totalPrice += $poItem->total;
                    $poItem->save();
                }

                $product = Product::find($itemInsert['product_id']);

                foreach ($itemInsert['requested_boxes'] as $itemBox) {
                    $collection = collect($coo);
                    $find = $collection->first(function ($value, $key) use ($itemBox) {
                        return isset($value[$itemBox['barcode']]);
                    });
                    $poBox = PurchaseOrderBox::create([
                        'po_id' => $po->id,
                        'box_number' => $itemBox['barcode'],
                        'invoice_number' => $po->invoice_number,
                        'tracking_number' => $itemBox['barcode'],
                        'coo_id' => isset($find) ? reset($find) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    PurchaseOrderBoxItem::create([
                        'po_id' => $po->id,
                        'po_box_id' => $poBox->id,
                        'product_id' => $product->id,
                        'sku' => $product->sku,
                        'gtin' => $product->gtin,
                        'quantity' => $itemBox['quantity'],
                        'coo_id' => isset($find) ? reset($find) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    StockTransferBoxLog::create([
                        'stock_transfer_id' => $stockTransfer->id,
                        'stock_transfer_item_id' => $itemInsert['stock_transfer_item_id'],
                        'box_id' => $itemBox['id'],
                        'box_number' => $itemBox['barcode'],
                        'product_id' => $product->id,
                        'quantity' => $itemBox['quantity'],
                        'coo_id' => isset($find) ? reset($find) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                        'employee_id' => $employeeId,
                    ]);
                }

                ///Todo : cong incoming cua product o kho dich
                ProductQuantityRepository::updateInComeQuantity($stockTransfer->destination_warehouse_id, $product->id, $poItem->quantity);
                ///Todo : tru quantity cua product o kho nguon
                $productQuantityInFromWarehouse = ProductQuantity::where('product_id', $product->id)
                    ->where('warehouse_id', $stockTransfer->from_warehouse_id)
                    ->first();

                if ($productQuantityInFromWarehouse) {
                    $productQuantityInFromWarehouse->quantity -= $poItem->quantity;
                    $productQuantityInFromWarehouse->save();
                }

                $stockItem = StockTransferItem::where('product_id', $poItem->product_id)
                    ->where('stock_transfer_id', $stockTransfer->id)
                    ->first();
                $stockItem->quantity = $poItem->quantity;
                $stockItem->request_box = count($itemInsert['requested_boxes']);
                $stockItem->save();
                $totalQuantityStockTransfer += $poItem->quantity;
                $totalBoxStockTransfer += $stockItem->request_box;
            }

            $po->total = $totalPrice;
            $po->save();
            $stockTransfer->total_box = $totalBoxStockTransfer;
            $stockTransfer->total_quantity = $totalQuantityStockTransfer;
            $stockTransfer->status = StockTransfer::COMPLETED_STATUS;
            $stockTransfer->updated_at = now();
            $stockTransfer->fulfill_by = $employeeId;
            $stockTransfer->save();

            foreach ($boxData as $box) {
                $box->is_deleted = Box::DELETED;
                $box->save();
                $locationProduct = LocationProduct::where('location_id', $box->location_id)
                    ->where('product_id', $box->product_id)
                    ->first();

                if ($locationProduct) {
                    $locationProduct->quantity -= $box->quantity;
                    $locationProduct->save();
                }

                Inventory::create([
                    'direction' => Inventory::DIRECTION_OUTPUT,
                    'type' => Inventory::TYPE_OUTPUT,
                    'product_id' => $box->product_id,
                    'warehouse_id' => $stockTransfer->from_warehouse_id,
                    'location_id' => $box->location_id,
                    'box_id' => $box->id,
                    'object_id' => $stockTransfer->id,
                    'object_name' => Inventory::OBJECT_STOCK_TRANSFER,
                    'quantity' => $box->quantity,
                ]);
            }

            if (!empty($deleteData)) {
                StockTransferItem::whereIn('id', $deleteData)->delete();
            }

            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $idTimeChecking);

            DB::commit();

            $message = "Warehouse: $warehouseName - A purchase order (PO) #{$po->po_number} has been automatically generated for a stock transfer request #{$stockTransfer->request_number}. Please review the price of this PO.";
            $alertService = new AlertService();
            $alertService->alertStockTransfer($message);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('StockTransferRepository.saveFulfill', [
                'stockTransfer' => $stockTransfer,
                'boxData' => $boxData,
                'insertData' => $insertData,
                'deleteData' => $deleteData,
                'idTimeChecking' => $idTimeChecking,
                'coo' => $coo,
                'employeeId' => $employeeId,
                'exception' => $e,
            ]);

            return false;
        }

        return true;
    }

    private function prepareDataForStockTransfer($stockTransfer, $boxes): array
    {
        if (empty($boxes)) {
            return [[], []];
        }

        // Initialize an array to hold the data to be inserted into the stock transfer.
        $insertData = [
            'stock_transfer_id' => $stockTransfer->id,
            'items' => [],
        ];

        // Initialize an array to hold the IDs of items to be deleted from the stock transfer.
        $deleteData = [];

        // Group the boxes by their product_id.
        $boxesByProductId = $boxes->groupBy('product_id');

        // Iterate through each item in the stock transfer.
        foreach ($stockTransfer->items as $item) {
            // Get the product ID for the current item.
            $productId = $item->product_id;

            // Check if there are boxes associated with the current product ID.
            if ($boxesByProductId->has($productId)) {
                // If boxes exist, prepare data for insertion.
                $insertData['items'][] = [
                    'stock_transfer_item_id' => $item->id,
                    'product_id' => $productId,
                    'requested_boxes' => $boxesByProductId[$productId]->toArray(),
                ];
            } else {
                // If no boxes are associated with the product, mark the item for deletion.
                $deleteData[] = $item->id;
            }
        }

        // Return the prepared data for insertion and deletion.
        return [$insertData, $deleteData];
    }

    public function getTotalRequestPending($warehouseId)
    {
        return StockTransfer::where('from_warehouse_id', $warehouseId)
            ->whereIn('status', [
                StockTransfer::PENDING_STATUS,
                StockTransfer::PARTIAL_COMPLETED_STATUS
            ])
            ->count();
    }

    public function saveTemporaryFulfill($request)
    {
        $boxData = [];
        $stockTransfer = StockTransfer::with(['items', 'fromWarehouse'])
            ->where('from_warehouse_id', $request->warehouse_id)
            ->where('id', $request->stock_transfer_id)
            ->first();
        $errors = [];

        if (!$stockTransfer) {
            $errors['stock_transfer_id'] = ['Stock transfer not exist'];
        } elseif (!in_array($stockTransfer->status, [StockTransfer::PENDING_STATUS, StockTransfer::PARTIAL_COMPLETED_STATUS])) {
            $errors['stock_transfer_id'] = ['Invalid Stock transfer'];
        } else {
            if (!empty($request->box_scanned)) {
                if ($stockTransfer->destination_warehouse_id == Warehouse::WAREHOUSE_MEXICO_ID) {
                    if (empty($request->coo)) {
                        $errors['coo'] = ['Mexico warehouse required COO'];
                    }
                }
                $boxData = Box::with('location')
                    ->where('warehouse_id', $stockTransfer->from_warehouse_id)
                    ->whereIn('barcode', $request->box_scanned)
                    ->select('id', 'barcode', 'product_id', 'location_id', 'quantity', 'is_deleted')
                    ->get();
                $allBoxBarcodes = $boxData->pluck('barcode');
                foreach ($request->box_scanned as $key => $boxBarcode) {
                    $box = $boxData->firstWhere('barcode', $boxBarcode);
                    if (!$allBoxBarcodes->contains($boxBarcode)) {
                        $errors["box_scanned.$key"] = ["Box ID {$boxBarcode} is not exist"];
                    } elseif ($box->is_deleted === Box::DELETED) {
                        $errors["box_scanned.$key"] = ["Box ID {$boxBarcode} is not exist"];
                    } elseif (!$stockTransfer->items->pluck('product_id')->contains($box->product_id)) {
                        $errors["box_scanned.$key"] = ["Product in the box ID {$boxBarcode} does not match the requested product."];
                    } elseif ($box->location->type === Location::PULLING_SHELVES) {
                        $errors["box_scanned.$key"] = ["The box {$boxBarcode} on the pulling shelves cannot be fulfilled."];
                    } elseif ($box->location->type === Location::PENDING_TYPE) {
                        $errors["box_scanned.$key"] = ["The box {$boxBarcode} cannot be fulfilled due to awaiting confirmation of an internal request."];
                    }
                }
            }
        }

        if (!empty($errors)) {
            return response()->json($errors, 422);
        }

        [$insertData, $deleteData] = $this->prepareDataForStockTransfer($stockTransfer, $boxData);
        $status = $this->saveTemporaryFulfillData($stockTransfer, $insertData, $request->coo ?? [], $request->employee_id);

        if ($status) {
            return response()->json(['message' => 'Save Fulfill has been successfully completed']);
        }

        return response()->json(['message' => 'Failed to save fulfill'], 500);
    }

    private function saveTemporaryFulfillData($stockTransfer, $insertData, $coo, $employeeId)
    {
        DB::beginTransaction();

        try {
            $totalQuantityStockTransfer = 0;
            $totalBoxStockTransfer = 0;
            StockTransferBoxLog::where('stock_transfer_id', $stockTransfer->id)->delete();

            if (!empty($insertData)) {
                foreach ($insertData['items'] as $itemInsert) {
                    $product = Product::find($itemInsert['product_id']);

                    foreach ($itemInsert['requested_boxes'] as $itemBox) {
                        $collection = collect($coo);
                        $find = $collection->first(function ($value, $key) use ($itemBox) {
                            return isset($value[$itemBox['barcode']]);
                        });
                        StockTransferBoxLog::create([
                            'stock_transfer_id' => $stockTransfer->id,
                            'stock_transfer_item_id' => $itemInsert['stock_transfer_item_id'],
                            'box_id' => $itemBox['id'],
                            'box_number' => $itemBox['barcode'],
                            'product_id' => $product->id,
                            'quantity' => $itemBox['quantity'],
                            'coo_id' => isset($find) ? reset($find) : null,
                            'created_at' => now(),
                            'updated_at' => now(),
                            'employee_id' => $employeeId,
                        ]);
                        $totalQuantityStockTransfer += $itemBox['quantity'];
                        $totalBoxStockTransfer += 1;
                    }
                }
            }

            $stockTransfer->total_box_received = $totalBoxStockTransfer;
            $stockTransfer->total_quantity_received = $totalQuantityStockTransfer;
            $stockTransfer->status = $insertData ? StockTransfer::PARTIAL_COMPLETED_STATUS : StockTransfer::PENDING_STATUS;
            $stockTransfer->updated_at = now();
            $stockTransfer->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('StockTransferRepository.saveTemporaryFulfillData', [$e]);

            return false;
        }

        return true;
    }
}
