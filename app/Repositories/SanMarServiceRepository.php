<?php

namespace App\Repositories;


use Illuminate\Support\Facades\Log;

class SanMarServiceRepository
{
    public function getInvoicesHeaderByInvoiceDateRange($startDate, $endDate)
    {
        try {
            $localhostWsdlUrl = "https://ws.sanmar.com:8080/SanMarWebService/InvoicePort?WSDL";
            $client = new \SoapClient($localhostWsdlUrl, array('trace' => true,
                'exceptions' => true
            ));
            $invoiceWebServiceUser = array(
                'CustomerNo' => '240111',
                'UserName' => 'swiftpod',
                'Password' => 'Pod1337!',
                'StartDate' => $startDate,
                'EndDate' => $endDate
            );
            //calling the GetInvoiceByInvoiceNo method.
            $result = $client->__soapCall('GetInvoicesHeaderByInvoiceDateRange',
                array('GetInvoicesHeaderByInvoiceDateRangeType' => $invoiceWebServiceUser));

        } catch (SoapFault $e) {
            Log::error($e->getMessage());
            return [];
        }

        return $result;

    }

    public function getInvoiceByInvoiceNo($invoice)
    {
        try {
            $localhostWsdlUrl = "https://ws.sanmar.com:8080/SanMarWebService/InvoicePort?WSDL";
            $client = new \SoapClient($localhostWsdlUrl, array('trace' => true,
                'exceptions' => true
            ));
            //web service credentail
            $invoiceWebServiceUser = array(
                'CustomerNo' => '240111',
                'UserName' => 'swiftpod',
                'Password' => 'Pod1337!',
                'InvoiceNo' => $invoice
            );
            //calling the GetInvoiceByInvoiceNo method.
            $result = $client->__soapCall('GetInvoiceByInvoiceNo',
                array('GetInvoiceByInvoiceNoType' => $invoiceWebServiceUser));

        } catch (SoapFault $e) {
            Log::error($e->getMessage());
           return [];
        }

        return $result;

    }

    public function ProductDataServiceBindingV2($productId, $partId)
    {
        try {
            $localhostWsdlUrl = "https://ws.sanmar.com:8080/promostandards/ProductDataServiceBindingV2?WSDL";

            $client = new \SoapClient($localhostWsdlUrl, array('trace' => true,
                'exceptions' => true
            ));
            //web service credentail
            $invoiceWebServiceUser = array(
                'wsVersion' => '2.0.0',
                'id' => 'swiftpod',
                'password' => 'Pod1337!',
                'productId' => $productId,
                'partId' => $partId,
                'localizationCountry' => 'US',
                'localizationLanguage' => 'EN'
            );

            //calling the GetInvoiceByInvoiceNo method.
            $result = $client->__soapCall('getProduct',
                array('GetProductRequest' => $invoiceWebServiceUser));

        } catch (SoapFault $e) {
            Log::error($e->getMessage());
            return [];
        }

        return $result;

    }


    public function OrderShipmentNotificationServiceBinding($referenceNumber)
    {
        try {
            $localhostWsdlUrl = "https://ws.sanmar.com:8080/promostandards/OrderShipmentNotificationServiceBinding?WSDL";

            $client = new \SoapClient($localhostWsdlUrl, array('trace' => true,
                'exceptions' => true
            ));

            //dd($client->__getFunctions() );
            //web service credentail
            $invoiceWebServiceUser = array(
                'wsVersion' => '1.0.0',
                'id' => 'swiftpod',
                'password' => 'Pod1337!',
                'queryType' =>1,
                'referenceNumber' => $referenceNumber,
            );

            //calling the GetInvoiceByInvoiceNo method.
            $result = $client->__soapCall('getOrderShipmentNotification',
                array('GetOrderShipmentNotificationRequest' => $invoiceWebServiceUser));

        } catch (SoapFault $e) {
            Log::error($e->getMessage());
            return [];
        }
        return $result;
    }



}
