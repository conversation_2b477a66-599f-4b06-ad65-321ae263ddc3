<?php

namespace App\Repositories;

use Illuminate\Support\Facades\DB;

class ShipmentItemRepository
{
    const TABLE = 'shipment_item';

    public function insertData($data)
    {
        DB::table(self::TABLE)
            ->insert($data);
    }

    public function checkShipmentItem($orderId, $orderItemId, $shipmentId)
    {
        return DB::table(self::TABLE)
            ->where('order_id', $orderId)
            ->where('order_item_id', $orderItemId)
            ->where('shipment_id', $shipmentId)
            ->first();
    }

    public function updateShipmentItemByShipmentItemId($shipmentItemId, $dataUpdate)
    {
        return DB::table(self::TABLE)
            ->where('id', $shipmentItemId)
            ->update($dataUpdate);
    }

    public function insertGetId($data)
    {
        return DB::table(self::TABLE)
             ->insertGetId($data);
    }
}
