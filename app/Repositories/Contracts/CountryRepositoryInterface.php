<?php
namespace App\Repositories\Contracts;


interface CountryRepositoryInterface {

    public function fetchAll($input);

    public function getCountryPartNumber($input);

    public function fetchStateByCountryId($id);

    public function create($input);

    public function fetch($id);

    public function update($id, $dataUpdate);

    public function delete($id);

    public function validateCode($id, $code);


}
