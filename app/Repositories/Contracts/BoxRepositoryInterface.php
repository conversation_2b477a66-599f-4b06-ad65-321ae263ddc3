<?php

namespace App\Repositories\Contracts;

interface BoxRepositoryInterface
{
    public function fetchAll($input);

    public function create($request);

    public function fetch($id);

    public function update($id, $dataUpdate);

    public function delete($id);

    public function bulkInsert($input);

    public function getBoxByParams($input);

    public function getOrderBoxByTrackingNumber($trackingNumber, $warehouseId);

    public function getBoxByBarcode($barcode);

    public function getByBarcodeAndProductId($barcode, $productId);

    public function fetchBoxInRack($request);

    public function generateBoxIDs($id);

    public function ListGenerateBox($request);

    public function updatePrintStatusBoxId($id);

    public function buildCollectionExportBox($params);

    public function getBoxNeedMovingToRack($params);
}
