<?php

namespace App\Repositories\Contracts;

interface LocationRepositoryInterface
{
    public function fetchAll($input);

    public function create($input);

    public function fetch($id);

    public function getDetail($id);

    public function update($id, $dataUpdate);

    public function delete($id);

    public function bulkInsert($input);

    public function getLocationByParams($input);

    public function checkMovingShelvesLocation($locationBarcode);

    public function fetchDarkPodByLocation($location);

    public function fetchLocationDarkPod($input);
}
