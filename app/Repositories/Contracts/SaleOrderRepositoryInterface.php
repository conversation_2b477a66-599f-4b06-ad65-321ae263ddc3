<?php

namespace App\Repositories\Contracts;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

interface SaleOrderRepositoryInterface {

    public function getList(Request $request);

    public function getBarcodeItemsNotReprint(Request $request);

    public function getDetail(int $id);

    public function getOrderItems(int $id);

    public function createComment(int $id, Request $request): array;

    public function updateOrderStatus(int $id, Request $request): array;

    public function updateShippingMethod(int $id, Request $request): array;

    public function updateTag(int $id, Request $request): array;

    public function updateNote(int $id, Request $request): array;

    public function updateAddress(int $id, Request $request): array;

    public function verifyAddress(int $id): array;

    public function reprint($request);

    public function reroute(Request $request);

    public function checkBeforeReroute($request);

    public function findById($id);

    public function getClaimOrder($request);

}
