<?php

namespace App\Repositories\Contracts;

interface ProductRepositoryInterface
{
    public function fetchAll($input);

    public function getList($input);

    public function getProductListWithAttribute($request);

    public function create($request);

    public function update(int $id, $request);

    public function upload($style, $request);

    public function updateById($id, $request);

    public function updateByStyle($style, $request);

    public function fetch($id);

    public function delete($id);

    public function validateCreateProductVariant($productVariants);

    public function validateUpdateProductVariant($productVariants);

    public function fetchProductForCreateOrder($input);

    public function getProductByParams($input);

    public function existsSku($sku);

    public function getProductAttributeList();

    public function getPullingShelvesProductQuantity($request);

    public function getProductByAttribute($input);

    public function fetchProducts($input);

    public function fetchProductByAttribute($input);

    public function getProductByGTIN($input);

    public function getProductBySku($input);

    public function getProductBySkuOrLabel($input);

    public function getCatalogDetail($id);

    public function getInfo($input);

    public function validateVariantsCsv($file);

    public function importVariants($file);

    public function validateWeightsCsv($file);

    public function importWeights($file);

    public function getColorsByStyle($input);

    public function coverProductBlankCostByYear($year);

    public function getProductHistoryBlankCostYear($productId);
}
