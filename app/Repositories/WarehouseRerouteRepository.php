<?php

namespace App\Repositories;

use App\Http\Service\AlertService;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SettingMoveWarehouseModel;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WarehouseRerouteRepository extends CommonRepository
{
    public function create($rawData)
    {
        $rawData['rule']['listMerchant'] = $this->checkValidArray($rawData['rule']['listMerchant']);
        $rawData['rule']['listZipcode'] = $this->checkValidArray($rawData['rule']['listZipcode']);
        $rawData['rule']['listSku'] = $this->checkValidArray($rawData['rule']['listSku']);

        $rawDataInsert = [
            'warehouse_id' => $rawData['warehouse_id'],
            'rule' => json_encode($rawData['rule']),
            'is_active' => $rawData['is_active'],
            'limit_daily' => $rawData['limit_daily'] ?? 0,
            'type' => 1,
        ];
        if (isset($rawData['rule']['priority'])) {
            $rawDataInsert['priority'] = $rawData['rule']['priority'];
        }
        $warehouseReroute = new SettingMoveWarehouseModel;
        if (isset($rawData['id']) && $rawData['id']) {
            $settingMoveWarehouse = SettingMoveWarehouseModel::find($rawData['id']);
            $oldData = $settingMoveWarehouse ? json_decode($settingMoveWarehouse->rule, true) : [];
            $oldData['priority'] = $settingMoveWarehouse->priority;
            $warehouseReroute->updateById($rawData['id'], $rawDataInsert);
            $settingMoveWarehouseNew = SettingMoveWarehouseModel::find($rawData['id']);
            $currentData = $settingMoveWarehouseNew ? json_decode($settingMoveWarehouseNew->rule, true) : [];
            $currentData['priority'] = $settingMoveWarehouseNew->priority;
            $this->handleRerouteSettingWarehouseException($rawData['warehouse_id'], $oldData, $currentData);

            return response()->json(['message' => 'success'], 200);
        }
        $warehouseReroute->insert($rawDataInsert);

        return response()->json(['message' => 'success'], 200);
    }

    private function handleRerouteSettingWarehouseException($warehouseId, $oldData, $currentData): bool
    {
        try {
            $messageArr = $this->logUpdatedSettingDiff($oldData, $currentData);
            if (empty($messageArr)) {
                return false;
            }
            $warehouse = Warehouse::find($warehouseId);
            $message = implode("\n", $messageArr);
            $notifyText = "Warehouse :{$warehouse->name}" . "\n" . $message;
            $alertService = new AlertService();
            $alertService->alertUpdateRerouteSetting($notifyText);
        } catch (\Exception $exception) {
            return false;
        }

        return true;
    }

    public function checkValidArray($input)
    {
        if (is_array($input)) {
            foreach ($input as $key => $item) {
                if ($item['value'] === null || $item['value'] === '') {
                    unset($input[$key]);
                }
            }

            return array_values($input);
        }
    }

    public function updatePriority($input)
    {
        $warehouseReroute = new SettingMoveWarehouseModel;

        if (isset($input['id']) && $input['id']) {
            $warehouseReroute->updateById($input['id'], $input);

            return response()->json(['message' => 'success'], 200);
        }
    }

    private function logUpdatedSettingDiff(array $oldData, array $currentData): array
    {
        $changes = [
            'Order type' => [
                'keyData' => 'listOrderType',
                'setting' => SettingMoveWarehouseModel::ORDER_TYPE_SETTING,
            ],
            'Print area' => [
                'keyData' => 'printAreas',
                'setting' => ProductPrintSide::pluck('name', 'code_name')->toArray(),
            ],
            'Merchant' => [
                'keyData' => 'listMerchant',
                'setting' => Store::pluck('name', 'id')->toArray(),
            ],
            'SKU' => [
                'keyData' => 'listSku',
            ],
            'Zipcode' => [
                'keyData' => 'listZipcode',
            ],
            'Shipping account' => [
                'keyData' => 'listShippingAccount',
                'setting' => ShippingMethod::whereNotIn('api_shipping_method', [SaleOrder::SHIPPING_METHOD_STANDARD, SaleOrder::SHIPPING_METHOD_EXPRESS, SaleOrder::SHIPPING_METHOD_PRIORITY])
                    ->groupBy('name')
                    ->pluck('name', 'name')
                    ->toArray()
            ],
        ];
        $message = [];
        $userName = auth()->user()->username;
        foreach ($changes as $attributeName => $arraySetting) {
            $oldArray = $oldData[$arraySetting['keyData']] ?? [];
            $currentArray = $currentData[$arraySetting['keyData']] ?? [];
            $result = $this->getChangeBetweenTwoArray($attributeName, $oldArray, $currentArray, $userName, $arraySetting['setting'] ?? null);
            if (!empty($result)) {
                $message = array_merge($message, $result);
            }
        }

        if ($oldData['is_active'] !== $currentData['is_active']) {
            $message[] = $this->buildUpdateMessage(
                'Active',
                SettingMoveWarehouseModel::ACTIVE_SETTING[$oldData['is_active']],
                SettingMoveWarehouseModel::ACTIVE_SETTING[$currentData['is_active']],
                $userName,
            );
        }
        if ($oldData['limit_daily'] !== $currentData['limit_daily']) {
            $message[] = $this->buildUpdateMessage(
                'Daily limit',
                $oldData['limit_daily'],
                $currentData['limit_daily'],
                $userName,
            );
        }
        if ($oldData['priority'] !== $currentData['priority']) {
            $message[] = $this->buildUpdateMessage(
                'Priority',
                $oldData['priority'],
                $currentData['priority'],
                $userName,
            );
        }

        if ($oldData['international'] !== $currentData['international']) {
            $message[] = $this->buildUpdateMessage(
                'International',
                SettingMoveWarehouseModel::INTERNATIONAl_SETTING[$oldData['international']],
                SettingMoveWarehouseModel::INTERNATIONAl_SETTING[$currentData['international']],
                $userName,
            );
        }

        $oldDate = $this->formatDate($oldData);
        $currentDate = $this->formatDate($currentData);

        if ($oldDate !== $currentDate) {
            $message[] = $this->buildUpdateMessage('Time Period', $oldDate, $currentDate, $userName);
        }

        return $message;
    }

    private function formatDate(array $data): ?string
    {
        $startDate = isset($data['startDate']) ? SettingMoveWarehouseModel::TIME_PERIOD_SETTING[$data['startDate']] : null;
        $startHours = $data['startHours'] ?? null;
        $endDate = isset($data['endDate']) ? SettingMoveWarehouseModel::TIME_PERIOD_SETTING[$data['endDate']] : null;
        $endHours = $data['endHours'] ?? null;

        return !is_null($startDate) ? "$startDate $startHours - $endDate $endHours" : null;
    }

    private function buildUpdateMessage($attributeName, $oldValue, $newValue, $updateBy)
    {
        return "The $attributeName setting was updated from " . ($oldValue ?? 'null') . ' to ' . ($newValue ?? 'null') . " by $updateBy";
    }

    private function getChangeBetweenTwoArray(string $attributeName, array $oldData, array $newData, string $updateBy, array $arraySetting = null): array
    {
        $changes = [];
        foreach ($oldData as $oldItem) {
            $newItem = collect($newData)->firstWhere('value', $oldItem['value']);
            if (!$newItem) {
                $oldValue = is_array($arraySetting) ? $arraySetting[$oldItem['value']] : $oldItem['value'];
                $changes[] = "The $oldValue - $attributeName was deleted by $updateBy";
            }
        }
        foreach ($newData as $newItem) {
            $oldItem = collect($oldData)->firstWhere('value', $newItem['value']);
            if ($oldItem && $newItem['status'] != $oldItem['status']) {
                $newValue = is_array($arraySetting) ? $arraySetting[$newItem['value']] : $newItem['value'];
                $oldStatus = $oldItem['status'] === true ? 'active' : 'inactive';
                $newStatus = $newItem['status'] === true ? 'active' : 'inactive';
                $changes[] = "The $newValue - $attributeName setting was updated from {$oldStatus} to {$newStatus} by $updateBy";
            }
        }
        $newValues = collect($newData)->pluck('value')->diff(collect($oldData)->pluck('value'));
        foreach ($newValues as $newValue) {
            $newValueName = is_array($arraySetting) ? $arraySetting[$newValue] : $newValue;
            $changes[] = "The new {$newValueName} - $attributeName was added by $updateBy";
        }

        return $changes;
    }

    public function getItemReroute()
    {
        setTimezone();

        $start = now()->subDays(30)->startOfDay();
        $end = now()->yesterday()->endOfDay();
        $warehouse = Warehouse::all()->pluck('name', 'code');
        $dataOldItem = SaleOrder::query()
            ->from(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->join('warehouse as w', 'w.id', '=', 'sale_order.warehouse_id')
            ->select(
                'w.name as warehouse_name',
                'w.code',
                'w.id',
                DB::raw('SUM(sale_order.order_quantity) as total_items_old'),
            )
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->whereIn('sale_order.order_status', [SaleOrder::STATUS_NEW_ORDER, SaleOrder::STATUS_IN_PRODUCTION])
            ->whereBetween('sale_order.created_at', [$start, $end])
            ->groupBy('w.id')
            ->get();

        $dataNewItem = SaleOrder::query()
            ->from(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->join('warehouse as w', 'w.id', '=', 'sale_order.warehouse_id')
            ->select(
                'w.code',
                DB::raw('SUM(sale_order.order_quantity) as total_items_new'),
            )
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->whereRaw('sale_order.created_at >= TIMESTAMP(CURRENT_DATE)')
            ->whereRaw('sale_order.created_at < TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY)')
            ->groupBy('w.id')
            ->get();

        $oldItems = $dataOldItem->keyBy('code')->toArray();
        $newItems = $dataNewItem->keyBy('code')->toArray();
        $dataWareHouseCapacity = [
            'SJ' => 100000,
            'TX' => 28000,
            'LV' => 14000,
            'MX' => 28000,
            'VA' => 28000,
            'GA' => 3360,
        ];
        $result = [];
        foreach ($dataWareHouseCapacity as $code => $capacity) {
            $oldItem = $oldItems[$code] ?? ['id' => null, 'warehouse_name' => null, 'total_items_old' => 0];
            $newItem = $newItems[$code] ?? ['total_items_new' => 0];
            $capacityUtilization = $oldItem['total_items_old'] + ($newItem['total_items_new'] ?? 0);
            $percentCapacityUtilization = $capacity > 0 ? round(($capacityUtilization / $capacity) * 100) : 0;
            $percentRemainingCapacity = 100 - $percentCapacityUtilization;
            $remainingCapacity = $capacity - $capacityUtilization;
            $result[] = [
                'code' => $code,
                'id' => $oldItem['id'],
                'warehouse_name' => $warehouse[$code],
                'capacity' => $capacity,
                'capacity_utilization' => $capacityUtilization,
                'percent_capacity_utilization' => $percentCapacityUtilization,
                'percent_remaining_capacity' => $percentRemainingCapacity,
                'remaining_capacity' => $remainingCapacity,
                'new_item' => $newItem['total_items_new'] ?? 0,
                'old_item' => $oldItem['total_items_old'] ?? 0,
            ];
        }

        return $result;
    }

    public function getItemRerouteFail($id)
    {
        setTimezone();
        $startOfTodayPST = Carbon::now('America/Los_Angeles')->startOfDay();
        $endOfTodayPST = Carbon::now('America/Los_Angeles')->endOfDay();
        $warehouse = Warehouse::find($id);
        if (!$warehouse) {
            return $this->errorResponse('Warehouse not found!');
        }

        // Tổng đơn hàng mới hôm nay
        $totalNewItem = SaleOrder::query()
            ->from(DB::raw('sale_order USE INDEX (idx_created_at)'))
            ->join('warehouse as w', 'w.id', '=', 'sale_order.warehouse_id')
            ->where('sale_order.is_test', SaleOrder::NOT_TEST)
            ->where('sale_order.warehouse_id', $id)
            ->whereRaw('sale_order.created_at >= TIMESTAMP(CURRENT_DATE)')
            ->whereRaw('sale_order.created_at < TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY)')
            ->sum('sale_order.order_quantity');

        // Tổng reroute thất bại (tính theo order_quantity)
        $dataRerouteFail = DB::query()
            ->from(DB::raw('reroute_logs AS rl USE INDEX (idx_created_at)'))
            ->join('sale_order as so', 'so.id', '=', 'rl.sale_order_id')
            ->where('rl.warehouse_to', $id)
            ->whereBetween('rl.created_at', [$startOfTodayPST, $endOfTodayPST])
            ->sum('so.order_quantity');

        // Gom tất cả lỗi condition_failed thành 1 query duy nhất
        $conditionCounts = DB::query()
            ->from(DB::raw('reroute_logs AS rl USE INDEX (idx_created_at)'))
            ->join('sale_order as so', 'so.id', '=', 'rl.sale_order_id')
            ->select([
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'order_type' THEN so.order_quantity ELSE 0 END) as total_order_type_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'product_type' THEN so.order_quantity ELSE 0 END) as total_product_type_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'print_area' THEN so.order_quantity ELSE 0 END) as total_print_area_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'shipping_account' THEN so.order_quantity ELSE 0 END) as total_shipping_account_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'limit_daily' THEN so.order_quantity ELSE 0 END) as total_limit_daily"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'zip_code' THEN so.order_quantity ELSE 0 END) as total_zip_code_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'international' THEN so.order_quantity ELSE 0 END) as total_international_fail"),
                DB::raw("SUM(CASE WHEN rl.condition_failed = 'sku' THEN so.order_quantity ELSE 0 END) as total_sku_fail"),
            ])
            ->where('rl.warehouse_to', $id)
            ->whereBetween('rl.created_at', [$startOfTodayPST, $endOfTodayPST])
            ->first();

        // Merchant fail (giữ nguyên do có GROUP BY)
        $dataMerchantFail = DB::query()
            ->from(DB::raw('reroute_logs AS rl USE INDEX (idx_created_at)'))
            ->join('store as s', 's.id', '=', 'rl.value_failed')
            ->select('rl.value_failed', 's.name', DB::raw('COUNT(*) as total'))
            ->where('rl.warehouse_to', $id)
            ->where('rl.condition_failed', 'merchant')
            ->whereBetween('rl.created_at', [$startOfTodayPST, $endOfTodayPST])
            ->groupBy('rl.value_failed', 's.name')
            ->orderBy('rl.value_failed')
            ->get();

        $totalMerchantFail = $dataMerchantFail->sum('total');

        // Các trường hợp lỗi cần hiển thị dạng chi tiết (vẫn giữ lại): order_type, product_type,...
        $dataOrderTypeFail = $this->getGroupedFailures($id, 'order_type', $startOfTodayPST, $endOfTodayPST);
        $dataProductTypeFail = $this->getGroupedFailures($id, 'product_type', $startOfTodayPST, $endOfTodayPST);
        $dataPrintAreaFail = $this->getGroupedFailures($id, 'print_area', $startOfTodayPST, $endOfTodayPST);
        $dataShippingAccountFail = $this->getGroupedFailures($id, 'shipping_account', $startOfTodayPST, $endOfTodayPST);

        $percentFailed = $totalNewItem > 0 ? round(($dataRerouteFail / $totalNewItem) * 100) : 0;

        return [
            'total_new_item' => $totalNewItem,
            'data_reroute_fail' => $dataRerouteFail,
            'data_merchant_fail' => $dataMerchantFail,
            'total_merchant_fail' => $totalMerchantFail,
            'data_order_type_fail' => $dataOrderTypeFail,
            'total_order_type_fail' => $conditionCounts->total_order_type_fail ?? 0,
            'data_product_type_fail' => $dataProductTypeFail,
            'total_product_type_fail' => $conditionCounts->total_product_type_fail ?? 0,
            'data_print_area_fail' => $dataPrintAreaFail,
            'total_print_area_fail' => $conditionCounts->total_print_area_fail ?? 0,
            'data_shipping_account_fail' => $dataShippingAccountFail,
            'total_shipping_account_fail' => $conditionCounts->total_shipping_account_fail ?? 0,
            'total_limit_daily' => $conditionCounts->total_limit_daily ?? 0,
            'total_zip_code_fail' => $conditionCounts->total_zip_code_fail ?? 0,
            'total_international_fail' => $conditionCounts->total_international_fail ?? 0,
            'total_sku_fail' => $conditionCounts->total_sku_fail ?? 0,
            'percent_failed' => $percentFailed,
        ];
    }

    private function getGroupedFailures($warehouseId, $condition, $start, $end)
    {
        return DB::table('reroute_logs as rl')
            ->join('sale_order as so', 'so.id', '=', 'rl.sale_order_id')
            ->select('rl.value_failed', DB::raw('SUM(so.order_quantity) as total'))
            ->where('rl.warehouse_to', $warehouseId)
            ->where('rl.condition_failed', $condition)
            ->whereBetween('rl.created_at', [$start, $end])
            ->groupBy('rl.value_failed')
            ->orderBy('rl.value_failed')
            ->get();
    }
}
