<?php

namespace App\Policies;

use App\Models\SaleOrderClaimSupport;
use App\Models\Store;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Auth;

class SaleOrderClaimSupportPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function view(Store|User $storeOrUser, SaleOrderClaimSupport $claim)
    {
        if ($storeOrUser instanceof Store) {
            return $claim->store_id == $storeOrUser->id;
        }
        if ($storeOrUser instanceof User) {
            return $storeOrUser->is_active;
        }
    }

    public function chat(Store|User $storeOrUser, SaleOrderClaimSupport $claim)
    {
        if (!$this->view($storeOrUser, $claim)) {
            return false;
        }

        $condition = $claim->status == 'in_review' && !$storeOrUser->is_login_support;
        if ($storeOrUser instanceof User) {
            $employeeId = request()->input('employeeId');
            return $condition && $employeeId && $employeeId == $claim->assign;
        }

        return $condition;
    }
}
