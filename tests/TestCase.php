<?php

namespace Tests;

use App\Models\AppIps;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user before running tests
        AppIps::factory()->create([
            'ip_address' => '127.0.0.1',
            'expired_at' => Carbon::now()->addYear(1)
        ]);
    }
}
