<?php
use App\Models\SaleOrder;
use App\Models\Country;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderHistory;

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    Country::factory()->create();
    $this->saleOrder = SaleOrder::factory()->has(SaleOrderAddress::factory(), 'address')->create();
    $this->addressId = $this->saleOrder->address->id;
});

// Kiểm tra address không tồn tại
test('Address not found', function () {
    $params = [
        "order_id" => 1,
        "name" => "<PERSON> Martin",
        "street1" => "AUGUSTA AVE",
        "street2" => "4992",
        "city" => "OLDSMAR",
        "state" => "FL",
        "zip" => "34677-6331",
        "country" => "US",
        "phone" => "",
        "email" => "",
    ];

    $response = $this->put('/api/sale-order/88/address-v1', $params);
    $data = json_decode($response->getContent(), true);

    $response->assertStatus(404);
});

// Kiểm tra sale order không tồn tại
test('Sale order not found', function () {
    $params = [
        "name" => "Candace Martin",
        "street1" => "AUGUSTA AVE",
        "street2" => "4992",
        "city" => "OLDSMAR",
        "state" => "FL",
        "zip" => "34677-6331",
        "country" => "US",
        "phone" => "",
        "email" => ""
    ];
    $saleOrderAddress = SaleOrderAddress::factory()->create();
    $response = $this->put("/api/sale-order/$saleOrderAddress->id/address-v1", $params);
    $data = json_decode($response->getContent(), true);

    $response->assertStatus(404);
});

test('Address check required', function () {
    $params = [
        "name" => "",
        "street1" => "",
        "street2" => "4992",
        "city" => "",
        "state" => "FL",
        "zip" => "",
        "country" => "",
        "phone" => "",
        "email" => ""
    ];
    $response = $this->put("/api/sale-order/$this->addressId/address-v1", $params);
    $data = json_decode($response->getContent(), true);

    $response->assertStatus(422);
    expect($data)->toHaveKey('errors.name');
    expect($data)->toHaveKey('errors.country');
    expect($data)->toHaveKey('errors.street1');
    expect($data)->toHaveKey('errors.city');
    expect($data)->toHaveKey('errors.zip');
});

// Kiểm tra email
test('Email validation', function () {
    $params = [
        "name" => "Candace Martin",
        "street1" => "AUGUSTA AVE",
        "street2" => "4992",
        "city" => "OLDSMAR",
        "state" => "FL",
        "zip" => "34677-6331",
        "country" => "US",
        "phone" => "",
        "email" => "email"
    ];
    $response = $this->put("/api/sale-order/$this->addressId/address-v1", $params);
    $data = json_decode($response->getContent(), true);

    $response->assertStatus(422);
    expect($data)->toHaveKey('errors.email');
});

// Kiểm tra lưu thành công
test('Save success', function () {
    $params = [
        "name" => "Candace Martin",
        "street1" => "AUGUSTA AVE",
        "street2" => "4992",
        "city" => "OLDSMAR",
        "state" => "FL",
        "zip" => "34677-6331",
        "country" => "US",
        "phone" => "0987654321",
        "email" => "<EMAIL>"
    ];
    $response = $this->put("/api/sale-order/$this->addressId/address-v1", $params);
    $data = json_decode($response->getContent(), true);

    $response->assertStatus(200);
    $address = SaleOrderAddress::where('id', $this->addressId)->first();

    foreach($params as $key => $value) {
        expect($address[$key])->toBe($value);
    }

    // Lưu lại lịch sử thay đổi
    $this->assertEquals(1, SaleOrderHistory::count());
});



