<?php

use App\Models\Country;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderHistory;
use App\Models\Store;
use App\Repositories\SaleOrderApiRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    Country::factory()->create();
    $this->store = Store::factory()->create();
    $this->saleOrder = SaleOrder::factory()->has(SaleOrderAddress::factory(), 'address')->create();
    $this->addressId = $this->saleOrder->address->id;
    $this->endpoint = '/api/sale-order';
    $this->params = [
        'line_items' => [
            [
                'order_item_id' => 'devtest-1',
                'quantity' => 1,
                'name' => 'devtest-1',
                'sku' => 'UNPT9C2XL',
                'print_files' => [
                    [
                        'name' => 'PrintFiles.Front',
                        'key' => 'front',
                        'print_side' => 'Front',
                        'url' => 'https://bizweb.dktcdn.net/100/386/478/products/ea38cde9-b93b-4008-bf40-ea1dcb28ec58.jpg?v=1648116739917'
                    ]
                ],
                'preview_files' => [
                    [
                        'name' => 'PreviewFiles.Front',
                        'key' => 'front',
                        'print_side' => 'Front',
                        'url' => 'https://bizweb.dktcdn.net/100/386/478/products/ea38cde9-b93b-4008-bf40-ea1dcb28ec58.jpg?v=1648116739917'
                    ]
                ],
                'style' => '3001',
                'color' => 'AQUA',
                'size' => '2XL'
            ]
        ],
        'external_number' => 'devtest124',
        'sample_order' => 0,
        'shipping_method' => 'standard',
        'return_address' => [
            'name' => 'mytest',
            'company' => null,
            'country' => 'US',
            'street1' => '715 Broadway',
            'street2' => null,
            'state' => 'NY',
            'city' => 'United States',
            'zip' => '10003',
            'email' => null,
            'phone' => null
        ],
        'address' => [
            'country' => 'US',
            'country_id' => 233,
            'state_id' => 1398,
            'name' => 'asd',
            'street1' => 'asd',
            'city' => 'asd',
            'zip' => 'ads',
            'state' => 'UM-84'
        ],
        'tax_id' => '',
        'tax_id_type' => '',
        'plastic_bag' => false,
        'label_url' => '',
        'store_id' => $this->store->id,
        'is_reprint' => false,
        'order_id' => 'devtest124'
    ];
});

// Create success
test('Create success: art file is url', function () {
    $dataResponse = json_decode('{"status": true,"message": "Success.","data": {"id": "9YEY1QYD","order_id": "777D888A999T1"}}');

    $this->mock(SaleOrderApiRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldReceive('sendRequest')->once()->andReturn($dataResponse);
    })->makePartial();

    $response = $this->post($this->endpoint, $this->params);

    $this->assertDatabaseHas('sale_order_history', [
        'type' => SaleOrderHistory::MANUAL_CREATE_ORDER_TYPE,
        'message' => 'Manual order successfully created',
    ]);
});

test('Create success: art file is file', function () {
    $this->params = [
        'line_items' => [
            [
                'order_item_id' => 'devtest-1',
                'quantity' => 1,
                'name' => 'devtest-1',
                'sku' => 'UNPT9C2XL',
                'print_files' => [
                    [
                        'name' => 'PrintFiles.Front',
                        'key' => 'front',
                        'print_side' => 'Front',
                        'print_file_raw' => UploadedFile::fake()->create('test.png'),
                        'url' => ''
                    ]
                ],
                'preview_files' => [
                    [
                        'name' => 'PreviewFiles.Front',
                        'key' => 'front',
                        'print_side' => 'Front',
                        'preview_file_raw' => UploadedFile::fake()->create('test.png'),
                        'url' => ''
                    ]
                ],
                'style' => '3001',
                'color' => 'AQUA',
                'size' => '2XL'
            ]
        ],
        'external_number' => 'devtest124',
        'sample_order' => 0,
        'shipping_method' => 'standard',
        'return_address' => [
            'name' => 'mytest',
            'company' => null,
            'country' => 'US',
            'street1' => '715 Broadway',
            'street2' => null,
            'state' => 'NY',
            'city' => 'United States',
            'zip' => '10003',
            'email' => null,
            'phone' => null
        ],
        'address' => [
            'country' => 'US',
            'country_id' => 233,
            'state_id' => 1398,
            'name' => 'asd',
            'street1' => 'asd',
            'city' => 'asd',
            'zip' => 'ads',
            'state' => 'UM-84'
        ],
        'tax_id' => '',
        'tax_id_type' => '',
        'plastic_bag' => false,
        'label_url' => '',
        'store_id' => $this->store->id,
        'is_reprint' => false,
        'order_id' => 'devtest124'
    ];
    Storage::fake('s3');

    $dataResponse = json_decode('{"status": true,"message": "Success.","data": {"id": "9YEY1QYD","order_id": "777D888A999T1"}}');

    $this->mock(SaleOrderApiRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldReceive('sendRequest')->once()->andReturn($dataResponse);
    })->makePartial();

    $this->post($this->endpoint, $this->params);

    $this->assertDatabaseHas('sale_order_history', [
        'type' => SaleOrderHistory::MANUAL_CREATE_ORDER_TYPE,
        'message' => 'Manual order successfully created',
    ]);
});
