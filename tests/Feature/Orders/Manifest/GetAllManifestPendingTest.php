<?php

use App\Models\Employee;
use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrierService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/manifest/pending?';
    $this->params = [
        'limit' => '10',
        'page' => '1',
        'tracking_number' => '',
        'status' => ''
    ];
});

//
test('get manifest pending success!', function () {
    // not found manifest
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    $dataManifest = ShipmentManifest::factory()->count(6)->state(new Sequence(
        [
            'url' => null,
            'printed_at' => null,
            'warehouse_id' => $this->warehouse->id,
            'status' => 'scanning'
        ],
        [
            'url' => null,
            'printed_at' => null,
            'warehouse_id' => $this->warehouse->id,
            'status' => 'generating'
        ],
        [
            'url' => 'https://dev-quen1.com',
            'printed_at' => null,
            'warehouse_id' => $this->warehouse->id,
            'status' => 'print_waiting'
        ],
        [
            'url' => 'https://dev-quen2.com',
            'printed_at' => null,
            'warehouse_id' => $this->warehouse->id,
            'status' => 'print_waiting'
        ],
        [
            'url' => 'https://dev-quen3.com',
            'printed_at' => null,
            'warehouse_id' => 9999,
            'status' => 'print_waiting'
        ],
        [
            'url' => null,
            'printed_at' => null,
            'warehouse_id' => 9999,
            'status' => 'scanning'
        ],
        [
            'url' => 'https://dev-quen4.com',
            'printed_at' => now(),
            'warehouse_id' => $this->warehouse->id,
            'status' => 'printed'
        ]
    ))
        ->has(ShipmentManifestTracking::factory()->count(3)
            ->for(ShippingCarrierService::factory(), 'shippingService'), 'shipmentManifestTrackings')
        ->for(Employee::factory(), 'employee')
        ->create();

    //get all manifest
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 4]);

    //search manifest pending status
    $this->params['status'] = "nhap_linh_tinh";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    //search manifest pending status continue
    $this->params['status'] = "generating";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 1]);

    //search manifest pending status print
    $this->params['status'] = "print_waiting";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 2]);

    //search manifest tracking number
    $this->params['status'] = "";
    $this->params['tracking_number'] = $dataManifest[0]->shipmentManifestTrackings[0]->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 1]);

    //search manifest tracking number, status print
    $this->params['status'] = "print_waiting";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    //search manifest tracking number, status pending
    $this->params['status'] = "generating";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    //search manifest tracking number thuoc manifest warehouse khac
    $this->params['status'] = "";
    $this->params['tracking_number'] = $dataManifest[5]->shipmentManifestTrackings[0]->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);
});

