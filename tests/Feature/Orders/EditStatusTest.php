<?php

use App\Models\InvoiceSaleOrder;
use App\Models\InvoiceSaleOrderError;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Tag;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->saleOrder = SaleOrder::factory()->create([
        'order_status' => SaleOrder::NEW_ORDER
    ]);
    $orderId = $this->saleOrder->id;
    $imageUrl = 'https:\/\/thoitrangmantis.com\/print-file.jpg';
    $this->imageUrl = 'https:\/\/thoitrangmantis.com\/print-file.jpg';
    $this->orderItem = SaleOrderItem::factory()
        ->has(SaleOrderItemImage::factory()->state(function (array $attributes, SaleOrderItem $orderItem) use ($orderId, $imageUrl) {
            return [
                'order_item_id' => $orderItem->id,
                'order_id' => $orderId,
                'print_side' => 0,
                'thumb_750' => 1,
                'image_url' => $imageUrl,
                'upload_s3_status' => 1,
                'thumb_250' => 1,
                'skip_retry' => 1,
                'sku' => 'MTk3MTc4Nw==vdYOGT1B00M',
                'retry_download_manual_count' => 5,
                'retry_detect_color_count' => 1,
                'last_retry_detect_color' => '2022-09-07 12:14:59',
                'retry_count' => 1,
            ];
        }), 'images')
        ->has(SaleOrderItemBarcode::factory()->state(function (array $attributes, SaleOrderItem $orderItem) use ($orderId) {
            return [
                'order_item_id' => $orderItem->id,
                'order_id' => $orderId,
                'sku' => 'MTk3MTc4Nw==vdYOGT1B00M'
            ];
        }), 'barcodes')
        ->create([
            'order_id' => $orderId,
            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/thoitrangmantis.com\/print-file.jpg"},{"name":"PreviewFiles.Front","value":"https:\/\/thoitrangmantis.com\/preview-file.jpg"}]'
        ]);
    //    $this->endpoint = '/api/sale-order/{order_id}/order-status';
    $this->endpoint = '/api/sale-order/';
    $this->params = [
        'order_status' => SaleOrder::NEW_ORDER,
        'is_xqc' => 0,
        'is_test' => 0,
    ];
});

//// Kiểm tra 1 sale order không tồn tại
//test('sale order not found', function () {
//    $response = $this->put($this->endpoint . 9999 . '/order-status', $this->params);
//    $response->assertStatus(422);
//    $response = json_decode($response->getContent(), true);
//    $this->assertEquals($response, ['message' => 'Order not found.']);
//});
//
//// Kiểm tra order_status có null hay không
//test('Check status required', function () {
//    $this->params['order_status'] = null;
//    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
//    $response->assertStatus(422);
//    $response = json_decode($response->getContent(), true);
//    $this->assertEquals($response, [
//        'message' => 'The given data was invalid.',
//        'errors' => [
//            'order_status' => [
//                'The order status field is required.'
//            ]
//        ]
//    ]);
//});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang on_hold không có release date
test('No param release_on_hold_at. Change order_status from valid status new_order to on_hold - success', function () {
    $this->params['order_status'] = SaleOrder::STATUS_ON_HOLD;
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "new_order" to "on_hold". '
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang on_hold có release date
test('Change order_status from valid status new_order to on_hold - success', function () {
    $this->params['order_status'] = SaleOrder::STATUS_ON_HOLD;
    $this->params['release_on_hold_at'] = now()->addDays(1)->format('Y-m-d');
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "new_order" to "on_hold". Release Date on ' . Carbon::parse($this->params['release_on_hold_at'])->format('Y-m-d H:i:s')
    ]);

    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $this->saleOrder->id,
        'release_on_hold_at' => Carbon::parse($this->params['release_on_hold_at'])->format('Y-m-d H:i:s')
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang on_hold
test('Change order_status from valid status in_production to on_hold - success', function () {
    $this->params['order_status'] = SaleOrder::STATUS_ON_HOLD;
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_IN_PRODUCTION]);
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "in_production" to "on_hold". '
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang on_hold
test('Change order_status from valid status shipped to on_hold - success', function () {
    $this->params['order_status'] = SaleOrder::STATUS_ON_HOLD;
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::SHIPPED]);
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "shipped" to "on_hold". '
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang rejected
test('Change order_status from valid status new_order to rejected  - success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::REJECTED,
        'rejected_reason' => $this->params['rejected_reason'],
        'tag' => Tag::MANUAL_REJECT_ID
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "new_order" to "rejected". '
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_id' => $this->saleOrder->id,
        'delete_status' => 1
    ]);

    $this->assertDatabaseHas('sale_order_item_barcode', [
        'order_id' => $this->saleOrder->id,
        'is_deleted' => 1
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang rejected
test('Change order_status from valid status in_production to rejected  - success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_IN_PRODUCTION]);
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::REJECTED,
        'rejected_reason' => $this->params['rejected_reason'],
        'tag' => Tag::MANUAL_REJECT_ID
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "in_production" to "rejected". '
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_id' => $this->saleOrder->id,
        'delete_status' => 1
    ]);

    $this->assertDatabaseHas('sale_order_item_barcode', [
        'order_id' => $this->saleOrder->id,
        'is_deleted' => 1
    ]);
});

// Trạng thái hiện tại của order là new_order, in_production, shipped cho phép chuyển sang rejected
test('Change order_status from valid status shipped to rejected  - success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::SHIPPED]);
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders Shipped to Rejected"
    ]);
});

// Trạng thái hiện tại của order là new_order không cho phép chuyển sang in_production, shipped, cancelled
test('Change order_status from status new_order to (in_production, shipped, cancelled) fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders New Order to In Production"
    ]);

    $this->params['order_status'] = SaleOrder::SHIPPED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders New Order to Shipped"
    ]);

    $this->params['order_status'] = SaleOrder::CANCELLED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders New Order to Cancelled"
    ]);
});

// Trạng thái hiện tại của order là in_production không cho phép chuyển sang new_order, shipped, cancelled
test('Change order_status from status in_production to (new_order, shipped, cancelled) fails', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::IN_PRODUCTION]);
    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders In Production to New Order"
    ]);

    $this->params['order_status'] = SaleOrder::SHIPPED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders In Production to Shipped"
    ]);

    $this->params['order_status'] = SaleOrder::CANCELLED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders In Production to Cancelled"
    ]);
});

// Trạng thái hiện tại của order là shipped không cho phép chuyển sang new_order, in_production, cancelled
test('Change order_status from status shipped to (new_order, in_production, cancelled) fails', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::SHIPPED]);
    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders Shipped to New Order"
    ]);

    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders Shipped to In Production"
    ]);

    $this->params['order_status'] = SaleOrder::CANCELLED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response, [
        'message' => "Can't change orders Shipped to Cancelled"
    ]);
});

// Trạng thái hiện tại của order là on_hold và order_production_at và shipment_id đều null cho phép chuyển sang new_order
test('If current order_status is on_hold & order_production_at is null & shipment_id is null allow change status new_order success', function () {
    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
    ]);
    // new order is required
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::NEW_ORDER,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "new_order". '
    ]);
});

// Trạng thái hiện tại của order là on_hold và order_production_at và shipment_id đều null cho phép chuyển sang rejected
test('If current order_status is on_hold & order_production_at is null & shipment_id is null allow change status rejected success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::REJECT_INVALID_DOWNLOAD_URL;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::REJECTED,
        'rejected_reason' => $this->params['rejected_reason'],
        'tag' => Tag::MANUAL_REJECT_ID
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "rejected". '
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_id' => $this->saleOrder->id,
        'delete_status' => 1
    ]);
    $this->assertDatabaseHas('sale_order_item_barcode', [
        'order_id' => $this->saleOrder->id,
        'is_deleted' => 1
    ]);
});

// Trạng thái hiện tại của order là on_hold và order_production_at và shipment_id đều null không cho phép chuyển sang shipped, in_production, cancelled
test('If current order_status is on_hold & order_production_at is null & shipment_id is null allow change status (shipped, in_production, cancelled) fails', function () {
})->skip('Not written yet');

// Trạng thái hiện tại của order là on_hold và order_production_at khác null và shipment_id null cho phép chuyển sang in_production
test('If current order_status is on_hold & order_production_at not null & shipment_id is null allow change status in_production success', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
        'order_production_at' => now()
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::IN_PRODUCTION,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "in_production". '
    ]);
});

// Trạng thái hiện tại của order là on_hold và order_production_at khác null và shipment_id null cho phép chuyển sang rejected
test('If current order_status is on_hold & order_production_at not null & shipment_id is null allow change status rejected success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
        'order_production_at' => now()
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::REJECTED,
        'rejected_reason' => $this->params['rejected_reason'],
        'tag' => Tag::MANUAL_REJECT_ID
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "rejected". '
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_id' => $this->saleOrder->id,
        'delete_status' => 1
    ]);
    $this->assertDatabaseHas('sale_order_item_barcode', [
        'order_id' => $this->saleOrder->id,
        'is_deleted' => 1
    ]);
});

// Trạng thái hiện tại của order là on_hold và order_production_at khác null và shipment_id  null không cho phép chuyển sang shipped, new_order, cancelled
test('If current order_status is on_hold & order_production_at not null & shipment_id is null allow change status (shipped, new_order, cancelled) fails', function () {
})->skip('Not written yet');

// Trạng thái hiện tại của order là on_hold và shipment_id không null cho phép chuyển sang shipped
test('If current order_status is on_hold & shipment_id not null allow change status shipped success', function () {
    // Thêm lịch sử thay đổi
    $this->params['order_status'] = SaleOrder::SHIPPED;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
        'shipment_id' => 9999
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::SHIPPED,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "shipped". '
    ]);
});

// Trạng thái hiện tại của order là on_hold và shipment_id không null cho phép chuyển sang rejected
test('If current order_status is on_hold & shipment_id not null allow change status rejected success', function () {
    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
        'shipment_id' => 9999
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'Update order successfully.');
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::REJECTED,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "on_hold" to "rejected". '
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_id' => $this->saleOrder->id,
        'delete_status' => 1
    ]);
    $this->assertDatabaseHas('sale_order_item_barcode', [
        'order_id' => $this->saleOrder->id,
        'is_deleted' => 1
    ]);
});

// Trạng thái hiện tại của order là on_hold và shipment_id không null không cho phép chuyển sang in_production, new_order, cancelled
test('If current order_status is on_hold & shipment_id not null allow change status (in_production, new_order, cancelled) fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::ON_HOLD,
        'shipment_id' => 9999
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders On Hold to In Production");

    // cho phép chuyển on hold khi có shipment sang new order

    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(200);

    $this->params['order_status'] = SaleOrder::CANCELLED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders New Order to Cancelled");
});

// Trạng thái hiện tại của order là rejected không cho phép chuyển sang new_order, in_production, shipped, cancelled, on_hold (Không cho phép chuyển trạng thái nào)
test('Change order_status from status rejected to (new_order, in_production, shipped, cancelled, on_hold) - fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::REJECTED,
        'shipment_id' => 9999
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Rejected to In Production");

    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Rejected to New Order");

    $this->params['order_status'] = SaleOrder::CANCELLED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Rejected to Cancelled");

    $this->params['order_status'] = SaleOrder::SHIPPED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Rejected to Shipped");

    $this->params['order_status'] = SaleOrder::ON_HOLD;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Rejected to On Hold");
});

// Trạng thái hiện tại của order là cancelled không cho phép chuyển sang new_order, in_production, shipped, rejected, on_hold (Không cho phép chuyển trạng thái nào)
test('Change order_status from status cancelled to (new_order, in_production, shipped, rejected, on_hold) - fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'order_status' => SaleOrder::CANCELLED,
        'shipment_id' => 9999
    ]);
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Cancelled to In Production");

    $this->params['order_status'] = SaleOrder::NEW_ORDER;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Cancelled to New Order");

    $this->params['order_status'] = SaleOrder::SHIPPED;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Cancelled to Shipped");

    $this->params['order_status'] = SaleOrder::ON_HOLD;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Cancelled to On Hold");

    $this->params['order_status'] = SaleOrder::REJECTED;
    $this->params['rejected_reason'] = SaleOrder::IP_VIOLATION_REJECTED_REASON;
    $response = $this->put($this->endpoint . $this->saleOrder->id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], "Can't change orders Cancelled to Rejected");
});

// Test when order exists in invoice_sale_order table
test('Change order status when order exists in invoice_sale_order table - fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    $invoice = InvoiceSaleOrder::factory()->create();

    $response = $this->put($this->endpoint . $invoice->sale_order_id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals(
        $response['message'],
        "This order status can’t be change since it already included in an invoice."
    );
});

// Test when order exists in invoice_sale_order_error table  
test('Change order status when order exists in invoice_sale_order_error table - fails', function () {
    $this->params['order_status'] = SaleOrder::IN_PRODUCTION;
    $invoice = InvoiceSaleOrderError::factory()->create();

    $response = $this->put($this->endpoint . $invoice->sale_order_id . '/order-status', $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals(
        $response['message'],
        "This order status can’t be change since it already included in an invoice."
    );
});
