<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\Setting;
use App\Models\Tag;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    Setting::create([
        'label' => 'store_exclude_check_ip',
        'name' => 'store_exclude_check_ip',
        'value' => 10000,
    ]);
});

// Kiểm tra 1 sale order không tồn tại
test('Sale order not found', function () {
    $saleOrder = SaleOrder::factory()->create(['id' => 2]);
    $response = $this->get('/api/sale-order/1');
    $response->assertStatus(404);
});

// <PERSON><PERSON><PERSON> tra lưu thành công khi update single tag
test('Update Fail - Sale Order Not Found', function () {
    $saleOrderId = 58;
    $params = [
        'tag' => [3],
        'id' => $saleOrderId,
    ];

    $response = $this->put("/api/sale-order/$saleOrderId/tag", $params);

    $data = json_decode($response->getContent(), true);
    expect($data)->toHaveKeys(['message']);
    $this->assertEquals($data['message'], 'Order not found.');
});

// Kiểm tra lưu thành công khi update single tag
test('Update Success - Single Tag', function () {
    for ($i = 0; $i < 5; $i++) {
        Tag::factory()->create(['id' => $i + 1]);
    }

    $tags = Tag::get()->pluck('name', 'id');

    $saleOrder = SaleOrder::factory()->create(['id' => 6, 'tag' => '1,2']);
    $saleOrderId = $saleOrder->id;

    $newTags = [3];
    $currentTags = !empty($saleOrder->tag) ? explode(',', $saleOrder->tag) : [];

    $addTags = [];
    $removeTags = [];
    foreach ($newTags as $tagId) {
        if (!isset($tags[$tagId])) {
            continue;
        }
        if (!in_array($tagId, $currentTags)) {
            $addTags[] = $tags[$tagId];
        }
    }
    foreach ($currentTags as $tagId) {
        if (!isset($tags[$tagId])) {
            continue;
        }
        if (!in_array($tagId, $newTags)) {
            $removeTags[] = $tags[$tagId];
        }
    }

    $message = '';
    if (count($addTags) > 0) {
        $message = 'Add new tag "';
        $message .= implode(', ', $addTags);
        $message .= '"';
    }
    if (count($removeTags) > 0) {
        $message .= $message ? '\nRemove tag "' : 'Remove tag "';
        $message .= implode(', ', $removeTags);
        $message .= '"';
    }

    $params = [
        'tag' => $newTags,
        'id' => $saleOrderId,
    ];

    // Dữ liệu truyền lên dạng mảng, lưu db dạng string (json) [1, 2] -> 1,2
    $response = $this->put("/api/sale-order/$saleOrderId/tag", $params);

    $data = json_decode($response->getContent(), true);

    $response->assertStatus(200);
    expect($data)->toHaveKeys(['message']);
    $data = $data['data'];

    $this->assertDatabaseHas('sale_order', [
        'id' => $saleOrderId,
        'tag' => $data['tag'],
    ]);

    // Lưu lại lịch sử thay đổi khi xóa tag và thêm mới tag
    $history = SaleOrderHistory::where('order_id', $saleOrderId)->first();
    $this->assertEquals($message, $history->message);
});

// Kiểm tra lưu thành công khi update multiple tag
test('Update Success - Multiple Tag', function () {
    for ($i = 0; $i < 10; $i++) {
        Tag::factory()->create(['id' => $i + 1]);
    }

    $tags = Tag::get()->pluck('name', 'id');

    $saleOrder = SaleOrder::factory()->create(['id' => 8, 'tag' => '6,12']);
    $saleOrderId = $saleOrder->id;

    $newTags = [6, 7, 8];
    $currentTags = !empty($saleOrder->tag) ? explode(',', $saleOrder->tag) : [];

    $addTags = [];
    $removeTags = [];
    foreach ($newTags as $tagId) {
        if (!isset($tags[$tagId])) {
            continue;
        }
        if (!in_array($tagId, $currentTags)) {
            $addTags[] = $tags[$tagId];
        }
    }
    foreach ($currentTags as $tagId) {
        if (!isset($tags[$tagId])) {
            continue;
        }
        if (!in_array($tagId, $newTags)) {
            $removeTags[] = $tags[$tagId];
        }
    }

    $message = '';
    if (count($addTags) > 0) {
        $message = 'Add new tag "';
        $message .= implode(', ', $addTags);
        $message .= '"';
    }
    if (count($removeTags) > 0) {
        $message .= $message ? '\nRemove tag "' : 'Remove tag "';
        $message .= implode(', ', $removeTags);
        $message .= '"';
    }

    $params = [
        'tag' => $newTags,
        'id' => $saleOrderId,
    ];

    // Dữ liệu truyền lên dạng mảng, lưu db dạng string (json) [1, 2] -> 1,2
    $response = $this->put("/api/sale-order/$saleOrderId/tag", $params);

    $data = json_decode($response->getContent(), true);
    $response->assertStatus(200);
    expect($data)->toHaveKeys(['message']);
    $data = $data['data'];

    $this->assertDatabaseHas('sale_order', [
        'id' => $saleOrderId,
        'tag' => $data['tag'],
    ]);

    // Lưu lại lịch sử thay đổi khi xóa tag và thêm mới tag
    $history = SaleOrderHistory::where('order_id', $saleOrderId)->first();
    $this->assertEquals($message, $history->message);
});
