<?php

use App\Models\Employee;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    Storage::fake('s3');
    ['accessToken' => $accessToken, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->user = $user;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/sale-order-item-image/update-art-file';
    $this->employee = Employee::factory()->create();
    $this->productPrintSide = \App\Models\ProductPrintSide::factory()->create([
        'name' => 'Front',
        'code' => 0,
        'code_name' => 'front',
        'code_wip' => 'F',
    ]);
    $this->saleOrder = SaleOrder::factory()->create([
        'order_status' => 'new_order',
    ]);
    $orderId = $this->saleOrder->id;
    $imageUrl = 'https:\/\/thoitrangmantis.com\/print-file.jpg';
    $this->imageUrl = 'https:\/\/thoitrangmantis.com\/print-file.jpg';
    $this->orderItem = SaleOrderItem::factory()
        ->has(SaleOrderItemImage::factory()->state(function (array $attributes, SaleOrderItem $orderItem) use ($orderId, $imageUrl) {
            return [
                'order_item_id' => $orderItem->id,
                'order_id' => $orderId,
                'print_side' => 0,
                'thumb_750' => 1,
                'image_url' => $imageUrl,
                'upload_s3_status' => 1,
                'thumb_250' => 1,
                'skip_retry' => 1,
                'sku' => 'MTk3MTc4Nw==vdYOGT1B00M',
                'retry_download_manual_count' => 5,
                'retry_detect_color_count' => 1,
                'last_retry_detect_color' => '2022-09-07 12:14:59',
                'retry_count' => 1,
            ];
        }), 'images')
        ->has(SaleOrderItemBarcode::factory()->state(function (array $attributes, SaleOrderItem $orderItem) use ($orderId) {
            return [
                'order_item_id' => $orderItem->id,
                'order_id' => $orderId,
                'sku' => 'MTk3MTc4Nw==vdYOGT1B00M'
            ];
        }), 'barcodes')
        ->create([
            'order_id' => $orderId,
            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/thoitrangmantis.com\/print-file.jpg"},{"name":"PreviewFiles.Front","value":"https:\/\/thoitrangmantis.com\/preview-file.jpg"}]'
        ]);
    $this->params = [
        'order_item_id' => $this->orderItem->id,
        'employee_id' => $this->employee->id,
        'print_file' => 'https://thoitrangmantis.com/wp-content/uploads/2019/01/ao-phong-co-tron-meo-3d1-tieu.jpg',
        'preview_file' => 'https://thuthuatnhanh.com/wp-content/uploads/2022/08/ao-thun-in-hinh-theo-yeu-cau.jpg',
        'print_side' => 'Front',
    ];
});

// Invalid data
test('Missing param: order_item_id, employee_id, print_file, print_side.', function () {
    $this->params = [];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => ['The employee id field is required.'],
            'order_item_id' => ['The order item id field is required.'],
            'print_side' => ['The print side field is required.'],
        ]
    ]);
});

// Invalid data
test('Param invalid: order_item_id, employee_id not found.', function () {
    $this->params['order_item_id'] = 999;
    $this->params['employee_id'] = 999;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => ['The selected employee id is invalid.'],
            'order_item_id' => ['The selected order item id is invalid.'],
        ]
    ]);
});

// Invalid data
test('Param invalid: print_file, preview_file is not url, print_side is not string.', function () {
    $this->params['print_file'] = 999;
    $this->params['preview_file'] = 999;
    $this->params['print_side'] = 999;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'print_file' => [
                'The print file must be a string.',
                'The print file must be a valid URL.',
            ],
            'preview_file' => [
                'The preview file must be a string.',
                'The preview file must be a valid URL.',
            ],
            'print_side' => [
                'The print side must be a string.'
            ],
        ]
    ]);
});

test('Update success: Item has been printed.', function () {
    SaleOrderItemBarcode::where('order_item_id', $this->orderItem->id)->update(['printed_at' => '2023-02-20 07:32:51']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertDatabaseHas('sale_order_item', [
        'id' => $this->orderItem->id,
        'ink_color_status' => 0,
        'options' => json_encode([
            [
                'name' => 'PrintFiles.Front',
                'value' => $this->params['print_file'],
            ],
            [
                'name' => 'PreviewFiles.Front',
                'value' => $this->params['preview_file'],
            ],
        ]),
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_item_id' => $this->orderItem->id,
        'print_side' => 0,
        'image_url' => $this->params['print_file'],
        'color_new' => null,
        'thumb_750' => 0,
        'upload_s3_status' => 0,
        'thumb_250' => 0,
        'skip_retry' => 0,
        'retry_download_manual_count' => 0,
        'retry_detect_color_count' => 0,
        'last_retry_detect_color' => null,
        'retry_count' => 0,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'type' => SaleOrderHistory::UPDATE_ART_FILE_TYPE,
    ]);
});

test('Update fail: Print side invalid.', function () {
    $this->params['print_side'] = 'Back';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'status' => false,
        'code' => 404,
        'message' => 'Order item not found.',
    ]);
});

test('Update success.', function () {
    SaleOrderItemImage::query()->update(['print_side' => 0]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'status' => true,
        'code' => 200,
        'message' => 'Update successfully.',
    ]);
    $this->assertDatabaseHas('sale_order_item', [
        'id' => $this->orderItem->id,
        'ink_color_status' => 0,
        'options' => json_encode([
            [
                'name' => 'PrintFiles.Front',
                'value' => $this->params['print_file'],
            ],
            [
                'name' => 'PreviewFiles.Front',
                'value' => $this->params['preview_file'],
            ],
        ]),
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_item_id' => $this->orderItem->id,
        'print_side' => 0,
        'image_url' => $this->params['print_file'],
        'color_new' => null,
        'thumb_750' => 0,
        'upload_s3_status' => 0,
        'thumb_250' => 0,
        'skip_retry' => 0,
        'retry_download_manual_count' => 0,
        'retry_detect_color_count' => 0,
        'last_retry_detect_color' => null,
        'retry_count' => 0,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'type' => SaleOrderHistory::UPDATE_ART_FILE_TYPE,
    ]);
});

test('Update success - only update print file.', function () {
    unset($this->params['preview_file']);
    $saleOrderItem = SaleOrderItem::find($this->orderItem->id);
    $options = json_decode($saleOrderItem->options, true);
    $previewFile = null;
    foreach ($options as $key => $option) {
        $optionName = explode('.', $option['name']);
        if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PREVIEW_FILES && !empty($optionName[1]) && $optionName[1] == $this->params['print_side']) {
            $previewFile = $option['value'];
        }
    }
    SaleOrderItemImage::query()->update(['print_side' => 0]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'status' => true,
        'code' => 200,
        'message' => 'Update successfully.',
    ]);

    $this->assertDatabaseHas('sale_order_item', [
        'id' => $this->orderItem->id,
        'ink_color_status' => 0,
        'options' => json_encode([
            [
                'name' => 'PrintFiles.Front',
                'value' => $this->params['print_file'],
            ],
            [
                'name' => 'PreviewFiles.Front',
                'value' => $previewFile,
            ],
        ]),
    ]);
    $this->assertDatabaseHas('sale_order_item_image', [
        'order_item_id' => $this->orderItem->id,
        'print_side' => 0,
        'image_url' => $this->params['print_file'],
        'color_new' => null,
        'thumb_750' => 0,
        'upload_s3_status' => 0,
        'thumb_250' => 0,
        'skip_retry' => 0,
        'retry_download_manual_count' => 0,
        'retry_detect_color_count' => 0,
        'last_retry_detect_color' => null,
        'retry_count' => 0,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'type' => SaleOrderHistory::UPDATE_ART_FILE_TYPE,
    ]);
});

test('Update success - only update preview file.', function () {
    unset($this->params['print_file']);
    $saleOrderItem = SaleOrderItem::find($this->orderItem->id);
    $options = json_decode($saleOrderItem->options, true);
    $printFile = null;
    foreach ($options as $key => $option) {
        $optionName = explode('.', $option['name']);
        if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PRINT_FILES && !empty($optionName[1]) && $optionName[1] == $this->params['print_side']) {
            $printFile = $option['value'];
        }
    }
    SaleOrderItemImage::query()->update(['print_side' => 0]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        'status' => true,
        'code' => 200,
        'message' => 'Update successfully.',
    ]);
    $newItem = SaleOrderItem::find($this->orderItem->id);
    $this->assertDatabaseHas('sale_order_item', [
        'id' => $this->orderItem->id,
        'options' => json_encode([
            [
                'name' => 'PrintFiles.Front',
                'value' => $printFile,
            ],
            [
                'name' => 'PreviewFiles.Front',
                'value' => $this->params['preview_file'],
            ],
        ]),
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'type' => SaleOrderHistory::UPDATE_ART_FILE_TYPE,
    ]);
});
