<?php

use App\Models\Shipment;
use App\Models\ShipmentLabelPrinted;
use App\Models\ShipmentManifest;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use App\Models\ShippingIntegration;
use App\Models\Warehouse;
use App\Models\ShippingIntegrationAccount;
use Carbon\Carbon;
use App\Http\Service\ShipmentService;

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouses = Warehouse::factory()->create(
        [
            'name' => "San Jose",
            'time_zone' => "America/Los_Angeles",
        ]);
    $this->store = \App\Models\Store::factory()->create([
        'name' => 'test'
    ]);
    Shipment::factory()->create([
        'created_at' => now()->subDays(1)
    ]);
    $this->ShippingIntegration = ShippingIntegration::factory()->create([
        'api_key' => 'EZTKf2715877b4f24598b2dd60da5c013767vS05QNSeRhLsvL80K0v9GA',
        'status' => 1,
        'is_default' => 1,
        'partner' => 'easypost',
    ]);
    $this->shippingIntegrationAccount = ShippingIntegrationAccount::factory()->create([
        'integration_id' => $this->ShippingIntegration->id,
        'carrier_id' => 1,
        'name' => 'swiftpod USPS',
        'carrier_account' => 'ca_00b626926efb4f5c89e01901d9fe8df4',
    ]);
    $this->shippingCarrierService = ShippingCarrierService::factory()->create();
    $this->saleOrders = App\Models\SaleOrder::factory()->count(1)->sequence([
        'order_type' => 6,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouses->id,
    ])
        ->has(Shipment::factory()->count(2)->sequence([
            'warehouse_id' => $this->warehouses->id,
            'tracking_status' => Shipment::UNKNOWN,
            'carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE,
            'provider' => Shipment::PROVIDER_EASYPOST,
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
            'account_shipping_easypost' => 'ca_00b626926efb4f5c89e01901d9fe8df4',
        ],
            [
                'warehouse_id' => $this->warehouses->id,
                'tracking_status' => Shipment::UNKNOWN,
                'carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE,
                'provider' => Shipment::PROVIDER_EASYPOST,
                'store_id' => $this->store->id,
                'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD_STORE,
                'account_shipping_easypost' => 'ca_00b626926efb4f5c89e01901d9fe8df4',
            ])
            ->has(ShipmentLabelPrinted::factory([
                'printed_date' => now('America/Los_Angeles')->setHour(12)->setMinute(40)->setSecond(0)
            ]), 'shipmentLabelPrinted')
            ->has(\App\Models\ShipmentEasypost::factory([
                'easypost_id' => 'shp_83590bfcfd464de683e02ad432683471'
            ]), 'shipmentEasypost')
            ->has(ShippingIntegrationAccount::factory([
                'carrier_account' => 'ca_00b626926efb4f5c89e01901d9fe8df4'
            ]), 'shippingIntegrationAccount')
        , 'shipment')->create();
    echo ($this->saleOrders[0]->shipment[0]->shipmentLabelPrinted[0]->printed_date);
});


test('Create success - shipment invalid, fake run time at 12h.', function () {
    $dataResponse = new \stdClass();
    $dataResponse->id = 1;
    $dataResponse->state = 'purchased';
    ShipmentLabelPrinted::query()->update(['printed_date' => now('America/Los_Angeles')->subDays(10)]);
    $this->mock( ShipmentService::class, function (\Mockery\MockInterface $mock) use($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('makeScanForm')->andReturn($dataResponse);
    })->makePartial();
    Carbon::setTestNowAndTimezone(now()->setHour(12), 'America/Los_Angeles');
    $this->artisan('command:scan-form-tiktok-order-in-a-day');
    $this->assertDatabaseCount('shipment_manifest', 0);
    $this->assertDatabaseCount('shipment_manifest_tracking', 0);

});

test('Create success - shipment invalid, current hour != 12.', function () {
    $dataResponse = new \stdClass();
    $dataResponse->id = 1;
    $dataResponse->state = 'purchased';
    ShipmentLabelPrinted::query()->update(['printed_date' => now('America/Los_Angeles')->subDays(10)]);
    $this->mock(ShipmentService::class, function (\Mockery\MockInterface $mock) use($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('makeScanForm')->andReturn($dataResponse);
    })->makePartial();
    $this->artisan('command:scan-form-tiktok-order-in-a-day');
    $this->assertDatabaseCount('shipment_manifest', 0);
    $this->assertDatabaseCount('shipment_manifest_tracking', 0);

});

test('Create success - shipment valid, fake run time at 12h.', function () {
    $dataResponse = new \stdClass();
    $dataResponse->id = 1;
    $dataResponse->state = 'purchased';
    $this->mock( ShipmentService::class, function (\Mockery\MockInterface $mock) use($dataResponse) {
        $mock->shouldReceive('makeScanForm')->once()->andReturn($dataResponse);
    })->makePartial();
    Carbon::setTestNowAndTimezone(Carbon::now('America/Los_Angeles')->setHour(12), 'America/Los_Angeles');
    $this->artisan('command:scan-form-tiktok-order-in-a-day');
    $this->assertDatabaseHas('shipment_manifest', [
        'batch_id_easypost' => $dataResponse->id,
        'status' => ShipmentManifest::STATUS_GENERATING,
        'state_batch' => $dataResponse?->state,
    ]);
    $this->assertDatabaseCount('shipment_manifest_tracking', 2);
    $manifest = ShipmentManifest::where('batch_id_easypost', $dataResponse->id)->first();
    $this->assertDatabaseHas('shipment_manifest_tracking', [
        'manifest_id' => $manifest->id,
    ]);


})->skip();

test('Create success - shipment valid, current hour != 12.', function () {
    $dataResponse = new \stdClass();
    $dataResponse->id = 1;
    $dataResponse->state = 'purchased';
    $this->mock(ShipmentService::class, function (\Mockery\MockInterface $mock) use($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('makeScanForm')->andReturn($dataResponse);
    })->makePartial();
    Carbon::setTestNowAndTimezone(Carbon::now('America/Los_Angeles')->setHour(10), 'America/Los_Angeles');
    $this->artisan('command:scan-form-tiktok-order-in-a-day');
    $this->assertDatabaseCount('shipment_manifest', 0);
    $this->assertDatabaseCount('shipment_manifest_tracking', 0);
});


