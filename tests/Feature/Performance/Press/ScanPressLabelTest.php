<?php

use App\Models\Employee;
use App\Models\PressLog;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\TimeTracking;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->store = Store::factory([
        'code' => 'TEST',
        'name' => 'TEST_STORE',
    ])->create();

    $this->color = ProductColor::factory([
        'name' => 'WHITE',
        'sku' => 'WHITE',
        'color_code' => '#ffffff'
    ])->create();

    $this->product = Product::factory([
        'name' => '1234 / WHITE / S',
        'sku' => 'UNPT9C0XS',
        'color' => 'WHITE'
    ])->create();
    $this->productStyle = ProductStyle::factory([
        'sku' => '3001',
        'name' => '3001',
        'type' => 'Tee',
    ])->create();

    $this->timeTrackingPress = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => TimeTracking::PRESS_JOB_TYPE,
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);

    $this->barcodeLabel = '021622-SJ-M-000505-4';

    $this->saleOrder = SaleOrder::factory([
        'id' => 1,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'sku' => $this->product->sku,
        'warehouse_id' => $this->warehouse->id,
        'print_sides' => 'FB',
        'options' => json_encode([
            [
                'name' => 'PrintFiles.Front',
                'value' => 'https://2st.s3.us-east-2.amazonaws.com/Design/PancreaticCancerAwarenessUnisexTshirtInThisFamilyNobodyFightsAloneSurvivorFighterGiftAwarenessMonth.png'
            ],
            [
                'name' => 'PreviewFiles.Front',
                'value' => 'https://i.etsystatic.com/24906590/r/il/2341c6/3145734374/il_fullxfull.3145734374_gcm9.jpg'
            ]
        ]),
        'product_style_sku' => $this->productStyle->sku,
    ])->has(SaleOrderItemBarcode::factory([
        'order_id' => 1,
        'sku' => $this->product->sku,
        'warehouse_id' => $this->warehouse->id,
        'label_id' => $this->barcodeLabel,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'is_deleted' => 0,
        'store_id' => $this->store->id,
    ]), 'barcodes'), 'items')
        ->create();

    // Fake 2 print sides
    $this->productPrintSide = ProductPrintSide::factory()->createMany([
        [
            'name' => 'Front',
            'code' => 0,
            'code_name' => 'front',
            'code_wip' => 'F',
            'order' => 1
        ],
        [
            'name' => 'Back',
            'code' => 1,
            'code_name' => 'back',
            'code_wip' => 'B',
            'order' => 2
        ]
    ]);

    // Fake 2 print areas
    $this->productPrintArea = ProductPrintArea::factory()->createMany([
        [
            'name' => $this->productPrintSide->first()->name,
            'product_style_id' => $this->productStyle->id,
            'print_method' => PrintMethod::DTG,
        ],
        [
            'name' => $this->productPrintSide->last()->name,
            'product_style_id' => $this->productStyle->id,
            'print_method' => PrintMethod::DTF,
        ]
    ]);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/press';
    $this->params = [
        'label' => $this->barcodeLabel,
        'id_time_checking' => [$this->timeTrackingPress->id],
        'employee_id' => $this->employee->id,
    ];
});

test('Press performance fail, label param is required.', function () {
    unset($this->params['label']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'label' => [
                'The label field is required.'
            ]
        ]
    ]);
});

test('Press performance fail, employee_id is required.', function () {
    unset($this->params['employee_id']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'The employee id field is required.'
            ]
        ]
    ]);
});

test('Press performance fail, id_time_checking is required.', function () {
    unset($this->params['id_time_checking']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'id_time_checking' => [
                'The id time checking field is required.'
            ]
        ]
    ]);
});

test('Press performance fail, all label sides are pressed.', function () {
    PressLog::factory()->createMany([
        [
            'label_id' => $this->barcodeLabel,
            'employee_id' => $this->employee->id
        ],
        [
            'label_id' => $this->barcodeLabel,
            'employee_id' => $this->employee->id
        ]
    ]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => [
            'Error! Invalid Label ID.'
        ],
    ]);
});

test('Press performance successfully.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);

    $resKeys = [
        'id',
        'order_item_id',
        'sku',
        'options',
        'print_side',
        'name',
        'label_id',
        'order_id',
        'is_fba_order',
        'order_date',
        'dtg_print_sides',
        'pressed_times',
        'total_pressed_times',
        'store_code'
    ];
    expect($dataResponse['data'])->toHaveKeys($resKeys);

    // Time tracking Press quantity must be +1
    $this->assertDatabaseHas('time_tracking', [
        'employee_id' => $this->employee->id,
        'quantity' => $this->timeTrackingPress->quantity + 1,
        'job_type' => TimeTracking::PRESS_JOB_TYPE
    ]);

    $this->assertDatabaseHas('sale_order_item_barcode', [
        'label_id' => $this->params['label'],
        'employee_press_id' => $this->employee->id,
    ]);

    $this->assertDatabaseHas('sale_order_history', [
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_PRESS_TYPE,
        'message' => 'Label ID ' . $this->params['label'] . ' has been pressed 1/' . $this->productPrintSide->count() . ' times.',
    ]);

    // Update production status Job
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_UPDATE_PRODUCTION_STATUS,
        'attempts' => 0,
    ]);

    // Add records to press log
    $this->assertDatabaseHas('press_logs', [
        'label_id' => $this->params['label'],
        'employee_id' => $this->employee->id
    ]);
});
