<?php

use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\Product;
use App\Models\StoreStyle;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;

    $this->endpoint = '/api/store-product/set-status/product/';
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);


    $this->store = Store::factory()->create();
    $this->store2 = Store::factory()->create();

    $this->product = Product::factory([
        'sku' => 'UNPT9C0033',
        "style" => "5001",
        'color' => 'BLACK',
    ])
        ->has(StoreStyle::factory([
            'store_id' => $this->store->id,
        ]), 'storeStyle')
        ->for(Brand::factory([
            'name' => 'brand_name_sub',
        ]), 'brand')
        ->has(StoreProduct::factory()->count(3)->sequence(
            [
                'store_id' => $this->store->id,
                'status' => 1
            ],
            [
                'store_id' => $this->store2->id,
                'status' => 1
            ]), 'storeProducts')
        ->create();

    $this->params = [
        "status" => "",
        "store_id" => "",
    ];
});

// miss param
test('change status failed - miss param', function () {
    unset($this->params['status']);
    unset($this->params['store_id']);
    $response = $this->post($this->endpoint. "123", $this->params);
    $response->assertStatus(422);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray([
        'store_id' => [
            'The store id field is required.'
        ],
        'status' => [
            'The status field is required.'
        ],
    ]);
});

// store not exists
test('change status failed - store not exists', function () {
    $this->params['store_id'] = 123;
    $this->params['status'] = true;
    $response = $this->post($this->endpoint. "123", $this->params);
    $response->assertStatus(422);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray([
        'store_id' => [
            'The selected store id is invalid.'
        ],
    ]);
});

// sku not in store
test('change status failed - store has not sku', function () {
    $this->params['store_id'] = $this->store->id;
    $this->params['status'] = false;
    $sku = 'UNPT3123132';
    $response = $this->post($this->endpoint. $sku, $this->params);
    $response->assertStatus(422);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['success'])->toEqual(false);
});

test('change status success', function () {
    $this->params['store_id'] = $this->store->id;
    $this->params['status'] = false;
    $sku = $this->product->sku;
    $response = $this->post($this->endpoint. $sku, $this->params);
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['success'])->toEqual(true);
    $this->assertDatabaseHas('store_product_price', ['product_id' => $this->product->id, 'store_id' => $this->params['store_id'], 'status' => $this->params['status']]);
    $this->assertDatabaseHas('store_product_price', ['product_id' => $this->product->id, 'store_id' => $this->store2->id, 'status' => !$this->params['status']]);
});
