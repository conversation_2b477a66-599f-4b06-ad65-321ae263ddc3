<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderOnHold;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->user = $user;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->store = Store::factory()->createMany([
        [
            'is_active' => Store::STATUS_ACTIVE,
            'code' => 'S1',
            'is_on_hold' => Store::IS_NOT_ON_HOLD,
        ],
        [
            'is_active' => Store::STATUS_ACTIVE,
            'code' => 'S2',
            'is_on_hold' => Store::IS_ON_HOLD,

        ],
    ]);

    $this->endpoint = '/api/store/store-info';
    $this->orders = SaleOrder::factory()->createMany([
        [
            'order_status' => SaleOrder::STATUS_NEW_ORDER,
            'store_id' => $this->store[0]->id,
        ],
        [
            'order_status' => SaleOrder::ON_HOLD,
            'store_id' => $this->store[0]->id,
        ],
    ]);
    $this->params = [
        'id' => $this->store[0]->id,
        'name' => 'Store 1',
        'company' => '324431',
        'country' => 'US',
        'street1' => '213',
        'street2' => '324',
        'city' => '34',
        'state' => 'DE',
        'zip' => '123',
        'phone' => '3242424',
        'email' => null,
        'contact_name' => 'Huong',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '************',
        'account_id' => 1,
        'is_active' => true,
        'is_on_hold' => 1,
        'template_neck' => 0,
        'code' => 'TEST1',
        'client_id' => 1,
        'payment_terms' => 1,
    ];
});

test('update fail - missing id, name', function () {
    unset($this->params['id'], $this->params['name']);
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => [
            'The id field is required.'
        ],
        'name' => [
            'The name field is required.'
        ]
    ]);
})->skip();
test('update fail - invalid format billing email', function () {
    $this->params['billing_email'] = 'email@gmailcom/12345@gmailcom';
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'billing_email' => [
            'The billing email format is invalid'
        ],
    ]);
});

test('update fail - invalid id, code', function () {
    $this->params['id'] = 'abc';
    $this->params['code'] = 'S2';
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => [
            'The selected id is invalid.'
        ],
        'code' => [
            'The code has already been taken.'
        ]
    ]);
})->skip();

test('update success - not on hold to on hold', function () {
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('store', [
        'id' => $this->store[0]->id,
        'name' => 'Store 1',
        'company' => '324431',
        'country' => 'US',
        'street1' => '213',
        'street2' => '324',
        'city' => '34',
        'state' => 'DE',
        'zip' => '123',
        'phone' => '3242424',
        'email' => null,
        'contact_name' => 'Huong',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '************',
        'account_id' => 1,
        'is_active' => true,
        'is_on_hold' => Store::IS_ON_HOLD,
        'template_neck' => 0,
        'code' => 'TEST1',
    ]);
    $this->assertDatabaseHas('store_on_hold_history', [
        'store_id' => $this->store[0]->id,
        'user_id' => $this->user->id,
        'type' => Store::IS_ON_HOLD,
    ]);
    $orderOnHolds = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::ON_HOLD)->count();
    $this->assertEquals(2, $orderOnHolds);
    $newOrders = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::NEW_ORDER)->count();
    $this->assertEquals(0, $newOrders);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $this->orders[0]->id,
        'user_id' => $this->user->id,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->orders[0]->id,
        'user_id' => $this->user->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => "Order status has been changed from 'new_order' to 'on_hold' due to the store being on hold.",
    ]);
})->skip();

test('update success - on hold to not on hold', function () {
    $this->params['is_on_hold'] = Store::IS_NOT_ON_HOLD;
    Store::where('id', $this->store[0]->id)->update(['is_on_hold' => Store::IS_ON_HOLD]);
    \App\Models\StoreOnHoldHistory::create([
        'store_id' => $this->store[0]->id,
        'user_id' => $this->user->id,
        'type' => Store::IS_ON_HOLD,
    ]);
    SaleOrderOnHold::create([
        'order_id' => $this->orders[1]->id,
        'user_id' => $this->user->id,
        'store_on_hold' => 1,
        'is_deleted' => 0,
    ]);
    SaleOrder::where('store_id', $this->store[0]->id)->update(['order_status' => SaleOrder::ON_HOLD]);
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(200);

    $this->assertDatabaseHas('store', [
        'id' => $this->store[0]->id,
        'name' => 'Store 1',
        'company' => '324431',
        'country' => 'US',
        'street1' => '213',
        'street2' => '324',
        'city' => '34',
        'state' => 'DE',
        'zip' => '123',
        'phone' => '3242424',
        'email' => null,
        'contact_name' => 'Huong',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '************',
        'account_id' => 1,
        'is_active' => true,
        'is_on_hold' => Store::IS_NOT_ON_HOLD,
        'template_neck' => 0,
        'code' => 'TEST1',
    ]);
    $this->assertDatabaseHas('store_on_hold_history', [
        'store_id' => $this->store[0]->id,
        'user_id' => $this->user->id,
        'type' => Store::IS_ON_HOLD,
    ]);
    $orderOnHolds = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::ON_HOLD)->count();
    $this->assertEquals(1, $orderOnHolds);
    $newOrders = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::NEW_ORDER)->count();
    $this->assertEquals(1, $newOrders);
    $this->assertDatabaseCount('sale_order_history', 1);
})->skip();

test('update success - not update on hold', function () {
    $this->params['is_on_hold'] = Store::IS_NOT_ON_HOLD;
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('store', [
        'id' => $this->store[0]->id,
        'name' => 'Store 1',
        'company' => '324431',
        'country' => 'US',
        'street1' => '213',
        'street2' => '324',
        'city' => '34',
        'state' => 'DE',
        'zip' => '123',
        'phone' => '3242424',
        'email' => null,
        'contact_name' => 'Huong',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '************',
        'account_id' => 1,
        'is_active' => true,
        'is_on_hold' => Store::IS_NOT_ON_HOLD,
        'template_neck' => 0,
        'code' => 'TEST1',
    ]);
    $orderOnHolds = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::ON_HOLD)->count();
    $this->assertEquals(1, $orderOnHolds);
    $newOrders = SaleOrder::where('store_id', $this->store[0]->id)->where('order_status', SaleOrder::NEW_ORDER)->count();
    $this->assertEquals(1, $newOrders);
})->skip();
