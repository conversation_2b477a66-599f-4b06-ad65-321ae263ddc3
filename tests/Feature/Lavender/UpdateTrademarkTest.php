<?php

use App\Models\Ownership;
use App\Models\Trademark;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create([
        'is_lavender_editor' => 1,
        'password' => bcrypt('123456')
    ]);
    $response = $this->post('/api/lavender/login', [
        'username' => $this->user->username,
        'password' => '123456',
    ]);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . json_decode($response->getContent(), true)['access_token'],
        'Accept' => 'application/json',
    ]);
    $this->ownership = Ownership::factory()->createMany([
        ['created_by' => $this->user->id, 'name' => 'Disney'],
        ['created_by' => $this->user->id, 'name' => 'Nintendo'],

    ]);
    $this->trademark = Trademark::factory()->create(
        [
            'name' => 'Mickey',
            'type' => 'character',
            'ownership_id' => $this->ownership[0]->id,
        ],
    );
    $this->endpoint = '/api/trademark/' . $this->trademark->id;

    $this->params = [
        'name' => 'Mickey 1',
        'type' => 'character',
        'ownership_id' => $this->ownership[0]->id,
        'is_active' => 1,
    ];
});

test('Update fail - Missing param', function () {
    unset($this->params['name'], $this->params['type'], $this->params['ownership_id'], $this->params['is_active']);
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'The name field is required.'
            ],
            'type' => [
                'The type field is required.'
            ],
            'is_active' => [
                'The is active field is required.'
            ]
        ]
    ]);
});

test('Update fail - ownership id not exists in ownership table', function () {
    $this->params['ownership_id'] = $this->ownership[0]->id + 10000;
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'ownership_id' => [
                'The selected ownership is invalid.'
            ]
        ]
    ]);
});

test('Update fail - name already exist.', function () {
    Trademark::factory()->create($this->params);

    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'Error! Duplicate Trademark.'
            ],
        ]
    ]);
})->skip('no need to test this case.');

test('Update success.', function () {
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('trademarks', $this->params);
});
