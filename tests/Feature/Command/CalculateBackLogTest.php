<?php

use App\Console\Commands\CalculateBackLog;
use App\Models\Employee;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\ShipmentItem;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::factory()->create();
    $this->employee = Employee::factory()->create([
        'department' => 'Printing',
    ]);
    $this->ordersPulled = SaleOrder::factory()->count(5)->sequence(
        [
            'order_number' => '100422-SJ-S-000072',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000066',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000068',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
        ],
        [
            'order_number' => '100422-SJ-S-000058',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::REJECTED,
        ],
        [
            'order_number' => '100422-SJ-S-000055',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::CANCELLED,
        ],
    )
        ->has(SaleOrderItemBarcode::factory()->count(3)->sequence(
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'employee_pull_id' => $this->employee->id,
                'pulled_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'is_deleted' => 1,
                'employee_pull_id' => $this->employee->id,
                'pulled_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'MUGS',
                'employee_pull_id' => $this->employee->id,
                'pulled_at' => now(),

            ],
        ), 'barcodeItems')
        ->create();

    $this->ordersPrinted = SaleOrder::factory()->count(5)->sequence(
        [
            'order_number' => '100422-SJ-S-000061',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000062',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000063',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
        ],
        [
            'order_number' => '100422-SJ-S-000064',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::REJECTED,
        ],
        [
            'order_number' => '100422-SJ-S-00065',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::CANCELLED,
        ],
    )
        ->has(SaleOrderItemBarcode::factory()->count(4)->sequence(
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'employee_print_id' => $this->employee->id,
                'printed_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'employee_print_id' => $this->employee->id,
                'printed_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'is_deleted' => 1,
                'employee_print_id' => $this->employee->id,
                'printed_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'MUGS',
                'employee_print_id' => $this->employee->id,
                'printed_at' => now(),

            ],
        ), 'barcodeItems')
        ->create();

    $this->ordersQC = SaleOrder::factory()->count(6)->sequence(
        [
            'order_number' => '100422-SJ-S-000066',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000067',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000068',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
        ],
        [
            'order_number' => '100422-SJ-S-000069',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::STATUS_IN_PRODUCTION_CANCELLED,
        ],
        [
            'order_number' => '100422-SJ-S-000070',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::REJECTED,
        ],
        [
            'order_number' => '100422-SJ-S-000071',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::CANCELLED,
        ],
    )
        ->has(SaleOrderItemBarcode::factory()->count(4)->sequence(
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'employee_qc_id' => $this->employee->id,
                'qc_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'employee_qc_id' => $this->employee->id,
                'qc_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'DTG',
                'is_deleted' => 1,
                'employee_qc_id' => $this->employee->id,
                'qc_at' => now(),

            ],
            [
                'warehouse_id' => $this->warehouse->id,
                'print_method' => 'MUGS',
                'employee_qc_id' => $this->employee->id,
                'qc_at' => now(),

            ],
        ), 'barcodeItems')
        ->create();

    $this->ordersShipping = SaleOrder::factory()->create([
        'order_number' => '100422-SJ-S-000090',
        'warehouse_id' => $this->warehouse->id,
        'order_folding_status' => 0,
        'order_status' => SaleOrder::IN_PRODUCTION,
        'order_quantity' => 2,
    ]);
    $shipment = \App\Models\Shipment::factory()->create([
        'order_id' => $this->ordersShipping->id,
        'employee_printed_id' => $this->employee->id,
    ]);
    \App\Models\ShipmentLabelPrinted::factory()->create([
        'shipment_id' => $shipment->id,
        'created_at' => now()->subDay()->toDateTimeString(),
    ]);

    $this->ordersShippingInvalid = SaleOrder::factory()->count(1)->sequence(
        [
            'order_number' => '100422-SJ-S-000091',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::IN_PRODUCTION,
            'order_quantity' => 2,
        ],
    )->has(SaleOrderItem::factory()->count(2)->sequence(
        [
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
        [
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
    ), 'items')->create();
    ShipmentItem::factory()->create([
        'order_id' => $this->ordersShippingInvalid[0]->id,
        'quantity' => 1,
    ]);
});

test('Calculate success', function () {
    $deductionJob = new CalculateBackLog();
    $deductionJob->handle();
    $cacheBackLog = Cache::store(config('cache.redis_store'))->get('CACHE_BACKLOG');
    $this->assertEquals($cacheBackLog, [
        $this->warehouse->id => [
            'printing' => 2,
            'quality_control' => 6,
            'press' => 6,
            'create_shipment_label' => 9,
            'folding' => 1
        ]
    ]);
});
