<?php

use App\Console\Commands\UpdateProductQuantityInventory;
use App\Models\OutOfStockOrderLog;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\Warehouse;
use App\Repositories\InventoryRepository;
use Illuminate\Console\OutputStyle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Console\Input\StringInput;
use Symfony\Component\Console\Output\BufferedOutput;

uses(RefreshDatabase::class);

// Kịch bản: ProductQuantity.quantity >= 0
// Kết quả: Log trong OutOfStockOrderLog bị xóa.
test('CheckSecondaryAlert_QuantityIsRestocked_DeletesLog', function () {
    $product = Product::factory()->create();
    $warehouse = Warehouse::factory()->create();
    ProductQuantity::factory()->create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
        'quantity' => 10,
    ]);

    $log = OutOfStockOrderLog::factory()->create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
    ]);

    $mockInventoryRepo = Mockery::mock(InventoryRepository::class);
    $this->app->instance(InventoryRepository::class, $mockInventoryRepo);
    $mockCommand = Mockery::mock(UpdateProductQuantityInventory::class)->makePartial();
    $mockCommand->shouldReceive('getOpenOrdersCount')->andReturn(0);
    $this->app->instance(UpdateProductQuantityInventory::class, $mockCommand);

    $mockCommand->checkAndSendSecondaryOutOfStockAlert();

    expect(OutOfStockOrderLog::find($log->id))->toBeNull();
});

// Kịch bản: Có log và đơn hàng mới tạo.
// Kết quả: Gửi alert lần 2 và xóa log.
test('CheckSecondaryAlert_NewOrderExists_SendsAlertAndDeletesLog', function () {
    $product = Product::factory()->create();
    $warehouse = Warehouse::factory()->create();
    ProductQuantity::factory()->create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
        'quantity' => -5,
    ]);

    $log = OutOfStockOrderLog::factory()->create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
    ]);

    $orderItem = SaleOrderItem::factory()->create([
        'product_id' => $product->id,
    ]);
    SaleOrderItemBarcode::factory()->create([
        'order_item_id' => $orderItem->id,
        'warehouse_id' => $warehouse->id,
        'is_deleted' => 0,
        'pulled_at' => null,
        'created_at' => now(),
    ]);

    $bufferedOutput = new BufferedOutput();
    $outputStyle = new OutputStyle(new StringInput(''), $bufferedOutput);

    $mockCommand = Mockery::mock(UpdateProductQuantityInventory::class)->makePartial();
    $mockCommand->shouldReceive('getOpenOrdersCount')->andReturn(1);
    $mockCommand->setOutput($outputStyle);
    $this->app->instance(UpdateProductQuantityInventory::class, $mockCommand);
    $mockCommand->checkAndSendSecondaryOutOfStockAlert();
    $result = $bufferedOutput->fetch();
    expect($result)->toContain('*Stock:*  -5');
    expect($result)->toContain('Open Orders (items):* 1');
    expect(OutOfStockOrderLog::find($log->id))->toBeNull();
});

// Kịch bản: SKU vẫn hết hàng nhưng chưa có đơn hàng mới.
// Kết quả: Không xóa log, không gửi alert.
test('CheckSecondaryAlert_NoNewOrders_DoesNothing', function () {
    $product = Product::factory()->create();
    $warehouse = Warehouse::factory()->create();

    ProductQuantity::factory()->create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
        'quantity' => -5,
    ]);

    $log = OutOfStockOrderLog::create([
        'product_id' => $product->id,
        'warehouse_id' => $warehouse->id,
    ]);
    $bufferedOutput = new BufferedOutput();
    $outputStyle = new OutputStyle(new StringInput(''), $bufferedOutput);
    $mockCommand = Mockery::mock(UpdateProductQuantityInventory::class)->makePartial();
    $mockCommand->shouldReceive('getOpenOrdersCount')->andReturn(0);
    $mockCommand->setOutput($outputStyle);
    $this->app->instance(UpdateProductQuantityInventory::class, $mockCommand);
    $mockCommand->checkAndSendSecondaryOutOfStockAlert();

    expect(OutOfStockOrderLog::find($log->id))->not()->toBeNull();
});

// Kịch bản: Có nhiều log.
// Kết quả: Xử lý từng log độc lập, không ảnh hưởng nhau.
test('CheckSecondaryAlert_MultipleLogs_AllHandledIndependently', function () {
    $warehouse = Warehouse::factory()->create();

    // Product A: sẽ có đơn hàng → nên được xóa log
    $productA = Product::factory()->create();
    ProductQuantity::factory()->create([
        'product_id' => $productA->id,
        'warehouse_id' => $warehouse->id,
        'quantity' => -5,
    ]);
    OutOfStockOrderLog::factory()->create([
        'product_id' => $productA->id,
        'warehouse_id' => $warehouse->id,
    ]);
    $orderItem = SaleOrderItem::factory()->create([
        'product_id' => $productA->id,
    ]);
    SaleOrderItemBarcode::factory()->create([
        'order_item_id' => $orderItem->id,
        'warehouse_id' => $warehouse->id,
        'pulled_at' => null,
        'is_deleted' => 0,
        'created_at' => now(),
    ]);

    // Product B: không có đơn → log vẫn giữ
    $productB = Product::factory()->create();
    ProductQuantity::factory()->create([
        'product_id' => $productB->id,
        'warehouse_id' => $warehouse->id,
        'quantity' => -3,
    ]);
    OutOfStockOrderLog::factory()->create([
        'product_id' => $productB->id,
        'warehouse_id' => $warehouse->id,
    ]);

    $mockInventoryRepo = Mockery::mock(InventoryRepository::class);
    app()->instance(InventoryRepository::class, $mockInventoryRepo);

    $bufferedOutput = new BufferedOutput();
    $outputStyle = new OutputStyle(new StringInput(''), $bufferedOutput);
    $mockCommand = Mockery::mock(UpdateProductQuantityInventory::class)->makePartial();
    $mockCommand->setOutput($outputStyle);
    $mockCommand->shouldReceive('getOpenOrdersCount')->with($productA->id, $warehouse->id)->andReturn(1);
    $mockCommand->shouldReceive('getOpenOrdersCount')->with($productB->id, $warehouse->id)->andReturn(0);

    $mockCommand->checkAndSendSecondaryOutOfStockAlert();

    // Product A: log đã bị xoá
    expect(OutOfStockOrderLog::where('product_id', $productA->id)->exists())->toBeFalse();
    // Product B: log vẫn còn
    expect(OutOfStockOrderLog::where('product_id', $productB->id)->exists())->toBeTrue();
});

// Kịch bản: Product không tồn tại.
// Kết quả: Log không bị xóa.
test('CheckSecondaryAlert_ProductDoesNotExist_SkipLog', function () {
    $warehouse = Warehouse::factory()->create();

    // Tạo log với product_id không tồn tại
    $log = OutOfStockOrderLog::factory()->create([
        'product_id' => 999999,
        'warehouse_id' => $warehouse->id,
    ]);

    $mockInventoryRepo = Mockery::mock(InventoryRepository::class);
    app()->instance(InventoryRepository::class, $mockInventoryRepo);
    $mockCommand = Mockery::mock(UpdateProductQuantityInventory::class)->makePartial();
    $mockCommand->checkAndSendSecondaryOutOfStockAlert();

    // Log không bị xóa
    expect(OutOfStockOrderLog::find($log->id))->not()->toBeNull();
});
