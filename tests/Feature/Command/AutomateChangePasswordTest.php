<?php

namespace Tests\Feature\Command;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Setting;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Tymon\JWTAuth\Facades\JWTAuth;
use Carbon\Carbon;

use function Pest\Laravel\artisan;

uses(RefreshDatabase::class);

beforeEach(function () {
  Storage::fake('public');
  QrCode::shouldReceive('size->generate')->andReturn('<svg>qr</svg>');
  
  DB::shouldReceive('table')
    ->with('setting')
    ->andReturnSelf();
  DB::shouldReceive('where')
    ->with('label', 'change_password_email_blocklist')
    ->andReturnSelf();
  DB::shouldReceive('value')
    ->with('value')
    ->andReturn('<EMAIL>');
});

it('throws error if status is invalid', function () {
  artisan('notify:automate-change-password invalid-status')
    ->expectsOutput('Invalid status. Use \'monthly\' or \'weekly\'.')
    ->assertExitCode(0);
});

it('updates password and sends email', function () {
  Mail::fake();
  $user = User::factory()->create([
    'username' => time() . 'testuser',
    'change_app_password_status' => 'weekly',
    'app_send_to' => '<EMAIL>,<EMAIL>',
  ]);

  Setting::create([
    'label' => 'change_password_email_blocklist',
    'value' => '<EMAIL>',
  ]);

  artisan('notify:automate-change-password weekly')
    ->expectsOutput("Passwords updated for user ID {$user->id}")
    ->assertExitCode(0);
});

it('generates PDF QR code and handles token expiration', function () {
  Setting::create([
    'label' => 'change_password_email_blocklist',
    'value' => '',
  ]);

  $warehouse = Warehouse::factory()->create(['name' => 'MainWH']);
  $user = User::factory()->create([
    'username' => time() . 'testuser',
    'change_app_password_status' => 'monthly',
    'app_send_to' => time() . '<EMAIL>',
    'include_app_qr_code' => true,
    'is_admin' => true,
  ]);

  View::shouldReceive('make')->andReturnSelf();
  View::shouldReceive('render')->andReturn('<html><body>PDF</body></html>');
  Mail::fake();

  // Mock JWT claims and token generation
  $mockToken = 'mocked_token_' . time();
  JWTAuth::shouldReceive('claims')
    ->with([
      'warehouse' => ['id' => $warehouse->id],
      'iat' => Carbon::now()->timestamp
    ])
    ->andReturnSelf();
  JWTAuth::shouldReceive('fromUser')
    ->with($user)
    ->andReturn($mockToken);

  // Mock DB transaction
  DB::shouldReceive('beginTransaction');
  DB::shouldReceive('commit');

  artisan('notify:automate-change-password monthly')
    ->expectsOutput("Passwords updated for user ID {$user->id}")
    ->assertExitCode(0);

  // Verify token expiration was set
  expect($user->fresh()->expired_at)->not->toBeNull();

  $file = now()->format('F') . now()->format('Y');
  $pdfName = "MainWH_Warehouse_QR_{$file}.pdf";

  Storage::assertMissing($pdfName);
});
