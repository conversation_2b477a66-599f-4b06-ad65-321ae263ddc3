<?php

use App\Models\UniversalReportCategory;
use App\Models\UniversalReportColumn;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $warehouse = Warehouse::factory()->create();
    $user = User::factory()->create([
        'is_all_warehouse' => 1,
    ]);
    $this->access_token =  JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/universal-reports';
    $this->categories = UniversalReportCategory::factory()
       ->count(6)
        ->state(new Sequence(
            ['id' => 1, 'name' => 'Sale Orders'],
            ['id' => 2, 'name' => 'Inventory'],
            ['id' => 3, 'name' => 'Shipment'],
            ['id' => 4, 'name' => 'Supply Inventory'],
            ['id' => 5, 'name' => 'Production'],
            ['id' => 6, 'name' => 'Other'],
        ))
        ->create();

    $this->template = UniversalReportTemplate::factory([
            'report_category_id' => $this->categories->random()->id,
            'status' => true,
            'name' => 'Sale Orders',
            'description' => 'Description for Sale Orders',
            'query' => "SELECT * FROM `universal_report_categories`",
        ])->create();

    UniversalReportTag::factory()
        ->count(3)
        ->state(new Sequence(
            [
                'report_template_id' => $this->template->id,
                'code' => 'multiple_name',
                'label' => 'Multiple Name',
                'type' => 'multi_select',
                'value_type' => 'sql',
                'is_required' => true,
                'position' => 0,
                'is_used' => true,
                'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
            ],
            [
                'report_template_id' => $this->template->id,
                'code' => 'single_name',
                'label' => 'Single Name',
                'type' => 'select',
                'value_type' => 'sql',
                'is_required' => true,
                'position' => 1,
                'is_used' => true,
                'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
            ],
            [
                'report_template_id' => $this->template->id,
                'code' => 'date_range',
                'label' => 'Date Range',
                'type' => 'date_range',
                'value_type' => 'date',
                'is_required' => true,
                'position' => 2,
                'is_used' => true,
            ]
        ))
        ->create();

    UniversalReportColumn::factory()
        ->count(3)
        ->state(new Sequence(
            [
                'report_template_id' => $this->template->id,
                'table' => null,
                'column' => "`name`",
                'alias' => 'Category Name',
                'virtual_column' => false,
            ],
            [
                'report_template_id' => $this->template->id,
                'table' => 'universal_report_categories',
                'column' => null,
                'alias' => 'urc',
                'virtual_column' => false,
            ],
            [
                'report_template_id' => $this->template->id,
                'table' => null,
                'column' => "CONCAT(`name`, '-' ,`name`)",
                'alias' => 'x2_name',
                'virtual_column' => true,
            ]
        ))
        ->create();
});

test('Test duplicate template', function () {
    $payload = [
        'name' => 'Duplicate Sale Orders',
        'description' => 'This is a duplicated template for Sale Orders',
    ];

    $response = $this->postJson("{$this->endpoint}/{$this->template->id}/duplicate", $payload);

    $response->assertStatus(201);
    $newTemplate = $response->json();

    expect($newTemplate['name'])->toBe($payload['name']);
    expect($newTemplate['description'])->toBe($payload['description']);

    unset($newTemplate['name'], $newTemplate['description']);
    expect($newTemplate)->toMatchArray([
        'report_category_id' => $this->template->report_category_id,
        'status' => $this->template->status,
        'query' => $this->template->query,
        'statement' => $this->template->statement,
    ]);

    $newTemplate['tags'] = collect($newTemplate['tags'])->map(function ($tag) {
        return collect($tag)->except(['id', 'report_template_id'])->toArray();
    })->toArray();
    $expectedTags = $this->template->tags->map(function ($tag) {
        return collect($tag)->except(['id', 'report_template_id'])->toArray();
    })->toArray();

    expect(collect($newTemplate['tags']))->toMatchArray($expectedTags);

    $newTemplate['columns'] = collect($newTemplate['columns'])->map(function ($column) {
        return collect($column)->except(['id', 'report_template_id'])->toArray();
    })->toArray();
    $expectedColumns = $this->template->columns->map(function ($column) {
        return collect($column)->except(['id', 'report_template_id'])->toArray();
    })->toArray();

    expect(collect($newTemplate['columns']))->toMatchArray($expectedColumns);

});
