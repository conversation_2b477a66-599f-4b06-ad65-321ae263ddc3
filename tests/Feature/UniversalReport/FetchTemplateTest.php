<?php

use App\Models\UniversalReportCategory;
use App\Models\UniversalReportTemplate;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Faker\Factory as Faker;

uses(RefreshDatabase::class);

beforeEach(function () {
    $warehouse = Warehouse::factory()->create();
    $user = User::factory()->create([
        'is_all_warehouse' => 1,
    ]);
    $this->access_token =  JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/universal-reports';
    $this->categories = UniversalReportCategory::factory()
       ->count(6)
        ->state(new Sequence(
            ['id' => 1, 'name' => 'Sale Orders'],
            ['id' => 2, 'name' => 'Inventory'],
            ['id' => 3, 'name' => 'Shipment'],
            ['id' => 4, 'name' => 'Supply Inventory'],
            ['id' => 5, 'name' => 'Production'],
            ['id' => 6, 'name' => 'Other'],
        ))
        ->create();

    $faker = Faker::create('en_US');
    $this->templates = UniversalReportTemplate::factory()->count(10)
        ->state(new Sequence(
            [
                'report_category_id' => $this->categories->random()->id,
                'status' => $faker->boolean(),
                'name' => $faker->sentence(),
                'description' => $faker->paragraph(),
                'query' => "SELECT * FROM `universal_report_categories`",
            ],
        ))
        ->create();
});

test('Fetch template list no limit', function () {
    $response = $this->getJson($this->endpoint);

    $response->assertStatus(200);
    $response->assertJsonStructure([
        '*' => [
            'id',
            'name',
        ],
    ]);
    $this->assertCount(10, $response->json());
});

test('Fetch template with params', function () {
    $params = [
        'limit' => 5,
        'name' => 'Sale Orders',
        'category_ids' => '1,3',
    ];

    $response = $this->get($this->endpoint . '?' . http_build_query($params));
    $response->assertStatus(200);
});
