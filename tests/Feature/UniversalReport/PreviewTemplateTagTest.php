<?php

use App\Models\UniversalReportCategory;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $warehouse = Warehouse::factory()->create();
    $user = User::factory()->create([
        'is_all_warehouse' => 1,
    ]);
    $this->access_token =  JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/universal-reports';
    $this->categories = UniversalReportCategory::factory()
        ->count(6)
        ->state(new Sequence(
            ['id' => 1, 'name' => 'Sale Orders'],
            ['id' => 2, 'name' => 'Inventory'],
            ['id' => 3, 'name' => 'Shipment'],
            ['id' => 4, 'name' => 'Supply Inventory'],
            ['id' => 5, 'name' => 'Production'],
            ['id' => 6, 'name' => 'Other'],
        ))
        ->create();

    $this->template = UniversalReportTemplate::factory([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Sale Orders',
        'description' => 'Description for Sale Orders',
        'query' => "SELECT * FROM `universal_report_categories`",
    ])->create();
});

test('Tag single select type free_text', function () {
    $tag = UniversalReportTag::factory([
        'report_template_id' => $this->template->id,
        'code' => 'single_select_free_text',
        'label' => 'Single Select Free Text',
        'type' => 'single_select',
        'value_type' => 'free_text',
        'is_required' => true,
        'position' => 0,
        'is_used' => true,
        'value' => "1|Product\n 2|Service\n 3|Other",
    ])->create();

    $response = $this->getJson("{$this->endpoint}/{$this->template->id}/tags/{$tag->id}");
    $response->assertStatus(200);
    $response->assertJson([
        'id' => $tag->id,
        'code' => $tag->code,
        'label' => $tag->label,
        'type' => $tag->type,
        'value_type' => $tag->value_type,
        'is_required' => $tag->is_required,
        'position' => $tag->position,
        'is_used' => $tag->is_used,
        'value' => $tag->value,
        'placeholder_1' => null,
        'placeholder_2' => null,
        'options' => [],
    ]);
});

test('Tag single select type sql', function () {
    $tag = UniversalReportTag::factory([
        'report_template_id' => $this->template->id,
        'code' => 'single_select_sql',
        'label' => 'Single Select SQL',
        'type' => UniversalReportTag::TYPE_SINGLE_SELECT,
        'value_type' => UniversalReportTag::VALUE_TYPE_SQL,
        'is_required' => true,
        'position' => 0,
        'is_used' => true,
        'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
    ])->create();

    $response = $this->getJson("{$this->endpoint}/{$this->template->id}/tags/{$tag->id}");
    $response->assertStatus(200);
    $response->assertJson([
        'id' => $tag->id,
        'code' => $tag->code,
        'label' => $tag->label,
        'type' => $tag->type,
        'value_type' => $tag->value_type,
        'is_required' => $tag->is_required,
        'position' => $tag->position,
        'is_used' => $tag->is_used,
        'value' => $tag->value,
        'placeholder_1' => null,
        'placeholder_2' => null,
    ]);

    $response->assertJsonStructure([
        'options' => [
            '*' => [
                'value',
                'text',
            ],
        ],
    ]);
});
