<?php

use App\Models\UniversalReportCategory;
use App\Models\UniversalReportColumn;
use App\Models\UniversalReportTag;
use App\Models\UniversalReportTemplate;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $warehouse = Warehouse::factory()->create();
    $user = User::factory()->create([
        'is_all_warehouse' => 1,
    ]);
    $this->access_token =  JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/universal-reports';
    $this->timezonePattern = '/SET\s+?time_zone\s+?=\s+?.*?;/i';

    $this->categories = UniversalReportCategory::factory()
        ->count(6)
        ->state(new Sequence(
            ['id' => 1, 'name' => 'Sale Orders'],
            ['id' => 2, 'name' => 'Inventory'],
            ['id' => 3, 'name' => 'Shipment'],
            ['id' => 4, 'name' => 'Supply Inventory'],
            ['id' => 5, 'name' => 'Production'],
            ['id' => 6, 'name' => 'Other'],
        ))
        ->create();
});

test('Update template with statement to empty statement', function () {
    $originalTemplate = UniversalReportTemplate::factory()->create([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Test Template',
        'description' => 'This is a test template',
        'query' => "SELECT * FROM `universal_report_categories`",
        'statement' => "SET time_zone = 'America/Los_Angeles';",
    ]);

    $sql = "SELECT
            {{ `urc`.* }},
            {{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},
            {{ `name` AS `Category Name` }},
            {{ `urc`.`description` AS `Category Description 2` }},
            {{ `description` `Category Description` }}
        FROM
            `universal_report_categories` as `urc`";
    $payload = [
        'template' => [
            'report_category_id' => $originalTemplate->report_category_id,
            'status' => false,
            'name' => 'Test update template to empty statement successfully',
            'description' => 'This is a test update Template to empty statement successfully',
            'query' => $sql,
        ],
    ];

    $response = $this->putJson("{$this->endpoint}/{$originalTemplate->id}", $payload);
    $response->assertStatus(200);
    $this->assertDatabaseHas('universal_report_templates', [
        'id' => $originalTemplate->id,
        'report_category_id' => $originalTemplate->report_category_id,
        'name' => 'Test update template to empty statement successfully',
        'status' => false,
        'description' => 'This is a test update Template to empty statement successfully',
        'query' => $sql,
        'statement' => null,
    ]);
});

test('Update template from empty statement to statement', function () {
    $originalTemplate = UniversalReportTemplate::factory()->create([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Test Template',
        'description' => 'This is a test template',
        'query' => "SELECT * FROM `universal_report_categories`",
        'statement' => null,
    ]);

    $sql = "SET time_zone = 'America/Los_Angeles';
        SELECT
            {{ `urc`.* }},
            {{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},
            {{ `name` AS `Category Name` }},
            {{ `urc`.`description` AS `Category Description 2` }},
            {{ `description` `Category Description` }}
        FROM
            `universal_report_categories` as `urc`";
    $payload = [
        'template' => [
            'report_category_id' => $originalTemplate->report_category_id,
            'status' => false,
            'name' => 'Test update template to empty statement successfully',
            'description' => 'This is a test update Template to empty statement successfully',
            'query' => $sql,
        ],
    ];

    $response = $this->putJson("{$this->endpoint}/{$originalTemplate->id}", $payload);
    $response->assertStatus(200);
    $onlySql = preg_replace($this->timezonePattern, '', $sql);
    $this->assertDatabaseHas('universal_report_templates', [
        'id' => $originalTemplate->id,
        'report_category_id' => $originalTemplate->report_category_id,
        'name' => 'Test update template to empty statement successfully',
        'status' => false,
        'description' => 'This is a test update Template to empty statement successfully',
        'query' => $onlySql,
        'statement' => "SET time_zone = 'America/Los_Angeles';",
    ]);
});

test('Update template with dynamic column and without tag', function () {
    $originalTemplate = UniversalReportTemplate::factory()->create([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Test Template',
        'description' => 'This is a test template',
        'query' => "SELECT * FROM `universal_report_categories`",
    ]);

    UniversalReportColumn::factory()->count(1)->state(new Sequence(
        [
            'table' => null,
            'column' => "`created_at`",
            'alias' => 'Created Date',
            'virtual_column' => false,
        ],
    ))->create(['report_template_id' => $originalTemplate->id]);

    $sql = "SET time_zone = 'America/Los_Angeles';
        SELECT
            {{ `urc`.* }},
            {{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},
            {{ `name` AS `Category Name` }},
            {{ `urc`.`description` AS `Category Description 2` }},
            {{ `description` `Category Description` }}
        FROM
            `universal_report_categories` as `urc`";

    $categoryId = $this->categories->random()->id;
    $payload = [
        'template' => [
            'report_category_id' => $categoryId,
            'status' => false,
            'name' => 'Test Template with Dynamic Column',
            'description' => 'This is a test template with dynamic column',
            'query' => $sql,
        ],
    ];

    $response = $this->putJson("{$this->endpoint}/{$originalTemplate->id}", $payload);
    $response->assertStatus(200);

    $onlySql = preg_replace($this->timezonePattern, '', $sql);
    $this->assertDatabaseHas('universal_report_templates', [
        'report_category_id' => $categoryId,
        'name' => 'Test Template with Dynamic Column',
        'status' => false,
        'description' => 'This is a test template with dynamic column',
        'query' => $onlySql,
        'statement' => "SET time_zone = 'America/Los_Angeles';",
    ]);

    $columns = UniversalReportColumn::where('report_template_id', $response->json('id'))->get();
    $this->assertCount(5, $columns);

    foreach ($columns as $column) {
        $original = trim($column->original);
        switch ($original) {
            case "{{ `name` AS `Category Name` }},":
                $this->assertDatabaseHas('universal_report_columns', [
                    'report_template_id' => $response->json('id'),
                    'table' => null,
                    'column' => "`name`",
                    'alias' => 'Category Name',
                    'virtual_column' => false,
                ]);
                $this->assertStringStartsWith(
                    "{{ `name` AS `Category Name` }},",
                    $column->original
                );
                break;

            case "{{ `urc`.* }},":
                $this->assertDatabaseHas('universal_report_columns', [
                    'report_template_id' => $response->json('id'),
                    'table' => 'universal_report_categories',
                    'column' => null,
                    'alias' => 'urc',
                    'virtual_column' => false,
                ]);

                $this->assertStringStartsWith(
                    "{{ `urc`.* }},",
                    $column->original
                );
                break;
            case "{{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},":
                $this->assertDatabaseHas('universal_report_columns', [
                    'report_template_id' => $response->json('id'),
                    'table' => null,
                    'column' => "CONCAT(`name`, '-' ,`name`)",
                    'alias' => 'x2_name',
                    'virtual_column' => true,
                ]);

                $this->assertStringStartsWith(
                    "{{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},",
                    $column->original
                );
                break;

            case "{{ `urc`.`description` AS `Category Description 2` }},":
                $this->assertDatabaseHas('universal_report_columns', [
                    'report_template_id' => $response->json('id'),
                    'table' => null,
                    'column' => "`urc`.`description`",
                    'alias' => 'Category Description 2',
                    'virtual_column' => false,
                ]);
                $this->assertStringStartsWith(
                    "{{ `urc`.`description` AS `Category Description 2` }},",
                    $column->original
                );
                break;
            
            case "{{ `description` `Category Description` }}":
                $this->assertDatabaseHas('universal_report_columns', [
                    'report_template_id' => $response->json('id'),
                    'table' => null,
                    'column' => "`description`",
                    'alias' => 'Category Description',
                    'virtual_column' => false,
                ]);
                $this->assertStringStartsWith(
                    "{{ `description` `Category Description` }}",
                    $column->original
                );
                break;
            
            default:
                $this->fail("Unexpected column original: {$original}");
                break;
        }
    }
});

test('Update template duplicate tag code', function () {
    $originalTemplate = UniversalReportTemplate::factory()->create([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Test Template',
        'description' => 'This is a test template',
        'query' => "SELECT * FROM `universal_report_categories`",
    ]);

    $sql = "SET time_zone = 'America/Los_Angeles';
        SELECT
            {{ `urc`.* }},
            {{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},
            {{ `name` AS `Category Name` }},
            {{ `description` AS `Category Description` }}
        FROM
            `universal_report_categories` as `urc`
        WHERE `name` in ([[multiple_name]])
        OR `name` in ([[single_name]])
        OR `description` = ([[input_description]])
        OR (`created_at` >= ([[date_range.0]]) AND `created_at` <= ([[date_range.1]]))
        OR `updated_at` <= ([[single_date]])";

    $categoryId = $this->categories->random()->id;
    $payload = [
        'template' => [
            'report_category_id' => $categoryId,
            'status' => false,
            'name' => 'Test Template with Dynamic Column',
            'description' => 'This is a test template with dynamic column',
            'query' => $sql,
        ],
        'tags' => [
            [
                'code' => 'multiple_name',
                'label' => 'Multiple Name',
                'type' => 'multi_select',
                'value_type' => 'sql',
                'is_required' => true,
                'position' => 0,
                'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
            ],
            [
                'code' => 'multiple_name',
                'label' => 'Single Name',
                'type' => 'single_select',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 1,
                'value' => "1|Product\n 2|Service\n 3|Other",
            ]
        ],
    ];

    $response = $this->putJson("{$this->endpoint}/{$originalTemplate->id}", $payload);
    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['tags.1.code']);
});

test('Update template with tag', function () {
    $originalTemplate = UniversalReportTemplate::factory()->create([
        'report_category_id' => $this->categories->random()->id,
        'status' => true,
        'name' => 'Test Template',
        'description' => 'This is a test template',
        'query' => "SELECT * FROM `universal_report_categories`",
    ]);

    UniversalReportTag::factory()->count(2)->state(new Sequence(
        [
            'code' => 'multiple_name',
            'label' => 'Multiple Name',
            'type' => 'multi_select',
            'value_type' => 'sql',
            'is_required' => false,
            'position' => 0,
            'is_used' => false,
            'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
        ],
        [
            'code' => 'single_name',
            'label' => 'Single Name',
            'type' => 'single_select',
            'value_type' => 'free_text',
            'is_required' => false,
            'position' => 1,
            'is_used' => false,
            'value' => "1|Product\n 2|Service\n 3|Other",
        ],
    ))->create(['report_template_id' => $originalTemplate->id]);

    $sql = "SET time_zone = 'America/Los_Angeles';
        SELECT
            {{ `urc`.* }},
            {{ CONCAT(`name`, '-' ,`name`) as `x2_name` }},
            {{ `name` AS `Category Name` }},
            {{ `description` AS `Category Description` }}
        FROM
            `universal_report_categories` as `urc`
        WHERE `name` in ([[multiple_name]])
        OR `name` in ([[single_name]])
        OR `description` = ([[input_description]])
        OR (`created_at` >= '[[date_range.0]]' AND `created_at` <= '[[date_range.1]]')
        OR `updated_at` <= '[[single_date]]'";

    $categoryId = $this->categories->random()->id;
    $payload = [
        'template' => [
            'report_category_id' => $categoryId,
            'status' => false,
            'name' => 'Test Template with Dynamic Column',
            'description' => 'This is a test template with dynamic column',
            'query' => $sql,
        ],
        'tags' => [
            [
                'code' => 'multiple_name',
                'label' => 'Multiple Name',
                'type' => 'multi_select',
                'value_type' => 'sql',
                'is_required' => true,
                'position' => 0,
                'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
            ],
            [
                'code' => 'single_name',
                'label' => 'Single Name',
                'type' => 'single_select',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 1,
                'value' => "1|Product\n 2|Service\n 3|Other",
            ],
            [
                'code' => 'input_description',
                'label' => 'Input Description',
                'type' => 'input',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 2,
            ],
            [
                'code' => 'date_range',
                'label' => 'Date Range',
                'type' => 'range_date',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 3,
            ],
            [
                'code' => 'single_date',
                'label' => 'Single Date',
                'type' => 'single_date',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 4,
            ],
            [
                'code' => 'single_date_2',
                'label' => 'Single Date 2',
                'type' => 'single_date',
                'value_type' => 'free_text',
                'is_required' => true,
                'position' => 5,
            ],
        ],
    ];

    $response = $this->putJson("{$this->endpoint}/{$originalTemplate->id}", $payload);
    $response->assertStatus(200);

    $tags = UniversalReportTag::where('report_template_id', $response->json('id'))->get();
    foreach ($tags as $tag) {
        switch ($tag->code) {
            case 'multiple_name':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'multiple_name',
                    'label' => 'Multiple Name',
                    'type' => 'multi_select',
                    'value_type' => 'sql',
                    'is_required' => true,
                    'position' => 0,
                    'is_used' => true,
                    'value' => "SELECT `id` as `value`, `name` as `text` FROM `universal_report_categories`",
                ]);
                break;

            case 'single_name':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'single_name',
                    'label' => 'Single Name',
                    'type' => 'single_select',
                    'value_type' => 'free_text',
                    'is_required' => true,
                    'position' => 1,
                    'is_used' => true,
                    'value' => "1|Product\n 2|Service\n 3|Other",
                ]);
                break;

            case 'input_description':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'input_description',
                    'label' => 'Input Description',
                    'type' => 'input',
                    'value_type' => 'free_text',
                    'is_required' => true,
                    'position' => 2,
                    'is_used' => true,
                    'value' => null,
                ]);
                break;

            case 'date_range':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'date_range',
                    'label' => 'Date Range',
                    'type' => 'range_date',
                    'value_type' => 'free_text',
                    'is_required' => true,
                    'position' => 3,
                    'is_used' => true,
                    'value' => null,
                ]);
                break;

            case 'single_date':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'single_date',
                    'label' => 'Single Date',
                    'type' => 'single_date',
                    'value_type' => 'free_text',
                    'is_required' => true,
                    'position' => 4,
                    'is_used' => true,
                    'value' => null,
                ]);
                break;

            case 'single_date_2':
                $this->assertDatabaseHas('universal_report_tags', [
                    'report_template_id' => $response->json('id'),
                    'code' => 'single_date_2',
                    'label' => 'Single Date 2',
                    'type' => 'single_date',
                    'value_type' => 'free_text',
                    'is_required' => true,
                    'position' => 5,
                    'is_used' => false,
                    'value' => null,
                ]);
                break;

            default:
                $this->fail("Unexpected tag code: {$tag->code}");
                break;
        }
    }
});

