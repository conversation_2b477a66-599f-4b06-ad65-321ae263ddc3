<?php

use App\Models\User;
use App\Models\ClaimMessage;
use App\Models\Employee;
use App\Models\SaleOrderClaimSupport;
use App\Models\Store;
use App\Repositories\ClaimMessageRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;

// php artisan test --env=local-testing --filter ClaimMessageTest
uses(RefreshDatabase::class);

beforeEach(function () {
    // Fake claim + user
    $this->user = User::factory()->create();
    $this->employee = Employee::factory()->create();
    $this->claim = SaleOrderClaimSupport::factory()->create();
    $this->messages = ClaimMessage::factory()->count(3)->create([
        'sale_order_claim_support_id' => $this->claim->id,
        'type' => 'message'
    ]);
});

it('getMessage_AdminUser_ReturnsPaginatedMessages', function () {
    $repository = new ClaimMessageRepository();

    // Set up test data
    $this->user->is_active = true;
    $this->user->is_admin = true;
    $this->user->save();

    // Get messages with pagination
    $result = $repository->getMessages($this->claim, 1, 2);
    
    // Assert pagination values
    expect($result['status'])->toBeTrue();
    expect($result['output']['data']['pagination'])->toBe([
        'current_page' => 1,
        'per_page' => 2,
        'total_messages' => 3,
        'total_pages' => 2,
        'has_more' => true
    ]);

    // Assert messages
    expect($result['output']['data']['messages'])->toHaveCount(2);
    expect($result['output']['data']['messages']->first())->toBeInstanceOf(ClaimMessage::class);
});

it('getMessage_StoreUser_ReturnsPaginatedMessages', function () {
    $repository = new ClaimMessageRepository();

    // Set up store user
    $store = Store::factory()->create(['client_id' => 'test-client-123']);
    Auth::shouldReceive('user')->andReturn($store);

    // Get messages with pagination
    $result = $repository->getMessages($this->claim, 2, 2);

    // Assert response structure
    expect($result['status'])->toBeTrue();
    expect($result['output']['data'])->toHaveKeys(['messages', 'pagination']);
    expect($result['output']['data']['pagination'])->toHaveKeys([
        'current_page',
        'per_page',
        'total_messages',
        'total_pages',
        'has_more'
    ]);

    // Assert pagination values
    expect($result['output']['data']['pagination'])->toBe([
        'current_page' => 2,
        'per_page' => 2,
        'total_messages' => 3,
        'total_pages' => 2,
        'has_more' => false
    ]);

    // Assert messages
    expect($result['output']['data']['messages'])->toHaveCount(1);
    expect($result['output']['data']['messages']->first())->toBeInstanceOf(ClaimMessage::class);
});

it('storeMessage_WithValidData_CreatesMessage', function () {
    $repository = new ClaimMessageRepository();

    Auth::shouldReceive('user')->andReturn($this->user);

    $data = [
        'content' => ['text' => 'Test message'],
        'employeeId' => $this->employee->id
    ];

    $result = $repository->store($this->claim, $data);
    
    expect($result['status'])->toBeTrue();
    $message = $result['output']['data'];
    expect($message)->toBeInstanceOf(ClaimMessage::class);
    expect($message->message)->toBe(['text' => 'Test message']);
    expect($message->sender_id)->toBe($this->employee->id);
    expect($message->sender_role)->toBe(ClaimMessage::ROLE_SUPPORT);
    expect($message->type)->toBe('message');
});

it('storeMessage_WithStore_CreatesClientMessage', function () {
    $repository = new ClaimMessageRepository();

    // Set up store user and support message
    $store = Store::factory()->create(['client_id' => 'test-client-123']);
    Auth::shouldReceive('user')->andReturn($store);
    
    // Create a support message first
    ClaimMessage::factory()->create([
        'sale_order_claim_support_id' => $this->claim->id,
        'sender_role' => ClaimMessage::ROLE_SUPPORT,
        'type' => 'message'
    ]);

    $data = [
        'content' => ['text' => 'Test message from client'],
    ];

    $result = $repository->store($this->claim, $data);
    
    expect($result['status'])->toBeTrue();
    $message = $result['output']['data'];
    expect($message)->toBeInstanceOf(ClaimMessage::class);
    expect($message->message)->toBe(['text' => 'Test message from client']);
    expect($message->sender_id)->toBe('test-client-123');
    expect($message->sender_role)->toBe(ClaimMessage::ROLE_CLIENT);
    expect($message->type)->toBe('message');
});

it('storeMessage_WithImages_ProcessesAndStoresImages', function () {
    $repository = new ClaimMessageRepository();
    Storage::fake(config('filesystems.default'));
    Auth::shouldReceive('user')->andReturn($this->user);

    $imageBase64 = 'data:image/jpeg;base64,' . base64_encode('fake image data');

    $data = [
        'content' => [
            'text' => 'Test with image',
            'images' => [$imageBase64]
        ],
        'employeeId' => $this->employee->id
    ];

    $result = $repository->store($this->claim, $data);
    
    expect($result['status'])->toBeTrue();
    $message = $result['output']['data'];
    expect($message)->toBeInstanceOf(ClaimMessage::class);
    expect($message->message['images'])->toHaveCount(1);
    expect($message->message['images'][0])->toContain('uploads/claim_attachments');

    // Verify image was stored
    $imagePath = str_replace('/storage/', '', $message->message['images'][0]);
    Storage::disk(config('filesystems.default'))->assertExists($imagePath);
});

it('storeMessage_WithStore_UsesClientId', function () {
    $repository = new ClaimMessageRepository();

    $store = Store::factory()->create(['client_id' => 'test-client-123']);
    Auth::shouldReceive('user')->andReturn($store);
    
    // Create a support message first
    ClaimMessage::factory()->create([
        'sale_order_claim_support_id' => $this->claim->id,
        'sender_role' => ClaimMessage::ROLE_SUPPORT,
        'type' => 'message'
    ]);

    $data = [
        'content' => ['text' => 'Store message']
    ];

    $result = $repository->store($this->claim, $data);
    
    expect($result['status'])->toBeTrue();
    $message = $result['output']['data'];
    expect($message->sender_id)->toBe('test-client-123');
    expect($message->sender_role)->toBe(ClaimMessage::ROLE_CLIENT);
});

it('storeMessage_WithStore_ThrowsExceptionWithoutSupportMessage', function () {
    $repository = new ClaimMessageRepository();
    $claim = SaleOrderClaimSupport::factory()->create();

    $store = Store::factory()->create(['client_id' => 'test-client-123']);
    Auth::shouldReceive('user')->andReturn($store);

    $data = [
        'content' => ['text' => 'Store message']
    ];
    
    $result = $repository->store($claim, $data);
    expect($result['status'])->toBeFalse();
    $message = $result['output']['message'];
    expect($message)->toBe('Cannot create seller message without a previous support message');
});
