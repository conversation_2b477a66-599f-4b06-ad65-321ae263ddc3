<?php

use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\Store;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->productStyles = ProductStyle::factory()->count(6)->state(new Sequence(
        ['name' => '1533', 'type' => 'Fleece'],
        ['name' => '2000', 'type' => 'Tee'],
        ['name' => '3001', 'type' => 'Fleece'],
        ['name' => '3600', 'type' => 'Tee'],
        ['name' => '4424', 'type' => 'Ornament'],
        ['name' => '5000', 'type' => 'Mugs'],
    ))->create();

    $this->productSizes = ProductSize::factory()->count(5)->state(new Sequence(
        ['sku' => '5XL', 'name' => '5XL'],
        ['sku' => '0XL', 'name' => 'XL'],
        ['sku' => '00L', 'name' => 'L'],
        ['sku' => '00M', 'name' => 'M'],
        ['sku' => '00S', 'name' => 'S'],
    ))->create();

    $this->store = Store::factory()->create();
    Storage::fake('local');
    $this->endpoint = '/api/products/style-size-weights/import-csv';
});

test('Verify product style size weight - File extension is invalid', function () {
    $fileContent = 'Style,Size,Multiple,Single,Unit'
        . "\n50005,S,5,4,lb"
        . "\n1533,K,5,5,oz"
        . "\n3600,L,5,5,oz_fake";
    $file = UploadedFile::fake()->createWithContent(
        'test.php',
        $fileContent,
    );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, ['file' => $file]);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'file' => [
                'The file must be a file of type: csv, xlsx, xls.'
            ]
        ]
    ]);
});

test('Verify product style size weight - Nothing to import.', function () {
    $fileContent = 'Style,Size,Multiple,Single,Unit'
        . "\n50005,S,5,4,lb"
        . "\n1533,K,5,5,oz"
        . "\n2000,L,-5,5,oz"
        . "\n3001,L,5,0,lb"
        . "\n3600,L,5,5,oz_fake";
    $file = UploadedFile::fake()->createWithContent(
        'test.csv',
        $fileContent,
    );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, ['file' => $file]);

    $response->assertStatus(200);
    $responseData = json_decode($response->getContent(), true);

    $this->assertEquals($responseData['status'], false);
    $this->assertEquals($responseData['message'], 'No valid record to import.');
});

test('Verify product style size weight - Import data successfully.', function () {
    Storage::fake('s3');
    $fileName = 'test.csv';
    $fileContent = 'Style,Size,Multiple,Single,Unit'
        . "\n" . $this->productStyles[0]->name . ',' . $this->productSizes[0]->name . ',5,4,lb'
        . "\n" . $this->productStyles[1]->name . ',' . $this->productSizes[1]->name . ',15,15,oz'
        . "\n" . $this->productStyles[2]->name . ',' . $this->productSizes[2]->name . ',25,25,oz'
        . "\n" . $this->productStyles[3]->name . ',' . $this->productSizes[3]->name . ',35,9,lb'
        . "\n" . $this->productStyles[4]->name . ',' . $this->productSizes[4]->name . ',5,5,oz';
    $file = UploadedFile::fake()->createWithContent(
        $fileName,
        $fileContent,
    );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, ['file' => $file]);

    $responseData = json_decode($response->getContent(), true);

    $this->assertEquals($responseData['message'], 'Import product sku matching successfully!');

    // Check if the weight data is saved to DB
    for ($i = 0; $i < count($this->productSizes); $i++) {
        $this->assertDatabaseHas('product_style_size_weights', [
            'product_style_id' => $this->productStyles[$i]->id,
            'product_size_id' => $this->productSizes[$i]->id
        ]);
    }
});
