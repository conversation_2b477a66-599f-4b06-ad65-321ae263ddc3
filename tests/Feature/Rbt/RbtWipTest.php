<?php

use App\Models\Product;
use App\Models\RbtProduct;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\StoreShipment;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->products = Product::factory()->createMany([
        [
            'sku' => 'UNGT1W00L',
            'style' => 'UNGT',
            'color' => '1W'
        ],
        [
            'sku' => 'UNGT1W00M',
            'style' => 'UNGT',
            'color' => '1W'
        ],
    ]);

    $this->products = RbtProduct::factory()->createMany([
        [
            'product_id' => $this->products[0]->id,
        ],
        [
            'product_id' => $this->products[1]->id,
        ],
    ]);

    \Illuminate\Support\Facades\Cache::store(config('cache.redis_store'))->flush();
    $date = Carbon::now()->timestamp;
    $externalId = 'RBT' . '.' . $date;
    $listRbtSku = RbtProduct::select('p.id', 'p.sku')
        ->join('product as p', 'p.id', '=', 'rbt_product.product_id')
        ->get();
    $orderCount = 2;
    for ($i = 0; $i < $orderCount; $i++) {
        $random = rand(1, 1);
        $saleOrder = SaleOrder::factory([
            'external_number' => $externalId . '.' . $i,
            'store_id' => SaleOrder::STORE_RBT,
            'order_quantity' => $random,
            'created_at' => now(),
            'warehouse_id' => 1,
            'shipping_method' => StoreShipment::SERVICE_STANDARD,
            'order_status' => SaleOrder::STATUS_NEW_ORDER,
            'source' => 'api',
            'order_date' => Carbon::now()->format('Y-m-d'),
            'order_number' => null,
            'order_time' => Carbon::now()->toDateTimeString(),
            'account_id' => 0,
            'is_rbt' => true
        ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'name' => 'Lynda Adner',
            'email' => '*********',
            'company' => '676767',
            'phone' => '******-419-8616 ext. 60877',
            'street1' => '5 N CLARKSON AVE',
            'street2' => '',
            'city' => 'MASSENA',
            'state' => 'NY',
            'country' => 'US',
            'zip' => '13662-1764'
        ]), 'address')
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::RETURN_ADDRESS,
            'name' => 'Lynda Adner',
            'email' => '*********',
            'company' => '676767',
            'phone' => '******-419-8616 ext. 60877',
            'street1' => '5 N CLARKSON AVE',
            'street2' => '',
            'city' => 'MASSENA',
            'state' => 'NY',
            'country' => 'US',
            'zip' => '13662-1764'
        ]), 'address')  // Adding the RETURN_ADDRESS
                        ->has(
                            SaleOrderItem::factory()->count($random)->state(
                                function (array $attributes) use ($listRbtSku, $i, $externalId) {
                                    // Select a random SKU
                                    $randomSku = $listRbtSku->random();

                                    return [
                                        'warehouse_id' => 1,
                                        'store_id' => SaleOrder::STORE_RBT,
                                        'quantity' => 1,
                                        'product_id' => $randomSku->id,
                                        'product_sku' => $randomSku->sku,
                                        'product_style_sku' => substr($randomSku->sku, 0, 4),
                                        'product_color_sku' => substr($randomSku->sku, 4, 2),
                                        'product_size_sku' => substr($randomSku->sku, 6, 3),
                                        'product_style_color_sku' => substr($randomSku->sku, 0, 6),
                                        'external_id' => $externalId . '.' . $i,
                                        'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"},{"name":"PreviewFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"}]',
                                        'is_rbt' => true
                                    ];
                                },
                            ),
                            'items',
                        )
            ->create();
        // Assign custom SKU format for each SaleOrderItem
        foreach ($saleOrder->items as $saleOrderItem) {
            // Generate a random `label_id`
            $randomPart1 = str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $randomPart2 = 'SJ';
            $randomPart3 = 'S';
            $randomPart4 = str_pad(mt_rand(1, 1), 6, '0', STR_PAD_LEFT);

            $labelId = "$randomPart1-$randomPart2-$randomPart3-$randomPart4";

            $sku = 'S' . $saleOrderItem->id . $saleOrderItem->product_sku;  // Custom SKU format
            $saleOrderItem->sku = $sku;
            $saleOrderItem->save();  // Save the updated SKU
            $valueBarcode = [
                'order_id' => $saleOrderItem->order_id,
                'account_id' => $saleOrderItem->account_id,
                'store_id' => $saleOrderItem->store_id,
                'warehouse_id' => $saleOrderItem->warehouse_id,
                'order_item_id' => $saleOrderItem->id,
                'sku' => $saleOrderItem->sku,
                'barcode_number' => 1,
                'order_quantity' => $saleOrderItem->quantity,
                'created_at' => date('Y-m-d H:i:s'),
                'label_id' => $labelId,
                'print_method' => 'DTG',
            ];
        }
        DB::table('sale_order_item_barcode')->insert($valueBarcode);
    }

    $this->endpointApiRbt = '/api/rbt/wips';
    $this->token = 'I3BDmQ9uMPz5OslTSv/q3rCxf8lNO!e7XkiAb2mBdnlNIoBBwbg1en/rR/!9MRhM';
});

test('get wip success', function () {
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->get($this->endpointApiRbt);
    $response->assertStatus(200);
    $this->assertCount(2, $response['data']);
});

test('get wip failed - invalid token', function () {
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer 65757565',
    ])->get($this->endpointApiRbt);
    $response->assertStatus(401);
});

test('get detail wip success', function () {
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->get($this->endpointApiRbt . '/' . $barcode[0]->label_id);
    $response->assertStatus(200);
});

test('get detail wip success - deleted wip', function () {
    SaleOrderItemBarcode::query()->update(['is_deleted' => 1]);
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->get($this->endpointApiRbt . '/' . $barcode[0]->label_id);
    $response->assertStatus(404);
});

test('get received wip', function () {
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt . '/' . $barcode[0]->label_id . '/receive');
    $response->assertStatus(200);
    // already received
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt . '/' . $barcode[0]->label_id . '/receive');
    $response->assertStatus(400);
});
