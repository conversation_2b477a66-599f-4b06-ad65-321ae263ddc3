<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Product;
use App\Models\RbtBatchNumber;
use App\Models\RbtProduct;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\StoreShipment;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->employee = Employee::factory()->create(['warehouse_id' => 1, 'code' => 13579]);

    $this->products = Product::factory()->createMany([
        [
            'parent_id' => 2113,
            'sku' => 'UNGT1W00L',
            'style' => 'UNGT',
            'color' => '1W'
        ],
        [
            'parent_id' => 21213,

            'sku' => 'UNGT1W00M',
            'style' => 'UNGT',
            'color' => '1W'
        ],
    ]);
    $this->location = Location::factory(
    )->createMany(
        [
            [
                'warehouse_id' => 1,
                'barcode' => 'moving 4',
                'type' => 3,
                'rbt_sku' => $this->products[0]->sku,
            ],
            [
                'warehouse_id' => 1,
                'barcode' => 'moving 3',
                'type' => 3,
                'rbt_sku' => $this->products[1]->sku,

            ],
        ]);
    $this->productsRbt = RbtProduct::factory()->createMany([
        [
            'product_id' => $this->products[0]->id,
        ],
        [
            'product_id' => $this->products[1]->id,
        ],
    ]);
    $date = Carbon::now()->timestamp;
    $externalId = 'RBT' . '.' . $date;
    $listRbtSku = RbtProduct::select('p.id', 'p.sku')
        ->join('product as p', 'p.id', '=', 'rbt_product.product_id')
        ->get();
    $orderCount = 2;

    for ($i = 0; $i < $orderCount; $i++) {
        $random = rand(1, 1);
        $saleOrder = SaleOrder::factory([
            'external_number' => $externalId . '.' . $i,
            'store_id' => SaleOrder::STORE_RBT,
            'order_quantity' => $random,
            'created_at' => now(),
            'warehouse_id' => 1,
            'shipping_method' => StoreShipment::SERVICE_STANDARD,
            'order_status' => SaleOrder::STATUS_NEW_ORDER,
            'source' => 'api',
            'order_date' => Carbon::now()->format('Y-m-d'),
            'order_number' => null,
            'order_time' => Carbon::now()->toDateTimeString(),
            'account_id' => 0,
            'is_rbt' => true
        ])
            ->has(SaleOrderAddress::factory([
                'type_address' => SaleOrderAddress::TO_ADDRESS,
                'name' => 'Lynda Adner',
                'email' => '*********',
                'company' => '676767',
                'phone' => '******-419-8616 ext. 60877',
                'street1' => '5 N CLARKSON AVE',
                'street2' => '',
                'city' => 'MASSENA',
                'state' => 'NY',
                'country' => 'US',
                'zip' => '13662-1764'
            ]), 'address')
            ->has(SaleOrderAddress::factory([
                'type_address' => SaleOrderAddress::RETURN_ADDRESS,
                'name' => 'Lynda Adner',
                'email' => '*********',
                'company' => '676767',
                'phone' => '******-419-8616 ext. 60877',
                'street1' => '5 N CLARKSON AVE',
                'street2' => '',
                'city' => 'MASSENA',
                'state' => 'NY',
                'country' => 'US',
                'zip' => '13662-1764'
            ]), 'address')
            ->has(
                SaleOrderItem::factory()->count($random)->state(
                    function (array $attributes) use ($listRbtSku, $i, $externalId) {
                        $randomSku = $listRbtSku[0];

                        return [
                            'warehouse_id' => 1,
                            'store_id' => SaleOrder::STORE_RBT,
                            'quantity' => 1,
                            'product_id' => $randomSku->id,
                            'product_sku' => $randomSku->sku,
                            'product_style_sku' => substr($randomSku->sku, 0, 4),
                            'product_color_sku' => substr($randomSku->sku, 4, 2),
                            'product_size_sku' => substr($randomSku->sku, 6, 3),
                            'product_style_color_sku' => substr($randomSku->sku, 0, 6),
                            'external_id' => $externalId . '.' . $i,
                            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"},{"name":"PreviewFiles.Front","value":"https:\/\/swiftpod.s3.us-west-1.amazonaws.com\/artwork\/2024-11-11\/S17146016UNGS1N00M-0.png"}]'
                        ];
                    },
                ),
                'items',
            )
            ->create();

        // Assign custom SKU format for each SaleOrderItem
        foreach ($saleOrder->items as $saleOrderItem) {
            // Generate a random `label_id`
            $randomPart1 = str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $randomPart2 = 'SJ';
            $randomPart3 = 'S';
            $randomPart4 = str_pad(mt_rand(1, 1), 6, '0', STR_PAD_LEFT);

            $labelId = "$randomPart1-$randomPart2-$randomPart3-$randomPart4";

            $sku = 'S' . $saleOrderItem->id . $saleOrderItem->product_sku;  // Custom SKU format
            $saleOrderItem->sku = $sku;
            $saleOrderItem->save();  // Save the updated SKU
            $valueBarcode = [
                'order_id' => $saleOrderItem->order_id,
                'account_id' => $saleOrderItem->account_id,
                'store_id' => $saleOrderItem->store_id,
                'warehouse_id' => $saleOrderItem->warehouse_id,
                'order_item_id' => $saleOrderItem->id,
                'sku' => $saleOrderItem->sku,
                'barcode_number' => 1,
                'order_quantity' => $saleOrderItem->quantity,
                'created_at' => date('Y-m-d H:i:s'),
                'label_id' => $labelId,
                'print_method' => 'DTG',
            ];
        }

        DB::table('sale_order_item_barcode')->insert($valueBarcode);
    }

    $this->barcodePrinted = BarcodePrinted::factory()->create([
        'convert_status' => 1,
        'user_id' => $this->employee->id
    ]);
    $this->endpointApiRbt = '/api/rbt/wip/print';
    $this->endpointGetApiRbt = '/api/rbt/wips';
    $this->endpointApiRbtConfirmPrint = '/api/rbt/wip/confirm-print';
    $this->token = 'I3BDmQ9uMPz5OslTSv/q3rCxf8lNO!e7XkiAb2mBdnlNIoBBwbg1en/rR/!9MRhM';
});

test('print wip failed - not received wip', function () {
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt, [
        'station_id' => '1',
        'wip_ids' => [
            $barcode[0]->label_id
        ]
    ]);
    $response->assertStatus(422);
});

test('print wip failed - wip not found', function () {
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt, [
        'station_id' => '1',
        'wip_ids' => [
            '12312321-12312312-21'
        ]
    ]);
    $response->assertStatus(302);
});

test('print wip failed - Wips cant be printed', function () {
    $barcode = SaleOrderItemBarcode::get();
    $token = $this->token;

    foreach ($barcode as $item) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $item->label_id . '/receive');
        $response->assertStatus(200);
    }

    $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt, [
        'station_id' => '1',
        'wip_ids' => [
            $barcode[0]->label_id
        ]
    ]);
});

test('print wip failed - All WIP IDs must have the same SKU.', function () {
    $barcode = SaleOrderItemBarcode::get();
    SaleOrderItem::first()->update(['product_id' => $this->products[1]->id, 'product_sku' => $this->products[1]->sku]);
    $token = $this->token;

    foreach ($barcode as $item) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $item->label_id . '/receive');
        $response->assertStatus(200);
    }

    $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbt, [
        'station_id' => '1',
        'wip_ids' => [
            $barcode[0]->label_id,
            $barcode[1]->label_id
        ]
    ]);
});

test('confirm print success.', function () {
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);
    }

    $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => $barcodes[1]->label_id,
        'location' => 'moving 4',
        'employee_id' => 13579,

    ]);
    $this->assertDatabaseHas('location', [
        'warehouse_id' => 1,
        'barcode' => 'moving 4',
        'type' => 3,
    ]);
    $this->assertDatabaseHas('location_product', [
        'product_id' => $this->products[0]->id,
        'quantity' => -2,
    ]);
    $this->assertDatabaseHas('product_quantity', [
        'product_id' => $this->products[0]->id,
        'quantity' => -2,
        'warehouse_id' => 1,
    ]);
});

test('confirm print success - auto adjustment', function () {
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $i = 1;
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);

        RbtBatchNumber::insert([
            'label_id' => $barcode->label_id,
            'batch_id' => $this->barcodePrinted->id,
            'batch_number' => $i++
        ]);
    }

    $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => $barcodes[0]->label_id,
        'location' => 'moving 4',
        'employee_id' => 13579,

    ]);
    $this->assertDatabaseHas('location', [
        'warehouse_id' => 1,
        'barcode' => 'moving 4',
        'type' => 3,
    ]);
    $this->assertDatabaseHas('location_product', [
        'product_id' => $this->products[0]->id,
        'quantity' => 0,
    ]);
    $this->assertDatabaseHas('product_quantity', [
        'product_id' => $this->products[0]->id,
        'quantity' => 0,
        'warehouse_id' => 1,
    ]);
});

test('confirm print failed - barcode already printed', function () {
    BarcodePrinted::where('id', $this->barcodePrinted->id)->update([
        'print_status' => true
    ]);
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $i = 1;
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);

        RbtBatchNumber::insert([
            'label_id' => $barcode->label_id,
            'batch_id' => $this->barcodePrinted->id,
            'batch_number' => $i++
        ]);
    }

    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => $barcodes[0]->label_id,
        'location' => 'moving 4',
        'employee_id' => 13579,

    ]);
    $response->assertStatus(422);
});

test('confirm print failed - barcode not found', function () {
    BarcodePrinted::where('id', $this->barcodePrinted->id)->update([
        'print_status' => true
    ]);
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $i = 1;
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);

        RbtBatchNumber::insert([
            'label_id' => $barcode->label_id,
            'batch_id' => $this->barcodePrinted->id,
            'batch_number' => $i++
        ]);
    }

    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => 2131223123123,
        'location' => 'moving 4',
        'employee_id' => 13579,

    ]);
    $response->assertStatus(302);
});

test('confirm print failed - No location found containing SKU', function () {
    Location::query()->update([
        'rbt_sku' => null,
    ]);
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);
    }

    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => $barcodes[1]->label_id,
        'location' => 'moving 4',
        'employee_id' => 13579,
    ]);

    $response->assertStatus(422);
    $response->assertJson([
        'message' => "No location found containing SKU '{$this->products[0]->sku}'",
    ]);
});

test('confirm print success - discard', function () {
    SaleOrderItemBarcode::where('barcode_printed_id', 0)->update([
        'barcode_printed_id' => $this->barcodePrinted->id
    ]);
    $barcodes = SaleOrderItemBarcode::where('barcode_printed_id', $this->barcodePrinted->id)->get();
    $token = $this->token;

    foreach ($barcodes as $barcode) {
        $response = $this->withHeaders([
            'Authorization' => "Bearer $token",
        ])->post($this->endpointGetApiRbt . '/' . $barcode->label_id . '/receive');
        $response->assertStatus(200);
    }

    $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiRbtConfirmPrint, [
        'station_id' => '1',
        'last_wip_id' => $barcodes[1]->label_id,
        'location' => 'moving 4',
        'employee_id' => 13579,
        'destroy' => true

    ]);
    $this->assertDatabaseHas('sale_order_item_barcode', [
        'label_root_id' => $barcodes[0]->label_id,
        'is_deleted' => 0,
    ]);
    $this->assertDatabaseHas('sale_order_item_barcode', [
        'label_id' => $barcodes[0]->label_id,
        'is_deleted' => true,
    ]);
});
