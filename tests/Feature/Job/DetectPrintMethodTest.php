<?php

use App\Jobs\DetectPrintMethod;
use App\Models\PrintMethod;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    SaleOrderItemBarcode::factory()->createMany([
        [
            'id' => 1,
            'order_item_id' => 1
        ],
        [
            'id' => 2,
            'order_item_id' => 2
        ],
        [
            'id' => 3,
            'order_item_id' => 3
        ],
    ]);
    SaleOrderItem::factory()->createMany([
        [
            'id' => 1,
            'product_style_sku' => 'sku1',
        ],
        [
            'id' => 2,
            'product_style_sku' => 'sku2',
        ],
        [
            'id' => 3,
            'product_style_sku' => 'sku3',
        ],
    ]);
    SaleOrderItemImage::factory()->createMany([
        [
            'order_item_id' => 1,
            'print_side' => 0
        ],
        [
            'order_item_id' => 1,
            'print_side' => 1
        ],
        [
            'order_item_id' => 2,
            'print_side' => 1
        ],
        [
            'order_item_id' => 2,
            'print_side' => 2
        ],
        [
            'order_item_id' => 2,
            'print_side' => 3
        ],
        [
            'order_item_id' => 3,
            'print_side' => 0
        ],
    ]);
    ProductStyle::factory()->createMany([
        [
            'id' => 1,
            'sku' => 'sku1',
        ],
        [
            'id' => 2,
            'sku' => 'sku2',
        ],
        [
            'id' => 3,
            'sku' => 'sku3',
        ],
    ]);
    PrintMethod::factory()->createMany([
        [
            'id' => 1,
            'name' => 'DTG',
            'priority' => 1,
        ],
        [
            'id' => 2,
            'name' => 'UV',
            'priority' => 2,
        ],
        [
            'id' => 3,
            'name' => 'MUGS',
            'priority' => 3,
        ],
        [
            'id' => 4,
            'name' => 'DTF',
            'priority' => 4,
        ],
    ]);
    ProductPrintSide::factory()->createMany([
        [
            'code' => 0,
            'name' => 'Front',
        ],
        [
            'code' => 1,
            'name' => 'Back',
        ],
        [
            'code' => 2,
            'name' => 'Pocket',
        ],
        [
            'code' => 3,
            'name' => 'Nack',
        ],
    ]);
    ProductPrintArea::factory()->createMany([
        [
            'product_style_id' => 1,
            'name' => 'Front',
            'print_method' => 'DTG',
        ],
        [
            'product_style_id' => 1,
            'name' => 'Back',
            'print_method' => 'DTG',
        ],
        [
            'product_style_id' => 1,
            'name' => 'Pocket',
            'print_method' => 'UV',
        ],
        [
            'product_style_id' => 1,
            'name' => 'Nack',
            'print_method' => 'DTF',
        ],
        [
            'product_style_id' => 2,
            'name' => 'Front',
            'print_method' => 'DTG',
        ],
        [
            'product_style_id' => 2,
            'name' => 'Back',
            'print_method' => 'DTF',
        ],
        [
            'product_style_id' => 2,
            'name' => 'Pocket',
            'print_method' => 'UV',
        ],
        [
            'product_style_id' => 2,
            'name' => 'Nack',
            'print_method' => 'DTF',
        ],
        [
            'product_style_id' => 3,
            'name' => 'Front',
            'print_method' => 'MUGS',
        ],
    ]);
});

test('expect DTG print method', function () {
    $s = new DetectPrintMethod(1);
    $s->actionDetect();
    $barcode = SaleOrderItemBarcode::find(1);
    expect($barcode->print_method)->toEqual('DTG');
})->skip();

test('expect DTF print method', function () {
    $s = new DetectPrintMethod(2);
    $s->actionDetect();
    $barcode = SaleOrderItemBarcode::find(2);
    expect($barcode->print_method)->toEqual('DTF');
})->skip();
test('expect MUGS print method', function () {
    $s = new DetectPrintMethod(3);
    $s->actionDetect();
    $barcode = SaleOrderItemBarcode::find(3);
    expect($barcode->print_method)->toEqual('MUGS');
})->skip();


