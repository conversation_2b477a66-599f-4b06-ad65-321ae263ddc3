<?php

use App\Models\Promotion;
use App\Models\PromotionType;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->store = Store::factory()->create();
    $this->promotionType = PromotionType::create([
        'id' => 1,
        'name' => 'Additional Print Area',
    ]);
    $this->post('/api/promotion', [
        'store_id' => $this->store->id,
        'promotion_type_id' => $this->promotionType->id,
        'is_public' => Promotion::ACTIVE,
        'start_time' => now()->toDateTimeString(),
        'amount' => 0.2,
        'end_time' => now()->addDays(2)->toDateTimeString()
    ]);
    $this->promotion = Promotion::first();
    $this->endpoint = "/api/promotion/{$this->promotion->id}/update-status";
    $this->params = [
        'is_active' => Promotion::ACTIVE,
    ];
});

test('Update fail - The promotion is currently active.', function () {
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_BAD_REQUEST);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Unable to activate promotion.'
    ]);
});

test('Update Success', function () {
    $this->params['is_active'] = Promotion::INACTIVE;
    $this->promotion->is_public = Promotion::ACTIVE;
    $this->promotion->save();
    $response = $this->put($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_OK);
    $this->assertDatabaseHas('promotions', [
        'id' => $this->promotion->id,
        'is_active' => Promotion::INACTIVE
    ]);
//    expect(json_decode($response->getContent(), true))->toMatchArray([
//        'message' => 'Unable to activate promotion.'
//    ]);
});
