<?php

use App\Models\Employee;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Warehouse;
use App\Repositories\BarcodeLatexRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    ['warehouse' => $warehouse] = createAccessToken();
    Config::set('jwt.warehouse_id', $warehouse->id);
    $this->warehouse = $warehouse;

    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'code' => 1011,
    ]);

    $this->store = Warehouse::factory()->create();

    $this->saleorder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => 1,
        'order_quantity' => 5,
    ]);

    $this->productStyle = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XXX',
    ]);

    $this->product = Product::factory()->create([
        'sku' => 'xxx'
    ]);

    $this->saleOrderItem = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 3,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 7,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 3));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 7));
    SaleOrderItemBarcode::factory()->createMany($mock);

    putenv('ID_SALE_ORDER_VALID=110');
});

test('Correctly count pending xqc success', function () {
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(10)
        ->and($data['printed_at'])
        ->toBeNull();
});

test('Correctly count pending xqc pass param.warehouse_id', function () {
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(10)
        ->and($data['printed_at'])
        ->toBeNull();
});

test('Correctly count pending xqc not pass param.warehouse_id', function () {
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id + 1
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])
        ->toBeNull();
});

test('Correctly count pending xqc exclude bulk order', function () {
    $this->saleorder->order_quantity = 20;
    $this->saleorder->save();
    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])
        ->toBeNull();
});

test('Correctly count pending xqc priority with fba', function () {
    $this->saleorder->is_fba_order = 1;
    $this->saleorder->save();
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataFba = resolve(BarcodeLatexRepository::class)->countPendingFba([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataFba['total'])->toBe(10);
});

test('Correctly count pending xqc priority with eps', function () {
    $this->saleorder->is_eps = true;
    $this->saleorder->save();
    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(10)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataEps = resolve(BarcodeLatexRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataEps['total'])->toBe(0);
});

test('Correctly count pending xqc priority with reprint', function () {
    $this->saleorder->is_eps = true;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = false;
    $this->saleorder->save();
    SaleOrderItemBarcode::query()->update([
        'label_root_id' => 1,
        'reprint_status' => false,
        'employee_reroute_id' => null,
    ]);

    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataReprint = resolve(BarcodeLatexRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(10);
});

test('Correctly count pending xqc priority with manual', function () {
    $this->saleorder->order_type = SaleOrder::ORDER_TYPE_NORMAL;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = true;
    $this->saleorder->save();
    SaleOrderItemBarcode::query()->update([
        'label_root_id' => null,
        'reprint_status' => false,
        'employee_reroute_id' => null,
    ]);

    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataReprint = resolve(BarcodeLatexRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(10);
});

test('Correctly count pending xqc priority with reroute', function () {
    $this->saleorder->order_type = SaleOrder::ORDER_TYPE_NORMAL;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = true;
    $this->saleorder->save();
    SaleOrderItemBarcode::query()->update([
        'label_root_id' => null,
        'reprint_status' => false,
        'employee_reroute_id' => 1,
    ]);

    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataReprint = resolve(BarcodeLatexRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(10);
});

test('Correctly count pending xqc priority with styles', function () {
    $this->saleorder->order_type = 1;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = false;
    $this->saleorder->save();
    SaleOrderItemBarcode::query()->update([
        'label_root_id' => null,
        'reprint_status' => false,
        'employee_reroute_id' => null,
    ]);

    $data = resolve(BarcodeLatexRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(10)
        ->and($data['printed_at'])
        ->toBeNull();

    $dataReprint = resolve(BarcodeLatexRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});
