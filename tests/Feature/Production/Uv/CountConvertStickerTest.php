<?php

use App\Models\Employee;
use App\Models\PdfConverted;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/sticker/count';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    // data match
    $this->productStyle = ProductStyle::factory([
        'print_method' => 'UV',
        'type' => ProductType::STICKER
    ])
        ->has(Product::factory([
            'parent_id' => 111,
        ]), 'product')
        ->create();

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'print_method' => 'UV',
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->barcodePrinted = PdfConverted::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouse->id,
        'print_method' => $this->productStyle->print_method
    ])
        ->create();

    $this->keySuccess = [
        'code',
        'success',
        'message',
        'data.data'
    ];

    $this->keySuccessChild = [
        'last_created_at',
        'product_id',
        'sku'
    ];
});

// Chỉ lấy ra những product có print method là UV
test('no products with print method uv and product type not Sticker', function () {
    $productStyle = ProductStyle::factory([
        'print_method' => 'UV',
        'type' => ProductType::ORNAMENT
    ])
        ->has(Product::factory([
            'parent_id' => 222,
        ]), 'product')
        ->create();

    SaleOrderItem::factory([
        'product_id' => $productStyle->product->last()->id,
        'order_id' => $this->saleOrder->id,
    ])->has(SaleOrderItemBarcode::factory([
        'order_id' => $this->saleOrder->id,
        'print_method' => 'UV',
        'warehouse_id' => $this->warehouse->id,
    ]), 'barcodes')
        ->create();

    PdfConverted::factory([
        'product_id' => $productStyle->product->last()->id,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouse->id,
        'print_method' => $productStyle->print_method
    ])
        ->create();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    $this->assertEquals(count($jsonRes['data']['data']), 1);
});

// Success
test('success', function () {
    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    $this->assertEquals(count($jsonRes['data']['data']), 1);
});
