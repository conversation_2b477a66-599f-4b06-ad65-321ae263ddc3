<?php
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Product;
use App\Models\SaleOrderHistory;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierPackage;
use App\Models\ShippingCarrierService;
use App\Models\User;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Factories\Sequence;
use App\Models\Store;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderAddress;
use App\Models\StoreAddress;
use \App\Models\SaleOrderAccount;
use App\Models\Shipment;
use App\Models\ShippingCarrierEasypost;
use App\Models\WeightCubic;
use App\Models\ProductStyle;
use App\Models\ProductTypeWeight;
use App\Models\ProductSize;
use App\Models\ShipmentItem;
use App\Models\ShippingMethod;
use App\Models\SaleOrderItemImage;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = 'api/insert-printing/get-label?';
    $this->product = Product::factory()->createMany([
        [
            "sku" => 'UNPT9C0XS',
            "style" => 'INSE',
            "size" => '4x6'
        ],
        [
            "sku" => 'UNPT2H00M',
            "style" => 'INSE',
            "size" => '4x6'
        ]
    ]);
    $this->user = User::factory()->create();
    $this->shippingCarrier = ShippingCarrier::factory()->createMany([
        [
            "name"       => "USPS",
            "code"       => "USPS",
            "is_deleted" => 0,
            "tracking_url" => "USPS_trackingNumber={tracking_code}"
        ],
        [
            "name"       => "Asendia USA",
            "code"       => "AsendiaUsa",
            "is_deleted" => 0,
            "tracking_url" => "Asendia_trackingNumber={tracking_code}"
        ]
    ]);
    $this->saleOrder = SaleOrder::factory()
        ->for(SaleOrderAccount::factory(), 'account')
        ->for(Store::factory()
            ->has(StoreAddress::factory()->count(2)->state(new Sequence(
                [
                    'type_address' => StoreAddress::TYPE_ADDRESS_RETURN
                ],
                [
                    'type_address' => StoreAddress::TYPE_ADDRESS_TO
                ]
            )), 'storeAddress')
            , 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            [
                'type_address' => SaleOrderAddress::TO_ADDRESS
            ],
            [
                'type_address' => SaleOrderAddress::RETURN_ADDRESS
            ]
        )), 'addressSaleOrder')
        ->has(SaleOrderHistory::factory([
            'type' => 'Create Label',
            'user_id' => $this->user->id
        ]), 'timeline')
        ->has(Shipment::factory()->count(2)->state(new Sequence(
            [
                'tracking_number' => *********,
                'carrier_code' => $this->shippingCarrier[0]->code
            ],
            [
                'tracking_number' => 987654321,
                'carrier_code' => $this->shippingCarrier[1]->code
            ]
        )), 'shipment')
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-9999-1',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'label_id' => '071722-SJ-M-9999-2',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ]
        )), 'barcodeItems')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => 'new_order',
            'shipping_method' => 'standard',
        ]);
    $dataMockSaleOrderItem = [];
    $dataMockProductStyle = [];
    foreach ($this->product as $key => $item) {
        $dataMockSaleOrderItem[] = [
            'order_id'   => $this->saleOrder->id,
            'quantity'   => 1,
            'product_id' => $item->id,
            'product_sku' => $item->sku,
            'product_style_sku' => substr($item->sku, 0, 4),
            'product_size_sku' => substr($item->sku, -3),
            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/2st.s3.us-east-2.amazonaws.com\/Design\/PancreaticCancerAwarenessUnisexTshirtInThisFamilyNobodyFightsAloneSurvivorFighterGiftAwarenessMonth.png"},{"name":"PreviewFiles.Front","value":"https:\/\/i.etsystatic.com\/24906590\/r\/il\/2341c6\/3145734374\/il_fullxfull.3145734374_gcm9.jpg"}]'
        ];
        $dataMockProductStyle[] = [
            'sku'  => substr($item->sku, 0, 4),
            'type' => 'Tee',
            'name' => 'INSE',
        ];
    }
    $this->saleOrderItem = SaleOrderItem::factory()->createMany($dataMockSaleOrderItem);
    $this->productStyle = ProductStyle::factory()->createMany($dataMockProductStyle);
    $this->params = array(
        'label_id' => ''
    );
});

test('validate label', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors'])->toMatchArray([
        "label_id" => [ "Label field is required."],
    ]);
});

test('get oder vaild', function () {
    //order not found
    $this->params['label_id'] = 'datpt';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => "Order not found!"
    ]);

    $saleOrder = SaleOrder::factory([
        'order_number' => '071722-SJ-M-000368',
        'warehouse_id' => $this->warehouse->id,
        'order_status' => 'draft'
    ])
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-000368-1',
                'warehouse_id' => $this->warehouse->id,
                'is_deleted' => 0
            ],
            [
                'label_id' => '071722-SJ-M-000368-2',
                'warehouse_id' => $this->warehouse->id,
                'is_deleted' => 1
            ]
        )), 'barcodeItems')
        ->create();
    $this->params['label_id'] = $saleOrder->barcodeItems[0]->label_id;
    //draft
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => 'Sale order is ' . str_replace('_', ' ', $saleOrder->order_status),
    ]);
    //on_hold
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'on_hold']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => "Sale order is on hold",
    ]);
    //cancelled
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'cancelled']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => "Sale order is cancelled",
    ]);
    //rejected
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'rejected']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => "Sale order is rejected",
    ]);

    // label is not Insert printing label
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $this->saleOrder->barcodeItems[0]->order_item_id = $this->saleOrderItem[0]->id;
    $this->saleOrder->barcodeItems[0]->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "message" => "Label is not Insert printing label",
    ]);
});

test('get label success', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $this->saleOrder->barcodeItems[0]->order_item_id = $this->saleOrderItem[0]->id;
    $this->saleOrder->barcodeItems[0]->save();
    $this->productStyle[0]->type = 'Insert';
    $this->productStyle[0]->save();
    $this->productStyle[1]->type = 'Insert';
    $this->productStyle[1]->save();
    $saleOrderImage = SaleOrderItemImage::factory()->count(2)->state(new Sequence(
        [
            'image_url' => 'https://images-merch.printify.com/branding-insert/b58e3312-469e-4386-ac88-9724bf4eae7d/print/file.pdf',
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'product_sku' => $this->saleOrderItem[0]->product_sku,
            'image_ext' => 'pdf'
        ],
        [
            'image_url' => 'https://cdn.pixabay.com/photo/2017/02/25/23/50/animal-2099057_1280.pdf',
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'product_sku' => $this->saleOrderItem[1]->product_sku,
            'image_ext' => 'pdf'
        ]
    ))->create();
    $dataResponse = [
        'status' => true,
        'url' => 'https://images-merch.printify.com/branding-insert/b58e3312-469e-4386-ac88-9724bf4eae7d/print/file.pdf',
    ];
    $this->mock( \App\Repositories\InsertPrintingRepository::class, function (MockInterface $mock) use($dataResponse) {
        $mock->shouldReceive('generateOrderInsert')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataReturnResponse = json_decode($response->getContent(), true);
    expect($dataReturnResponse['label_id'])->toMatchArray([$this->params['label_id']]);
    expect($dataReturnResponse['sale_order_item_image'][0]['url_s3'])->toMatchArray([$dataResponse['url']]);
});




