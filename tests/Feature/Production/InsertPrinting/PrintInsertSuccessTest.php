<?php

use App\Models\Employee;
use App\Models\SaleOrder;
use App\Models\SaleOrderInsert;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/insert-printing/printed-success';

    $this->saleOrder = SaleOrder::factory()
        ->has(SaleOrderInsert::factory()->count(2)->state(new Sequence(
            [
                'type' => SaleOrderInsert::GIFT_MESSAGE_TYPE,
            ],
            [
                'type' => SaleOrderInsert::PACKING_SLIP_TYPE,
            ],
        )), 'orderInsert')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'shipping_method' => 'standard',
        ]);
    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0,
    ]);

    $this->params = [
        'order_insert_id' => $this->saleOrder->orderInsert->first()->id,
        'employee_id' => $this->employee->id,
    ];
});

test('validate input', function () {
    $this->params = [
        'order_insert_id' => '',
        'employee_id' => '',
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors'])->toMatchArray([
        'order_insert_id' => ['Order insert field is required.'],
        'employee_id' => ['Employee field is required.']
    ]);

    $this->params = [
        'order_insert_id' => 99999,
        'employee_id' => 9999,
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors'])->toMatchArray([
        'order_insert_id' => ['Order insert is not found.'],
        'employee_id' => ['Employee is not found.']
    ]);

    $employee = Employee::factory()->create([
        'warehouse_id' => 9999,
        'is_deleted' => 1,
    ]);
    $this->params = [
        'order_insert_id' => $this->saleOrder->orderInsert->first()->label_id,
        'employee_id' => $employee->id,
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors'])->toMatchArray([
        'employee_id' => ['Employee is not found.']
    ]);

    $employee->is_deleted = 0;
    $employee->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors'])->toMatchArray([
        'employee_id' => ['Employee is not found.']
    ]);
});

test('print success', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $res = json_decode($response->getContent(), true);
    $response->assertStatus(200);
    expect($res['status'])->toMatchArray([true]);

    $this->assertDatabaseHas('sale_order_history', [
        'employee_id' => $this->params['employee_id'],
        'order_id' => $this->saleOrder->orderInsert->first()->order_id,
        'message' => 'Printed insert: ' . str_replace('_', ' ', $this->saleOrder->orderInsert->first()->type)]);
    $dataOrderInsert = SaleOrderInsert::where('id', $this->saleOrder->orderInsert->first()->id)->first();
    $this->assertNotNull($dataOrderInsert->printed_at);
});
