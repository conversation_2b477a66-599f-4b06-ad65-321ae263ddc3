<?php

use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/barcode/count';
    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'code' => 1011,
    ]);
    $this->productStyle = ProductStyle::factory()->create([
        'type' => 'test',
        'sku' => 'UNGT',
        'name' => 'UNGT',
    ]);
    $this->product = Product::factory()->create([
        'id' => 1000,
        'parent_id' => 111,
        'style' => 'UNGT',
        'sku' => 'UNPT9C0XS',
    ]);
    $this->store = Store::factory()->create();
    $this->saleOrder = SaleOrder::factory()->create([
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'shipment_id' => null,
        'created_at' => '2024-11-20 20:00:00',
    ]);
    $this->saleOrderItem = SaleOrderItem::factory()->create([
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id,
        'product_id' => $this->product->id,
        'product_sku' => $this->product->sku,
        'product_style_sku' => $this->productStyle->sku,
        'ink_color_status' => 1,
    ]);
    $this->saleOrderItemBarcode = SaleOrderItemBarcode::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id,
        'order_item_id' => $this->saleOrderItem->id,
        'barcode_printed_id' => 0,
        'is_deleted' => 0,
        'print_method' => ProductStyle::METHOD_DTG,
    ]);
});

//XQC
test('XQC success with not filter datetime', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(1)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('XQC success with filter datetime have record', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(1)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('XQC success with filter range datetime have record', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(1)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('XQC success with filter datetime not have record', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('XQC success with filter range datetime not have record', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//EPS
test('EPS success with not filter datetime', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(1)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('EPS success with filter datetime have record', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(1)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('EPS success with filter range datetime have record', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(1)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('EPS success with filter datetime not have record', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('EPS success with filter range datetime not have record', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Reprint
test('Reprint success with not filter datetime', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(1)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reprint success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(1)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reprint success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(1)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reprint success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reprint success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Manual
test('Manual success with not filter datetime', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(1)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Manual success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(1)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Manual success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(1)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Manual success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Manual success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Reroute
test('Reroute success with not filter datetime', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(1)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reroute success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(1)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reroute success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(1)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reroute success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Reroute success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Fba
test('Fba success with not filter datetime', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(1)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Fba success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(1)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Fba success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(1)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Fba success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Fba success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Styles
test('Styles success with not filter datetime', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['name'])->toBe($this->productStyle->name)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(1)
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Styles success with filter datetime have record', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['name'])->toBe($this->productStyle->name)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(1)
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Styles success with filter range datetime have record', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['name'])->toBe($this->productStyle->name)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(1)
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Styles success with filter datetime not have record', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

test('Styles success with filter range datetime not have record', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();
});

//Tiktok
test('Tiktok success with not filter datetime', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(1)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();
});

test('Tiktok success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(1)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();
});

test('Tiktok success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(1)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();
});

test('Tiktok success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(0)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

test('Tiktok success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(0)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

//Bulk order
test('Bulk order success with not filter datetime', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(1)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

test('Bulk order success with filter datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-20&end=2024-11-20');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(1)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

test('Bulk order success with filter range datetime have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-01&end=2024-11-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(1)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

test('Bulk order success with filter datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-11-19&end=2024-11-19');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(0)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});

test('Bulk order success with filter range datetime not have record', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $response = $this->get($this->endpoint . '?start=2024-10-01&end=2024-10-30');
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(0)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();
});
