<?php

use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/barcode/count';
    $this->endpointConfirm = 'api/barcode/print';
    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'code' => 1011,
    ]);
    $this->productStyle = ProductStyle::factory()->create([
        'type' => 'test',
        'sku' => 'UNGT',
        'name' => 'UNGT',
    ]);
    $this->product = Product::factory()->create([
        'id' => 1000,
        'parent_id' => 111,
        'style' => 'UNGT',
        'sku' => 'UNPT9C0XS',
    ]);
    $this->store = Store::factory()->create();
    $this->saleOrder = SaleOrder::factory()->create([
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'shipment_id' => null,
        'created_at' => '2024-11-20 20:00:00',
    ]);
    $this->saleOrderItem = SaleOrderItem::factory()->create([
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id,
        'product_id' => $this->product->id,
        'product_sku' => $this->product->sku,
        'product_style_sku' => $this->productStyle->sku,
        'ink_color_status' => 1,
    ]);
    $this->saleOrderItemBarcode = SaleOrderItemBarcode::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id,
        'order_item_id' => $this->saleOrderItem->id,
        'barcode_printed_id' => 0,
        'is_deleted' => 0,
        'print_method' => ProductStyle::METHOD_DTG,
    ]);

    $this->saleOrder2 = SaleOrder::factory()->create([
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'shipment_id' => null,
        'created_at' => '2024-11-21 20:00:00',
    ]);
    $this->saleOrderItem2 = SaleOrderItem::factory()->create([
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder2->id,
        'product_id' => $this->product->id,
        'product_sku' => $this->product->sku,
        'product_style_sku' => $this->productStyle->sku,
        'ink_color_status' => 1,
    ]);
    $this->saleOrderItemBarcode2 = SaleOrderItemBarcode::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder2->id,
        'order_item_id' => $this->saleOrderItem2->id,
        'barcode_printed_id' => 0,
        'is_deleted' => 0,
        'print_method' => ProductStyle::METHOD_DTG,
    ]);
});

//XQC
test('XQC success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 1;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(2)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => 1,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(1)
        ->and($jsonRes['xqc']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('XQC success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 1;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(2)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => 1,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(1)
        ->and($jsonRes['xqc']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('XQC success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 1;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(2)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => 1,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//EPS
test('EPS success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_eps = 1;
    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(2)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => 1,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(1)
        ->and($jsonRes['eps']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('EPS success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_eps = 1;
    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(2)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => 1,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(1)
        ->and($jsonRes['eps']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('EPS success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_eps = 1;
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_eps = 1;
    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(2)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => 1,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Reprint
test('Reprint success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = 1;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(2)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => 1,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(1)
        ->and($jsonRes['reprint']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Reprint success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = 1;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(2)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => 1,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(1)
        ->and($jsonRes['reprint']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Reprint success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = 1;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = 1;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(2)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => 1,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Manual
test('Manual success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(2)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => 1,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(1)
        ->and($jsonRes['manual']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Manual success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(2)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => 1,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(1)
        ->and($jsonRes['manual']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Manual success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(2)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => 1,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Reroute
test('Reroute success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(2)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => 1,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(1)
        ->and($jsonRes['reroute']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Reroute success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(2)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => 1,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(1)
        ->and($jsonRes['reroute']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Reroute success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(2)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => 1,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Fba
test('Fba success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(2)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => 1,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(1)
        ->and($jsonRes['fba']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Fba success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(2)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => 1,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(1)
        ->and($jsonRes['fba']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Fba success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(2)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => 1,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Styles
test('Styles success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_eps = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(2)
        ->and($jsonRes['styles'][0]['printed_at'])->toBeNull()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => 'UNGT',
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(1)
        ->and($jsonRes['styles'][0]['printed_at'])->not()->toBeNull()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Styles success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_eps = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(2)
        ->and($jsonRes['styles'][0]['printed_at'])->toBeNull()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => 'UNGT',
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(1)
        ->and($jsonRes['styles'][0]['printed_at'])->not()->toBeNull()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Styles success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_xqc = 0;
    $this->saleOrder->is_eps = 0;
    $this->saleOrder->is_manual = 0;
    $this->saleOrder->is_fba_order = 0;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = null;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_xqc = 0;
    $this->saleOrder2->is_eps = 0;
    $this->saleOrder2->is_manual = 0;
    $this->saleOrder2->is_fba_order = 0;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = null;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toHaveCount(1)
        ->and($jsonRes['styles'][0]['product_style_sku'])->toBe($this->productStyle->sku)
        ->and($jsonRes['styles'][0]['total'])->toBe(2)
        ->and($jsonRes['styles'][0]['printed_at'])->toBeNull()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => 'UNGT',
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Tiktok
test('Tiktok success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 6;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(2)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => 1,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(1)
        ->and($jsonRes['tiktok']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Tiktok success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 6;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(2)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => 1,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(1)
        ->and($jsonRes['tiktok']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Tiktok success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 6;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 6;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(2)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => 1,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

//Bulk order
test('Bulk order success confirm with filter datetime 2024-11-20', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 1;
    $this->saleOrder2->order_quantity = 20;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(2)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => 1,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-20',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(1)
        ->and($jsonRes['bulk_orders']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);
});

test('Bulk order success confirm with filter datetime 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 1;
    $this->saleOrder2->order_quantity = 20;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(2)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => 1,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-21',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(1)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(1)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['printed_at'])->not()->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(1)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Bulk order success confirm with filter datetime 2024-11-20 + 2024-11-21', function () {
    $this->saleOrder->is_manual = 1;
    $this->saleOrder->order_type = 1;
    $this->saleOrder->order_quantity = 20;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();

    $this->saleOrderItemBarcode->reprint_status = 0;
    $this->saleOrderItemBarcode->label_root_id = null;
    $this->saleOrderItemBarcode->employee_reroute_id = 1;
    $this->saleOrderItemBarcode->barcode_printed_id = 0;
    $this->saleOrderItemBarcode->save();

    $this->saleOrder2->is_manual = 1;
    $this->saleOrder2->order_type = 1;
    $this->saleOrder2->order_quantity = 20;
    $this->saleOrder2->is_fba_order = 1;
    $this->saleOrder2->save();

    $this->saleOrderItemBarcode2->reprint_status = 0;
    $this->saleOrderItemBarcode2->label_root_id = null;
    $this->saleOrderItemBarcode2->employee_reroute_id = 1;
    $this->saleOrderItemBarcode2->barcode_printed_id = 0;
    $this->saleOrderItemBarcode2->save();

    expect(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->toBe(0);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'][0]['id'])->toBe($this->store->id)
        ->and($jsonRes['stores'][0]['total'])->toBe(2)
        ->and($jsonRes['warehouse']['warehouse_id'])->toBe($this->warehouse->id)
        ->and($jsonRes['warehouse']['total'])->toBe(2)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['total'])->toBe(2)
        ->and($jsonRes['bulk_orders']['printed_at'])->toBeNull();

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => 1,
        'is_tiktok' => null,
        'limit' => 2,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
        'start' => '2024-11-20',
        'end' => '2024-11-21',
    ]);
    $response->assertStatus(200);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    expect($jsonRes['styles'])->toBeEmpty()
        ->and($jsonRes['stores'])->toBeEmpty()
        ->and($jsonRes['warehouse']['warehouse_id'])->toBeNull()
        ->and($jsonRes['warehouse']['total'])->toBe(0)
        ->and($jsonRes['xqc']['total'])->toBe(0)
        ->and($jsonRes['xqc']['printed_at'])->toBeNull()
        ->and($jsonRes['eps']['total'])->toBe(0)
        ->and($jsonRes['eps']['printed_at'])->toBeNull()
        ->and($jsonRes['reprint']['total'])->toBe(0)
        ->and($jsonRes['reprint']['printed_at'])->toBeNull()
        ->and($jsonRes['manual']['total'])->toBe(0)
        ->and($jsonRes['manual']['printed_at'])->toBeNull()
        ->and($jsonRes['reroute']['total'])->toBe(0)
        ->and($jsonRes['reroute']['printed_at'])->toBeNull()
        ->and($jsonRes['fba']['total'])->toBe(0)
        ->and($jsonRes['fba']['printed_at'])->toBeNull()
        ->and($jsonRes['tiktok']['total'])->toBe(0)
        ->and($jsonRes['tiktok']['printed_at'])->toBeNull()
        ->and($jsonRes['bulk_orders']['printed_at'])->not()->toBeNull()
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode->id)->first()->barcode_printed_id)->not()->toBe(0)
        ->and(SaleOrderItemBarcode::where('id', $this->saleOrderItemBarcode2->id)->first()->barcode_printed_id)->not()->toBe(0);
});

test('Manual order success confirm with filter datetime ', function () {
    $this->saleOrderManual = SaleOrder::factory([
        'id' => 2000000111,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_manual' => SaleOrder::ACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'id' => 39521530011,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
        [
            'id' => 3952153001111,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
    ), 'barcodes'), 'items')
        ->create();

    $this->saleOrderManual->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrderManual->id;
            $barcode->created_at = \Carbon\Carbon::now()->subDays(20);
            $barcode->save();
        });
    });

    $response = $this->post($this->endpointConfirm, [
        'employee_id' => $this->employee->id,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'limit' => 1,
        'is_eps' => null,
        'is_fba' => null,
        'is_manual' => 1,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'style_sku' => null,
    ]);
    $response->assertStatus(200);
    $jsonRes = json_decode($response->getContent(), true);
    $barcodePrinted = \App\Models\BarcodePrinted::where('warehouse_id', $this->warehouse->id)
        ->where('store_id', null)
        ->where('account_id', null)
        ->where('quantity_input', 1)
        ->where('is_xqc', null)
        ->where('is_eps', null)
        ->where('is_manual', 1)
        ->where('is_reprint', null)
        ->where('is_reroute', null)
        ->where('is_fba', null)
        ->where('is_insert', null)
        ->where('is_tiktok', null)
        ->where('is_bulk_order', null)
        ->where('is_top_style', false)
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcodes = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
        ->where('sale_order_item_barcode.barcode_printed_id', $response['id'])
        ->get();
    $this->assertCount(1, $barcodes);
})->skip();
