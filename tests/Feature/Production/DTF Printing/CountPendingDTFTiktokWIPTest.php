<?php

use App\Models\Employee;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Warehouse;
use App\Repositories\BarcodeDTFRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['warehouse' => $warehouse] = createAccessToken();
    Config::set('jwt.warehouse_id', $warehouse->id);
    $this->warehouse = $warehouse;

    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    $this->store = Warehouse::factory()->create();

    $this->saleOrder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
        'shipment_id' => true,
        'is_xqc' => false,
        'order_quantity' => 5,
    ]);

    $this->productStyle = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XXX',
    ]);

    $this->product = Product::factory()->create([
        'sku' => 'xxx'
    ]);

    $this->saleOrderItem = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleOrder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 2,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleOrder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 3,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 2));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 3));
    SaleOrderItemBarcode::factory()->createMany($mock);

    putenv('ID_SALE_ORDER_VALID=110');
});

test('Count pending Tiktok line', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line not pass param.warehouse_id', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id + 1
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line exclude xqc', function () {
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line priority with fba', function () {
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    $dataFba = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataFba['total'])->toBe(5);
});

test('Correctly count pending Tiktok line priority with eps', function () {
    $this->saleOrder->is_eps = true;
    $this->saleOrder->is_xqc = false;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataEps = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataEps['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with reprint', function () {
    $this->saleOrder->is_eps = true;
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = false;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->label_root_id = null;
    $barcode->reprint_status = false;
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with manual', function () {
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = true;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with reroute', function () {
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = true;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = 1;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with styles', function () {
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = false;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with Bulk order', function () {
    $saleOrder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
        'shipment_id' => true,
        'is_xqc' => false,
        'order_quantity' => 20,
    ]);

    $saleOrderItem = SaleOrderItem::factory()->create(
        [
            'order_id' => $saleOrder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 20,
            'product_sku' => $this->product->sku,
        ],
    );

    $mock = array_map(function () use ($saleOrderItem, $saleOrder) {
        return [
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 20));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(25)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);
    expect($dataReprint['total'])->toBe(0);
});

test('Count pending Tiktok line - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line not pass param.warehouse_id - order label', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id + 1
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line exclude xqc - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_xqc = 1;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending Tiktok line priority with fba - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_fba_order = 1;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    $dataFba = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataFba['total'])->toBe(5);
});

test('Correctly count pending Tiktok line priority with eps - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_eps = true;
    $this->saleOrder->is_xqc = false;
    $this->saleOrder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataEps = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataEps['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with reprint - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_eps = true;
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = false;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->label_root_id = null;
    $barcode->reprint_status = false;
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with manual - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = true;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with reroute - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = true;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = 1;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with styles - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->is_fba_order = false;
    $this->saleOrder->is_manual = false;
    $this->saleOrder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(5)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending Tiktok line priority with Bulk order - order label', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->save();
    $saleOrder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER,
        'shipment_id' => true,
        'is_xqc' => false,
        'order_quantity' => 20,
    ]);

    $saleOrderItem = SaleOrderItem::factory()->create(
        [
            'order_id' => $saleOrder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 20,
            'product_sku' => $this->product->sku,
        ],
    );

    $mock = array_map(function () use ($saleOrderItem, $saleOrder) {
        return [
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 20));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $data = resolve(BarcodeDTFRepository::class)->countPendingTiktok([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(25)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);
    expect($dataReprint['total'])->toBe(0);
});
