<?php

use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\TimeTracking;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->store = Store::factory()->create();

    $this->product = Product::factory(['name' => 'product-test'])->create();
    $this->productStyle = ProductStyle::factory()->count(2)->sequence(
        ['type' => 'Tee', 'sku' => 'UNGU'],
        ['type' => 'Fleece', 'sku' => 'UNPT'],
    )
        ->create();

    $this->productType = ProductType::factory()->count(2)->sequence(
        ['name' => 'Tee', 'icon' => 'tee.png'],
        ['name' => 'Fleece', 'icon' => 'fleece.png'],
    )
        ->create();

    $this->saleOrder = SaleOrder::factory([
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItem::factory([
        'warehouse_id' => $this->warehouse->id,
        'sku' => 'UNPTCS0XL',
        'product_id' => $this->product->id,
        'product_style_sku' => $this->productStyle->last()->sku,
        'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/d1ud88wu9m1k4s.cloudfront.net\/fulfill\/design\/2022\/08\/22\/fbcc7d9ed3af43eb26ce2625e2f1134e.png"},{"name":"PreviewFiles.Front","value":"https:\/\/d1ud88wu9m1k4s.cloudfront.net\/fulfill\/mockup\/2022\/08\/22\/65511047b6bdb6a7eaf97e496d30597f.jpg"}]'
    ]), 'items')
        ->create();

    $this->itemBarcode = SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'label_id' => '021622-SJ-M-000505-4',
        'sku' => 'UNPTCS0XL',
        'order_item_id' => $this->saleOrder->items->first()->id,
        'order_id' => $this->saleOrder->id,
    ])
        ->create();

    $this->printSide = ProductPrintSide::factory(['code_name' => 'Front', 'order' => 2])->create();

    $this->timeTracking = TimeTracking::factory()->count(2)->sequence(
        [
            'id' => 2000,
            'employee_id' => $this->employee->id,
            'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE,
            'quantity' => 0,
        ],
        [
            'parent_id' => 2000,
            'employee_id' => 164,
            'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE,
            'quantity' => 0,
        ],
    )
        ->create();

    $this->endpoint = 'api/quality-control';
    $this->params = [
        'label' => $this->itemBarcode->label_id,
        'employee_id' => $this->employee->id,
        'id_time_checking' => $this->timeTracking->pluck('id')->toArray(),
    ];
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->resultData = [
        'id',
        'sku',
        'staged_at',
        'options',
        'print_side',
        'name',
        'label_id',
        'order_id',
        'is_fba_order',
        'order_date',
        'store_name',
        'map_options',
        'order_pulled_status',
    ];

    $this->result = [
        'data',
        'iconProduct',
        'totalItem',
        'totalItemScan',
    ];
});

//miss label param
test('get quality control failed - miss label param', function () {
    unset($this->params['label']);
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//miss employee_id param
test('get quality control failed - miss employee_id param', function () {
    unset($this->params['employee_id']);
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//miss id_time_checking param
test('get quality control failed - miss id_time_checking param', function () {
    unset($this->params['id_time_checking']);
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//id_time_checking not found
test('get quality control failed - id_time_checking not found', function () {
    $this->params['id_time_checking'] = [100000];
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//employee not found
test('get quality control failed - employee not found', function () {
    Employee::query()->where('id', $this->employee->id)->update(['is_deleted' => true]);
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//label not found
test('get quality control failed - label not found', function () {
    $this->params['label'] = 'not_found';
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//product not found
test('get quality control failed - product not found', function () {
    Product::query()->delete();
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//order status = cancelled | rejected | draft | on_hold
test('get quality control failed - order status is cancelled | rejected | draft | on_hold', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::CANCELLED]);
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));

    $response->assertStatus(422);
});

//data true
test('get quality control success', function () {
    $response = $this->get($this->endpoint . '?' . http_build_query($this->params));
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    expect($response)->toHaveKeys($this->result);
    expect($response['data'])->toHaveKeys($this->resultData);
    $this->assertDatabaseHas('sale_order_history', ['order_id' => $this->itemBarcode->order_id, 'employee_id' => $this->params['employee_id'], 'type' => SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE]);
});
