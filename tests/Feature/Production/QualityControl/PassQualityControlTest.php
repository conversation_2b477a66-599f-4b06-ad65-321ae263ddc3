<?php

use App\Models\Employee;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemQualityControl;
use App\Models\TimeTracking;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->employee2 = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->product = Product::factory(['name' => '1234 / AKN / S'])->create();
    $this->timeTrackingQC = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE,
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);
    $this->timeTrackingStage = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => TimeTracking::STAGE_JOB_TYPE,
            'quantity' => 2,
            'start_time' => Carbon::now()
        ]);
    $this->timeTrackingQCPerson2 = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee2->id,
            'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE,
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);
    $this->cancelledLabel = '021622-SJ-M-000505-7';
    $this->newOrderLabel = '021622-SJ-M-000505-8';
    $this->saleOrder = SaleOrder::factory([
        'id' => 1,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'order_id' => 1,
        'warehouse_id' => $this->warehouse->id,
        'label_id' => '021622-SJ-M-000505-4',
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();

    $this->endpoint = 'api/quality-control/pass';
    $this->endpointRejectQC = 'api/quality-control/reject';
    $this->endpointTimeTrackingLogin = 'api/employee/time-checking';
    $this->params = [
        'label' => '021622-SJ-M-000505-4',
        'id_time_checking' => [$this->timeTrackingQC->id, $this->timeTrackingStage->id],
        'employee_id' => $this->employee->id,
    ];
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
});

// Pass QC failed missing id param
test('Pass QC failed - missing label ID param', function () {
    unset($this->params['label']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'label' => [
                'The label field is required.'
            ]
        ]
    ]);
});

// Pass QC failed - missing employee_id param
test('Pass QC failed - miss employee_id param', function () {
    unset($this->params['employee_id']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'The employee id field is required.'
            ]
        ]
    ]);
});

// Pass QC failed - employee_id not exist in warehouse
test('Pass QC failed - employee_id not exist in warehouse', function () {
    $this->params['employee_id'] = 200;

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'The selected employee id is invalid.'
            ]
        ]
    ]);
});

// Pass QC failed - label id param is invalid but still in correct format
test('Pass QC failed - Label id param not found', function () {
    $this->params['label'] = '021622-SJ-M-000509';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'label' => [
                'The selected label is invalid.'
            ]
        ]
    ]);
});

// Pass QC failed - ID time checking must be an array.
test('Pass quality control failed - ID time checking must be an array.', function () {
    $this->params['id_time_checking'] = 1;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'id_time_checking' => [
                'The id time checking must be an array.'
            ]
        ]
    ]);
});

// Pass QC failed - ID time checking is not existed.
test('Pass quality control failed - ID time checking is not existed.', function () {
    $this->params['id_time_checking'] = [1];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'id_time_checking.0' => [
                'The selected id_time_checking.0 is invalid.'
            ]
        ]
    ]);
});

// Pass QC failed - ID time checking is not existed.
test('Pass quality control failed - ID time checking array must contain multiple_staging and quality_control type.', function () {
    $this->params['id_time_checking'] = [1];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'id_time_checking.0' => [
                'The selected id_time_checking.0 is invalid.'
            ]
        ]
    ]);
});

// Pass QC failed, order is cancelled.
test('Pass quality control failed - order is cancelled.', function () {
    $this->cancelledSaleOrder = SaleOrder::factory([
        'id' => 999,
        'order_status' => 'cancelled',
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'order_id' => 999,
        'label_id' => $this->cancelledLabel,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();

    $this->params['label'] = $this->cancelledLabel;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toMatchArray([
        'message' => [
            'Sale order is cancelled'
        ]
    ]);
});

// QC pass failed - can not pass 2 times.
test('QC pass failed - can not pass 2 times.', function () {
    DB::table('sale_order_item_quality_control')->insert([
        'order_id' => $this->saleOrder->id,
        'order_item_id' => $this->saleOrder->items->first()->barcodes->first()->id,
        'status' => 'pass',
        'user_id' => $this->user->id,
        'label_id' => '021622-SJ-M-000505-4',
        'employee_id' => $this->employee->id,
        'part_number_id' => null,
    ]);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => [
            'Label ID 021622-SJ-M-000505-4 has been passed'
        ]
    ]);
});

// QC failed - Label ID is not found .
test('Reprint order QC passed - check valid label root ID.', function () {
    $this->params['label'] = 'NOT FOUND';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'label' => [
                'The selected label is invalid.'
            ]
        ]
    ]);
});

// Pass QC failed - Label ID is deleted same case scan old label when reprinting.
test('Reprint order QC passed - Label ID is deleted.', function () {
    $this->saleOrder = SaleOrder::factory([
        'id' => 2,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory(
        [
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '021622-SJ-M-000505-9',
            'order_id' => 2,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'is_deleted' => 1
        ],
    ), 'barcodes'), 'items')
        ->create();
    $this->params['label'] = '021622-SJ-M-000505-9';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'label' => [
                'The selected label is invalid.'
            ]
        ]
    ]);
});

// Pass QC successfully (pass -> reject -> pass again).
test('QC passed successfully - Do pass -> reject -> pass again.', function () {
    $this->paramsRejectQC = [
        'country' => null,
        'employee_id' => $this->employee->id,
        'hasReprint' => false,
        'id_time_checking' => [$this->timeTrackingQC->id],
        'label' => $this->params['label'],
        'status' => 'printing machine'
    ];

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = $this->post($this->endpointRejectQC, $this->paramsRejectQC);
    $response->assertStatus(200);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $this->assertDatabaseHas(
        'sale_order_item_quality_control',
        [
            'label_id' => $this->params['label'],
            'order_id' => $this->saleOrder->id,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'status' => 'pass',
            'created_at' => $dataResponse['data']['qc_at'],
            'updated_at' => $dataResponse['data']['qc_at'],
        ],
    );
});

// QC success - Many people QC 1 label with doing pass then reject.
test('Many people QC 1 label.', function () {
    $this->paramsRejectQCPerson2 = [
        'country' => null,
        'hasReprint' => false,
        'id_time_checking' => [$this->timeTrackingQCPerson2->id],
        'employee_id' => $this->employee2->id,
        'label' => $this->params['label'],
        'status' => 'printing machine'
    ];

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $responsePerson2 = $this->post($this->endpointRejectQC, $this->paramsRejectQCPerson2);
    $responsePerson2->assertStatus(200);

    $this->assertDatabaseHas('time_tracking', [
        'employee_id' => $this->employee->id,
        'quantity' => $this->timeTrackingQC->quantity + 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    $this->assertDatabaseHas('time_tracking', [
        'employee_id' => $this->employee2->id,
        'quantity' => $this->timeTrackingQCPerson2->quantity + 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);
});

// QC Success - All order label barcode are QCed - Order QC status is updated.
test('All order label barcode are QCed.', function () {
    $this->saleOrder = SaleOrder::factory([
        'id' => 999,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '021622-SJ-M-000505-9',
            'order_id' => 999,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '021622-SJ-M-000505-8',
            'order_id' => 999,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        ],
    ), 'barcodes'), 'items')
        ->create();
    // QC first label
    $this->params['label'] = '021622-SJ-M-000505-9';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_staged_status' => false,
        'order_qc_status' => false,
    ]);

    // QC second label
    $this->params['label'] = '021622-SJ-M-000505-8';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_staged_status' => true,
        'order_qc_status' => true,
    ]);
});

// QC pass successfully - Many employee login 1 machine, add time tracking quantity for all employees.
test('QC success - Many employee login 1 machine, add time tracking quantity for all employees.', function () {
    $this->paramsLoginPerson1 = [
        'code' => $this->employee->code,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson1);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId1 = $dataResponse['id_time_checking'];

    $this->paramsLoginPerson2 = [
        'code' => $this->employee2->code,
        'id_parent' => $dataResponse['id_time_checking'],
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson2);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId2 = $dataResponse['id_time_checking'];

    $this->params['id_time_checking'] = [
        $timeTrackingId1, $timeTrackingId2
    ];

    //Pass QC request
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId1,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId2,
        'employee_id' => $this->employee2->id,
        'parent_id' => $timeTrackingId1,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);
});

// QC passed successfully - Add 1 performance with 1 label when 1 person pass/reject QC many times.
test('QC passed - Add 1 performance with 1 label when 1 person pass/reject QC many times.', function () {
    $this->paramsLoginPerson1 = [
        'code' => $this->employee->code,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson1);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId1 = $dataResponse['id_time_checking'];

    $this->paramsLoginPerson2 = [
        'code' => $this->employee2->code,
        'id_parent' => $dataResponse['id_time_checking'],
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson2);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId2 = $dataResponse['id_time_checking'];

    $this->params['id_time_checking'] = [
        $timeTrackingId1
    ];

    // Person 1 pass QC
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId1,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    // Person 2 reject QC
    $this->paramsRejectQCPerson2 = [
        'country' => null,
        'hasReprint' => false,
        'id_time_checking' => [$timeTrackingId2],
        'employee_id' => $this->employee2->id,
        'label' => $this->params['label'],
        'status' => 'printing machine'
    ];
    $responsePerson2 = $this->post($this->endpointRejectQC, $this->paramsRejectQCPerson2);
    $responsePerson2->assertStatus(200);

    // Then person 1 reject QC, performance +1 only
    $this->paramsRejectQCPerson1 = [
        'country' => null,
        'hasReprint' => true,
        'id_time_checking' => [$timeTrackingId1],
        'employee_id' => $this->employee->id,
        'label' => $this->params['label'],
        'status' => 'peeling print'
    ];
    $response = $this->post($this->endpointRejectQC, $this->paramsRejectQCPerson1);
    $response->assertStatus(200);
    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId1,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);
});

// QC is passed.
test('Pass quality control successfully. All data updated.', function () {
    $this->newOrder = SaleOrder::factory([
        'id' => 999,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'order_id' => 999,
        'label_id' => $this->newOrderLabel,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();
    $this->params['label'] = $this->newOrderLabel;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    $this->assertDatabaseHas(
        'sale_order_item_quality_control',
        [
            'label_id' => $this->newOrderLabel,
            'order_id' => 999,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
            'label_root_id' => $this->newOrderLabel,
            'status' => 'pass'
        ],
    );

    $this->assertDatabaseHas('sale_order_item_barcode_status',
        [
            'label_id' => $this->newOrderLabel,
            'status' => SaleOrderItemQualityControl::STATUS_PASS_QC
        ]);
    $resKeys = [
        'is_count',
        'isCount',
        'status',
        'data',
        'total_item',
        'totalItem',
        'total_item_scan',
        'totalItemScan',
    ];
    expect($dataResponse)->toHaveKeys($resKeys);
    //Check all items passed
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->newOrder->id,
        'order_staged_status' => true,
        'order_qc_status' => true,
    ]);

    //Time tracking QC quantity must be +1
    $this->assertDatabaseHas('time_tracking', [
        'employee_id' => $this->employee->id,
        'quantity' => $this->timeTrackingQC->quantity + 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    //Time tracking Stage quantity must be +1
    $this->assertDatabaseHas('time_tracking', [
        'employee_id' => $this->employee->id,
        'quantity' => $this->timeTrackingStage->quantity + 1,
        'job_type' => TimeTracking::STAGE_JOB_TYPE
    ]);

    //Job is sent to queue
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_UPDATE_PRODUCTION_STATUS,
        'attempts' => 0,
    ]);

    //Sale Order history is created
    $this->assertDatabaseHas('sale_order_history', [
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'order_id' => $this->newOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
        'message' => 'Label ID ' . $this->params['label'] . ' approved by QC',
    ]);

    //If All item passed QC, update order status
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->newOrder->id,
        'order_staged_status' => true,
        'order_qc_status' => true,
    ]);
});
