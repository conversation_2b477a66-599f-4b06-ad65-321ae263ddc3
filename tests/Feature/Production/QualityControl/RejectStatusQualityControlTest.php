<?php

use App\Models\BarcodeStatus;
use App\Models\Country;
use App\Models\Employee;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->employee2 = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->product = Product::factory(['name' => '1234 / AKN / S'])->create();
    $this->endpointTimeTrackingLogin = 'api/employee/time-checking';
    $this->timeTracking = TimeTracking::factory()
    ->create([
        'employee_id' => $this->employee->id,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE,
        'quantity' => 0,
        'start_time' => Carbon::now()
    ]);
    $this->saleOrder = SaleOrder::factory([
        'id' => 1,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,

        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'order_id' => 1,
        'label_id' => '021622-SJ-M-000505-4',
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();

    $this->barcodeStatus = BarcodeStatus::factory([
        'label_id' => '021622-SJ-M-000505-4',
        'sent_reject_email' => 1,
        'sent_oos_email' => 0,
    ]);
    $this->qc = DB::table('sale_order_item_quality_control')->first();

    $this->country = Country::factory()->count(2)->sequence(
        ['iso2' => 'US'],
        ['iso2' => 'AX'],
    )
    ->create();

    $this->partNumber = PartNumber::factory()->count(2)->sequence(
        [
            'part_number' => 'part_number_1',
            'country' => $this->country->first()->iso2,
            'product_id' => 20,
        ],
        [
            'part_number' => 'part_number_2',
            'country' => $this->country->last()->iso2,
            'product_id' => 20,
        ],
    )->create();

    $this->partNumberFifo = PartNumberFifo::factory()->count(2)->sequence(
        [
            'quantity' => 0,
            'product_id' => $this->saleOrder->items->first()->product_id,
            'date_input' => '2023-03-01 00:00:00',
            'part_number_id' => $this->partNumber->first()->id,
        ],
        [
            'quantity' => 5,
            'product_id' => $this->saleOrder->items->first()->product_id,
            'date_input' => '2023-03-01 00:00:00',
            'part_number_id' => $this->partNumber->last()->id,
        ],
    )
        ->create();
    Storage::fake('s3');
    $this->endpoint = 'api/quality-control/reject';
    $this->params = [
        'label' => '021622-SJ-M-000505-4',
        'status' => 'defective item',
        'hasReprint' => false,
        'employee_id' => $this->employee->id,
        'id_time_checking' => [$this->timeTracking->id],
        // 'image' => UploadedFile::fake()->image('image.jpg')->size(100)

    ];
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
});

// miss id param
test('update quality control failed - miss id param', function () {
    unset($this->params['label']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'label' => [
            'The label field is required.'
        ]
    ]);
});

//miss status param
test('update quality control failed - miss status param', function () {
    unset($this->params['status']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'status' => [
            'The status field is required.'
        ]
    ]);
});

//miss hasReprint param
test('update quality control failed - miss hasReprint param', function () {
    unset($this->params['hasReprint']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'has_reprint' => [
            'The has reprint field is required.'
        ]
    ]);
});

//miss employee_id param
test('update quality control failed - missing employee_id param', function () {
    unset($this->params['employee_id']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'employee_id' => [
            'The employee id field is required.'
        ]
    ]);
});

//employee_id not exist in warehouse
test('update quality control failed - employee_id not exist in warehouse', function () {
    $this->params['employee_id'] = 200;

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'employee_id' => [
            'The selected employee id is invalid.'
        ]
    ]);
});

//id param not found
test('update quality control failed - id param not found', function () {
    $this->params['label'] = '021622-SJ-M-000505-42342342';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'label' => [
            'The selected label is invalid.'
        ]
    ]);
});

//data true
test('Reject quality control success without reprint.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $resKLeys = [
        'is_count',
        'isCount',
        'message',
    ];
    expect($dataResponse)->toHaveKeys($resKLeys);
    $this->assertDatabaseMissing('claim', [
        'label_id' => $this->params['label'],
        'type' => 'QC',
        'reason' => $this->params['status'],
        'employee_id' => $this->params['employee_id'],
    ]);

    //QC history saved
    $this->assertDatabaseHas(
        'sale_order_item_quality_control',
        [
            'order_id' => $this->saleOrder->id,
            'user_id' => $this->user->id,
            'status' => $this->params['status'],
            'label_id' => $this->params['label'],
            'employee_id' => $this->employee->id,
            'warehouse_id' => $this->warehouse->id,
            'label_root_id' => $this->params['label'],
            'part_number_id' => null,
        ],
    );

    //Sale Order history is created
    $this->assertDatabaseHas('sale_order_history', [
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
        'message' => 'Label ID ' . $this->params['label'] . ' rejected by QC.',
    ]);

    //Time tracking quantity must be +1
    $this->assertDatabaseHas('time_tracking', [
        'id' => $this->timeTracking->id,
        'quantity' => $this->timeTracking->quantity + 1,
        'employee_id' => $this->employee->id
    ]);

    //Job is sent to queue
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_UPDATE_PRODUCTION_STATUS,
        'attempts' => 0,
    ]);

    //Send out of stock notification to service background
    //because product is out of stock and this barcode is not notified.
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_SEND_MAIL_ALERT_OOS,
        'attempts' => 0,
    ]);
});

test('Reject quality control success with reprint.', function () {
    $this->params['hasReprint'] = true;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $resKLeys = [
        'is_count',
        'isCount',
        'message',
    ];
    expect($dataResponse)->toHaveKeys($resKLeys);
    $this->assertDatabaseHas('claim', [
        'label_id' => $this->params['label'],
        'type' => 'QC',
        'reason' => $this->params['status'],
        'employee_id' => $this->params['employee_id'],
    ]);

    $this->assertDatabaseHas(
        'sale_order_item_quality_control',
        [
            'order_id' => $this->saleOrder->id,
            'user_id' => $this->user->id,
            'status' => $this->params['status'],
            'label_id' => $this->params['label'],
            'employee_id' => $this->employee->id,
            'warehouse_id' => $this->warehouse->id,
            'label_root_id' => $this->params['label'],
            'part_number_id' => null,
        ],
    );

    //Sale Order history is created
    $this->assertDatabaseHas('sale_order_history', [
        'user_id' => $this->user->id,
        'employee_id' => $this->employee->id,
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_QUALITY_TYPE,
        'message' => 'Label ID ' . $this->params['label'] . ' rejected by QC, request reprint.',
    ]);

    //Time tracking quantity must be +1
    $this->assertDatabaseHas('time_tracking', [
        'id' => $this->timeTracking->id,
        'quantity' => $this->timeTracking->quantity + 1,
        'employee_id' => $this->employee->id
    ]);

    //Job is sent to queue
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_UPDATE_PRODUCTION_STATUS,
        'attempts' => 0,
    ]);

    //Send out of stock notification to service background
    //because product is out of stock and this barcode is not notified.
    $this->assertDatabaseHas('jobs', [
        'queue' => SaleOrder::JOB_SEND_MAIL_ALERT_OOS,
        'attempts' => 0,
    ]);
});

//warehouse mexico - Country not
test('Reject quality control failed - warehouse mexico - not found country', function () {
    $mexicoWarehouse = Warehouse::WAREHOUSE_MEXICO_ID;
    $this->saleOrder = SaleOrder::factory([
        'id' => 999,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $mexicoWarehouse,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,

        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $mexicoWarehouse,
        'order_id' => 999,
        'label_id' => '021622-SJ-M-000505-4-MX',
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();

    $token = JWTAuth::customClaims(['warehouse' => ['id' => 18]])->fromUser($this->user);
    Employee::query()->where('id', $this->employee->id)->update(['warehouse_id' => 18]);
    SaleOrderItem::where('id', $this->saleOrder->items->first()->id)->update(['warehouse_id' => 18]);
    PartNumberFifo::query()->update(['warehouse_id' => 18]);

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ]);
    $this->params['label'] = '021622-SJ-M-000505-4-MX';
    $response = $this->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(422);
    expect($dataResponse)->toMatchArray([
        'message' => [
            'Country not found !'
        ]
    ]);
});

//warehouse mexico - not found part number
test('Reject quality control failed- warehouse mexico - not found part number', function () {
    $mexicoWarehouse = Warehouse::WAREHOUSE_MEXICO_ID;
    $this->saleOrder = SaleOrder::factory([
        'id' => 999,
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $mexicoWarehouse,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,

        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $mexicoWarehouse,
        'order_id' => 999,
        'label_id' => '021622-SJ-M-000505-4-MX',
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
    ]), 'barcodes'), 'items')
        ->create();

    $token = JWTAuth::customClaims(['warehouse' => ['id' => 18]])->fromUser($this->user);
    Employee::query()->where('id', $this->employee->id)->update(['warehouse_id' => 18]);
    SaleOrderItem::where('id', $this->saleOrder->items->first()->id)->update(['warehouse_id' => 18]);
    PartNumberFifo::query()->update(['warehouse_id' => 18]);

    $this->params['country'] = $this->partNumber->last()->country;

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ]);
    $this->params['label'] = '021622-SJ-M-000505-4-MX';
    $response = $this->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(422);
    expect($dataResponse)->toMatchArray([
        'message' => [
            'Part number not found !'
        ]
    ]);
});

// QC pass successfully - Many employee login 1 machine, add time tracking quantity for all employees.
test('QC reject success - Many employee login 1 machine, add time tracking quantity for all employees.', function () {
    $this->paramsLoginPerson1 = [
        'code' => $this->employee->code,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson1);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId1 = $dataResponse['id_time_checking'];

    $this->paramsLoginPerson2 = [
        'code' => $this->employee2->code,
        'id_parent' => $dataResponse['id_time_checking'],
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson2);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId2 = $dataResponse['id_time_checking'];

    $this->params['id_time_checking'] = [
        $timeTrackingId1, $timeTrackingId2
    ];

    //Reject QC request
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId1,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId2,
        'employee_id' => $this->employee2->id,
        'parent_id' => $timeTrackingId1,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);
});

// QC passed successfully - Add 1 performance with 1 label when 1 person reject QC many times.
test('QC reject success - Add 1 performance with 1 label when 1 person reject QC many times.', function () {
    $this->paramsLoginPerson = [
        'code' => $this->employee->code,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ];
    $response = $this->post($this->endpointTimeTrackingLogin, $this->paramsLoginPerson);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $timeTrackingId = $dataResponse['id_time_checking'];
    $this->params['id_time_checking'] = [$timeTrackingId];
    $this->params['status'] = 'printing machine';

    // Person 1 reject QC the first time
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);

    // Person 1 reject QC the second time, but with other reason.
    $this->params['status'] = 'peeling print';

    $responsePerson = $this->post($this->endpoint, $this->params);
    $responsePerson->assertStatus(200);

    // Then performance is still +1 only
    $this->assertDatabaseHas('time_tracking', [
        'id' => $timeTrackingId,
        'employee_id' => $this->employee->id,
        'quantity' => 1,
        'job_type' => TimeTracking::QUALITY_CONTROL_JOB_TYPE
    ]);
});
