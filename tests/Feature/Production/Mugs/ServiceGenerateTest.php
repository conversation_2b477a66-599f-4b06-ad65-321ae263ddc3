<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\BarcodePrinted;
use Illuminate\Support\Carbon;
use App\Http\Service\ConvertService;
use App\Models\SaleOrderItemImage;
use App\Models\Product;
use App\Models\PrintingPresetSku;
use App\Models\ImageHash;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->convertService = new ConvertService();

    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    Storage::fake('s3');
});

// Không có item barcode nào có barcode_printed_id = id của barcode printed vừa được tạo
// result = echo "bug error item == 0"
// update barcode_printed: convert_status = 2
//test('not found barcode for generate template', function () {
//    $barcodeFail = BarcodePrinted::factory()->create();
//
//    $output = $this->convertService->convertMugsToPdf($barcodeFail);
//
//    expect($output)->toBeFalsy();
//    $this->expectOutputString("bug error item == 0\n");
//    $this->assertEquals(BarcodePrinted::find($barcodeFail->id)->convert_status, BarcodePrinted::FAILED);
//});
//
//// Không có product sku hoặc chưa setup printing preset
//// result = echo "bug error product not found"
//// update barcode_printed: convert_status = 2
//test('not found product', function () {
//    $product = Product::factory([
//            'sku' => 'MUGS1B15O'
//        ])
//        ->create();
//
//    $saleOrder = SaleOrder::factory([
//            'order_status' => 'new_order'
//        ])->has(SaleOrderItem::factory([
//                'product_id' => $product->id
//            ])
//            ->has(SaleOrderItemBarcode::factory([
//                'warehouse_id' => $this->warehouse->id,
//                'label_id' => 'xxxxxx'
//            ]), 'barcodes')
//            ->has(SaleOrderItemImage::factory([
//                'image_url' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/test/purple/2.png',
//                'image_width' => 2640,
//                'image_height' => 1080
//            ]), 'images')
//        , 'items')
//        ->create();
//
//    $barcodePrinted = BarcodePrinted::factory([
//            'created_at' => Carbon::now(),
//            'warehouse_id' => $this->warehouse->id,
//            'product_id' => $product->id,
//            'print_method' => 'MUGS'
//        ])
//        ->create();
//
//    $saleOrder->items->map(function ($item) use ($saleOrder, $barcodePrinted) {
//        return $item->barcodes->map(function ($barcode) use ($saleOrder, $barcodePrinted) {
//            $barcode->order_id = $saleOrder->id;
//            $barcode->barcode_printed_id = $barcodePrinted->id;
//            $barcode->save();
//        });
//    });
//
//    $output = $this->convertService->convertMugsToPdf($barcodePrinted);
//
//    expect($output)->toBeFalsy();
//    $this->expectOutputString("bug error product not found\n");
//    $this->assertEquals(BarcodePrinted::find($barcodePrinted->id)->convert_status, BarcodePrinted::FAILED);
//    $this->assertEquals(SaleOrderItemBarcode::where('barcode_printed_id', 0)->where('retry_convert', 1)->count(), 1);
//});

// Barcode có 2 ảnh hoặc 1 ảnh có width < height
// result echo "item has two images order_item_id"
test('barcode have 2 images', function () {
    $product = Product::factory([
            'sku' => 'MUGS1B16O'
        ])
        ->has(PrintingPresetSku::factory(), 'printingPresetSku')
        ->create();

    $saleOrder = SaleOrder::factory([
            'order_status' => 'new_order'
        ])->has(SaleOrderItem::factory([
            'product_id' => $product->id
        ])
            ->has(SaleOrderItemBarcode::factory([
                'warehouse_id' => $this->warehouse->id,
                'label_id' => 'zzzz'
            ]), 'barcodes')
            ->has(SaleOrderItemImage::factory([
                'image_url' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/test/purple/3.png',
                'image_width' => 4200,
                'image_height' => 4800
            ]), 'images')
        , 'items')
        ->create();

    $barcodePrinted = BarcodePrinted::factory([
            'created_at' => Carbon::now(),
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $product->id,
            'print_method' => 'MUGS'
        ])
        ->create();

    $saleOrder->items->map(function ($item) use ($saleOrder, $barcodePrinted) {
        $item->barcodes->map(function ($barcode) use ($saleOrder, $barcodePrinted) {
            $barcode->order_id = $saleOrder->id;
            $barcode->barcode_printed_id = $barcodePrinted->id;
            $barcode->save();
        });
        $item->images->map(function ($image) use ($saleOrder) {
            $image->order_id = $saleOrder->id;
            $image->save();
        });
    });

    $output = $this->convertService->convertMugsToPdf($barcodePrinted);

    expect($output)->toBeFalsy();
    $this->expectOutputString("item has two images order_item_id: " . $saleOrder->items->first()->id . "\n" . "error: outputs empty \n");
})->skip();

// generate success
// result: echo "done ==> pdf_converted_id = "
// put file to s3
// update bảng barcode_printed: convert_percent = số lượng barcode convert thành công, convert_status = 1, converted_at = now, quantity = số lượng barcode thực tế lấy ra khi convert
// update sale_order_item_barcode convert thành công: barcode_printed_id, convert_pdf_status = 1, employee_pull_id, print_barcode_at = now, sale_order.order_status = in_production
// update sale_order_item_barcode convert lỗi: barcode_printed_id = 0, convert_pdf_status = 0, retry_convert ++
test('success', function () {
    $product = Product::factory([
            'sku' => 'MUGS1B11O'
        ])
        ->has(PrintingPresetSku::factory(), 'printingPresetSku')
        ->create();

    $imageHash = ImageHash::factory([
            'hash_md5' => md5(file_get_contents('https://swiftpoddev.s3.us-west-1.amazonaws.com/test/purple/4.png'))
        ])
        ->create();

    $saleOrder = SaleOrder::factory([
            'order_status' => 'new_order'
        ])->has(SaleOrderItem::factory([
                'product_id' => $product->id
            ])
                ->has(SaleOrderItemBarcode::factory([
                    'warehouse_id' => $this->warehouse->id,
                    'label_id' => 'adsdasds'
                ]), 'barcodes')
                ->has(SaleOrderItemImage::factory([
                    'image_url' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/test/purple/4.png',
                    'image_width' => 2640,
                    'image_height' => 1080,
                    'image_hash_id' => $imageHash->id
                ]), 'images')
            , 'items')
        ->create();

    $barcodePrinted = BarcodePrinted::factory([
            'created_at' => Carbon::now(),
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $product->id,
            'print_method' => 'MUGS',
        ])
        ->create();

    $saleOrder->items->map(function ($item) use ($saleOrder, $barcodePrinted) {
        return $item->barcodes->map(function ($barcode) use ($saleOrder, $barcodePrinted) {
            $barcode->order_id = $saleOrder->id;
            $barcode->barcode_printed_id = $barcodePrinted->id;
            $barcode->save();
        });
    });

    $this->convertService->convertMugsToPdf($barcodePrinted);

    Storage::disk('s3')->assertExists("/mugs/" . $barcodePrinted->id . ".pdf");
    $this->expectOutputString("done ==> pdf_converted_id = " . $barcodePrinted->id . "\n");
    $this->assertEquals(BarcodePrinted::find($barcodePrinted->id)->convert_status, BarcodePrinted::ACTIVE);
    $this->assertEquals(SaleOrder::find($saleOrder->id)->order_status, SaleOrder::STATUS_IN_PRODUCTION);
    $this->assertEquals(SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)->first()->convert_pdf_status, SaleOrderItemBarcode::CONVERT_PDF_SUCCESS);
})->skip();
