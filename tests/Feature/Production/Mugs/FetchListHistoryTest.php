<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Employee;
use App\Models\BarcodePrinted;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/mug/history-pdf?limit=11';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
});

// Success
// result = [print_status = 1, print_method = MUGS]
// 3 records match
test('success', function () {
    BarcodePrinted::factory()->count(7)->sequence(
        [
            // warehouse id is not match
            'warehouse_id' => 100000,
            'print_status' => 1,
            'print_method' => 'MUGS',
            'employee_id' => $this->employee->id,
        ],
        [
            // warehouse id is match
            'warehouse_id' => $this->warehouse->id,
            'print_status' => 1,
            'print_method' => 'MUGS',
            'employee_id' => $this->employee->id,
        ],
        [
            // print status is not match
            'print_status' => 0,
            'warehouse_id' => $this->warehouse->id,
            'print_method' => 'MUGS',
            'employee_id' => $this->employee->id,
        ],
        [
            // print status is match
            'print_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'print_method' => 'MUGS',
            'employee_id' => $this->employee->id,
        ],
        [
            // print method is not match
            'print_method' => 'UV',
            'print_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
        ],
        [
            // print status is not match
            'print_method' => 'DTG',
            'print_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
        ],
        [
            // print method is match
            'print_method' => 'MUGS',
            'print_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
        ]
    )
    ->has(SaleOrderItemBarcode::factory(), 'barcodes')
    ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys([
        'code',
        'success',
        'message',
        'data',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total'
    ]);

    expect($jsonRes['data']['data'])->toBeArray();

    expect($jsonRes['data']['data'][0])->toHaveKeys([
        'id',
        'store_id',
        'employee_id',
        'warehouse_id',
        'user_id',
        'is_xqc',
        'is_reprint',
        'style_sku',
        'quantity',
        'quantity_input',
        'created_at',
        'convert_status',
        'print_status',
        'converted_at',
        'account_id',
        'convert_percent',
        'first_sku',
        'last_sku',
        'is_manual',
        'print_method',
        'product_id',
        'employee_convert',
        'product'
    ]);

    expect($jsonRes['data']['total'])->toEqual(3);
});

// 1 record match
test('search label', function () {
    $labelIds = [
        'xxx',
        'yyy'
    ];

    foreach ($labelIds as $labelId) {
        BarcodePrinted::factory([
            'print_method' => 'MUGS',
            'print_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'employee_id' => $this->employee->id,
        ])
            ->has(SaleOrderItemBarcode::factory([
                'label_id' => $labelId
            ]), 'barcodes')
            ->create();
    }

    $response = $this->get($this->endpoint . '&label_id=xxx');

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys([
        'code',
        'success',
        'message',
        'data',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total'
    ]);

    expect($jsonRes['data']['data'])->toBeArray();

    expect($jsonRes['data']['data'][0])->toHaveKeys([
        'id',
        'store_id',
        'employee_id',
        'warehouse_id',
        'user_id',
        'is_xqc',
        'is_reprint',
        'style_sku',
        'quantity',
        'quantity_input',
        'created_at',
        'convert_status',
        'print_status',
        'converted_at',
        'account_id',
        'convert_percent',
        'first_sku',
        'last_sku',
        'is_manual',
        'print_method',
        'product_id',
        'employee_convert',
        'product'
    ]);

    expect($jsonRes['data']['total'])->toEqual(1);
});
