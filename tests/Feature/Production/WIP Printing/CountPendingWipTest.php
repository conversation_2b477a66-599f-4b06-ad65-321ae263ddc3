<?php

use App\Models\BarcodePrintedTime;
use App\Models\Product;
use App\Models\ProductRoulette;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\BarcodeRepository;
use App\Repositories\StoreRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    $this->setting = Setting::factory([
        'label' => Setting::TEST_WIP_SKU,
        'name' => Setting::TEST_WIP_SKU,
        'value' => 'UNGT,UNNT'
    ])->create();
    $this->warehouse = Warehouse::factory()->create();

    $this->productStyle = ProductStyle::factory([
        'print_method' => 'DTG',
        'name' => '3001',
        'sku' => 'UNPT',
        'type' => 'Fleece',
    ])
        ->has(Product::factory([
            'parent_id' => 111,
            'sku' => 'UNPT9C00L'
        ]), 'product')
        ->create();
    $this->product = Product::factory([
        'id' => 1111,
        'sku' => 'UNPT9C00S',
        'name' => 'UNPT9C00S',
    ])->create();

    $this->saleOrderAccount = SaleOrderAccount::factory([
        'id' => 2000,
        'name' => 'shipstation',
    ])->create();
    $this->store = Store::factory([
        'account_id' => $this->saleOrderAccount->id,
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'id' => 2000000,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
        'is_manual' => SaleOrder::IS_NOT_MANUAL,
        'is_xqc' => SaleOrder::INACTIVE,
        'is_eps' => SaleOrder::INACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => *********,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_DTG,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
    ]), 'barcodes'), 'items')
        ->create();
    $this->productRoulette = ProductRoulette::factory()->create(
        [
            'product_id' => $this->product->id,
            'is_active' => true,
            'created_by' => 1,
        ]);

    $this->topSKUOrder = SaleOrder::factory([
        'id' => ********,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
        'is_manual' => SaleOrder::IS_NOT_MANUAL,
        'is_xqc' => SaleOrder::INACTIVE,
        'is_eps' => SaleOrder::INACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->product->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => *********,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_DTG,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
    ]), 'barcodes'), 'items')
        ->create();

    $this->topSKUOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->topSKUOrder->id;
            $barcode->save();
        });
    });

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->barcodePrintedTime = BarcodePrintedTime::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'account_id' => $this->saleOrderAccount->id,
        'is_fba' => true,
    ])->create();

    $this->priorityStore = Store::factory()->create();
    Setting::factory()->create([
        'label' => Setting::PRIORITY_STORE,
        'name' => Setting::PRIORITY_STORE,
        'value' => $this->priorityStore->id
    ]);
    $this->orderOfPriorityStore = SaleOrder::factory([
        'id' => ********,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'store_id' => $this->priorityStore->id,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => *********1,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_DTG,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
        'print_barcode_at' => null,
        'store_id' => $this->priorityStore->id,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderOfPriorityStore->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderOfPriorityStore->id;
            $barcode->save();
        });
    });

    $this->barcodeRepo = new BarcodeRepository();
    $this->storeRepo = new StoreRepository();
    $this->params = [
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'account_id' => $this->saleOrderAccount->id,
        'priorityStores' => $this->storeRepo->getPriorityStores(),
    ];
});

//account error
test('get count pending WIP success - account error', function () {
    $this->params['account_id'] = 2000;
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//store error
test('get count pending WIP success - store error', function () {
    $this->params['store_id'] = 2000;
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//warehouse error
test('get count pending WIP success - warehouse error', function () {
    $this->params['warehouse_id'] = 2000;
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//label_root_id != null
test('get count pending WIP success - label_root_id != null', function () {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['label_root_id' => 20]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//reprint_status != 0
test('get count pending WIP success - reprint_status != 0', function () {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['reprint_status' => SaleOrderItemBarcode::REPRINTED]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//employee_reroute_id != Null
test('get count pending WIP success - employee_reroute_id != null', function () {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['employee_reroute_id' => 20]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//print_method != DTG
test('get count pending WIP success - print_method != DTG', function () {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['print_method' => SaleOrderItemBarcode::METHOD_DTF]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//barcode_printed_id != 0
test('get count pending WIP success - barcode_printed_id != 0', function () {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['barcode_printed_id' => 10]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//ink_color_status not active
test('get count pending WIP success - ink_color_status not active', function () {
    SaleOrderItem::query()->where('sku', '63QCYN')->update(['ink_color_status' => SaleOrder::INACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order eps
test('get count pending WIP success - order eps', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_eps' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order xqc
test('get count pending WIP success - order xqc', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_xqc' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order xqc and eps
test('get count pending WIP success - order xqc and eps', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_xqc' => SaleOrder::ACTIVE,
        'is_eps' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order manual
test('get count pending WIP success - order manual', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::ACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order not fba
test('get count pending WIP success - order not fba', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

//order inactive status
test('get count pending WIP success - order inactive status', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_ON_HOLD]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

test('get count pending WIP success - order insert and not fba', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
    ProductStyle::where('id', $this->productStyle->id)->update(['type' => ProductStyle::TYPE_INSERT]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 1);
});

test('get count pending WIP success - order insert, reprint, manual process, reroute, fba, xqc.', function () {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::IS_MANUAL, 'is_xqc' => 1]);
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['reprint_status' => SaleOrderItemBarcode::REPRINTED, 'employee_reroute_id' => 20]);
    ProductStyle::where('id', $this->productStyle->id)->update(['type' => ProductStyle::TYPE_INSERT]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

// default
test('get count pending WIP success', function () {
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});

// priority store
test('get count pending WIP success - count priority WIP', function () {
    $this->params['store_id'] = $this->priorityStore->id;
    $this->assertEquals(($this->barcodeRepo->countPendingStyleAll($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStore($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingAccount($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingOrderInsert($this->params))->total, 0);
});
