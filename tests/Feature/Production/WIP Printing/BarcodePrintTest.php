<?php

use App\Jobs\MultipleStatusOrderJob;
use App\Models\BarcodePrinted;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\ProductStyle;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\BarcodePrintedTime;
use App\Models\Employee;
use App\Models\Job;
use App\Models\ProductType;
use App\Models\SaleOrderAccount;
use App\Models\Setting;
use App\Models\Store;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/barcode/print';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id]);

    $this->productStyle = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_DTG,
        'name' => '3001',
        'sku' => 'UNPT',
        'type' => 'Tee'
    ])
        ->has(Product::factory([
            'parent_id' => 111,
            'sku' => 'UNPT9C00L'
        ]), 'product')
        ->create();
    $this->productStyleInsert = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_DTG,
        'name' => '3002',
        'sku' => 'INPS'
    ])
        ->has(Product::factory([
            'parent_id' => 112,
            'sku' => 'INPS9C00L'
        ]), 'product')
        ->create();
    $this->saleOrderAccount = SaleOrderAccount::factory([
        'name' => 'shipstation'
    ])->create();

    $this->store = Store::factory([
        'account_id' => $this->saleOrderAccount->id,
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'id' => 2000000,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => *********,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_DTG,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
        'print_barcode_at' => null,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->barcodePrintedTime = BarcodePrintedTime::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'account_id' => $this->saleOrderAccount->id,
        'is_fba' => true,
    ])->create();
    $this->priorityStore = Store::factory()->create();

    Setting::factory()->create([
        'label' => Setting::PRIORITY_STORE,
        'name' => Setting::PRIORITY_STORE,
        'value' => $this->priorityStore->id
    ]);
    $this->orderOfPriorityStore = SaleOrder::factory([
        'id' => ********,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::INACTIVE,
        'is_eps' => SaleOrder::INACTIVE,
        'is_manual' => SaleOrder::ACTIVE,
        'is_xqc' => SaleOrder::INACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'store_id' => $this->priorityStore->id,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => **********,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_DTG,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
        'print_barcode_at' => null,
        'store_id' => $this->priorityStore->id,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderOfPriorityStore->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderOfPriorityStore->id;
            $barcode->save();
        });
    });
    $this->params = [
        'account_id' => null,
        'all' => 0,
        'employee_id' => $this->employee->id,
        'employee_number' => $this->employee->code,
        'is_fba' => true,
        'is_manual' => null,
        'is_reprint' => null,
        'is_reroute' => null,
        'is_xqc' => null,
        'is_eps' => null,
        'limit' => 1,
        'store_id' => null,
        'style_sku' => null,
        'is_insert' => null,
    ];
    Queue::fake();
});



function barcodePrintSuccess($self) {
    $barcodePrinted = BarcodePrinted::first();
    $self->assertNotEmpty($barcodePrinted);
    $self->assertEquals($barcodePrinted->quantity, 1);

    $saleOrderItemBarcode = SaleOrderItemBarcode::where('employee_pull_id', $self->employee->id)
        ->where('barcode_printed_id', $barcodePrinted->id)
        ->whereNotNull('print_barcode_at')
        ->first();
    $self->assertNotEmpty($saleOrderItemBarcode);

    $saleOrder = SaleOrder::where('id', $self->saleOrder->id)
        ->where('order_status', SaleOrder::IN_PRODUCTION)
        ->whereNotNull('order_production_at')
        ->first();
    $self->assertNotEmpty($saleOrder);

    $barcodePrintedTime = BarcodePrintedTime::where('store_id', $self->params['store_id'])
        ->where('account_id', $self->params['account_id'])
        ->where('is_xqc', $self->params['is_xqc'])
        ->where('is_eps', $self->params['is_eps'])
        ->where('is_reprint', $self->params['is_reprint'])
        ->where('style_sku', $self->params['style_sku'])
        ->where('is_manual', $self->params['is_manual'])
        ->where('is_reroute', $self->params['is_reroute'])
        ->where('is_fba', $self->params['is_fba'])
        ->where('warehouse_id', $self->warehouse->id)
        ->whereNotNull('printed_at')
        ->first();
    $self->assertNotEmpty($barcodePrintedTime);

    Queue::assertPushed(MultipleStatusOrderJob::class, function ($job) {
        return $job;
    });
}


function barcodePrintPriorityOrderSuccess($self) {
    $barcodePrinted = BarcodePrinted::first();
    $self->assertNotEmpty($barcodePrinted);
    $self->assertEquals($barcodePrinted->quantity, 1);

    $saleOrderItemBarcode = SaleOrderItemBarcode::where('employee_pull_id', $self->employee->id)
        ->where('barcode_printed_id', $barcodePrinted->id)
        ->whereNotNull('print_barcode_at')
        ->where('store_id', $self->priorityStore->id)
        ->first();
    $self->assertNotEmpty($saleOrderItemBarcode);

    $saleOrder = SaleOrder::where('id', $self->orderOfPriorityStore->id)
        ->where('order_status', SaleOrder::IN_PRODUCTION)
        ->where('store_id', $self->priorityStore->id)
        ->whereNotNull('order_production_at')
        ->first();
    $self->assertNotEmpty($saleOrder);

    $barcodePrintedTime = BarcodePrintedTime::where('store_id', $self->params['store_id'])
        ->where('account_id', $self->params['account_id'])
        ->where('is_xqc', $self->params['is_xqc'])
        ->where('is_eps', $self->params['is_eps'])
        ->where('is_reprint', $self->params['is_reprint'])
        ->where('style_sku', $self->params['style_sku'])
        ->where('is_manual', $self->params['is_manual'])
        ->where('is_reroute', $self->params['is_reroute'])
        ->where('is_fba', $self->params['is_fba'])
        ->where('warehouse_id', $self->warehouse->id)
        ->whereNotNull('printed_at')
        ->first();
    $self->assertNotEmpty($barcodePrintedTime);

    Queue::assertPushed(MultipleStatusOrderJob::class, function ($job) {
        return $job;
    });
}

function barcodePrintFail($self) {
    $barcodePrinted = BarcodePrinted::where('quantity', 0)->first();
    $self->assertNotEmpty($barcodePrinted);

    $saleOrderItemBarcode = SaleOrderItemBarcode::where('employee_pull_id', $self->employee->id)
        ->where('barcode_printed_id', $barcodePrinted->id)
        ->whereNotNull('print_barcode_at')
        ->first();
    $self->assertEmpty($saleOrderItemBarcode);

    $saleOrder = SaleOrder::where('id', $self->saleOrder->id)
        ->where('order_status', SaleOrder::IN_PRODUCTION)
        ->whereNotNull('order_production_at')
        ->first();
    $self->assertEmpty($saleOrder);

    $barcodePrintedTime = BarcodePrintedTime::where('store_id', $self->params['store_id'])
        ->where('account_id', $self->params['account_id'])
        ->where('is_xqc', $self->params['is_xqc'])
        ->where('is_eps', $self->params['is_eps'])
        ->where('is_reprint', $self->params['is_reprint'])
        ->where('style_sku', $self->params['style_sku'])
        ->where('is_manual', $self->params['is_manual'])
        ->where('is_reroute', $self->params['is_reroute'])
        ->where('is_fba', $self->params['is_fba'])
        ->where('warehouse_id', $self->warehouse->id)
        ->whereNotNull('printed_at')
        ->first();
    $self->assertNotEmpty($barcodePrintedTime);
    Queue::assertNotPushed(MultipleStatusOrderJob::class, function ($job) {
        return $job;
    });
}

//warehouse error
//test('barcode print success - warehouse error', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['warehouse_id' => 2000]);
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////order inactive
//test('barcode print success - order inactive', function() {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_CANCELLED]);
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////  order print method not DTG
//test('barcode print success - order print method != DTG', function () {
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['print_method' => ProductStyle::METHOD_DTF]);
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////  order ink_color_status = 0
//test('barcode print success - order ink_color_status = 0', function () {
//    SaleOrderItem::where('sku', '63QCYN')->update(['ink_color_status' => 0]);
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////  sale order item barcode đã xóa
//test('barcode print success - sale order item barcode deleted', function () {
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['is_deleted' => true]);
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
//// đơn order nhưng đã reroute
//test('barcode print success - order manual but reroute done', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::IS_MANUAL, 'is_fba_order' => SaleOrder::INACTIVE]);
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['employee_reroute_id' => 20]);
//    $this->params['is_manual'] = SaleOrder::IS_MANUAL;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////success order manual
//test('barcode print success - order manual', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::IS_MANUAL, 'is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_manual'] = SaleOrder::IS_MANUAL;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
////param reroute nhưng không có order reroute
//test('barcode print success - param reroute but order reroute not found', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_reroute'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////success order reroute
//test('barcode print success - order reroute', function () {
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['employee_reroute_id' => 20]);
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_reroute'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
////param fba nhưng order fba not found
//test('barcode print success - order fba not found', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
////success order fba
//test('barcode print success - order fba', function () {
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// order reprint nhưng không có label root id
//test('barcode print success - order reprint but label id null', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_reprint'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
//// order reprint nhưng đã reroute
//test('barcode print success - order reprint but reroute done', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['employee_reroute_id' => 20]);
//
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_reprint'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintFail($this);
//});
//
//// success order reprint
//test('barcode print success - order reprint', function () {
//    SaleOrderItemBarcode::where('label_id', 'xxxxx')->update(['label_root_id' => 20]);
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_reprint'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order xqc
//test('barcode print success - order xqc', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'is_xqc' => SaleOrder::ACTIVE, 'is_eps' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_xqc'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order eps
//test('barcode print success - order eps', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'is_eps' => SaleOrder::ACTIVE, 'is_xqc' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_eps'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order eps and xqc
//test('barcode print success - order xqc and eps', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'is_eps' => SaleOrder::ACTIVE, 'is_xqc' => SaleOrder::ACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['is_xqc'] = SaleOrder::ACTIVE;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order not reprint & store id != 0
//test('barcode print success - order not reprint & isset store id', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'store_id' => 100, 'is_xqc' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['store_id'] = 100;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order not reprint & account id != 0
//test('barcode print success - order not reprint & isset account id', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'account_id' => 100, 'is_xqc' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['account_id'] = 100;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order not reprint & isset style sku
//test('barcode print success - order not reprint & isset style sku', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE, 'is_xqc' => SaleOrder::INACTIVE]);
//    SaleOrderItem::where('sku', '63QCYN')->update(['product_style_sku' => 'UNPT']);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['style_sku'] = 'UNPT';
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintSuccess($this);
//});
//
//// success order not reprint and is insert
//test('barcode print success - order not reprint & is insert', function () {
//    SaleOrderItem::query()->update(['product_style_sku' => 'INPS', 'product_id' => $this->productStyleInsert->product->first()->id, 'product_sku' => $this->productStyleInsert->product->first()->sku]);
//    ProductStyle::where('sku', $this->productStyleInsert->id)->update(['type' => ProductStyle::TYPE_INSERT]);
//    $this->params['is_insert'] = SaleOrder::ACTIVE;
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//    barcodePrintFail($this);
//});

//
//// print style sku test
//test('print style sku test', function () {
//    $warehouse = Warehouse::factory(['id' => 1])->create();
//    $user = User::factory()->create();
//    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
//    $this->withHeaders([
//        'Authorization' => 'Bearer ' . $accessToken,
//        'Accept' => 'application/json',
//    ]);
//
//    $employee = Employee::factory()->create(['warehouse_id' => 1]);
//    $this->params['is_fba'] = null;
//    $this->params['style_sku'] = 'UNPT';
//    $this->params['employee_id'] = $employee->id;
//    $this->params['employee_number'] = $employee->code;
//
//    $this->setting = Setting::factory([
//        'label' => Setting::TEST_WIP_SKU,
//        'name' => Setting::TEST_WIP_SKU,
//        'value' => 'UNPT,UNNT'
//    ])->create();
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//    $this->assertEquals(json_decode($response->getContent(), true)['id'], 0);
//});
//
//// print style sku test with value null
//test('print style sku test with value null', function () {
//    $warehouse = Warehouse::factory(['id' => 1])->create();
//    $user = User::factory()->create();
//    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
//    $this->withHeaders([
//        'Authorization' => 'Bearer ' . $accessToken,
//        'Accept' => 'application/json',
//    ]);
//
//    $employee = Employee::factory()->create(['warehouse_id' => 1]);
//    $this->params['is_fba'] = null;
//    $this->params['style_sku'] = 'UNPT';
//    $this->params['employee_id'] = $employee->id;
//    $this->params['employee_number'] = $employee->code;
//
//    $this->setting = Setting::factory([
//        'label' => Setting::TEST_WIP_SKU,
//        'name' => Setting::TEST_WIP_SKU
//    ])->create();
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//    $this->assertEquals(BarcodePrinted::where('id', json_decode($response->getContent(), true)['id'])->first()->style_sku, 'UNPT');
//});


// success - print with style, priority store
//test('barcode print success - order priority', function () {
//    SaleOrder::where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['style_sku'] = $this->productStyle->sku;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});
//
//test('barcode print success - order priority, fba order', function () {
//    SaleOrder::query()->update(['is_fba_order' => SaleOrder::ACTIVE]);
//    $this->params['is_fba'] = SaleOrder::ACTIVE;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});

//test('barcode print success - order priority, xqc order', function () {
//    SaleOrder::query()->update(['is_xqc' => SaleOrder::ACTIVE]);
//    $this->params['is_xqc'] = SaleOrder::ACTIVE;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});

//test('barcode print success - order priority, xqc order', function () {
//    SaleOrder::query()->update(['is_xqc' => SaleOrder::ACTIVE]);
//    $this->params['is_xqc'] = SaleOrder::ACTIVE;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});

//test('barcode print success - order priority, manual process order', function () {
//    SaleOrder::query()->update(['is_fba_order' => SaleOrder::INACTIVE]);
//    $this->params['is_manual'] = SaleOrder::ACTIVE;
//    $this->params['is_xqc'] = SaleOrder::INACTIVE;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});

//test('barcode print success - order priority, reprint order', function () {
//    SaleOrder::query()->update(['is_fba_order' => SaleOrder::INACTIVE, 'is_manual' => SaleOrder::INACTIVE]);
//    SaleOrderItemBarcode::where('order_id', $this->orderOfPriorityStore->id)->update(['label_root_id' => 20]);
//    $this->params['is_reprint'] = SaleOrder::ACTIVE;
//    $this->params['is_xqc'] = SaleOrder::INACTIVE;
//    $this->params['is_fba'] = SaleOrder::INACTIVE;
//    $this->params['store_id'] = $this->priorityStore->id;
//
//    $response = $this->post($this->endpoint, $this->params);
//
//    $response->assertStatus(200);
//
//    barcodePrintPriorityOrderSuccess($this);
//});
