<?php

use App\Models\BarcodePrintedTime;
use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductRoulette;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/barcode/generate-batch';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id]);

    $this->productStyle = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_DTG,
        'name' => '3001',
        'sku' => 'UNPT',
        'type' => 'Tee'
    ])
        ->has(Product::factory([
            'parent_id' => 111,
            'sku' => 'UNPT9C00L'
        ]), 'product')
        ->create();

    $this->productStyleAnother = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_DTG,
        'name' => '3002',
        'sku' => 'UNGH',
        'type' => 'Fleece'
    ])
        ->has(Product::factory([
            'parent_id' => 111,
            'sku' => 'UNGH9C00L'
        ]), 'product')
        ->create();
    $this->productStyleInsert = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_DTG,
        'name' => '3002',
        'sku' => 'INPS'
    ])
        ->has(Product::factory([
            'parent_id' => 112,
            'sku' => 'INPS9C00L'
        ]), 'product')
        ->create();

    $this->store = Store::factory([
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'id' => 2000000,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'id' => 395215300,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
        [
            'id' => 3952153001,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
    ), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->created_at = Carbon::now()->subDays(20);
            $barcode->save();
        });
    });

    $this->saleOrderManual = SaleOrder::factory([
        'id' => 2000000111,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_manual' => SaleOrder::ACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'id' => 39521530011,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
        [
            'id' => 3952153001111,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
        ],
    ), 'barcodes'), 'items')
        ->create();

    $this->saleOrderManual->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrderManual->id;
            $barcode->created_at = Carbon::now()->subDays(20);
            $barcode->save();
        });
    });

    $this->barcodePrintedTime = BarcodePrintedTime::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'is_fba' => true,
    ])->create();
    $this->priorityStore = Store::factory()->create();

    Setting::factory()->create([
        'label' => Setting::PRIORITY_STORE,
        'name' => Setting::PRIORITY_STORE,
        'value' => $this->priorityStore->id
    ]);
    $this->orderOfPriorityStore = SaleOrder::factory([
        'id' => 20000001,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::INACTIVE,
        'is_eps' => SaleOrder::INACTIVE,
        'is_manual' => SaleOrder::ACTIVE,
        'is_xqc' => SaleOrder::INACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'store_id' => $this->priorityStore->id,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'id' => 3952153003,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
            'store_id' => $this->priorityStore->id,
        ],
        [
            'id' => 3952153004,
            'sku' => '63QCYN',
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => 0,
            'label_id' => 'xxxxx',
            'print_method' => ProductStyle::METHOD_DTG,
            'employee_reroute_id' => null,
            'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
            'label_root_id' => null,
            'print_barcode_at' => null,
            'store_id' => $this->priorityStore->id,
        ],
    ), 'barcodes'), 'items')
        ->create();

    $this->orderOfPriorityStore->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderOfPriorityStore->id;
            $barcode->created_at = Carbon::now()->subDays(10);

            $barcode->save();
        });
    });
    $this->params = [
        'employee_id' => $this->employee->id,
        'is'
    ];
    \Illuminate\Support\Facades\Cache::store(config('cache.redis_store'))->flush();

    Queue::fake();
});

test('Success', function () {
    ProductRoulette::factory()->create([
        'product_id' => $this->productStyle->product->first()->id,
        'is_active' => ProductRoulette::STATUS_ACTIVE,
        'created_by' => 1
    ]);

    $response = $this->post($this->endpoint, $this->params);
    $response = json_decode($response->getContent(), true);

    $barcodePrinted = \App\Models\BarcodePrinted::where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->where('store_id', null)
        ->where('style_sku', $this->productStyle->sku)
        ->where('account_id', null)
        ->where('quantity_input', 2)
        ->where('is_xqc', null)
        ->where('is_eps', null)
        ->where('is_manual', null)
        ->where('is_reprint', null)
        ->where('is_reroute', null)
        ->where('is_fba', null)
        ->where('is_insert', null)
        ->where('is_tiktok', null)
        ->where('is_bulk_order', null)
        ->where('is_top_style', 1)
        ->first();

    $this->assertNotNull($barcodePrinted);

    $barcodes = SaleOrderItemBarcode::join('sale_order_item', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
        ->where('sale_order_item.product_style_sku', $response['style_sku'])
        ->where('sale_order_item_barcode.barcode_printed_id', $response['id'])
        ->where('sale_order_item_barcode.warehouse_id', $this->warehouse->id)
        ->get();
    $this->assertCount(2, $barcodes);
});

test('There are currently no pending WIPs.', function () {
    SaleOrder::query()->update(['warehouse_id' => 2000]);

    $response = $this->post($this->endpoint, $this->params);
    $response = json_decode($response->getContent(), true);
    $this->assertEquals($response['message'], 'There are currently no pending WIPs.');
});

//order inactive
test('barcode print fail - time gap not allow', function () {
    ProductRoulette::factory()->create([
        'product_id' => $this->productStyle->product->first()->id,
        'is_active' => ProductRoulette::STATUS_ACTIVE,
        'created_by' => 1
    ]);
    Setting::factory()->create([
        'label' => Setting::WIP_ASSIGNMENT_TIME_GAP,
        'name' => Setting::WIP_ASSIGNMENT_TIME_GAP,
        'value' => 5
    ]);
    $this->post($this->endpoint, $this->params);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(400);
    $this->assertEquals($response['message'], 'You have a batch in progress. Please complete it before starting a new one.');
});
