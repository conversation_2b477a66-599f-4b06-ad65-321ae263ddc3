<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierEasypost;
use App\Models\ShippingCarrierPackage;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\LabelRepository;
use EasyPost\Util;
use Faker\Factory as faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery\MockInterface;

uses(RefreshDatabase::class);
beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/label/create-shipment';
    $this->params = [
        'order_id' => '',
        'weight_value' => '',
        'weight_unit' => '',
        'predefinedPackageId' => '',
        'length' => '',
        'width' => '',
        'height' => ''
    ];
});
// invalid input
test('invalid input', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'order_id' => ['The order id field is required.'],
        'weight_value' => ['The weight value field is required.'],
        'weight_unit' => ['The weight unit field is required.'],
        'predefinedPackageId' => ['The predefined package id field is required.'],
    ]);
    $this->params['order_id'] = 99999;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'order_id' => ['The selected order id is invalid.'],
        'weight_value' => ['The weight value field is required.'],
        'weight_unit' => ['The weight unit field is required.'],
        'predefinedPackageId' => ['The predefined package id field is required.'],
    ]);
    $dataSaleOrder = SaleOrder::factory()->create();
    $this->params['order_id'] = $dataSaleOrder->id;
    $this->params['weight_value'] = 'onlyLove';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'weight_unit' => ['The weight unit field is required.'],
        'predefinedPackageId' => ['The predefined package id field is required.'],
    ]);
    $this->params['weight_unit'] = 'onlyLove';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'predefinedPackageId' => ['The predefined package id field is required.'],
    ]);
});
// fail Order does not to Address. Please provide.
test('Order does not to Address. Please provide - error', function () {
    $dataSaleOrder = SaleOrder::factory()->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => 1
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Order does not to Address. Please provide.'
    ]);
});
//fail Order does not country for address to. Please provide.
test('Order do not country for address to. Please provide - error', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => null
        ]), 'addressSaleOrder')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => 1
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Order does not country for address to. Please provide.'
    ]);
});
//fail Store does not have Return Address. Please provide.
test('store have not return address. please provide - error', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
        ]), 'addressSaleOrder')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => 1
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Store ' . $dataSaleOrder->store->name . ' does not have Return Address. Please provide.'
    ]);
});
// Carrier package not found - error
test('carrier package not found - error', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => 1
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Carrier package not found!'
    ]);
});
// Shipping method not found!!- error
test('Shipping method not found! - error', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create('');
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Shipping method not found!'
    ]);
});
// has message from easypost - error
test('has message from easypost', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create([
            'shipping_method' => 'standard',
        ]);
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->has(ShippingCarrierEasypost::factory([
            'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            'warehouse_id' => $dataSaleOrder->warehouse_id,
            'status' => 1,
            'carrier_account' => faker::create()->userName(),
            'name' => 'USPS1',
            'api_key_easypost' => faker::create()->userName(),
        ]), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory([
            'name' => 'USPS1',
            'api_shipping_method' => 'standard',
            'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
        ]), 'shippingMethods')
        ->create();

    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $dataResponse = '{"created_at": "2023-01-05T09:58:33Z","is_return": false,"messages": [{"type": "rate_error","carrier": "USPS","message": "to postal code: zipcode format must be zzzzz[pppp]"}],"mode": "test","options": {"currency": "USD","payment": {"type": "SENDER"},"date_advance": 0},"reference": null,"status": "unknown","tracking_code": null,"updated_at": "2023-01-05T09:58:33Z","batch_id": null,"batch_status": null,
    "batch_message": null,"customs_info": null,"from_address": {},"insurance": null,"order_id": null,"parcel": {},"postage_label": null,"rates": [],"refund_status": null,"scan_form": null,"selected_rate": null,"tracker": null,"to_address": {},"usps_zone": 3,"return_address": {},"buyer_address": {"id": "adr_83e2e33a8cdf11ed828fac1f6bc7b362","object": "Address","created_at": "2023-01-05T09:58:33+00:00","updated_at": "2023-01-05T09:58:33+00:00","name": "Michael Franklin","company": null,"street1": "23425 W Coyote Trail","street2": null,"city": "SAND SPRINGS","state": "OK","zip": "7406343","country": "US","phone": "6574523231","email": "","mode": "test","carrier_facility": null,"residential": null,"federal_tax_id": null,"state_tax_id": null,"verifications": {}},"forms": [],"fees": [],"id": "shp_c2ca981ee9b14119bf1f945446e3c5b2","object": "Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('createShipmentFromEasypost')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => $dataResponse->messages[0]->message
    ]);
});

// get rate success with use store default of swiftpod
test('get rate success with use store default of swiftpod', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create(['shipping_method' => 'standard']);
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->has(ShippingCarrierEasypost::factory([
            'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            'warehouse_id' => $dataSaleOrder->warehouse_id,
            'status' => 1,
            'carrier_account' => faker::create()->userName(),
            'name' => 'USPS1',
            'api_key_easypost' => faker::create()->userName(),
        ]), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory([
            'name' => 'USPS1',
            'api_shipping_method' => 'standard',
            'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
        ]), 'shippingMethods')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $dataResponse = '{"created_at":"2022-08-16T17:42:34Z","is_return":false,"messages":[],"mode":"test","options":{"label_format":"PNG","invoice_number":"123","currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"123","status":"unknown","tracking_code":null,"updated_at":"2022-08-16T17:42:34Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":{"id":"cstinfo_25b2fdffe4424454a18f084009c54eeb","object":"CustomsInfo","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","contents_explanation":null,"contents_type":"merchandise","customs_certify":true,"customs_signer":"Steve Brule","eel_pfc":"NOEEI 30.37(a)","non_delivery_option":"return","restriction_comments":null,"restriction_type":"none","mode":"test","declaration":null,"customs_items":[{"id":"cstitem_54c75ae1f0a341748607ec8a4d3d0b3a","object":"CustomsItem","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","description":"Sweet shirts","hs_tariff_number":"654321","origin_country":"US","quantity":2,"value":"23.25","weight":11,"code":null,"mode":"test","manufacturer":null,"currency":null,"eccn":null,"printed_commodity_identifier":null}]},"from_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_c2f6f30fc810414da498d2b2b881dbd0","object":"Parcel","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","length":10,"width":8,"height":4,"predefined_package":null,"weight":15.4,"mode":"test"},"postage_label":null,"rates":[{"id":"rate_bf9a28151d324567b25a03e16683c7c6","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"ParcelSelect","carrier":"USPS","rate":"7.75","currency":"USD","retail_rate":"7.75","retail_currency":"USD","list_rate":"7.75","list_currency":"USD","billing_type":"easypost","delivery_days":5,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":5,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_53bcc7cb0e72462f88db69e870af6a71","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Priority","carrier":"USPS","rate":"7.90","currency":"USD","retail_rate":"9.45","retail_currency":"USD","list_rate":"7.90","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_5da393ed1de344438992a0cbc7258695","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"First","carrier":"USPS","rate":"5.57","currency":"USD","retail_rate":"5.57","retail_currency":"USD","list_rate":"5.57","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_978c1377e85941768c3848da25264f40","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Express","carrier":"USPS","rate":"29.50","currency":"USD","retail_rate":"33.55","retail_currency":"USD","list_rate":"29.50","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"}],"refund_status":null,"scan_form":null,"selected_rate":null,"tracker":null,"to_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"usps_zone":4,"return_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"buyer_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"forms":[],"fees":[],"id":"shp_be00ffa42721445a9f4c48143bcdb11e","object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('createShipmentFromEasypost')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'success'
    ]);
});

// get rate success with use store has apikey and account of store
test('get rate success with use store has apikey and account of store', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create(['shipping_method' => 'standard']);
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => $dataSaleOrder->store_id,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS1',
                'api_key_easypost' => faker::create()->userName(),
            ],
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => faker::create()->userName(),
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS1',
                'api_shipping_method' => 'standard',
                'store_id' => $dataSaleOrder->store_id,
            ],
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
        )), 'shippingMethods')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $dataResponse = '{"created_at":"2022-08-16T17:42:34Z","is_return":false,"messages":[],"mode":"test","options":{"label_format":"PNG","invoice_number":"123","currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"123","status":"unknown","tracking_code":null,"updated_at":"2022-08-16T17:42:34Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":{"id":"cstinfo_25b2fdffe4424454a18f084009c54eeb","object":"CustomsInfo","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","contents_explanation":null,"contents_type":"merchandise","customs_certify":true,"customs_signer":"Steve Brule","eel_pfc":"NOEEI 30.37(a)","non_delivery_option":"return","restriction_comments":null,"restriction_type":"none","mode":"test","declaration":null,"customs_items":[{"id":"cstitem_54c75ae1f0a341748607ec8a4d3d0b3a","object":"CustomsItem","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","description":"Sweet shirts","hs_tariff_number":"654321","origin_country":"US","quantity":2,"value":"23.25","weight":11,"code":null,"mode":"test","manufacturer":null,"currency":null,"eccn":null,"printed_commodity_identifier":null}]},"from_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_c2f6f30fc810414da498d2b2b881dbd0","object":"Parcel","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","length":10,"width":8,"height":4,"predefined_package":null,"weight":15.4,"mode":"test"},"postage_label":null,"rates":[{"id":"rate_bf9a28151d324567b25a03e16683c7c6","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"ParcelSelect","carrier":"USPS","rate":"7.75","currency":"USD","retail_rate":"7.75","retail_currency":"USD","list_rate":"7.75","list_currency":"USD","billing_type":"easypost","delivery_days":5,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":5,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_53bcc7cb0e72462f88db69e870af6a71","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Priority","carrier":"USPS","rate":"7.90","currency":"USD","retail_rate":"9.45","retail_currency":"USD","list_rate":"7.90","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_5da393ed1de344438992a0cbc7258695","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"First","carrier":"USPS","rate":"5.57","currency":"USD","retail_rate":"5.57","retail_currency":"USD","list_rate":"5.57","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_978c1377e85941768c3848da25264f40","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Express","carrier":"USPS","rate":"29.50","currency":"USD","retail_rate":"33.55","retail_currency":"USD","list_rate":"29.50","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"}],"refund_status":null,"scan_form":null,"selected_rate":null,"tracker":null,"to_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"usps_zone":4,"return_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"buyer_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"forms":[],"fees":[],"id":"shp_be00ffa42721445a9f4c48143bcdb11e","object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('createShipmentFromEasypost')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'success'
    ]);
});

// get rate success with use store has apikey of store default and account of store
test('get rate success with use store has apikey of store default and account of store', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS],
        )), 'addressSaleOrder')
        ->create(['shipping_method' => 'USPS_store']);
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => $dataSaleOrder->store_id,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'StoreUsps',
                'api_key_easypost' => faker::create()->userName(),
            ],
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => faker::create()->userName(),
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'StoreUsps',
                'api_shipping_method' => 'USPS_store',
                'store_id' => $dataSaleOrder->store_id,
            ],
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
        )), 'shippingMethods')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $dataResponse = '{"created_at":"2022-08-16T17:42:34Z","is_return":false,"messages":[],"mode":"test","options":{"label_format":"PNG","invoice_number":"123","currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"123","status":"unknown","tracking_code":null,"updated_at":"2022-08-16T17:42:34Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":{"id":"cstinfo_25b2fdffe4424454a18f084009c54eeb","object":"CustomsInfo","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","contents_explanation":null,"contents_type":"merchandise","customs_certify":true,"customs_signer":"Steve Brule","eel_pfc":"NOEEI 30.37(a)","non_delivery_option":"return","restriction_comments":null,"restriction_type":"none","mode":"test","declaration":null,"customs_items":[{"id":"cstitem_54c75ae1f0a341748607ec8a4d3d0b3a","object":"CustomsItem","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","description":"Sweet shirts","hs_tariff_number":"654321","origin_country":"US","quantity":2,"value":"23.25","weight":11,"code":null,"mode":"test","manufacturer":null,"currency":null,"eccn":null,"printed_commodity_identifier":null}]},"from_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_c2f6f30fc810414da498d2b2b881dbd0","object":"Parcel","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","length":10,"width":8,"height":4,"predefined_package":null,"weight":15.4,"mode":"test"},"postage_label":null,"rates":[{"id":"rate_bf9a28151d324567b25a03e16683c7c6","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"ParcelSelect","carrier":"USPS","rate":"7.75","currency":"USD","retail_rate":"7.75","retail_currency":"USD","list_rate":"7.75","list_currency":"USD","billing_type":"easypost","delivery_days":5,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":5,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_53bcc7cb0e72462f88db69e870af6a71","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Priority","carrier":"USPS","rate":"7.90","currency":"USD","retail_rate":"9.45","retail_currency":"USD","list_rate":"7.90","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_5da393ed1de344438992a0cbc7258695","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"First","carrier":"USPS","rate":"5.57","currency":"USD","retail_rate":"5.57","retail_currency":"USD","list_rate":"5.57","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_978c1377e85941768c3848da25264f40","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Express","carrier":"USPS","rate":"29.50","currency":"USD","retail_rate":"33.55","retail_currency":"USD","list_rate":"29.50","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"}],"refund_status":null,"scan_form":null,"selected_rate":null,"tracker":null,"to_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"usps_zone":4,"return_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"buyer_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"forms":[],"fees":[],"id":"shp_be00ffa42721445a9f4c48143bcdb11e","object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('createShipmentFromEasypost')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'success'
    ]);
});

// get rate success with use store has apikey of store default and account of store
test('get rate success with use store has apikey of store default and account of store - carrier ontracv3', function () {
    $dataSaleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(['street1' => '388 Townsend St from']), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            ['type_address' => SaleOrderAddress::TO_ADDRESS],
            ['type_address' => SaleOrderAddress::RETURN_ADDRESS, 'street1' => '388 Townsend St return'],
        )), 'addressSaleOrder')
        ->create(['shipping_method' => 'ontracv3_GRND']);
    $carrier = ShippingCarrier::factory()
        ->has(ShippingCarrierPackage::factory([
            'name' => faker::create()->userName(),
        ]), 'shippingCarrierPredefinedPackage')
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => $dataSaleOrder->store_id,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'ONTRACV3',
                'api_key_easypost' => faker::create()->userName(),
            ],
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $dataSaleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => faker::create()->userName(),
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'ONTRACV3',
                'api_shipping_method' => 'ontracv3_GRND',
                'store_id' => $dataSaleOrder->store_id,
            ],
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
        )), 'shippingMethods')
        ->create();
    $this->params = [
        'order_id' => $dataSaleOrder->id,
        'weight_value' => 4,
        'weight_unit' => 'oz',
        'predefinedPackageId' => $carrier->shippingCarrierPredefinedPackage[0]->id,
        'length' => '',
        'width' => '',
        'height' => ''
    ];
    $dataResponse = '{"created_at":"2022-08-16T17:42:34Z","is_return":false,"messages":[],"mode":"test","options":{"label_format":"PNG","invoice_number":"123","currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"123","status":"unknown","tracking_code":null,"updated_at":"2022-08-16T17:42:34Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":{"id":"cstinfo_25b2fdffe4424454a18f084009c54eeb","object":"CustomsInfo","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","contents_explanation":null,"contents_type":"merchandise","customs_certify":true,"customs_signer":"Steve Brule","eel_pfc":"NOEEI 30.37(a)","non_delivery_option":"return","restriction_comments":null,"restriction_type":"none","mode":"test","declaration":null,"customs_items":[{"id":"cstitem_54c75ae1f0a341748607ec8a4d3d0b3a","object":"CustomsItem","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","description":"Sweet shirts","hs_tariff_number":"654321","origin_country":"US","quantity":2,"value":"23.25","weight":11,"code":null,"mode":"test","manufacturer":null,"currency":null,"eccn":null,"printed_commodity_identifier":null}]},"from_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St return","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_c2f6f30fc810414da498d2b2b881dbd0","object":"Parcel","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","length":10,"width":8,"height":4,"predefined_package":null,"weight":15.4,"mode":"test"},"postage_label":null,"rates":[{"id":"rate_bf9a28151d324567b25a03e16683c7c6","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"ParcelSelect","carrier":"USPS","rate":"7.75","currency":"USD","retail_rate":"7.75","retail_currency":"USD","list_rate":"7.75","list_currency":"USD","billing_type":"easypost","delivery_days":5,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":5,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"},{"id":"rate_978c1377e85941768c3848da25264f40","object":"Rate","created_at":"2022-08-16T17:42:34Z","updated_at":"2022-08-16T17:42:34Z","mode":"test","service":"Express","carrier":"USPS","rate":"29.50","currency":"USD","retail_rate":"33.55","retail_currency":"USD","list_rate":"29.50","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_be00ffa42721445a9f4c48143bcdb11e","carrier_account_id":"ca_8dc116debcdb49b5a66a2ddee4612600"}],"refund_status":null,"scan_form":null,"selected_rate":null,"tracker":null,"to_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"usps_zone":4,"return_address":{"id":"adr_cf70e2421d8a11edb012ac1f6bc7b362","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Jack Sparrow","company":null,"street1":"388 Townsend St from","street2":"Apt 20","city":"San Francisco","state":"CA","zip":"94107","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"buyer_address":{"id":"adr_cf6ee6d01d8a11ed932fac1f6bc7bdc6","object":"Address","created_at":"2022-08-16T17:42:34+00:00","updated_at":"2022-08-16T17:42:34+00:00","name":"Elizabeth Swan","company":null,"street1":"179 N Harbor Dr","street2":null,"city":"Redondo Beach","state":"CA","zip":"90277","country":"US","phone":"**********","email":"<EMAIL>","mode":"test","carrier_facility":null,"residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"forms":[],"fees":[],"id":"shp_be00ffa42721445a9f4c48143bcdb11e","object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('createShipmentFromEasypost')->once()->andReturn($dataResponse);
    })->makePartial();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['data']['return_address']['street1'])->toEqual($dataSaleOrder->wareHouse->street1);
    expect(json_decode($response->getContent(), true)['data']['from_address']['street1'])->toEqual($dataSaleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::RETURN_ADDRESS)->first()->street1);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'success'
    ]);
});
