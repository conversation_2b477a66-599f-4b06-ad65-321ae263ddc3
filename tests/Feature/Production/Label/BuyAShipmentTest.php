<?php

use App\Models\Employee;
use App\Models\RedbubblePacking;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierEasypost;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\TimeTracking;
use App\Models\Warehouse;
use App\Repositories\LabelRepository;
use EasyPost\Error;
use EasyPost\Util;
use Faker\Factory as faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery\MockInterface;

uses(RefreshDatabase::class);
beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->user = $user;
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/label/buy-a-shipment';
    $this->employee = Employee::factory()->create();
    $this->timeTracking = TimeTracking::factory()->create();
    $this->setting = Setting::factory([
        'name' => 'easypost_api_key',
        'value' => faker::create()->phoneNumber()
    ])->create();
    $this->RedbubblePacking = RedbubblePacking::factory()->createMany([
        [
            'condition_package' => '= 1',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1028'
        ],
        [
            'condition_package' => '>= 2 <= 5',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1023'
        ],
        [
            'condition_package' => '> 5',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1001'
        ],
        [
            'condition_package' => '>= 2 <= 5',
            'packaging_active' => 0,
            'rb_sku' => 'RBPKGUS10223'
        ],
        [
            'condition_package' => null,
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS9999',
            'packaging_type' => RedbubblePacking::PACKAGING_TYPE_INSERT
        ],
    ]);
    $this->params = [
        'rate' => '',
        'id_shipment' => '',
        'order_id' => '',
        'id_time_checking' => '',
        'employeeId' => '',
        'is_shipment' => false,
        'label_verify' => [],
    ];
});
// invalid input
test('invalid input', function () {
    unset($this->params['is_shipment']);
    // invalid all
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'rate' => ['The rate field is required.'],
        'id_shipment' => ['The id shipment field is required.'],
        'order_id' => ['The order id field is required.'],
        'id_time_checking' => ['The id time checking field is required.'],
        'employeeId' => ['The employee id field is required.'],
        'is_shipment' => ['The is shipment field is required.'],
    ]);
    $this->params['rate'] = '123455677890sads';
    $this->params['id_shipment'] = 'abcxyz';
    $this->params['order_id'] = 12313;
    $this->params['id_time_checking'] = 99999;
    $this->params['employeeId'] = 999999;
    $this->params['is_shipment'] = false;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id_time_checking' => ['The selected id time checking is invalid.'],
        'employeeId' => ['The selected employee id is invalid.'],
    ]);
});
//order not found
test('order not found', function () {
    $this->params['rate'] = ['data' => 123];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = 12313;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'create label error'
    ]);
});
// success with api key default - shipment_account swiftpod
test('success with create by easypost swiftpod', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->create([
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'standard',
        ]);
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(1)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(1)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'swiftpod']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});
// success with api key default - shipment_account store
test('success with create by easypost of store', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber()
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'store_usps',
        ]);
    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});
// success with sale order had shipment
test('success with sale order had shipment', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber()
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => Shipment::factory()->create()->id,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::SHIPPED,
            'shipping_method' => 'store_usps',
        ]);
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    $this->params['rate'] = ['data' => 123, 'carrier' => 'USPS'];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $saleOrder->shipment_id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});
// success with sale order had shipment buy that shipment refunded
test('success with sale order had shipment that shipment refunded', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber()
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => Shipment::factory(['employee_refund_id' => 111, 'refund_status' => 'submitted'])->create()->id,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::SHIPPED,
            'shipping_method' => 'store_usps',
        ]);
    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});
// An error occurred, please try again later!
test('An error occurred, please try again later!', function () {
    $this->mock(LabelRepository::class, function (MockInterface $mock) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn(['errorr']);
    })->makePartial();
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber()
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::SHIPPED,
            'shipping_method' => 'store_usps',
        ]);
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(500);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'An error occurred, please try again later!'
    ]);
    $this->assertDatabaseHas('sale_order', ['id' => $saleOrder->id, 'is_shipment_create_error' => SaleOrder::IS_SHIPMENT_CREATE_ERROR]);
    $this->assertDatabaseHas('shipment_create_error_log', ['order_id' => $saleOrder->id]);
});
// payment required
test('error from EasyPost', function () {
    $data = '{"error":{"code":"PARAMETER.REQUIRED","message":"Missing required parameter.","errors":[{"field":"shipment","message":"cannot be blank"}]}}';
    $dataException = new Error('loi', 422, $data);
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataException) {
        $mock->shouldAllowMockingProtectedMethods()
            ->shouldReceive('buyAShipmentFromEasyPost')
            ->once()
            ->andThrowExceptions([$dataException]);
    })->makePartial();
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber()
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::SHIPPED,
            'shipping_method' => 'store_usps',
        ]);
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(500);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'An error occurred, please try again later!',
        'errorEasypost' => $dataException->jsonBody
    ]);
    $this->assertDatabaseHas('sale_order', ['id' => $saleOrder->id, 'is_shipment_create_error' => SaleOrder::IS_SHIPMENT_CREATE_ERROR]);
    $this->assertDatabaseHas('shipment_create_error_log', ['order_id' => $saleOrder->id, 'note' => $data]);
});
// success with create shipment use apikey of switfpod but use account of store
test('success with create shipment use apikey of switfpod but use account of store', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->create([
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'store_usps',
        ]);
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => $this->setting->value
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'swiftpod_store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

test('success with create by easypost of store Redbubble with order quantity = 1 ', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber(),
            'id' => Store::STORE_REDBUBBLE
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'store_usps',
            'order_quantity' => 1
        ]);
    $skuRbPackaging = 'RBPKGUS1028';
    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbPackaging]);
    $skuRbInsert = 'RBPKGUS9999';
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbInsert]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

test('success with create by easypost of store Redbubble with order quantity = 3 ', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber(),
            'id' => Store::STORE_REDBUBBLE
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'store_usps',
            'order_quantity' => 3
        ]);
    $skuRbPackaging = 'RBPKGUS1023';
    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbPackaging]);
    $skuRbInsert = 'RBPKGUS9999';
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbInsert]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

test('success with create by easypost of store Redbubble with order quantity = 10', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Store::factory([
            'easypost_api_key' => faker::create()->phoneNumber(),
            'id' => Store::STORE_REDBUBBLE
        ]), 'store')
        ->create([
            'warehouse_id' => $this->warehouse->id,
            'shipment_id' => null,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'store_usps',
            'order_quantity' => 10
        ]);
    $skuRbPackaging = 'RBPKGUS1001';
    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbPackaging]);
    $skuRbInsert = 'RBPKGUS9999';
    $this->assertDatabaseHas('shipment_package', ['shipment_id' => $shipment->id, 'order_id' => $saleOrder->id, 'sku' => $skuRbInsert]);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

// success with create shipment has label
test('success with label ship - is shipment true and has label barcode', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderItem::factory()
            ->count(2)
            ->sequence(fn ($sequence) => [
                'quantity' => $sequence->index < 1 ? 1 : 2,
            ])
            ->state(function ($attributes, SaleOrder $saleOrder) {
                return [
                    'warehouse_id' => $saleOrder->warehouse_id,
                ];
            })
            ->afterCreating(function (SaleOrderItem $orderItem) {
                $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
                for ($i = 0; $i < $barcodeCount; $i++) {
                    $labelId = uniqid('071722-SJ-M-9999-');
                    $orderItem->barcodes()->create([
                        'order_id' => $orderItem->order_id,
                        'label_id' => $labelId,
                        'is_deleted' => 0,
                        'warehouse_id' => $orderItem->warehouse_id,
                    ]);
                }
            }), 'items')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'standard',
            'shipment_id' => null,
        ]);
    $dataLabel = SaleOrderItemBarcode::where('order_id', $saleOrder->id)
                                       ->get();

    $arrLabel = $dataLabel->map(function ($item) {
        return  $item->label_id;
    })->toArray();
    $this->params['label_verify'] = $arrLabel;
    $this->params['is_shipment'] = true;

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentItem = \App\Models\ShipmentItem::where('order_id', $saleOrder->id)->count();
    expect($shipmentItem)->toEqual($saleOrder->items->count());
    $shipmentItemLabel = \App\Models\ShipmentItemLabel::whereIn('label_id', $arrLabel)->count();
    expect($shipmentItemLabel)->toEqual(count($arrLabel));
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

// success with create shipment has label
test('success with label ship - is shipment false and has label barcode', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderItem::factory()
            ->count(2)
            ->sequence(fn ($sequence) => [
                'quantity' => $sequence->index < 1 ? 1 : 2,
            ])
            ->state(function ($attributes, SaleOrder $saleOrder) {
                return [
                    'warehouse_id' => $saleOrder->warehouse_id,
                ];
            })
            ->afterCreating(function (SaleOrderItem $orderItem) {
                $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
                for ($i = 0; $i < $barcodeCount; $i++) {
                    $labelId = uniqid('071722-SJ-M-9999-');
                    $orderItem->barcodes()->create([
                        'order_id' => $orderItem->order_id,
                        'label_id' => $labelId,
                        'is_deleted' => 0,
                        'warehouse_id' => $orderItem->warehouse_id,
                    ]);
                }
            }), 'items')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'standard',
            'shipment_id' => null,
        ]);
    $dataLabel = SaleOrderItemBarcode::where('order_id', $saleOrder->id)
        ->get();

    $arrLabel = $dataLabel->map(function ($item) {
        return  $item->label_id;
    })->toArray();
    $this->params['label_verify'] = $arrLabel;
    $this->params['is_shipment'] = false;

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentItem = \App\Models\ShipmentItem::where('order_id', $saleOrder->id)->count();
    expect($shipmentItem)->toEqual(0);
    $shipmentItemLabel = \App\Models\ShipmentItemLabel::whereIn('label_id', $arrLabel)->count();
    expect($shipmentItemLabel)->toEqual(0);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});

// success with create shipment has label
test('success with label ship - is shipment true and has label barcode empty', function () {
    $saleOrder = SaleOrder::factory()
        ->for(Warehouse::factory(), 'wareHouse')
        ->for(Store::factory(), 'store')
        ->has(SaleOrderItem::factory()
            ->count(2)
            ->sequence(fn ($sequence) => [
                'quantity' => $sequence->index < 1 ? 1 : 2,
            ])
            ->state(function ($attributes, SaleOrder $saleOrder) {
                return [
                    'warehouse_id' => $saleOrder->warehouse_id,
                ];
            })
            ->afterCreating(function (SaleOrderItem $orderItem) {
                $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
                for ($i = 0; $i < $barcodeCount; $i++) {
                    $labelId = uniqid('071722-SJ-M-9999-');
                    $orderItem->barcodes()->create([
                        'order_id' => $orderItem->order_id,
                        'label_id' => $labelId,
                        'is_deleted' => 0,
                        'warehouse_id' => $orderItem->warehouse_id,
                    ]);
                }
            }), 'items')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'account_id' => faker::create()->randomNumber([1, 2, 3, 4, 5, 6]),
            'order_status' => SaleOrder::IN_PRODUCTION,
            'shipping_method' => 'standard',
            'shipment_id' => null,
        ]);
    $dataLabel = SaleOrderItemBarcode::where('order_id', $saleOrder->id)
        ->get();

    $arrLabel = $dataLabel->map(function ($item) {
        return  $item->label_id;
    })->toArray();
    $this->params['label_verify'] = [];
    $this->params['is_shipment'] = true;

    $dataResponse = '{"id":"shp_814aff9caaf0402a8af38302742667f9","created_at":"2023-01-12T07:11:14Z","is_return":false,"messages":[],"mode":"production","options":{"currency":"USD","payment":{"type":"SENDER"},"date_advance":0},"reference":"011023-SJ-S-003247","status":"unknown","tracking_code":"9400136109679306043254","updated_at":"2023-01-12T07:11:16Z","batch_id":null,"batch_status":null,"batch_message":null,"customs_info":null,"from_address":{"id":"adr_4d265b07924811edafc0ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"FULFILLMENT CENTER CA","company":null,"street1":"1371 Oakland Rd","street2":null,"city":"San Jose","state":"CA","zip":"95112","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":"LAX","residential":null,"federal_tax_id":null,"state_tax_id":null,"verifications":[]},"insurance":null,"order_id":null,"parcel":{"id":"prcl_a34520cdb09c44e6acf7506813db902f","object":"Parcel","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","length":null,"width":null,"height":null,"predefined_package":null,"weight":4,"mode":"production"},"postage_label":{"id":"pl_bc5e0ed3315246c88b79a006b176268f","object":"PostageLabel","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:17Z","date_advance":0,"integrated_form":"none","label_date":"2023-01-12T07:11:15Z","label_resolution":300,"label_size":"4x6","label_type":"default","label_file_type":"image\/png","label_url":"https:\/\/easypost-files.s3.us-west-2.amazonaws.com\/files\/postage_label\/20230112\/412f16d7996d403083817b8af19eb282.png","label_pdf_url":null,"label_zpl_url":"https:\/\/easypost-files.s3-us-west-2.amazonaws.com\/files\/postage_label\/20230112\/a468eb466553434dbb6137da72f95548.zpl","label_epl2_url":null,"label_file":null},"rates":[{"id":"rate_04562daf7b8b4c6d9e9fe0783ff647f5","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Priority","carrier":"USPS","rate":"6.60","currency":"USD","retail_rate":"9.00","retail_currency":"USD","list_rate":"7.62","list_currency":"USD","billing_type":"easypost","delivery_days":2,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":2,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_59b1365028574da298e1cf54149ddcd4","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"Express","carrier":"USPS","rate":"22.09","currency":"USD","retail_rate":"27.25","retail_currency":"USD","list_rate":"23.75","list_currency":"USD","billing_type":"easypost","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},{"id":"rate_7ebae2206ccc4a6c87780fcc1c22934f","object":"Rate","created_at":"2023-01-12T07:11:14Z","updated_at":"2023-01-12T07:11:14Z","mode":"production","service":"ParcelSelect","carrier":"USPS","rate":"7.18","currency":"USD","retail_rate":"7.47","retail_currency":"USD","list_rate":"7.47","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"}],"refund_status":null,"scan_form":null,"selected_rate":{"id":"rate_0082496c1d48470b8ed04c4d2a7ffbd3","object":"Rate","created_at":"2023-01-12T07:11:15Z","updated_at":"2023-01-12T07:11:15Z","mode":"production","service":"First","carrier":"USPS","rate":"3.59","currency":"USD","retail_rate":"4.80","retail_currency":"USD","list_rate":"3.62","list_currency":"USD","billing_type":"easypost","delivery_days":3,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":3,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier_account_id":"ca_4ec6859ba7344c25a66371ca340fec42"},"tracker":{"id":"trk_19f3ac55679d4ce7ba9e69e78380ff08","object":"Tracker","mode":"production","tracking_code":"9400136109679306043254","status":"unknown","status_detail":"unknown","created_at":"2023-01-12T07:11:16Z","updated_at":"2023-01-12T07:11:16Z","signed_by":null,"weight":null,"est_delivery_date":null,"shipment_id":"shp_814aff9caaf0402a8af38302742667f9","carrier":"USPS","tracking_details":[],"fees":[],"carrier_detail":null,"public_url":"https:\/\/track.easypost.com\/djE6dHJrXzE5ZjNhYzU1Njc5ZDRjZTdiYTllNjllNzgzODBmZjA4"},"to_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"usps_zone":2,"return_address":{"id":"adr_4d2a4398924811edafd1ac1f6b0a0d1e","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"MY NEW STORE","company":null,"street1":"1322 BUSHWICK AVE","street2":null,"city":"BROOKLYN","state":"NY","zip":"11207-1046","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":40.68613,"longitude":-73.91223,"time_zone":"America\/New_York"}}}},"buyer_address":{"id":"adr_4d2460b9924811ed9068ac1f6bc72124","object":"Address","created_at":"2023-01-12T07:11:14+00:00","updated_at":"2023-01-12T07:11:14+00:00","name":"JULIO  MONROY","company":null,"street1":"3248 W NOBILITY DR","street2":null,"city":"FRESNO","state":"CA","zip":"93711-0296","country":"US","phone":"**********","email":null,"mode":"production","carrier_facility":null,"residential":true,"federal_tax_id":null,"state_tax_id":null,"verifications":{"delivery":{"success":true,"errors":[],"details":{"latitude":36.8383,"longitude":-119.85071,"time_zone":"America\/Los_Angeles"}}}},"forms":[],"fees":[{"object":"Fee","type":"LabelFee","amount":"0.00500","charged":true,"refunded":false},{"object":"Fee","type":"PostageFee","amount":"3.59000","charged":true,"refunded":false}],"object":"Shipment"}';
    $dataResponse = Util::convertToEasyPostObject(json_decode($dataResponse, true), '*********', null, 'shipment');
    $this->mock(LabelRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('buyAShipmentFromEasyPost')->once()->andReturn($dataResponse);
    })->makePartial();
    ShippingCarrier::factory()
        ->has(ShippingCarrierEasypost::factory()->count(2)->state(new Sequence(
            [
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'USPS',
                'api_key_easypost' => $this->setting->value,
            ],
            [
                'store_id' => $saleOrder->store_id,
                'warehouse_id' => $saleOrder->warehouse_id,
                'status' => 1,
                'carrier_account' => faker::create()->userName(),
                'name' => 'STORE',
                'api_key_easypost' => faker::create()->userName()
            ],
        )), 'shippingCarrierEasyposts')
        ->has(ShippingMethod::factory()->count(2)->state(new Sequence(
            [
                'name' => 'USPS',
                'api_shipping_method' => 'standard',
                'store_id' => \App\Repositories\LabelRepository::DEFAULT_STORE,
            ],
            [
                'name' => 'STORE',
                'api_shipping_method' => $saleOrder->shipping_method,
                'store_id' => $saleOrder->store_id,
            ],
        )), 'shippingMethods')
        ->create(['code' => 'USPS']);

    $this->params['rate'] = [
        'data' => 123,
        'carrier' => 'USPS',
    ];
    $this->params['id_shipment'] = 'shp_814aff9caaf0402a8af38302742667f9';
    $this->params['order_id'] = $saleOrder->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $this->params['employeeId'] = $this->employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id' => $dataResponse->id
    ]);
    $shipment = Shipment::where('tracking_number', $dataResponse['tracking_code'])->first();
    $this->assertDatabaseHas('shipment', ['tracking_number' => $dataResponse['tracking_code'], 'shipment_account' => 'store']);
    $this->assertDatabaseHas('sale_order', ['shipment_id' => $shipment->id, 'order_status' => SaleOrder::SHIPPED]);
    $this->assertDatabaseHas('shipment_easypost', ['order_id' => $saleOrder->id, 'shipment_id' => $shipment->id]);
    $this->assertDatabaseHas('sale_order_history', ['user_id' => $this->user->id, 'employee_id' => $this->params['employeeId'], 'order_id' => $saleOrder->id, 'type' => SaleOrderHistory::CREATE_LABEL]);
    $shipmentItem = \App\Models\ShipmentItem::where('order_id', $saleOrder->id)->count();
    expect($shipmentItem)->toEqual(0);
    $shipmentItemLabel = \App\Models\ShipmentItemLabel::whereIn('label_id', $arrLabel)->count();
    expect($shipmentItemLabel)->toEqual(0);
    $shipmentFee = \App\Models\ShipmentFee::where('shipment_id', $shipment->id)->count();
    $this->assertEquals(2, $shipmentFee);
});
