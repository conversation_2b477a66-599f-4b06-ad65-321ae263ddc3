<?php

use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\Shipment;
use App\Models\ShipmentItem;
use App\Models\ShipmentItemLabel;
use App\Models\ShippingCarrier;
use App\Models\TimeTracking;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->employee = Employee::factory()->create();
    $this->product = Product::factory()->createMany([
        [
            'sku' => 'UNPT9C0XS',
        ],
        [
            'sku' => 'UNPT2H00M',
        ]
    ]);
    $this->user = User::factory()->create();
    $this->shippingCarrier = ShippingCarrier::factory()->createMany([
        [
            'name' => 'USPS',
            'code' => 'USPS',
            'is_deleted' => 0,
            'tracking_url' => 'USPS_trackingNumber={tracking_code}'
        ],
        [
            'name' => 'Asendia USA',
            'code' => 'AsendiaUsa',
            'is_deleted' => 0,
            'tracking_url' => 'Asendia_trackingNumber={tracking_code}'
        ]
    ]);
    $this->saleOrder = SaleOrder::factory()
        ->has(Shipment::factory()->count(2)->state(new Sequence(
            [
                'tracking_number' => 123456789,
                'carrier_code' => $this->shippingCarrier[0]->code,
                'warehouse_id' => $this->warehouse->id,
                'is_deleted' => false,
                'refund_status' => null,
                'tracking_status' => Shipment::UNKNOWN
            ],
            [
                'tracking_number' => 987654321,
                'carrier_code' => $this->shippingCarrier[1]->code,
                'warehouse_id' => $this->warehouse->id,
                'is_deleted' => false,
                'refund_status' => null,
                'tracking_status' => Shipment::UNKNOWN
            ],
        )), 'shipment')
        ->has(SaleOrderItem::factory()
            ->count(2)
            ->sequence(fn ($sequence) => [
                'quantity' => $sequence->index < 1 ? 1 : 2,
            ])
            ->state(function ($attributes, SaleOrder $saleOrder) {
                return [
                    'warehouse_id' => $saleOrder->warehouse_id,
                ];
            })
            ->afterCreating(function (SaleOrderItem $orderItem) {
                $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
                for ($i = 0; $i < $barcodeCount; $i++) {
                    $labelId = uniqid('071722-SJ-M-9999-');
                    $orderItem->barcodes()->create([
                        'order_id' => $orderItem->order_id,
                        'label_id' => $labelId,
                        'is_deleted' => 0,
                        'warehouse_id' => $orderItem->warehouse_id,
                    ]);
                }
            }), 'items')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => 'shipped',
            'shipping_method' => 'standard',
        ]);
    $dataMockSaleOrderItem = [];
    $dataMockProductStyle = [];
    foreach ($this->product as $key => $item) {
        $dataMockSaleOrderItem[] = [
            'order_id' => $this->saleOrder->id,
            'quantity' => 1,
            'product_id' => $item->id,
            'product_sku' => $item->sku,
            'product_style_sku' => substr($item->sku, 0, 4),
            'product_size_sku' => substr($item->sku, -3),
            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/2st.s3.us-east-2.amazonaws.com\/Design\/PancreaticCancerAwarenessUnisexTshirtInThisFamilyNobodyFightsAloneSurvivorFighterGiftAwarenessMonth.png"},{"name":"PreviewFiles.Front","value":"https:\/\/i.etsystatic.com\/24906590\/r\/il\/2341c6\/3145734374\/il_fullxfull.3145734374_gcm9.jpg"}]'
        ];
        $dataMockProductStyle[] = [
            'sku' => substr($item->sku, 0, 4),
            'type' => $key > 1 ? 'Fleece' : 'Tee',
        ];
    }

    $this->productStyle = ProductStyle::factory()->createMany($dataMockProductStyle);
    $this->timeTracking = TimeTracking::factory()->create();
    $this->endpoint = 'api/label/verify-label-id-for-order?';
    $this->params = [
        'order_id' => '',
        'label_id' => '',
        'shipment_id' => '',
        'employee_id' => '',
        'id_time_checking' => '',
    ];
});
// Invalid input
test('invalid input - error 422', function () {
    // required
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'order_id' => ['The order id field is required.'],
        'label_id' => ['The label id field is required.'],
        'shipment_id' => ['The shipment id field is required.'],
        'employee_id' => ['The employee id field is required.'],
        'id_time_checking' => ['The id time checking field is required.'],
    ]);

    // order id invalid
    $this->params['order_id'] = '9999999';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'order_id' => ['The selected order id is invalid.'],
    ]);

    //id_time_checking invalid
    $this->params['id_time_checking'] = 999999999;
    $this->params['order_id'] = '9999999';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'id_time_checking' => ['The selected id time checking is invalid.'],
    ]);

    // order not found
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = 'motngaynhoem';
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'message' => 'Order not found!'
    ]);
});

// Th chua co shipment item nao - label add new
test('Scan label not shipment item - label scan success', function () {
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items->first()->barcodes->first()->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    $dataResult = ['data' => [[
        'order_item_id' => $this->saleOrder->barcodeItems[0]->order_item_id,
        'total_verify' => 1,
    ]],
        'message' => 'Scan label success',
        'code' => 'label_insert',
    ];
    expect($res)->toMatchArray($dataResult);

    $shipmentItem = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $this->saleOrder->barcodeItems[0]->order_item_id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', 1)
        ->first();

    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $this->saleOrder->barcodeItems[0]->order_item_id,
        'shipment_id' => $this->params['shipment_id']]);
    $this->assertDatabaseHas('shipment_item_label', ['label_id' => $this->params['label_id'],
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItem->id,
    ]);
});

//Th đã có shipment item - Label has been scanned
test('scan label has shipment item - label has been scanned', function () {
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items->first()->barcodes->first()->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;

    $shipmentItem = ShipmentItem::factory()
        ->has(ShipmentItemLabel::factory()->count(1)->state(
            new Sequence(
                [
                    'label_id' => $this->saleOrder->items->first()->barcodes->first()->label_id,
                    'shipment_id' => $this->saleOrder->shipment[0]->id,
                    'employee_id' => 12121
                ],
            )), 'shipmentItemLabels')
        ->create(
            [
                'shipment_id' => $this->saleOrder->shipment[0]->id,
                'order_id' => $this->saleOrder->id,
                'order_item_id' => $this->saleOrder->items->first()->id,
            ],
        );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'message' => 'Label has been scanned',
        'code' => 'label_scanned',
    ]);
});

//Th đã có shipment item - label add new
test('scan label has shipment item - label scan success', function () {
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items[1]->barcodes[0]->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;

    $shipmenIttem = ShipmentItem::factory()
        ->has(ShipmentItemLabel::factory()->count(1)->state(
            new Sequence(
                [
                    'label_id' => $this->saleOrder->items[1]->barcodes[1]->label_id,
                    'shipment_id' => $this->saleOrder->shipment[0]->id,
                ],
            )), 'shipmentItemLabels')
        ->create(
            [
                'shipment_id' => $this->saleOrder->shipment[0]->id,
                'order_id' => $this->saleOrder->id,
                'quantity' => 1,
                'order_item_id' => $this->saleOrder->items[1]->id,
            ],
        );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataResult = ['data' => [[
        'order_item_id' => $this->saleOrder->items[1]->id,
        'total_verify' => 1,
    ]],
        'message' => 'Scan label success',
        'code' => 'label_success',
    ];
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray($dataResult);

    $shipmentItem = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $this->saleOrder->items[1]->id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', $shipmenIttem->quantity + 1)
        ->first();
    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $this->saleOrder->items[1]->barcodes[1]->order_item_id,
        'shipment_id' => $this->params['shipment_id']]);
    $this->assertDatabaseHas('shipment_item_label', [
        'label_id' => $this->params['label_id'],
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItem->id,
    ]);
});

// TH chưa có shipment item - Label has been scanned
test('scan label not shipment item - label has been scanned', function () {
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items->first()->barcodes->first()->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;

    ShipmentItemLabel::factory()->create(
        [
            'label_id' => $this->saleOrder->items->first()->barcodes->first()->label_id,
            'shipment_id' => $this->saleOrder->shipment[0]->id,
            'employee_id' => $this->employee->id,
        ],
    );

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        'message' => 'Label has been scanned',
        'code' => 'label_scanned',
    ]);
});

//Th chưa có shipment item - label add new - order insert printing
test('Scan label not shipment item - label scan success - order insert printing', function () {
    $productInsert = Product::factory()->create([
        'sku' => 'UNPT9C0XS',
        'style' => 'INSE',
        'size' => '4x6'
    ]);
    $this->productStyle = ProductStyle::factory()->create([
        'sku' => substr($productInsert->sku, 0, 4),
        'type' => 'Insert',
        'name' => 'INSE',
    ]);
    $saleOrderInsert = SaleOrderItem::factory()
        ->afterCreating(function (SaleOrderItem $orderItem) {
            $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
            for ($i = 0; $i < $barcodeCount; $i++) {
                $orderItem->barcodes()->create([
                    'order_id' => $orderItem->order_id,
                    'label_id' => uniqid('071722-SJ-M-9999-'),
                    'is_deleted' => 0,
                    'warehouse_id' => $orderItem->warehouse_id,
                ]);
            }
        })
        ->create([
            'order_id' => $this->saleOrder->id,
            'quantity' => 1,
            'product_id' => $productInsert->id,
            'product_sku' => $productInsert->sku,
            'product_style_sku' => substr($productInsert->sku, 0, 4),
            'product_size_sku' => substr($productInsert->sku, -3),
            'warehouse_id' => $this->warehouse->id
        ]);
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items->first()->barcodes->first()->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    $dataResult = ['data' => [
        [
            'order_item_id' => $saleOrderInsert->id,
            'total_verify' => 1,
        ],
        [
            'order_item_id' => $this->saleOrder->items->first()->id,
            'total_verify' => 1,
        ]
    ],
        'message' => 'Scan label success',
        'code' => 'label_insert',
    ];
    expect($res)->toMatchArray($dataResult);

    $shipmentItem = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $this->saleOrder->barcodeItems[0]->order_item_id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', 1)
        ->first();
    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $this->saleOrder->barcodeItems[0]->order_item_id,
        'shipment_id' => $this->params['shipment_id']]);
    $this->assertDatabaseHas('shipment_item_label', ['label_id' => $this->params['label_id'],
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItem->id,
    ]);
    $shipmentItemInsert = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $saleOrderInsert->id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', 1)
        ->first();
    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $saleOrderInsert->id,
        'shipment_id' => $this->params['shipment_id'],
        'quantity' => 1,
    ]);
    $this->assertDatabaseHas('shipment_item_label', ['label_id' => $saleOrderInsert->barcodes->first()->label_id,
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItemInsert->id,
    ]);
});

//Th đã có shipment item - label add new - order insert printing
test('scan label has shipment item - label scan success - order insert printing', function () {
    $this->params['order_id'] = $this->saleOrder->id;
    $this->params['label_id'] = $this->saleOrder->items[1]->barcodes[0]->label_id;
    $this->params['shipment_id'] = $this->saleOrder->shipment[0]->id;
    $this->params['employee_id'] = $this->employee->id;
    $this->params['id_time_checking'] = $this->timeTracking->id;

    $productInsert = Product::factory()->create([
        'sku' => 'UNPT9C0XS',
        'style' => 'INSE',
        'size' => '4x6'
    ]);
    $this->productStyle = ProductStyle::factory()->create([
        'sku' => substr($productInsert->sku, 0, 4),
        'type' => 'Insert',
        'name' => 'INSE',
    ]);
    $saleOrderInsert = SaleOrderItem::factory()
        ->afterCreating(function (SaleOrderItem $orderItem) {
            $barcodeCount = $orderItem->quantity === 1 ? 1 : 2;
            for ($i = 0; $i < $barcodeCount; $i++) {
                $orderItem->barcodes()->create([
                    'order_id' => $orderItem->order_id,
                    'label_id' => uniqid('071722-SJ-M-9999-'),
                    'is_deleted' => 0,
                    'warehouse_id' => $orderItem->warehouse_id,
                ]);
            }
        })
        ->create([
            'order_id' => $this->saleOrder->id,
            'quantity' => 1,
            'product_id' => $productInsert->id,
            'product_sku' => $productInsert->sku,
            'product_style_sku' => substr($productInsert->sku, 0, 4),
            'product_size_sku' => substr($productInsert->sku, -3),
            'warehouse_id' => $this->warehouse->id
        ]);
    $shipmenIttem = ShipmentItem::factory()
        ->has(ShipmentItemLabel::factory()->count(1)->state(
            new Sequence(
                [
                    'label_id' => $this->saleOrder->items[1]->barcodes[1]->label_id,
                    'shipment_id' => $this->saleOrder->shipment[0]->id,
                ],
            )), 'shipmentItemLabels')
        ->create(
            [
                'shipment_id' => $this->saleOrder->shipment[0]->id,
                'order_id' => $this->saleOrder->id,
                'quantity' => 1,
                'order_item_id' => $this->saleOrder->items[1]->id,
            ],
        );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataResult = ['data' => [
        [
            'order_item_id' => $saleOrderInsert->id,
            'total_verify' => 1,
        ],
        [
            'order_item_id' => $this->saleOrder->items[1]->id,
            'total_verify' => 1,
        ]
    ],
        'message' => 'Scan label success',
        'code' => 'label_success',
    ];
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray($dataResult);
    $shipmentItem = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $this->saleOrder->items[1]->id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', $shipmenIttem->quantity + 1)
        ->first();
    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $this->saleOrder->items[1]->barcodes[1]->order_item_id,
        'shipment_id' => $this->params['shipment_id']]);
    $this->assertDatabaseHas('shipment_item_label', [
        'label_id' => $this->params['label_id'],
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItem->id,
    ]);

    $shipmentItemInsert = ShipmentItem::where('order_id', $this->params['order_id'])
        ->where('order_item_id', $saleOrderInsert->id)
        ->where('shipment_id', $this->params['shipment_id'])
        ->where('quantity', 1)
        ->first();
    $this->assertDatabaseHas('shipment_item', ['order_id' => $this->params['order_id'],
        'order_item_id' => $saleOrderInsert->id,
        'shipment_id' => $this->params['shipment_id'],
        'quantity' => 1,
    ]);
    $this->assertDatabaseHas('shipment_item_label', ['label_id' => $saleOrderInsert->barcodes->first()->label_id,
        'shipment_id' => $this->params['shipment_id'],
        'employee_id' => $this->params['employee_id'],
        'shipment_item_id' => $shipmentItemInsert->id,
    ]);
});
