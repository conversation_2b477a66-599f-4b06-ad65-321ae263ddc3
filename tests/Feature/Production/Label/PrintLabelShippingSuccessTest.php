<?php

use App\Models\Employee;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemBarcode;
use App\Models\Shipment;
use App\Models\TimeTracking;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/label/printed-success';
    $this->saleOrder = SaleOrder::factory()
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-9999-1',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'label_id' => '071722-SJ-M-9999-2',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ],
        )), 'barcodeItems')
        ->for(Shipment::factory(), 'shipmentDefault')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'shipping_method' => 'standard',
            'order_status' => 'in_production'
        ]);
    $this->saleOrder->shipmentDefault->order_id = $this->saleOrder->id;
    $this->saleOrder->shipmentDefault->save();
    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0,
    ]);
    $this->timeTracking = TimeTracking::factory()->create();
    $this->params = [
        'shipmentId' => $this->saleOrder->shipmentDefault->id,
        'id_time_checking' => $this->timeTracking->id,
        'employeeId' => $this->employee->id,
    ];
});
test('validate input', function () {
    $this->params = [
        'shipmentId' => '',
        'id_time_checking' => '',
        'employeeId' => '',
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'shipmentId' => ['The shipment id field is required.'],
        'employeeId' => ['The employee id field is required.'],
        'id_time_checking' => ['The id time checking field is required.']
    ]);
    $this->params = [
        'shipmentId' => $this->saleOrder->shipmentDefault->id,
        'id_time_checking' => 9999,
        'employeeId' => 9999,
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'id_time_checking' => ['The selected id time checking is invalid.'],
    ]);
});
test('employee and shipment not found', function () {
    $this->params = [
        'shipmentId' => $this->saleOrder->shipmentDefault->id,
        'id_time_checking' => $this->timeTracking->id,
        'employeeId' => 9999,
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Log printed label shipment error',
    ]);
    $this->params = [
        'shipmentId' => 99999,
        'id_time_checking' => $this->timeTracking->id,
        'employeeId' => $this->employee->id
    ];
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Log printed label shipment error',
    ]);
});
test('print success', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('time_tracking', ['id' => $this->params['id_time_checking'], 'quantity' => 1]);
    $this->assertDatabaseHas('sale_order_item_barcode', ['order_id' => $this->saleOrder->id, 'employee_ship_id' => $this->params['employeeId']]);
    $this->assertDatabaseHas('sale_order_history', ['order_id' => $this->saleOrder->id, 'employee_id' => $this->params['employeeId'], 'type' => SaleOrderHistory::PRINT_LABEL, 'message' => 'Print label']);
    $this->assertDatabaseHas('sale_order', ['id' => $this->saleOrder->id, 'order_status' => SaleOrder::SHIPPED]);
});
test('print success - order type = 5', function () {
    $this->saleOrder->order_type = SaleOrder::ORDER_TYPE_LABEL_ORDER;
    $this->saleOrder->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('time_tracking', ['id' => $this->params['id_time_checking'], 'quantity' => 1]);
    $this->assertDatabaseHas('sale_order_item_barcode', ['order_id' => $this->saleOrder->id, 'employee_ship_id' => $this->params['employeeId']]);
    $this->assertDatabaseHas('sale_order_history', ['order_id' => $this->saleOrder->id, 'employee_id' => $this->params['employeeId'], 'type' => SaleOrderHistory::PRINT_LABEL, 'message' => 'Print label']);
    $this->assertDatabaseHas('sale_order', ['id' => $this->saleOrder->id, 'order_status' => SaleOrder::SHIPPED, 'order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER]);
});

test('print success - for order reroute', function () {
    SaleOrderHistory::factory()->create([
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::IMPORT_REROUTE
    ]);
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::IN_PRODUCTION]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    $this->assertDatabaseHas('time_tracking', ['id' => $this->params['id_time_checking'], 'quantity' => 1]);
    $this->assertDatabaseHas('sale_order_item_barcode', ['order_id' => $this->saleOrder->id, 'employee_ship_id' => $this->params['employeeId']]);
    $this->assertDatabaseHas('sale_order_history', ['order_id' => $this->saleOrder->id, 'employee_id' => $this->params['employeeId'], 'type' => SaleOrderHistory::PRINT_LABEL, 'message' => 'Print label']);
    $this->assertDatabaseHas('sale_order', ['order_status' => SaleOrder::SHIPPED, 'id' => $this->saleOrder->id]);
});
