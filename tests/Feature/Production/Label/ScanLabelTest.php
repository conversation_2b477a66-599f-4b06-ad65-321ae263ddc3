<?php

use App\Models\Product;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\ProductTypeWeight;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderInsert;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentItem;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierEasypost;
use App\Models\ShippingCarrierPackage;
use App\Models\ShippingCarrierService;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Models\StoreAddress;
use App\Models\StoreProductWeight;
use App\Models\User;
use App\Models\WeightCubic;
use App\Repositories\InsertPrintingRepository;
use App\Repositories\SaleOrderInsertRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery\MockInterface;

uses(RefreshDatabase::class);
beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = 'api/label?';
    $this->params = [
        'label_id' => ''
    ];
    $this->weightCubic = WeightCubic::factory()->createMany([
        [
            'weight_start' => 16.00,
            'weight_end' => 40,
            'cubic' => '15x11x1',
            'name_cubic' => '10',
        ],
        [
            'weight_start' => 40.00,
            'weight_end' => 48,
            'cubic' => '7x5x14',
            'name_cubic' => '30',
        ],
        [
            'weight_start' => 48.00,
            'weight_end' => 80,
            'cubic' => '12x3x15',
            'name_cubic' => '40',
        ]
    ]);
    $this->product = Product::factory()->createMany([
        [
            'sku' => 'UNPT9C0XS',
            'style' => 'Tee',
            'weight_single' => 5,
            'weight_multiple' => 10,
        ],
        [
            'sku' => 'UNPT2H00M',
            'style' => 'Tee',
            'weight_single' => 5,
            'weight_multiple' => 10,
        ]
    ]);
    $this->user = User::factory()->create();
    $this->shippingCarrier = ShippingCarrier::factory()->createMany([
        [
            'name' => 'USPS',
            'code' => 'USPS',
            'is_deleted' => 0,
            'tracking_url' => 'USPS_trackingNumber={tracking_code}'
        ],
        [
            'name' => 'Asendia USA',
            'code' => 'AsendiaUsa',
            'is_deleted' => 0,
            'tracking_url' => 'Asendia_trackingNumber={tracking_code}'
        ],
        [
            'name' => 'DHL',
            'code' => 'DhlEcs',
            'is_deleted' => 0,
            'tracking_url' => 'DHL_trackingNumber={tracking_code}'
        ]
    ]);
    $this->saleOrder = SaleOrder::factory()
        ->for(SaleOrderAccount::factory(), 'account')
        ->for(Store::factory()
            ->has(StoreAddress::factory()->count(2)->state(new Sequence(
                [
                    'type_address' => StoreAddress::TYPE_ADDRESS_RETURN
                ],
                [
                    'type_address' => StoreAddress::TYPE_ADDRESS_TO
                ],
            )), 'storeAddress'), 'store')
        ->has(SaleOrderAddress::factory()->count(2)->state(new Sequence(
            [
                'type_address' => SaleOrderAddress::TO_ADDRESS
            ],
            [
                'type_address' => SaleOrderAddress::RETURN_ADDRESS
            ],
        )), 'addressSaleOrder')
        ->has(SaleOrderHistory::factory([
            'type' => 'Create Label',
            'user_id' => $this->user->id
        ]), 'timeline')
        ->has(Shipment::factory()->count(2)->state(new Sequence(
            [
                'tracking_number' => *********,
                'carrier_code' => $this->shippingCarrier[0]->code
            ],
            [
                'tracking_number' => 987654321,
                'carrier_code' => $this->shippingCarrier[1]->code
            ],
        )), 'shipment')
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-9999-1',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'label_id' => '071722-SJ-M-9999-2',
                'is_deleted' => 0,
                'reprint_status' => 0,
                'warehouse_id' => $this->warehouse->id,
            ],
        )), 'barcodeItems')
        ->create([
            'order_number' => '071722-SJ-M-9999',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => 'new_order',
            'shipping_method' => 'standard',
            'order_quantity' => 2,
            'shipment_id' => null,
        ]);
    $dataMockSaleOrderItem = [];
    $dataMockProductTypeWeight = [];
    $dataMockProductStyle = [];
    foreach ($this->product as $key => $item) {
        $dataMockSaleOrderItem[] = [
            'order_id' => $this->saleOrder->id,
            'quantity' => 1,
            'product_id' => $item->id,
            'product_sku' => $item->sku,
            'product_style_sku' => substr($item->sku, 0, 4),
            'product_size_sku' => substr($item->sku, -3),
            'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/2st.s3.us-east-2.amazonaws.com\/Design\/PancreaticCancerAwarenessUnisexTshirtInThisFamilyNobodyFightsAloneSurvivorFighterGiftAwarenessMonth.png"},{"name":"PreviewFiles.Front","value":"https:\/\/i.etsystatic.com\/24906590\/r\/il\/2341c6\/3145734374\/il_fullxfull.3145734374_gcm9.jpg"}]'
        ];
        $dataMockProductStyle[] = [
            'sku' => substr($item->sku, 0, 4),
            'type' => $key > 1 ? 'Fleece' : 'Tee',
        ];
        $dataMockProductTypeWeight[] = [
            'name' => $key > 1 ? 'Fleece' : 'Tee',
            'size' => trim(str_replace('0', '', substr($item->sku, -3))),
            'weight' => $key > 1 ? 8 : 4,
            'weight_unit' => 'oz',
            'weight_oz' => $key > 1 ? 8 : 4,
        ];
    }
    $dataMockProductTypeWeightAny = [
        [
            'name' => 'Fleece',
            'size' => 'any',
            'weight' => 8,
            'weight_unit' => 'oz',
            'weight_oz' => 8,
        ],
        [
            'name' => 'Tee',
            'size' => 'any',
            'weight' => 4,
            'weight_unit' => 'oz',
            'weight_oz' => 4,
        ]
    ];
    $dataMockProductTypeWeight = array_merge($dataMockProductTypeWeight, $dataMockProductTypeWeightAny);
    $this->saleOrderItem = SaleOrderItem::factory()->createMany($dataMockSaleOrderItem);
    $this->productStyle = ProductStyle::factory()->createMany($dataMockProductStyle);
    $this->productTypeWeight = ProductTypeWeight::factory()->createMany($dataMockProductTypeWeight);
    $this->productSize = ProductSize::factory()->createMany([
        [
            'name' => 'XS',
            'sku' => '0XS',
        ],
        [
            'name' => 'M',
            'sku' => '00M',
        ],
    ]);
    $dataMockShippingCarrierEasypost = [];
    $datMockShippingPredefinedPackage = [];
    foreach ($this->shippingCarrier as $key => $item) {
        $dataMockShippingCarrierEasypost[] = [
            'store_id' => $this->saleOrder->store->id,
            'carrier_id' => $item->id,
            'warehouse_id' => $this->warehouse->id,
            'carrier_account' => Str::random(10)
        ];
        $datMockShippingPredefinedPackage[] = [
            'name' => 'Package',
            'predefined_package' => 'Package',
            'is_default' => $item->id == 1 ? 1 : 0,
            'carrier_id' => $item->id,
        ];
    }
    $this->shippingCarrierAccount = ShippingCarrierEasypost::factory()->createMany($dataMockShippingCarrierEasypost);
    $this->shippingCarrierPackage = ShippingCarrierPackage::factory()->createMany($datMockShippingPredefinedPackage);
});
// Invalid input
test('invalid input - error 422', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'label_id' => ['The label id field is required.'],
    ]);
});
//not found order
test('Order not found', function () {
    //barcode not map with sale order
    $barcodeNotMapWarehouse = SaleOrderItemBarcode::factory([
        'label_id' => '071722-SJ-M-200-1',
        'is_deleted' => 0
    ])->create();
    $this->params['label_id'] = $barcodeNotMapWarehouse->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Order not found!',
    ]);
    $saleOrder = SaleOrder::factory([
        'order_number' => '071722-SJ-M-000368',
        'warehouse_id' => $this->warehouse->id,
    ])
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-000368-1',
                'is_deleted' => 0
            ],
            [
                'label_id' => '071722-SJ-M-000368-2',
                'is_deleted' => 1
            ],
        )), 'barcodeItems')
        ->create();
    // order in warehouse other
    $this->params['label_id'] = $saleOrder->barcodeItems[0]->label_id;
    SaleOrder::find($saleOrder->id)->update(['warehouse_id' => 9999]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Order not found!',
    ]);
    // label deleted
    $this->params['label_id'] = $saleOrder->barcodeItems[1]->label_id;
    SaleOrder::find($saleOrder->id)->update(['warehouse_id' => $this->warehouse->id]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Order not found!',
    ]);
});
//order in [draft, on_hold, cancelled, rejected]
test('order in draft, on_hold, cancelled, rejected', function () {
    $saleOrder = SaleOrder::factory([
        'order_number' => '071722-SJ-M-000368',
        'warehouse_id' => $this->warehouse->id,
        'order_status' => 'draft'
    ])
        ->has(SaleOrderItemBarcode::factory()->count(2)->state(new Sequence(
            [
                'label_id' => '071722-SJ-M-000368-1',
                'is_deleted' => 0
            ],
            [
                'label_id' => '071722-SJ-M-000368-2',
                'is_deleted' => 1
            ],
        )), 'barcodeItems')
        ->create();
    $this->params['label_id'] = $saleOrder->barcodeItems[0]->label_id;
    //draft
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Sale order is draft',
    ]);
    //on_hold
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'on_hold']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Sale order is on hold',
    ]);
    //cancelled
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'cancelled']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Sale order is cancelled',
    ]);
    //rejected
    SaleOrder::find($saleOrder->id)->update(['order_status' => 'rejected']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Sale order is rejected',
    ]);
});
// testcase - sale order không có địa chỉ return - 200 - output trả ra địa chỉ return là return address warehouse
test('get data success', function () {
    SaleOrderAddress::query()->delete();
    StoreAddress::query()->delete();
    SaleOrderAddress::factory()->create(
        [
            'order_id' => $this->saleOrder->id,
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'name' => 'Jennifer Castro',
            'email' => '<EMAIL>',
            'company' => 'datpt',
            'phone' => '2533069676',
            'street1' => '12818 120th Ave E	',
            'street2' => '',
            'city' => 'Puyallup',
            'state' => 'WA',
            'zip' => '98374-3693',
            'country' => 'US',
            'residential' => '0',
            'verified_status' => '1',
        ],
    );
    setTimezone('America/Los_Angeles');
    $storeAddress = StoreAddress::factory()->create([
        'store_id' => $this->saleOrder->store->id,
        'type_address' => StoreAddress::TYPE_ADDRESS_RETURN,
        'name' => 'FULFILLMENT CENTER'
    ]);
    $dataMockResult = $storeAddress->toArray();
    $dataMockResult['type_address'] = 'return_address';
    $dataMockResult['default'] = true;
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));

    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['address_sale_order'][1])->toMatchArray($dataMockResult);
});
// testcase - sale order không có địa chỉ return - 200 - output trả ra địa chỉ return là return address warehouse
test('Test scan label ID: sale order has not address return - SUCCESS - Output address warehouse', function () {
    SaleOrderAddress::query()->delete();
    StoreAddress::query()->delete();
    setTimezone('America/Los_Angeles');
    $this->saleOrderAddress = SaleOrderAddress::factory()->create(
        [
            'order_id' => $this->saleOrder->id,
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'name' => 'Jennifer Castro',
            'email' => '<EMAIL>',
            'company' => 'datpt',
            'phone' => '2533069676',
            'street1' => '12818 120th Ave E	',
            'street2' => '',
            'city' => 'Puyallup',
            'state' => 'WA',
            'zip' => '98374-3693',
            'country' => 'US',
            'residential' => '0',
            'verified_status' => '1',
        ],
    );
    $this->storeAddress = StoreAddress::factory()->create([
        'store_id' => $this->saleOrder->store->id,
        'type_address' => StoreAddress::TYPE_ADDRESS_RETURN,
        'name' => 'FULFILLMENT CENTER'
    ]);
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataMockResult = $this->storeAddress->toArray();
    $dataMockResult['default'] = true;
    $dataMockResult['type_address'] = 'return_address';
    $dataMockResult['default'] = true;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['address_sale_order'][1])->toMatchArray($dataMockResult);
});
// testcase - sale order chưa có shipment nào - 200
test('Test scan label ID: sale order not have shipment - SUCCESS - Output not have shipment', function () {
    Shipment::query()->delete();
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipment'])->toMatchArray([]);
});
// testcase - sale order có shipment ( ghép URL tracking khi có shipment) - 200
test('Test scan label ID: sale order have shipment - SUCCESS - Output have shipment', function () {
    $dataResultMockAsen = 'Asendia_trackingNumber=' . $this->saleOrder->shipment->where('carrier_code', $this->shippingCarrier[1]->code)->pluck('tracking_number')->first();
    $dataResultMockUSPS = 'USPS_trackingNumber=' . $this->saleOrder->shipment->where('carrier_code', $this->shippingCarrier[0]->code)->pluck('tracking_number')->first();
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipment'][0]['url_tracking_easypost'])->toEqual($dataResultMockAsen);
    expect(json_decode($response->getContent(), true)['shipment'][1]['url_tracking_easypost'])->toEqual($dataResultMockUSPS);
});
// testcase - sale order có store có khai báo shipping carrier với easypost - 200
test('Test scan label ID: sale order have carrier easypost - SUCCESS - Output ', function () {
    $accountEasypostMock = ShippingCarrierEasypost::where('store_id', $this->saleOrder->store->id)->pluck('carrier_account')->toArray();
    $this->saleOrder->store->easypost_api_key = '***********';
    $this->saleOrder->store->save();
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $accountEasypostResponse = [];
    foreach (json_decode($response->getContent(), true)['shipping_carrier_easypost'] as $item) {
        $accountEasypostResponse[] = isset($item['carrier_account']) ? $item['carrier_account'] : null;
    }
    expect($accountEasypostResponse)->toEqualCanonicalizing($accountEasypostMock);
});
// testcase - sale order có store không khai báo shipping carrier với easypost - 200
test('Test scan label ID: sale order have not carrier easypost  - SUCCESS - Output ', function () {
    ShippingCarrierEasypost::query()->delete();
    $accountEasypostDefault = ShippingCarrierEasypost::query()->create([
        'store_id' => 1,
        'carrier_id' => $this->shippingCarrier[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'carrier_account' => Str::random(10)
    ]);
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipping_carrier_easypost'][0]['carrier_account'])->toEqual($accountEasypostDefault->carrier_account);
});
// testcase - sale order co tinh duoc khoi luong
test('Test scan label ID: sale order has weight > 0  - SUCCESS - Output ', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['estimate_dimension']['weight'])->toEqual(20);
});
// testcase - Not found data product
test('Test scan label ID: Not found data product  - SUCCESS - Output ', function () {
    Product::query()->delete();
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true)['message'])->toEqual('Not found data product.');
});

// testcase - product not weight
test('Test scan label ID: product not weight  - SUCCESS - Output ', function () {
    $this->product->first()->weight_single = null;
    $this->product->first()->weight_multiple = null;
    $this->product->first()->save();
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true)['message'])->toEqual('Weight not defined for product SKU UNPT9C0XS (Tee XS) for multiple item orders.');
});

//testcase - has shipment and verify item
test('Test scan label ID: has shipment and verify item', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $shipmentItem = ShipmentItem::factory()->create([
        'quantity' => 1,
        'shipment_id' => $this->saleOrder->shipment[0]->id,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $shipmentItemLabel = \App\Models\ShipmentItemLabel::factory()->create([
        'shipment_item_id' => $shipmentItem->id,
        'employee_id' => 111,
        'shipment_id' => $this->saleOrder->shipment[0]->id,
        'label_id' => $this->saleOrder->barcodeItems[0]->label_id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['total_verify'])->toEqual(1);
});
//testcase - calculator total quantity of order
test('test case label ID: calculator total quantity of order', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $total = $this->saleOrderItem->sum('quantity');
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['total'])->toEqual($total);
});
//testcase - show shipping method not in standard, express
test('show shipping method not in standard, express', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => 'storeMethodOfOrder']);
    $shippingCarrierService = ShippingCarrierService::factory()->create([
        'display_name' => 'tinhYeuMauNang',
        'carrier_id' => $this->shippingCarrier[0]->id,
    ]);
    ShippingMethod::factory()->create(
        [
            'api_shipping_method' => 'storeMethodOfOrder',
            'name' => 'storeMethod',
            'store_id' => $this->saleOrder->store_id,
            'carrier_id' => $this->shippingCarrier[0]->id,
            'shipping_carrier_service_id' => $shippingCarrierService->id,
        ],
    );
    $dataShippingMethodMock = $this->shippingCarrier[0]->name . ' - ' . $shippingCarrierService->display_name;
    ShipmentItem::factory()->create([
        'quantity' => 1,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipping_method'])->toEqual($dataShippingMethodMock);
});
test('order has insert - not product sku insert', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataResponse = [
        'status' => true,
        'url' => 'https://images-merch.printify.com/branding-insert/b58e3312-469e-4386-ac88-9724bf4eae7d/print/file.pdf',
    ];
    $this->mock(SaleOrderInsertRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldReceive('generateOrderInsert')->andReturn($dataResponse);
    })->makePartial();
    $this->mock(SaleOrderInsertRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldReceive('generateOrderInsertGiftMessage')->andReturn($dataResponse);
    })->makePartial();

    $saleOrderInsert = SaleOrderInsert::factory()->createMany([
        [
            'order_id' => $this->saleOrder->id,
            'url' => $dataResponse['url'],
            'type' => SaleOrderInsert::PACKING_SLIP_TYPE,
            'size' => '3x4',
        ],
        [
            'order_id' => $this->saleOrder->id,
            'url' => $dataResponse['url'],
            'type' => SaleOrderInsert::PACKING_SLIP_TYPE,
            'size' => '3x4',
        ],
        [
            'order_id' => 999999,
            'url' => $dataResponse['url'],
            'type' => SaleOrderInsert::PACKING_SLIP_TYPE,
            'size' => '3x4',
        ],
        [
            'order_id' => $this->saleOrder->id,
            'url' => null,
            'type' => SaleOrderInsert::GIFT_MESSAGE_TYPE,
            'size' => '3x4',
            'message' => 'Hello world!'
        ]
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    $dataCountInsert = count($res['order_insert']);
    $dataMock = $saleOrderInsert->where('order_id', $this->saleOrder->id)->count();
    expect($dataCountInsert)->toEqual($dataMock);
});
test('order has insert - has product sku insert', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;

    $product = Product::factory()->createMany([
        [
            'style' => 'INTU',
            'sku' => 'INTU-1',
            'size' => '4x6'
        ],
        [
            'style' => 'INPS',
            'sku' => 'INPS-1',
            'size' => '4x6'
        ]
    ]);

    ProductStyle::factory()->createMany([
        [
            'name' => $product[0]->style,
            'type' => ProductStyle::TYPE_INSERT,
            'sku' => $product[0]->style,
        ],
        [
            'name' => $product[1]->style,
            'type' => ProductStyle::TYPE_INSERT,
            'sku' => $product[1]->style,
        ],
    ]);

    $saleOrderItemImageInsert = SaleOrderItemImage::factory()->createMany([
        [
            'order_id' => $this->saleOrder->id,
            'product_sku' => $product[0]->sku,
            'image_url' => 'than_thoai'
        ],
        [
            'order_id' => $this->saleOrder->id,
            'product_sku' => $product[1]->sku,
            'image_url' => 'ban_tinh_ca_mua_dong',
        ],
        [
            'order_id' => 909090,
            'product_sku' => $product[1]->sku,
            'image_url' => 'ban_tinh_ca_mua_dong',
        ],
        [
            'order_id' => 919191,
            'product_sku' => $product[1]->sku,
            'image_url' => 'ban_tinh_ca_mua_dong',
        ],
    ]);
    $dataResponse = [
        'status' => true,
        'url' => 'https://images-merch.printify.com/branding-insert/b58e3312-469e-4386-ac88-9724bf4eae7d/print/file.pdf',
    ];

    $this->mock(InsertPrintingRepository::class, function (MockInterface $mock) use ($dataResponse) {
        $mock->shouldReceive('generateOrderInsert')->andReturn($dataResponse);
    })->makePartial();

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    expect(count($res['order_inserts']))->toEqual($saleOrderItemImageInsert->where('order_id', $this->saleOrder->id)->count());
});
//testcase - show shipping method not in standard, express and order has not store api key
test('show shipping method not in standard, express and order has not store api key', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => 'storeMethodOfOrder']);

    $shippingCarrierService = ShippingCarrierService::factory()->create([
        'display_name' => 'tinhYeuMauNang',
        'carrier_id' => $this->shippingCarrier[0]->id,
    ]);
    $shippingMethod = ShippingMethod::factory()->create(
        [
            'api_shipping_method' => 'storeMethodOfOrder',
            'name' => 'storeMethod',
            'store_id' => $this->saleOrder->store_id,
            'carrier_id' => $this->shippingCarrier[0]->id,
            'shipping_carrier_service_id' => $shippingCarrierService->id,
        ],
    );
    $shippingCarrierEasypost = ShippingCarrierEasypost::factory()->create([
        'carrier_id' => $this->shippingCarrier[0]->id,
        'name' => $shippingMethod->name,
        'warehouse_id' => $this->saleOrder->warehouse_id,
        'store_id' => $this->saleOrder->store_id,
    ]);
    $dataShippingMethodMock = $this->shippingCarrier[0]->name . ' - ' . $shippingCarrierService->display_name;
    ShipmentItem::factory()->create([
        'quantity' => 1,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipping_carrier_easypost'])->toHaveCount(1);
});
//testcase - show shipping method not in standard, express and order has store api key
test('show shipping method not in standard, express and order has store api key', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => 'storeMethodOfOrder']);
    $this->saleOrder->store->easypost_api_key = 'EZTK-*********';
    $this->saleOrder->store->save();

    $shippingCarrierService = ShippingCarrierService::factory()->create([
        'display_name' => 'tinhYeuMauNang',
        'carrier_id' => $this->shippingCarrier[0]->id,
    ]);
    ShippingMethod::factory()->create(
        [
            'api_shipping_method' => 'storeMethodOfOrder',
            'name' => 'storeMethod',
            'store_id' => $this->saleOrder->store_id,
            'carrier_id' => $this->shippingCarrier[0]->id,
            'shipping_carrier_service_id' => $shippingCarrierService->id,
        ],
    );
    ShipmentItem::factory()->create([
        'quantity' => 1,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipping_carrier_easypost'])->toHaveCount(1);
});
test('show shipping method not in standard, express and order has store api key but not shipping method', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => 'storeMethodOfOrder']);
    $this->saleOrder->store->easypost_api_key = 'EZTK-*********';
    $this->saleOrder->store->save();

    ShipmentItem::factory()->create([
        'quantity' => 1,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['shipping_carrier_easypost'])->toHaveCount(3);
});
//testcase - show carrier for get rate
test('show carrier for get rate - Domestic', function () {
    // carrier rate USPS
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'US';
    $dataSaleOrderAddress->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_DOMESTIC);
});

test('show carrier for get rate - international', function () {
    // carrier rate AsendiaUsa
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'VN';
    $dataSaleOrderAddress->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_INTERNATIONAL);
});

test('show carrier for get rate - international - hard goods', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'VN';
    $dataSaleOrderAddress->save();
    ProductType::factory()->create([
        'name' => 'Tee',
        'is_hard_goods' => 1
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_INTERNATIONAL);
});

test('show carrier for get rate - Domestic - hard goods - < 16oz', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $this->product->map(function ($product) {
        $product->weight_multiple = 2;
        $product->save();
    });
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'US';
    $dataSaleOrderAddress->save();
    ProductType::factory()->create([
        'name' => 'Tee',
        'is_hard_goods' => 1
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_DOMESTIC);
});

test('show carrier for get rate - Domestic - hard goods - >= 16oz', function () {
    foreach ($this->productTypeWeight as $productType) {
        $productType->weight_oz = 20;
        $productType->save();
    }
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'US';
    $dataSaleOrderAddress->save();
    ProductType::factory()->create([
        'name' => 'Tee',
        'is_hard_goods' => 1
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_HARD_GOOD_DOMESTIC);
});

test('show carrier for get rate with shipping method not in standard, express', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => 'storeMethodOfOrder']);
    $this->saleOrder->store->easypost_api_key = 'EZTK-*********';
    $this->saleOrder->store->save();

    $shippingCarrierService = ShippingCarrierService::factory()->create([
        'display_name' => 'tinhYeuMauNang',
        'carrier_id' => $this->shippingCarrier[0]->id,
    ]);
    ShippingMethod::factory()->create(
        [
            'api_shipping_method' => 'storeMethodOfOrder',
            'name' => 'storeMethod',
            'store_id' => $this->saleOrder->store_id,
            'carrier_id' => $this->shippingCarrier[0]->id,
            'shipping_carrier_service_id' => $shippingCarrierService->id,
        ],
    );
    ShipmentItem::factory()->create([
        'quantity' => 1,
        'order_item_id' => $this->saleOrderItem[0]->id
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual($this->shippingCarrier[0]->code);
});

test('show carrier for get rate - international - hard goods - store use USPS', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'VN';
    $dataSaleOrderAddress->save();
    $setting = Setting::factory()->create([
        'name' => Setting::STORE_HARD_GOODS_SHIP_USPS,
        'label' => Setting::STORE_HARD_GOODS_SHIP_USPS,
        'value' => $this->saleOrder->store->id
    ]);
    ProductType::factory()->create([
        'name' => 'Tee',
        'is_hard_goods' => 1
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_INTERNATIONAL);
});

test('show carrier for get rate - Domestic - hard goods - - store use USPS', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $dataSaleOrderAddress = $this->saleOrder->addressSaleOrder->where('type_address', SaleOrderAddress::TO_ADDRESS)->first();
    $dataSaleOrderAddress->country = 'US';
    $dataSaleOrderAddress->save();
    Setting::factory()->create([
        'name' => Setting::STORE_HARD_GOODS_SHIP_USPS,
        'label' => Setting::STORE_HARD_GOODS_SHIP_USPS,
        'value' => $this->saleOrder->store->id
    ]);
    ProductType::factory()->create([
        'name' => 'Tee',
        'is_hard_goods' => 1
    ]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['carrier_get_rate'])->toEqual(\App\Repositories\LabelRepository::DEFAULT_CARRIER_DOMESTIC);
});

test('get data error - photo required', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update([
        'tag' => \App\Models\Tag::TAG_PHOTO_REQUIRED
    ]);

    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(404);
    $dataJson = json_decode($response->getContent(), true);
    expect($dataJson['message'])->toEqual('Order Requires Photo Upload.');
});

test('Test scan label ID: sale order has weight > 0  - SUCCESS for store has product sku weight - Output ', function () {
    $this->params['label_id'] = $this->saleOrder->barcodeItems[0]->label_id;
    $this->saleOrder->store_id = Store::PRINTIFY_API_ID;
    $this->saleOrder->save();
    StoreProductWeight::factory()->create(
        [
            'store_id' => Store::PRINTIFY_API_ID,
            'product_sku' => $this->product->first()->sku,
            'weight' => 50
        ],
    );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['estimate_dimension']['weight'])->toEqual(60);
});
