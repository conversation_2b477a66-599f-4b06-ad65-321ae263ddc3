<?php

use App\Models\ProductColor;
use App\Models\ProductSize;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\Shipment;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->store = Store::factory(['has_note' => 1])->create();
    $this->saleOrder = SaleOrder::factory([
        'external_number' => '41366-PW2144397-SPOD',
        'store_id' => $this->store->id
    ])->has(SaleOrderItem::factory([
        'name' => 'Unisex Jersey Tee US',
        'quantity' => 5,
        'product_color_sku' => '9G',
        'product_size_sku' => '00M',
        'store_id' => $this->store->id
    ]), 'items')
        ->has(Shipment::factory([
            'store_id' => $this->store->id,
            'provider' => 'api'
        ]), 'shipment')
        ->create();

    $this->productColor = ProductColor::factory([
        'sku' => '9G',
        'name' => 'HEATHER KELLY'
    ])->create();

    $this->productSize = ProductSize::factory([
        'sku' => '00M',
        'name' => 'M'
    ])->create();

    $this->storage = Storage::fake('s3');
    $this->pdf = UploadedFile::fake()->create("{$this->saleOrder->shipment->first()->id}.pdf");
    $this->storage->put("/label/{$this->saleOrder->shipment->first()->id}.pdf", $this->pdf);

    $this->endpoint = "api/label/download/{$this->saleOrder->shipment->first()->id}";
});

test('shipment not exist', function () {
    $endpoint = 'api/label/download/2000000';

    $response = $this->get($endpoint);

    $response->assertViewIs('label.404');
});

test('label generate by shipstation', function () {
    $shipment = Shipment::factory(['provider' => 'shipstation'])->create();
    $endpoint = "api/label/download/$shipment->id";

    $response = $this->get($endpoint);

    $response->assertViewIs('label.shipstation');
});

test('shipment voided', function () {
    $shipment = Shipment::factory([
        'provider' => 'api',
        'refund_status' => 'submitted',
        'carrier_code' => Shipment::CARRIER_CODE_ASENDIA,
        'label_url' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/unitest/label.png',
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id
    ])
        ->create();

    $endpoint = "api/label/download/$shipment->id";
    $response = $this->get($endpoint);

    $response->assertViewIs('label.voidShipment');
});

test('file already exists', function () {
    $response = $this->get($this->endpoint);

    $response->assertRedirect(env('AWS_URL') . "/label/{$this->saleOrder->shipment->first()->id}.pdf?v=" . time());
});

test('create new file and upload to s3', function () {
    $shipment = Shipment::factory([
        'provider' => 'api',
        'carrier_code' => Shipment::CARRIER_CODE_ASENDIA,
        'label_url' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/unitest/label.png',
        'store_id' => $this->store->id,
        'order_id' => $this->saleOrder->id
    ])
        ->create();

    $endpoint = "api/label/download/$shipment->id";
    $response = $this->get($endpoint);
    $response->assertRedirect(env('AWS_URL') . "/label/{$shipment->id}.pdf?v=" . time());
});
