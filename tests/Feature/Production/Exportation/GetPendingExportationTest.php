<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Exportation;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Faker\Factory as faker;
use App\Models\ExportationTracking;
use App\Models\Employee;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/shipment-exportation/pending?';
    $this->params = [
        'limit' => '10',
        'page' => '1',
        'tracking_number' => '',
        'start_date' => '',
        'end_date' => '',
        'staff_id' => '',
    ];
});

test('', function () {
    // not found tracking number
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    $dataExportation = Exportation::factory()->count(6)->state(new Sequence(
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'scanning',
        ],
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'scanning',
        ],
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'scanning',
        ],
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'scanning',
        ],
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'download',
        ],
        [
            'number_report' => faker::create()->words(1, true),
            'status' => 'download',
        ],
    ))
        ->has(ExportationTracking::factory()->count(3), 'shipmentExportationTrackings')
        ->for(Employee::factory(), 'employee')
        ->create();

    //get all Exportation
    $total = $dataExportation->where('status', Exportation::SCANNING)->count();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => $total]);

    //search exportation tracking_number nhung không thuoc exportation có url va da in
    $this->params['tracking_number'] = $dataExportation[5]->shipmentExportationTrackings[0]->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    //search Exportation tracking_number thoa man dk
    $this->params['tracking_number'] = $dataExportation[0]->shipmentExportationTrackings[0]->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 1]);

    //search Exportation by employee printed khong co thuoc record trong Exportation
    $this->params['tracking_number'] = '';
    $this->params['staff_id'] = 999;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);

    // search Exportation by employee printed co record
    $employee = Employee::factory()->create();
    $dataExportation[2]->employee_create_id =  $employee->id;
    $dataExportation[2]->save();
    $this->params['staff_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 1]);

    // search theo start va end date
    $this->params['tracking_number'] = '';
    $this->params['staff_id'] = '';
    $this->params['start_date'] = now()->subDays(3)->toDateTimeString();
    $this->params['end_date'] = now()->toDateTimeString();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => $total]);

    // search theo start > end
    $this->params['start_date'] = now()->addDay()->toDateTimeString();
    $this->params['end_date'] = now()->subDays(3)->toDateTimeString();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes)->toMatchArray(['total' => 0]);
});
