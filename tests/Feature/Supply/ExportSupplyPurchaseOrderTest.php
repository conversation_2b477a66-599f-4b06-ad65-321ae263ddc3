<?php

use App\Exports\SupplyPurchaseOrderExport;
use App\Models\Brand;
use App\Models\Supply;
use App\Models\SupplyCategory;
use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyUnit;
use App\Models\Vendor;
use App\Models\Warehouse;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::factory()->create();
    $this->brand = Brand::factory()->create(['name' => faker::create()->word()]);
    $this->vendor = Vendor::factory()->create();
    $this->supplyCategory = SupplyCategory::factory()->create(['name' => faker::create()->word()]);
    $this->supplyUnit = SupplyUnit::factory()->create(['name' => faker::create()->word()]);
    $this->po = SupplyPurchaseOrder::factory()->count(5)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'vendor_id' => $this->vendor->id,
            'po_number' => faker::create()->unique()->text(12),
            'order_number' => faker::create()->unique()->text(12),
            'invoice_number' => faker::create()->unique()->text(10),
            'order_date' => faker::create()->date(),
            'delivery_date' => faker::create()->date(),
            'payment_terms' => faker::create()->randomDigitNotNull(),
            'tracking_carrier' => faker::create()->randomDigitNotNull(),
            'tracking_number' => faker::create()->unique()->text(15),
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'vendor_id' => $this->vendor->id,
            'po_number' => faker::create()->unique()->text(12),
            'order_number' => faker::create()->unique()->text(12),
            'invoice_number' => faker::create()->unique()->text(10),
            'order_date' => faker::create()->date(),
            'delivery_date' => faker::create()->date(),
            'payment_terms' => faker::create()->randomDigitNotNull(),
            'tracking_carrier' => faker::create()->randomDigitNotNull(),
            'tracking_number' => faker::create()->unique()->text(15),
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'vendor_id' => $this->vendor->id,
            'po_number' => faker::create()->unique()->text(12),
            'order_number' => faker::create()->unique()->text(12),
            'invoice_number' => faker::create()->unique()->text(10),
            'order_date' => faker::create()->date(),
            'delivery_date' => faker::create()->date(),
            'payment_terms' => faker::create()->randomDigitNotNull(),
            'tracking_carrier' => faker::create()->randomDigitNotNull(),
            'tracking_number' => faker::create()->unique()->text(15),
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'vendor_id' => $this->vendor->id,
            'po_number' => faker::create()->unique()->text(12),
            'order_number' => faker::create()->unique()->text(12),
            'invoice_number' => faker::create()->unique()->text(10),
            'order_date' => faker::create()->date(),
            'delivery_date' => faker::create()->date(),
            'payment_terms' => faker::create()->randomDigitNotNull(),
            'tracking_carrier' => faker::create()->randomDigitNotNull(),
            'tracking_number' => faker::create()->unique()->text(15),
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'vendor_id' => $this->vendor->id,
            'po_number' => faker::create()->unique()->text(12),
            'order_number' => faker::create()->unique()->text(12),
            'invoice_number' => faker::create()->unique()->text(10),
            'order_date' => faker::create()->date(),
            'delivery_date' => faker::create()->date(),
            'payment_terms' => faker::create()->randomDigitNotNull(),
            'tracking_carrier' => faker::create()->randomDigitNotNull(),
            'tracking_number' => faker::create()->unique()->text(15),
        ],
    )
        ->has(SupplyPurchaseOrderItem::factory([
            'supply_id' => Supply::factory()->create(['sku' => faker::create()->unique()->text(5)]),
            'quantity' => faker::create()->randomDigitNotNull(),
            'price' => 0,
            'total' => 0,
        ]),
            'items')
        ->create();
    $this->params = [
        'warehouse_id' => $this->warehouse->id,
        'export' => true,
    ];
    $this->endpoint = '/api/supply-purchase-orders/export?';
});
test('export supply PO', function () {
    $orders = $this->po;
    $export = new SupplyPurchaseOrderExport($this->po, true);
    $collection = $export->collection();
    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(5, $collection);
    $headers = $export->headings();
    $this->assertEquals([
        'PO#', 'Order#', 'Invoice#', 'Order Date', 'Vendor',
        'Status', 'Tracking#', 'ETA', 'Sub Total', 'Other fees & charges', 'Tag', 'User'
    ], $headers);
    foreach ($collection as $i => $row) {
        $this->assertEquals($orders[$i]->po_number, $row['po_number']);
        $this->assertEquals($orders[$i]->order_number, $row['order_number']);
        $this->assertEquals($orders[$i]->invoice_number, $row['invoice_number']);
        $this->assertEquals($orders[$i]->order_date, $row['order_date']);
        $this->assertEquals($orders[$i]->vendor?->name ?? null, $row['vendor']);
        $this->assertEquals($orders[$i]->order_status, $row['order_status']);
        $this->assertEquals($orders[$i]->tracking_number, $row['tracking_number']);
        $this->assertEquals($orders[$i]->delivery_date, $row['delivery_date']);
        $this->assertEquals($orders[$i]->sub_total, $row['amount']);
        $this->assertEquals($orders[$i]->fee, $row['fee']);
        $this->assertEmpty($row['tag']);
        $this->assertNull($row['user']);
    }
});
