<?php

use App\Jobs\UpdateQuantitySupplyJob;
use App\Models\Employee;
use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyCategory;
use App\Models\SupplyInventory;
use App\Models\SupplyInventoryAddition;
use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyQuantity;
use App\Models\SupplyUnit;
use App\Models\TimeTracking;
use App\Models\Vendor;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Queue;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    config(['jwt.warehouse_id' => $this->warehouse->id]);
    $this->vendor = Vendor::factory()->create();
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->supplyCategory = SupplyCategory::factory()->create(['name' => faker::create()->word()]);
    $this->supplyUnit = SupplyUnit::factory()->create(['name' => faker::create()->word()]);
    $this->timeTracking = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => 'supply_addition',
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);
    $this->supply = Supply::factory()->count(2)->sequence(
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() . '001',
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ],
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() . '002',
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ],
    )->create();

    $this->supplyPo = SupplyPurchaseOrder::factory([
        'warehouse_id' => $this->warehouse->id,
        'vendor_id' => $this->vendor->id,
        'po_number' => faker::create()->unique()->text(12),
        'order_number' => faker::create()->unique()->text(12),
        'invoice_number' => faker::create()->unique()->text(10),
        'order_date' => faker::create()->date(),
        'delivery_date' => faker::create()->date(),
        'payment_terms' => faker::create()->randomDigitNotNull(),
        'tracking_carrier' => faker::create()->randomDigitNotNull(),
        'tracking_number' => faker::create()->unique()->text(15),
        'order_status' => SupplyPurchaseOrder::SHIPPED_STATUS,
    ])
        ->has(SupplyPurchaseOrderItem::factory()->count(2)->sequence(
            [
                'supply_id' => $this->supply[0]->id,
                'quantity' => 10,
                'price' => 0,
                'total' => 0,
            ],
            [
                'supply_id' => $this->supply[1]->id,
                'quantity' => 10,
                'price' => 0,
                'total' => 0,
            ],
        ),
            'items')
        ->create();

    $this->supplyQuantity = SupplyQuantity::factory()->count(2)->sequence(
        [
            'supply_id' => $this->supply[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'incoming_stock' => 10,
        ],
        [
            'supply_id' => $this->supply[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'incoming_stock' => 10,
        ],

    )->create();

    $this->endpoint = '/api/supply-addition';
    $this->params = [
        'vendor_id' => $this->vendor->id,
        'quantity' => 1,
        'box_id' => 1,
        'location_id' => 1,
        'po_number' => $this->supplyPo->po_number,
        'invoice_number' => $this->supplyPo->invoice_number,
        'supply_id' => $this->supply[0]->id,
        'sku' => $this->supply[0]->sku,
        'po_id' => $this->supplyPo->id,
        'employee_id' => $this->employee->id,
        'id_time_checking' => $this->timeTracking->id,
    ];
});
/// validate field is required
test('supply addition : validate required', function () {
    $fields = ['po_id', 'supply_id', 'quantity', 'employee_id', 'id_time_checking'];
    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->access_token,
            'Accept' => 'application/json',
        ])->post($this->endpoint, $inputs);
        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toMatchArray([
            "$field" => ['The ' . str_replace('_', ' ', $field) . ' field is required.']
        ]);
    }
});

test('supply addition : employee is belong another warehouse', function () {
    $employee = Employee::factory()->create(['warehouse_id' => faker::create()->randomDigitNot($this->warehouse->id)]);
    $this->params['employee_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'employee_id' => ['The selected employee id is invalid.']
    ]);
});
test('supply addition : validate supply not belong PO', function () {
    $supply = Supply::factory()->create([
        'name' => faker::create()->word(),
        'sku' => faker::create()->unique()->word(),
        'vendor_id' => $this->vendor->id,
        'category_id' => $this->supplyCategory->id,
        'unit_id' => $this->supplyUnit->id,
    ]);
    $this->params['supply_id'] = $supply->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'supply_id' => ['supply is not belong PO']
    ]);
});
test('supply addition : PO is completed', function () {
    SupplyPurchaseOrder::where('id', $this->supplyPo->id)->update(['order_status' => SupplyPurchaseOrder::COMPLETED_STATUS]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'purchase_order' => ['The purchase order has been completed or canceled and cannot be updated!']
    ]);
});
test('supply addition : PO is canceled', function () {
    SupplyPurchaseOrder::where('id', $this->supplyPo->id)->update(['order_status' => SupplyPurchaseOrder::CANCELLED_STATUS]);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'purchase_order' => ['The purchase order has been completed or canceled and cannot be updated!']
    ]);
});
test('supply addition : max quantity', function () {
    SupplyPurchaseOrderItem::where('supply_id', $this->supply[0]->id)->update(['quantity' => 2, 'quantity_onhand' => 1]);
    $this->params['quantity'] = 2;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'quantity' => ['The quantity may not be greater than 1.']
    ]);
});
test('supply addition :successfully', function () {
    Queue::fake();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $inventoryAddition = SupplyInventoryAddition::where('po_id', $dataResponse['po_id'])->first();
    $inventory = SupplyInventory::where('object_id', $inventoryAddition->id)->first();
    expect($inventoryAddition)->toBeObject();
    expect($inventory)->toBeObject();
    Queue::assertPushed(UpdateQuantitySupplyJob::class, function ($job) {
        $job->handle();

        return true;
    });

    $supplyQuantity = SupplyQuantity::where('supply_id', $dataResponse['supply_id'])->first();
    expect($supplyQuantity->incoming_stock)->toEqual(9);
    $timeTracking = TimeTracking::find($this->timeTracking->id);
    expect($timeTracking->quantity)->toEqual($this->params['quantity']);
});

test('supply addition: The Box ID existed', function () {
    SupplyBox::create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'CODE_DEMO',
        'supply_id' => 1,
        'location_id' => 1,
        'quantity' => 100
    ]);
    $this->params['box_id'] = 'CODE_DEMO';
    $this->params['location_id'] = 1;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'box_id' => ['The box ID has already been taken.']
    ]);
});

test('supply addition: Create new addition with new Box and selected location', function () {
    $this->params['box_id'] = 'New BOX';
    $this->params['location_id'] = 1;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('supply_boxes', [
        'supply_id' => $this->params['supply_id'],
        'location_id' => $this->params['location_id'],
        'quantity' => $this->params['quantity'],
        'barcode' => $this->params['box_id'],
        'warehouse_id' => $this->warehouse->id
    ]);
});
