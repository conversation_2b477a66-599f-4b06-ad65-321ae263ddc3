<?php

use App\Models\SupplyLocation;
use Faker\Factory as Faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->faker = Faker::create();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->endpoint = '/api/supply-location/create';

    $this->params = [
        'items' => "Location 1\nLocation 2\nLocation 3",
    ];
});

test('Create supply location fail - Barcode items is required.', function () {
    unset($this->params['items']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'items' => [
                'Location ID is required.'
            ]
        ]
    ]);
});

test('Create supply location fail - Barcode items must be string.', function () {
    $this->params['items'] = 1998;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'items' => [
                'Location barcode must be string.'
            ]
        ]
    ]);
});

test('Create supply location successfully - But string contain existed barcode only.', function () {
    SupplyLocation::create([
        'barcode' => 'Existed barcode',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->params['items'] = 'Existed barcode';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'items' => [
                'Existed barcode is already existing in Supply Locations.'
            ]
        ]
    ]);
});

test('Create supply location successfully - But string contain existed barcode and some valid barcode.', function () {
    SupplyLocation::create([
        'barcode' => 'Existed barcode',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->params['items'] = "Existed barcode\nLocation 1\nLocation 2";
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_CREATED);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'totalInsertSuccess' => 2
    ]);

    // Assert supply location table
    $this->assertDatabaseHas('supply_locations', [
        'barcode' => 'Location 1',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->assertDatabaseHas('supply_locations', [
        'barcode' => 'Location 2',
        'warehouse_id' => $this->warehouse->id
    ]);
});

test('Create supply location fail because supply locations list exceed 1000 elements when imploded.', function () {
    $fakeElements = [];
    $locationNumberConfig = 1100;
    for ($i = 0; $i < $locationNumberConfig; $i++) {
        // Fake a string less than 100 character
        $fakeElements[] = $this->faker->sentence(10);
    }
    // Implode the elements with '\n'
    $string = implode("\n", $fakeElements);
    $this->params['items'] = $string;
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'items' => [
                'The items list must not exceed 1000 lines.'
            ]
        ]
    ]);
});

test('Create supply location successfully.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_CREATED);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'totalInsertSuccess' => 3
    ]);

    // Assert supply location table
    $this->assertDatabaseHas('supply_locations', [
        'barcode' => 'Location 1',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->assertDatabaseHas('supply_locations', [
        'barcode' => 'Location 2',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->assertDatabaseHas('supply_locations', [
        'barcode' => 'Location 3',
        'warehouse_id' => $this->warehouse->id
    ]);
});

test('Create supply location successfully with 1000 locations.', function () {
    $fakeElements = [];
    $locationNumberConfig = 1000;
    for ($i = 0; $i < $locationNumberConfig; $i++) {
        // Fake a string less than 100 character
        $fakeElements[] = $this->faker->sentence(10);
    }
    // Implode the elements with '\n'
    $string = implode("\n", $fakeElements);
    $this->params['items'] = $string;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(Response::HTTP_CREATED);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'totalInsertSuccess' => $locationNumberConfig
    ]);

    $this->assertDatabaseCount('supply_locations', $locationNumberConfig);
});
