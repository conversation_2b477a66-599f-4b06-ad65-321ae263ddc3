<?php

use App\Models\Dl2400Printer;
use App\Models\Dl2400PrintLog;
use App\Models\ProductPrintSide;
use App\Models\SaleOrderItemBarcode;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/dl2400/device/4716B4C6-5031-4EB2-9357-3588DDAD45EF';
});

test('get device', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $device = Dl2400Printer::where('device_id', '4716B4C6-5031-4EB2-9357-3588DDAD45EF')->first();
    expect($device->device_id)->toBe($response['device_id']);
});

test('update device', function () {
    Dl2400Printer::factory()->create([
        'device_id' => '4716B4C6-5031-4EB2-9357-3588DDAD45EF',
        'name' => 'test 123',
    ]);
    $response = $this->put($this->endpoint, ['name' => 'abc']);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $device = Dl2400Printer::where('device_id', '4716B4C6-5031-4EB2-9357-3588DDAD45EF')->first();
    expect($device->device_id)->toBe($response['device_id'])
        ->and($device->name)->toBe($response['name']);
});

test('push log print', function () {
    Dl2400Printer::factory()->create([
        'device_id' => '4716B4C6-5031-4EB2-9357-3588DDAD45EF',
        'name' => 'test 123',
    ]);
    SaleOrderItemBarcode::factory()->create([
        'label_id' => '100824-SJ-M-000009-3',
        'order_id' => 9270021,
    ]);
    ProductPrintSide::factory()->create([
        'code' => 1,
        'name' => 'Back',
    ]);
    $response = $this->post('/dl2400/log/4716B4C6-5031-4EB2-9357-3588DDAD45EF', [
        'employee_id' => 253,
        'label_id' => '100824-SJ-M-000009-3-1',
    ]);
    $response->assertStatus(200);
    $device = Dl2400Printer::where('device_id', '4716B4C6-5031-4EB2-9357-3588DDAD45EF')->first();
    $deviceLog = Dl2400PrintLog::where('dl2400_printers_id', $device->id)->first();
    expect($deviceLog->employee_id)->toBe(253)
        ->and($deviceLog->label_id)->toBe('100824-SJ-M-000009-3')
        ->and($deviceLog->order_id)->toBe(9270021)
        ->and($deviceLog->side)->toBe('Back');
});
