<?php

use App\Jobs\DetectShippingMethodRedBubbleJob;
use App\Models\ShippingMethod;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->shippingMethod = ShippingMethod::factory()
        ->createMany([
            [
                'api_shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_USPS01_GroundAdvantage',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_fedex01_FEDEX_GROUND',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_UPSM01_PriorityMailInnovations',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_USPS01_Priority',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_passport01_PriorityDduDelcon',
                'store_id' => Store::STORE_REDBUBBLE
            ],
            [
                'api_shipping_method' => 'RB_passport01_epacketDdp',
                'store_id' => Store::STORE_REDBUBBLE
            ],
        ]);
});

test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations ', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight >=20 -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 21;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $dataWeightOrder = 19;
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight > 5 <=20 -> RB_USPS01_GroundAdvantage', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight >=6 -> RB_fedex01_FEDEX_GROUND or RB_fedex01_FEDEX_GROUND_HOME_DELIVERY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 6;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight > 5 < 6  -> RB_USPS01_GroundAdvantage', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5.5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight >=8  -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'CO',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight > 5 < 8  -> RB_USPS01_GroundAdvantage', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 6;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'CO',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight >= 11 -> RB_fedex01_FEDEX_GROUND or RB_fedex01_FEDEX_GROUND_HOME_DELIVERY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight > 5 < 11 -> RB_USPS01_GroundAdvantage', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 10;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});

//test('standard, not domestic, weight < 4 -> RB_passport01_epacketDdp - warehouse 1', function () {
test('standard, not domestic, weight < 4 -> RB_UPSM01_PriorityMailInnovations - warehouse 1', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 3;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'CA',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_epacketDdp',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_epacketDdp',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, not domestic, weight < 4 -> RB_passport01_PriorityDduDelcon - warehouse 1', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 3;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'VN',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});

//test('standard, not domestic, weight >= 4 -> RB_passport01_PriorityDduDelcon - warehouse 1', function () {
test('standard, not domestic, weight >= 4 -> RB_passport01_PriorityDduDelcon - warehouse 1', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'VN',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight <= 7 -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 7;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight <= 7 -> RB_USPS01_Priority', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 7;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight > 7 -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight > 0 < 999999 -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 888;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

//test('express, not domestic, weight > 0 < 999999 -> RB_passport01_PriorityDduDelcon - warehouse 1', function () {
test('express, not domestic, weight > 0 < 999999 -> RB_fedex01_INTERNATIONAL_ECONOMY - warehouse 1', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 888;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'VN',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight < 10 for territory -> RB_UPSM01_ExpeditedMailInnovations', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 9;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight >= 10 for territory -> RB_fedex01_INTERNATIONAL_ECONOMY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight > 0 < 999999 for territory -> RB_fedex01_INTERNATIONAL_ECONOMY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight 0 -> RB_fedex01_FEDEX_2_DAY', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight 0 -> RB_USPS01_Priority', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight 0 -> RB_UPSM01_ExpeditedMailInnovations', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight 0 -> fail', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AS',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});

//th chua khai bao shipping method cho store
test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations but not shipping method of store ->fail', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 1;
    $shippingMethodRb = 'standard';
    $datashippingMethod = $this->shippingMethod->where('api_shipping_method', 'RB_UPSM01_ExpeditedMailInnovations')->first();
    $datashippingMethod->api_shipping_method = 'swiftpod';
    $datashippingMethod->save();
    $dataWeightOrder = 5;
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});

//th chua không thỏa mãn rule nao của khách do state không có
test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations but not rule shipping method of RB ->fail', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 1;
    $shippingMethodRb = 'standard';
    $dataWeightOrder = 5;
    $address = [
        'country' => 'US',
        'state' => 'cc',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});

test('address is po box - standard', function () {
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    // p.o. BOX 234 Main St
    $address['street1'] = 'p.o. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // P.O. BOX 234 Main St
    $address['street1'] = 'P.O. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // Main St, P.O. BOX 234
    $address['street1'] = 'Main St, P.O. BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // post office BOX 234 Main St
    $address['street1'] = 'post office BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO BOX 234 Main St
    $address['street1'] = 'PO BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // po box 234 Main St
    $address['street1'] = 'po box 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // po Box 234, 453 Main St
    $address['street1'] = 'po Box 234, 453 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // Postal Office BOX 234
    $address['street1'] = 'Postal Office BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // POST BOX 234, Main St
    $address['street1'] = 'POST BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // POSTAL BOX 234, Main St
    $address['street1'] = 'POSTAL BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO 234, 345 Main St
    $address['street1'] = 'PO 234, 345 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // won't return match
    // 345 Box St, Main St
    $address['street1'] = '345 Box St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = '345 Po St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = 'p.o. BOX ABC';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('address is po box - express', function () {
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    // p.o. BOX 234 Main St
    $address['street1'] = 'p.o. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // P.O. BOX 234 Main St
    $address['street1'] = 'P.O. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // Main St, P.O. BOX 234
    $address['street1'] = 'Main St, P.O. BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // post office BOX 234 Main St
    $address['street1'] = 'post office BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO BOX 234 Main St
    $address['street1'] = 'PO BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // po box 234 Main St
    $address['street1'] = 'po box 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // po Box 234, 453 Main St
    $address['street1'] = 'po Box 234, 453 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // Postal Office BOX 234
    $address['street1'] = 'Postal Office BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // POST BOX 234, Main St
    $address['street1'] = 'POST BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // POSTAL BOX 234, Main St
    $address['street1'] = 'POSTAL BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO 234, 345 Main St
    $address['street1'] = 'PO 234, 345 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // won't return match
    // 345 Box St, Main St
    $address['street1'] = '345 Box St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = '345 Po St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = 'p.o. BOX ABC';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('express, standard, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 1;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $saleOrder->shipping_method = 'standard';
    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});

test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight >=20 -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 21;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $dataWeightOrder = 19;
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight > 5 <=20 -> RB_USPS01_GroundAdvantage - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight >=6 -> RB_fedex01_FEDEX_GROUND or RB_fedex01_FEDEX_GROUND_HOME_DELIVERY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 6;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
        'status' => true,
        'message' => 'Success',
    ]);

    $dataWeightOrder = 6.6;
    $residential = 0;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight > 5 < 6  -> RB_USPS01_GroundAdvantage - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5.5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight >=8  -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'CT',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight > 5 < 8  -> RB_USPS01_GroundAdvantage - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 6;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'WV',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight >= 11 -> RB_fedex01_FEDEX_GROUND or RB_fedex01_FEDEX_GROUND_HOME_DELIVERY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_GROUND_HOME_DELIVERY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight > 5 < 11 -> RB_USPS01_GroundAdvantage - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 10;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_GroundAdvantage',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, not domestic, weight < 4 -> RB_passport01_epacketDdp - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 3;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'CA',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_epacketDdp',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_epacketDdp',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, not domestic, weight < 4 -> RB_passport01_PriorityDduDelcon - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 3;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'VN',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, not domestic, weight >= 4 -> RB_passport01_PriorityDduDelcon - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'VN',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight <= 7 -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 7;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight <= 7 -> RB_USPS01_Priority - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 7;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight > 7 -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 8;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight > 0 < 999999 -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 888;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, not domestic, weight > 0 < 999999 -> RB_passport01_PriorityDduDelcon - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 888;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'VN',
        'state' => 'AK',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_passport01_PriorityDduDelcon',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight < 10 for territory -> RB_UPSM01_ExpeditedMailInnovations - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 9;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight >= 10 for territory -> RB_fedex01_INTERNATIONAL_ECONOMY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight > 0 < 999999 for territory -> RB_fedex01_INTERNATIONAL_ECONOMY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 11;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'PR',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_INTERNATIONAL_ECONOMY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight 0 -> RB_fedex01_FEDEX_2_DAY - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight 0 -> RB_USPS01_Priority - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight 0 -> fail - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 0;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AS',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});
test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations but not shipping method of store ->fail - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 19;
    $shippingMethodRb = 'standard';
    $datashippingMethod = $this->shippingMethod->where('api_shipping_method', 'RB_UPSM01_ExpeditedMailInnovations')->first();
    $datashippingMethod->api_shipping_method = 'swiftpod';
    $datashippingMethod->save();
    $dataWeightOrder = 5;
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});
test('standard, domestic, weight <=5 -> RB_UPSM01_ExpeditedMailInnovations but not rule shipping method of RB ->fail - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'cc',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);

    // $residential false
    $residential = 1;
    $detect = new DetectShippingMethodRedBubbleJob(1212);
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => '',
        'status' => false,
        'message' => 'The desired carrier/service is not available on this package',
    ]);
});
test('address is po box - standard - warehouse 19', function () {
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'state' => 'AL',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    // p.o. BOX 234 Main St
    $address['street1'] = 'p.o. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // P.O. BOX 234 Main St
    $address['street1'] = 'P.O. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // Main St, P.O. BOX 234
    $address['street1'] = 'Main St, P.O. BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // post office BOX 234 Main St
    $address['street1'] = 'post office BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO BOX 234 Main St
    $address['street1'] = 'PO BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // po box 234 Main St
    $address['street1'] = 'po box 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // po Box 234, 453 Main St
    $address['street1'] = 'po Box 234, 453 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // Postal Office BOX 234
    $address['street1'] = 'Postal Office BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // POST BOX 234, Main St
    $address['street1'] = 'POST BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // POSTAL BOX 234, Main St
    $address['street1'] = 'POSTAL BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO 234, 345 Main St
    $address['street1'] = 'PO 234, 345 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_STANDARD,
        'status' => true,
        'message' => 'Success',
    ]);

    // won't return match
    // 345 Box St, Main St
    $address['street1'] = '345 Box St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = '345 Po St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = 'p.o. BOX ABC';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_UPSM01_ExpeditedMailInnovations',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('address is po box - express - warehouse 19', function () {
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->external_number = 'swiftpod-01';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'state' => 'AZ',
        'street2' => 'tang 8'
    ];
    $residential = 0;
    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    // p.o. BOX 234 Main St
    $address['street1'] = 'p.o. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // P.O. BOX 234 Main St
    $address['street1'] = 'P.O. BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // Main St, P.O. BOX 234
    $address['street1'] = 'Main St, P.O. BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // post office BOX 234 Main St
    $address['street1'] = 'post office BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO BOX 234 Main St
    $address['street1'] = 'PO BOX 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // po box 234 Main St
    $address['street1'] = 'po box 234 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // po Box 234, 453 Main St
    $address['street1'] = 'po Box 234, 453 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // Postal Office BOX 234
    $address['street1'] = 'Postal Office BOX 234';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // POST BOX 234, Main St
    $address['street1'] = 'POST BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // POSTAL BOX 234, Main St
    $address['street1'] = 'POSTAL BOX 234, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // PO 234, 345 Main St
    $address['street1'] = 'PO 234, 345 Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => ShippingMethod::SHIPPING_API_FOR_PO_BOX_DOMESTIC_RB_EXPRESS,
        'status' => true,
        'message' => 'Success',
    ]);

    // won't return match
    // 345 Box St, Main St
    $address['street1'] = '345 Box St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);

    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = '345 Po St, Main St';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);

    // 345 Po St, Main St
    $address['street1'] = 'p.o. BOX ABC';
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_fedex01_FEDEX_2_DAY',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('standard, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'standard';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'standard';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});
test('express, standard, domestic, weight any, Military (AA, AE, AP) -> RB_USPS01_Priority - warehouse 19', function () {
    // $residential false
    $saleOrder = new Collection();
    $saleOrder->shipping_method = 'express';
    $saleOrder->warehouse_id = 19;
    $dataWeightOrder = 5;
    $shippingMethodRb = 'express';
    $address = [
        'country' => 'US',
        'street1' => '28 Nguyen co thanh',
        'street2' => 'tang 8'
    ];

    $urlGoogleSpace = '';
    $detect = new DetectShippingMethodRedBubbleJob(1212);

    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $saleOrder->shipping_method = 'standard';
    $address['state'] = 'AA';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AA';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AE';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 0;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);

    $address['state'] = 'AP';
    $residential = 1;
    $result = $detect->getShippingMethod($saleOrder, $shippingMethodRb, $dataWeightOrder, $address, $residential, $urlGoogleSpace);
    expect($result)->toMatchArray([
        'shipping_method' => 'RB_USPS01_Priority',
        'status' => true,
        'message' => 'Success',
    ]);
});
