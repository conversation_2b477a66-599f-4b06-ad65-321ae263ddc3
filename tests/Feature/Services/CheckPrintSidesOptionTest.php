<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Http\Service\GetOrderService;
use App\Models\ProductPrintSide;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->getOrderService = new GetOrderService();
    ProductPrintSide::factory()->createMany([
        [
            'code' => 0,
            'order' => 1,
            'code_name' => 'front',
            'code_wip' => 'F',
        ],
        [
            'code' => 1,
            'order' => 2,
            'code_name' => 'back',
            'code_wip' => 'B',
        ],
        [
            'code' => 2,
            'order' => 3,
            'code_name' => 'pocket',
            'code_wip' => 'P',
        ],
        [
            'code' => 5,
            'order' => 4,
            'code_name' => 'inner_neck_label',
            'code_wip' => 'I',
        ],
    ]);
});

test('Key invalid', function () {
    $inputs = [
        [
            (object) [
                "key_name_invalid" => "PrintFiles.Front",
                "key_values_invalid" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "key_name_invalid" => "PrintFiles.Back",
                "key_values_invalid" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (object) [
                "key_name_invalid" => "PrintFiles.Pocket",
                "key_values_invalid" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "key_name_invalid" => "PrintFiles.Back",
                "key_values_invalid" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        []
    ];

    foreach ($inputs as $input) {
        $output = $this->getOrderService->checkPrintSidesOption($input);
        $this->assertEquals($output, '');
    }
});

test('Value empty', function () {
    // value front empty
    $input = [
        (object) [
            "name" => "PrintFiles.Front",
            "value" => ""
        ],
        (object) [
            "name" => "PrintFiles.Back",
            "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
        ],
    ];


    $output = $this->getOrderService->checkPrintSidesOption($input);
    $this->assertEquals($output, 'B');
});

test('Duplicate side', function () {
    $inputs = [
        [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],

        ],
        [
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
    ];

    foreach ($inputs as $key => $input) {
        $output = $this->getOrderService->checkPrintSidesOption($input);
        $this->assertEquals($output, 'FBP');
    }
});

test('Valid data', function () {
    $input1 =
        [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];
        $output = $this->getOrderService->checkPrintSidesOption($input1);
        $this->assertEquals($output, 'FB');

        $input2 = [
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];
        $output = $this->getOrderService->checkPrintSidesOption($input2);
        $this->assertEquals($output, 'BP');

        $input3 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];
        $output = $this->getOrderService->checkPrintSidesOption($input3);
        $this->assertEquals($output, 'FBP');

        $input4 = [
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];
        $output = $this->getOrderService->checkPrintSidesOption($input4);
        $this->assertEquals($output, 'B');

        $input5 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.InnerNeckLabel",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];

        $output = $this->getOrderService->checkPrintSidesOption($input5);
        $this->assertEquals($output, 'FBPI');

        $input6 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.INNERNECKLABEL",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];

        $output = $this->getOrderService->checkPrintSidesOption($input6);
        $this->assertEquals($output, 'FBPI');

        $input7 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Inner_Neck_Label",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];

        $output = $this->getOrderService->checkPrintSidesOption($input7);
        $this->assertEquals($output, 'FBPI');

        $input8 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.inner neck label",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];

        $output = $this->getOrderService->checkPrintSidesOption($input8);
        $this->assertEquals($output, 'FBPI');

        $input9 = [
            (object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (object) [
                "name" => "PrintFiles.inner_neck_label",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ];

        $output = $this->getOrderService->checkPrintSidesOption($input9);
        $this->assertEquals($output, 'FBPI');

});

