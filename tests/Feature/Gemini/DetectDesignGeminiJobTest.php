<?php

use App\Jobs\DetectDesignGeminiJob;
use App\Models\ImageHash;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemImageTag;
use App\Models\VisuaDetectImage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;

uses(RefreshDatabase::class);

afterEach(function() {
    Mockery::close();
});
beforeEach(function () {
    $errorKey = 'gemini_error_count';
    Cache::store('redis')->delete($errorKey);
    $imageHash = ImageHash::factory()->create([
        'is_ip_violation' => false,
        'user_check_id' => null
    ]);

    $this->imageUrl = 'https://d1ud88wu9m1k4s.cloudfront.net/design/2023/01/27/A27037_ySmJDNGPWKLc4pL4K8cV5FK5X_1674815464134.png';
    $saleOrderItemImage = SaleOrderItemImage::factory()
        ->for($imageHash, 'imageHash')
        ->create([
            'order_id' => 1,
            'image_url' => $this->imageUrl,
        ]);

    $this->visuaDetectImage = VisuaDetectImage::factory()
        ->for($saleOrderItemImage, 'image')
        ->create([
            'order_id' => $saleOrderItemImage->order_id,
            'image_id' => $saleOrderItemImage->id
        ]);

    $this->visuaDetectImageId = $this->visuaDetectImage->id;
    $this->job = new DetectDesignGeminiJob([
        'visua_detect_image_id' => $this->visuaDetectImage->id,
        'url' => $this->imageUrl,
    ]);
});

test('detect design gemini job - Too many errors', function () {
    // Set error count to exceed max_attempt_error
    $errorKey = 'gemini_error_count';
    Cache::store('redis')->put($errorKey, config('gemini.max_attempt_error'), 600);
    
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);
    
    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"error":{"message":"Permission denied","type":"PERMISSION_DENIED","code":403}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);
    
    $this->job->handle();
    
    // Verify no tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
    
    // Verify error count was reset
    expect(Cache::store('redis')->get($errorKey))->toBeNull();
});



test('detect design gemini job - success', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);
    
    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"candidates":[{"content":{"parts":[{"text":"[{\"name\": \"Mario\", \"type\": \"character\"}, {\"name\": \"Nike\", \"type\": \"brand\"}]"}],"role":"model"},"finishReason":"STOP","avgLogprobs":-0.039917900012089655}],"usageMetadata":{"promptTokenCount":632,"candidatesTokenCount":26,"totalTokenCount":658,"promptTokensDetails":[{"modality":"IMAGE","tokenCount":258},{"modality":"TEXT","tokenCount":374}],"candidatesTokensDetails":[{"modality":"TEXT","tokenCount":26}]},"modelVersion":"gemini-1.5-flash","responseId":"uA40aPmZMbOMgLUPkI_toQE"}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);
    $this->job->handle();
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(2);
});

test('detect design gemini job - 5 consecutive failures', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);
    
    $imageHash = ImageHash::factory()->create([
        'is_ip_violation' => false,
        'user_check_id' => null
    ]);

    $saleOrderItemImages = [];
    $visuaDetectImages = [];

    // Create 5 images and detect jobs
    for ($i = 0; $i < 6; $i++) {
        $saleOrderItemImage = SaleOrderItemImage::factory()
            ->for($imageHash, 'imageHash')
            ->create([
                'order_id' => $i + 1,
                'image_url' => $this->imageUrl,
            ]);
        $saleOrderItemImages[] = $saleOrderItemImage;

        $visuaDetectImage = VisuaDetectImage::factory()
            ->for($saleOrderItemImage, 'image')
            ->create([
                'order_id' => $saleOrderItemImage->order_id,
                'image_id' => $saleOrderItemImage->id
            ]);
        $visuaDetectImages[] = $visuaDetectImage;
    }
    
    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"error":{"message":"Internal error","type":"INTERNAL","code":500}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    // Process all jobs
    foreach ($visuaDetectImages as $visuaDetectImage) {
        $job = new DetectDesignGeminiJob([
            'visua_detect_image_id' => $visuaDetectImage->id,
            'url' => $this->imageUrl,
        ]);
        $job->handle();

        $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $visuaDetectImage->image_id)->get();
        expect($visuaDetectImageTags)->toHaveCount(0);
    }
});

test('detect design gemini job - exception handling', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);
    
    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => function() {
            throw new \Exception('API connection error');
        },
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);
    
    $this->job->handle();
    
    $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
    expect($visuaDetectImage->gemini_json)->toBeInstanceOf(\Illuminate\Database\Eloquent\Casts\ArrayObject::class)
        ->and($visuaDetectImage->gemini_json)->toHaveKey('error')
        ->and($visuaDetectImage->gemini_json['error'])->toContain('API connection error');
    
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
});
