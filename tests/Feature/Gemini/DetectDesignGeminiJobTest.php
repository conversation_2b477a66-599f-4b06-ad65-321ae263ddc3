<?php

use App\Jobs\DetectDesignGeminiJob;
use App\Models\ImageHash;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemImageTag;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\Trademark;
use App\Models\VisuaDetectImage;
use App\Repositories\SaleOrderRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

afterEach(function () {
    Mockery::close();
});
beforeEach(function () {
    $errorKey = 'gemini_error_count';
    Cache::store('redis')->delete($errorKey);
    $imageHash = ImageHash::factory()->create([
        'is_ip_violation' => false,
        'user_check_id' => null
    ]);

    $this->imageUrl = 'https://d1ud88wu9m1k4s.cloudfront.net/design/2023/01/27/A27037_ySmJDNGPWKLc4pL4K8cV5FK5X_1674815464134.png';
    $saleOrderItemImage = SaleOrderItemImage::factory()
        ->for($imageHash, 'imageHash')
        ->create([
            'order_id' => 1,
            'image_url' => $this->imageUrl,
        ]);

    $this->visuaDetectImage = VisuaDetectImage::factory()
        ->for($saleOrderItemImage, 'image')
        ->create([
            'order_id' => $saleOrderItemImage->order_id,
            'image_id' => $saleOrderItemImage->id,
            'is_ip_violation' => VisuaDetectImage::IS_FALSE // Ensure it starts as false
        ]);

    $this->visuaDetectImageId = $this->visuaDetectImage->id;
    $this->job = new DetectDesignGeminiJob([
        'visua_detect_image_id' => $this->visuaDetectImage->id,
        'url' => $this->imageUrl,
    ]);
});

test('detect design gemini job - Too many errors', function () {
    // Set error count to exceed max_attempt_error
    $errorKey = 'gemini_error_count';
    Cache::store('redis')->put($errorKey, config('gemini.max_attempt_error'), 600);

    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"error":{"message":"Permission denied","type":"PERMISSION_DENIED","code":403}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    $this->job->handle();

    // Verify no tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);

    // Verify error count was reset
    expect(Cache::store('redis')->get($errorKey))->toBeNull();
})->skip();

test('detect design gemini job - success', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"candidates":[{"content":{"parts":[{"text":"[{\"name\": \"Mario\", \"type\": \"character\"}, {\"name\": \"Nike\", \"type\": \"brand\"}]"}],"role":"model"},"finishReason":"STOP","avgLogprobs":-0.039917900012089655}],"usageMetadata":{"promptTokenCount":632,"candidatesTokenCount":26,"totalTokenCount":658,"promptTokensDetails":[{"modality":"IMAGE","tokenCount":258},{"modality":"TEXT","tokenCount":374}],"candidatesTokensDetails":[{"modality":"TEXT","tokenCount":26}]},"modelVersion":"gemini-1.5-flash","responseId":"uA40aPmZMbOMgLUPkI_toQE"}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);
    $this->job->handle();
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(2);
})->skip();

test('detect design gemini job - 5 consecutive failures', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    $imageHash = ImageHash::factory()->create([
        'is_ip_violation' => false,
        'user_check_id' => null
    ]);

    $saleOrderItemImages = [];
    $visuaDetectImages = [];

    // Create 5 images and detect jobs
    for ($i = 0; $i < 6; $i++) {
        $saleOrderItemImage = SaleOrderItemImage::factory()
            ->for($imageHash, 'imageHash')
            ->create([
                'order_id' => $i + 1,
                'image_url' => $this->imageUrl,
            ]);
        $saleOrderItemImages[] = $saleOrderItemImage;

        $visuaDetectImage = VisuaDetectImage::factory()
            ->for($saleOrderItemImage, 'image')
            ->create([
                'order_id' => $saleOrderItemImage->order_id,
                'image_id' => $saleOrderItemImage->id
            ]);
        $visuaDetectImages[] = $visuaDetectImage;
    }

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"error":{"message":"Internal error","type":"INTERNAL","code":500}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    // Process all jobs
    foreach ($visuaDetectImages as $visuaDetectImage) {
        $job = new DetectDesignGeminiJob([
            'visua_detect_image_id' => $visuaDetectImage->id,
            'url' => $this->imageUrl,
        ]);
        $job->handle();

        $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $visuaDetectImage->image_id)->get();
        expect($visuaDetectImageTags)->toHaveCount(0);
    }
})->skip();

test('detect design gemini job - exception handling', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => function () {
            throw new \Exception('API connection error');
        },
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);

    $this->job->handle();

    $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
    expect($visuaDetectImage->gemini_json)->toBeInstanceOf(\Illuminate\Database\Eloquent\Casts\ArrayObject::class)
        ->and($visuaDetectImage->gemini_json)->toHaveKey('error')
        ->and($visuaDetectImage->gemini_json['error'])->toContain('API connection error');

    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
})->skip();

test('detect design gemini job - retry logic with redis', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('get')->with("gemini_retry:{$this->visuaDetectImage->id}")->andReturn(2);
    $redisMock->shouldReceive('incr')->andReturn(3);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('del')->andReturn(1);

    // Mock Setting
    $settingMock = Mockery::mock(Setting::class);
    $settingMock->shouldReceive('where->first')->andReturn((object) ['value' => 'webhook_url']);
    app()->instance(Setting::class, $settingMock);

    // Mock sendGoogleChat function
    $this->mockFunction('sendGoogleChat', function ($message, $webhook) {
        expect($message)->toContain('Retry max 3 times error');
        expect($webhook)->toBe('webhook_url');

        return true;
    });

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"success": false, "detections": {"code": 500}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    $this->job->handle();

    // Verify no tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
})->skip();

test('detect design gemini job - retry with code not 503', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('del')->andReturn(1);

    // Mock Setting
    $settingMock = Mockery::mock(Setting::class);
    $settingMock->shouldReceive('where->first')->andReturn((object) ['value' => 'webhook_url']);
    app()->instance(Setting::class, $settingMock);

    // Mock sendGoogleChat function
    $this->mockFunction('sendGoogleChat', function ($message, $webhook) {
        expect($message)->toContain('visual_id retry');
        expect($webhook)->toBe('webhook_url');

        return true;
    });

    // Mock dispatch
    $dispatchMock = Mockery::mock();
    $dispatchMock->shouldReceive('delay')->andReturn(true);
    $dispatchMock->shouldReceive('onQueue')->andReturn($dispatchMock);

    $this->mockMethod(DetectDesignGeminiJob::class, 'dispatch', function ($data) use ($dispatchMock) {
        expect($data)->toBe($this->data);

        return $dispatchMock;
    });

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"detections": {"code": 400}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    $this->job->handle();

    // Verify no tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
})->skip();

test('detect design gemini job - check trademark violation', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    // Mock Trademark
    $trademarkMock = Mockery::mock(Trademark::class);
    $trademarkMock->shouldReceive('whereIn->where->count')->andReturn(1);
    app()->instance(Trademark::class, $trademarkMock);

    // Mock SaleOrderRepository
    $orderServiceMock = Mockery::mock(SaleOrderRepository::class);
    $orderServiceMock->shouldReceive('holdOrderByVisua')->once()->andReturn(true);
    app()->instance(SaleOrderRepository::class, $orderServiceMock);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"success": true, "detections": [{"name": "Nike", "type": "brand"}]}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);

    $this->job->handle();

    // Verify VisuaDetectImage was updated
    $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
    expect($visuaDetectImage->is_ip_violation)->toBe(1);
})->skip();

test('detect design gemini job - handle order on hold with store or manual hold', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    // Mock Trademark
    $trademarkMock = Mockery::mock(Trademark::class);
    $trademarkMock->shouldReceive('whereIn->where->count')->andReturn(0);
    app()->instance(Trademark::class, $trademarkMock);

    // Mock SaleOrderOnHold
    $orderOnHoldMock = Mockery::mock(SaleOrderOnHold::class);
    $orderOnHoldMock->shouldReceive('where->where->where->update')->andReturn(1);
    app()->instance(SaleOrderOnHold::class, $orderOnHoldMock);

    // Mock SaleOrder
    $orderObj = (object) [
        'tag' => Tag::VISUA_DETECTED_ID,
        'save' => function () {
            return true;
        }
    ];

    $orderMock = Mockery::mock(SaleOrder::class);
    $orderMock->shouldReceive('find')->andReturn($orderObj);
    app()->instance(SaleOrder::class, $orderMock);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"success": true, "detections": [{"name": "Generic", "type": "brand"}]}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);

    $this->job->handle();

    // Verify tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(1);
})->skip();

test('detect design gemini job - handle order on hold without store or manual hold', function () {
    // Mock Redis facade
    $redisMock = Mockery::mock('alias:Illuminate\Support\Facades\Redis');
    $redisMock->shouldReceive('incr')->andReturn(1);
    $redisMock->shouldReceive('expire')->andReturn(true);
    $redisMock->shouldReceive('get')->andReturn(0);
    $redisMock->shouldReceive('del')->andReturn(1);

    // Mock Trademark
    $trademarkMock = Mockery::mock(Trademark::class);
    $trademarkMock->shouldReceive('whereIn->where->count')->andReturn(0);
    app()->instance(Trademark::class, $trademarkMock);

    // Mock SaleOrderOnHold
    $orderOnHoldMock = Mockery::mock(SaleOrderOnHold::class);
    $orderOnHoldMock->shouldReceive('where->where->where->update')->andReturn(0);
    $orderOnHoldMock->shouldReceive('where->delete')->andReturn(true);
    app()->instance(SaleOrderOnHold::class, $orderOnHoldMock);

    // Mock SaleOrder
    $saleOrderMock = Mockery::mock();
    $saleOrderMock->order_status = SaleOrder::ON_HOLD;
    $saleOrderMock->tag = Tag::VISUA_DETECTED_ID;
    $saleOrderMock->shouldReceive('save')->andReturn(true);

    $orderMock = Mockery::mock(SaleOrder::class);
    $orderMock->shouldReceive('whereDoesntHave->whereHas->where->where->first')->andReturn($saleOrderMock);
    app()->instance(SaleOrder::class, $orderMock);

    // Mock SaleOrderHistory
    $historyMock = Mockery::mock(SaleOrderHistory::class);
    $historyMock->shouldReceive('create')->andReturn(true);
    app()->instance(SaleOrderHistory::class, $historyMock);

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"success": true, "detections": [{"name": "Generic", "type": "brand"}]}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ]),
    ]);

    $this->job->handle();

    // Verify tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(1);

    // Verify order status was updated
    expect($saleOrderMock->order_status)->toBe(SaleOrder::NEW_ORDER);
})->skip();

test('detect design gemini job - empty visua_detect_image_id', function () {
    // Create job with empty visua_detect_image_id
    $job = new DetectDesignGeminiJob([
        'url' => $this->imageUrl,
    ]);

    // Execute job
    $result = $job->handle();

    // Verify job returns early
    expect($result)->toBeNull();
})->skip();

test('detect design gemini job - image not found', function () {
    // Mock VisuaDetectImage
    $visuaDetectImageMock = new \stdClass();
    $visuaDetectImageMock->image = null;

    // Mock the static methods directly using global function mocking
    $this->mockFunction('App\\Models\\VisuaDetectImage::with', function () use ($visuaDetectImageMock) {
        $mock = new \stdClass();
        $mock->find = function () use ($visuaDetectImageMock) {
            return $visuaDetectImageMock;
        };

        return $mock;
    });

    // Create job
    $job = new DetectDesignGeminiJob([
        'visua_detect_image_id' => 999,
        'url' => $this->imageUrl,
    ]);

    // Mock jobEcho function
    $this->mockFunction('jobEcho', function ($message) {
        expect($message)->toContain('not found image');

        return true;
    });

    // Execute job
    $result = $job->handle();

    // Verify job returns true
    expect($result)->toBeTrue();
})->skip();

test('detect design gemini job - image has been checked', function () {
    // Mock image with user_check_id
    $imageHashMock = new \stdClass();
    $imageHashMock->user_check_id = 1;
    $imageHashMock->is_ip_violation = false;

    $imageMock = new \stdClass();
    $imageMock->imageHash = $imageHashMock;

    $visuaDetectImageMock = new \stdClass();
    $visuaDetectImageMock->image = $imageMock;

    // Mock the static methods directly using global function mocking
    $this->mockFunction('App\\Models\\VisuaDetectImage::with', function () use ($visuaDetectImageMock) {
        $mock = new \stdClass();
        $mock->find = function () use ($visuaDetectImageMock) {
            return $visuaDetectImageMock;
        };

        return $mock;
    });

    // Create job
    $job = new DetectDesignGeminiJob([
        'visua_detect_image_id' => 999,
        'url' => $this->imageUrl,
    ]);

    // Mock jobEcho function
    $this->mockFunction('jobEcho', function ($message) {
        expect($message)->toContain('image has been checked');

        return true;
    });

    // Execute job
    $result = $job->handle();

    // Verify job returns null
    expect($result)->toBeNull();
})->skip();

test('detect design gemini job - image is ip violation', function () {
    // Mock image with is_ip_violation = true
    $imageHashMock = new \stdClass();
    $imageHashMock->user_check_id = null;
    $imageHashMock->is_ip_violation = true;

    $imageMock = new \stdClass();
    $imageMock->imageHash = $imageHashMock;

    $visuaDetectImageMock = new \stdClass();
    $visuaDetectImageMock->image = $imageMock;

    // Mock the static methods directly using global function mocking
    $this->mockFunction('App\\Models\\VisuaDetectImage::with', function () use ($visuaDetectImageMock) {
        $mock = new \stdClass();
        $mock->find = function () use ($visuaDetectImageMock) {
            return $visuaDetectImageMock;
        };

        return $mock;
    });

    // Create job
    $job = new DetectDesignGeminiJob([
        'visua_detect_image_id' => 999,
        'url' => $this->imageUrl,
    ]);

    // Mock jobEcho function
    $this->mockFunction('jobEcho', function ($message) {
        expect($message)->toContain('due to IP Violation');

        return true;
    });

    // Execute job
    $result = $job->handle();

    // Verify job returns null
    expect($result)->toBeNull();
})->skip();

test('detect design gemini job - service unavailable retry', function () {
    // Mock Redis facade using mockFunction
    $this->mockFunction('Illuminate\\Support\\Facades\\Redis::get', function () {
        return 0;
    });
    $this->mockFunction('Illuminate\\Support\\Facades\\Redis::incr', function () {
        return 1;
    });
    $this->mockFunction('Illuminate\\Support\\Facades\\Redis::expire', function () {
        return true;
    });
    $this->mockFunction('Illuminate\\Support\\Facades\\Redis::del', function () {
        return 1;
    });

    // Mock dispatch
    $dispatchMock = Mockery::mock();
    $dispatchMock->shouldReceive('delay')->andReturn(true);

    // Create a mock for DetectDesignGeminiJob
    $jobMock = Mockery::mock('overload:App\\Jobs\\DetectDesignGeminiJob');
    $jobMock->shouldReceive('dispatch')->andReturnUsing(function ($data) use ($dispatchMock) {
        expect($data)->toBe($this->data);

        return $dispatchMock;
    });

    Http::fake([
        config('gemini.base_url') . '?key=' . config('gemini.api_key') => Http::response(
            json_decode('{"detections": {"code": 503}}', true),
        ),
        $this->imageUrl => Http::response(file_get_contents(base_path('tests/files/image.png')), 200, [
            'Content-Type' => 'image/png',
        ])
    ]);

    $this->job->handle();

    // Verify no tags were created
    $visuaDetectImageTags = SaleOrderItemImageTag::where('sale_order_item_image_id', $this->visuaDetectImage->image_id)->get();
    expect($visuaDetectImageTags)->toHaveCount(0);
})->skip();
