<?php

use App\Models\PrintingPresetHistory;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->user = $user;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/printing-preset';

    $this->params = [
        'name' => 'FWHITE1200DL',
        'data' => '<?xml version="1.0" encoding="UTF-8"?>
<GTOPTION xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><name>FALLPURPLEDL</name><mode>gtx4</mode><szFileName></szFileName><uiCopies>1</uiCopies><byMachineMode>0</byMachineMode><szJobName>Untitled</szJobName><byPlatenSize>2</byPlatenSize><byResolution>1</byResolution><byInk>2</byInk><bySubInk>1</bySubInk><bEcoMode>false</bEcoMode><byInkVolume>10</byInkVolume><byHighlight>8</byHighlight><byMask>4</byMask><bWhiteConvert>true</bWhiteConvert><bMaterialBlack>false</bMaterialBlack><bMultiple>false</bMultiple><bTransColor>false</bTransColor><colorTrans>50944</colorTrans><byTolerance>30</byTolerance><byChoke>2</byChoke><bPause>false</bPause><byPauseSpan>20</byPauseSpan><bySaturation>0</bySaturation><byBrightness>0</byBrightness><byContrast>0</byContrast><bUniDirection>false</bUniDirection><byDoublePrint>1</byDoublePrint><iCyanBalance>0</iCyanBalance><iMagentaBalance>0</iMagentaBalance><iYellowBalance>0</iYellowBalance><iBlackBalance>0</iBlackBalance><byMinWhite>2</byMinWhite><byTransLayer>0</byTransLayer><uiReserved1>1</uiReserved1><uiReserved2>50331654</uiReserved2></GTOPTION>',
    ];
});


test('Create fail - Missing name, data', function () {
    unset($this->params['name'], $this->params['data']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'The name field is required.'
            ],
            'data' => [
                'The data field is required.'
            ],
        ]
    ]);
});

test('Create fail - name, data is not string', function () {
    $this->params['name'] = true;
    $this->params['data'] = true;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'The name must be a string.'
            ],
            'data' => [
                'The data must be a string.'
            ]
        ]
    ]);
});

test('Create fail - name is too long', function () {
    $this->params['name'] = 'Get the validation rules that apply to the request.Get the validation rules that apply to the request.
    Get the validation rules that apply to the request.Get the validation rules that apply to the request. Get the validation rules that apply to the request.Get the validation rules that apply to the request.';

$response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'The name must not be greater than 255 characters.'
            ],
        ]
    ]);
});

test('Create success.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $data = $response['data'];
    $this->assertDatabaseHas('printing_preset', [
        'name' => $this->params['name'],
        'data' => $this->params['data'],
    ]);
    $dataHistory = json_encode([
        'name' => $this->params['name'],
        'data' => $this->params['data'],
    ]) ;
    $this->assertDatabaseHas('printing_preset_history', [
        'data' => $dataHistory,
        'printing_preset_id' => $data['id'],
        'action' => PrintingPresetHistory::ACTION_CREATE
    ]);

});

