<?php

use App\Models\Warehouse;
use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\EmployeeCheckin;
use App\Models\DepartmentJobType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Carbon;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::factory()->create();
    $this->department = Department::factory(['name' => "Inventory", 'performance_report' => 1])
        ->has(DepartmentJobType::factory()->count(2)->state(new Sequence(
            ['job_type' => 'addition'],
            ['job_type' => 'create_box_moving'],
        )), 'jobTypes')->create();
    $this->user = User::factory()->create(['department_id' => $this->department->id]);
    $this->access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse->id]])->fromUser($this->user);
    $this->endpoint = '/api/employee/department-checkout';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->checkin = EmployeeCheckin::factory()->create([
        'employee_id' => $this->employee->id,
        'department_id' => $this->department->id,
        'job_type' => "addition",
    ]);
    $this->params = array(
        "id" => $this->checkin->id,
        "employee_code" => $this->employee->code,
    );
});
/// missing id checkin, employee_code
test('employee checkout : missing id, employee_code', function () {
    $fields = ['id', 'employee_code'];
    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);
        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $inputs);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

/// ID checkin invalid
test('employee checkout : ID checkin invalid', function () {
    $this->params['id'] = "abc";
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toHaveKey("message");
    expect($dataResponse['message'])->toBe("Employee ID Invalid");
});
/// Checkout successfully
test('employee checkout : successfully', function () {
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toHaveKey("message");
    expect($dataResponse['message'])->toBe( "Employee checkout successfully!");
    $checkin = EmployeeCheckin::where('id', $this->params['id'])->first();
    expect($checkin)->toBeObject();
    expect($checkin->working_time_second)->toEqual(Carbon::parse($checkin->end_time)->diffInSeconds(Carbon::parse($checkin->start_time)));
});

