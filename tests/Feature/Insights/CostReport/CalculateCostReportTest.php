<?php

use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Repositories\CostReportRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['warehouse' => $warehouse] = createAccessToken();
    $this->warehouse = $warehouse;


    ProductStyle::factory()->count(2)->sequence([
        'name' => '3000',
        'type' => 'Tee',
        'sku' => 'UNGS'
    ], [
        'type' => 'Fleece',
        'name' => '3001',
        'sku' => 'UNPT'
    ], [
        'name' => '3002',
        'type' => 'MUGS',
        'sku' => 'MUGS'
    ])->create();
    ProductType::factory()->count(2)->sequence([
        'name' => 'Fleece'
    ], [
        'name' => 'Tee'
    ], [
        'name' => 'Mugs'
    ])->create();

    // order Tee + fleece -> type = fleece
    $saleOrderFleece = SaleOrder::factory([
        'order_status' => SaleOrder::STATUS_SHIPPED,
        'is_test' => SaleOrder::NOT_TEST,
        'order_date' => Carbon::now()->format('Y-m-d'),
        'warehouse_id' => $this->warehouse->id
    ])
        ->has(
            SaleOrderItem::factory()->count(8)
                ->sequence(fn($sequence) => [
                    'quantity' => $sequence->index < 4 ? 3 : 4,
                    'product_style_sku' => $sequence->index < 4 ? 'UNGS' : 'UNPT'
                ])->state(function ($attributes, $saleOrder) {
                    return [
                        'warehouse_id' => $saleOrder->warehouse_id
                    ];
                })
                ->has(SaleOrderItemImage::factory()
                    ->count(2)
                    ->sequence(fn($sequense) => [
                        'ink_color_cc' => $sequense->index < 8 ? 3 : 4,
                        'ink_white_cc' => $sequense->index < 8 ? 2.22 : 3.32
                    ])->state(fn($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'order_item_id' => $saleOrderItem->id,
                        'order_id' => $saleOrderItem->order_id
                    ]), 'images'),
            'items')
        ->has(Shipment::factory()->count(2)->sequence(fn($sequense) => [
            'shipment_cost' => $sequense->index < 1 ? 4.5 : 5.5
        ])->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id
        ]))
        ->create();
    DB::table('sale_order')->where('id', $saleOrderFleece->id)->update([
        'shipment_id' => Shipment::where('order_id', $saleOrderFleece->id)->first()->id
    ]);
    // order Tee + Mug-> type = Tee
    $saleOrderTee = SaleOrder::factory([
        'order_status' => SaleOrder::STATUS_SHIPPED,
        'is_test' => SaleOrder::NOT_TEST,
        'order_date' => Carbon::now()->format('Y-m-d'),
        'warehouse_id' => $this->warehouse->id
    ])
        ->has(
            SaleOrderItem::factory()->count(8)
                ->sequence(fn($sequence) => [
                    'quantity' => $sequence->index < 4 ? 2 : 3,
                    'product_style_sku' => $sequence->index < 4 ? 'UNGS' : 'MUGS'
                ])->state(function ($attributes, $saleOrder) {
                    return [
                        'warehouse_id' => $saleOrder->warehouse_id
                    ];
                })
                ->has(SaleOrderItemImage::factory()
                    ->count(2)
                    ->sequence(fn($sequense) => [
                        'ink_color_cc' => $sequense->index < 8 ? 1 : 2,
                        'ink_white_cc' => $sequense->index < 8 ? 2 : 3
                    ])->state(fn($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'order_item_id' => $saleOrderItem->id,
                        'order_id' => $saleOrderItem->order_id
                    ]), 'images'),
            'items')
        ->has(Shipment::factory()->count(2)->sequence(fn($sequense) => [
            'shipment_cost' => $sequense->index < 1 ? 3 : 4
        ])->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id
        ]))
        ->create();
    DB::table('sale_order')->where('id', $saleOrderTee->id)->update([
        'shipment_id' => Shipment::where('order_id', $saleOrderTee->id)->first()->id
    ]);

    // order not in warehouse
    SaleOrder::factory([
        'order_status' => SaleOrder::STATUS_SHIPPED,
        'is_test' => SaleOrder::NOT_TEST,
        'order_date' => Carbon::now()->format('Y-m-d'),
        'warehouse_id' => $this->warehouse->id + 1
    ])
        ->has(
            SaleOrderItem::factory()->count(8)
                ->sequence(fn($sequence) => [
                    'quantity' => $sequence->index < 4 ? 3 : 4,
                    'product_style_sku' => $sequence->index < 4 ? 'UNGS' : 'UNPT'
                ])->state(function ($attributes, $saleOrder) {
                    return [
                        'warehouse_id' => $saleOrder->warehouse_id
                    ];
                })
                ->has(SaleOrderItemImage::factory()
                    ->count(2)
                    ->sequence(fn($sequense) => [
                        'ink_color_cc' => $sequense->index < 8 ? 3 : 4,
                        'ink_white_cc' => $sequense->index < 8 ? 2.22 : 3.32
                    ])->state(fn($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'order_item_id' => $saleOrderItem->id,
                        'order_id' => $saleOrderItem->order_id
                    ]), 'images'),
            'items')
        ->has(Shipment::factory()->count(2)->sequence(fn($sequense) => [
            'shipment_cost' => $sequense->index < 1 ? 4.5 : 5.5
        ])->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id
        ]))
        ->create();
    // order not shipped
    SaleOrder::factory([
        'order_status' => SaleOrder::NEW_ORDER,
        'is_test' => SaleOrder::NOT_TEST,
        'order_date' => Carbon::now()->format('Y-m-d'),
        'warehouse_id' => $this->warehouse->id
    ])
        ->has(
            SaleOrderItem::factory()->count(8)
                ->sequence(fn($sequence) => [
                    'quantity' => $sequence->index < 4 ? 3 : 4,
                    'product_style_sku' => $sequence->index < 4 ? 'UNGS' : 'UNPT'
                ])->state(function ($attributes, $saleOrder) {
                    return [
                        'warehouse_id' => $saleOrder->warehouse_id
                    ];
                })
                ->has(SaleOrderItemImage::factory()
                    ->count(2)
                    ->sequence(fn($sequense) => [
                        'ink_color_cc' => $sequense->index < 8 ? 3 : 4,
                        'ink_white_cc' => $sequense->index < 8 ? 2.22 : 3.32
                    ])->state(fn($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'order_item_id' => $saleOrderItem->id,
                        'order_id' => $saleOrderItem->order_id
                    ]), 'images'),
            'items')
        ->has(Shipment::factory()->count(2)->sequence(fn($sequense) => [
            'shipment_cost' => $sequense->index < 1 ? 4.5 : 5.5
        ])->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id
        ]))
        ->create();
    // order last month
    SaleOrder::factory([
        'order_status' => SaleOrder::SHIPPED,
        'is_test' => SaleOrder::NOT_TEST,
        'order_date' => Carbon::now()->format('Y-m-d'),
        'warehouse_id' => $this->warehouse->id,
        'created_at' => Carbon::now()->subMonths(2)->format('Y-m-d'),
        'updated_at' => Carbon::now()->subMonths(2)->format('Y-m-d'),
    ])
        ->has(
            SaleOrderItem::factory()->count(8)
                ->sequence(fn($sequence) => [
                    'quantity' => $sequence->index < 4 ? 3 : 4,
                    'product_style_sku' => $sequence->index < 4 ? 'UNGS' : 'UNPT'
                ])->state(function ($attributes, $saleOrder) {
                    return [
                        'warehouse_id' => $saleOrder->warehouse_id
                    ];
                })
                ->has(SaleOrderItemImage::factory()
                    ->count(2)
                    ->sequence(fn($sequense) => [
                        'ink_color_cc' => $sequense->index < 8 ? 3 : 4,
                        'ink_white_cc' => $sequense->index < 8 ? 2.22 : 3.32
                    ])->state(fn($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'order_item_id' => $saleOrderItem->id,
                        'order_id' => $saleOrderItem->order_id
                    ]), 'images'),
            'items')
        ->has(Shipment::factory()->count(2)->sequence(fn($sequense) => [
            'shipment_cost' => $sequense->index < 1 ? 4.5 : 5.5
        ])->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id,
            'created_at' => Carbon::now()->subMonths(2)->format('Y-m-d'),
            'updated_at' => Carbon::now()->subMonths(2)->format('Y-m-d'),
        ]))
        ->create();
    $this->repository = new CostReportRepository();
});

test('Calculate tee + fleece in warehouse, current month', function () {
    $params = [
        'product_type' => 'Tee',
        'affected_at' => Carbon::now()->format('Y-m'),
        'warehouse_id' => $this->warehouse->id
    ];

    $res = $this->repository->calculateCostReport($params, 'shipment');
    $this->assertEquals(3 + 4, $res->total_shipment_cost);
    $res = $this->repository->calculateCostReport($params, 'total_item');
    $this->assertEquals(4 * 2 + 4 * 3, $res->total_item);
    $res = $this->repository->calculateCostReport($params, 'ink_color');
    $this->assertEquals(round((2 * 8 * 1) + (3 * 8 * 2), 2), $res->total_color_cc);
    $this->assertEquals(round((2 * 8 * 2) + (3 * 8 * 3), 2), $res->total_white_cc);
    $res = $this->repository->calculateCostReport($params, 'total_order');
    $this->assertEquals(1, $res->total_order);
    $params = [
        'product_type' => 'Fleece',
        'affected_at' => Carbon::now()->format('Y-m'),
        'warehouse_id' => $this->warehouse->id
    ];
    $res = $this->repository->calculateCostReport($params, 'shipment');
    $this->assertEquals(round(4.5 + 5.5, 2), $res->total_shipment_cost);
    $res = $this->repository->calculateCostReport($params, 'total_item');
    $this->assertEquals(4 * 3 + 4 * 4, $res->total_item);
    $res = $this->repository->calculateCostReport($params, 'ink_color');
    $this->assertEquals(round((3 * 3 * 8) + (4 * 4 * 8), 2), $res->total_color_cc);
    $this->assertEquals(round((2.22 * 3 * 8) + (3.32 * 4 * 8), 2), $res->total_white_cc);
    $res = $this->repository->calculateCostReport($params, 'total_order');
    $this->assertEquals(1, $res->total_order);
});
