<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\SupplyInkReport;
use App\Models\Warehouse;
use App\Repositories\SupplyReportRepository;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->wareHouses = Warehouse::factory()->createMany([
        [
            'name' => 'San Jose',
            'state' => 'CA',
        ],
        [
            'name' => 'Dallas',
            'state' => 'NY',
        ],
    ]);

    SupplyInkReport::factory()->createMany([
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202212,
            'ink_color_cc' => 608.49102830887,
            'ink_white_cc' => 611.63334083557,
            'order_quantity' => 150,
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'month_report' => 202212,
            'ink_color_cc' => 705.90539169312,
            'ink_white_cc' => 700.21898365021,
            'order_quantity' => 150,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202301,
            'ink_color_cc' => 308.49102830887,
            'ink_white_cc' => 311.63334083557,
            'order_quantity' => 200,
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'month_report' => 202301,
            'ink_color_cc' => 405.90539169312,
            'ink_white_cc' => 400.21898365021,
            'order_quantity' => 300,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202312,
            'ink_color_cc' => 358.49102830887,
            'ink_white_cc' => 351.63334083557,
            'order_quantity' => 250,
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'month_report' => 202312,
            'ink_color_cc' => 455.90539169312,
            'ink_white_cc' => 450.21898365021,
            'order_quantity' => 350,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202401,
            'ink_color_cc' => 208.49102830887,
            'ink_white_cc' => 211.63334083557,
            'order_quantity' => 200,
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'month_report' => 202401,
            'ink_color_cc' => 305.90539169312,
            'ink_white_cc' => 300.21898365021,
            'order_quantity' => 300,
        ]
    ]);
});

test('Case return 12 month with year is valid: 2023', function () {
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast(['year' => '2023']);
    $dataMonthTarget = [
        '202301' => 'Jan 2023',
        '202302' => 'Feb 2023',
        '202303' => 'Mar 2023',
        '202304' => 'Apr 2023',
        '202305' => 'May 2023',
        '202306' => 'Jun 2023',
        '202307' => 'Jul 2023',
        '202308' => 'Aug 2023',
        '202309' => 'Sep 2023',
        '202310' => 'Oct 2023',
        '202311' => 'Nov 2023',
        '202312' => 'Dec 2023',
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);
    $dataReport = $result['dataReport'];

    foreach ($dataReport as $item) {
        foreach ($item['months'] as $key => $value) {
            expect(array_key_exists($key, $dataMonthTarget))->toBeTruthy()
                ->and(count($value))->toBe(3);
        }
    }
});

test('Case return 12 month with year is valid: now 2024', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast(['year' => '2024']);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataReport = $result['dataReport'];

    foreach ($dataReport as $item) {
        foreach ($item['months'] as $key => $value) {
            expect(array_key_exists($key, $dataMonthTarget))->toBeTruthy()
                ->and(count($value))->toBe(3);
        }
    }
});

test('Case return 12 month with not pass year', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast([]);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataReport = $result['dataReport'];

    foreach ($dataReport as $item) {
        foreach ($item['months'] as $key => $value) {
            expect(array_key_exists($key, $dataMonthTarget))->toBeTruthy()
                ->and(count($value))->toBe(3);
        }
    }
});

test('Correctly data return all', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast([]);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];
    $dataReportExpect = [
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => $this->wareHouses[0]->name,
            'ink' => 'ink_color_cc',
            'ink_label' => 'Color Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 0
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => $this->wareHouses[0]->name,
            'ink' => 'ink_white_cc',
            'ink_label' => 'White Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 0
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'warehouse' => $this->wareHouses[1]->name,
            'ink' => 'ink_color_cc',
            'ink_label' => 'Color Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 1
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
        [
            'warehouse_id' => $this->wareHouses[1]->id,
            'warehouse' => $this->wareHouses[1]->name,
            'ink' => 'ink_white_cc',
            'ink_label' => 'White Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 1
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ]
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataReport = $result['dataReport'];

    expect($dataReport)->toMatchArray($dataReportExpect);
});

test('Correctly data return with warehouse', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast(['warehouse_id' => $this->wareHouses[0]->id]);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];
    $dataReportExpect = [
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => $this->wareHouses[0]->name,
            'ink' => 'ink_color_cc',
            'ink_label' => 'Color Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 0
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => $this->wareHouses[0]->name,
            'ink' => 'ink_white_cc',
            'ink_label' => 'White Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 0
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataReport = $result['dataReport'];

    expect($dataReport)->toMatchArray($dataReportExpect);
});

test('Correctly data return with color type', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast(['warehouse_id' => $this->wareHouses[0]->id, 'color_type' => 'ink_color_cc']);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];
    $dataReportExpect = [
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => $this->wareHouses[0]->name,
            'ink' => 'ink_color_cc',
            'ink_label' => 'Color Ink',
            'months' => [
                '202401' => [
                    'forecast' => 1,
                    'actual' => 0,
                    'variance' => 0
                ],
                '202402' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202403' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202404' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202405' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202406' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202407' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202408' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202409' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202410' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202411' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ],
                '202412' => [
                    'forecast' => '-',
                    'actual' => '-',
                    'variance' => '-'
                ]
            ]
        ],
    ];

    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataReport = $result['dataReport'];

    expect($dataReport)->toMatchArray($dataReportExpect);
});

test('Correctly run cmd', function () {
    $saleOrder = SaleOrder::factory()->create([
        'warehouse_id' => $this->wareHouses[0]->id,
        'order_date' => '2023-02-04',
        'created_at' => '2023-02-04 00:00:00',
    ]);
    SaleOrderItem::factory()->createMany([
        [
            'order_id' => $saleOrder->id,
            'quantity' => 10,
        ],
        [
            'order_id' => $saleOrder->id,
            'quantity' => 20,
        ]
    ]);
    SaleOrderItemImage::factory()->createMany(
        [
            [
                'order_id' => $saleOrder->id,
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 101.5,
                'ink_white_cc' => 11.1,
                'order_date' => '2023-02-04',
                'created_at' => '2023-02-04 00:00:00',
            ],
            [
                'order_id' => $saleOrder->id,
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 202.5,
                'ink_white_cc' => 12.1,
                'order_date' => '2023-02-04',
                'created_at' => '2023-02-04 00:00:00',
            ],
        ],
    );

    $this->artisan(' calculate-supply-ink-report --start=2023-02-04 --end=2023-02-04');
    $supplyInkReport = SupplyInkReport::where('month_report', 202302)
        ->first();

    expect($supplyInkReport)->toBeTruthy()
        ->and($supplyInkReport->warehouse_id)->toBe($this->wareHouses[0]->id)
        ->and($supplyInkReport->ink_color_cc)->toBe(101.5 + 202.5)
        ->and($supplyInkReport->ink_white_cc)->toBe(11.1 + 12.1)
        ->and($supplyInkReport->order_quantity)->toBe(30.0);
});

test('Correctly forecast in feature', function () {
    Carbon::setTestNow(Carbon::parse('2024-06-17'));
    SupplyInkReport::where('id', '>=', 1)->delete();
    SupplyInkReport::factory()->createMany([
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202212,
            'ink_color_cc' => 1000,
            'ink_white_cc' => 5000,
            'order_quantity' => 100,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202301,
            'ink_color_cc' => 1000,
            'ink_white_cc' => 5000,
            'order_quantity' => 100,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202302,
            'ink_color_cc' => 1100,
            'ink_white_cc' => 5100,
            'order_quantity' => 110,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202303,
            'ink_color_cc' => 1200,
            'ink_white_cc' => 5200,
            'order_quantity' => 120,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202304,
            'ink_color_cc' => 1300,
            'ink_white_cc' => 5300,
            'order_quantity' => 130,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202305,
            'ink_color_cc' => 1400,
            'ink_white_cc' => 5400,
            'order_quantity' => 140,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202306,
            'ink_color_cc' => 1500,
            'ink_white_cc' => 5500,
            'order_quantity' => 150,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202307,
            'ink_color_cc' => 1600,
            'ink_white_cc' => 5600,
            'order_quantity' => 160,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202308,
            'ink_color_cc' => 1700,
            'ink_white_cc' => 5700,
            'order_quantity' => 170,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202309,
            'ink_color_cc' => 1800,
            'ink_white_cc' => 5800,
            'order_quantity' => 180,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202310,
            'ink_color_cc' => 1900,
            'ink_white_cc' => 5900,
            'order_quantity' => 190,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202311,
            'ink_color_cc' => 2000,
            'ink_white_cc' => 6000,
            'order_quantity' => 200,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202312,
            'ink_color_cc' => 2100,
            'ink_white_cc' => 6100,
            'order_quantity' => 210,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202401,
            'ink_color_cc' => 2200,
            'ink_white_cc' => 6200,
            'order_quantity' => 220,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202402,
            'ink_color_cc' => 2300,
            'ink_white_cc' => 6300,
            'order_quantity' => 230,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202403,
            'ink_color_cc' => 2400,
            'ink_white_cc' => 6400,
            'order_quantity' => 240,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202404,
            'ink_color_cc' => 2500,
            'ink_white_cc' => 6500,
            'order_quantity' => 250,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202405,
            'ink_color_cc' => 2400,
            'ink_white_cc' => 6400,
            'order_quantity' => 240,
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'month_report' => 202406,
            'ink_color_cc' => 2500,
            'ink_white_cc' => 6500,
            'order_quantity' => 250,
        ],
    ]);

    $result = resolve(SupplyReportRepository::class)->getInkConsumptionForecast(['warehouse_id' => $this->wareHouses[0]->id]);
    $dataMonthTarget = [
        '202401' => 'Jan 2024',
        '202402' => 'Feb 2024',
        '202403' => 'Mar 2024',
        '202404' => 'Apr 2024',
        '202405' => 'May 2024',
        '202406' => 'Jun 2024',
        '202407' => 'Jul 2024',
        '202408' => 'Aug 2024',
        '202409' => 'Sep 2024',
        '202410' => 'Oct 2024',
        '202411' => 'Nov 2024',
        '202412' => 'Dec 2024',
    ];
    expect($result['months'])->toMatchArray($dataMonthTarget);

    $dataExpect = [
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => 'San Jose',
            'ink' => 'ink_color_cc',
            'ink_label' => 'Color Ink',
            'months' => [
                202401 => [
                    'forecast' => 2,
                    'actual' => 2,
                    'variance' => 0,
                ],
                202402 => [
                    'forecast' => 2,
                    'actual' => 2,
                    'variance' => 0,
                ],
                202403 => [
                    'forecast' => 3,
                    'actual' => 2,
                    'variance' => 0,
                ],
                202404 => [
                    'forecast' => 3,
                    'actual' => 3,
                    'variance' => 0,
                ],
                202405 => [
                    'forecast' => 3,
                    'actual' => 2,
                    'variance' => 0,
                ],
                202406 => [
                    'forecast' => 3,
                    'actual' => 3,
                    'variance' => 0,
                ],
                202407 => [
                    'forecast' => 3,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202408 => [
                    'forecast' => 3,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202409 => [
                    'forecast' => 3,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202410 => [
                    'forecast' => 3,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202411 => [
                    'forecast' => 3,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202412 => [
                    'forecast' => 4,
                    'actual' => '-',
                    'variance' => '-',
                ],
            ],
        ],
        [
            'warehouse_id' => $this->wareHouses[0]->id,
            'warehouse' => 'San Jose',
            'ink' => 'ink_white_cc',
            'ink_label' => 'White Ink',
            'months' => [
                202401 => [
                    'forecast' => 11,
                    'actual' => 6,
                    'variance' => 4,
                ],
                202402 => [
                    'forecast' => 11,
                    'actual' => 6,
                    'variance' => 5,
                ],
                202403 => [
                    'forecast' => 11,
                    'actual' => 6,
                    'variance' => 4,
                ],
                202404 => [
                    'forecast' => 11,
                    'actual' => 7,
                    'variance' => 4,
                ],
                202405 => [
                    'forecast' => 10,
                    'actual' => 6,
                    'variance' => 4,
                ],
                202406 => [
                    'forecast' => 9,
                    'actual' => 7,
                    'variance' => 3,
                ],
                202407 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202408 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202409 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202410 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202411 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
                202412 => [
                    'forecast' => 10,
                    'actual' => '-',
                    'variance' => '-',
                ],
            ],
        ],
    ];

    expect($result['dataReport'])->toMatchArray($dataExpect);
});
