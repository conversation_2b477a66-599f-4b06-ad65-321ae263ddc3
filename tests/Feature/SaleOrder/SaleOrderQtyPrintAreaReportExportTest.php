<?php

use App\Exports\SaleOrderQtyPrintAreaReportExport;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

uses(RefreshDatabase::class);

beforeEach(function () {
    setTimezone();
    $this->warehouse = Warehouse::factory()->createMany([
        ['name' => 'warehouse 1'],
        ['name' => 'warehouse 2'],
    ]);
    $this->orders = $orders = SaleOrder::factory()->createMany([
        [
            'warehouse_id' => $this->warehouse[0]->id,
            'is_test' => SaleOrder::NOT_TEST,
            'order_status' => SaleOrder::SHIPPED,
        ],
        [
            'warehouse_id' => $this->warehouse[1]->id,
            'is_test' => SaleOrder::NOT_TEST,
            'order_status' => SaleOrder::SHIPPED,
        ],
    ]);
    SaleOrderItemBarcode::factory()->createMany([
        [
            'order_id' => $orders[1]->id,
            'print_barcode_at' => '2024-04-22 12:00:00',
            'order_item_id' => 1,
        ],
        [
            'order_id' => $orders[0]->id,
            'print_barcode_at' => '2024-05-23 12:00:00',
            'order_item_id' => 2,
        ],
        [
            'order_id' => $orders[0]->id,
            'print_barcode_at' => '2024-05-23 12:00:00',
            'order_item_id' => 3,
        ],
        [
            'order_id' => $orders[0]->id,
            'print_barcode_at' => '2024-05-24 12:00:00',
            'order_item_id' => 4,
        ],
        [
            'order_id' => $orders[1]->id,
            'print_barcode_at' => '2024-05-23 12:00:00',
            'order_item_id' => 5,
        ],
        [
            'order_id' => $orders[1]->id,
            'print_barcode_at' => '2024-05-22 12:00:00',
            'order_item_id' => 6,
        ],
    ]);

    SaleOrderItemImage::factory()->createMany([
        [
            'order_item_id' => 1,
        ],
        [
            'order_item_id' => 2,
        ],
        [
            'order_item_id' => 3,
        ],
        [
            'order_item_id' => 4,
        ],
        [
            'order_item_id' => 5,
        ],
        [
            'order_item_id' => 6,
        ],
    ]);
});

test('exactly export data', function () {
    $params = [
        'start_date' => '2024-04',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[0]->id . ',' . $this->warehouse[1]->id,
    ];
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(3, $collection);
    $headers = $export->headings();
    $this->assertEquals(['Month', 'Name', 'Printing volume'], $headers);
    $dataExpect = [
        [
            'month_report' => '2024-04',
            'w_name' => 'warehouse 2',
            'printing_volume' => 1,
        ],
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 1',
            'printing_volume' => 3,
        ],
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 2',
            'printing_volume' => 2,
        ],
    ];

    expect($collection->toArray())->toMatchArray($dataExpect);
});

test('only export data of sale order with status: SHIPPED', function () {
    $params = [
        'start_date' => '2024-05',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[0]->id . ',' . $this->warehouse[1]->id,
    ];
    SaleOrder::where('id', $this->orders[0]->id)->update(['order_status' => SaleOrder::STATUS_MANUAL_PROCESS]);
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(1, $collection);
    $headers = $export->headings();
    $this->assertEquals(['Month', 'Name', 'Printing volume'], $headers);
    $dataExpect = [
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 2',
            'printing_volume' => 2,
        ]
    ];

    expect($collection->toArray())->toMatchArray($dataExpect);
});

test('only export data of sale order with status: NOT_TEST', function () {
    $params = [
        'start_date' => '2024-05',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[0]->id . ',' . $this->warehouse[1]->id,
    ];
    SaleOrder::where('id', $this->orders[0]->id)->update(['is_test' => SaleOrder::ACTIVE]);
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(1, $collection);
    $headers = $export->headings();
    $this->assertEquals(['Month', 'Name', 'Printing volume'], $headers);
    $dataExpect = [
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 2',
            'printing_volume' => 2,
        ]
    ];

    expect($collection->toArray())->toMatchArray($dataExpect);
});

test('only export data of sale_order_item_barcode.print_barcode_at is not null', function () {
    $params = [
        'start_date' => '2024-05',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[0]->id . ',' . $this->warehouse[1]->id,
    ];
    SaleOrderItemBarcode::where('order_id', $this->orders[0]->id)->update(['print_barcode_at' => null]);
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(1, $collection);
    $headers = $export->headings();
    $this->assertEquals(['Month', 'Name', 'Printing volume'], $headers);
    $dataExpect = [
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 2',
            'printing_volume' => 2,
        ]
    ];

    expect($collection->toArray())->toMatchArray($dataExpect);
});

test('exactly export with search warehouse condition', function () {
    $params = [
        'start_date' => '2024-05',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[1]->id,
    ];
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(1, $collection);

    foreach ($collection as $item) {
        expect($item['w_name'])->toBe($this->warehouse[1]->name);
    }
});

test('exactly export with search date condition', function () {
    $params = [
        'start_date' => '2024-05',
        'end_date' => '2024-05',
        'warehouse_ids' => $this->warehouse[0]->id . ',' . $this->warehouse[1]->id,
    ];
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    $this->assertInstanceOf(Collection::class, $collection);
    $this->assertCount(2, $collection);

    $dataExpect = [
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 1',
            'printing_volume' => 3,
        ],
        [
            'month_report' => '2024-05',
            'w_name' => 'warehouse 2',
            'printing_volume' => 2,
        ]
    ];

    expect($collection->toArray())->toMatchArray($dataExpect);
});

test('exactly export with export no data', function () {
    $params = [
        'start_date' => '2024-04',
        'end_date' => '2024-04',
        'warehouse_ids' => 5,
    ];
    $export = new SaleOrderQtyPrintAreaReportExport($params);
    $collection = $export->collection();

    expect(count($collection->toArray()))->toEqual(0);
});
