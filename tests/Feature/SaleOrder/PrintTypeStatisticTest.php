<?php

use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\SaleOrderRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/sale-order/print-type-statistic?';

    $this->store = Store::factory()->create([
        'username' => 'test',
        'is_calculate_shipping' => true,
        'is_calculate_price' => true,
    ]);
    $this->productStyles = ProductStyle::factory()->createMany([
        [
            'sku' => 'sku1',
            'name' => 'sku1',
        ],
        [
            'sku' => 'sku2',
            'name' => 'sku2',
        ],
        [
            'sku' => 'sku3',
            'name' => 'sku3',
        ],
    ]);
    $this->printSides = ProductPrintSide::factory()->createMany([
        [
            'name' => 'front',
            'code' => '1'
        ],
        [
            'name' => 'back',
            'code' => '2'
        ],
        [
            'name' => 'Left Sleeve',
            'code' => '3',
        ]
    ]);
    $this->printAreas = ProductPrintArea::factory()->createMany([
        [
            'name' => $this->printSides->first()->name,
            'print_method' => 'DTG',
            'product_style_id' => $this->productStyles->first()->id,
        ],
        [
            'name' => $this->printSides[1]->name,
            'print_method' => 'DTF',
            'product_style_id' => $this->productStyles[1]->id,
        ],
        [
            'name' => $this->printSides[2]->name,
            'print_method' => 'DTF',
            'product_style_id' => $this->productStyles->last()->id,
        ]
    ]);

    $this->saleOrder1 = SaleOrder::factory([
        'store_id' => $this->store->id,
        'is_test' => SaleOrder::NOT_TEST,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'created_at' => '2024-12-10 23:00:00',
        'warehouse_id' => $this->warehouse->id,
    ])->has(
        SaleOrderItem::factory()->count(1)->sequence(
            [
                'store_id' => $this->store->id,
                'product_style_sku' => $this->productStyles->first()->sku,
                'quantity' => 3,
            ],
        )->has(
            SaleOrderItemBarcode::factory()->count(3)->sequence(
                [
                    'is_deleted' => 0,
                ],
                [
                    'is_deleted' => 0,
                ],
                [
                    'is_deleted' => 0,
                ],
            ),
            'barcodes',
        )
        ->has(
            SaleOrderItemImage::factory()->count(1)->sequence(
                [
                    'print_side' => $this->printSides->first()->code,
                ],
            ),
            'images',
        ),
        'items',
    )->create();

    $this->saleOrder2 = SaleOrder::factory([
        'store_id' => $this->store->id,
        'is_test' => SaleOrder::NOT_TEST,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'created_at' => '2024-12-11 23:00:00',
        'warehouse_id' => $this->warehouse->id,
    ])->has(
        SaleOrderItem::factory()->count(1)->sequence(
            [
                'store_id' => $this->store->id,
                'product_style_sku' => $this->productStyles->first()->sku,
                'quantity' => 3,
            ],
        )->has(
            SaleOrderItemBarcode::factory()->count(3)->sequence(
                [
                    'is_deleted' => 0,
                    'barcode_number' => 1,
                ],
                [
                    'is_deleted' => 0,
                    'barcode_number' => 2,
                ],
                [
                    'is_deleted' => 0,
                    'barcode_number' => 3,
                ],
            ),
            'barcodes',
        )
        ->has(
            SaleOrderItemImage::factory()->count(2)->sequence(
                [
                    'print_side' => $this->printSides->first()->code,
                ],
                [
                    'print_side' => $this->printSides[1]->code,
                ],
            ),
            'images',
        ),
        'items',
    )->create();

    $this->saleOrder3 = SaleOrder::factory([
        'store_id' => $this->store->id,
        'is_test' => SaleOrder::NOT_TEST,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'created_at' => '2024-12-12 23:00:00',
        'warehouse_id' => $this->warehouse->id,
    ])->has(
        SaleOrderItem::factory()->count(2)->sequence(
            [
                'store_id' => $this->store->id,
                'product_style_sku' => $this->productStyles->first()->sku,
                'quantity' => 3,
            ],
            [
                'store_id' => $this->store->id,
                'product_style_sku' => $this->productStyles->last()->sku,
                'quantity' => 3,
            ],
        )->has(
            SaleOrderItemBarcode::factory()->count(3)->sequence(
                [
                    'is_deleted' => 0,
                    'barcode_number' => 1,
                ],
                [
                    'is_deleted' => 0,
                    'barcode_number' => 2,
                ],
                [
                    'is_deleted' => 0,
                    'barcode_number' => 3,
                ],
            ),
            'barcodes',
        )
        ->has(
            SaleOrderItemImage::factory()->count(2)->sequence(
                [
                    'print_side' => $this->printSides->first()->code,
                ],
                [
                    'print_side' => $this->printSides->last()->code,
                ],
            ),
            'images',
        ),
        'items',
    )->create();
});

test('get data item print method by date exactly date 2024-12-10', function () {
    $collection = app(SaleOrderRepository::class)->getDataItemPrintMethodByDate('2024-12-10');
    $this->assertInstanceOf(Collection::class, $collection);
    expect($collection->count())->toBe(1);
    $firstItem = $collection->first();
    $this->assertTrue(property_exists($firstItem, 'print_method'));
    $this->assertTrue(property_exists($firstItem, 'store_id'));
    $this->assertTrue(property_exists($firstItem, 'warehouse_id'));
    $this->assertTrue(property_exists($firstItem, 'total'));
    $this->assertTrue(property_exists($firstItem, 'order_date'));
    expect($firstItem->total)->toBe(3);
});

test('get data item print method by date exactly date 2024-12-11', function () {
    $collection = app(SaleOrderRepository::class)->getDataItemPrintMethodByDate('2024-12-11');
    $this->assertInstanceOf(Collection::class, $collection);
    expect($collection->count())->toBe(1);
    $firstItem = $collection->first();
    $this->assertTrue(property_exists($firstItem, 'print_method'));
    $this->assertTrue(property_exists($firstItem, 'store_id'));
    $this->assertTrue(property_exists($firstItem, 'warehouse_id'));
    $this->assertTrue(property_exists($firstItem, 'total'));
    $this->assertTrue(property_exists($firstItem, 'order_date'));
    expect($firstItem->print_method)->toBe('DTG');
    expect($firstItem->total)->toBe(3);
});

test('get data item print method by date exactly date 2024-12-12 and has sleeve type', function () {
    $collection = app(SaleOrderRepository::class)->getDataItemPrintMethodByDate('2024-12-12');
    $this->assertInstanceOf(Collection::class, $collection);
    expect($collection->count())->toBe(3);

    //DTF
    $firstItem = $collection->first();
    $this->assertTrue(property_exists($firstItem, 'print_method'));
    $this->assertTrue(property_exists($firstItem, 'store_id'));
    $this->assertTrue(property_exists($firstItem, 'warehouse_id'));
    $this->assertTrue(property_exists($firstItem, 'total'));
    $this->assertTrue(property_exists($firstItem, 'order_date'));
    expect($firstItem->print_method)->toBe('DTF');
    expect($firstItem->total)->toBe(3);

    //DTG
    $secondItem = $collection[1];
    $this->assertTrue(property_exists($secondItem, 'print_method'));
    $this->assertTrue(property_exists($secondItem, 'store_id'));
    $this->assertTrue(property_exists($secondItem, 'warehouse_id'));
    $this->assertTrue(property_exists($secondItem, 'total'));
    $this->assertTrue(property_exists($secondItem, 'order_date'));
    expect($secondItem->print_method)->toBe('DTG');
    expect($secondItem->total)->toBe(3);

    //SLEEVE
    $lastItem = $collection->last();
    $this->assertTrue(property_exists($lastItem, 'print_method'));
    $this->assertTrue(property_exists($lastItem, 'store_id'));
    $this->assertTrue(property_exists($lastItem, 'warehouse_id'));
    $this->assertTrue(property_exists($lastItem, 'total'));
    $this->assertTrue(property_exists($lastItem, 'order_date'));
    expect($lastItem->print_method)->toBe('SLEEVE');
    expect($lastItem->total)->toBe(3);
});

test('get data print type statistic success', function () {
    app(SaleOrderRepository::class)->coverDataItemPrintMethodByDate('2024-12-10');
    app(SaleOrderRepository::class)->coverDataItemPrintMethodByDate('2024-12-11');
    $this->params = [
        'warehouse' => $this->warehouse->id,
        'start_date' => '2024-12-10',
        'end_date' => '2024-12-12'
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->get($this->endpoint . http_build_query($this->params));

    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    $firstItem = $data[0];
    $this->assertArrayHasKey('print_method', $firstItem);
    $this->assertArrayHasKey('total_order_item', $firstItem);
});
