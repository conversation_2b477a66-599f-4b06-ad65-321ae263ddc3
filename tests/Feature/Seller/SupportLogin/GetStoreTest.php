<?php

use App\Models\Store;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    Store::factory()->count(5)->sequence(
        ['is_active' => true],
        ['is_active' => true],
        ['is_active' => false],
        ['is_active' => true],
        ['is_active' => false],
    )->create();
    $this->user = User::factory()->create();
    $this->user->stores()->sync(Store::all()->pluck('id'));
    $this->user->save();
    $response = $this->post('/api/seller/login', ['email' => '<EMAIL>', 'password' => 'test1337', 'support_login' => true]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];

    $this->endpoint = '/api/seller/get-store';
});

// get store success
test('get store success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(3, $response);
    foreach ($response as $key => $value) {
        expect($value)->toHaveKeys(['id', 'name']);
    }
});

// get store success - not set store in user not admin-> not show store active
test('get store success - not set store user', function () {
    User::find($this->user->id)->stores()->detach();
    Store::factory()->count(3)->sequence(
        ['is_active' => true],
        ['is_active' => false],
        ['is_active' => false],
    )->create();
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertCount(0, $response);
});
