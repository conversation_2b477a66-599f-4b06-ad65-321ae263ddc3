<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->endpoint = '/api/seller/login';
});

// UT login
test('login success', function () {
    $response = $this->post($this->endpoint, ['email' => '<EMAIL>', 'password' => 'test1337', 'support_login' => true]);
    $response->assertStatus(200)->assertJsonStructure([
        'access_token',
        'token_type',
        'user'
    ]);
});

test('login failed - missing email or password', function () {
    $response = $this->post($this->endpoint, ['email' => '<EMAIL>', 'support_login' => true]);
    $response->assertStatus(422)->assertJsonStructure([
        'password'
    ]);

    $response = $this->post($this->endpoint, ['password' => 'test1337', 'support_login' => true]);
    $response->assertStatus(422)->assertJsonStructure([
        'email'
    ]);
});

//không có biến support login - sẽ hiểu là đăng nhập store thông thường: miss param username
test('login failed - missing support_login', function () {
    $response = $this->post($this->endpoint, ['email' => '<EMAIL>', 'password' => 'test1337']);
    $response->assertStatus(422)->assertJsonStructure([
        'username'
    ]);
});

test('login failed - wrong email', function () {
    $response = $this->post($this->endpoint, ['email' => '<EMAIL>', 'password' => 'test1337', 'support_login' => true]);
    $response->assertStatus(401)->assertJsonStructure([
        'error'
    ]);
});

test('login failed - wrong password', function () {
    $response = $this->post($this->endpoint, ['email' => '<EMAIL>', 'password' => '1234567', 'support_login' => true]);
    $response->assertStatus(401)->assertJsonStructure([
        'error'
    ]);
});
