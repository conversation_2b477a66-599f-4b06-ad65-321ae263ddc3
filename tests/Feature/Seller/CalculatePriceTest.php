<?php

use App\Console\Commands\CalculatePriceForSeller;
use App\Models\Client;
use App\Models\Product;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderCalculateFailed;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderSurchargeFee;
use App\Models\Shipment;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use App\Models\Tag;
use App\Repositories\SaleOrderPricingRepository;
use Illuminate\Console\OutputStyle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\NullOutput;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'is_calculate_shipping' => true, 'is_calculate_price' => true, 'client_id' => $this->client->id]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456']);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];

    $this->productStyle = ProductStyle::factory()->count(2)->sequence(
        ['type' => 'TEE', 'sku' => '3001', 'name' => 'style A'],
        ['type' => 'FLEECE', 'sku' => '5001', 'name' => 'style B'],
    )->create();

    $this->printArea = ProductPrintArea::factory()->count(6)->sequence(
        ['name' => 'Front', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Front', 'product_style_id' => $this->productStyle[1]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[1]->id],
        ['name' => 'Right Sleeve', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Right Sleeve', 'product_style_id' => $this->productStyle[1]->id],
    )->create();

    $this->productPrintSide = ProductPrintSide::factory()->count(3)->sequence(
        ['name' => 'Front', 'code' => 0, 'code_wip' => 'F', 'code_name' => 'front'],
        ['name' => 'Back', 'code' => 1, 'code_wip' => 'B', 'code_name' => 'back'],
        ['name' => 'Right Sleeve', 'code' => 4, 'code_wip' => 'R', 'code_name' => 'right_sleeve'],

    )->create();

    $this->product = Product::factory()->count(2)->sequence(
        ['name' => '3001 / AQUA / XS', 'sku' => 'PRODUCT1', 'style' => $this->productStyle[0]->name],
        ['name' => '5001 / WHITE / XL', 'sku' => 'PRODUCT2', 'style' => $this->productStyle[1]->name],
    )->create();

    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 1.5],
    )->create();

    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 0.5],
    )->create();

    $this->storeShipment = StoreShipment::factory()->count(6)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 9,
            'addition_price' => 6,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 8,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 30,
            'addition_price' => 25,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 35,
            'addition_price' => 25,
        ],
    )->create();
    $this->surchargeService = SurchargeService::factory()->count(11)->sequence(
        ['name' => SurchargeService::TYPE_HANDLING, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_HANDLING],
        ['name' => SurchargeService::TYPE_TIKTOK_ORDER_SERVICE, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_TIKTOK_FEE],
        ['name' => SurchargeService::TYPE_LABEL_PRINTING_FEE, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_LABEL_PRINTING_FEE],
        ['name' => SurchargeService::TYPE_PLASTIC_BAG, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_PLASTIC_BAG],
        ['name' => SurchargeService::TYPE_MUG_PACKAGING, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Mug', 'api_value' => SurchargeService::API_VALUE_MUG_PACKAGING],
        ['name' => SurchargeService::TYPE_HOLOGRAM_STICKER, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Tee,Fleece,BIB,SweatPants,Tote Bag,CAP,Short,Jacket,Tank', 'api_value' => SurchargeService::API_VALUE_HOLOGRAM_STICKER],
        ['name' => SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_STICKER_AND_BAG],
        ['name' => SurchargeService::TYPE_EMBROIDERY_10001_TO_15000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_15001_TO_20000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_20001_TO_25000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_25001_TO_30000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES],
    )->create();

    SurchargeFee::factory()->count(11)->sequence(
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_HANDLING)->id,
            'value' => 2.5,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id,
            'value' => 10.5,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id,
            'value' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id,
            'value' => 3,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id,
            'value' => 4,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id,
            'value' => 4,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_HOLOGRAM_STICKER)->id,
            'value' => 2,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES)->id,
            'value' => 0.5,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES)->id,
            'value' => 1,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES)->id,
            'value' => 1.5,
        ],
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES)->id,
            'value' => 2,
        ],
    )->create();

    Tag::factory()->count(5)->sequence(
        [
            'name' => Tag::TAG_LABEL,
        ],
        [
            'name' => Tag::TAG_PLASTIC_BAG,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id,
        ],
        [
            'name' => Tag::TAG_HOLOGRAM_STICKER,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_HOLOGRAM_STICKER)->id,
        ],
        [
            'name' => Tag::TAG_STICKER_AND_PLASTIC_BAG,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id,
        ],
        [
            'name' => Tag::TAG_MUG_PACKAGING,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id,
        ],
    )->create();
    $this->handlingFeeByStore = SurchargeFee::first();
    $this->command = new CalculatePriceForSeller();
    $this->command->setOutput(new OutputStyle(new ArgvInput(), new NullOutput()));

    $this->promotionType = \App\Models\PromotionType::create([
        'id' => 1,
        'name' => 'Additional Print Area',

    ]);
});

// store chưa được set giá cho các product
test('calculate failed - not set price product in store', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(2)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);

    $saleOrderItemResult = SaleOrderItem::where('product_id', $product->id)
        ->where('amount_paid', 0)
        ->where('unit_price', 0)
        ->where('handling_fee', 0)
        ->where('blank_price', 0)
        ->first();
    $this->assertNotNull($saleOrderItemResult);
});

// order đã được tính giá
test('calculate failed - order calculated', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'calculated_at' => '2022-10-18 00:00:00',
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->where('calculated_at', '2022-10-18 00:00:00')
        ->first();
    $this->assertNotNull($saleOderResult);
});

// order có trạng thái khác shipped & late cancelled
test('calculate failed - order status different shipped or late cancelled', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::NEW_ORDER,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);
});

// order không có địa chỉ ship
test('calculate failed - order address not found', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => $this->productStyle[0]->sku,
                    'product_size_sku' => 'S',
                    'print_sides' => 'FB',
                    'options' => '[
                          {
                            "name": "PrintFiles.Front",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/eef44000-5d88-447e-82fe-2ec8de30e59a.png"
                          },
                          {
                            "name": "PrintFiles.Back",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/a5d3191d-8569-441e-b35f-611249420662.png"
                          }
                        ]'
                ],
            )->has(ProductStyle::factory([
                'type' => 'FLEECE',
            ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);

    $this->command->calculate();
    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);
    $saleOrderItemResult = SaleOrderItem::where('product_id', $product->id)
        ->where('amount_paid', 0)
        ->where('unit_price', 0)
        ->where('handling_fee', 0)
        ->where('blank_price', 0)
        ->first();
    $this->assertNotNull($saleOrderItemResult);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)
        ->first();
    $this->assertNotNull($saleOrderCalculateFailed);
});

// Chưa set giá ship
test('calculate failed - store shipment not found', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_EXPRESS,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                    'options' => '[
                          {
                            "name": "PrintFiles.Front",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/eef44000-5d88-447e-82fe-2ec8de30e59a.png"
                          },
                          {
                            "name": "PrintFiles.Back",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/a5d3191d-8569-441e-b35f-611249420662.png"
                          }
                        ]'
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->has(
            SaleOrderItem::factory(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 10,
                    'product_id' => $this->product->last()->id,
                    'product_style_sku' => '6000',
                    'product_size_sku' => 'XL',
                    'options' => '[
                          {
                            "name": "PrintFiles.Front",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/eef44000-5d88-447e-82fe-2ec8de30e59a.png"
                          },
                          {
                            "name": "PrintFiles.Back",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/a5d3191d-8569-441e-b35f-611249420662.png"
                          }
                        ]'
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'not found',  // type not exist
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);
    // trong 2 item , có 1 item không tính được giá do không lấy được giá ship -> sẽ lưu lại log
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderCalculateFailed);
});

//store không cần tính giá ship (free ship)
test('calculate success - free ship', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $this->product->first()->id,
                    'product_style_sku' => $this->productStyle[0]->sku,
                    'product_size_sku' => 'S',
                    'print_sides' => 'FB',
                    'options' => '[
                          {
                            "name": "PrintFiles.Front",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/eef44000-5d88-447e-82fe-2ec8de30e59a.png"
                          },
                          {
                            "name": "PrintFiles.Back",
                            "value": "https://s3-hn-1.mangoteeprints.com/products/a5d3191d-8569-441e-b35f-611249420662.png"
                          }
                        ]'
                ],
            )->has(ProductStyle::factory([
                'type' => 'FLEECE',
            ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();
    Store::query()->update(['is_calculate_shipping' => false]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 180)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 180)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $saleOrder->id)
        ->where('unit_price', 9)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 180)
        ->first();
    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//international order
test('calculate success - international order', function () {
    // Tạo đơn hàng với thông tin quốc tế
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    SaleOrderAddress::where('order_id', $saleOrder->id)->update(['country' => 'VN']); // Cập nhật quốc gia nếu cần
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 650)
        ->where('shipping_calculate', 510)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//Không tính giá ship cho late cancelled order
test('calculate success - free ship for late cancelled order', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    SaleOrder::where('id', $saleOrder->id)->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 140)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//order gooten=> shipping fee = 0
test('calculate success - gooten order =>shipping free = 0', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    Shipment::where('id', $saleOrder->shipment_id)->update(['shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD_STORE]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 160)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 160)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 160)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//order labeling => shipping fee = 0
test('calculate success - order labeling', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    SaleOrder::where('id', $saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 160)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 160)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 160)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);

    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($labelFeeOfStore->value);
});

test('calculate success - order labeling has order_type = 1 and tag is label', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
    SaleOrder::where('id', $saleOrder->id)->update(
        [
            'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
            'tag' => "$tagLabel->id"
        ],
    );

    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 160)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 160)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 160)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($labelFeeOfStore->value);
});

test('calculate success - order tiktok', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle, StoreShipment::SERVICE_STANDARD, 'US');
    SaleOrder::where('id', $saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 263)
        ->where('shipping_calculate', 123)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNull($saleOrderSurcharge);
});

test('calculate success - order has plastic bag (plastic_bag = true)', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    SaleOrder::where('id', $saleOrder->id)->update(['plastic_bag' => true]);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 263)
        ->where('shipping_calculate', 123)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
        $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
            ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id)->first();
        $saleOrderSurcharge = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $plasticBagFee->id)->first();
        $this->assertNotNull($saleOrderSurcharge);
        expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value * $orderItem->quantity);
    }
});

test('calculate success - order has plastic bag (tag has plastic_bag)', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    $tagPlasticBag = Tag::where('name', Tag::TAG_PLASTIC_BAG)->first();

    SaleOrder::where('id', $saleOrder->id)->update(
        [
            'tag' => "$tagPlasticBag->id"
        ],
    );
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 263)
        ->where('shipping_calculate', 123)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);

    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
        $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
            ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id)->first();
        $saleOrderSurcharge = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $plasticBagFee->id)->first();
        $this->assertNotNull($saleOrderSurcharge);
        expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value * $orderItem->quantity);
    }
});

test('calculate success - order has tag is sticker_and_bag', function () {
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    $tagStickerAndBag = Tag::where('name', Tag::TAG_STICKER_AND_PLASTIC_BAG)->first();
    SaleOrder::where('id', $saleOrder->id)->update(
        [
            'tag' => "$tagStickerAndBag->id"
        ],
    );
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 263)
        ->where('shipping_calculate', 123)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);

    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
    }
    $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $saleOrder->id)
        ->where('surcharge_id', $plasticBagFee->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value);
});

test('calculate success - order has tag is mug_packing and product_type is Mug', function () {
    $productStyle = ProductStyle::first();
    $productStyle->type = 'Mug';
    $productStyle->save();
    StoreShipment::factory()->count(2)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'Mug',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'Mug',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 10,
            'addition_price' => 5,
        ],
    )->create();
    $tagMugPacking = Tag::where('name', Tag::TAG_MUG_PACKAGING)->first();
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    SaleOrder::where('id', $saleOrder->id)->update(
        [
            'tag' => "$tagMugPacking->id"
        ],
    );
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 263)
        ->where('shipping_calculate', 123)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $mugPackagingFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $mugPackagingFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $mugPackagingFeeOfStore->id)->first();
        if ($orderItem->getTypeProduct->type == 'Mug') {
            $this->assertNotNull($mugPackagingFee);
            expect($mugPackagingFee->value)->toEqual($mugPackagingFeeOfStore->value * $orderItem->quantity);
        } else {
            $this->assertNull($mugPackagingFee);
        }
    }
});

test('calculate success - one item of order has shipping price via size', function () {
    StoreShipment::where('store_id', $this->store->id)->delete();
    StoreShipment::factory()->count(2)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 15,
            'addition_price' => 8,
            'size' => 'S'
        ],

    )->create();
    $saleOrder = createSaleOrder($this->store, $this->product, $this->productStyle);
    $saleOrderData = SaleOrder::with([
        'items',
        'items.getTypeProduct',
        'addressSaleOrder' => function ($query) {
            $query->where('type_address', SaleOrderAddress::TO_ADDRESS);
        }
    ])->where('id', $saleOrder->id)->first();
    $saleOrderPricingRepo = app()->make(SaleOrderPricingRepository::class);
    $saleOrderPricingRepo->saveOrderPricingSnapshot($saleOrderData);
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $saleOrder->id)
        ->where('order_total', 307)
        ->where('shipping_calculate', 167)
        ->where('amount_paid', 140)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 140)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
});
function createSaleOrder($store, $product, $productStyle, $shippingMethod = StoreShipment::SERVICE_STANDARD, $country = 'US')
{
    $saleOrder = SaleOrder::factory([
        'store_id' => $store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => $shippingMethod,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $store->id,
                    'quantity' => 20,
                    'product_id' => $product->last()->id,
                    'product_style_sku' => $productStyle[1]->sku,
                    'product_size_sku' => 'S',
                    'print_sides' => 'FB',
                    'options' => json_encode([
                        ['name' => 'PrintFiles.Front', 'value' => 'https://s3-hn-1.mangoteeprints.com/products/eef44000-5d88-447e-82fe-2ec8de30e59a.png'],
                        ['name' => 'PrintFiles.Back', 'value' => 'https://s3-hn-1.mangoteeprints.com/products/a5d3191d-8569-441e-b35f-611249420662.png'],
                    ]),
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'), 'items',
        )
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => $country,
        ]), 'address')
        ->for(Shipment::factory([
            'store_id' => $store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    return $saleOrder;
}
