<?php

use App\Models\Client;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456', 'root_username' => $this->client->username]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];

    $this->saleOrder = SaleOrder::factory([
        'id' => 20000,
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'encode_id' => 20000,
    ])
        ->has(
            SaleOrderItem::factory()->count(2)->sequence([
                'product_id' => 2,
                'store_id' => $this->store->id,
            ], [
                'product_id' => 1,
                'store_id' => $this->store->id,
            ]),
            'items',
        )
        ->has(SaleOrderItemBarcode::factory()->count(2)->sequence([
            'store_id' => $this->store->id,
            'is_deleted' => false,
        ]), 'barcodeItems')
        ->has(SaleOrderItemImage::factory()->count(2)->sequence([
            'delete_status' => false,
        ]), 'imageItems')
        ->create();

    $this->endpoint = '/api/seller/orders/cancel-order/' . $this->saleOrder->encode_id;
});

// truyền sai order id
test('cancel order fail - id error', function () {
    $endpoint = '/api/seller/orders/cancel-order/20';

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(404);
});

//không có order id trong store
test('cancel order fail - id not found in store ', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => 20,
        'order_status' => SaleOrder::NEW_ORDER,
    ])->create();
    $endpoint = '/api/seller/orders/cancel-order/' . $saleOrder->id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(404);
});

// trạng thái order không phải New_Order hoặc Draft
test('cancel order fail - order status error', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => 20,
        'order_status' => SaleOrder::SHIPPED,
    ])->create();
    $endpoint = '/api/seller/orders/cancel-order/' . $saleOrder->id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(404);
});

// no permission
test('cancel order fail - no permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::NO_PERMISSION,
    ]);

    $endpoint = '/api/seller/orders/cancel-order/' . $this->saleOrder->id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(403);
});

test('cancel order fail - view permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
    ]);

    $endpoint = '/api/seller/orders/cancel-order/' . $this->saleOrder->id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(403);
});

// Không có order item
test('cancel order success - no order item', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::DRAFT,
        'encode_id' => 2000000,
    ])
    ->create();

    $endpoint = '/api/seller/orders/cancel-order/' . $saleOrder->encode_id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(200);
    $saleOrderTest = SaleOrder::find($saleOrder->id);
    $this->assertNotEmpty($saleOrderTest);
    $this->assertNotNull($saleOrderTest->cancelled_at);
    $this->assertEquals($saleOrderTest->order_status, SaleOrder::CANCELLED);

    $saleOrderHistory = SaleOrderHistory::where('order_id', $saleOrder->id)
        ->where('type', SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE)
        ->get();
    $this->assertNotEmpty($saleOrderHistory);
    $this->assertCount(1, $saleOrderHistory);
})->skip();

// Không có order item barcode
test('cancel order success - no order item barcode', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::DRAFT,
        'encode_id' => 200,
    ])
        ->has(SaleOrderItem::factory([
            'product_id' => 2,
            'store_id' => $this->store->id,
        ]), 'items')
        ->has(SaleOrderItemImage::factory([
            'delete_status' => false,
        ]), 'imageItems')
        ->create();

    $endpoint = '/api/seller/orders/cancel-order/' . $saleOrder->encode_id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(200);
    $saleOrderTest = SaleOrder::find($saleOrder->id);
    $this->assertNotEmpty($saleOrderTest);
    $this->assertNotNull($saleOrderTest->cancelled_at);
    $this->assertEquals($saleOrderTest->order_status, SaleOrder::CANCELLED);

    $saleOrderItem = SaleOrderItem::where('order_id', $saleOrder->id)
        ->where('is_deleted', true)
        ->get();
    $this->assertCount(1, $saleOrderItem);

    $saleOrderItemImage = SaleOrderItemImage::where('order_id', $saleOrder->id)
        ->where('delete_status', true)
        ->get();
    $this->assertCount(1, $saleOrderItemImage);

    $saleOrderHistory = SaleOrderHistory::where('order_id', $saleOrder->id)
        ->where('type', SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE)
        ->get();
    $this->assertNotEmpty($saleOrderHistory);
    $this->assertCount(1, $saleOrderHistory);
})->skip();

// Không có order item image
test('cancel order success - no order item image', function () {
    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'encode_id' => 200
    ])
        ->has(SaleOrderItem::factory([
            'product_id' => 2,
            'store_id' => $this->store->id,
        ]), 'items')
        ->has(SaleOrderItemBarcode::factory([
            'store_id' => $this->store->id,
            'is_deleted' => false,
        ]), 'barcodeItems')
        ->create();

    $endpoint = '/api/seller/orders/cancel-order/' . $saleOrder->encode_id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($endpoint);

    $response->assertStatus(200);
    $saleOrderTest = SaleOrder::find($saleOrder->id);
    $this->assertNotEmpty($saleOrderTest);
    $this->assertNotNull($saleOrderTest->cancelled_at);
    $this->assertEquals($saleOrderTest->order_status, SaleOrder::CANCELLED);

    $saleOrderItem = SaleOrderItem::where('order_id', $saleOrder->id)
        ->where('is_deleted', true)
        ->get();
    $this->assertCount(1, $saleOrderItem);

    $saleOrderItemBarcode = SaleOrderItemBarcode::where('order_id', $saleOrder->id)
        ->where('is_deleted', true)
        ->get();
    $this->assertCount(1, $saleOrderItemBarcode);

    $saleOrderHistory = SaleOrderHistory::where('order_id', $saleOrder->id)
        ->where('type', SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE)
        ->get();
    $this->assertNotEmpty($saleOrderHistory);
    $this->assertCount(1, $saleOrderHistory);
})->skip();

// cancel order success
test('get order success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->put($this->endpoint);

    $response->assertStatus(200);

    $saleOrder = SaleOrder::find($this->saleOrder->id);
    $this->assertNotEmpty($saleOrder);
    $this->assertNotNull($saleOrder->cancelled_at);
    $this->assertEquals($saleOrder->order_status, SaleOrder::CANCELLED);

    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)
        ->where('is_deleted', true)
        ->get();
    $this->assertCount(2, $saleOrderItem);

    $saleOrderItemBarcode = SaleOrderItemBarcode::where('order_id', $this->saleOrder->id)
        ->where('is_deleted', true)
        ->get();
    $this->assertCount(2, $saleOrderItemBarcode);

    $saleOrderItemImage = SaleOrderItemImage::where('order_id', $this->saleOrder->id)
        ->where('delete_status', true)
        ->get();
    $this->assertCount(2, $saleOrderItemImage);

    $saleOrderHistory = SaleOrderHistory::where('order_id', $this->saleOrder->id)
        ->where('type', SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE)
        ->get();
    $this->assertNotEmpty($saleOrderHistory);
    $this->assertCount(1, $saleOrderHistory);
})->skip();
