<?php

use App\Models\Client;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456', 'root_username' => $this->client->username]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->tracking_url = 'https://www.ups.com/track?tracknumber={tracking_code}';
    $this->saleOrder = SaleOrder::factory([
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::SHIPPED,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_PRIORITY,
        'encode_id' => 2000,
    ])
        ->has(
            SaleOrderItem::factory()->count(2)->sequence([
                'product_id' => 2,
                'quantity' => 1,
                'store_id' => $this->store->id,
                'warehouse_id' => 1,
                'product_style_sku' => 'UNPT',
                'unit_price' => 2.5,
                'blank_price' => 3.5,
                'handling_fee' => 4.6,
            ], [
                'product_id' => 1,
                'quantity' => 3,
                'store_id' => $this->store->id,
                'warehouse_id' => 1,
                'product_style_sku' => 'WORB',
                'unit_price' => 2.0,
                'blank_price' => 3.1,
                'handling_fee' => 0.5,
            ])
                ->has(
                    SaleOrderItemImage::factory([
                        'warehouse_id' => 1,
                        'store_id' => $this->store->id,
                        'print_side' => 0,
                    ])
                        ->has(ProductPrintSide::factory([
                            'name' => 'Fleece',
                        ]), 'printSizeType'),
                    'images',
                )
                ->has(ProductStyle::factory([
                    'name' => '5100',
                ]), 'getTypeProduct')
                ->has(Product::factory([
                    'name' => 'product',
                ]), 'product'),
            'items',
        )
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
        ]), 'address')
        ->has(
            Shipment::factory([
                'store_id' => $this->store->id,
            ])
                ->for(ShippingCarrier::factory([
                    'name' => 'USPS',
                    'code' => 'USPS',
                    'tracking_url' => $this->tracking_url,
                ]), 'shippingCarrier')
                ->for(ShippingCarrierService::factory([
                    'display_name' => 'carrier-service',
                ]), 'shippingCarrierService'),
            'shipment',
        )
        ->create();

    $this->endpoint = '/api/seller/orders/detail/' . $this->saleOrder->encode_id;

    $this->result = [
        'encode_id',
        'key',
        'external_number',
        'order_status',
        'order_date',
        'created_at',
        'calculated_at',
        'shipping_calculate',
        'shipping_method',
        'quantity',
        'address',
        'shipment',
        'items',
    ];
    $this->resultAddress = [
        'id',
        'order_id',
        'name',
        'phone',
        'street1',
        'street2',
        'city',
        'state',
        'zip',
        'country',
    ];
    $this->resultShipment = [
        'id',
        'order_id',
        'ship_date',
        'tracking_number',
        'carrier_code',
        'service_code',
        'shipping_carrier',
        'shipping_carrier_service',
    ];
    $this->resultItem = [
        'name',
        'product_sku',
        'sku',
        'quantity',
        'product_id',
        'options',
        'unit_price',
        'blank_price',
        'handling_fee',
        'images',
        'product_style_sku',
        'get_type_product',
        'product',
    ];
});

//member has no permission
test('get order detail fail - member has no permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::NO_PERMISSION,
    ]);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(403);
});
// id error - truyền sai id
test('get order detail fail - id error', function () {
    $endpoint = '/api/seller/orders/detail/20';
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);

    $response->assertStatus(404);
});

// id error - truyền id của store khác
test('get order detail fail - id store another', function () {
    $saleOrder = SaleOrder::factory([
        'warehouse_id' => 1,
        'store_id' => 20,
        'order_status' => SaleOrder::NEW_ORDER,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_EXPRESS,
        'encode_id' => 20,
    ])->create();

    $endpoint = '/api/seller/orders/detail/' . $saleOrder->encode_id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);

    $response->assertStatus(404);
});

// get order success - full data
test('get order detail success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys($this->result);
    expect($response['address'])->toHaveKeys($this->resultAddress);
    foreach ($response['shipment'] as $key => $shipment) {
        expect($shipment)->toHaveKeys($this->resultShipment);
        expect($shipment['shipping_carrier'])->toHaveKeys(['id', 'code', 'name']);
        expect($shipment['shipping_carrier_service'])->toHaveKeys(['id', 'display_name', 'name']);
        expect($shipment['url'])->toEqual(str_replace('{tracking_code}', $shipment['tracking_number'], $this->tracking_url));
    }
    foreach ($response['items'] as $key => $item) {
        expect($item)->toHaveKeys($this->resultItem);
        expect($item['get_type_product'])->toHaveKeys(['sku', 'name']);
        foreach ($item['images'] as $keyImage => $image) {
            expect($image)->toHaveKeys(['id', 'order_item_id', 'image_url', 'image_width', 'image_height', 'print_side', 'print_size_type']);
        }
    }
});

// get order success - no item
test('get order detail success - no item', function () {
    $saleOrder = SaleOrder::factory([
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_EXPRESS,
        'encode_id' => 20000000,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
        ]), 'address')
        ->has(
            Shipment::factory([
                'store_id' => $this->store->id,
            ])
                ->for(ShippingCarrier::factory([
                    'name' => 'carrier',
                ]), 'shippingCarrier')
                ->for(ShippingCarrierService::factory([
                    'display_name' => 'carrier-service',
                ]), 'shippingCarrierService'),
            'shipment',
        )
        ->create();
    $endpoint = '/api/seller/orders/detail/' . $saleOrder->encode_id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys($this->result);
    expect($response['address'])->toHaveKeys($this->resultAddress);
    foreach ($response['shipment'] as $key => $shipment) {
        expect($shipment)->toHaveKeys($this->resultShipment);
        expect($shipment['shipping_carrier'])->toHaveKeys(['id', 'code', 'name']);
        expect($shipment['shipping_carrier_service'])->toHaveKeys(['id', 'display_name', 'name']);
    }
    expect($response['items'])->toBeEmpty();
});

// get order success - no shipment - address
test('get order detail success - no shipment', function () {
    $saleOrder = SaleOrder::factory([
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::REJECTED,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
        'encode_id' => 20,
    ])
        ->has(SaleOrderItem::factory()->count(2)->sequence([
            'product_id' => 3,
            'quantity' => 2,
            'store_id' => $this->store->id,
            'warehouse_id' => 1,
            'product_style_sku' => 'UNPT',
            'unit_price' => 2.5,
            'blank_price' => 3.5,
            'handling_fee' => 4.6,
        ]), 'items')
    ->create();
    $endpoint = '/api/seller/orders/detail/' . $saleOrder->encode_id;

    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys($this->result);
    expect($response['address'])->toBeEmpty();
    expect($response['shipment'])->toBeEmpty();

    foreach ($response['items'] as $key => $item) {
        expect($item)->toHaveKeys($this->resultItem);
        expect($item['get_type_product'])->toHaveKeys(['sku', 'name']);
        foreach ($item['images'] as $keyImage => $image) {
            expect($image)->toHaveKeys(['id', 'order_item_id', 'image_url', 'image_width', 'image_height', 'print_side', 'print_size_type']);
        }
    }
});
