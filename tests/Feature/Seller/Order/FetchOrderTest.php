<?php

use App\Models\Client;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use App\Models\Store;
use App\Models\Tag;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456', 'root_username' => $this->client->username]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->endpoint = '/api/seller/orders';
    $this->tracking_url = 'https://www.ups.com/track?tracknumber={tracking_code}';
    $this->saleOrder = SaleOrder::factory([
        'order_number' => '100422-SJ-S-000060',
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'external_number' => '000060',
        'order_quantity' => 4,
    ])
        ->has(SaleOrderItem::factory()->count(2)->sequence([
            'product_id' => 2,
            'quantity' => 1,
        ], [
            'quantity' => 3,
            'product_id' => 1,
        ]), 'items')
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
        ]), 'address')
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'tracking_number' => faker::create()->userName(),
        ])
            ->for(ShippingCarrier::factory([
                'name' => 'USPS',
                'code' => 'USPS',
                'tracking_url' => $this->tracking_url,
            ]), 'shippingCarrier')
            ->for(ShippingCarrierService::factory([
                'display_name' => 'carrier-service',
            ]), 'shippingCarrierService'), 'shipmentDefault')
        ->create();

    SaleOrder::factory()->count(10)->sequence(
        [
            'order_number' => '100422-SJ-S-000059',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::SHIPPED,
            'store_id' => $this->store->id,
            'external_number' => '000059',
        ],
        [
            'order_number' => '100422-SJ-S-000061',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::CANCELLED,
            'store_id' => $this->store->id,
            'external_number' => '000061',
        ],
        [
            'order_number' => '100422-SJ-S-000062',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::IN_PRODUCTION,
            'store_id' => $this->store->id,
            'external_number' => '000062',
        ],
        [
            'order_number' => '100422-SJ-S-000063',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::ON_HOLD,
            'store_id' => $this->store->id,
            'external_number' => '000063',
        ],
        [
            'order_number' => '100422-SJ-S-000064',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::DRAFT,
            'store_id' => $this->store->id,
            'external_number' => '000064',
        ],
        [
            'order_number' => '100422-SJ-S-000065',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::REJECTED,
            'store_id' => $this->store->id,
        ],
        [
            'order_number' => '100422-SJ-S-000066',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::MANUAL_PROCESS,
            'store_id' => $this->store->id,
        ],
        [
            'order_number' => '100422-SJ-S-000067',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::REJECTED,
            'store_id' => $this->store->id,
        ],
        [
            'order_number' => '100422-SJ-S-000068',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::MANUAL_PROCESS,
            'store_id' => $this->store->id,
        ],
        [
            'order_number' => '100422-SJ-S-000069',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::MANUAL_PROCESS,
            'store_id' => $this->store->id,
        ],
        [
            'order_number' => '100422-SJ-S-000070',
            'warehouse_id' => 2,
            'order_status' => SaleOrder::NEW_ORDER,
            'store_id' => 3,
        ],
        [
            'order_number' => '100422-SJ-S-000071',
            'warehouse_id' => 3,
            'order_status' => SaleOrder::MANUAL_PROCESS,
            'store_id' => 2,
        ],
        [
            'order_number' => '100422-SJ-S-000072',
            'warehouse_id' => 1,
            'order_status' => SaleOrder::SHIPPED,
            'store_id' => 4,
        ],
    )->create();

    $this->result = [
        'encode_id',
        'key',
        'external_number',
        'order_status',
        'created_at',
        'quantity',
        'address',
        'shipment_default',
        'amount_paid',
        'shipping_calculate',
    ];

    $this->resultShipment = [
        'id',
        'tracking_number',
        'carrier_code',
        'url',
        'tracking_status'
    ];

    $this->resultAddress = [
        'id',
        'name',
    ];
});

//member has no permission
test('get order fail - member has no permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::NO_PERMISSION,
    ]);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(403);
});

//valid key_search -  invalid key_search
test('get order success - key_search', function () {
    $assert['valid_params'] = ['key_search' => '000060'];
    $assert['invalid_params'] = ['key_search' => 'invalid_params'];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $response);
        if ($keyAssert === 'valid_params') {
            expect($response['data'])->toHaveCount(1);
        } else {
            expect($response['data'])->toBeEmpty();
        }
        foreach ($response['data'] as $key => $value) {
            expect($value)->toHaveKeys($this->result);
            if ($keyAssert === 'valid_params') {
                expect($value['order_status'])->toEqual(SaleOrder::NEW_ORDER);
                expect($value['address'])->toHaveKeys($this->resultAddress);
                expect($value['shipment_default'])->toHaveKeys($this->resultShipment);
            }
        }
    }
});

//valid order_status - invalid order_status
test('get order success - order_status', function () {
    $assert['valid_params'] = ['order_status' => SaleOrder::NEW_ORDER];
    $assert['invalid_params'] = ['order_status' => 'invalid_params'];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $response);
        if ($keyAssert === 'valid_params') {
            expect($response['data'])->toHaveCount(1);
        } else {
            expect($response['data'])->toBeEmpty();
        }
        foreach ($response['data'] as $key => $value) {
            expect($value)->toHaveKeys($this->result);
            if ($keyAssert === 'valid_params') {
                expect($value['order_status'])->toEqual(SaleOrder::NEW_ORDER);
                expect($value['quantity'])->toEqual(4);
                expect($value['address'])->toHaveKeys($this->resultAddress);
                expect($value['shipment_default'])->toHaveKeys($this->resultShipment);
            }
        }
    }
});

//valid order_date_start
test('get order success - order_date_start', function () {
    $endpoint = $this->endpoint . '?' . http_build_query(['order_date_start' => '2022-01-01']);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertArrayHasKey('data', $response);
    expect($response['data'])->toHaveCount(11);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        if (!empty($value['address'])) {
            expect($value['address'])->toHaveKeys($this->resultAddress);
        }
        if (!empty($value['shipment'])) {
            foreach ($value['shipment'] as $keyShipment => $shipment) {
                expect($shipment)->toHaveKeys($this->resultShipment);
            }
        }
    }
});

//valid order_date_end - invalid order_date_end
test('get order success - order_date_end', function () {
    $assert['valid_params'] = ['order_date_end' => Carbon::now()->addDay()->format('Y-m-d')];
    $assert['invalid_params'] = ['order_date_end' => 'invalid_params'];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $response);
        if ($keyAssert === 'invalid_params') {
            expect($response['data'])->toBeEmpty();
        } else {
            expect($response['data'])->toHaveCount(11);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                if (!empty($value['address'])) {
                    expect($value['address'])->toHaveKeys($this->resultAddress);
                }
                if (!empty($value['shipment_default'])) {
                    expect($value['shipment_default'])->toHaveKeys($this->resultShipment);
                }
            }
        }
    }
});

// valid action - invalid action
test('get order success - action', function () {
    $assert['valid_params'] = ['action' => 'export'];
    $assert['invalid_params'] = ['action' => 'view'];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($keyAssert === 'valid_params') {
            unset($this->resultShipment['url']);
            $this->assertArrayNotHasKey('data', $response);
            expect($response)->toHaveCount(11);
            foreach ($response as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                if (!empty($value['address'])) {
                    expect($value['address'])->toHaveKeys($this->resultAddress);
                }
                if (!empty($value['shipment'])) {
                    foreach ($value['shipment'] as $keyShipment => $shipment) {
                        expect($shipment)->toHaveKeys($this->resultShipment);
                    }
                }
            }
        } else {
            $this->assertArrayHasKey('data', $response);
            expect($response['data'])->toHaveCount(11);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                if (!empty($value['address'])) {
                    expect($value['address'])->toHaveKeys($this->resultAddress);
                }
                if (!empty($value['shipment'])) {
                    foreach ($value['shipment'] as $keyShipment => $shipment) {
                        expect($shipment)->toHaveKeys($this->resultShipment);
                    }
                }
            }
        }
    }
});

//get order success
test('get order success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertArrayHasKey('data', $response);
    expect($response['data'])->toHaveCount(11);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        if (!empty($value['address'])) {
            expect($value['address'])->toHaveKeys($this->resultAddress);
        }
        if (!empty($value['shipment_default'])) {
            expect($value['shipment_default'])->toHaveKeys($this->resultShipment);
        }
        if ($value['order_status'] !== SaleOrder::NEW_ORDER) {
            expect($value['address'])->toBeEmpty();
            expect($value['shipment_default'])->toBeEmpty();
            expect($value['quantity'])->toBeEmpty();
        }
    }
});

//get order success - has url shipping
test('get order success - has url shipping', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertArrayHasKey('data', $response);
    expect($response['data'])->toHaveCount(11);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        if (!empty($value['address'])) {
            expect($value['address'])->toHaveKeys($this->resultAddress);
        }
        if (!empty($value['shipment_default'])) {
            expect($value['shipment_default'])->toHaveKeys($this->resultShipment);
            expect($value['shipment_default']['url'])->toEqual(str_replace('{tracking_code}', $value['shipment_default']['tracking_number'], $this->tracking_url));
        }
        if ($value['order_status'] !== SaleOrder::NEW_ORDER) {
            expect($value['address'])->toBeEmpty();
            expect($value['shipment_default'])->toBeEmpty();
            expect($value['quantity'])->toBeEmpty();
        }
    }
});

test('get order success - has seller tags', function () {
    $orderItem = SaleOrderItem::create([
        'order_id' => $this->saleOrder->id,
        'product_style_sku' => 'SKU1',
        'store_id' => $this->store->id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    ProductStyle::insert([
        'sku' => 'SKU1',
        'type' => 'Tee',
        'id' => 1,
    ]);

    $apprelTag = Tag::create([
        'name' => 'Apparel',
        'source' => 'seller',
    ]);
    $embroideryTag = Tag::create([
        'name' => 'Embroidery',
        'source' => 'seller',
    ]);

    ProductPrintArea::insert([
        [
            'name' => 'Sleeve',
            'print_method' => 'EMB',
            'product_style_id' => 1,
        ], [
            'name' => 'Front',
            'print_method' => 'DTG',
            'product_style_id' => 1,
        ]
    ],
    );

    ProductPrintSide::insert([
        ['name' => 'Front', 'code' => '0'],
        ['name' => 'Sleeve', 'code' => '3'],
        ['name' => 'Back', 'code' => '1'],
    ]);

    SaleOrderItemImage::insert([
        [
            'order_item_id' => $orderItem->id,
            'print_side' => '0',
        ],
        [
            'order_item_id' => $orderItem->id,
            'print_side' => '3',
        ],
        [
            'order_item_id' => $orderItem->id,
            'print_side' => '1',
        ],
    ]);

    $endpoint = $this->endpoint . '?' . http_build_query([
        'seller_tag' => $embroideryTag->id,
    ]);
    Artisan::call('order:generate-seller-tags');
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($endpoint);
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    $items = $data['data'][0]['tags'];
    $tagIdArray = collect($items)->pluck('tag_id')->toArray();
    expect($tagIdArray)->toContain($embroideryTag->id);
    expect($tagIdArray)->toContain($apprelTag->id);
});
