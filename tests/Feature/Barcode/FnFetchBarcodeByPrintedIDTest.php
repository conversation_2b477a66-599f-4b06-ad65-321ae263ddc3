<?php

use App\Models\BarcodePrinted;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Repositories\BarcodeRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->account = $account = SaleOrderAccount::factory()->create();
    $this->productStyle = $productStyle = ProductStyle::factory()->createMany([
        [
            'name' => '1533',
            'sku' => 'WORB',
            'type' => 'Tee'
        ]
    ]);
    $this->productColor = $productColor = ProductColor::factory()->createMany([
        [
            'name' => 'ANTIQUE CHERRY RED',
            'sku' => '7S',
        ]
    ]);
    $this->productSize = $productSize = ProductColor::factory()->createMany([
        [
            'name' => '0-3M',
            'sku' => '03M',
        ]
    ]);
    $this->product = $product = Product::factory()->createMany([
        [
            'sku' => $productStyle[0]->sku . $productColor[0]->sku . $productSize[0]->sku,
            'style' => $productStyle[0]->name,
            'color' => $productColor[0]->name,
            'size' => $productSize[0]->name
        ]
    ]);
    $this->saleOrder = $saleOrder = SaleOrder::factory()->createMany([
        [
            'account_id' => $account->id,
            'order_time' => '2025-07-24 15:00:00',
        ],
        [
            'account_id' => $account->id,
            'order_time' => '2025-07-24 12:00:00',
        ]
    ]);
    $this->orderItem = SaleOrderItem::factory()->createMany([
        [
            'product_sku' => $product[0]->sku,
            'product_style_sku' => $productStyle[0]->sku,
            'order_id' => $saleOrder[0]->id,
            'is_rbt' => true,
        ],
        [
            'product_sku' => $product[0]->sku,
            'product_style_sku' => $productStyle[0]->sku,
            'order_id' => $saleOrder[1]->id,
            'is_rbt' => true,
        ]
    ]);
    $this->barcodePrinted = $barcodePrinted = BarcodePrinted::factory()->createMany([
        [
            'id' => 1,
        ]
    ]);
    $this->orderBarcode = $orderBarcode = SaleOrderItemBarcode::factory()->createMany([
        [
            'order_item_id' => $this->orderItem[0]->id,
            'order_id' => $this->saleOrder[0]->id,
            'label_id' => '072425-SJ-M-000001-3',
            'barcode_printed_id' => $barcodePrinted[0]->id,
            'is_deleted' => 0,
        ],
        [
            'order_item_id' => $this->orderItem[1]->id,
            'order_id' => $this->saleOrder[1]->id,
            'label_id' => '072425-SJ-M-000001-4',
            'barcode_printed_id' => $barcodePrinted[0]->id,
            'is_deleted' => 0,
        ],
    ]);
    $this->orderImage = $orderImage = SaleOrderItemImage::factory()->createMany([
        [
            'order_item_id' => $this->orderItem[0]->id,
            'order_id' => $this->saleOrder[0]->id,
        ],
        [
            'order_item_id' => $this->orderItem[0]->id,
            'order_id' => $this->saleOrder[0]->id,
        ],
        [
            'order_item_id' => $this->orderItem[1]->id,
            'order_id' => $this->saleOrder[1]->id,
            'custom_platen' => '16x18',
        ]
    ]);
});

test('fetchBarcodeByPrintedID returns correct data count', function () {
    /** @var BarcodeRepository $barcodeRepo */
    $barcodeRepo = resolve(BarcodeRepository::class);
    $result = $barcodeRepo->fetchBarcodeByPrintedID($this->barcodePrinted[0]->id);
    expect($result->count())->toBe(2);
});

test('fetchBarcodeByPrintedID returns correct field', function () {
    /** @var BarcodeRepository $barcodeRepo */
    $barcodeRepo = resolve(BarcodeRepository::class);
    $result = $barcodeRepo->fetchBarcodeByPrintedID($this->barcodePrinted[0]->id);
    $fields = [
        'label_id',
        'label_urgent',
        'id',
        'sku',
        'order_id',
        'is_xqc',
        'created_at',
        'is_eps',
        'barcode_number',
        'order_quantity',
        'color_formatted',
        'size_formatted',
        'style_formatted',
        'product_style_id',
        'product_type',
        'product_style_sku',
        'account_name',
        'account_id',
        'store_name',
        'store_code',
        'store_id',
        'is_resize',
        'order_external_number',
        'is_fba',
        'order_type',
        'order_date',
        'print_side',
        'print_sides',
        'ink_color',
        'ink_color_detected_at',
        'order_item_id',
        'print_method',
        'pretreat_name',
        'is_manual',
        'is_rbt',
        'custom_platen',
    ];
    foreach ($result->toArray() as $item) {
        foreach ($fields as $fieldName) {
            expect(array_key_exists($fieldName, $item))->toBeTruthy();
        }
    }
});

test('fetchBarcodeByPrintedID returns correct data sort', function () {
    /** @var BarcodeRepository $barcodeRepo */
    $barcodeRepo = resolve(BarcodeRepository::class);
    $result = $barcodeRepo->fetchBarcodeByPrintedID($this->barcodePrinted[0]->id);
    expect($result[0]->id)->toBe($this->orderBarcode[1]->id)
        ->and($result[0]->custom_platen)->toBe('16x18')
        ->and($result[1]->id)->toBe($this->orderBarcode[0]->id)
        ->and($result[1]->custom_platen)->toBeNull();
});
