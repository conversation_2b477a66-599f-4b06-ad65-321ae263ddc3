<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Repositories\InternalRequestRepository;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mo<PERSON>y\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->product = Product::factory()->create([
        'sku' => 'UNGH1M0XL', 'gtin' => '00821780067052',
        'color' => 'HEATHER GREY', 'size' => 'L', 'style' => '340', 'parent_id' => 2
    ]);
    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'department' => Department::DEPARTMENT_INVENTORY,
        'is_deleted' => false,
        'code' => faker::create()->word(),
        'avatar' => 'avatar/employee/10170.jpg'
    ]);
    $this->endpoint = '/api/internal-request/login?';
    $this->params = [
        'code' => $this->employee->code
    ];
});

// validate input
test('validate input', function () {
    $this->params['code'] = '';
    $this->params['token'] = '';
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'code' => ['The code field is required.'],
        ]
    ]);

    //product not found
    $this->params['code'] = 99999;
    $this->params['token'] = '';
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    $data = json_decode($response->getContent(), true);
    expect($data['errors'])->toMatchArray([
        'code' => ['Employee not found.'],
    ],
    );
});

// login success with employee in department inventory
test('login success with employee in department inventory', function () {
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    $this->employee = $this->employee->toArray();
    if (!empty($data['employee']['avatar'])) {
        $this->assertNotNull($this->employee['avatar']);
        $this->assertEquals($data['employee']['avatar'], env('AWS_S3_URL', '') . '/' . $this->employee['avatar'] ?? '');
    }
    unset($data['employee']['avatar']);
    unset($this->employee['avatar']);
    expect($data['employee'])->toMatchArray($this->employee);
    expect($data['time_tracking_id'])->toEqual(\App\Models\TimeTracking::first()->id);
});

// login success with employee in department not in inventory
test('login success with employee in department not in inventory', function () {
    $this->employee->department = Department::DEPARTMENT_PULLING;
    $this->employee->save();
//    $this->mock(InternalRequestRepository::class, function (MockInterface $mock) {
//        $mock->shouldAllowMockingProtectedMethods()->shouldReceive('fireBaseSubTopic')->once()->andReturn(true);
//    })->makePartial();

    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);

    $this->employee = $this->employee->toArray();
    if (!empty($data['employee']['avatar'])) {
        $this->assertNotNull($this->employee['avatar']);
        $this->assertEquals($data['employee']['avatar'], env('AWS_S3_URL', '') . '/' . $this->employee['avatar'] ?? '');
    }
    unset($data['employee']['avatar']);
    unset($this->employee['avatar']);
    expect($data['employee'])->toMatchArray($this->employee);
    expect($data['time_tracking_id'])->toEqual(null);
});
