<?php

use App\Models\Box;
use App\Models\Employee;
use App\Models\InternalRequest;
use App\Models\Location;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\RbtProduct;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouseId = 1;
    $this->employee = Employee::factory()->create([
        'id' => 17,
        'warehouse_id' => $this->warehouseId
    ]);
    InternalRequest::factory()->count(10)->sequence(
        [
            'status' => 'new',
            'warehouse_id' => $this->warehouseId
        ],
    )->sequence(
        ['product_id' => 1], //OOS
        ['product_id' => 2], //incoming
        ['product_id' => 3], //đủ box
        ['product_id' => 4], //đủ box khi chưa lọc lane, và thiếu khi lọc lane
        ['product_id' => 5], //thiếu box
    )->create();
    InternalRequest::factory()->count(8)->sequence(
        fn ($sequence) => [
            'warehouse_id' => $this->warehouseId,
            'product_id' => $sequence->index + 101,
        ],
    )->sequence(
        [
            'status' => 'picking_up',
            'employee_receive_id' => $this->employee->id
        ],
        [
            'status' => 'uncheck',
            'employee_fulfill_id' => $this->employee->id
        ],
        [
            'status' => 'checked',
            'employee_confirm_id' => $this->employee->id
        ],
        [
            'status' => 'rejected',
            'employee_reject_id' => $this->employee->id
        ],
    )->create();
    InternalRequest::factory()->createMany([
        [
            'product_id' => 3,
            'status' => 'new',
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouseId
        ],
        [
            'product_id' => 3,
            'status' => 'new',
            'is_deleted' => 0,
            'warehouse_id' => $this->warehouseId + 1
        ],
        [
            'product_id' => 3,
            'priority' => 1,
            'status' => 'new',
            'warehouse_id' => $this->warehouseId,
        ],
    ]);

    $otherLocation = ['11', '10', 'Pallet ', 'Container '];
    Location::factory()->count(18)->sequence(
        fn ($sequence) => [
            'id' => $sequence->index + 1,
            'warehouse_id' => $this->warehouseId,
            'is_deleted' => 0,
            'type' => Location::RACK,
            'barcode' => $sequence->index < 6 ? 'AA-' . ($sequence->index + 1) : ($sequence->index < 12 ? 'BB-' . ($sequence->index + 1) : $otherLocation[rand(0, count($otherLocation) - 1)] . ($sequence->index + 1))
        ],
    )->create();
    Location::factory()->create([
        'barcode' => '1417',
        'warehouse_id' => $this->warehouseId
    ]);
    $this->locationOtherWarehouse = Location::factory()->create([
        'barcode' => 'AA-11',
        'warehouse_id' => $this->warehouseId + 1
    ]);

    Box::factory()->count(18)->sequence(
        fn ($sequence) => [
            'product_id' => 3,
            'location_id' => $sequence->index + 1,
            'warehouse_id' => $this->warehouseId,
            'is_deleted' => 0,
            'quantity' => 72,
        ],
    )->create();
    Box::factory()->createMany([
        [
            'product_id' => 3,
            'location_id' => 1,
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 72,
        ],
        [
            'product_id' => 3,
            'location_id' => 1,
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 0,
        ],
        [
            'product_id' => 3,
            'location_id' => 2,
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 72,
        ],
        [
            'product_id' => 3,
            'location_id' => 2,
            'is_deleted' => 0,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 0,
        ],
        [
            'product_id' => 3,
            'location_id' => $this->locationOtherWarehouse->id,
            'is_deleted' => 0,
            'warehouse_id' => $this->locationOtherWarehouse->warehouse_id,
            'quantity' => 72,
        ],
    ]);
    Box::factory()->count(2)->sequence(
        ['location_id' => 1],
        ['location_id' => 7],
    )->sequence(
        [
            'product_id' => 4,
            'warehouse_id' => $this->warehouseId,
            'is_deleted' => 0,
            'quantity' => 72,
        ],
    )->create();
    Box::factory()->createMany([
        [
            'product_id' => 5,
            'location_id' => 1,
            'is_deleted' => 0,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 72,
        ],
        [
            'product_id' => 5,
            'location_id' => 1,
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 72,
        ],
        [
            'product_id' => 5,
            'location_id' => 1,
            'is_deleted' => 0,
            'warehouse_id' => $this->warehouseId,
            'quantity' => 0,
        ],
    ]);

    ProductQuantity::factory()->createMany([
        [
            'product_id' => 1,
            'quantity' => 0,
            'warehouse_id' => $this->warehouseId,
            'incoming_stock' => 0
        ],
        [
            'product_id' => 2,
            'quantity' => 0,
            'warehouse_id' => $this->warehouseId,
            'incoming_stock' => 17
        ],
    ]);

    Product::factory()->createMany([
        [
            'style' => 'ABCD1',
            'color' => '1W',
            'size' => '0XL',
            'sku' => 'ABCD1W0X1',
            'id' => 1
        ],
        [
            'style' => 'ABCD2',
            'color' => '1W',
            'size' => '0XL',
            'sku' => 'ABCD1W0X2',
            'id' => 2
        ],
        [
            'style' => 'ABCD3',
            'color' => '1W',
            'size' => '0XL',
            'sku' => 'ABCD1W0X3',
            'id' => 3
        ],
        [
            'style' => 'ABCD',
            'color' => '1W',
            'size' => '0XL',
            'sku' => 'ABCD1W0XL',
            'id' => 4
        ],
        [
            'style' => 'ABCD5',
            'color' => '1W',
            'size' => '0XL',
            'sku' => 'ABCD1W0X5',
            'id' => 5
        ],
    ]);

    $this->endpoint = '/api/internal-request?';
});

test('app inventory get list internal request non-filter: success.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    // $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect(count($data))->toEqual(6);
    expect($data[0])->toMatchArray([
        'product_id' => 3,
        'priority' => 1,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
    expect($data[1])->toMatchArray([
        'product_id' => 3,
        'priority' => 0,
        'location_suggest' => 'AA-2',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
    expect($data[2])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1', 'BB-7']
    ]);
    expect($data[3])->toMatchArray([
        'product_id' => 5,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1']
    ]);
    expect($data[4])->toMatchArray([
        'product_id' => 3,
        'priority' => 0,
        'location_suggest' => 'AA-3',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
    expect($data[5])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'BB-7',
        'locations' => ['AA-1', 'BB-7']
    ]);
});

test('App inventory get list internal request filter lane: success.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
        'location' => 'AA-1'
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect(count($data))->toEqual(5);
    expect($data[0])->toMatchArray([
        'product_id' => 3,
        'priority' => 1,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
    expect($data[1])->toMatchArray([
        'product_id' => 3,
        'priority' => 0,
        'location_suggest' => 'AA-2',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
    expect($data[2])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1']
    ]);
    expect($data[3])->toMatchArray([
        'product_id' => 5,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1']
    ]);
    expect($data[4])->toMatchArray([
        'product_id' => 3,
        'priority' => 0,
        'location_suggest' => 'AA-3',
        'locations' => ['AA-1', 'AA-2', 'AA-3', 'AA-4', 'AA-5']
    ]);
});

test('App inventory get list internal request filter sku: success.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
        'key_word' => 'ABCD1W0XL'
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect(count($data))->toEqual(2);
    expect($data[0])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1', 'BB-7']
    ]);
    expect($data[1])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'BB-7',
        'locations' => ['AA-1', 'BB-7']
    ]);
});

test('App inventory get list internal request filter lane and sku: success.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
        'location' => 'AA-1',
        'key_word' => 'ABCD1W0XL'
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect(count($data))->toEqual(1);
    expect($data[0])->toMatchArray([
        'product_id' => 4,
        'priority' => 0,
        'location_suggest' => 'AA-1',
        'locations' => ['AA-1']
    ]);
});

test('App inventory get list internal error: employee not exist.', function () {
    $params = [
        'employee' => 1,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(422);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Employee not exist'
    ]);
});

test('App inventory get list internal error: location not exist.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
        'location' => 'AB123456',
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(422);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'Location not exist'
    ]);
});

test('App inventory get list internal error: location belongs to another warehouse.', function () {
    Location::factory()->create([
        'barcode' => 'AB123456',
        'warehouse_id' => 2
    ]);
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::INVENTORY_DEPARTMENT,
        'location' => 'AB123456',
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(422);
    $data = json_decode($response->getContent(), true);
    expect($data)->toMatchArray([
        'message' => 'This location belongs to another warehouse'
    ]);
});

test('App pulling get list internal request: success.', function () {
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::PULLING_DEPARTMENT,
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    $dataClone = $data;
    usort($data, 'sortRequest');
    expect($data)->toMatchArray($dataClone);
    expect(count($data))->toEqual(19);
});

test('App pulling get list internal filter by status.', function () {
    Location::factory()->create([
        'barcode' => 'AB123456',
        'warehouse_id' => 2
    ]);
    $testCase = [
        InternalRequest::NEW_STATUS => [
            'count' => 11,
            'is_priority' => true,
        ],
        InternalRequest::PICKING_UP_STATUS => [
            'count' => 2,
            'is_priority' => true,
        ],
        InternalRequest::UNCHECK_STATUS => [
            'count' => 2,
            'is_priority' => false,
        ],
    ];
    $params = [
        'employee' => $this->employee->id,
        'app' => InternalRequest::PULLING_DEPARTMENT,
    ];
    foreach ($testCase as $key => $item) {
        $params['status'] = [$key];
        $response = $this->withHeaders([
            'Accept' => 'application/json',
        ])->get($this->endpoint . http_build_query($params));
        $response->assertStatus(200);
        $data = json_decode($response->getContent(), true);
        expect(count($data))->toEqual($item['count']);
        if ($item['is_priority']) {
            $dataClone = $data;
            usort($data, 'sortRequest');
            expect($data)->toMatchArray($dataClone);
        }
    }
});

function sortRequest($a, $b)
{
    if ($a['priority'] !== $b['priority']) {
        return $b['priority'] - $a['priority'];
    }

    return strcmp($a['created_at'], $b['created_at']);
}

test('pc get list internal request non-filter: success.', function () {
    $params = [];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['data'][0])->toMatchArray([
        'product_id' => 3,
        'priority' => 1
    ]);
    expect($data['total'])->toEqual(20);
});

test('pc get list internal request filter by warehouse: success.', function () {
    $params = [
        'warehouse_id' => $this->warehouseId
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['data'][0])->toMatchArray([
        'product_id' => 3,
        'priority' => 1
    ]);
    expect($data['total'])->toEqual(19);
});

test('pc get list internal request tab name history: success.', function () {
    InternalRequest::whereNull('employee_create_id')->update(['employee_create_id' => 12312]);

    $params = [
        'tab_name' => 'history',
        'warehouse_id' => $this->warehouseId
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['total'])->toEqual(6);
    $data = $data['data'];
    expect($data[0])->toMatchArray([
        'product_id' => 102,
        'status' => 'uncheck',
    ]);
    expect($data[1])->toMatchArray([
        'product_id' => 106,
        'status' => 'uncheck',
    ]);
    expect($data[2])->toMatchArray([
        'product_id' => 103,
        'status' => 'checked',
    ]);
    expect($data[3])->toMatchArray([
        'product_id' => 104,
        'status' => 'rejected',
    ]);
    expect($data[4])->toMatchArray([
        'product_id' => 107,
        'status' => 'checked',
    ]);
    expect($data[5])->toMatchArray([
        'product_id' => 108,
        'status' => 'rejected',
    ]);
});

test('pc get list internal request new: in stock, oos, incoming.', function () {
    $params = [
        'warehouse_id' => $this->warehouseId,
        'status' => [
            'new'
        ],
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['total'])->toEqual(11);
});
test('pc get list internal request multiple status: success.', function () {
    $params = [
        'warehouse_id' => $this->warehouseId,
        'status' => [
            'new',
            'picking_up'
        ],
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['total'])->toEqual(13);
});

test('pc get list internal request filter employee id: success.', function () {
    $params = [
        'warehouse_id' => $this->warehouseId,
        'employee_id' => $this->employee->id
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['total'])->toEqual(8);
});

test('pc get list internal request tab name history: success - rbt tab', function () {
    InternalRequest::whereNull('employee_create_id')->update(['employee_create_id' => RbtProduct::RBT_EMPLOYEE_ID, 'is_rbt' => 1]);

    $params = [
        'tab_name' => 'history',
        'warehouse_id' => $this->warehouseId,
        'tab' => 'restricted'
    ];
    $response = $this->withHeaders([
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($params));
    $response->assertStatus(200);
    $data = json_decode($response->getContent(), true);
    expect($data['total'])->toEqual(6);
});
