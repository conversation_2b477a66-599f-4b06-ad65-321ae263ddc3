<?php

use App\Models\Employee;
use App\Models\PretreatPreset;
use App\Models\PretreatPresetSKU;
use App\Models\PrintingPreset;
use App\Models\PrintingPresetSku;
use App\Models\PrintSetting;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\StoreProductStyleResize;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;

    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
    ]);
    ProductPrintSide::factory()->create([
        'code' => 0,
        'name' => 'Front',
        'code_name' => 'front',
        'code_wip' => 'F',
    ]);
    $this->productStyles = ProductStyle::factory()->createMany([
        [
            'sku' => 'UNPT',
            'name' => '3001',
        ],
        [
            'sku' => 'UNGH',
            'name' => '18500',
        ],
    ]);
    $this->productColor = ProductColor::factory()->create([
        'sku' => '9C',
        'name' => 'AQUA',
    ]);
    $this->pretreatPreset = PretreatPreset::factory()->create([
        'preset_name' => 'Pretreat',
    ]);
    PretreatPresetSKU::factory()->createMany([
        [
            'pretreat_preset_id' => $this->pretreatPreset->id,
            'style' => $this->productStyles[0]->sku,
            'color' => $this->productColor->sku,
        ],
        [
            'pretreat_preset_id' => $this->pretreatPreset->id,
            'style' => $this->productStyles[1]->sku,
            'color' => $this->productColor->sku,
        ],
    ]);
    $this->products = Product::factory()->createMany([
        [
            'sku' => 'UNPT9C0XS',
            'style' => $this->productStyles[0]->name,
            'color' => 'AQUA',
            'size' => 'XS',
        ],
        [
            'sku' => 'UNPT9C00L',
            'style' => $this->productStyles[0]->name,
            'color' => 'AQUA',
            'size' => 'L',
        ],
        [
            'sku' => 'UNGH9C00L',
            'style' => $this->productStyles[1]->name,
            'color' => 'AQUA',
            'size' => 'L',
        ],

    ]);

    $this->printingPreset = PrintingPreset::factory()->create();
    PrintingPresetSku::factory()->createMany([
        [
            'sku' => $this->products[0]->sku,
            'black_ink' => $this->printingPreset->name,
            'white_ink' => $this->printingPreset->name,
            'mix_ink' => $this->printingPreset->name,
            'black_ink_xqc' => $this->printingPreset->name,
            'white_ink_xqc' => $this->printingPreset->name,
            'mix_ink_xqc' => $this->printingPreset->name,
            'front_size' => '7x8',
            'platen_front_size' => '7x8',
            'front_position' => '0x0'
        ],
        [
            'sku' => $this->products[1]->sku,
            'black_ink' => $this->printingPreset->name,
            'white_ink' => $this->printingPreset->name,
            'mix_ink' => $this->printingPreset->name,
            'black_ink_xqc' => $this->printingPreset->name,
            'white_ink_xqc' => $this->printingPreset->name,
            'mix_ink_xqc' => $this->printingPreset->name,
            'front_size' => '14x16',
            'platen_front_size' => '14x16',
            'front_position' => '0x0',
            'pocket_size' => '4x5',
            'platen_pocket_size' => '14x16',
            'pocket_position' => '9x2',
        ],
        [
            'sku' => $this->products[2]->sku,
            'black_ink' => $this->printingPreset->name,
            'white_ink' => $this->printingPreset->name,
            'mix_ink' => $this->printingPreset->name,
            'black_ink_xqc' => $this->printingPreset->name,
            'white_ink_xqc' => $this->printingPreset->name,
            'mix_ink_xqc' => $this->printingPreset->name,
            'front_size' => '14x10',
            'platen_front_size' => '14x16',
            'front_position' => '0x0',
            'pocket_size' => '4x5',
            'platen_pocket_size' => '14x16',
            'pocket_position' => '9x2',
        ],
    ]);

    $this->storePrintify = Store::factory()->create([
        'id' => Store::PRINTIFY_API_ID,
        'is_resize' => 0,
    ]);
    $this->storeResize = Store::factory()->create([
        'is_resize' => 1,
    ]);
    $this->storeNotResize = Store::factory()->create([
        'is_resize' => 0,
    ]);
    $this->orderPrintify = SaleOrder::factory()->create([
        'store_id' => $this->storePrintify->id,
        'order_status' => SaleOrder::IN_PRODUCTION,
        'warehouse_id' => $this->warehouse->id,
    ]);
    $this->orderResize = SaleOrder::factory()->create([
        'store_id' => $this->storeResize->id,
        'order_status' => SaleOrder::IN_PRODUCTION,
        'warehouse_id' => $this->warehouse->id,
    ]);
    $this->orderNotResize = SaleOrder::factory()->create([
        'store_id' => $this->storeNotResize->id,
        'order_status' => SaleOrder::IN_PRODUCTION,
        'warehouse_id' => $this->warehouse->id,
    ]);

    $this->saleOrderItemPrintifies = SaleOrderItem::factory()->count(3)->sequence([
        'order_id' => $this->orderPrintify->id,
        'store_id' => $this->orderPrintify->store_id,
        'warehouse_id' => $this->warehouse->id,
        'quantity' => 1,
        'options' => json_encode([]),
    ])->sequence(
        [
            'product_id' => $this->products[0]->id,
            'product_sku' => $this->products[0]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[1]->id,
            'product_sku' => $this->products[1]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[2]->id,
            'product_sku' => $this->products[2]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[2]->sku,
            'product_style_sku' => $this->products[2]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
    )->create();
    $this->saleOrderItemsNotResize = SaleOrderItem::factory()->count(3)->sequence([
        'order_id' => $this->orderNotResize->id,
        'store_id' => $this->orderNotResize->store_id,
        'warehouse_id' => $this->warehouse->id,
        'quantity' => 1,
        'options' => json_encode([]),
    ])->sequence(
        [
            'product_id' => $this->products[0]->id,
            'product_sku' => $this->products[0]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[1]->id,
            'product_sku' => $this->products[1]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[2]->id,
            'product_sku' => $this->products[2]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[2]->sku,
            'product_style_sku' => $this->products[2]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
    )->create();
    $this->saleOrderItemsResize = SaleOrderItem::factory()->count(3)->sequence([
        'order_id' => $this->orderResize->id,
        'store_id' => $this->orderResize->store_id,
        'warehouse_id' => $this->warehouse->id,
        'quantity' => 1,
        'options' => json_encode([]),
    ])->sequence(
        [
            'product_id' => $this->products[0]->id,
            'product_sku' => $this->products[0]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[1]->id,
            'product_sku' => $this->products[1]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
        [
            'product_id' => $this->products[2]->id,
            'product_sku' => $this->products[2]->sku,
            'sku' => $this->orderPrintify->id . '-' . $this->products[2]->sku,
            'product_style_sku' => $this->products[2]->productStyle->sku,
            'product_color_sku' => "9C"
        ],
    )->create();
    $this->barcodePrintifies = SaleOrderItemBarcode::factory()->count(3)->sequence([
        'order_id' => $this->orderPrintify->id,
        'store_id' => $this->orderPrintify->store_id,
        'warehouse_id' => $this->warehouse->id
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemPrintifies[0]->id,
            'label_id' => $this->products[0]->sku . "-CH-" . $this->products[0]->size . "-" . $this->saleOrderItemPrintifies[0]->id . "-1",
            'sku' => $this->saleOrderItemPrintifies[0]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemPrintifies[1]->id,
            'label_id' => $this->products[1]->sku . "-CH-" . $this->products[1]->size . "-" . $this->saleOrderItemPrintifies[1]->id . "-1",
            'sku' => $this->saleOrderItemPrintifies[1]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemPrintifies[2]->id,
            'label_id' => $this->products[2]->sku . "-CH-" . $this->products[2]->size . "-" . $this->saleOrderItemPrintifies[2]->id . "-1",
            'sku' => $this->saleOrderItemPrintifies[2]->sku,
        ],
    )->create();

    $this->barcodeResizes = SaleOrderItemBarcode::factory()->count(3)->sequence([
        'order_id' => $this->orderResize->id,
        'store_id' => $this->orderResize->store_id,
        'warehouse_id' => $this->warehouse->id
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemsResize[0]->id,
            'label_id' => $this->products[0]->sku . "-CH-" . $this->products[0]->size . "-" . $this->saleOrderItemsResize[0]->id . "-1",
            'sku' => $this->saleOrderItemsResize[0]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemsResize[1]->id,
            'label_id' => $this->products[1]->sku . "-CH-" . $this->products[1]->size . "-" . $this->saleOrderItemsResize[1]->id . "-1",
            'sku' => $this->saleOrderItemsResize[1]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemsResize[2]->id,
            'label_id' => $this->products[2]->sku . "-CH-" . $this->products[2]->size . "-" . $this->saleOrderItemsResize[2]->id . "-1",
            'sku' => $this->saleOrderItemsResize[2]->sku,
        ],
    )->create();
    $this->barcodeNotResizes = SaleOrderItemBarcode::factory()->count(3)->sequence([
        'order_id' => $this->orderNotResize->id,
        'store_id' => $this->orderNotResize->store_id,
        'warehouse_id' => $this->warehouse->id
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemsNotResize[0]->id,
            'label_id' => $this->products[0]->sku . "-CH-" . $this->products[0]->size . "-" . $this->saleOrderItemsNotResize[0]->id . "-1",
            'sku' => $this->saleOrderItemsNotResize[0]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemsNotResize[1]->id,
            'label_id' => $this->products[1]->sku . "-CH-" . $this->products[1]->size . "-" . $this->saleOrderItemsNotResize[1]->id . "-1",
            'sku' => $this->saleOrderItemsNotResize[1]->sku,
        ],
        [
            'order_item_id' => $this->saleOrderItemsNotResize[2]->id,
            'label_id' => $this->products[2]->sku . "-CH-" . $this->products[2]->size . "-" . $this->saleOrderItemsNotResize[2]->id . "-1",
            'sku' => $this->saleOrderItemsNotResize[2]->sku,
        ],
    )->create();

    $this->orderDate = "1111-11-11";

    $this->itemImageResizes = SaleOrderItemImage::factory()->count(3)->sequence([
        'order_id' => $this->orderResize->id,
        'store_id' => $this->orderResize->store_id,
        'warehouse_id' => $this->warehouse->id,
        'print_side' => '0',
        'color_new' => 2,
        'upload_s3_status' => SaleOrderItemImage::SYNC_S3_SUCCESS,
        'order_date' => $this->orderDate,
        'pretreat_info' => json_encode((object)[
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 1,
        ]),
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemsResize[0]->id,
            'sku' => $this->saleOrderItemsResize[0]->sku,
            'product_sku' => $this->saleOrderItemsResize[0]->product_sku,
            'image_width' => 4200,
            'image_height' => 4800
        ],
        [
            'order_item_id' => $this->saleOrderItemsResize[1]->id,
            'sku' => $this->saleOrderItemsResize[1]->sku,
            'product_sku' => $this->saleOrderItemsResize[1]->product_sku,
            'image_width' => 4200,
            'image_height' => 4800,
        ],
        [
            'order_item_id' => $this->saleOrderItemsResize[2]->id,
            'sku' => $this->saleOrderItemsResize[2]->sku,
            'product_sku' => $this->saleOrderItemsResize[2]->product_sku,
            'image_width' => 4200,
            'image_height' => 4800,
            'pretreat_info' => json_encode([
                'top' => 0,
                'left' => 0,
                'width' => 1,
                'height' => 0.625,
            ])
        ],
    )->create();
    $this->itemImageNotResizes = SaleOrderItemImage::factory()->count(3)->sequence([
        'order_id' => $this->orderNotResize->id,
        'store_id' => $this->orderNotResize->store_id,
        'warehouse_id' => $this->warehouse->id,
        'print_side' => '0',
        'color_new' => 2,
        'upload_s3_status' => SaleOrderItemImage::SYNC_S3_SUCCESS,
        'order_date' => $this->orderDate,
        'pretreat_info' => json_encode((object)[
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 1,
        ]),
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemsNotResize[0]->id,
            'sku' => $this->saleOrderItemsNotResize[0]->sku,
            'product_sku' => $this->saleOrderItemsNotResize[0]->product_sku,
            'image_width' => 2100,
            'image_height' => 2400
        ],
        [
            'order_item_id' => $this->saleOrderItemsNotResize[1]->id,
            'sku' => $this->saleOrderItemsNotResize[1]->sku,
            'product_sku' => $this->saleOrderItemsNotResize[1]->product_sku,
            'image_width' => 4050,
            'image_height' => 4050,
        ],
        [
            'order_item_id' => $this->saleOrderItemsNotResize[2]->id,
            'sku' => $this->saleOrderItemsNotResize[2]->sku,
            'product_sku' => $this->saleOrderItemsNotResize[2]->product_sku,
            'image_width' => 4200,
            'image_height' => 4800,
        ],
    )->create();
    $this->itemImagePrintifies = SaleOrderItemImage::factory()->count(3)->sequence([
        'order_id' => $this->orderPrintify->id,
        'store_id' => $this->orderPrintify->store_id,
        'warehouse_id' => $this->warehouse->id,
        'print_side' => '0',
        'color_new' => 2,
        'upload_s3_status' => SaleOrderItemImage::SYNC_S3_SUCCESS,
        'order_date' => $this->orderDate,
        'pretreat_info' => json_encode((object)[
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 1,
        ]),
    ])->sequence(
        [
            'order_item_id' => $this->saleOrderItemPrintifies[0]->id,
            'sku' => $this->saleOrderItemPrintifies[0]->sku,
            'product_sku' => $this->saleOrderItemPrintifies[0]->product_sku,
            'image_width' => 2100,
            'image_height' => 2400
        ],
        [
            'order_item_id' => $this->saleOrderItemPrintifies[1]->id,
            'sku' => $this->saleOrderItemPrintifies[1]->sku,
            'product_sku' => $this->saleOrderItemPrintifies[1]->product_sku,
            'image_width' => 4200,
            'image_height' => 4800
        ],
        [
            'order_item_id' => $this->saleOrderItemPrintifies[2]->id,
            'sku' => $this->saleOrderItemPrintifies[2]->sku,
            'product_sku' => $this->saleOrderItemPrintifies[2]->product_sku,
            'image_width' => 4200,
            'image_height' => 3000
        ],
    )->create();

    PrintSetting::factory()->createMany([
        [
            'key' => 'bulb_power_pretreat',
            'data' => json_encode([
                1, 2
            ])
        ],
        [
            'key' => 'bulb_power_printing',
            'data' => json_encode([
                1, 2
            ])
        ],
    ]);

    $this->endpoint = 'all-in-one?';
});

test('Get printing success - store is resize platen 7x8', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[0]->label_id,
    ];
    $imageName = $this->barcodeResizes[0]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "7x8",
        "platen_width" => "7",
        "platen_height" => "8",
        "offset_left" => "01330152",
        "image_size" => "15111727",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 133,
        "offset_top" => 152,
        "image_width" => 1511,
        "image_height" => 1727
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 133,
        "offset_top" => 152,
        "image_width" => 1511,
        "image_height" => 1727,
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0.52362,
        "offset_top" => 0.59843,
        "image_width" => 5.94882,
        "image_height" => 6.79921
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.84983,
        "height" => 0.8499,
        "top" => 0.0748,
        "left" => 0.0748
    ]);
    expect($position['scale'])->toEqual(0.42);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0.09843,
        "left" => -0.00197,
        "width" => 7.94882,
        "height" => 8.79921,
        "expand_x" => 1,
        "expand_y" => 1
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0.00547,
        "left" => 0.27909,
        "width" => 0.4416,
        "height" => 0.48885
    ]);
})->skip();

test('Get printing success - store is resize platen 14x16 - image fit', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35564064",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 4064
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 4064,
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 14,
        "image_height" => 16,
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 1,
        "top" => 0,
        "left" => 0,
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 17,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.94444,
    ]);
})->skip();

test('Get printing success - store is resize platen 14x16 - image inside platen - ratio width > height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 2100,
        'image_height' => 2100,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556,
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 14,
        "image_height" => 14
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 0.875,
        "top" => 0,
        "left" => 0,
    ]);
    expect($position['scale'])->toEqual(2);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 15,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.83333
    ]);
})->skip();

test('Get printing success - store is resize platen 14x16 - image inside platen - ratio width < height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 2000,
        'image_height' => 2400,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00850000",
        "image_size" => "33874064",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 85,
        "offset_top" => 0,
        "image_width" => 3387,
        "image_height" => 4064
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 85,
        "offset_top" => 0,
        "image_width" => 3387,
        "image_height" => 4064
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0.33465,
        "offset_top" => 0,
        "image_width" => 13.33465,
        "image_height" => 16
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.95248,
        "height" => 1,
        "top" => 0,
        "left" => 0.0239
    ]);
    expect($position['scale'])->toEqual(2);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 15.33465,
        "height" => 17,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.07415,
        "width" => 0.85193,
        "height" => 0.94444
    ]);
})->skip();

test('Get printing success - store is resize platen 14x16 - image exceed platen - ratio width > height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 8400,
        'image_height' => 8400,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 14,
        "image_height" => 14
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 0.875,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(0.5);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 15,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.83333
    ]);
})->skip();

test('Get printing success - store is resize platen 14x16 - image exceed platen - ratio width < height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 8000,
        'image_height' => 9600,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00850000",
        "image_size" => "33874064",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 85,
        "offset_top" => 0,
        "image_width" => 3387,
        "image_height" => 4064
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 85,
        "offset_top" => 0,
        "image_width" => 3387,
        "image_height" => 4064
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0.33465,
        "offset_top" => 0,
        "image_width" => 13.33465,
        "image_height" => 16
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.95248,
        "height" => 1,
        "top" => 0,
        "left" => 0.0239
    ]);
    expect($position['scale'])->toEqual(0.5);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 15.33465,
        "height" => 17,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.07415,
        "width" => 0.85193,
        "height" => 0.94444
    ]);
})->skip();

test('Get printing success - store is resize hoodie - trim artwork inside print area - fit', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[2]->id)->update([
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 0.625,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[2]->label_id,
    ];
    $imageName = $this->barcodeResizes[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "06670000",
        "image_size" => "22232540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540,
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 2.62598,
        "offset_top" => 0,
        "image_width" => 8.75197,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.62514,
        "height" => 0.625,
        "top" => 0,
        "left" => 0.18757
    ]);
    expect($position['scale'])->toEqual(0.63);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 10.75197,
        "height" => 7.25,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.20144,
        "width" => 0.59733,
        "height" => 0.40278
    ]);
})->skip();

test('Get printing success - store is resize hoodie - trim artwork inside print area - ratio width > height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[2]->id)->update([
        'image_width' => 2100,
        'image_height' => 2400,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 0,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[2]->label_id,
    ];
    $imageName = $this->barcodeResizes[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "06670000",
        "image_size" => "22232540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 2.62598,
        "offset_top" => 0,
        "image_width" => 8.75197,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.62514,
        "height" => 0.625,
        "top" => 0,
        "left" => 0.18757
    ]);
    expect($position['scale'])->toEqual(1.25);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 10.75197,
        "height" => 1,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.20144,
        "width" => 0.59733,
        "height" => 0.05556
    ]);
})->skip();

test('Get printing success - store is resize hoodie - trim artwork inside print area - ratio width < height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[2]->id)->update([
        'image_width' => 1800,
        'image_height' => 2400,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 0.625,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[2]->label_id,
    ];
    $imageName = $this->barcodeResizes[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "08260000",
        "image_size" => "19052540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 826,
        "offset_top" => 0,
        "image_width" => 1905,
        "image_height" => 2540
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 826,
        "offset_top" => 0,
        "image_width" => 1905,
        "image_height" => 2540
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 3.25197,
        "offset_top" => 0,
        "image_width" => 7.5,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.53571,
        "height" => 0.625,
        "top" => 0,
        "left" => 0.23228
    ]);
    expect($position['scale'])->toEqual(1.25);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 9.5,
        "height" => 7.25,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.23622,
        "width" => 0.52778,
        "height" => 0.40278
    ]);
})->skip();

test('Get printing success - store is resize hoodie - trim artwork exceed print area - ratio width > height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[2]->id)->update([
        'image_width' => 2100,
        'image_height' => 2400,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 0.5,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[2]->label_id,
    ];
    $imageName = $this->barcodeResizes[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "06670000",
        "image_size" => "22232540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 2.62598,
        "offset_top" => 0,
        "image_width" => 8.75197,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.62514,
        "height" => 0.625,
        "top" => 0,
        "left" => 0.18757
    ]);
    expect($position['scale'])->toEqual(1.25);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 10.75197,
        "height" => 6,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.20144,
        "width" => 0.59733,
        "height" => 0.33333
    ]);
})->skip();

test('Get printing success - store is resize hoodie - trim artwork exceed print area - ratio width < height', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[2]->id)->update([
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 1,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[2]->label_id,
    ];
    $imageName = $this->barcodeResizes[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => true,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "06670000",
        "image_size" => "22232540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 667,
        "offset_top" => 0,
        "image_width" => 2223,
        "image_height" => 2540
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 2.62598,
        "offset_top" => 0,
        "image_width" => 8.75197,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.62514,
        "height" => 0.625,
        "top" => 0,
        "left" => 0.18757
    ]);
    expect($position['scale'])->toEqual(0.63);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0.00197,
        "width" => 10.75197,
        "height" => 11,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.20144,
        "width" => 0.59733,
        "height" => 0.61111
    ]);
})->skip();

test('Get printing success - store is printify 7x8', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodePrintifies[0]->label_id,
    ];
    $imageName = $this->barcodePrintifies[0]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "7x8",
        "platen_width" => "7",
        "platen_height" => "8",
        "offset_left" => "00000000",
        "image_size" => "17782032",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 7,
        "image_height" => 8,
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 1,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 9,
        "height" => 9,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.25,
        "width" => 0.5,
        "height" => 0.5
    ]);
})->skip();

test('Get printing success - store is printify 14x16', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodePrintifies[1]->label_id,
    ];
    $imageName = $this->barcodePrintifies[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35564064",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 4064,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 4064
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 14,
        "image_height" => 16
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 1,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 17,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.94444
    ]);
})->skip();

test('Get printing success - store is printify hoodie', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodePrintifies[2]->label_id,
    ];
    $imageName = $this->barcodePrintifies[2]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35562540",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 2540,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 2540
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 14,
        "image_height" => 10
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 0.625,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 11,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.61111
    ]);
})->skip();

test('Get printing success - store is not resize 7x8', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeNotResizes[0]->label_id,
    ];
    $imageName = $this->barcodeNotResizes[0]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "7x8",
        "platen_width" => "7",
        "platen_height" => "8",
        "offset_left" => "00000000",
        "image_size" => "17782032",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 7,
        "image_height" => 8
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 1,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 9,
        "height" => 9,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.25,
        "width" => 0.5,
        "height" => 0.5
    ]);
})->skip();

test('Get printing success - store is not resize 14x16 - artwork inside platen', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeNotResizes[1]->label_id,
    ];
    $imageName = $this->barcodeNotResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00630000",
        "image_size" => "34293429",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 63,
        "offset_top" => 0,
        "image_width" => 3429,
        "image_height" => 3429,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 63,
        "offset_top" => 0,
        "image_width" => 3429,
        "image_height" => 3429
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0.24803,
        "offset_top" => 0,
        "image_width" => 13.5,
        "image_height" => 13.5
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.96429,
        "height" => 0.84375,
        "top" => 0,
        "left" => 0.01772
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => -0.00197,
        "width" => 15.5,
        "height" => 14.5,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.06934,
        "width" => 0.86111,
        "height" => 0.80556
    ]);
})->skip();

test('Get printing success - store is resize, style off resize 7x8', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[0]->id)->update([
        'image_width' => 2100,
        'image_height' => 2400,
    ]);
    StoreProductStyleResize::factory()->create([
        'store_id' => $this->barcodeResizes[0]->store_id,
        'product_style_sku' => $this->productStyles[0]->sku,
        'is_active' => 1,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[0]->label_id,
    ];
    $imageName = $this->barcodeResizes[0]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "7x8",
        "platen_width" => "7",
        "platen_height" => "8",
        "offset_left" => "00000000",
        "image_size" => "17782032",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 2032
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 7,
        "image_height" => 8
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 1,
        "height" => 1,
        "top" => 0,
        "left" => 0
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 9,
        "height" => 9,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.25,
        "width" => 0.5,
        "height" => 0.5
    ]);
})->skip();

test('Get printing success - store is resize, style off resize 14x16', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 2100,
        'image_height' => 2100,
    ]);
    StoreProductStyleResize::factory()->create([
        'store_id' => $this->barcodeResizes[1]->store_id,
        'product_style_sku' => $this->productStyles[0]->sku,
        'is_active' => 1,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "08890000",
        "image_size" => "17781778",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 889,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 1778
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => 889,
        "offset_top" => 0,
        "image_width" => 1778,
        "image_height" => 1778
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => 3.5,
        "offset_top" => 0,
        "image_width" => 7,
        "image_height" => 7
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 0.5,
        "height" => 0.4375,
        "top" => 0,
        "left" => 0.25
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 9,
        "height" => 8,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.25,
        "width" => 0.5,
        "height" => 0.44444
    ]);
})->skip();

test('Get printing fail - not resize platen 16x21 - Platen 16x21 is not supported.', function () {
    //store not resize
    SaleOrderItemImage::where('id', $this->itemImageNotResizes[1]->id)->update([
        'image_width' => 4500,
        'image_height' => 4500,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0,
            'width' => 1,
            'height' => 1,
        ]),
        'custom_platen' => '16x21'
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeNotResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        "message" => "Platen 16x21 is not supported.",
    ]);
})->skip();


test('Get printing success - not resize, artwork oversize, trim inside platen', function () {
    //store not resize
    SaleOrderItemImage::where('id', $this->itemImageNotResizes[1]->id)->update([
        'image_width' => 8400,
        'image_height' => 8400,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0.25,
            'width' => 0.5,
            'height' => 0.5,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeNotResizes[1]->label_id,
    ];
    $imageName = $this->barcodeNotResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/trim/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556,
    ]);

    //store resize nhưng product style off resize
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 8400,
        'image_height' => 8400,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0.25,
            'width' => 0.5,
            'height' => 0.5,
        ])
    ]);
    StoreProductStyleResize::factory()->create([
        'store_id' => $this->barcodeResizes[1]->store_id,
        'product_style_sku' => $this->productStyles[0]->sku,
        'is_active' => 1,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/trim/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => -1778,
        "offset_top" => 0,
        "image_width" => 7112,
        "image_height" => 7112
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => -7,
        "offset_top" => 0,
        "image_width" => 28,
        "image_height" => 28
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 2,
        "height" => 1.75,
        "top" => 0,
        "left" => -0.5
    ]);
    expect($position['scale'])->toEqual(1);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 15,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.83333
    ]);
})->skip();

test('Get printing success - not resize, artwork oversize, trim exceed platen', function () {
    //store not resize
    SaleOrderItemImage::where('id', $this->itemImageNotResizes[1]->id)->update([
        'image_width' => 9000,
        'image_height' => 9000,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0.25,
            'width' => 0.5,
            'height' => 0.5,
        ])
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeNotResizes[1]->label_id,
    ];
    $imageName = $this->barcodeNotResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/trim/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556,
    ]);

    //store resize nhưng product style off resize
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'image_width' => 9000,
        'image_height' => 9000,
        'pretreat_info' => json_encode([
            'top' => 0,
            'left' => 0.25,
            'width' => 0.5,
            'height' => 0.5,
        ])
    ]);
    StoreProductStyleResize::factory()->create([
        'store_id' => $this->barcodeResizes[1]->store_id,
        'product_style_sku' => $this->productStyles[0]->sku,
        'is_active' => 1,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $imageName = $this->barcodeResizes[1]->sku . '-0.png';
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $arx = $response['data']['arx'];
    expect($arx)->toMatchArray([
        "is_resize" => false,
        "platen_size" => "14x16",
        "platen_width" => "14",
        "platen_height" => "16",
        "offset_left" => "00000000",
        "image_size" => "35563556",
        "image_url" => env('AWS_S3_URL') . "/artwork/1111-11-11/trim/" . $imageName,
    ]);
    expect($arx['adjust'])->toMatchArray([
        "offset_left" => 0,
        "offset_top" => 0,
        "image_width" => 3556,
        "image_height" => 3556,
    ]);

    $position = $response['data']['position'];
    expect($position['adjustMm'])->toMatchArray([
        "offset_left" => -1778,
        "offset_top" => 0,
        "image_width" => 7112,
        "image_height" => 7112
    ]);
    expect($position['adjust'])->toMatchArray([
        "offset_left" => -7,
        "offset_top" => 0,
        "image_width" => 28,
        "image_height" => 28
    ]);
    expect($position['artwork'])->toMatchArray([
        "width" => 2,
        "height" => 1.75,
        "top" => 0,
        "left" => -0.5
    ]);
    expect($position['scale'])->toEqual(0.93);
    expect($position['pretreat_zone'])->toMatchArray([
        "top" => 0,
        "left" => 0,
        "width" => 16,
        "height" => 15,
        "expand_x" => 1,
        "expand_y" => 0.5
    ]);
    expect($position['pretreat_side'])->toMatchArray([
        "top" => 0,
        "left" => 0.05556,
        "width" => 0.88889,
        "height" => 0.83333
    ]);
})->skip();

test('Get printing fail - label is required', function () {
    $params = [
        'employee_id' => $this->employee->id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        "label" => [
            0 => "The label field is required."
        ]
    ]);
})->skip();

test('Get printing fail - employee is required', function () {
    $params = [
        'label' => 'sku_not_found',
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toMatchArray([
        "employee_id" => [
            0 => "The employee id field is required."
        ]
    ]);
})->skip();

test('Get printing fail - Label not found', function () {
    $params = [
        'employee_id' => $this->employee->id,
        'label' => 'sku_not_found',
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Label not found"
    ]);
})->skip();

test('Get printing fail - Order not found', function () {
    SaleOrderItemBarcode::where('id', $this->barcodeResizes[1]->id)->update([
        'order_id' => 0,
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Order not found"
    ]);
})->skip();

test('Get printing fail - Order status not allowed', function () {
    foreach (SaleOrder::ARRAY_STATUS_INACTIVE as $orderStatus) {
        SaleOrder::where('id', $this->orderResize->id)->update([
            'order_status' => $orderStatus,
        ]);
        $params = [
            'employee_id' => $this->employee->id,
            'label' => $this->barcodeResizes[1]->label_id,
        ];
        $endpoint = $this->endpoint . http_build_query($params);
        $response = $this->get($endpoint);
        $response->assertStatus(422);
        $response = json_decode($response->getContent(), true);
        expect($response)->toEqual([
            "message" => "Sale order is " . str_replace('_', ' ', $orderStatus)
        ]);
    }
})->skip();

test('Get printing fail - Employee not found', function () {
    $params = [
        'employee_id' => 0,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Employee not found"
    ]);
})->skip();

test('Get printing fail - This label belongs to another warehouse', function () {
    Employee::where('id', $this->employee->id)->update([
        'warehouse_id' => $this->barcodeResizes[1]->warehouse_id + 1
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "This label belongs to another warehouse"
    ]);
})->skip();

test('Get printing fail - Image not found!', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'print_side' => 3
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Image not found!"
    ]);
})->skip();

test('Get printing fail - Platen size missing', function () {
    PrintingPresetSku::where('sku', $this->itemImageResizes[1]->product_sku)->update([
        'platen_front_size' => null
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "platen_front_size is missing"
    ]);
})->skip();

test('Get printing fail - Preset pretreat not found!', function () {
    PretreatPreset::where('id', $this->pretreatPreset->id)->delete();
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Preset pretreat not found!"
    ]);
})->skip();

test('Get printing fail - Product color not found!', function () {
    ProductColor::where('sku', $this->saleOrderItemsResize[1]->product_color_sku)->delete();
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Product color not found!"
    ]);
})->skip();

test('Get printing fail - Preset not found', function () {
    SaleOrderItemImage::where('id', $this->itemImageResizes[1]->id)->update([
        'color_new' => 17
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Preset not found"
    ]);
})->skip();;

test('Get printing fail - Preset printing not found!', function () {
    PrintingPresetSku::where('sku', $this->itemImageResizes[1]->product_sku)->update([
        'mix_ink' => "empty",
        'black_ink' => "empty",
        'white_ink' => "empty",
    ]);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toEqual([
        "message" => "Preset printing not found!"
    ]);
})->skip();

test('get bulb power sucess', function () {
    $endpoint = '/api/bulb-power/pretreat';
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $xml = $response['data']['XMLData'];
    $xml = simplexml_load_string($xml);
    expect($xml->Art->ArtInfo->PretreatCure->BulbPowers->int)->toMatchArray([
        1, 2
    ]);
    expect($xml->Art->ArtInfo->PrintCure->BulbPowers->int)->toMatchArray([
        1, 2
    ]);
})->skip();

test('update bulb power pretreat', function () {
    $endpointUpdate = '/api/bulb-power/pretreat';
    $inputs = [
        'bulbPowers' => [
            3, 4
        ]
    ];
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($endpointUpdate, $inputs);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $xml = $response['data']['XMLData'];
    $xml = simplexml_load_string($xml);
    expect($xml->Art->ArtInfo->PretreatCure->BulbPowers->int)->toMatchArray([
        3, 4
    ]);
    expect($xml->Art->ArtInfo->PrintCure->BulbPowers->int)->toMatchArray([
        1, 2
    ]);
})->skip();

test('update bulb power printing', function () {
    $endpointUpdate = '/api/bulb-power/printing';
    $inputs = [
        'bulbPowers' => [
            3, 4
        ]
    ];
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($endpointUpdate, $inputs);
    $params = [
        'employee_id' => $this->employee->id,
        'label' => $this->barcodeResizes[1]->label_id,
    ];
    $endpoint = $this->endpoint . http_build_query($params);
    $response = $this->get($endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $xml = $response['data']['XMLData'];
    $xml = simplexml_load_string($xml);
    expect($xml->Art->ArtInfo->PretreatCure->BulbPowers->int)->toMatchArray([
        1, 2
    ]);
    expect($xml->Art->ArtInfo->PrintCure->BulbPowers->int)->toMatchArray([
        3, 4
    ]);
})->skip();
