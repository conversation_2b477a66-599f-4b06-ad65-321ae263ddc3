<?php

use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\User;
use App\Models\Warehouse;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/inventory-deduction/revert';
    $this->order = SaleOrder::factory()->create();
    $this->product = Product::factory()->create(['sku' => 'UNGH1M0XL', 'gtin' => '00821780067052']);
    $this->location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->deduction = InventoryDeduction::factory()->create([
        'product_id' => $this->product->id,
        'warehouse_id' => $this->warehouse->id,
        'location_id' => $this->location->id,
        'sale_order_id' => $this->order->id,
    ]);
    $this->inventory = Inventory::factory()->create([
        'object_name' => Inventory::OBJECT_DEDUCTION,
        'object_id' => $this->deduction->id,
    ]);
    $this->productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $this->locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->product->id, 'quantity' => 10]);
    $this->params = [
        'id' => '',
    ];
});

/// validate
test('revert deduction failed - validate', function () {
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'id' => ['The id field is required.']
    ]);

    $this->deduction->is_deleted = 1;
    $this->deduction->save();
    $this->params = [
        'id' => $this->deduction->id
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'Inventory deduction not exists.'
    ]);
});

test('revert deduction success', function () {
    $this->params = [
        'id' => $this->deduction->id
    ];
    $deductionBeforeRevert = InventoryDeduction::find($this->params['id']);
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'success'
    ]);

    $inventoryDeduction = InventoryDeduction::where('id', $this->params['id'])->where('is_deleted', 0)->first();
    $this->assertEquals($inventoryDeduction, null);

    $inventory = Inventory::where('object_id', $this->params['id'])
        ->where('object_name', Inventory::OBJECT_DEDUCTION)
        ->where('is_deleted', 0)
        ->first();
    $this->assertEquals($inventory, null);

    //Assert quantity += 1
    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $this->productQuantityBeforeTest->quantity + 1);

    //Assert quantity += 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->where('location_id', $this->location->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $this->locationQuantityBeforeTest->quantity + 1);

    if ($deductionBeforeRevert->label_id) {
        $this->assertDatabaseHas('sale_order_history', ['order_id' => InventoryDeduction::where('id', $this->params['id'])->first()?->sale_order_id, 'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE]);
    }
});

// deduction success with Mexico.
test('deduction success with Mexico - only inventory deduction', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();

    $partNumber = PartNumber::factory([
        'product_id' => $this->product->id,
        'country' => 'VN',
    ])->create();
    $barcodeLabel = SaleOrderItemBarcode::factory()->create([
        'part_number_id' => $partNumber->id,
        'label_id' => faker::create()->password(6, 10)
    ]);
    $this->productQuantityBeforeTest->warehouse_id = $warehouse->id;
    $this->productQuantityBeforeTest->save();
    $this->deduction->warehouse_id = $warehouse->id;
    $this->deduction->label_id = $barcodeLabel->label_id;
    $this->deduction->save();

    $this->params = [
        'id' => $this->deduction->id
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'success'
    ]);

    $inventoryDeduction = InventoryDeduction::where('id', $this->params['id'])->where('is_deleted', 0)->first();
    $this->assertEquals($inventoryDeduction, null);

    $inventory = Inventory::where('object_id', $this->params['id'])
        ->where('object_name', Inventory::OBJECT_DEDUCTION)
        ->where('is_deleted', 0)
        ->first();

    $this->assertEquals($inventory, null);

    //Assert quantity += 1
    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $this->productQuantityBeforeTest->quantity + 1);

    //Assert quantity += 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->where('location_id', $this->location->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $this->locationQuantityBeforeTest->quantity + 1);

    $this->assertDatabaseHas('sale_order_history', ['order_id' => InventoryDeduction::where('id', $this->params['id'])->first()?->sale_order_id, 'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE]);
});

// deduction success with mexico many inventory deduction
test('deduction success with Mexico - many inventory deduction', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $this->productQuantityBeforeTest->warehouse_id = $warehouse->id;
    $this->productQuantityBeforeTest->save();

    $partNumber = PartNumber::factory()->createMany([
        [
            'product_id' => $this->product->id,
            'country' => substr(Str::random(), 0, 2),
        ],
        [
            'product_id' => $this->product->id,
            'country' => substr(Str::random(), 0, 2),
        ]
    ]);
    $barcodeLabel = SaleOrderItemBarcode::factory()->create([
        'part_number_id' => $partNumber->first()->id,
        'label_id' => faker::create()->password(6, 10)
    ]);
    $this->deduction->warehouse_id = $warehouse->id;
    $this->deduction->label_id = $barcodeLabel->label_id;
    $this->deduction->save();

    // data change
    InventoryDeduction::factory()->create([
        'product_id' => $this->product->id,
        'warehouse_id' => $warehouse->id,
        'location_id' => $this->location->id,
        'coo_iso2' => $partNumber->last()->country,
        'label_id' => $barcodeLabel->label_id
    ]);

    $this->params = [
        'id' => $this->deduction->id
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'success'
    ]);

    $inventoryDeduction = InventoryDeduction::where('id', $this->params['id'])->where('is_deleted', 0)->first();
    $this->assertEquals($inventoryDeduction, null);

    $inventory = Inventory::where('object_id', $this->params['id'])
        ->where('object_name', Inventory::OBJECT_DEDUCTION)
        ->where('is_deleted', 0)
        ->first();
    $this->assertEquals($inventory, null);

    //Assert quantity += 1
    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $this->productQuantityBeforeTest->quantity + 1);

    //Assert quantity += 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->where('location_id', $this->location->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $this->locationQuantityBeforeTest->quantity + 1);

    $barcode = SaleOrderItemBarcode::where('label_id', $barcodeLabel->label_id)->first();
    $this->assertEquals($barcode->part_number_id, $partNumber->last()->id);
    $this->assertDatabaseHas('sale_order_history', ['order_id' => InventoryDeduction::where('id', $this->params['id'])->first()?->sale_order_id, 'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE]);
});

// deduction success with Mexico.
test('deduction success with Mexico - only inventory deduction but barcode is delete', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();

    $partNumber = PartNumber::factory([
        'product_id' => $this->product->id,
        'country' => 'VN',
    ])->create();
    $barcodeLabel = SaleOrderItemBarcode::factory()->create([
        'part_number_id' => $partNumber->id,
        'label_id' => faker::create()->password(6, 10),
        'is_deleted' => 1
    ]);
    $this->productQuantityBeforeTest->warehouse_id = $warehouse->id;
    $this->productQuantityBeforeTest->save();
    $this->deduction->warehouse_id = $warehouse->id;
    $this->deduction->label_id = $barcodeLabel->label_id;
    $this->deduction->save();

    $this->params = [
        'id' => $this->deduction->id
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'message' => 'The barcode for this deduction is deleted.'
    ]);

    $inventoryDeduction = InventoryDeduction::where('id', $this->params['id'])->where('is_deleted', 1)->first();
    $this->assertEquals($inventoryDeduction, null);

    $inventory = Inventory::where('object_id', $this->params['id'])->where('is_deleted', 1)->first();
    $this->assertEquals($inventory, null);

    //Assert quantity += 1
    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->product->id)->first();
    $this->assertEquals($productQuantity->quantity, $this->productQuantityBeforeTest->quantity);

    //Assert quantity += 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->product->id)->where('location_id', $this->location->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $this->locationQuantityBeforeTest->quantity);
});
