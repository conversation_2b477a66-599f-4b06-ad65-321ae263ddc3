<?php

use App\Models\FifoInventory;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Warehouse;
use App\Repositories\InventoryRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    config(['queue.default' => 'sync']);
    $this->products = Product::factory()->count(2)->sequence(
        fn ($sequence) => [
            'id' => $sequence->index + 1,
            'style' => 'style ' . ($sequence->index + 1),
            'sku' => 'sku' . ($sequence->index + 1),
        ],
    )->create();

    Warehouse::factory()->create([
        'id' => 1,
        'code' => 'SJ'
    ]);

    //mock data trước 2022-10-01
    Inventory::factory()->count(10)->sequence(
        ['direction' => 0, 'quantity' => 2],
        ['direction' => 1, 'quantity' => 1],
    )->sequence(
        ['created_at' => '2022-09-09'],
        ['created_at' => '2022-07-09'],
        ['created_at' => '2022-06-09'],
        ['created_at' => '2022-07-09'],
        ['created_at' => '2022-07-09'],
    )->sequence(
        [
            'product_id' => $this->products[0]->id,
            'warehouse_id' => 1,
            'is_deleted' => 0
        ],
    )->create();
    //mock data sau 2022-10-01 và trước 2022-10-31
    Inventory::factory()->count(10)->sequence(
        ['direction' => 0, 'quantity' => 2],
        ['direction' => 1, 'quantity' => 1],
    )->sequence(
        ['created_at' => '2022-10-09'],
        ['created_at' => '2022-10-10'],
        ['created_at' => '2022-10-09'],
        ['created_at' => '2022-10-19'],
        ['created_at' => '2022-10-09'],
    )->sequence(
        [
            'product_id' => $this->products[0]->id,
            'warehouse_id' => 1,
            'is_deleted' => 0
        ],
    )->create();

    //mock data khong thoa man
    Inventory::factory()->count(10)->sequence(
        ['created_at' => '2022-11-11'],
        ['warehouse_id' => 2],
        ['is_deleted' => 1],
    )->sequence(
        [
            'product_id' => $this->products[0]->id,
        ],
    )->create();

    //Mock data PO
    PurchaseOrder::factory()->count(2)->has(
        PurchaseOrderItem::factory()->sequence(
            [
                'quantity' => 3,
                'price' => 2.0,
                'product_id' => $this->products[0]->id,
            ],
        ),
        'items',
    )->sequence(
        [
            'order_date' => '2022-04-30',
        ],
        [
            'order_date' => '2022-05-30',
        ],
    )->sequence(
        [
            'order_status' => PurchaseOrder::COMPLETED_STATUS,
            'warehouse_id' => 1,
        ],
    )->create();
});

// kiểm tra function fetchInventoryEndUnit
test('check function fetchInventoryEndUnit', function () {
    $startDate = '2022-10-01';
    $endDate = '2022-10-31';
    $warehouseId = 1;
    $inventoryRepository = new InventoryRepository();
    $inventoryData = $inventoryRepository->fetchInventoryEndUnit($startDate, $endDate, $warehouseId);
    $dataExpect = [
        (object) [
            'sku' => 'sku1',
            'product_id' => 1,
            'product_style' => 'style 1',
            'start_unit' => 5,
            'end_unit' => 10,
        ]
    ];
    expect($inventoryData)->toEqual($dataExpect);
});

// kiểm tra function handle
test('check function handle', function () {
    DB::table('cost_2021')->insert([
        'style' => 'style 1',
        'cost' => 1.0
    ]);
    $this->artisan('inventory:report 2022-10-31');
    $data = FifoInventory::select('product_id', 'sku', 'value', 'start_date', 'end_date', 'start_unit', 'end_unit', 'last_unit', 'coun_transaction', 'warehouse_id', 'product_style')
        ->get()
        ->toArray();
    $dataExpect = [
        [
            'product_id' => 1,
            'sku' => 'sku1',
            'value' => '16.00',
            'start_date' => '2022-10-01',
            'end_date' => '2022-10-31',
            'start_unit' => 5,
            'end_unit' => 10,
            'last_unit' => 0,
            'coun_transaction' => 2,
            'warehouse_id' => 1,
            'product_style' => 'style 1'
        ]
    ];
    expect($data)->toEqual($dataExpect);
});
