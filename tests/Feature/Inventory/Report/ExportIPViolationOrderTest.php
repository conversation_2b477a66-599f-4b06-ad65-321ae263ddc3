<?php

use App\Exports\IPViolationOrderExport;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->store = Store::factory()->count(2)->create();
    $this->product = Product::factory([
        'color' => 'color1',
        'size' => 'size1',
        'sku' => 'UNPTOO1L'
    ])->create();
    $this->imageHash = \App\Models\ImageHash::factory([
        'is_ip_violation' => 1
    ])->create();
    $this->saleOrder = SaleOrder::factory()->count(5)->sequence(
        [
            'order_status' => SaleOrder::REJECTED,
            'rejected_reason' => 'ip_violation',
            'store_id' => $this->store[0]->id,
            'is_test' => 0,
            'created_at' => '2023-10-10 00:00:00',
        ],
        [
            'order_status' => SaleOrder::REJECTED,
            'rejected_reason' => 'ip_violation',
            'store_id' => $this->store[1]->id,
            'is_test' => 0,
            'created_at' => '2023-10-10 00:00:00',
        ],
        ['warehouse_id' => 1,
            'order_status' => SaleOrder::REJECTED,
            'rejected_reason' => 'test',
            'store_id' => $this->store[0]->id,
            'is_test' => 0,
            'created_at' => '2023-10-10 00:00:00',
        ],
        [
            'warehouse_id' => 1,
            'order_status' => SaleOrder::NEW_ORDER,
            'rejected_reason' => 'ip_violation',
            'store_id' => $this->store[0]->id,
            'is_test' => 0,
            'created_at' => '2023-10-10 00:00:00',
        ],
        ['warehouse_id' => 1,
            'order_status' => SaleOrder::REJECTED,
            'store_id' => $this->store[0]->id,
            'rejected_reason' => 'test',
            'is_test' => 1,
            'created_at' => '2023-10-10 00:00:00',
        ],
    )->has(SaleOrderItem::factory([
        'store_id' => $this->store[0]->id,
        'product_id' => $this->product->id,
        'product_sku' => $this->product->sku,
    ])->has(\App\Models\SaleOrderItemImage::factory([
        'image_hash_id' => $this->imageHash->id,
    ]), 'images'), 'items')
    ->has(SaleOrderAddress::factory([
        'type_address' => SaleOrderAddress::TO_ADDRESS
    ]), 'addressSaleOrder')->create();
});

// kiểm tra số lượng data export
test('check count data export: has data and param not exists store_id', function () {
    $request = [
        'start_date' => '2023-09-09',
        'end_date' => '2023-11-11',
    ];
    $deductionExport = new IPViolationOrderExport($request);
    $dataExport = $deductionExport->collection();
    expect($dataExport)->toHaveCount(2);
});

test('check count data export: has data and param exists store_id', function () {
    $request = [
        'start_date' => '2023-09-09',
        'end_date' => '2023-11-11',
        'store_ids' => $this->store[0]->id
    ];
    $deductionExport = new IPViolationOrderExport($request);
    $dataExport = $deductionExport->collection();
    expect($dataExport)->toHaveCount(1);
})->skip('Store 0 has 1 order with ip violation');
// kiểm tra số lượng data export
test('check count data export: not have data', function () {
    $request = [
        'start_date' => '2021-09-09',
        'end_date' => '2021-11-11',
    ];
    $fifoExport = new IPViolationOrderExport($request);
    $dataExport = $fifoExport->collection();
    expect($dataExport)->toHaveCount(0);
});
