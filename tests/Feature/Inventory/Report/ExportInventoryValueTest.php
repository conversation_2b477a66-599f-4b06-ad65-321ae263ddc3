<?php

use App\Exports\FifoExport;
use App\Models\FifoInventory;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->product = Product::factory()->create([
        'name' => 'Product 1',
        'sku' => 'SKU001',
    ]);
    FifoInventory::factory()->count(20)->sequence(
        ['warehouse_id' => 1],
        ['warehouse_id' => 2],
    )
        ->sequence(
            fn ($sequence) => [
                'product_id' => $this->product->id,
                'end_date' => $sequence->index < 10 ? '2022-09-30' : '2022-10-31',
            ],
        )->create();
});

// kiểm tra số lượng data export
test('check count data export inventory value: input valid', function () {
    $request = [
        'start_date' => '2022-01-01',
        'end_date' => '2022-10-31',
        'warehouse_id' => 1
    ];
    $fifoExport = new FifoExport($request);
    $dataExport = $fifoExport->setUpData();
    expect($dataExport)->toHaveCount(5);
});
// kiểm tra số lượng data export
test('check count data export inventory value: input invalid', function () {
    $requests = [
        [],
        [
            'end_date' => '2022-10-31',
            'warehouse_id' => 1
        ],
        [
            'start_date' => '2022-01-01',
            'warehouse_id' => 1
        ],
        [
            'start_date' => '2022-01-01',
            'end_date' => '2022-10-31',
        ],
    ];
    foreach ($requests as $request) {
        $fifoExport = new FifoExport($request);
        $dataExport = $fifoExport->setUpData();
        expect($dataExport)->toHaveCount(0);
    }
});
