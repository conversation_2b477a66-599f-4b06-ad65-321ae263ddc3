<?php

use App\Models\Product;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Repositories\LabelRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo = new LabelRepository();
    $this->productStyles = ProductStyle::factory()->count(4)->state(new Sequence(
        ['sku' => 'UNGH', 'type' => 'Fleece'],
        ['sku' => 'UNPT', 'type' => 'Tee'],
        ['sku' => 'YOGH', 'type' => 'Fleece'],
        ['sku' => 'UNGT', 'type' => 'Tee'],
    ))->create();
    $this->productSize = ProductSize::factory()->count(4)->state(new Sequence(
        ['sku' => '5XL', 'name' => '5XL'],
        ['sku' => '00L', 'name' => 'L'],
        ['sku' => '0XL', 'name' => 'XL'],
        ['sku' => '00S', 'name' => 'S'],
    ))->create();
    $this->product = Product::factory()->createMany(
        [
            [
                'sku' => 'shoes',
                'style' => 'Fleece',
                'weight_single' => 8,
                'weight_multiple' => 10
            ],
            [
                'sku' => 'shoes1',
                'style' => 'Fleece',
                'weight_single' => 8,
                'weight_multiple' => 10
            ]
        ],
    );
    $this->saleOrder = SaleOrder::factory()->has(
        SaleOrderItem::factory()->count(2)->state(new Sequence([
            'product_style_sku' => $this->productStyles->first()->sku,
            'product_size_sku' => $this->productSize->first()->sku,
            'product_id' => $this->product->first()->id,
            'quantity' => 1
        ], [
            'product_style_sku' => $this->productStyles->last()->sku,
            'product_size_sku' => $this->productSize->last()->sku,
            'product_id' => $this->product->last()->id,
            'quantity' => 1
        ])), 'items')->create([
            'order_status' => 'in_production',
            'external_number' => '071722-SJ-M-000368',
            'order_quantity' => 2
        ]);
});

// not found data product
test('Not found data product', function () {
    Product::query()->delete();
    calculatorWeightForSaleOrder($this->saleOrder);
})->throws('Not found data product');

// Product not data weight multiple
test('Not found data product weight multiple', function () {
    $this->product->first()->weight_single = null;
    $this->product->first()->weight_multiple = null;
    $this->product->first()->save();
    calculatorWeightForSaleOrder($this->saleOrder);
})->throws('Weight not defined for product SKU shoes (Fleece 5XL) for multiple item orders.');

// Product not data weight single
test('Not found data product weight single', function () {
    $this->product->first()->weight_single = null;
    $this->product->first()->weight_multiple = null;
    $this->product->first()->save();
    $this->saleOrder->order_quantity = 1;
    calculatorWeightForSaleOrder($this->saleOrder);
})->throws('Weight not defined for product SKU shoes (Fleece 5XL) for single item order.');

// weight for sale order success - multiple
test('weight for sale order multiple', function () {
    $data = calculatorWeightForSaleOrder($this->saleOrder);
    expect($data)->toEqual(20);
});

// weight for sale order success - single
test('weight for sale order single', function () {
    $this->saleOrder->order_quantity = 1;
    $data = calculatorWeightForSaleOrder($this->saleOrder);
    expect($data)->toEqual(16);
});

// weight for store has product sku weight
test('weight for store has product sku weight', function () {
    $storeProductWeight = [
        'shoes1' => 50
    ];
    $data = calculatorWeightForSaleOrder($this->saleOrder, $storeProductWeight);
    expect($data)->toEqual(60);
});
