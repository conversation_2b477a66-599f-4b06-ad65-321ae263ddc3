<?php

use App\Models\Warehouse;
use App\Repositories\LabelRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo = new LabelRepository();
    $this->warehouse = Warehouse::factory()->create();
});

// có data from - trả về mảng data address from
test('sale order address from - success', function () {
    $dataExpect = [
        'name' => $this->warehouse->from_name ?? 'FULFILLMENT CENTER',
        'company' => 'SWIFTPOD LLC',
        'street1' => $this->warehouse->street1 ?? '',
        'street2' => $this->warehouse->street2 ?? '',
        'city' => $this->warehouse->city ?? '',
        'state' => $this->warehouse->state ?? '',
        'zip' => $this->warehouse->zip ?? '',
        'country' => $this->warehouse->country ?? '',
        'phone' => LabelRepository::getNumberPhone($this->warehouse->phone, $this->warehouse->country ?? ''),
        'email' => '',
        'carrier_facility' => $this->warehouse->carrier_facility ?? null
    ];
    $dataAddress = $this->labelRepo->dataAddressFrom($this->warehouse, 'datpt', 'usps');
    expect($dataAddress)->toEqual($dataExpect);
});
