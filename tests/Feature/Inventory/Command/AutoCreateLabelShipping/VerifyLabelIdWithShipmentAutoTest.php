<?php

use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\Shipment;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\LabelRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo = new LabelRepository();
    $this->wareHouse = Warehouse::factory()->create();
    $this->store = Store::factory()->create();
    $this->productStyles = ProductStyle::factory()->count(4)->state(new Sequence(
        ['sku' => 'UNGH', 'type' => 'Fleece'],
        ['sku' => 'UNPT', 'type' => 'Tee'],
        ['sku' => 'YOGH', 'type' => 'Fleece'],
        ['sku' => 'UNGT', 'type' => 'Tee'],
    ))->create();
    $this->productSize = ProductSize::factory()->count(4)->state(new Sequence(
        ['sku' => '5XL', 'name' => '5XL'],
        ['sku' => '00L', 'name' => 'L'],
        ['sku' => '0XL', 'name' => 'XL'],
        ['sku' => '00S', 'name' => 'S'],
    ))->create();
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'in_production',
        'shipping_method' => 'standard',
        'order_number' => '071722-SJ-M-000368',
        'external_number' => '071722-SJ-M',
        'warehouse_id' => $this->wareHouse->id,
        'store_id' => $this->store->id,
        'is_shipment_create_error' => 0,
        'order_quantity' => 1,
        'barcode_printed_status' => 1,
        'shipment_id' => null,
        'order_date' => Carbon::now(),
        'order_pulled_status' => 1,
        'order_staged_status' => 1,
        'order_printed_status' => 1,
    ])
        ->has(SaleOrderItem::factory()->count(2)->state(new Sequence(
            [
                'product_style_sku' => $this->productStyles->first()->sku,
                'product_size_sku' => $this->productSize->first()->sku,
                'warehouse_id' => $this->wareHouse->id,
                'store_id' => $this->store->id,
                'quantity' => 1,
            ],
            [
                'product_style_sku' => $this->productStyles->last()->sku,
                'product_size_sku' => $this->productSize->last()->sku,
                'warehouse_id' => $this->wareHouse->id,
                'store_id' => $this->store->id,
                'quantity' => 1
            ],
        ))
            ->has(SaleOrderItemBarcode::factory([
                'warehouse_id' => $this->wareHouse->id,
                'is_deleted' => 0
            ]), 'barcodes'), 'items')
        ->create();
    $this->shipment = Shipment::factory([
        'order_id' => $this->saleOrder->id
    ])->create();
});

test('verify label id auto success', function () {
    $this->labelRepo->verifyLabelIdWithShipmentAuto($this->saleOrder->items, $this->shipment->id);
    foreach ($this->saleOrder->items as $saleOrderItem) {
        $this->assertDatabaseHas('shipment_item', [
            'order_id' => $saleOrderItem->order_id,
            'quantity' => $saleOrderItem->quantity,
            'shipment_id' => $this->shipment->id,
            'order_item_id' => $saleOrderItem->id
        ]);
        foreach ($saleOrderItem->barcodes as $barcode) {
            $this->assertDatabaseHas('shipment_item_label', [
                'label_id' => $barcode->label_id,
                'shipment_id' => $this->shipment->id,
            ]);
        }
    }
});

test('verify label id auto fail', function () {
    $this->labelRepo->verifyLabelIdWithShipmentAuto($this->saleOrder->items, null);
})->throws(Exception::class);
