<?php

use App\Models\SaleOrder;
use App\Models\Store;
use App\Repositories\LabelRepository;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
beforeEach(function () {
    $this->labeRepo = new LabelRepository();
});

// Tra về mảng rỗng
test('rate not map service', function () {
    $responseData = '{"rates":[{"id":"rate_ee62a3bd623b412e9d683837c32b8e31","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQStandard","carrier":"AsendiaUsa","rate":"5.41","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQStandard"},{"id":"rate_23120fcbb21a49bf9ef8cb5bdecd374d","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelect","carrier":"AsendiaUsa","rate":"19.76","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQSelect"}]}';
    $dataShipmentMock = \EasyPost\Util::convertToEasyPostObject(json_decode($responseData, true), '*********', null, 'Rate');
    $dataSaleOrder = SaleOrder::factory()->create([
        'shipping_method' => 'standard',
        'store_id' => faker::create()->randomNumber()
    ]);
    $country = 'vn';
    $dataResult = $this->labeRepo->getRateInternational($dataShipmentMock, $country, $dataSaleOrder);
    expect($dataResult)->toBeEmpty();
});

// Trả về thành công
test('get rate success', function () {
    $serviceCode = 'ePAQPlus';
    $responseData = '{"rates":[{"id":"rate_ee62a3bd623b412e9d683837c32b8e31","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQStandard","carrier":"AsendiaUsa","rate":"5.41","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQStandard"},{"id":"rate_3912f2f7e9c243689ad4eadaae993fdd","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQPlus","carrier":"AsendiaUsa","rate":"6.23","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQPlus"},{"id":"rate_23120fcbb21a49bf9ef8cb5bdecd374d","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelect","carrier":"AsendiaUsa","rate":"19.76","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQSelect"}]}';
    $dataShipmentMock = \EasyPost\Util::convertToEasyPostObject(json_decode($responseData, true), '*********', null, 'Rate');
    $dataSaleOrder = SaleOrder::factory()->create([
        'shipping_method' => 'standard',
        'store_id' => faker::create()->randomNumber()
    ]);
    $country = 'vn';
    $dataResult = $this->labeRepo->getRateInternational($dataShipmentMock, $country, $dataSaleOrder);
    expect($serviceCode)->toBeIn($dataResult);
});

// Trả về thành công
test('get rate success - shipping method letter_first_class', function () { // update for slack https://swiftpod.slack.com/archives/C03KTBJHTDZ/p1742313365188669?thread_ts=**********.260369&cid=C03KTBJHTDZ
    $serviceCode = 'ePAQPlus';
    $responseData = '{"rates":[{"id":"rate_ee62a3bd623b412e9d683837c32b8e31","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQPlus","carrier":"AsendiaUsa","rate":"5.41","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQStandard"},{"id":"rate_3912f2f7e9c243689ad4eadaae993fdd","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelectDirectAccessCanadaDDP","carrier":"AsendiaUsa","rate":"6.23","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQPlus"},{"id":"rate_23120fcbb21a49bf9ef8cb5bdecd374d","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelect","carrier":"AsendiaUsa","rate":"19.76","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQSelect"}]}';
    $dataShipmentMock = \EasyPost\Util::convertToEasyPostObject(json_decode($responseData, true), '*********', null, 'Rate');
    $dataSaleOrder = SaleOrder::factory()->create([
        'shipping_method' => 'letter_first_class',
        'store_id' => faker::create()->randomNumber()
    ]);
    $country = 'vn';
    $dataResult = $this->labeRepo->getRateInternational($dataShipmentMock, $country, $dataSaleOrder);
    expect($serviceCode)->toBeIn($dataResult);
});

// Trả về thành công
test('get rate success international for printify with ship to CA', function () {
    $serviceCode = LabelRepository::RATE_SHIP_INTERNATIONAL_CANADA;
    $responseData = '{"rates":[{"id":"rate_ee62a3bd623b412e9d683837c32b8e31","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelectDirectAccessCanadaDDP","carrier":"AsendiaUsa","rate":"5.41","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQStandard"},{"id":"rate_3912f2f7e9c243689ad4eadaae993fdd","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQPlus","carrier":"AsendiaUsa","rate":"6.23","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQPlus"},{"id":"rate_23120fcbb21a49bf9ef8cb5bdecd374d","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelect","carrier":"AsendiaUsa","rate":"19.76","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQSelect"}]}';
    $dataShipmentMock = \EasyPost\Util::convertToEasyPostObject(json_decode($responseData, true), '*********', null, 'Rate');
    $dataSaleOrder = SaleOrder::factory()->create([
        'shipping_method' => 'standard',
        'store_id' => Store::PRINTIFY_API_ID
    ]);
    $country = 'ca';
    $dataResult = $this->labeRepo->getRateInternational($dataShipmentMock, $country, $dataSaleOrder);
    expect($serviceCode)->toBeIn($dataResult);
});

test('get rate success international for printify with ship to not CA', function () {
    $serviceCode = 'ePAQPlus';
    $responseData = '{"rates":[{"id":"rate_ee62a3bd623b412e9d683837c32b8e31","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQStandard","carrier":"AsendiaUsa","rate":"5.41","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQStandard"},{"id":"rate_3912f2f7e9c243689ad4eadaae993fdd","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQPlus","carrier":"AsendiaUsa","rate":"6.23","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQPlus"},{"id":"rate_23120fcbb21a49bf9ef8cb5bdecd374d","object":"Rate","created_at":"2022-11-04T07:37:38Z","updated_at":"2022-11-04T07:37:38Z","mode":"production","service":"ePAQSelect","carrier":"AsendiaUsa","rate":"19.76","currency":"USD","retail_rate":null,"retail_currency":null,"list_rate":null,"list_currency":null,"billing_type":"carrier","delivery_days":null,"delivery_date":null,"delivery_date_guaranteed":false,"est_delivery_days":null,"shipment_id":"shp_e546b6946eb140a587559e5a727e9d09","carrier_account_id":"ca_145730a6c35c4562a07801d6fa61db95","service_name":"ePAQSelect"}]}';
    $dataShipmentMock = \EasyPost\Util::convertToEasyPostObject(json_decode($responseData, true), '*********', null, 'Rate');
    $dataSaleOrder = SaleOrder::factory()->create([
        'shipping_method' => 'standard',
        'store_id' => Store::PRINTIFY_API_ID
    ]);
    $country = 'VN';
    $dataResult = $this->labeRepo->getRateInternational($dataShipmentMock, $country, $dataSaleOrder);
    expect($serviceCode)->toBeIn($dataResult);
});
