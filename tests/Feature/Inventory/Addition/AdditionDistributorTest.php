<?php

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Employee;
use App\Models\Inventory;
use App\Models\InventoryAddition;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Setting;
use App\Models\TimeTracking;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Warehouse;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();

    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/inventory-addition/distributor';
    $this->purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $this->warehouse->id]);
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $timeTracking = TimeTracking::factory()->create(['job_type' => 'addition']);
    $this->location_max_box = 100;
    Setting::factory()->create([
        'name' => Setting::LOCATION_MAX_BOX,
        'value' => $this->location_max_box,
    ]);
    $this->params = [
        'tracking_number' => '1Z302E4R0334768605',
        'vendor_id' => $this->purchaseOrder->vendor_id,
        'location_id' => $this->location->id,
        'location_name' => $this->location->barcode,
        'barcode' => 'Barcode 123',
        'po_number' => $this->purchaseOrder->po_number,
        'invoice_number' => $this->purchaseOrder->invoice_number,
        'location_type' => $this->location->type,
        'employee_id' => $this->employee->code,
        'id_time_checking' => $timeTracking->id,
        'country' => '',
    ];
});

function buildRandomItems($purchaseOrder, $multipleItems = true)
{
    $totalItems = rand(2, 10);
    if (!$multipleItems) {
        $totalItems = 1;
    }
    $items = [];
    for ($i = 1; $i <= $totalItems; $i++) {
        $purchaseOrderItems = PurchaseOrderItem::factory()->for(Product::factory()->create(['sku' => "UNGH1M0XL$i", 'gtin' => "0082178006705$i"]))
            ->create(['po_id' => $purchaseOrder->id, 'quantity' => 10,  'price' => faker::create()->randomFloat(2, 1, 10)]);
        array_push($items, [
            'id' => rand(1, 10),
            'po_id' => $purchaseOrder->id,
            'po_box_id' => rand(1, 10),
            'sku' => $purchaseOrderItems->product->sku,
            'external_sku' => null,
            'product_id' => $purchaseOrderItems->product->id,
            'gtin' => $purchaseOrderItems->product->gtin,
            'quantity' => 10,
            'quantity_api' => 10,
            'created_at' => '2022-06-28 11:00:26',
            'updated_at' => null,
        ]);
    }

    return $items;
}

///Validate required field : missing po_number or invoice_number
test('missing po_number or invoice_number', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    $fields = ['po_number', 'invoice_number'];
    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $inputs);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

///Validate required field : missing cả po_number và invoice_number cùng lúc
test('addition failed - missing both po_number and invoice_number', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    unset($this->params['po_number']);
    unset($this->params['invoice_number']);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKeys(['po_number', 'invoice_number']);
});

///Validate required field : items, id_time_checking, employee_id
test('addition failed - missing items, id_time_checking, employee_id', function () {
    $fieldValidate = [
        'id_time_checking',
        'items',
        'employee_id',
    ];
    foreach ($fieldValidate as $field) {
        $input = $this->params;
        unset($input[$field]);

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $input);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

// addition failed - location max box 
test('addition failed - location max box', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    Box::factory()->count($this->location_max_box)->create(['location_id' => $this->location->id]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

///Employee thuộc warehouse khác => dừng addition
test('addition failed - employee in another warehouse', function () {
    $employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id + 1, 'code' => 'WA']);
    $this->params['employee_id'] = $employee->code;
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('employee_id');
});
///PO thuộc warehouse khác => dừng addition
test('addition failed - PO in another warehouse', function () {
    $warehouse = Warehouse::factory()->create();
    PurchaseOrder::where('po_number', $this->params['po_number'])->update(['warehouse_id' => $warehouse->id]);
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('purchase_order');
});
//Kiểm tra box đã tồn tại chưa ? nếu rồi thì dừng addition
test('addition failed - The box id has already been taken', function () {
    $items = buildRandomItems($this->purchaseOrder, false);
    $this->params['items'] = $items;
    $this->params['barcode'] = 'abc';
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => $this->params['barcode'],
        'location_id' => $this->location->id
    ]);
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('barcode');
});

//Kiểm tra nếu barcode khác empty thì location phải là Rack

test('addition failed - Location must is rack when barcode is NOT empty', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO98']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

//Nếu addition cùng lúc nhiều loại sản phẩm (count(items)>1) thì phải chọn location Pulling shelves

test('addition failed - Location must is Pulling shelves when total items > 1', function () {
    $items = buildRandomItems($this->purchaseOrder, true);
    $this->params['items'] = $items;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

//Nếu barcode khác rỗng  và items có nhiều hơn một loại product thì phải chọn location là Pulling shelves
test('addition failed - Location must is Pulling shelves when total items > 1 and barcode not empty', function () {
    $items = buildRandomItems($this->purchaseOrder, true);
    $this->params['items'] = $items;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

//nếu truyền 2 items khác purchase order thì dừng addition
test('Items belonging to more than PO', function () {
    $item1 = buildRandomItems($this->purchaseOrder, false);

    $product = Product::factory()->create(['sku' => 'ABC123', 'gtin' => '005621780067052']);
    $purchaseOrder = PurchaseOrder::factory()->create(['warehouse_id' => $this->warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2]);
    $item2 = buildRandomItems($purchaseOrder, false);
    $items = array_merge($item1, $item2);
    $this->params['items'] = $items;
    $this->params['barcode'] = '';
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO98']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('items');
});

//Kiểm tra đã tracking number trong addition chưa? nếu rồi thì dừng addition

test('addition failed - This tracking number was already added', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    InventoryAddition::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'tracking_number' => $this->params['tracking_number'],
    ]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('tracking_number');
});

///PO có trạng thái là completed hoặc canceled thì không được phép addition

test('addition failed - The purchase order has been completed or canceled', function () {
    $orderStatus = [
        PurchaseOrder::COMPLETED_STATUS,
        PurchaseOrder::CANCELLED_STATUS
    ];
    foreach ($orderStatus as $status) {
        $product = Product::factory()->create();
        $purchaseOrder = PurchaseOrder::factory()->create([
            'warehouse_id' => $this->warehouse->id,
            'order_status' => $status,
            'po_number' => 'PO 20220710',
            'invoice_number' => 'IN 20220710',
        ]);
        PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2]);
        $this->params['po_number'] = $purchaseOrder->po_number;
        $this->params['invoice_number'] = $purchaseOrder->invoice_number;
        $this->params['items'] = buildRandomItems($this->purchaseOrder, false);

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey('purchase_order');
    }
});

test('addition failed - quantity is zero', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);
    $this->params['items'][0]['quantity'] = 0;
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('product');
});
//addition thành công khi chọn location là RACK
test('addition SUCCESS - location is Rack', function () {
    $this->params['items'] = buildRandomItems($this->purchaseOrder, false);

    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->params['items'][0]['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $itemsInResponseData = buildResponseDataStruct(count($this->params['items']));

    $response->assertStatus(201)->assertJsonStructure($itemsInResponseData);

    ///Assert them inventory_addition thanh cong
    $addition = InventoryAddition::where('po_number', $this->params['po_number'])->first();
    $this->assertNotEmpty($addition);

    ///Assert them box thanh cong (Vì ban đầu chưa có box sẽ tự động tạo ra box trong quá trình addition)
    $box = Box::where('barcode', $this->params['barcode'])->first();
    $this->assertNotEmpty($box);

    ///Assert them box_moving thanh cong (Tạo box đồng thời thêm history trong box_moving)
    $boxMoving = BoxMoving::where('box_id', $box->id)->first();
    $this->assertNotEmpty($boxMoving);

    $purchaseOderItem = PurchaseOrderItem::where('product_id', $box->product_id)->first();
    ///Assert quantity_onhand = input quantity , received_status = received

    $this->assertEquals($purchaseOderItem->quantity_onhand, $box->quantity);
    $this->assertEquals($purchaseOderItem->received_status, PurchaseOrderItem::RECEIVED_STATUS);

    ///Assert order_status = completed
    $po = PurchaseOrder::where('po_number', $this->params['po_number'])->first();
    $this->assertEquals($po->order_status, PurchaseOrder::COMPLETED_STATUS);

    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $box->product_id)
        ->first();

    ///Assert quantity += input quantity  và incoming_stock -= input quantity
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity + $box->quantity);
    $this->assertEquals($productQuantity->incoming_stock, $productQuantityBeforeTest->incoming_stock - $box->quantity);

    $locationProductQuantity = LocationProduct::where('product_id', $box->product_id)
        ->where('location_id', $this->location->id)
        ->sum('quantity');
    ///Assert location quantity += input quantity ( hien tai location quantity = 0)
    $this->assertEquals($locationProductQuantity, $box->quantity);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
    foreach ($this->params['items'] as $item) {
        $poItem = PurchaseOrderItem::where('po_id', $this->purchaseOrder->id)->where('product_id', $item['product_id'])->first();
        $addition = InventoryAddition::where('po_id', $this->purchaseOrder->id)->where('product_id', $item['product_id'])->first();
        $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);
        $inventory = Inventory::where('object_id', $addition->id)
            ->where('direction', 0)
            ->where('type', Inventory::TYPE_INPUT)
            ->where('product_id', $item['product_id'])
            ->where('quantity', $item['quantity'])
            ->first();
        $this->assertNotEmpty($inventory);
        $box = Box::where('barcode', $this->params['barcode'])->first();
        $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $box->cost_value);
        $this->assertEquals($box->po_id, $po->id);
        $this->assertEquals($box->po_item_id, $poItem->id);
    }
});

//addition thành công khi chọn location là Pulling Shelves
test('addition SUCCESS - location is Pulling Shelves', function () {
    unset($this->params['barcode']);

    $items = buildRandomItems($this->purchaseOrder, true);
    $this->params['items'] = $items;

    $arrProductQuantity = [];
    foreach ($this->params['items'] as $item) {
        $productQuantity = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
            'product_id' => $item['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
        $arrProductQuantity[$item['product_id']] = [
            'quantity' => $productQuantity->quantity,
            'incoming_stock' => $productQuantity->incoming_stock,
        ];
    }

    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO189']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $itemsInResponseData = buildResponseDataStruct(count($this->params['items']));
    $response->assertStatus(201)->assertJsonStructure($itemsInResponseData);

    foreach ($this->params['items'] as $item) {
        ///Assert them inventory_addition thanh cong
        $addition = InventoryAddition::where('product_id', $item['product_id'])->first();
        $this->assertNotEmpty($addition);
        ///Assert them box_moving thanh cong (thêm history trong box_moving)
        $boxMoving = BoxMoving::where('inventory_addition_id', $addition->id)->first();
        $this->assertNotEmpty($boxMoving);
        $poItem = PurchaseOrderItem::where('po_id', $this->purchaseOrder->id)->where('product_id', $item['product_id'])->first();
        $addition = InventoryAddition::where('po_id', $this->purchaseOrder->id)->where('product_id', $item['product_id'])->first();
        $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);
        $inventory = Inventory::where('object_id', $addition->id)
            ->where('direction', 0)
            ->where('type', Inventory::TYPE_INPUT)
            ->where('product_id', $item['product_id'])
            ->where('quantity', $item['quantity'])
            ->first();
        $this->assertNotEmpty($inventory);
    }
    $po = PurchaseOrder::with('items')->first();

    ///Assert status PO and PO items
    foreach ($po->items as $item) {
        $this->assertEquals($item->received_status, PurchaseOrderItem::RECEIVED_STATUS);

        $locationProductQuantity = LocationProduct::where('product_id', $item->product_id)
            ->where('location_id', $location->id)
            ->sum('quantity');
        ///Assert location quantity += input quantity ( hien tai location quantity = 0)
        $this->assertEquals($locationProductQuantity, $item->quantity);

        $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
            ->where('product_id', $item->product_id)
            ->first();
        $itemProductQuantity = $arrProductQuantity[$item->product_id];
        $this->assertEquals($productQuantity->quantity, $itemProductQuantity['quantity'] + $item->quantity);
        $this->assertEquals($productQuantity->incoming_stock, $itemProductQuantity['incoming_stock'] - $item->quantity);
    }
    $this->assertEquals($po->order_status, PurchaseOrder::COMPLETED_STATUS);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
});

function buildResponseDataStruct($totalItems)
{
    $items = [];
    for ($i = 1; $i <= $totalItems; $i++) {
        array_push($items, [
            'id',
            'vendor_id',
            'po_id',
            'po_number',
            'invoice_number',
            'location_id',
            'gtin',
            'quantity',
            'product_id',
            'box_id',
            'created_at',
            'updated_at',
            'user_id',
            'warehouse_id',
            'is_deleted',
            'tracking_number',
            'employee_id'
        ]);
    }

    return $items;
}

// addition cho warehouse mexico validate country and partnumber
test('addition with warehouse mexico validate country and partnumber', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);

    unset($this->params['barcode']);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    $items = buildRandomItems($purchaseOrder, false);

    $this->params['items'] = $items;
    $arrProductQuantity = [];
    foreach ($this->params['items'] as $item) {
        $productQuantity = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
            'product_id' => $item['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
        $arrProductQuantity[$item['product_id']] = [
            'quantity' => $productQuantity->quantity,
            'incoming_stock' => $productQuantity->incoming_stock,
        ];
    }
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouse->id, 'barcode' => 'LO188']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;
    $this->params['employee_id'] = $employee->code;

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['country'][0])->toMatchArray(['The country field is required.']);

    $this->params['country'] = 'us';

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['partNumber'][0])->toMatchArray(['There is no part number available for both the country and the product']);
});

// addition cho warehouse mexico validate gtin and not map partnumber
test('addition with warehouse mexico validate gtin and not map partnumber', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);

    unset($this->params['barcode']);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    $items = buildRandomItems($purchaseOrder, false);
    $gtin = $items[0]['gtin'];
    unset($items[0]['gtin']);
    $this->params['items'] = $items;
    $arrProductQuantity = [];
    foreach ($this->params['items'] as $item) {
        $productQuantity = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
            'product_id' => $item['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
        $arrProductQuantity[$item['product_id']] = [
            'quantity' => $productQuantity->quantity,
            'incoming_stock' => $productQuantity->incoming_stock,
        ];
    }
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouse->id, 'barcode' => 'LO188']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;
    $this->params['employee_id'] = $employee->code;
    $dataPartNumber = PartNumber::factory()->create([
        'part_number' => 'datpt123',
        'product_id' => 123,
        'country' => 'vn',
    ]);
    $this->params['country'] = $dataPartNumber->country;
    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['product'][0])->toMatchArray(['The product sku ' . $items[0]['sku'] . ' does not have a gtin.']);

    $this->params['items'][0]['gtin'] = $gtin;
    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['product'][0])->toMatchArray(['The product sku ' . $items[0]['sku'] . ' is not mapped with a part number.']);
});

// addition cho warehouse mexico - success
test('addition with warehouse mexico - success', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);

    unset($this->params['barcode']);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    $items = buildRandomItems($purchaseOrder, false);
    $dataPartNumber = PartNumber::factory([
        'part_number' => 'datpt123',
        'product_id' => $items[0]['product_id'],
        'country' => 'vn',
    ])->has(PartNumberFifo::factory([
        'quantity' => 100,
        'warehouse_id' => $warehouse->id,
        'product_id' => $items[0]['product_id'],
    ]), 'partNumberFifos')
        ->create();

    $this->params['items'] = $items;
    $arrProductQuantity = [];
    foreach ($this->params['items'] as $item) {
        $productQuantity = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
            'product_id' => $item['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
        $arrProductQuantity[$item['product_id']] = [
            'quantity' => $productQuantity->quantity,
            'incoming_stock' => $productQuantity->incoming_stock,
        ];
    }
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);

    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $warehouse->id, 'barcode' => 'LO188']);
    $this->params['location_id'] = $location->id;
    $this->params['location_name'] = $location->barcode;
    $this->params['location_type'] = $location->type;
    $this->params['employee_id'] = $employee->code;
    $this->params['country'] = $dataPartNumber->country;
    $this->params['barcode'] = 'test-12345';
    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);

    $itemsInResponseData = buildResponseDataStruct(count($this->params['items']));
    $response->assertStatus(201)->assertJsonStructure($itemsInResponseData);
    foreach ($this->params['items'] as $item) {
        ///Assert them inventory_addition thanh cong
        $addition = InventoryAddition::where('product_id', $item['product_id'])->first();
        $this->assertEquals($addition->country, $this->params['country']);
        $this->assertNotEmpty($addition);
        ///Assert them box_moving thanh cong (thêm history trong box_moving)
        $boxMoving = BoxMoving::where('inventory_addition_id', $addition->id)->first();
        $this->assertNotEmpty($boxMoving);
        //Assert box
        $box = Box::where('barcode', $this->params['barcode'])->where('country', $this->params['country'])->first();
        $this->assertNotEmpty($box);

        $poItem = PurchaseOrderItem::where('po_id', $purchaseOrder->id)->where('product_id', $item['product_id'])->first();
        $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);

        $inventory = Inventory::where('object_id', $addition->id)
            ->where('direction', 0)
            ->where('type', Inventory::TYPE_INPUT)
            ->where('product_id', $item['product_id'])
            ->where('quantity', $item['quantity'])
            ->first();
        $this->assertNotEmpty($inventory);
    }
    $po = PurchaseOrder::with('items')->where('id', $purchaseOrder->id)->first();

    ///Assert status PO and PO items
    foreach ($po->items as $item) {
        $this->assertEquals($item->received_status, PurchaseOrderItem::RECEIVED_STATUS);

        $locationProductQuantity = LocationProduct::where('product_id', $item->product_id)
            ->where('location_id', $location->id)
            ->sum('quantity');
        ///Assert location quantity += input quantity ( hien tai location quantity = 0)
        $this->assertEquals($locationProductQuantity, $item->quantity);

        $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
            ->where('product_id', $item->product_id)
            ->first();
        $itemProductQuantity = $arrProductQuantity[$item->product_id];
        $this->assertEquals($productQuantity->quantity, $itemProductQuantity['quantity'] + $item->quantity);
        $this->assertEquals($productQuantity->incoming_stock, $itemProductQuantity['incoming_stock'] - $item->quantity);
    }
    $this->assertEquals($po->order_status, PurchaseOrder::COMPLETED_STATUS);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
});
