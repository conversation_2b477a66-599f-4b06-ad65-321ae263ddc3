<?php

use App\Models\Box;
use App\Models\Employee;
use App\Models\Inventory;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderBox;
use App\Models\StockTransfer;
use App\Models\StockTransferBoxLog;
use App\Models\StockTransferItem;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->destinationWarehouse = Warehouse::factory(['id' => 999])->create();

    $this->endpoint = '/api/stock-transfer/fulfill';
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->products = Product::factory()->count(4)->state(new Sequence(
        ['sku' => 'B00060734', 'gtin' => '00884074019267', 'name' => 'A', 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060735', 'gtin' => '00821780020764', 'name' => 'B', 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060736', 'gtin' => '00821780009970', 'name' => 'C', 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060740', 'gtin' => '00821780009955', 'name' => 'D', 'gtin_case' => 72, 'parent_id' => 1],
    ))->create();

    $this->boxs = Box::factory()->count(4)->state(new Sequence(
        ['warehouse_id' => $this->warehouse->id, 'barcode' => 'BARCODE1', 'product_id' => $this->products[0]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => 'BARCODE2', 'product_id' => $this->products[1]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => 'BARCODE3', 'product_id' => $this->products[2]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => 'BARCODE4', 'product_id' => $this->products[3]->id, 'quantity' => 72, 'location_id' => $this->location->id],

    ))->create();
    $this->stockTransfer = StockTransfer::factory()->create([
        'request_number' => 'SJ123',
        'status' => StockTransfer::PENDING_STATUS,
        'from_warehouse_id' => $this->warehouse->id,
        'destination_warehouse_id' => $this->destinationWarehouse->id,
    ]);
    StockTransferItem::factory()->count(2)->state(new Sequence(
        ['product_id' => $this->products[0]->id, 'request_box' => 1, 'quantity' => 72, 'stock_transfer_id' => $this->stockTransfer->id],
        ['product_id' => $this->products[1]->id, 'request_box' => 1, 'quantity' => 72, 'stock_transfer_id' => $this->stockTransfer->id],
    ))->create();

    $this->locationProducts = LocationProduct::factory()->count(2)->state(new Sequence(
        ['location_id' => $this->location->id, 'product_id' => $this->products[0]->id, 'quantity' => 72],
        ['location_id' => $this->location->id, 'product_id' => $this->products[1]->id, 'quantity' => 144],

    ))->create();

    $this->productsQuantity = ProductQuantity::factory()->count(2)->state(new Sequence(
        ['product_id' => $this->products[0]->id, 'warehouse_id' => $this->warehouse->id, 'incoming_stock' => 0, 'quantity' => 144],
        ['product_id' => $this->products[1]->id, 'warehouse_id' => $this->warehouse->id, 'incoming_stock' => 0, 'quantity' => 216],
    ))->create();

    $this->params = [
        'employee_id' => $this->employee->id,
        'stock_transfer_id' => $this->stockTransfer->id,
        'id_time_checking' => rand(1, 100),
        'box_scanned' => [
            $this->boxs[0]->barcode,
            $this->boxs[1]->barcode,
        ],
    ];
});
test('stock transfer fulfill - missing employee_id', function () {
    $this->params['employee_id'] = '';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('employee_id');
});
test('stock transfer fulfill - missing id_time_checking', function () {
    $this->params['id_time_checking'] = '';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('id_time_checking');
});
test('stock transfer fulfill - missing stock_transfer_id', function () {
    $this->params['stock_transfer_id'] = '';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('stock_transfer_id');
});
test('stock transfer fulfill - missing box_scanned', function () {
    $this->params['box_scanned'] = '';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('box_scanned');
});
test('stock transfer fulfill - Invalid Stock transfer', function () {
    $this->stockTransfer->status = StockTransfer::COMPLETED_STATUS;
    $this->stockTransfer->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('stock_transfer_id');
});
test('stock transfer fulfill - invalid box', function () {
    $this->boxs[0]->is_deleted = Box::DELETED;
    $this->boxs[0]->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('box_scanned.0');
});
test('stock transfer fulfill - box not exist', function () {
    $this->boxs[1]->delete();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('box_scanned.1');
});

test('stock transfer fulfill - product in the box does not match the requested product', function () {
    $this->params['box_scanned'][0] = $this->boxs[2]->barcode;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});
test('stock transfer fulfill - successfully', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $purchaseOrder = PurchaseOrder::where('warehouse_id', $this->destinationWarehouse->id)->first();
    expect($purchaseOrder)->toBeObject();
    $purchaseOrderBox = PurchaseOrderBox::whereIn('tracking_number', [$this->boxs[0]->barcode, $this->boxs[1]->barcode])->get();
    expect($purchaseOrderBox)->toHaveCount(2);
    expect(LocationProduct::where('id', $this->locationProducts[0]->id)->first()->quantity)->toEqual(0);
    expect(LocationProduct::where('id', $this->locationProducts[1]->id)->first()->quantity)->toEqual(72);
    $totalFulfillLog = StockTransferBoxLog::count();
    expect($totalFulfillLog)->toEqual(2);
    $inventoryCount = Inventory::where('object_name', Inventory::OBJECT_STOCK_TRANSFER)->count();
    expect($inventoryCount)->toEqual(2);
});
