<?php

use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\PartNumberHistory;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $this->access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);

    $this->product = Product::factory()->count(2)->sequence(
        ['style' => 'UNPT'],
        ['style' => '5000'],
    )
        ->create();

    $this->partNumber = PartNumber::factory()->count(2)->sequence(
        ['part_number' => 'T5011UNGT1B00M', 'country' => 'AF', 'product_id' => $this->product->first()->id],
        ['part_number' => 'T5121UNGS1B00L', 'country' => 'US', 'product_id' => $this->product->last()->id],
    )
        ->has(Country::factory(), 'partNumberCountry')
        ->has(PartNumberFifo::factory()->count(2)->sequence(fn (Sequence $sequence) => ['quantity' => $sequence->index + 1, 'warehouse_id' => $this->warehouse])
            ->state(function (array $attribute, PartNumber $partNumber) {
                return ['product_id' => $partNumber->product_id];
            }, 'partNumberFifos'))
        ->has(PartNumberHistory::factory()->count(2)->sequence(
            ['quantity' => 1, 'type' => PartNumberHistory::TYPE_EXPORT],
            ['quantity' => 2, 'type' => PartNumberHistory::TYPE_IMPORT],
        )
            ->state(['warehouse_id' => $this->warehouse]), 'partNumberHistory')
        ->create();

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->endpoint = 'api/part-number-overview';

    $this->result = [
        'id',
        'part_number',
        'product_id',
        'country',
        'created_at',
        'total_balance',
        'total_import',
        'total_export',
        'product',
        'part_number_country',
    ];
});

//fetch part number overview success - key_search param
test('fetch part number overview success - key_search param', function () {
    $asserts['valid'] = ['key_search' => "T5011UNGT1B00M"];
    $asserts['in_valid'] = ['key_search' => 123123];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['product'])->toHaveKeys(['id', 'style']);
                expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
            }
        }
    }
});

//fetch part number overview success - style param
test('fetch part number overview success - style param', function () {
    $asserts['valid'] = ['style' => "5000"];
    $asserts['in_valid'] = ['style' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['product'])->toHaveKeys(['id', 'style']);
                expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
            }
        }
    }
});

//fetch part number overview success - country param
test('fetch part number overview success - country param', function () {
    $asserts['valid'] = ['country' => "AF"];
    $asserts['in_valid'] = ['country' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['product'])->toHaveKeys(['id', 'style']);
                expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
            }
        }
    }
});

//fetch part number overview success - part number fifo in another warehouse
test('fetch part number overview success - part number fifo in another warehouse', function () {
    PartNumberFifo::query()->update(['warehouse_id' => 1]);
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(2, $response['data']);
    foreach ($response['data'] as $key => $value) {
        $this->assertNull($value['total_balance']);
        expect($value)->toHaveKeys($this->result);
        expect($value['product'])->toHaveKeys(['id', 'style']);
        expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
    }
});

//fetch part number overview success - part number hisroty not found
test('fetch part number overview success - part number history not found', function () {
    PartNumberHistory::query()->update(['warehouse_id' => 1]);
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(2, $response['data']);
    foreach ($response['data'] as $key => $value) {
        $this->assertNull($value['total_import']);
        $this->assertNull($value['total_export']);
        expect($value)->toHaveKeys($this->result);
        expect($value['product'])->toHaveKeys(['id', 'style']);
        expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
    }
});

//fetch part number overview success
test('fetch part number overview success', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(2, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        expect($value['product'])->toHaveKeys(['id', 'style']);
        expect($value['part_number_country'])->toHaveKeys(['name', 'iso2']);
    }
});
