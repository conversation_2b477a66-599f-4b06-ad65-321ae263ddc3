<?php

use App\Models\EmbroideryTask;
use App\Models\EmbroideryUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->team = EmbroideryUser::factory()->create(['name' => 'team 1']);
    $this->userEditor = EmbroideryUser::factory()->create(['username' => 'editor1', 'role' => 'editor', 'team_id' => 1]);
    $this->userAdmin = EmbroideryUser::factory()->create(['username' => 'admin1', 'role' => 'admin', 'team_id' => 1]);
    $this->userLeader = EmbroideryUser::factory()->create(['username' => 'leader1', 'role' => 'leader', 'team_id' => 1]);

    $this->Tasks = EmbroideryTask::factory()->count(5)->create();
    $this->endpoint = '/api/embroidery/login';
    $this->endpointApiGetListTask = '/api/embroidery/tool/list/task';
    $this->endpointApiGetTask = '/api/embroidery/tool/get/task';
    $this->endpointApiAssignTask = '/api/embroidery/tool/assign/task';
    $this->endpointApiRejectTask = '/api/embroidery/tool/reject/task';
    $this->endpointApiApproveTask = '/api/embroidery/tool/approve/task';
    $this->endpointApiUploadTask = '/api/embroidery/tool/upload/task';
    $this->endpointApiCreateTeam = '/api/embroidery/create/team';
    $this->endpointApiUpdateTeam = '/api/embroidery/update/team';
    $this->endpointApiDeleteTeam = '/api/embroidery/delete/team';
    $this->endpointApiWarningTask = '/api/embroidery/tool/warning/task';
    $this->endpointApiContinueTask = '/api/embroidery/tool/comment/task';
});

test('login success', function () {
    $this->user = EmbroideryUser::factory()->create(['username' => 'test']);
    $response = $this->post($this->endpoint, ['username' => 'test', 'password' => '123456']);
    $response->assertStatus(200)->assertJsonStructure([
        'access_token',
        'token_type',
        'user'
    ]);
});
test('fetch list task success', function () {
    EmbroideryTask::factory()->count(5)->create();
    $response = $this->post($this->endpoint, ['username' => 'editor1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->get('/api/embroidery/tool/list/task');
    $response->assertStatus(200);
});

test('editor get task success', function () {
    EmbroideryTask::factory()->count(2)->create();
    $response = $this->post('/api/embroidery/login', ['username' => 'editor1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiGetTask);
    $response->assertStatus(422);
});

test('get task failed - editor have pending task', function () {
    $response = $this->post('/api/embroidery/login', ['username' => 'editor1', 'password' => '123456']);
    $token = $response['access_token'];
    EmbroideryTask::find($this->Tasks[0]->id)->update([
        'embroidery_user_id' => $this->userEditor->id,
        'embroidery_team_id' => 1,
        'status' => 'in_progress'
    ]);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiGetTask);
    $response->assertStatus(422, 'Please finish uploading your current task before taking on a new one.' . $response->status());
});

test('get task failed - no new task', function () {
    $response = $this->post('/api/embroidery/login', ['username' => 'editor1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiGetTask);
    $response->assertStatus(422, 'There are currently no new tasks that can be assigned to you.' . $response->status());
});

test('get task failed - admin/leader task', function () {
    $response = $this->post('/api/embroidery/login', ['username' => 'admin1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiGetTask);
    $response->assertStatus(401, 'Unauthorized.' . $response->status());

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiGetTask);
    $response->assertStatus(401, 'Unauthorized.' . $response->status());
});

test('assign to worker success - admin/leader assign task', function () {
    EmbroideryTask::factory()->count(5)->create();

    $response = $this->post('/api/embroidery/login', ['username' => 'admin1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiAssignTask, [
        'embroidery_user_id' => $this->userEditor->id,
        'task_id' => $this->Tasks[4]->id,
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiAssignTask, [
        'embroidery_user_id' => $this->userEditor->id,
        'task_id' => $this->Tasks[3]->id,
    ]);
    $response->assertStatus(200, 'Assignee updated successfully.' . $response->status());
});

test('assign to worker failed - leader team 2 try to assign task from team 1', function () {
    $userLeader = EmbroideryUser::factory()->create(['username' => 'leader2', 'role' => 'leader', 'team_id' => 2]);
    $userEditor2 = EmbroideryUser::factory()->create(['username' => 'editor2', 'role' => 'editor', 'team_id' => 2]);
    $currentTasks = EmbroideryTask::factory()->count(5)->create([
        'embroidery_user_id' => $this->userEditor->id,
        'embroidery_team_id' => 1,
        'status' => 'in_progress'
    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'leader2', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiAssignTask, [
        'embroidery_user_id' => $userEditor2->id,
        'task_id' => $currentTasks[4]->id,
    ]);
    $response->assertStatus(401, 'Unauthorized.' . $response->status());
});

test('assign to worker failed - worker try to assign people', function () {
    $response = $this->post('/api/embroidery/login', ['username' => 'editor1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiAssignTask, [
        'embroidery_user_id' => 445,
        'task_id' => $this->Tasks[4]->id,
    ]);
    $response->assertStatus(401, 'Unauthorized.' . $response->status());
});

test('assign to worker failed - try to assign when task is completed', function () {
    $task = EmbroideryTask::factory()->count(1)->create([
        'embroidery_user_id' => $this->userEditor->id,
        'embroidery_team_id' => 1,
        'status' => 'completed'
    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiAssignTask, [
        'embroidery_user_id' => 2333334,
        'task_id' => $task[0]->id,
    ]);
    $response->assertStatus(401, 'Unauthorized.' . $response->status());
});

test('reject task success', function () {
    $tasks = EmbroideryTask::factory()->count(2)->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'in_review'
    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiRejectTask . '/' . $tasks[0]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(200);
    $this->assertDatabaseHas('embroidery_task', [
        'id' => $tasks[0]->id,
        'status' => 'rejected',
        'reject_reason' => 'sai'
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiRejectTask . '/' . $tasks[1]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(200);
    $this->assertDatabaseHas('embroidery_task', [
        'id' => $tasks[1]->id,
        'status' => 'rejected',
        'reject_reason' => 'sai'
    ]);
});

test('reject task failed - leader other team try to reject', function () {
    $tasks = EmbroideryTask::factory()->count(1)->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 2,
        'status' => 'in_review'
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiRejectTask . '/' . $tasks[0]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(401);
});

test('reject task failed - try to reject if not status review', function () {
    $tasks = EmbroideryTask::factory()->count(1)->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'completed'
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiRejectTask . '/' . $tasks[0]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(422);
});

test('approve task failed - status not in_review', function () {
    $tasks = EmbroideryTask::factory()->count(1)->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'reject'
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiApproveTask . '/' . $tasks[0]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(422);
});

test('approve task failed - leader not same team', function () {
    $tasks = EmbroideryTask::factory()->count(1)->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 2,
        'status' => 'in_review'
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'leader1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->put($this->endpointApiApproveTask . '/' . $tasks[0]->id, [
        'reject_reason' => 'sai'
    ]);
    $response->assertStatus(401);
});

test('update success - warning image unable to be digitized', function () {
    EmbroideryUser::factory()->create(['username' => 'admin', 'role' => 'admin', 'team_id' => 1]);
    $task = EmbroideryTask::factory()->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'in_progress',
    ]);

    $response = $this->post('/api/embroidery/login', ['username' => 'admin', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiWarningTask . '/' . $task->id, [
    ]);
    $response->assertStatus(200);
});

test('update continue failed - status not in review', function () {
    EmbroideryUser::factory()->create(['username' => 'admin', 'role' => 'admin', 'team_id' => 1]);
    $task = EmbroideryTask::factory()->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'pending',
    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'admin', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiContinueTask, [
        'id' => $task->id,
        'value' => '134'
    ]);
    $response->assertStatus(422);
});

test('update continue failed - not mark digitizied', function () {
    EmbroideryUser::factory()->create(['username' => 'admin', 'role' => 'admin', 'team_id' => 1]);
    $task = EmbroideryTask::factory()->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'in_review',
    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'admin', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiContinueTask, [
        'id' => $task->id,
    ]);
    $response->assertStatus(422);
});

test('update continue failed - missing note', function () {
    EmbroideryUser::factory()->create(['username' => 'admin', 'role' => 'admin', 'team_id' => 1]);
    $task = EmbroideryTask::factory()->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'in_review',

    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'admin', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiContinueTask, [
        'id' => $task->id,
    ]);
    $response->assertStatus(422);
});

test('update continue success', function () {
    $task = EmbroideryTask::factory()->create([
        'embroidery_user_id' => 99,
        'embroidery_team_id' => 1,
        'status' => 'in_review',
        'is_not_digitized' => true

    ]);
    $response = $this->post('/api/embroidery/login', ['username' => 'admin1', 'password' => '123456']);
    $token = $response['access_token'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $token",
    ])->post($this->endpointApiContinueTask, [
        'id' => $task->id,
        'value' => '1231',
    ]);
    $response->assertStatus(200);
});
