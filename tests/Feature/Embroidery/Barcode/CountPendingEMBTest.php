<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\ProductStyle;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\BarcodePrintedTime;
use App\Models\SaleOrderAccount;
use App\Models\Setting;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\BarcodeEMBRepository;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->setting = Setting::factory([
        'label' => Setting::TEST_WIP_SKU,
        'name' => Setting::TEST_WIP_SKU,
        'value' => 'UNGT,UNNT'
    ])->create();
    $this->warehouse = Warehouse::factory()->create();

    $this->productStyle = ProductStyle::factory([
        'print_method' => ProductStyle::METHOD_EMB,
        'name' => '3001',
        'sku' => 'UNPT',
        'type' => 'Fleece',
    ])
        ->has(Product::factory([
            'parent_id' => 111,
            'sku' => 'UNPT9C00L'
        ]), 'product')
        ->create();

    $this->saleOrderAccount = SaleOrderAccount::factory([
        'id' => 2000,
        'name' => 'shipstation',
    ])->create();
    $this->store = Store::factory([
        'account_id' => $this->saleOrderAccount->id,
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'id' => 2000000,
        'order_status' => SaleOrder::NEW_ORDER,
        'account_id' => $this->saleOrderAccount->id,
        'store_id' => $this->store->id,
        'warehouse_id' => $this->warehouse->id,
        'is_fba_order' => SaleOrder::ACTIVE,
        'is_manual' => SaleOrder::IS_NOT_MANUAL,
        'is_xqc' => SaleOrder::INACTIVE,
        'is_eps' => SaleOrder::INACTIVE,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'ink_color_status' => SaleOrderItem::ACTIVE,
        'product_style_sku' => $this->productStyle->sku,
        'product_sku' => $this->productStyle->product->first()->sku,
        'sku' => '63QCYN',
    ])->has(SaleOrderItemBarcode::factory([
        'id' => *********,
        'sku' => '63QCYN',
        'warehouse_id' => $this->warehouse->id,
        'barcode_printed_id' => 0,
        'label_id' => 'xxxxx',
        'print_method' => ProductStyle::METHOD_EMB,
        'employee_reroute_id' => null,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->barcodePrintedTime = BarcodePrintedTime::factory([
        'warehouse_id' => $this->warehouse->id,
        'is_fba' => true,
    ])->create();

    $this->barcodeRepo = new BarcodeEMBRepository();
    $this->params = [
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'account_id' => $this->saleOrderAccount->id,
    ];

});

//account error
test('get count pending WIP success - account error', function() {
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//store error
test('get count pending WIP success - store error', function() {
    $this->params['store_id'] = 2000;
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//warehouse error
test('get count pending WIP success - warehouse error', function() {
    $this->params['warehouse_id'] = 2000;
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//label_root_id != null
test('get count pending WIP success - label_root_id != null', function() {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['label_root_id' => 20]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//reprint_status != 0
test('get count pending WIP success - reprint_status != 0', function() {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['reprint_status' => SaleOrderItemBarcode::REPRINTED]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//employee_reroute_id != Null
test('get count pending WIP success - employee_reroute_id != null', function() {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['employee_reroute_id' => 20]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//print_method != DTG
test('get count pending WIP success - print_method != DTG', function() {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['print_method' => SaleOrderItemBarcode::METHOD_DTF]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//barcode_printed_id != 0
test('get count pending WIP success - barcode_printed_id != 0', function() {
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['barcode_printed_id' => 10]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//ink_color_status not active
test('get count pending WIP success - ink_color_status not active', function() {
    SaleOrderItem::query()->where('sku', '63QCYN')->update(['ink_color_status' => SaleOrder::INACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//order eps
test('get count pending WIP success - order eps', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_eps' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//order xqc
test('get count pending WIP success - order xqc', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_xqc' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//order xqc and eps
test('get count pending WIP success - order xqc and eps', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update([
        'is_xqc' => SaleOrder::ACTIVE,
        'is_eps' => SaleOrder::ACTIVE,
        'is_fba_order' => SaleOrder::INACTIVE
    ]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//order manual
test('get count pending WIP success - order manual', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::ACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});

//order not fba
test('get count pending WIP success - order not fba', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);

    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 1);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

//order inactive status
test('get count pending WIP success - order inactive status', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_ON_HOLD]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

test('get count pending WIP success - order insert and not fba', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_fba_order' => SaleOrder::INACTIVE]);
    ProductStyle::where('id', $this->productStyle->id)->update(['type' => ProductStyle::TYPE_INSERT]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

test('get count pending WIP success - order insert, reprint, manual process, reroute, fba, xqc.', function() {
    SaleOrder::query()->where('id', $this->saleOrder->id)->update(['is_manual' => SaleOrder::IS_MANUAL, 'is_xqc' => 1]);
    SaleOrderItemBarcode::query()->where('label_id', 'xxxxx')->update(['reprint_status' => SaleOrderItemBarcode::REPRINTED, 'employee_reroute_id' => 20]);
    ProductStyle::where('id', $this->productStyle->id)->update(['type' => ProductStyle::TYPE_INSERT]);
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 0);
});

// default
test('get count pending WIP success', function() {
    $this->assertEquals(($this->barcodeRepo->countPendingStyle($this->params))->count(), 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleXQC($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingStyleEps($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingWarehouse($this->params))->total, 1);
    $this->assertEquals(($this->barcodeRepo->countPendingReprint($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingManualProcess($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingReroute($this->params))->total, 0);
    $this->assertEquals(($this->barcodeRepo->countPendingFba($this->params))->total, 1);
});


