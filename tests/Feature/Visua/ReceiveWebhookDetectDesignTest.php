<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\Store;
use App\Models\VisuaDetectImage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->store = Store::factory()->createMany([
        [
            'name' => 'test 1'
        ],
        [
            'name' => 'test 2'
        ]
    ]);
    $this->saleOrder = SaleOrder::factory([
        'id' => 20000,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store[0]->id
    ])
        ->has(SaleOrderItemImage::factory()->count(1)->sequence([
            'image_url' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/Nintendo.svg/800px-Nintendo.svg.png',
        ]), 'imageItems')
        ->create();
    $this->input = [
        'dataRootName' => 'data',
        'responseData' => [
            'sessionId' => '8poA78L',
            'status' => 200,
            'processTime' => [
                'time' => 6.05,
                'unit' => 'seconds'
            ],
            'detections' => [
                [
                    'area' => 1466.6,
                    'validationFlags' => [1],
                    'size' => 'tiny',
                    'areaPercentage' => 0.0031,
                    'coordinates' => [484.6, 747.45, 484.65, 680.4, 506.71, 681.87, 506.19, 749.21],
                    'type' => 'logo',
                    'name' => 'Pepsi',
                    'iconUrl' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/Nintendo.svg/800px-Nintendo.svg.png',
                    'meta' => ['brand_name' => 'pepsicola'],
                    'id' => 164,
                    'confidence' => 1,
                    'confidenceALE' => 1
                ]
            ]
        ],
        'method' => 'POST',
        'requestURI' => '/detect',
        'processTime' => [
            'unit' => 'milliseconds',
            'time' => 9386
        ]
    ];
    $this->setting = Setting::factory()->create([
        'label' => 'store_on_hold_setting_test',
        'name' => 'store_on_hold_setting_test',
        'value' => $this->store[0]->id,
    ]);
});

test('success store setting on hold', function () {
    SaleOrderOnHold::create([
        'order_id' => $this->saleOrder->id,
        'store_on_hold' => 1
    ]);
    $this->saleOrder->order_status = 'on_hold';
    $detectDesign = VisuaDetectImage::factory()->create([
        'visua_session_id' => '8poA78L',
        'image_id' => $this->saleOrder->imageItems[0]->id,
        'order_id' => $this->saleOrder->id,
    ]);
    $job = new \App\Jobs\DetectDesignWebhookJob($this->input);
    $job->handle();
    $this->assertDatabaseHas('visua_detect_image', [
        'visua_session_id' => '8poA78L',
        'image_id' => $this->saleOrder->imageItems[0]->id,
        'order_id' => $this->saleOrder->id,
        'is_received_response' => 1,
        'is_ip_violation' => 1,
    ]);
    $this->assertDatabaseHas('visua_detect_image_item', [
        'visua_detect_image_id' => $detectDesign->id,
    ]);
    $this->assertDatabaseHas('sale_order', [
        'id' => $detectDesign->order_id,
        'order_status' => SaleOrder::ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $this->saleOrder->id,
        'store_on_hold' => 1,
        'visua_on_hold' => 1,
    ]);
})->skip();

test('success store do not setting on hold', function () {
    $detectDesign = VisuaDetectImage::factory()->create([
        'visua_session_id' => '8poA78L',
        'image_id' => $this->saleOrder->imageItems[0]->id,
        'order_id' => $this->saleOrder->id,
    ]);
    $job = new \App\Jobs\DetectDesignWebhookJob($this->input);
    $job->handle();
    $this->assertDatabaseHas('visua_detect_image', [
        'visua_session_id' => '8poA78L',
        'image_id' => $this->saleOrder->imageItems[0]->id,
        'order_id' => $this->saleOrder->id,
        'is_received_response' => 1,
        'is_ip_violation' => 1,
    ]);
    $this->assertDatabaseHas('visua_detect_image_item', [
        'visua_detect_image_id' => $detectDesign->id,
    ]);
    $this->assertDatabaseHas('sale_order', [
        'id' => $detectDesign->order_id,
        'order_status' => SaleOrder::ON_HOLD
    ]);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $detectDesign->order_id,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE
    ]);
})->skip();
