<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\Store;
use App\Models\VisuaDetectImage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/webhook/detect';
    $this->store = Store::factory()->createMany([
        [
            'name' => 'test 1'
        ],
        [
            'name' => 'test 2'
        ]
    ]);
    $this->setting = Setting::factory()->create([
        'label' => 'store_on_hold_setting_test',
        'name' => 'store_on_hold_setting_test',
        'value' => $this->store[0]->id,
    ]);
    $this->saleOrder = SaleOrder::factory([
        'id' => 20000,
        'order_status' => SaleOrder::NEW_ORDER,
        'store_id' => $this->store[0]->id
    ])
        ->has(SaleOrderItemImage::factory()->count(1)->sequence([
            'image_url' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/Nintendo.svg/800px-Nintendo.svg.png',
        ]), 'imageItems')
        ->create();
    VisuaDetectImage::factory()->create([
        'order_id' => $this->saleOrder->id,
        'image_id' => $this->saleOrder->imageItems[0]->id,
        'visua_session_id' => '2156mLE1',
    ]);
});

test('Store khong on hold, Co vi pham ip violation', function () {
    $params = [
        'requestType' => 'detectionResponse',
        'requestHash' => '2156mLE1',
        'responseData' => [
            'sessionId' => '2156mLE1',
            'status' => 200,
            'modelVersion' => 1712017356,
            'mediaInfo' => [
                'width' => 750,
                'height' => 250
            ],
            'mediaUrl' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/thumb/750/2024-04-02/S796412UNGT1W3XL-1.png',
            'processTime' => [
                'time' => 2.07,
                'unit' => 'seconds'
            ],
            'detections' => [
                [
                    'visualClassId' => 3311,
                    'id' => 1757,
                    'type' => 'logo',
                    'name' => 'Nintendo',
                    'iconUrl' => 'https://s3-eu-west-1.amazonaws.com/s3.logograb.com/logograb/cache-dir-public/3b4fa4369b7ba1c868a45fa5f669816a2771a09e.png',
                    'size' => 'large',
                    'area' => 106965.62,
                    'areaPercentage' => 0.5705,
                    'validationFlags' => [0.99],
                    'confidence' => 1,
                    'confidenceALE' => 1,
                    'coordinates' => [54.73, 47.39, 698.69, 40.11, 698.87, 206.72, 50.05, 211.79]
                ]
            ]
        ]
    ];

    $job = new \App\Jobs\DetectDesignWebhookJob($params);
    $job->handle();
    $detectDesign = VisuaDetectImage::where('visua_session_id', $params['responseData']['sessionId'])->first();
    $this->assertNotNull($detectDesign);
    expect($detectDesign->is_received_response)->toEqual(1);
    expect($detectDesign->is_ip_violation)->toEqual(1);
    $this->assertDatabaseHas('visua_detect_image_item', [
        'visua_detect_image_id' => $detectDesign->id,
    ]);
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::ON_HOLD,
    ]);
    $saleOrderOnHold = SaleOrderOnHold::where('order_id', $this->saleOrder->id)->get();
    expect(count($saleOrderOnHold))->toEqual(1);
    expect($saleOrderOnHold[0]->visua_on_hold)->toEqual(1);

    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "new_order" to "on hold" due to IP Violation by Gemini',
    ]);
    $saleOrder = SaleOrder::find($this->saleOrder->id);
})->skip('Store on hold, khong co vi pham ip violation');

test('Store khong on hold, khong co vi pham ip violation', function () {
//    SaleOrder::find($this->saleOrder)->update(['order_status' => 1]);
    $params = [
        'requestType' => 'detectionResponse',
        'requestHash' => '2156mLE1',
        'responseData' => [
            'sessionId' => '2156mLE1',
            'status' => 200,
            'modelVersion' => 1712017356,
            'mediaInfo' => [
                'width' => 750,
                'height' => 250
            ],
            'mediaUrl' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/thumb/750/2024-04-02/S796412UNGT1W3XL-1.png',
            'processTime' => [
                'time' => 2.07,
                'unit' => 'seconds'
            ],
            'detections' => [
            ]
        ]
    ];

    $job = new \App\Jobs\DetectDesignWebhookJob($params);
    $job->handle();
    $detectDesign = VisuaDetectImage::where('visua_session_id', $params['responseData']['sessionId'])->first();
    expect($detectDesign->is_received_response)->toEqual(1);
    expect($detectDesign->is_ip_violation)->toEqual(0);
    $this->assertDatabaseMissing('visua_detect_image_item', ['visua_detect_image_id' => $detectDesign->id]);
    $this->assertDatabaseMissing('sale_order_on_hold', ['order_id' => $this->saleOrder->id]);
})->skip('Store on hold, khong co vi pham ip violation');

test('Store co on hold, Co vi pham ip violation', function () {
    SaleOrderOnHold::create([
        'order_id' => $this->saleOrder->id,
        'store_on_hold' => 1
    ]);
    SaleOrder::find($this->saleOrder->id)->update(['order_status' => SaleOrder::ON_HOLD]);
    $params = [
        'requestType' => 'detectionResponse',
        'requestHash' => '2156mLE1',
        'responseData' => [
            'sessionId' => '2156mLE1',
            'status' => 200,
            'modelVersion' => 1712017356,
            'mediaInfo' => [
                'width' => 750,
                'height' => 250
            ],
            'mediaUrl' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/thumb/750/2024-04-02/S796412UNGT1W3XL-1.png',
            'processTime' => [
                'time' => 2.07,
                'unit' => 'seconds'
            ],
            'detections' => [
                [
                    'visualClassId' => 3311,
                    'id' => 1757,
                    'type' => 'logo',
                    'name' => 'Nintendo',
                    'iconUrl' => 'https://s3-eu-west-1.amazonaws.com/s3.logograb.com/logograb/cache-dir-public/3b4fa4369b7ba1c868a45fa5f669816a2771a09e.png',
                    'size' => 'large',
                    'area' => 106965.62,
                    'areaPercentage' => 0.5705,
                    'validationFlags' => [0.99],
                    'confidence' => 1,
                    'confidenceALE' => 1,
                    'coordinates' => [54.73, 47.39, 698.69, 40.11, 698.87, 206.72, 50.05, 211.79]
                ]
            ]
        ]
    ];

    $job = new \App\Jobs\DetectDesignWebhookJob($params);
    $job->handle();
    $detectDesign = VisuaDetectImage::where('visua_session_id', $params['responseData']['sessionId'])->first();
    $this->assertNotNull($detectDesign);
    expect($detectDesign->is_received_response)->toEqual(1);
    expect($detectDesign->is_ip_violation)->toEqual(1);
    $this->assertDatabaseHas('visua_detect_image_item', [
        'visua_detect_image_id' => $detectDesign->id,
    ]);
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'order_status' => SaleOrder::ON_HOLD,
    ]);
    $saleOrderOnHold = SaleOrderOnHold::where('order_id', $this->saleOrder->id)->get();
    expect(count($saleOrderOnHold))->toEqual(1);
    expect($saleOrderOnHold[0]->visua_on_hold)->toEqual(1);
    expect($saleOrderOnHold[0]->store_on_hold)->toEqual(1);

    $this->assertDatabaseMissing('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
        'message' => 'Order status changed from "new_order" to "on hold" due to IP Violation by Gemini',
    ]);
    $saleOrder = SaleOrder::find($this->saleOrder->id);
})->skip('Store on hold, khong co vi pham ip violation');

test('Store on hold, khong co vi pham ip violation', function () {
    SaleOrderOnHold::create([
        'order_id' => $this->saleOrder->id,
        'store_on_hold' => 1
    ]);
    SaleOrder::find($this->saleOrder->id)->update(['order_status' => SaleOrder::ON_HOLD]);
    $params = [
        'requestType' => 'detectionResponse',
        'requestHash' => '2156mLE1',
        'responseData' => [
            'sessionId' => '2156mLE1',
            'status' => 200,
            'modelVersion' => 1712017356,
            'mediaInfo' => [
                'width' => 750,
                'height' => 250
            ],
            'mediaUrl' => 'https://swiftpoddev.s3.us-west-1.amazonaws.com/thumb/750/2024-04-02/S796412UNGT1W3XL-1.png',
            'processTime' => [
                'time' => 2.07,
                'unit' => 'seconds'
            ],
            'detections' => [
            ]
        ]
    ];

    $job = new \App\Jobs\DetectDesignWebhookJob($params);
    $job->handle();
    $detectDesign = VisuaDetectImage::where('visua_session_id', $params['responseData']['sessionId'])->first();
    expect($detectDesign->is_received_response)->toEqual(1);
    expect($detectDesign->is_ip_violation)->toEqual(0);
    $this->assertDatabaseMissing('visua_detect_image_item', ['visua_detect_image_id' => $detectDesign->id]);
    $this->assertDatabasehas('sale_order_on_hold', [
        'order_id' => $this->saleOrder->id,
        'visua_on_hold' => 0,
        'store_on_hold' => 1,
    ]);
})->skip('Store on hold, khong co vi pham ip violation');
