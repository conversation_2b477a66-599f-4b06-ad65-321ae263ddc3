 <?php

// use App\Jobs\DetectDesignWebhookJob;
// use App\Models\ImageHash;
// use App\Models\SaleOrder;
// use App\Models\SaleOrderHistory;
// use App\Models\SaleOrderItemImage;
// use App\Models\SaleOrderOnHold;
// use App\Models\Tag;
// use App\Models\VisuaDetectImage;
// use App\Models\VisuaDetectImageItem;
// use App\Repositories\SaleOrderRepository;
// use Illuminate\Foundation\Testing\RefreshDatabase;

// uses(RefreshDatabase::class);

// afterEach(function() {
//     Mockery::close();
// });

// beforeEach(function () {
//     // Tạo dữ liệu cơ bản cho các bài kiểm thử
//     $imageHash = ImageHash::factory()->create([
//         'is_ip_violation' => false,
//         'user_check_id' => null
//     ]);

//     $this->imageUrl = 'https://d1ud88wu9m1k4s.cloudfront.net/design/2023/01/27/A27037_ySmJDNGPWKLc4pL4K8cV5FK5X_1674815464134.png';
//     $saleOrderItemImage = SaleOrderItemImage::factory()
//         ->for($imageHash, 'imageHash')
//         ->create([
//             'order_id' => 1,
//             'image_url' => $this->imageUrl,
//         ]);

//     $this->visuaSessionId = 'test-session-id-' . rand(1000, 9999);
//     $this->visuaDetectImage = VisuaDetectImage::factory()
//         ->for($saleOrderItemImage, 'image')
//         ->create([
//             'order_id' => $saleOrderItemImage->order_id,
//             'image_id' => $saleOrderItemImage->id,
//             'visua_session_id' => $this->visuaSessionId,
//             'is_received_response' => 0,
//             'is_ip_violation' => 0,
//             'json' => null
//         ]);

//     $this->visuaDetectImageId = $this->visuaDetectImage->id;

//     // Dữ liệu đầu vào cho job
//     $this->webhookData = [
//         'responseData' => [
//             'sessionId' => $this->visuaSessionId,
//             'status' => 200,
//             'detections' => [
//                 [
//                     'id' => 'detection-1',
//                     'name' => 'Nike',
//                     'iconUrl' => 'https://example.com/nike.png',
//                     'type' => 'brand',
//                     'confidence' => 0.95
//                 ],
//                 [
//                     'id' => 'detection-2',
//                     'name' => 'Adidas',
//                     'iconUrl' => 'https://example.com/adidas.png',
//                     'type' => 'brand',
//                     'confidence' => 0.85
//                 ]
//             ]
//         ]
//     ];

//     $this->job = new DetectDesignWebhookJob($this->webhookData);

//     // Mock jobEcho function
//     $this->mockFunction('jobEcho', function($message) {
//         // Không làm gì, chỉ để tránh lỗi khi gọi hàm jobEcho
//         return true;
//     });
// });

// test('detect design webhook job - session id not found', function () {
//     // Tạo dữ liệu đầu vào không có session id
//     $webhookData = [
//         'responseData' => [
//             'status' => 200,
//             'detections' => []
//         ]
//     ];

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra không có thay đổi trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(0);
//     expect($visuaDetectImage->json)->toBeNull();
// });

// test('detect design webhook job - visua detect image not found', function () {
//     // Tạo dữ liệu đầu vào với session id không tồn tại
//     $webhookData = [
//         'responseData' => [
//             'sessionId' => 'non-existent-session-id',
//             'status' => 200,
//             'detections' => []
//         ]
//     ];

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra không có thay đổi trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(0);
//     expect($visuaDetectImage->json)->toBeNull();
// });

// test('detect design webhook job - status not ok', function () {
//     // Tạo dữ liệu đầu vào với status không phải 200
//     $webhookData = [
//         'responseData' => [
//             'sessionId' => $this->visuaSessionId,
//             'status' => 500,
//             'detections' => []
//         ]
//     ];

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra cập nhật trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(1);
//     expect($visuaDetectImage->json)->not->toBeNull();
//     expect($visuaDetectImage->is_ip_violation)->toBe(0);
// });

// test('detect design webhook job - ip violation detected', function () {
//     // Mock SaleOrderRepository
//     $orderServiceMock = Mockery::mock(SaleOrderRepository::class);
//     $orderServiceMock->shouldReceive('holdOrderByVisua')->once()->andReturn(true);
//     app()->instance(SaleOrderRepository::class, $orderServiceMock);

//     // Thực thi job
//     $this->job->handle();

//     // Kiểm tra cập nhật trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(1);
//     expect($visuaDetectImage->json)->not->toBeNull();
//     expect($visuaDetectImage->is_ip_violation)->toBe(1);

//     // Kiểm tra VisuaDetectImageItem được tạo
//     $visuaDetectImageItems = VisuaDetectImageItem::where('visua_detect_image_id', $this->visuaDetectImageId)->get();
//     expect($visuaDetectImageItems)->toHaveCount(2);
//     expect($visuaDetectImageItems[0]->name)->toBe('Nike');
//     expect($visuaDetectImageItems[1]->name)->toBe('Adidas');
// });

// test('detect design webhook job - no ip violation', function () {
//     // Tạo dữ liệu đầu vào với confidence thấp (không vi phạm)
//     $webhookData = [
//         'responseData' => [
//             'sessionId' => $this->visuaSessionId,
//             'status' => 200,
//             'detections' => [
//                 [
//                     'id' => 'detection-1',
//                     'name' => 'Nike',
//                     'iconUrl' => 'https://example.com/nike.png',
//                     'type' => 'brand',
//                     'confidence' => 0.5 // Dưới ngưỡng VisuaDetectImage::CONFIDENCE_ON_HOLD
//                 ]
//             ]
//         ]
//     ];

//     // Tạo order on hold
//     SaleOrderOnHold::create([
//         'order_id' => $this->visuaDetectImage->order_id,
//         'visua_on_hold' => true,
//         'store_on_hold' => true,
//         'manual_on_hold' => false
//     ]);

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra cập nhật trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(1);
//     expect($visuaDetectImage->json)->not->toBeNull();
//     expect($visuaDetectImage->is_ip_violation)->toBe(0);

//     // Kiểm tra SaleOrderOnHold được cập nhật
//     $orderOnHold = SaleOrderOnHold::where('order_id', $this->visuaDetectImage->order_id)->first();
//     expect($orderOnHold->visua_on_hold)->toBe(false);
//     expect($orderOnHold->store_on_hold)->toBe(true);
// });

// test('detect design webhook job - order status changed from on hold to new order', function () {
//     // Tạo dữ liệu đầu vào với confidence thấp (không vi phạm)
//     $webhookData = [
//         'responseData' => [
//             'sessionId' => $this->visuaSessionId,
//             'status' => 200,
//             'detections' => [
//                 [
//                     'id' => 'detection-1',
//                     'name' => 'Generic',
//                     'iconUrl' => 'https://example.com/generic.png',
//                     'type' => 'brand',
//                     'confidence' => 0.5 // Dưới ngưỡng VisuaDetectImage::CONFIDENCE_ON_HOLD
//                 ]
//             ]
//         ]
//     ];

//     // Tạo order với status on hold và tag VISUA_DETECTED_ID
//     $order = SaleOrder::factory()->create([
//         'id' => $this->visuaDetectImage->order_id,
//         'order_status' => SaleOrder::ON_HOLD,
//         'tag' => Tag::VISUA_DETECTED_ID
//     ]);

//     // Tạo order on hold chỉ với visua_on_hold = true
//     SaleOrderOnHold::create([
//         'order_id' => $this->visuaDetectImage->order_id,
//         'visua_on_hold' => true,
//         'store_on_hold' => false,
//         'manual_on_hold' => false
//     ]);

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra cập nhật trong database
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(1);
//     expect($visuaDetectImage->json)->not->toBeNull();
//     expect($visuaDetectImage->is_ip_violation)->toBe(0);

//     // Kiểm tra SaleOrder được cập nhật
//     $updatedOrder = SaleOrder::find($this->visuaDetectImage->order_id);
//     expect($updatedOrder->order_status)->toBe(SaleOrder::NEW_ORDER);
//     expect($updatedOrder->tag)->toBe('');

//     // Kiểm tra SaleOrderOnHold đã bị xóa
//     $orderOnHold = SaleOrderOnHold::where('order_id', $this->visuaDetectImage->order_id)->first();
//     expect($orderOnHold)->toBeNull();

//     // Kiểm tra SaleOrderHistory được tạo
//     $orderHistory = SaleOrderHistory::where('order_id', $this->visuaDetectImage->order_id)->first();
//     expect($orderHistory)->not->toBeNull();
//     expect($orderHistory->type)->toBe(SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE);
//     expect($orderHistory->message)->toContain('Order status changed from "on hold" to "new order" by Visua');
// });

// test('detect design webhook job - exception handling', function () {
//     // Mock Log facade
//     $logMock = Mockery::mock('alias:Illuminate\Support\Facades\Log');
//     $logMock->shouldReceive('channel->error')->once();

//     // Tạo job với dữ liệu gây ra exception
//     $webhookData = [
//         'responseData' => [
//             'sessionId' => $this->visuaSessionId,
//             'status' => 200
//             // Thiếu trường 'detections' sẽ gây ra lỗi khi truy cập
//         ]
//     ];

//     $job = new DetectDesignWebhookJob($webhookData);

//     // Thực thi job
//     $job->handle();

//     // Kiểm tra không có thay đổi trong database do exception
//     $visuaDetectImage = VisuaDetectImage::find($this->visuaDetectImageId);
//     expect($visuaDetectImage->is_received_response)->toBe(0);
// });
