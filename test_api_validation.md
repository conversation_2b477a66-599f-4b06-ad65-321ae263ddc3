# Test API Validation - Two-way Style & Color Comparison with Error Classification

## Endpoint
`POST /api/products/validate-variants-csv-product-spec`

## New Response Structure - Complete Classification
```json
{
    // Valid data phân loại
    "valid": {...},                    // Tất cả valid (tương thích với code cũ)
    "validCsv": {...},                // Valid từ CSV
    "validProduct": {...},            // Valid từ Product Data

    // Invalid data phân loại
    "invalid": [...],                 // Tất cả invalid (tương thích với code cũ)
    "invalidCsv": [...],             // Invalid từ CSV
    "invalidProduct": [...],          // Invalid từ Product Data

    // Count phân loại
    "countValid": 0,                  // Tổng số valid (tương thích với code cũ)
    "countValidCsv": 0,              // Số valid từ CSV
    "countValidProduct": 0,           // Số valid từ Product Data

    "countInvalid": 0,               // Tổng số invalid (tương thích với code cũ)
    "countInvalidCsv": 0,           // Số invalid từ CSV
    "countInvalidProduct": 0,        // Số invalid từ Product Data

    "status": false
}
```

## Test Cases

### Test Case 1: Perfect Match (Should Pass)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
STYLE_A,BLUE,L,BRAND1,Description 2
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ✅ Status 200
```json
{
    "countValidCsv": 2,
    "countValidProduct": 2,
    "countValid": 4,
    "countInvalidCsv": 0,
    "countInvalidProduct": 0,
    "countInvalid": 0,
    "validCsv": {
        "STYLE_A_RED": {/* transformed CSV data */},
        "STYLE_A_BLUE": {/* transformed CSV data */}
    },
    "validProduct": {
        "STYLE_A_RED": {"style": "STYLE_A", "color": "RED", "source": "product_data", "matched_in_csv": true},
        "STYLE_A_BLUE": {"style": "STYLE_A", "color": "BLUE", "source": "product_data", "matched_in_csv": true}
    },
    "status": true
}
```

---

### Test Case 2: CSV Missing Color (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ❌ Status 422
```json
{
    "countValidCsv": 1,
    "countValidProduct": 1,
    "countValid": 2,
    "countInvalidCsv": 0,
    "countInvalidProduct": 1,
    "countInvalid": 1,
    "validCsv": {
        "STYLE_A_RED": {/* transformed CSV data */}
    },
    "validProduct": {
        "STYLE_A_RED": {"style": "STYLE_A", "color": "RED", "source": "product_data", "matched_in_csv": true}
    },
    "invalidProduct": [
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "BLUE"},
            "reason": "Product data contains Style 'STYLE_A' and Color 'BLUE' but this combination is missing in CSV file.",
            "source": "product_data"
        }
    ],
    "status": false
}
```

---

### Test Case 3: CSV Has Extra Color (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
STYLE_A,BLUE,L,BRAND1,Description 2
STYLE_A,GREEN,S,BRAND1,Description 3
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ❌ Status 422
```json
{
    "countInvalidCsv": 1,
    "countInvalidProduct": 0,
    "countInvalid": 1,
    "invalidCsv": [
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "GREEN", "SIZE": "S", ...},
            "reason": "Color 'GREEN' is not in the allowed colors: RED, BLUE.",
            "source": "csv"
        }
    ],
    "status": false
}
```

---

### Test Case 4: Wrong Style (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_B,RED,M,BRAND1,Description 1
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"}
]
```

**Expected Result:** ❌ Status 422
```json
{
    "countInvalidCsv": 1,
    "countInvalidProduct": 1,
    "countInvalid": 2,
    "invalidCsv": [
        {
            "row": {"STYLE": "STYLE_B", "COLOR": "RED", ...},
            "reason": "Style 'STYLE_B' does not match expected style 'STYLE_A'.",
            "source": "csv"
        }
    ],
    "invalidProduct": [
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "RED"},
            "reason": "Product data contains Style 'STYLE_A' and Color 'RED' but this combination is missing in CSV file.",
            "source": "product_data"
        }
    ],
    "status": false
}
```

### Test Case 5: Mixed Errors (Both CSV and Product Data Issues)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
STYLE_A,YELLOW,L,BRAND1,Description 2
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"},
    {"style": "STYLE_A", "color": "GREEN", "size": "S"}
]
```

**Expected Result:** ❌ Status 422
```json
{
    "countInvalidCsv": 1,
    "countInvalidProduct": 2,
    "countInvalid": 3,
    "invalidCsv": [
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "YELLOW", ...},
            "reason": "Color 'YELLOW' is not in the allowed colors: RED, BLUE, GREEN.",
            "source": "csv"
        }
    ],
    "invalidProduct": [
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "BLUE"},
            "reason": "Product data contains Style 'STYLE_A' and Color 'BLUE' but this combination is missing in CSV file.",
            "source": "product_data"
        },
        {
            "row": {"STYLE": "STYLE_A", "COLOR": "GREEN"},
            "reason": "Product data contains Style 'STYLE_A' and Color 'GREEN' but this combination is missing in CSV file.",
            "source": "product_data"
        }
    ],
    "status": false
}
```

---

## How to Test

1. Create CSV file with test data
2. Send POST request with:
   - `file`: CSV file
   - `data`: JSON string of product data
3. Check response status and error messages

## Validation Logic Summary

✅ **Two-way validation now implemented:**
1. **CSV → Product Data**: Every style/color in CSV must exist in product data
2. **Product Data → CSV**: Every style/color in product data must exist in CSV
3. **Only passes when both directions are valid**

This ensures complete synchronization between CSV import data and expected product specifications.
