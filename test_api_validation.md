# Test API Validation - Two-way Style & Color Comparison

## Endpoint
`POST /api/products/validate-variants-csv-product-spec`

## Test Cases

### Test Case 1: Perfect Match (Should Pass)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
STYLE_A,BLUE,L,BRAND1,Description 2
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ✅ Status 200, validation passes

---

### Test Case 2: CSV Missing Color (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,<PERSON>,<PERSON>AND1,Description 1
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ❌ Status 422
**Error Message:** "Product data contains Style 'STYLE_A' and Color 'BLUE' but this combination is missing in CSV file."

---

### Test Case 3: CSV Has Extra Color (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_A,RED,M,BRAND1,Description 1
STYLE_A,BLUE,L,BRAND1,Description 2
STYLE_A,GREEN,S,BRAND1,Description 3
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"},
    {"style": "STYLE_A", "color": "BLUE", "size": "L"}
]
```

**Expected Result:** ❌ Status 422
**Error Message:** "Color 'GREEN' is not in the allowed colors: RED, BLUE."

---

### Test Case 4: Wrong Style (Should Fail with 422)
**CSV Data:**
```
STYLE,COLOR,SIZE,BRAND,DESCRIPTION
STYLE_B,RED,M,BRAND1,Description 1
```

**Product Data (JSON):**
```json
[
    {"style": "STYLE_A", "color": "RED", "size": "M"}
]
```

**Expected Result:** ❌ Status 422
**Error Message:** "Style 'STYLE_B' does not match expected style 'STYLE_A'."

---

## How to Test

1. Create CSV file with test data
2. Send POST request with:
   - `file`: CSV file
   - `data`: JSON string of product data
3. Check response status and error messages

## Validation Logic Summary

✅ **Two-way validation now implemented:**
1. **CSV → Product Data**: Every style/color in CSV must exist in product data
2. **Product Data → CSV**: Every style/color in product data must exist in CSV
3. **Only passes when both directions are valid**

This ensures complete synchronization between CSV import data and expected product specifications.
