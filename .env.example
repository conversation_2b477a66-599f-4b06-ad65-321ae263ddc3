APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:fxF5uuwSjn6/yYmiIz5vYpuIE/EfPqQ8Z/5YMa9+ta0=
APP_DEBUG=true
APP_URL=http://localhost
STORAGE_URL=https://api.swiftpodapp.com/storage

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=inventory
DB_USERNAME=swiftpod
DB_PASSWORD=4PsUM_Ptkk#rsbL

BROADCAST_DRIVER=redis
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


DEBUGBAR_ENABLED=false

STORAGE_URL=https://api.swiftpodapp.com/storage
AWS_S3_URL=https://swiftpod.s3.us-west-1.amazonaws.com

DATE_START_CONVERT_MUG=2022-01-01

HASH_ID_ALPHABET=abcdefghijklmnopqrstuvwxyz1234567890
HASH_ID_MIN_LENGTH=8
