<?php

/**
 * Complete Classification Example
 * Minh họa cấu trúc response hoàn chỉnh với phân loại valid/invalid từ cả CSV và Product Data
 */

echo "=== COMPLETE CLASSIFICATION EXAMPLE ===\n\n";

// Mô phỏng response từ API với phân loại hoàn chỉnh
$validationResponse = [
    // ===== VALID DATA =====
    'valid' => [
        // Tất cả valid data (tương thích với code cũ)
        'STYLE_A_RED' => ['transformed_csv_data' => 'from CSV'],
        'STYLE_A_BLUE' => ['transformed_csv_data' => 'from CSV'],
        'STYLE_A_RED_PRODUCT' => ['style' => 'STYLE_A', 'color' => 'RED', 'source' => 'product_data'],
        'STYLE_A_BLUE_PRODUCT' => ['style' => 'STYLE_A', 'color' => 'BLUE', 'source' => 'product_data'],
    ],
    
    'validCsv' => [
        // Valid data từ CSV
        'STYLE_A_RED' => ['transformed_csv_data' => 'from CSV'],
        'STYLE_A_BLUE' => ['transformed_csv_data' => 'from CSV'],
    ],
    
    'validProduct' => [
        // Valid data từ Product Data
        'STYLE_A_RED' => [
            'style' => 'STYLE_A',
            'color' => 'RED',
            'source' => 'product_data',
            'matched_in_csv' => true
        ],
        'STYLE_A_BLUE' => [
            'style' => 'STYLE_A',
            'color' => 'BLUE',
            'source' => 'product_data',
            'matched_in_csv' => true
        ],
    ],
    
    // ===== INVALID DATA =====
    'invalid' => [
        // Tất cả invalid data (tương thích với code cũ)
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'YELLOW'],
            'reason' => "Color 'YELLOW' is not in the allowed colors: RED, BLUE, GREEN.",
            'source' => 'csv'
        ],
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'GREEN'],
            'reason' => "Product data contains Style 'STYLE_A' and Color 'GREEN' but this combination is missing in CSV file.",
            'source' => 'product_data'
        ]
    ],
    
    'invalidCsv' => [
        // Invalid data từ CSV
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'YELLOW'],
            'reason' => "Color 'YELLOW' is not in the allowed colors: RED, BLUE, GREEN.",
            'source' => 'csv'
        ]
    ],
    
    'invalidProduct' => [
        // Invalid data từ Product Data
        [
            'row' => ['STYLE' => 'STYLE_A', 'COLOR' => 'GREEN'],
            'reason' => "Product data contains Style 'STYLE_A' and Color 'GREEN' but this combination is missing in CSV file.",
            'source' => 'product_data'
        ]
    ],
    
    // ===== COUNTS =====
    'countValid' => 4,              // Tổng số valid (2 CSV + 2 Product)
    'countValidCsv' => 2,          // Số valid từ CSV
    'countValidProduct' => 2,       // Số valid từ Product Data
    
    'countInvalid' => 2,           // Tổng số invalid (1 CSV + 1 Product)
    'countInvalidCsv' => 1,        // Số invalid từ CSV
    'countInvalidProduct' => 1,     // Số invalid từ Product Data
    
    'status' => false
];

echo "=== RESPONSE STRUCTURE ===\n";
echo json_encode($validationResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

echo "\n\n=== USAGE EXAMPLES ===\n\n";

echo "1. Tổng quan validation:\n";
echo "   Status: " . ($validationResponse['status'] ? 'PASS' : 'FAIL') . "\n";
echo "   Total valid: " . $validationResponse['countValid'] . "\n";
echo "   Total invalid: " . $validationResponse['countInvalid'] . "\n\n";

echo "2. Phân tích Valid data:\n";
echo "   CSV valid: " . $validationResponse['countValidCsv'] . "\n";
echo "   Product valid: " . $validationResponse['countValidProduct'] . "\n\n";

echo "3. Phân tích Invalid data:\n";
echo "   CSV invalid: " . $validationResponse['countInvalidCsv'] . "\n";
echo "   Product invalid: " . $validationResponse['countInvalidProduct'] . "\n\n";

echo "4. Chi tiết Valid CSV:\n";
foreach ($validationResponse['validCsv'] as $key => $data) {
    echo "   - {$key}: Valid CSV data\n";
}

echo "\n5. Chi tiết Valid Product:\n";
foreach ($validationResponse['validProduct'] as $key => $data) {
    echo "   - {$key}: {$data['style']}/{$data['color']} (matched: " . ($data['matched_in_csv'] ? 'Yes' : 'No') . ")\n";
}

echo "\n6. Chi tiết Invalid CSV:\n";
foreach ($validationResponse['invalidCsv'] as $error) {
    echo "   - " . $error['reason'] . "\n";
}

echo "\n7. Chi tiết Invalid Product:\n";
foreach ($validationResponse['invalidProduct'] as $error) {
    echo "   - " . $error['reason'] . "\n";
}

echo "\n=== BENEFITS OF COMPLETE CLASSIFICATION ===\n";
echo "✅ Phân loại hoàn chỉnh: Valid/Invalid cho cả CSV và Product Data\n";
echo "✅ Thống kê chi tiết: Count riêng cho từng loại\n";
echo "✅ Tương thích ngược: Vẫn có 'valid', 'invalid', 'countValid', 'countInvalid'\n";
echo "✅ Phân tích sâu: Biết chính xác data nào valid/invalid từ nguồn nào\n";
echo "✅ UX tốt hơn: Người dùng hiểu rõ trạng thái từng phần dữ liệu\n";

?>
