<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRbtCountStickersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rbt_count_stickers', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id')->nullable();
            $table->integer('rbt_count_sticker_printed_id')->nullable();
            $table->tinyInteger('convert_pdf_status')->default('0');
            $table->tinyInteger('retry_convert')->default('0');
            $table->integer('user_id')->nullable();
            $table->string('barcode')->nullable();
            $table->string('label')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rbt_count_stickers');
    }
}
