<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsRbtToSaleOrderItemTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sale_order_item', function (Blueprint $table) {
            $table->integer('is_rbt')->default(\App\Models\SaleOrderItem::IS_NOT_RBT);
            $table->index(['is_rbt']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sale_order_item', function (Blueprint $table) {
            $table->dropColumn('is_rbt');
        });
    }
}
