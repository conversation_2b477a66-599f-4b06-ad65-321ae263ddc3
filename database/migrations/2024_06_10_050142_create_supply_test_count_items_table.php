<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupplyTestCountItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supply_test_count_items', function (Blueprint $table) {
            $table->id();
            $table->integer('test_count_id');
            $table->integer('location_id');
            $table->integer('box_id');
            $table->enum('type', ['not_found', 'box_moving', 'found', 'box_new'])->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supply_test_count_items');
    }
}
