<?php

use App\Models\SaleOrder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order', function (Blueprint $table) {
            $table->id();
            $table->string('order_number', 100)->nullable()->comment('swiftpod number');
            $table->integer('external_id')->nullable()->comment('id shipstation, partner');
            $table->string('external_key', 50)->nullable();
            $table->string('external_number');
            $table->enum('order_status', ['new_order', 'shipped', 'in_production', 'on_hold', 'cancelled', 'manual_process', 'rejected', 'draft', 'in_production_cancelled'])->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('account_id')->nullable()->comment('sale_order_account');
            $table->integer('store_id')->nullable();
            $table->integer('is_test')->default('0');
            $table->string('customer_email')->nullable();
            $table->decimal('order_total', 13, 2)->nullable();
            $table->decimal('amount_paid', 13, 2)->nullable();
            $table->decimal('tax_amount', 13, 2)->nullable();
            $table->decimal('shipping_amount', 13, 2)->nullable();
            $table->text('customer_note')->nullable();
            $table->text('internal_note')->nullable();
            $table->integer('is_gift')->default('0');
            $table->string('gift_message')->default('');
            $table->string('payment_method', 100)->nullable();
//            $table->enum('shipping_method',['standard','express','priority'])->default('standard');
            $table->string('shipping_method')->default('standard')->nullable();
            $table->enum('source', ['shipstation', 'api', 'manual', ''])->nullable();
            $table->timestamps();
            $table->timestamp('external_created_at')->nullable();
            $table->string('tag')->nullable();
            $table->tinyInteger('is_eps')->default('0')->comment('shipping express');
            $table->date('order_date')->nullable();
            $table->timestamp('order_time')->nullable();
            $table->tinyInteger('is_xqc')->nullable()->comment('high quality');
            $table->tinyInteger('barcode_printed_status')->default('0');
            $table->integer('order_printed_status')->default('0');
            $table->timestamp('order_printed_at')->nullable();
            $table->timestamp('barcode_printed_at')->nullable();
            $table->timestamp('print_file_created_at')->nullable();
            $table->tinyInteger('print_file_status')->default('0');
            $table->tinyInteger('order_pulled_status')->default('0');
            $table->timestamp('order_pulled_at')->nullable();
            $table->tinyInteger('order_staged_status')->default('0');
            $table->timestamp('order_staged_at')->nullable();
            $table->tinyInteger('skip_alert_default')->default('0');
            $table->timestamp('order_pretreated_at')->nullable();
            $table->tinyInteger('order_pretreated_status')->default('0');
            $table->tinyInteger('order_qc_status')->default('0');
            $table->timestamp('order_qc_at')->nullable();
            $table->timestamp('order_folding_at')->nullable();
            $table->tinyInteger('order_folding_status')->default('0');
            $table->tinyInteger('order_shipping_status')->default('0')->comment('labeling');
            $table->timestamp('order_shipped_at')->nullable();
            $table->timestamp('order_production_at')->nullable();
            $table->integer('order_quantity')->default('0');
            $table->integer('shipment_id')->nullable();
            $table->timestamp('calculated_at')->nullable();
            $table->string('merchant_name')->nullable()->comment('dùng cho ship from trên label');
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->enum('rejected_reason', ['ip_violation', 'invalid_artwork', 'invalid_address', 'out_of_stock', 'customer_changed_mind', 'invalid_label_url'])->nullable();
            $table->tinyInteger('is_manual')->default('0');
            $table->tinyInteger('is_shipment_create_error')->default('0')->comment('tạo shipment lỗi');
            $table->timestamp('manual_process_at')->nullable();
            $table->integer('employee_manual_id')->nullable();
            $table->decimal('shipping_calculate', 13, 2)->default('0.00');
            $table->tinyInteger('is_fba_order')->default(null);
            $table->text('fba_shipping_label')->default(null);
            $table->text('fba_item_label')->default(null);
            $table->string('ioss_number')->default(null);
            $table->tinyInteger('order_type')->default(null);
            $table->string('encode_id')->nullable();
            $table->string('tax_id_type')->nullable();
            $table->string('production_status')->nullable();
            $table->tinyInteger('is_create_manual')->default('0');
            $table->integer('plastic_bag')->default('0');
            $table->integer('is_rbt')->default(App\Models\SaleOrderItem::IS_NOT_RBT);
            $table->enum('payment_status', [SaleOrder::PAYMENT_STATUS_PENDING, SaleOrder::PAYMENT_STATUS_PAID, SaleOrder::PAYMENT_STATUS_REFUNDED, SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED])->default(SaleOrder::PAYMENT_STATUS_PENDING);
            $table->timestamp('paid_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order');
    }
}
