<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderClaimSupportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_claim_support', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->string('store_id');
            $table->string('customer_email');
            $table->string('sale_order_item_id')->nullable();
            $table->string('type');
            $table->string('issue')->nullable();
            $table->string('solution');
            $table->text('additional_details')->nullable();
            $table->string('error_log')->nullable();
            $table->nullableTimestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_claim_support');
    }
}
