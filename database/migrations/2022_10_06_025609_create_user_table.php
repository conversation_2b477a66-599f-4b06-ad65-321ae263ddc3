<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user', function (Blueprint $table) {
            $table->id();
            $table->string('username', 100)->nullable();
            $table->string('password', 100)->nullable();
            $table->string('seller_password', 100)->nullable();
            $table->string('password_tmp', 50)->nullable();
            $table->string('email', 100)->nullable();
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->integer('group_id')->default('0');
            $table->integer('is_active')->default('1');
            $table->integer('gender')->nullable();
            $table->string('title', 20)->nullable();
            $table->string('first_name', 50)->nullable();
            $table->string('last_name', 50)->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('department', 50)->nullable();
            $table->tinyInteger('is_admin')->default('0');
            $table->timestamp('updated_at')->default('0000-00-00 00:00:00');
            $table->integer('department_id')->nullable();
            $table->string('store_ids')->nullable();
            $table->tinyInteger('is_all_warehouse')->default('0');
            $table->timestamp('assign_at')->nullable();
            $table->timestamp('expired_at')->nullable();
            $table->text('assign_history')->nullable();
            $table->tinyInteger('is_lavender_editor')->default(0);
            $table->tinyInteger('is_bypass_restriction')->default(0);
            $table->tinyInteger('is_all_store')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user');
    }
}
