<?php

    use Illuminate\Database\Migrations\Migration;
    use Illuminate\Database\Schema\Blueprint;
    use Illuminate\Support\Facades\Schema;

    class AddColumnDiscontinuedAtToProductsTable extends Migration
    {
        /**
         * Run the migrations.
         *
         * @return void
         */
        public function up()
        {
            Schema::table('product', function (Blueprint $table) {
                $table->timestamp('discontinued_at')->nullable()->after('stock_status');
            });
        }

        /**
         * Reverse the migrations.
         *
         * @return void
         */
        public function down()
        {
            Schema::table('product', function (Blueprint $table) {
                $table->dropColumn('discontinued_at');
            });
        }
    }
