<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRbtSkuMovementImportHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rbt_sku_movement_import_history', function (Blueprint $table) {
            $table->id();
            $table->integer('upload_by');
            $table->string('status', 50)->nullable();
            $table->string('file_name', 255);
            $table->string('link_url', 255)->nullable();
            $table->string('link_url_error', 255)->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rbt_sku_movement_import_history');
    }
}
