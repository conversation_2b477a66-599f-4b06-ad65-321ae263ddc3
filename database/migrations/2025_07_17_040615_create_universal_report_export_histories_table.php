<?php

use App\Models\UniversalReportExportHistory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUniversalReportExportHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('universal_report_export_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('report_template_id')->index();
            $table->string('name');
            $table->enum('status', [
                UniversalReportExportHistory::STATUS_PENDING,
                UniversalReportExportHistory::STATUS_COMPLETED,
                UniversalReportExportHistory::STATUS_FAILED,
                UniversalReportExportHistory::STATUS_EXPIRED,
            ]);
            $table->string('file_path')->nullable();
            $table->unsignedBigInteger('file_size')->default(0);
            $table->boolean('show_in_popup')->default(false);
            $table->text('message')->nullable();
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->timestamp('last_download_at')->nullable();
            $table->json('report_setting_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('universal_report_export_histories');
    }
}
