<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExportationTrackingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exportation_tracking', function (Blueprint $table) {
            $table->id();
            $table->integer('exportation_id')->nullable();
            $table->string('tracking_number')->nullable();
            $table->integer('order_id')->nullable();
            $table->integer('shipment_id')->nullable();
            $table->integer('employee_scan_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exportation_tracking');
    }
}
