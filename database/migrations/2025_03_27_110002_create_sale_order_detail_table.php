<?php

use App\Models\SaleOrderDetail;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderDetailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_detail', function (Blueprint $table) {
            $table->integer('order_id')->primary();
            $table->integer('manual_warehouse_id')->nullable();
            $table->integer('active_shipment_id')->nullable();
            $table->smallInteger('label_status')->default(SaleOrderDetail::LABEL_STATUS_DEFAULT);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_detail');
    }
}
