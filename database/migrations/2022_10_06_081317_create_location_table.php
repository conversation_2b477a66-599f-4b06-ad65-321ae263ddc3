<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLocationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('location', function (Blueprint $table) {
            $table->id();
            $table->string('barcode');
            $table->integer('warehouse_id');
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->integer('type')->default('0');
            $table->timestamp('updated_at')->nullable();
            $table->string('rbt_sku')->nullable();
            $table->string('rbt_type')->nullable();
            $table->tinyInteger('is_deleted')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('location');
    }
}
