<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentSyncTable extends Migration
{
    public function up()
    {
        Schema::create('shipment_sync', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('shipment_id');
            $table->integer('sync_tracking_shipstation')->default(0);
            $table->integer('sync_tracking_shipstation_retry')->default(0);
            $table->integer('sync_tracking_orderdesk')->default(0);
            $table->integer('sync_tracking_orderdesk_retry')->default(0)->comment('sync to shipstation');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->text('response_orderdesk')->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('shipment_sync');
    }
}
