<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInternalRequestTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('internal_request', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id')->nullable();
            $table->integer('product_id')->nullable();
            $table->integer('employee_create_id')->nullable();
            $table->integer('employee_receive_id')->nullable();
            $table->integer('box_id')->nullable();
            $table->integer('priority')->nullable();
            $table->enum('status', ['new', 'picking_up', 'uncheck', 'checked', 'rejected'])->default('new');
            $table->timestamp('received_at')->nullable();
            $table->timestamp('fulfilled_at')->nullable();
            $table->integer('employee_fulfill_id')->nullable();
            $table->integer('employee_confirm_id')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->integer('employee_reject_id')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->tinyInteger('is_rbt')->default(0);
            $table->tinyInteger('is_deleted')->default(0);
            $table->integer('dark_pod_quantity')->nullable();
            $table->integer('new_box_id')->nullable();
            $table->integer('count_sticker_id')->nullable();
            $table->text('station_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('internal_request');
    }
}
