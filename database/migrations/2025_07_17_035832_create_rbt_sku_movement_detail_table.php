<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRbtSkuMovementDetailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rbt_sku_movement_detail', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('rbt_sku_movement_id');
            $table->string('status', 50)->default('new');
            $table->unsignedBigInteger('product_id');
            $table->string('sku')->nullable();
            $table->integer('action');
            $table->unsignedBigInteger('old_location_id');
            $table->unsignedBigInteger('new_location_id');
            $table->integer('curent_change_rack')->default(0);
            $table->integer('total_change_rack')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rbt_sku_movement_detail');
    }
}
