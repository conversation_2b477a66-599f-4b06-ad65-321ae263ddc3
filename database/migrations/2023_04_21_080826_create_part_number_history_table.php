<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartNumberHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('part_number_history', function (Blueprint $table) {
            $table->id();
            $table->integer('part_number_id')->nullable();
            $table->enum('action', ['addition_to_pulling_shelves','adjust_pulling_shelves','box_moving_rack_to_pulling','qc_failed','exportation_report','create_label'])->nullable();
            $table->enum('type', ['import', 'export'])->nullable();
            $table->integer('quantity')->nullable();
            $table->integer('balance')->nullable();
            $table->integer('employee_id')->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('order_id')->nullable();
            $table->timestamp('created_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('part_number_history');
    }
}
