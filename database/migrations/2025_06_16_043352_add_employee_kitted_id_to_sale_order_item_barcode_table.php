<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEmployeeKittedIdToSaleOrderItemBarcodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sale_order_item_barcode', function (Blueprint $table) {
            $table->bigInteger('employee_kitted_id')->default(0);
            $table->timestamp('kitted_at')->nullable();
            $table->index(['employee_kitted_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sale_order_item_barcode', function (Blueprint $table) {
            $table->dropColumn('employee_kitted_id');
            $table->dropColumn('kitted_at');
        });
    }
}
