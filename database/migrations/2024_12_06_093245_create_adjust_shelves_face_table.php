<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdjustShelvesFaceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('adjust_shelves_face', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('product_available')->nullable();
            $table->integer('product_on_hand')->nullable();
            $table->integer('product_adjust')->nullable();
            $table->string('sku', 50)->nullable();
            $table->integer('product_id')->nullable();
            $table->string('note')->nullable();
            $table->integer('employee_id')->nullable();
            $table->string('country', 10)->nullable();
            $table->decimal('cost_value_on_hand', 13, 2)->nullable();
            $table->decimal('cost_value_adjusted', 13, 2)->nullable();
            $table->decimal('cost_value_available', 13, 2)->nullable();
            $table->integer('type')->default(3);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('adjust_shelves_face');
    }
}
