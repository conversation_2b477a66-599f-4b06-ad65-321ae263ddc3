<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderPrintMethodReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_print_method_reports', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('print_method');
            $table->bigInteger('total');
            $table->bigInteger('store_id');
            $table->bigInteger('warehouse_id');
            $table->date('order_date');
        });

        Schema::table('sale_order', function (Blueprint $table) {
            $table->index('created_at', 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_print_method_reports');

        Schema::table('sale_order', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });
    }
}
