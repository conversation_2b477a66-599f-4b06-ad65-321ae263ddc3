<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFifoInventoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fifo_inventory', function (Blueprint $table) {
            $table->id();
            $table->integer('product_id')->default(0);
            $table->integer('warehouse_id')->default(0);
            $table->string('product_style', 50)->nullable();
            $table->string('sku', 50)->nullable();
            $table->decimal('value', 13, 2)->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('start_unit')->default(0);
            $table->integer('end_unit')->default(0);
            $table->integer('last_unit')->default(0);
            $table->integer('coun_transaction')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fifo_inventory');
    }
}
