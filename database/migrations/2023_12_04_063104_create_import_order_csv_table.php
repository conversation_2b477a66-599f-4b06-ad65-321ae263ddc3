<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateImportOrderCsvTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('import_order_csv', function (Blueprint $table) {
            $table->id();
            $table->integer('store_id')->nullable();
            $table->string('file_name')->nullable();
            $table->integer('order_total')->nullable();
            $table->integer('order_imported')->nullable();
            $table->integer('order_failed')->nullable();
            $table->string('file_imported')->nullable();
            $table->string('file_failed')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('import_order_csv');
    }
}
