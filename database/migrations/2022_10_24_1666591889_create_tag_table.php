<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTagTable extends Migration
{
    public function up()
    {
        Schema::create('tag', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->nullable();
            $table->string('color', 50)->default('');
            $table->enum('source', ['sale_order', 'purchase_order', 'global', 'seller'])->nullable();
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->timestamp('updated_at')->nullable()->default('0000-00-00 00:00:00');
            $table->integer('account_id')->nullable();
            $table->integer('external_id')->nullable();
            $table->integer('surcharge_service_id')->nullable();
            $table->tinyInteger('is_additional_service')->default(0);
        });
    }

    public function down()
    {
        Schema::dropIfExists('tag');
    }
}
