<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWorkOrderItemTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('work_order_item', function (Blueprint $table) {
            $table->id();
            $table->integer('work_order_id')->nullable();
            $table->string('product_sku', 50)->nullable();
            $table->enum('status', ['completed','cancelled','incompleted'])->default('incompleted');
            $table->string('box_number', 50);
            $table->string('location_number', 50);
            $table->integer('is_alternative')->default(0);
            $table->integer('is_replaced')->default(0)->comment('hủy để thay cái khác');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('work_order_item');
    }
}
