<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVendorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vendor', function (Blueprint $table) {
            $table->id();
            $table->string('name',250)->nullable();
            $table->string('display_name',100)->nullable();
            $table->string('email',100)->nullable();
            $table->string('phone',50)->nullable();
            $table->string('work_phone',50)->nullable();
            $table->string('cell_phone',50)->nullable();
            $table->string('skype',50)->nullable();
            $table->string('department',100)->nullable();
            $table->text('note')->nullable();
            $table->integer('is_deleted')->default('0');
            $table->integer('payment_term_id')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->tinyInteger('type')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vendor');
    }
}
