<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUniversalReportTagsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('universal_report_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_template_id');
            $table->boolean('is_used')->default(false);
            $table->boolean('is_required')->default(true);
            $table->string('code');
            $table->integer('position')->nullable();
            $table->string('type')->comment('input, single_select, multi_select, single_date, range_date');
            $table->string('label')->nullable();
            $table->text('value')->nullable();
            $table->enum('value_type', ['free_text', 'sql'])->nullable();
            $table->string('placeholder_1')->nullable();
            $table->string('placeholder_2')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('universal_report_tags');
    }
}
