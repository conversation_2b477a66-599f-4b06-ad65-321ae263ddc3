<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesReportTotals extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_report_totals', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id');
            $table->integer('store_id');
            $table->date('date');
            $table->integer('total_orders')->nullable();
            $table->integer('total_items')->nullable();
            $table->integer('total_shipped_orders')->nullable();
            $table->integer('total_shipped_items')->nullable();
            $table->decimal('total_order_sales_amount', 13, 2)->nullable();
            $table->decimal('total_order_items_sales_amount', 13, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales_report_totals');
    }
}
