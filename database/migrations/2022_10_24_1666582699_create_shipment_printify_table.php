<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentPrintifyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipment_printify', function (Blueprint $table) {
            $table->increments("id");
            $table->integer('warehouse_id')->default(0);
            $table->integer('account_id')->default(0);
            $table->integer('store_id')->default(0);
            $table->integer('order_id')->nullable();
            $table->enum('shipment_account',['swiftpod','store'])->nullable()->comment('tao boi account');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->date('ship_date')->nullable()->comment('ngay ship');
            $table->decimal('shipment_cost',13,2)->nullable()->comment('phi ship');
            $table->decimal('insurance_cost',13,2)->nullable()->comment('phi bao hiem');
            $table->string('carrier_code')->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('tracking_status')->nullable();
            $table->string('service_code')->nullable();
            $table->tinyInteger('is_return_label')->nullable();
            $table->string('package_code')->nullable();
            $table->decimal('weight_value',13,2)->nullable();
            $table->enum('weight_unit',['pounds','ounces','grams'])->nullable();
            $table->decimal('dimension_length',13,2)->nullable();
            $table->decimal('dimension_width',13,2)->nullable();
            $table->decimal('dimension_height',13,2)->nullable();
            $table->enum('dimension_unit',['inches','centimeters'])->nullable();
            $table->integer('shipment_quantity')->default(0);
            $table->string('label_url',500)->nullable();
            $table->string('label_zpl_url',500)->nullable();
            $table->enum('confirmation',['online','delivery','signature','adult_signature','verbal'])->nullable();
            $table->enum('insurance',['none','shipsurance','carrier','external'])->nullable();
            $table->integer('employee_create_id')->nullable();
            $table->enum('refund_status',['submitted','refunded','rejected'])->nullable();
            $table->integer('employee_refund_id')->nullable();
            $table->integer('sync_tracking')->default(0)->comment('1 thanh cong');
            $table->tinyInteger('sync_tracking_retry')->default(0)->comment('so lan retry');
            $table->enum('provider',['shipstation','easypost','manual'])->default('shipstation');
            $table->tinyInteger('is_auto_created')->default(0)->comment('0 manual 1 auto');
            $table->string('url_tracking_easypost',500)->nullable();
            $table->tinyInteger('is_deleted')->default(0);
            $table->integer('employee_printed_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipment_printify');
    }
}
