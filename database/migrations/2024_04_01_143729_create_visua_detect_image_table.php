<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisuaDetectImageTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visua_detect_image', function (Blueprint $table) {
            $table->id();
            $table->string('visua_session_id');
            $table->longText('json')->nullable();
            $table->integer('order_id');
            $table->integer('image_id');
            $table->integer('user_detect_id')->nullable();
            $table->tinyInteger('is_ip_violation')->default(0);
            $table->tinyInteger('is_received_response')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visua_detect_image');
    }
}
