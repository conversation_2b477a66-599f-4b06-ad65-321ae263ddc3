<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployeeWorkLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_work_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('employee_id');
            $table->integer('department_id');
            $table->string('task_type');
            $table->integer('warehouse_id');
            $table->date('work_date');
            $table->integer('total_tasks');
            $table->integer('single_order_tasks')->nullable();
            $table->integer('multiple_order_tasks')->nullable();
            $table->decimal('total_hours_worked', 8, 2)->nullable();
            $table->decimal('average_ink_usage', 8, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_work_logs');
    }
}
