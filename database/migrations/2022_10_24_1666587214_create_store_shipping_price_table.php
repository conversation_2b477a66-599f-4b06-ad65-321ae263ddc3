<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoreShippingPriceTable extends Migration
{
    public function up()
    {
        Schema::create('store_shipping_price', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('store_id')->nullable();
            $table->string('product_type')->nullable();
            $table->string('product_style')->nullable();
            $table->string('destination')->nullable();
            $table->string('service_type')->nullable();
            $table->string('size')->default('');
            $table->decimal('price', 13, 2)->default('0.00');
            $table->decimal('addition_price', 13, 2)->default('0.00');
            $table->tinyInteger('status')->default(1);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('store_shipping_price');
    }
}
