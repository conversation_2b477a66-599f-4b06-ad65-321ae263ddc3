<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePdfConvertedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pdf_converted', function (Blueprint $table) {
            $table->id();
            $table->integer('quantity')->default('0');
            $table->tinyInteger('convert_status')->default('0');
            $table->integer('convert_percent')->default('0');
            $table->timestamp('convert_at');
            $table->tinyInteger('download_status')->default('0');
            $table->integer('employee_convert_id');
            $table->integer('warehouse_id');
            $table->timestamp('download_at')->nullable();
            $table->string('type', 50)->nullable();
            $table->integer('product_id')->nullable();
            $table->integer('employee_download_id')->nullable();
            $table->integer('quantity_input')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('print_method', 100)->nullable()
                ->nullable();
            $table->longText('options')->nullable();
            $table->string('batch_number', 50)->nullable();
            $table->string('code_wip', 50)->nullable();
            $table->integer('country_id')->nullable();
            $table->longText('position_illustrator')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pdf_converted');
    }
}
