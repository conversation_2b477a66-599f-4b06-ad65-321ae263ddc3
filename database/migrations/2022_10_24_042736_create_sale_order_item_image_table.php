<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderItemImageTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_item_image', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id')->default('0');
            $table->integer('order_id')->nullable();
            $table->integer('order_item_id')->nullable();
            $table->integer('account_id')->nullable();
            $table->integer('store_id')->nullable();
            $table->string('sku')->nullable();
            $table->string('name')->nullable();
            $table->string('image_url', 1000)->nullable();
            $table->string('image_ext', 10)->nullable();
            $table->integer('image_width')->nullable();
            $table->integer('image_height')->nullable();
            $table->integer('image_size')->nullable();
            $table->tinyInteger('download_status')->default('0');
            $table->tinyInteger('resize_status')->default('0');
            $table->tinyInteger('convert_status')->default('0');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table->tinyInteger('color')->nullable();
            $table->tinyInteger('is_resize')->default('0');
            $table->integer('resize_with')->nullable();
            $table->integer('resize_height')->nullable();
            $table->date('order_date');
            $table->timestamp('order_time')->nullable();
            $table->string('print_side', 4)->nullable();
            $table->tinyInteger('is_double_side')->default('0');
            $table->tinyInteger('worker')->nullable();
            $table->string('error')->nullable();
            $table->tinyInteger('preset_status')->default('0');
            $table->string('preset_name', 50)->nullable();
            $table->tinyInteger('is_tool_update')->default('0');
            $table->tinyInteger('worker_thumb')->nullable();
            $table->tinyInteger('thumb_status')->nullable();
            $table->string('preset_new', 50)->nullable();
            $table->tinyInteger('thumb_750')->default('0');
            $table->tinyInteger('upload_s3_status')->default('0');
            $table->tinyInteger('color_new')->nullable();
            $table->tinyInteger('thumb_250')->default('0');
            $table->tinyInteger('manual_color')->nullable();
            $table->string('product_sku', 9)->nullable();
            $table->tinyInteger('is_xqc')->default('0');
            $table->integer('delete_status')->default('0');
            $table->tinyInteger('skip_retry')->default('0');
            $table->float('ink_color_cc')->default('0');
            $table->float('ink_white_cc')->default('0');
            $table->string('image_hash_id', 32)->nullable();
            $table->tinyInteger('retry_download_manual_count')->default('0');
            $table->timestamp('last_retry_download_manual')->nullable();
            $table->tinyInteger('retry_detect_color_count')->default('0');
            $table->timestamp('last_retry_detect_color')->nullable();
            $table->tinyInteger('retry_count')->default('0');
            $table->string('pretreat_info')->nullable();
            $table->string('image_dpi')->nullable();
            $table->tinyInteger('is_purple')->default('0');
            $table->tinyInteger('is_og')->default('0');
            $table->string('custom_platen', 5)->nullable();
            $table->timestamp('icc_converted_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_item_image');
    }
}
