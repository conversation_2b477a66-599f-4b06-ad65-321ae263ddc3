<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEasypostAdjustmentsTable extends Migration
{
    public function up()
    {
        Schema::create('easypost_adjustment', function (Blueprint $table) {
            $table->id();
            $table->timestamp('invoice_date')->nullable();
            $table->string('shipment_id', 255);
            $table->dateTime('label_date')->nullable();
            $table->string('carrier_account_id', 255)->nullable();
            $table->string('carrier', 255)->nullable();
            $table->string('tracking_code', 255)->nullable();
            $table->string('carrier_invoice_id', 255)->nullable();
            $table->string('package_dispute_id', 255)->nullable();
            $table->string('status', 50)->nullable();
            $table->string('quoted_currency', 50)->nullable();
            $table->decimal('initially_paid_amount', 10, 2)->nullable();
            $table->decimal('quoted_amount', 10, 2)->nullable();
            $table->decimal('claimed_length', 10, 2)->nullable();
            $table->decimal('claimed_width', 10, 2)->nullable();
            $table->decimal('claimed_height', 10, 2)->nullable();
            $table->decimal('claimed_weight', 10, 2)->nullable();
            $table->string('claimed_package', 255)->nullable();
            $table->string('claimed_service', 255)->nullable();
            $table->decimal('final_invoice_amount', 10, 2)->nullable();
            $table->decimal('captured_length', 10, 2)->nullable();
            $table->decimal('captured_width', 10, 2)->nullable();
            $table->decimal('captured_height', 10, 2)->nullable();
            $table->decimal('captured_weight', 10, 2)->nullable();
            $table->string('captured_package', 255)->nullable();
            $table->string('captured_service', 255)->nullable();
            $table->string('captured_currency', 50)->nullable();
            $table->decimal('adjustment_amount', 10, 2)->nullable();
            $table->text('adjustment_reason')->nullable();
            $table->string('initially_paid_payment_log', 255)->nullable();
            $table->string('refund_payment_log', 255)->nullable();
            $table->string('clawback_payment_log', 255)->nullable();
            $table->string('invoice_payment_log', 255)->nullable();
            $table->string('user_id', 255)->nullable();
            $table->string('user_parent_id', 255)->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('easypost_adjustments');
    }
}
