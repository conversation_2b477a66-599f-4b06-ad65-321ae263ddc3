<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrintingPresetSkuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('printing_preset_sku', function (Blueprint $table) {
            $table->id();
            $table->string('sku', 50)->nullable();
            $table->string('black_ink', 50)->nullable();
            $table->string('white_ink', 50)->nullable();
            $table->string('mix_ink', 50)->nullable();
            $table->string('black_ink_xqc', 50)->nullable();
            $table->string('white_ink_xqc', 50)->nullable();
            $table->string('mix_ink_xqc', 50)->nullable();
            $table->string('platen_size', 50)->nullable();
            $table->string('platen_front_size', 50)->nullable();
            $table->string('front_size', 50)->nullable();
            $table->string('front_position', 50)->nullable();
            $table->string('platen_back_size', 50)->nullable();
            $table->string('back_size', 50)->nullable();
            $table->string('back_position', 50)->nullable();
            $table->string('platen_pocket_size', 50)->nullable();
            $table->string('pocket_size', 50)->nullable();
            $table->string('pocket_position', 50)->nullable();
            $table->integer('user_id')->nullable();
            $table->string('background_color', 50)->nullable();
            $table->string('neck_size', 50)->nullable();
            $table->string('left_sleeve_size', 50)->nullable();
            $table->string('right_sleeve_size', 50)->nullable();
            $table->string('purple_ink', 50)->nullable();
            $table->string('purple_ink_xqc', 50)->nullable();
            $table->string('inner_neck_label_size', 50)->nullable();
            $table->string('outer_neck_label_size', 50)->nullable();
            $table->string('die_cut_size', 50)->nullable();
            $table->boolean('admin_edit_only')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('printing_preset_sku');
    }
}
