<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLabelUrgentToSaleOrderItemBarcodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sale_order_item_barcode', function (Blueprint $table) {
            $table->string('label_urgent', 25)->after('label_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sale_order_item_barcode', function (Blueprint $table) {
            $table->dropColumn('label_urgent');
        });
    }
}
