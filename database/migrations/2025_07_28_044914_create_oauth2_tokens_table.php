<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOauth2TokensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('oauth2_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('platform')->unique();           // ví dụ: 'usps'
            $table->string('token_type')->nullable();       // ví dụ: 'Bearer'
            $table->text('access_token');                   // token chính
            $table->text('refresh_token')->nullable();      // nếu có
            $table->integer('expires_in')->nullable();      // giây đến khi hết hạn
            $table->timestamp('issued_at')->nullable();     // lúc token được cấp
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('oauth2_tokens');
    }
}
