<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUniversalReportColumnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('universal_report_columns', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_template_id')->index();
            $table->boolean('virtual_column')->default(false);
            $table->text('original');
            $table->text('table')->nullable();
            $table->text('column')->nullable();
            $table->string('alias')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('universal_report_columns');
    }
}
