<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSellbriteSyncTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sellbrite_sync', function (Blueprint $table) {
            $table->id();
            $table->string('sku',100)->nullable();
            $table->integer('sellbrite_warehouse_id')->unsigned()->nullable();
            $table->integer('product_id')->unsigned()->nullable();
            $table->decimal('cost',13,2)->default('0.00');
            $table->timestamp('added_sync_at')->nullable();
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sellbrite_sync');
    }
}
