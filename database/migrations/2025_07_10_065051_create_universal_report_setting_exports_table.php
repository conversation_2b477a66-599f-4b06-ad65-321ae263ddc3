<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUniversalReportSettingExportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('universal_report_setting_exports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_setting_id')->index();
            $table->string('model_type');
            $table->unsignedBigInteger('model_id')->nullable();
            $table->text('model_key')->nullable();
            $table->string('alias')->nullable();
            $table->string('value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('universal_report_exports');
    }
}
