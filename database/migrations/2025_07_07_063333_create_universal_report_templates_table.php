<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUniversalReportTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('universal_report_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_category_id')->nullable();
            $table->boolean('status')->default(false);
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('query');
            $table->text('statement')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('universal_report_templates');
    }
}
