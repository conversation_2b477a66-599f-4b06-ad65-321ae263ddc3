<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddConversionValueUnitConversionIdSupplyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('supply', function (Blueprint $table) {
            $table->double('conversion_value')->nullable()->after('sku');
            $table->integer('unit_conversion_id')->after('conversion_value');
            $table->index(['unit_conversion_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('supply', function ($table) {
            $table->dropColumn('conversion_value');
            $table->dropColumn('unit_conversion_id');
        });
    }
}
