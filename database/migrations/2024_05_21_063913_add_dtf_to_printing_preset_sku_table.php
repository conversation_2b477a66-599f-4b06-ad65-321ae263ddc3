<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDtfToPrintingPresetSkuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('printing_preset_sku', function (Blueprint $table) {
            $table->string('platen_dtf_f_size', 5)->nullable();
            $table->string('platen_dtf_b_size', 5)->nullable();
            $table->string('dtf_f_size', 5)->nullable();
            $table->string('dtf_b_size', 5)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('printing_preset_sku', function ($table) {
            $table->dropColumn('conversion_value');
            $table->dropColumn('unit_conversion_id');
        });
    }
}
