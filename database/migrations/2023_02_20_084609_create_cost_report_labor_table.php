<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCostReportLaborTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cost_report_labor', function (Blueprint $table) {
            $table->id();
            $table->date('pay_period_begin')->default(null);
            $table->date('pay_period_end')->default(null);
            $table->decimal('cost')->default(null);
            $table->integer('employee_id')->default(null);
            $table->integer('warehouse_id')->default(null);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('table_cost_report_labor');
    }
}
