<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBarcodePrintedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('barcode_printed', function (Blueprint $table) {
            $table->id();
            $table->integer('store_id')->nullable();
            $table->integer('employee_id')->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->tinyInteger('is_xqc')->default(0)->nullable();
            $table->tinyInteger('is_reprint')->default(0)->nullable();
            $table->string('style_sku', 10)->nullable();
            $table->integer('quantity')->default(0);
            $table->integer('quantity_input')->default(0);
            $table->timestamp('created_at')->nullable();
            $table->tinyInteger('convert_status')->default(0)->comment('1 là tạo thành công');
            $table->tinyInteger('print_status')->default(0);
            $table->timestamp('converted_at')->nullable();
            $table->integer('account_id')->nullable();
            $table->integer('convert_percent')->default(0);
            $table->string('first_sku', 50)->nullable();
            $table->string('last_sku', 50)->nullable();
            $table->tinyInteger('is_manual')->default(0)->nullable();
            $table->string('print_method', 20)->default('DTG');
            $table->integer('product_id')->nullable();
            $table->tinyInteger('is_reroute')->default(0)->nullable();
            $table->tinyInteger('is_fba')->default(0)->nullable();
            $table->string('color_sku')->nullable();
            $table->tinyInteger('is_error_print')->default(0);
            $table->integer('reprint_approved_by')->nullable();
            $table->string('status_print')->nullable();
            $table->tinyInteger('is_insert')->nullable();
            $table->timestamp('printed_at')->nullable();
            $table->tinyInteger('is_deduct')->default(0);
            $table->tinyInteger('is_eps')->nullable();
            $table->tinyInteger('is_tiktok')->nullable();
            $table->tinyInteger('station_id')->nullable();
            $table->tinyInteger('is_top_style')->default(0);

            $table->timestamp('pulled_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('barcode_printed');
    }
}
