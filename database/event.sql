CREATE DEFINER=`admin`@`localhost` EVENT `delete_image` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-05-06 11:17:26' ON COMPLETION NOT PRESERVE ENABLE DO UPDATE sale_order_item_image SET delete_status = 1 WHERE sku IN (SELECT sku FROM sale_order_item_barcode WHERE is_deleted = 1) AND delete_status = 0

CREATE DEFINER=`admin`@`localhost` EVENT `delete_log_tag_shipstation_over_30_day` ON SCHEDULE EVERY 1 DAY STARTS '2022-07-06 03:12:08' ON COMPLETION NOT PRESERVE ENABLE DO DELETE FROM tag_shipstation_log	 WHERE created_at < NOW() - INTERVAL 30 DAY

CREATE DEFINER=`doanduchome`@`%` EVENT `retry_download_image` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-09-15 17:19:47' ON COMPLETION PRESERVE ENABLE DO BEGIN
UPDATE   sale_order_item_image SET thumb_750 = 0, color_new = NULL, thumb_250 = 0,
                                   skip_retry = IF(retry_count > 9, 1, 0) , retry_count= retry_count+ 1
WHERE  skip_retry = 0 AND delete_status = 0 AND  (thumb_750 = 2 OR thumb_250 = 2 OR color_new = 3) AND updated_at < SUBTIME(NOW(), 300)
  AND order_date >  DATE_SUB(NOW(), INTERVAL 15 DAY);
END

CREATE DEFINER=`inventory`@`%` EVENT `retry_move_warehouse` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-04-08 00:00:00' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
# reset push tag move warehouse
UPDATE  log_move_warehouse SET is_push_tag = 0  WHERE is_push_tag = 2;
END

CREATE DEFINER=`admin`@`localhost` EVENT `retry_tag_printed_tool` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-03-21 17:16:48' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
# reset push tag wip printed to shiptation
UPDATE  sale_order SET order_printed_status = 1  WHERE order_printed_status  = 3;
END

CREATE DEFINER=`admin`@`localhost` EVENT `retry_tag_pulled_tool` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-03-21 17:16:48' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
# reset push tag wip printed to shiptation
UPDATE  sale_order SET order_pulled_status = 1  WHERE order_pulled_status  = 3;
END

CREATE DEFINER=`admin`@`localhost` EVENT `retry_tag_staged_tool` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-03-21 17:18:17' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
# reset push tag wip printed to shiptation
UPDATE  sale_order SET order_staged_status  = 1  WHERE order_staged_status   = 3;
END

CREATE DEFINER=`inventory`@`%` EVENT `retry_tag_wip_printed_tool` ON SCHEDULE EVERY 3 MINUTE STARTS '2022-02-21 09:47:03' ON COMPLETION NOT PRESERVE ENABLE COMMENT 'tự động reset để đánh tag lại sau 3 phút nếu lỗi' DO BEGIN
# reset push tag wip printed to shiptation
UPDATE  sale_order SET barcode_printed_status = 1  WHERE barcode_printed_status = 3;
END

CREATE DEFINER=`inventory`@`%` EVENT `retry_tag_wip_read_tool` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-02-22 00:00:00' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
# reset push tag WIP READY TOOL to shiptation
UPDATE  sale_order SET print_file_status = 1  WHERE print_file_status = 3;
END

CREATE DEFINER=`admin`@`localhost` EVENT `update_forecast_quantity` ON SCHEDULE EVERY 1 DAY STARTS '2022-08-23 02:22:54' ON COMPLETION NOT PRESERVE ENABLE DO REPLACE INTO
    forecast_sale_order(
        product_id,
        warehouse_id,
        order_date,
        quantity
    ) SELECT
                 sale_order_item.product_id AS product_id,
                 sale_order.warehouse_id AS warehouse_id,
                 sale_order.order_date AS order_date,
                 SUM(sale_order_item.quantity) AS quantity
      FROM
                 sale_order
                     JOIN sale_order_item ON sale_order.id = sale_order_item.order_id
      WHERE
                 sale_order_item.product_id IS NOT NULL AND order_date = DATE(SUBDATE(NOW(), INTERVAL 1 DAY))
      GROUP BY
                 sale_order_item.product_id,
                 sale_order.warehouse_id,
                 sale_order.order_date

CREATE DEFINER=`inventory`@`%` EVENT `update_status_barcode_printed` ON SCHEDULE EVERY 5 MINUTE STARTS '2022-02-18 09:22:47' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
UPDATE sale_order JOIN (SELECT b.order_id, b.order_quantity, o.order_date FROM sale_order_item_barcode b JOIN sale_order o ON o.id = b.order_id AND o.barcode_printed_status = 0  GROUP BY b.order_id
    HAVING SUM(b.barcode_printed_id > 0) = b.order_quantity) t ON t.order_id = sale_order.id SET sale_order.barcode_printed_status = 1 ;
END
