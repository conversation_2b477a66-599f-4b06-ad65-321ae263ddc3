<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class EmbroideryUserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'username' => $this->faker->userName(),
            'password' => bcrypt('123456'),
            'team_id' => 1,
            'role' => 'admin',
            'email' => $this->faker->unique()->safeEmail(),
        ];
    }
}
