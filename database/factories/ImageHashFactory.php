<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class ImageHashFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'hash_md5' => $this->faker->sha256,
            'created_at' => now(),
            'updated_at' => now()
        ];
    }
}
