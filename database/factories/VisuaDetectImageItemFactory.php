<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class VisuaDetectImageItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'visua_id' => $this->faker->numberBetween(1, 100),
            'visua_detect_image_id' => $this->faker->numberBetween(1, 100),
            'name' => $this->faker->word,
            'url' => $this->faker->url,
            'type' => $this->faker->randomElement(['logo', 'trademark', 'design']),
            'confidence' => $this->faker->randomFloat(2, 0, 1),
            'employee_detect_id' => $this->faker->optional()->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now()
        ];
    }
}
