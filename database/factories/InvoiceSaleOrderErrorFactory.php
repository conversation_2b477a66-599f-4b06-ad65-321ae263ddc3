<?php

namespace Database\Factories;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrderError;
use App\Models\SaleOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceSaleOrderErrorFactory extends Factory
{
  protected $model = InvoiceSaleOrderError::class;

  public function definition()
  {
    return [
      'invoice_id' => Invoice::factory()->create()->id,
      'sale_order_id' => SaleOrder::factory()->create()->id,
    ];
  }
}
