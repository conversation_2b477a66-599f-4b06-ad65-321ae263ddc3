<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SaleOrderAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'source' => $this->faker->text(),
            'api_key' => $this->faker->randomNumber(5),
            'api_secret' => $this->faker->randomNumber(5),
            'is_active' => 1,
            'is_resize' => 0,
            'note' => $this->faker->text(),
            'folder' => $this->faker->text(),
            'partner_id' => $this->faker->randomNumber(5),
            'sync_shipstation' => 0,
            'sync_orderdesk' => 0,
            'is_editing' => 0
        ];
    }
}
