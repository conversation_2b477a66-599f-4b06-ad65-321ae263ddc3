<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class VisuaDetectImageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'visua_session_id' => Str::random(32),
            'json' => null,
            'gemini_json' => null,
            'image_id' => $this->faker->numberBetween(1, 100),
            'order_id' => $this->faker->numberBetween(1, 100),
            'is_received_response' => $this->faker->boolean(70),
            'is_ip_violation' => $this->faker->boolean(30),
            'user_detect_id' => $this->faker->optional()->numberBetween(1, 100),
            'created_at' => now(),
            'updated_at' => now()
        ];
    }
}
