<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class EmbroideryTaskFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'sale_order_id' => $this->faker->randomNumber(6),
            'order_item_id' => $this->faker->randomNumber(6),
            'sale_order_image_id' => $this->faker->randomNumber(6),
            'status' => 'pending',
            'print_area' => 'back',
            'image_hash_id' => $this->faker->randomNumber(7)
        ];
    }
}
