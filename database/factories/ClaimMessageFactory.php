<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\SaleOrderClaimSupport;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClaimMessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $sender = $this->faker->randomElement([
            User::factory()->create(),
            Client::factory()->create()
        ]);

        return [
            'sale_order_claim_support_id' => SaleOrderClaimSupport::factory()->create(),
            'sender_id' => $sender->id,
            'sender_role' => get_class($sender) === User::class ? 'support' : 'seller',
            'message' => [
                'content' => $this->faker->paragraph(),
                'attachments' => []
            ]
        ];
    }
}
