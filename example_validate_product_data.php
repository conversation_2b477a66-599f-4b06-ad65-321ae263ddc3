<?php

/**
 * <PERSON><PERSON> dụ sử dụng hàm validateDataWithProductArray
 * File này chỉ để demo, không chạy trực tiếp
 */

// Giả sử bạn đang trong một Controller hoặc Service
class ExampleUsage
{
    public function validateProductData()
    {
        // 1. Dữ liệu dataProduct như bạn cung cấp
        $dataProduct = [
            [
                "sku" => "UNPT3W24M",
                "style" => "3001",
                "color" => "ASH",
                "size" => "24MO",
                "gtin" => "",
                "gtin_case" => 1
            ],
            [
                "sku" => "UNPT3W24M",
                "style" => "3002",
                "color" => "ASH",
                "size" => "24MO",
                "gtin" => "",
                "gtin_case" => 1
            ]
        ];

        // 2. Dữ liệu $data từ Excel hoặc form (ví dụ)
        $data = [
            ['STYLE' => '3001', 'COLOR' => 'ASH', 'OTHER_FIELD' => 'value1'],
            ['STYLE' => '3002', 'COLOR' => 'ASH', 'OTHER_FIELD' => 'value2'],
            ['STYLE' => '3003', 'COLOR' => 'RED', 'OTHER_FIELD' => 'value3'], // Này sẽ lỗi
            ['STYLE' => '3001', 'COLOR' => 'BLUE', 'OTHER_FIELD' => 'value4'], // Này cũng sẽ lỗi
        ];

        // 3. Sử dụng hàm validate
        $productRepository = new \App\Repositories\ProductRepository();
        $result = $productRepository->validateDataWithProductArray($data, $dataProduct);

        // 4. Xử lý kết quả
        if ($result['has_errors']) {
            echo "Có lỗi trong dữ liệu:\n";
            foreach ($result['errors'] as $error) {
                echo "- Dòng {$error['row_index']}: {$error['error']}\n";
            }
            echo "Tổng số lỗi: {$result['total_errors']}\n";
            echo "Tổng số hợp lệ: {$result['total_valid']}\n";
        } else {
            echo "Tất cả dữ liệu hợp lệ!\n";
            echo "Tổng số dòng hợp lệ: {$result['total_valid']}\n";
        }

        return $result;
    }

    /**
     * Ví dụ sử dụng trong Controller
     */
    public function exampleInController()
    {
        // Trong Controller của bạn
        $request = request(); // Laravel request
        
        // Lấy dataProduct từ request hoặc database
        $dataProduct = json_decode($request->input('dataProduct'), true);
        
        // Lấy data từ Excel hoặc form
        $data = $request->input('data'); // hoặc từ Excel import
        
        // Validate
        $productRepository = app(\App\Repositories\ProductRepository::class);
        $validationResult = $productRepository->validateDataWithProductArray($data, $dataProduct);
        
        if ($validationResult['has_errors']) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validationResult['errors'],
                'total_errors' => $validationResult['total_errors'],
                'total_valid' => $validationResult['total_valid']
            ], 422);
        }
        
        // Tiếp tục xử lý với dữ liệu hợp lệ
        $validData = $validationResult['valid'];
        
        return response()->json([
            'success' => true,
            'message' => 'Dữ liệu hợp lệ',
            'data' => $validData,
            'total_valid' => $validationResult['total_valid']
        ]);
    }

    /**
     * Ví dụ sử dụng với Excel import
     */
    public function exampleWithExcelImport()
    {
        // Giả sử bạn đã import Excel và có dữ liệu như này:
        $excelData = [
            ['STYLE' => '3001', 'COLOR' => 'ASH', 'QUANTITY' => 100],
            ['STYLE' => '3002', 'COLOR' => 'ASH', 'QUANTITY' => 200],
            ['STYLE' => '3001', 'COLOR' => 'RED', 'QUANTITY' => 150], // Sẽ lỗi vì không có RED
        ];

        $dataProduct = [
            ["style" => "3001", "color" => "ASH"],
            ["style" => "3002", "color" => "ASH"],
        ];

        $productRepository = app(\App\Repositories\ProductRepository::class);
        $result = $productRepository->validateDataWithProductArray($excelData, $dataProduct);

        if ($result['has_errors']) {
            // Trả về lỗi cho user
            $errorMessages = [];
            foreach ($result['errors'] as $error) {
                $errorMessages[] = "Dòng " . ($error['row_index'] + 1) . ": " . $error['error'];
            }
            
            return [
                'status' => 'error',
                'message' => 'Có lỗi trong file Excel',
                'errors' => $errorMessages
            ];
        }

        // Xử lý dữ liệu hợp lệ
        foreach ($result['valid'] as $row) {
            // Lưu vào database hoặc xử lý logic khác
            echo "Xử lý: Style {$row['STYLE']}, Color {$row['COLOR']}, Quantity {$row['QUANTITY']}\n";
        }

        return [
            'status' => 'success',
            'message' => "Đã xử lý thành công {$result['total_valid']} dòng dữ liệu"
        ];
    }
}

/**
 * Kết quả mong đợi khi chạy ví dụ:
 * 
 * Có lỗi trong dữ liệu:
 * - Dòng 2: Style '3003' và Color 'RED' không tồn tại trong danh sách sản phẩm.
 * - Dòng 3: Style '3001' và Color 'BLUE' không tồn tại trong danh sách sản phẩm.
 * Tổng số lỗi: 2
 * Tổng số hợp lệ: 2
 */
